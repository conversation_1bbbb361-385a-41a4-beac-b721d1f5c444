// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGMeshGeneration.h"
#include "AuracronPCGElementLibraryExtended.h"
#include "PCGPoint.h"
#include "ProceduralMeshComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGMeshGeneration() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshCombinerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshGenerationUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMeshEntry();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGMetadata_NoRegister();
PCG_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPoint();
PROCEDURALMESHCOMPONENT_API UScriptStruct* Z_Construct_UScriptStruct_FProcMeshTangent();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGMeshGenerationType ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType;
static UEnum* EAuracronPCGMeshGenerationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMeshGenerationType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshGenerationType>()
{
	return EAuracronPCGMeshGenerationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh generation types\n" },
#endif
		{ "HierarchicalMesh.DisplayName", "Hierarchical Instanced Mesh" },
		{ "HierarchicalMesh.Name", "EAuracronPCGMeshGenerationType::HierarchicalMesh" },
		{ "InstancedMesh.DisplayName", "Instanced Mesh" },
		{ "InstancedMesh.Name", "EAuracronPCGMeshGenerationType::InstancedMesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
		{ "ProceduralMesh.DisplayName", "Procedural Mesh" },
		{ "ProceduralMesh.Name", "EAuracronPCGMeshGenerationType::ProceduralMesh" },
		{ "SkeletalMesh.DisplayName", "Skeletal Mesh" },
		{ "SkeletalMesh.Name", "EAuracronPCGMeshGenerationType::SkeletalMesh" },
		{ "SplineMesh.DisplayName", "Spline Mesh" },
		{ "SplineMesh.Name", "EAuracronPCGMeshGenerationType::SplineMesh" },
		{ "StaticMesh.DisplayName", "Static Mesh" },
		{ "StaticMesh.Name", "EAuracronPCGMeshGenerationType::StaticMesh" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh generation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMeshGenerationType::StaticMesh", (int64)EAuracronPCGMeshGenerationType::StaticMesh },
		{ "EAuracronPCGMeshGenerationType::InstancedMesh", (int64)EAuracronPCGMeshGenerationType::InstancedMesh },
		{ "EAuracronPCGMeshGenerationType::HierarchicalMesh", (int64)EAuracronPCGMeshGenerationType::HierarchicalMesh },
		{ "EAuracronPCGMeshGenerationType::ProceduralMesh", (int64)EAuracronPCGMeshGenerationType::ProceduralMesh },
		{ "EAuracronPCGMeshGenerationType::SplineMesh", (int64)EAuracronPCGMeshGenerationType::SplineMesh },
		{ "EAuracronPCGMeshGenerationType::SkeletalMesh", (int64)EAuracronPCGMeshGenerationType::SkeletalMesh },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMeshGenerationType",
	"EAuracronPCGMeshGenerationType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType.InnerSingleton;
}
// ********** End Enum EAuracronPCGMeshGenerationType **********************************************

// ********** Begin Enum EAuracronPCGMeshSelectionMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode;
static UEnum* EAuracronPCGMeshSelectionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMeshSelectionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshSelectionMode>()
{
	return EAuracronPCGMeshSelectionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ByAttribute.DisplayName", "By Attribute" },
		{ "ByAttribute.Name", "EAuracronPCGMeshSelectionMode::ByAttribute" },
		{ "ByDensity.DisplayName", "By Density" },
		{ "ByDensity.Name", "EAuracronPCGMeshSelectionMode::ByDensity" },
		{ "ByDistance.DisplayName", "By Distance" },
		{ "ByDistance.Name", "EAuracronPCGMeshSelectionMode::ByDistance" },
		{ "ByNormal.DisplayName", "By Normal" },
		{ "ByNormal.Name", "EAuracronPCGMeshSelectionMode::ByNormal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh selection modes\n" },
#endif
		{ "Custom.DisplayName", "Custom Expression" },
		{ "Custom.Name", "EAuracronPCGMeshSelectionMode::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EAuracronPCGMeshSelectionMode::Random" },
		{ "Sequential.DisplayName", "Sequential" },
		{ "Sequential.Name", "EAuracronPCGMeshSelectionMode::Sequential" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh selection modes" },
#endif
		{ "Weighted.DisplayName", "Weighted" },
		{ "Weighted.Name", "EAuracronPCGMeshSelectionMode::Weighted" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMeshSelectionMode::Random", (int64)EAuracronPCGMeshSelectionMode::Random },
		{ "EAuracronPCGMeshSelectionMode::ByAttribute", (int64)EAuracronPCGMeshSelectionMode::ByAttribute },
		{ "EAuracronPCGMeshSelectionMode::ByDensity", (int64)EAuracronPCGMeshSelectionMode::ByDensity },
		{ "EAuracronPCGMeshSelectionMode::ByDistance", (int64)EAuracronPCGMeshSelectionMode::ByDistance },
		{ "EAuracronPCGMeshSelectionMode::ByNormal", (int64)EAuracronPCGMeshSelectionMode::ByNormal },
		{ "EAuracronPCGMeshSelectionMode::Sequential", (int64)EAuracronPCGMeshSelectionMode::Sequential },
		{ "EAuracronPCGMeshSelectionMode::Weighted", (int64)EAuracronPCGMeshSelectionMode::Weighted },
		{ "EAuracronPCGMeshSelectionMode::Custom", (int64)EAuracronPCGMeshSelectionMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMeshSelectionMode",
	"EAuracronPCGMeshSelectionMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGMeshSelectionMode ***********************************************

// ********** Begin Enum EAuracronPCGMeshCombineMode ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode;
static UEnum* EAuracronPCGMeshCombineMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMeshCombineMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshCombineMode>()
{
	return EAuracronPCGMeshCombineMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Append.DisplayName", "Append" },
		{ "Append.Name", "EAuracronPCGMeshCombineMode::Append" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh combination modes\n" },
#endif
		{ "Intersect.DisplayName", "Intersect" },
		{ "Intersect.Name", "EAuracronPCGMeshCombineMode::Intersect" },
		{ "Merge.DisplayName", "Merge" },
		{ "Merge.Name", "EAuracronPCGMeshCombineMode::Merge" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
		{ "Simplify.DisplayName", "Simplify" },
		{ "Simplify.Name", "EAuracronPCGMeshCombineMode::Simplify" },
		{ "Subtract.DisplayName", "Subtract" },
		{ "Subtract.Name", "EAuracronPCGMeshCombineMode::Subtract" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh combination modes" },
#endif
		{ "Union.DisplayName", "Union" },
		{ "Union.Name", "EAuracronPCGMeshCombineMode::Union" },
		{ "Weld.DisplayName", "Weld" },
		{ "Weld.Name", "EAuracronPCGMeshCombineMode::Weld" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMeshCombineMode::Merge", (int64)EAuracronPCGMeshCombineMode::Merge },
		{ "EAuracronPCGMeshCombineMode::Union", (int64)EAuracronPCGMeshCombineMode::Union },
		{ "EAuracronPCGMeshCombineMode::Subtract", (int64)EAuracronPCGMeshCombineMode::Subtract },
		{ "EAuracronPCGMeshCombineMode::Intersect", (int64)EAuracronPCGMeshCombineMode::Intersect },
		{ "EAuracronPCGMeshCombineMode::Append", (int64)EAuracronPCGMeshCombineMode::Append },
		{ "EAuracronPCGMeshCombineMode::Weld", (int64)EAuracronPCGMeshCombineMode::Weld },
		{ "EAuracronPCGMeshCombineMode::Simplify", (int64)EAuracronPCGMeshCombineMode::Simplify },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMeshCombineMode",
	"EAuracronPCGMeshCombineMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGMeshCombineMode *************************************************

// ********** Begin Enum EAuracronPCGMeshLODGenerationMode *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode;
static UEnum* EAuracronPCGMeshLODGenerationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMeshLODGenerationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshLODGenerationMode>()
{
	return EAuracronPCGMeshLODGenerationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Automatic.DisplayName", "Automatic" },
		{ "Automatic.Name", "EAuracronPCGMeshLODGenerationMode::Automatic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD generation modes\n" },
#endif
		{ "Custom.DisplayName", "Custom Rules" },
		{ "Custom.Name", "EAuracronPCGMeshLODGenerationMode::Custom" },
		{ "Density.DisplayName", "Density Based" },
		{ "Density.Name", "EAuracronPCGMeshLODGenerationMode::Density" },
		{ "Distance.DisplayName", "Distance Based" },
		{ "Distance.Name", "EAuracronPCGMeshLODGenerationMode::Distance" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGMeshLODGenerationMode::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD generation modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMeshLODGenerationMode::None", (int64)EAuracronPCGMeshLODGenerationMode::None },
		{ "EAuracronPCGMeshLODGenerationMode::Automatic", (int64)EAuracronPCGMeshLODGenerationMode::Automatic },
		{ "EAuracronPCGMeshLODGenerationMode::Distance", (int64)EAuracronPCGMeshLODGenerationMode::Distance },
		{ "EAuracronPCGMeshLODGenerationMode::Density", (int64)EAuracronPCGMeshLODGenerationMode::Density },
		{ "EAuracronPCGMeshLODGenerationMode::Custom", (int64)EAuracronPCGMeshLODGenerationMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMeshLODGenerationMode",
	"EAuracronPCGMeshLODGenerationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGMeshLODGenerationMode *******************************************

// ********** Begin Enum EAuracronPCGCollisionMode *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCollisionMode;
static UEnum* EAuracronPCGCollisionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCollisionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCollisionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCollisionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCollisionMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCollisionMode>()
{
	return EAuracronPCGCollisionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "BoundingBox.DisplayName", "Bounding Box" },
		{ "BoundingBox.Name", "EAuracronPCGCollisionMode::BoundingBox" },
		{ "BoundingSphere.DisplayName", "Bounding Sphere" },
		{ "BoundingSphere.Name", "EAuracronPCGCollisionMode::BoundingSphere" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision generation modes\n" },
#endif
		{ "Complex.DisplayName", "Complex Collision" },
		{ "Complex.Name", "EAuracronPCGCollisionMode::Complex" },
		{ "ConvexHull.DisplayName", "Convex Hull" },
		{ "ConvexHull.Name", "EAuracronPCGCollisionMode::ConvexHull" },
		{ "Custom.DisplayName", "Custom Collision" },
		{ "Custom.Name", "EAuracronPCGCollisionMode::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGCollisionMode::None" },
		{ "Simple.DisplayName", "Simple Collision" },
		{ "Simple.Name", "EAuracronPCGCollisionMode::Simple" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision generation modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCollisionMode::None", (int64)EAuracronPCGCollisionMode::None },
		{ "EAuracronPCGCollisionMode::Simple", (int64)EAuracronPCGCollisionMode::Simple },
		{ "EAuracronPCGCollisionMode::Complex", (int64)EAuracronPCGCollisionMode::Complex },
		{ "EAuracronPCGCollisionMode::ConvexHull", (int64)EAuracronPCGCollisionMode::ConvexHull },
		{ "EAuracronPCGCollisionMode::BoundingBox", (int64)EAuracronPCGCollisionMode::BoundingBox },
		{ "EAuracronPCGCollisionMode::BoundingSphere", (int64)EAuracronPCGCollisionMode::BoundingSphere },
		{ "EAuracronPCGCollisionMode::Custom", (int64)EAuracronPCGCollisionMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCollisionMode",
	"EAuracronPCGCollisionMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCollisionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCollisionMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCollisionMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGCollisionMode ***************************************************

// ********** Begin ScriptStruct FAuracronPCGMeshGenerationEntry ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry;
class UScriptStruct* FAuracronPCGMeshGenerationEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGMeshGenerationEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Mesh Entry Descriptor\n * Describes a mesh entry with all its properties and settings\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh Entry Descriptor\nDescribes a mesh entry with all its properties and settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mesh_MetaData[] = {
		{ "Category", "Mesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weight_MetaData[] = {
		{ "Category", "Mesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialOverrides_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LocalOffset_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LocalRotation_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LocalScale_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionAttribute_MetaData[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeRange_MetaData[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateLODs_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODCount_MetaData[] = {
		{ "Category", "LOD" },
		{ "EditCondition", "bGenerateLODs" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMode_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastShadows_MetaData[] = {
		{ "Category", "Rendering" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReceiveDecals_MetaData[] = {
		{ "Category", "Rendering" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderingLODBias_MetaData[] = {
		{ "Category", "Rendering" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weight;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialOverrides_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialOverrides;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LocalOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LocalRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LocalScale;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectionAttribute;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttributeRange;
	static void NewProp_bGenerateLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODCount;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionMode;
	static void NewProp_bCastShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastShadows;
	static void NewProp_bReceiveDecals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReceiveDecals;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RenderingLODBias;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGMeshGenerationEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mesh_MetaData), NewProp_Mesh_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_Weight = { "Weight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, Weight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weight_MetaData), NewProp_Weight_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_MaterialOverrides_Inner = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_MaterialOverrides = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, MaterialOverrides), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialOverrides_MetaData), NewProp_MaterialOverrides_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LocalOffset = { "LocalOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, LocalOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LocalOffset_MetaData), NewProp_LocalOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LocalRotation = { "LocalRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, LocalRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LocalRotation_MetaData), NewProp_LocalRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LocalScale = { "LocalScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, LocalScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LocalScale_MetaData), NewProp_LocalScale_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_SelectionAttribute = { "SelectionAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, SelectionAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionAttribute_MetaData), NewProp_SelectionAttribute_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_AttributeRange = { "AttributeRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, AttributeRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeRange_MetaData), NewProp_AttributeRange_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bGenerateLODs_SetBit(void* Obj)
{
	((FAuracronPCGMeshGenerationEntry*)Obj)->bGenerateLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bGenerateLODs = { "bGenerateLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMeshGenerationEntry), &Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bGenerateLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateLODs_MetaData), NewProp_bGenerateLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LODCount = { "LODCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, LODCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODCount_MetaData), NewProp_LODCount_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_CollisionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_CollisionMode = { "CollisionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, CollisionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMode_MetaData), NewProp_CollisionMode_MetaData) }; // 1493187721
void Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bCastShadows_SetBit(void* Obj)
{
	((FAuracronPCGMeshGenerationEntry*)Obj)->bCastShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bCastShadows = { "bCastShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMeshGenerationEntry), &Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bCastShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastShadows_MetaData), NewProp_bCastShadows_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bReceiveDecals_SetBit(void* Obj)
{
	((FAuracronPCGMeshGenerationEntry*)Obj)->bReceiveDecals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bReceiveDecals = { "bReceiveDecals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMeshGenerationEntry), &Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bReceiveDecals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReceiveDecals_MetaData), NewProp_bReceiveDecals_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_RenderingLODBias = { "RenderingLODBias", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshGenerationEntry, RenderingLODBias), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderingLODBias_MetaData), NewProp_RenderingLODBias_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_Weight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_MaterialOverrides_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_MaterialOverrides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LocalOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LocalRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LocalScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_SelectionAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_AttributeRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bGenerateLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_LODCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_CollisionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_CollisionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bCastShadows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_bReceiveDecals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewProp_RenderingLODBias,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGMeshGenerationEntry",
	Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::PropPointers),
	sizeof(FAuracronPCGMeshGenerationEntry),
	alignof(FAuracronPCGMeshGenerationEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGMeshGenerationEntry *************************************

// ********** Begin ScriptStruct FAuracronPCGProceduralMeshDescriptor ******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor;
class UScriptStruct* FAuracronPCGProceduralMeshDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGProceduralMeshDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Procedural Mesh Descriptor\n * Describes parameters for procedural mesh generation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural Mesh Descriptor\nDescribes parameters for procedural mesh generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vertices_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Triangles_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normals_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVs_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexColors_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tangents_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Material_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateNormals_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateUVs_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateTangents_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vertices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Vertices;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Triangles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Triangles;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Normals;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UVs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VertexColors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VertexColors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tangents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tangents;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Material;
	static void NewProp_bAutoGenerateNormals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateNormals;
	static void NewProp_bAutoGenerateUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateUVs;
	static void NewProp_bAutoGenerateTangents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateTangents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGProceduralMeshDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Vertices_Inner = { "Vertices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Vertices = { "Vertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProceduralMeshDescriptor, Vertices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vertices_MetaData), NewProp_Vertices_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Triangles_Inner = { "Triangles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Triangles = { "Triangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProceduralMeshDescriptor, Triangles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Triangles_MetaData), NewProp_Triangles_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Normals_Inner = { "Normals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Normals = { "Normals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProceduralMeshDescriptor, Normals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normals_MetaData), NewProp_Normals_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_UVs_Inner = { "UVs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_UVs = { "UVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProceduralMeshDescriptor, UVs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVs_MetaData), NewProp_UVs_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_VertexColors_Inner = { "VertexColors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_VertexColors = { "VertexColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProceduralMeshDescriptor, VertexColors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexColors_MetaData), NewProp_VertexColors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Tangents_Inner = { "Tangents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FProcMeshTangent, METADATA_PARAMS(0, nullptr) }; // 3217639600
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Tangents = { "Tangents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProceduralMeshDescriptor, Tangents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tangents_MetaData), NewProp_Tangents_MetaData) }; // 3217639600
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Material = { "Material", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProceduralMeshDescriptor, Material), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Material_MetaData), NewProp_Material_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateNormals_SetBit(void* Obj)
{
	((FAuracronPCGProceduralMeshDescriptor*)Obj)->bAutoGenerateNormals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateNormals = { "bAutoGenerateNormals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGProceduralMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateNormals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateNormals_MetaData), NewProp_bAutoGenerateNormals_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateUVs_SetBit(void* Obj)
{
	((FAuracronPCGProceduralMeshDescriptor*)Obj)->bAutoGenerateUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateUVs = { "bAutoGenerateUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGProceduralMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateUVs_MetaData), NewProp_bAutoGenerateUVs_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateTangents_SetBit(void* Obj)
{
	((FAuracronPCGProceduralMeshDescriptor*)Obj)->bAutoGenerateTangents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateTangents = { "bAutoGenerateTangents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGProceduralMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateTangents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateTangents_MetaData), NewProp_bAutoGenerateTangents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Vertices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Vertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Triangles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Triangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Normals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Normals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_UVs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_UVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_VertexColors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_VertexColors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Tangents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Tangents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_Material,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateNormals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewProp_bAutoGenerateTangents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGProceduralMeshDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGProceduralMeshDescriptor),
	alignof(FAuracronPCGProceduralMeshDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGProceduralMeshDescriptor ********************************

// ********** Begin Class UAuracronPCGAdvancedStaticMeshSpawnerSettings ****************************
void UAuracronPCGAdvancedStaticMeshSpawnerSettings::StaticRegisterNativesUAuracronPCGAdvancedStaticMeshSpawnerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings;
UClass* UAuracronPCGAdvancedStaticMeshSpawnerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedStaticMeshSpawnerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedStaticMeshSpawnerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedStaticMeshSpawnerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_NoRegister()
{
	return UAuracronPCGAdvancedStaticMeshSpawnerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMeshGeneration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshEntries_MetaData[] = {
		{ "Category", "Mesh Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh entries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh entries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionMode_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionAttribute_MetaData[] = {
		{ "Category", "Selection" },
		{ "EditCondition", "SelectionMode == EAuracronPCGMeshSelectionMode::ByAttribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionExpression_MetaData[] = {
		{ "Category", "Selection" },
		{ "EditCondition", "SelectionMode == EAuracronPCGMeshSelectionMode::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationType_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Generation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHierarchicalInstancing_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerComponent_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODMode_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "LOD" },
		{ "EditCondition", "LODMode != EAuracronPCGMeshLODGenerationMode::None" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityLODThreshold_MetaData[] = {
		{ "Category", "LOD" },
		{ "EditCondition", "LODMode == EAuracronPCGMeshLODGenerationMode::Density" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFrustumCulling_MetaData[] = {
		{ "Category", "Culling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseOcclusionCulling_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGPUInstancing_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBatching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshEntries;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SelectionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SelectionMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectionAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectionExpression;
	static const UECodeGen_Private::FBytePropertyParams NewProp_GenerationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GenerationType;
	static void NewProp_bUseHierarchicalInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHierarchicalInstancing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerComponent;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DensityLODThreshold;
	static void NewProp_bUseFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFrustumCulling;
	static void NewProp_bUseOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseOcclusionCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static void NewProp_bUseGPUInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGPUInstancing;
	static void NewProp_bUseBatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBatching;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedStaticMeshSpawnerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_MeshEntries_Inner = { "MeshEntries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMeshEntry, METADATA_PARAMS(0, nullptr) }; // 492763619
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_MeshEntries = { "MeshEntries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, MeshEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshEntries_MetaData), NewProp_MeshEntries_MetaData) }; // 492763619
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionMode = { "SelectionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, SelectionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionMode_MetaData), NewProp_SelectionMode_MetaData) }; // 232537351
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionAttribute = { "SelectionAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, SelectionAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionAttribute_MetaData), NewProp_SelectionAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionExpression = { "SelectionExpression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, SelectionExpression), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionExpression_MetaData), NewProp_SelectionExpression_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_GenerationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_GenerationType = { "GenerationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, GenerationType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshGenerationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationType_MetaData), NewProp_GenerationType_MetaData) }; // 1309368601
void Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseHierarchicalInstancing_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedStaticMeshSpawnerSettings*)Obj)->bUseHierarchicalInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseHierarchicalInstancing = { "bUseHierarchicalInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedStaticMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseHierarchicalInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHierarchicalInstancing_MetaData), NewProp_bUseHierarchicalInstancing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_MaxInstancesPerComponent = { "MaxInstancesPerComponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, MaxInstancesPerComponent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerComponent_MetaData), NewProp_MaxInstancesPerComponent_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODMode = { "LODMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, LODMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshLODGenerationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODMode_MetaData), NewProp_LODMode_MetaData) }; // 3962011530
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_DensityLODThreshold = { "DensityLODThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, DensityLODThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityLODThreshold_MetaData), NewProp_DensityLODThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseFrustumCulling_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedStaticMeshSpawnerSettings*)Obj)->bUseFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseFrustumCulling = { "bUseFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedStaticMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFrustumCulling_MetaData), NewProp_bUseFrustumCulling_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseOcclusionCulling_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedStaticMeshSpawnerSettings*)Obj)->bUseOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseOcclusionCulling = { "bUseOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedStaticMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseOcclusionCulling_MetaData), NewProp_bUseOcclusionCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedStaticMeshSpawnerSettings, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseGPUInstancing_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedStaticMeshSpawnerSettings*)Obj)->bUseGPUInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseGPUInstancing = { "bUseGPUInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedStaticMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseGPUInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGPUInstancing_MetaData), NewProp_bUseGPUInstancing_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseBatching_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedStaticMeshSpawnerSettings*)Obj)->bUseBatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseBatching = { "bUseBatching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedStaticMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseBatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBatching_MetaData), NewProp_bUseBatching_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_MeshEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_MeshEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_SelectionExpression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_GenerationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_GenerationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseHierarchicalInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_MaxInstancesPerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_DensityLODThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseGPUInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::NewProp_bUseBatching,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedStaticMeshSpawnerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedStaticMeshSpawnerSettings);
UAuracronPCGAdvancedStaticMeshSpawnerSettings::~UAuracronPCGAdvancedStaticMeshSpawnerSettings() {}
// ********** End Class UAuracronPCGAdvancedStaticMeshSpawnerSettings ******************************

// ********** Begin Class UAuracronPCGInstancedMeshGeneratorSettings *******************************
void UAuracronPCGInstancedMeshGeneratorSettings::StaticRegisterNativesUAuracronPCGInstancedMeshGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings;
UClass* UAuracronPCGInstancedMeshGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGInstancedMeshGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGInstancedMeshGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGInstancedMeshGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_NoRegister()
{
	return UAuracronPCGInstancedMeshGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMeshGeneration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshEntries_MetaData[] = {
		{ "Category", "Mesh Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHierarchicalInstancing_MetaData[] = {
		{ "Category", "Instancing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instancing settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instancing settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerComponent_MetaData[] = {
		{ "Category", "Instancing" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoClusterInstances_MetaData[] = {
		{ "Category", "Instancing" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterDistance_MetaData[] = {
		{ "Category", "Instancing" },
		{ "EditCondition", "bAutoClusterInstances" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePerInstanceVariation_MetaData[] = {
		{ "Category", "Variation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Variation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Variation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "EditCondition", "bUsePerInstanceVariation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "EditCondition", "bUsePerInstanceVariation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseColorVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "EditCondition", "bUsePerInstanceVariation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseColor_MetaData[] = {
		{ "Category", "Variation" },
		{ "EditCondition", "bUseColorVariation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "EditCondition", "bUseColorVariation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLODOptimization_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Optimization settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODScreenSizes_MetaData[] = {
		{ "Category", "Optimization" },
		{ "EditCondition", "bUseLODOptimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseImpostors_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorDistance_MetaData[] = {
		{ "Category", "Optimization" },
		{ "EditCondition", "bUseImpostors" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshEntries;
	static void NewProp_bUseHierarchicalInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHierarchicalInstancing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerComponent;
	static void NewProp_bAutoClusterInstances_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoClusterInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterDistance;
	static void NewProp_bUsePerInstanceVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePerInstanceVariation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleVariation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotationVariation;
	static void NewProp_bUseColorVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseColorVariation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorVariation;
	static void NewProp_bUseLODOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLODOptimization;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODScreenSizes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODScreenSizes;
	static void NewProp_bUseImpostors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseImpostors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ImpostorDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGInstancedMeshGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_MeshEntries_Inner = { "MeshEntries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMeshEntry, METADATA_PARAMS(0, nullptr) }; // 492763619
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_MeshEntries = { "MeshEntries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, MeshEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshEntries_MetaData), NewProp_MeshEntries_MetaData) }; // 492763619
void Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseHierarchicalInstancing_SetBit(void* Obj)
{
	((UAuracronPCGInstancedMeshGeneratorSettings*)Obj)->bUseHierarchicalInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseHierarchicalInstancing = { "bUseHierarchicalInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancedMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseHierarchicalInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHierarchicalInstancing_MetaData), NewProp_bUseHierarchicalInstancing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_MaxInstancesPerComponent = { "MaxInstancesPerComponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, MaxInstancesPerComponent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerComponent_MetaData), NewProp_MaxInstancesPerComponent_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bAutoClusterInstances_SetBit(void* Obj)
{
	((UAuracronPCGInstancedMeshGeneratorSettings*)Obj)->bAutoClusterInstances = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bAutoClusterInstances = { "bAutoClusterInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancedMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bAutoClusterInstances_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoClusterInstances_MetaData), NewProp_bAutoClusterInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ClusterDistance = { "ClusterDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, ClusterDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterDistance_MetaData), NewProp_ClusterDistance_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUsePerInstanceVariation_SetBit(void* Obj)
{
	((UAuracronPCGInstancedMeshGeneratorSettings*)Obj)->bUsePerInstanceVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUsePerInstanceVariation = { "bUsePerInstanceVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancedMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUsePerInstanceVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePerInstanceVariation_MetaData), NewProp_bUsePerInstanceVariation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ScaleVariation = { "ScaleVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, ScaleVariation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleVariation_MetaData), NewProp_ScaleVariation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_RotationVariation = { "RotationVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, RotationVariation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationVariation_MetaData), NewProp_RotationVariation_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseColorVariation_SetBit(void* Obj)
{
	((UAuracronPCGInstancedMeshGeneratorSettings*)Obj)->bUseColorVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseColorVariation = { "bUseColorVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancedMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseColorVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseColorVariation_MetaData), NewProp_bUseColorVariation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_BaseColor = { "BaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, BaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseColor_MetaData), NewProp_BaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ColorVariation = { "ColorVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, ColorVariation), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariation_MetaData), NewProp_ColorVariation_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseLODOptimization_SetBit(void* Obj)
{
	((UAuracronPCGInstancedMeshGeneratorSettings*)Obj)->bUseLODOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseLODOptimization = { "bUseLODOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancedMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseLODOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLODOptimization_MetaData), NewProp_bUseLODOptimization_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_LODScreenSizes_Inner = { "LODScreenSizes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_LODScreenSizes = { "LODScreenSizes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, LODScreenSizes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODScreenSizes_MetaData), NewProp_LODScreenSizes_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseImpostors_SetBit(void* Obj)
{
	((UAuracronPCGInstancedMeshGeneratorSettings*)Obj)->bUseImpostors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseImpostors = { "bUseImpostors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancedMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseImpostors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseImpostors_MetaData), NewProp_bUseImpostors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ImpostorDistance = { "ImpostorDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancedMeshGeneratorSettings, ImpostorDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorDistance_MetaData), NewProp_ImpostorDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_MeshEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_MeshEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseHierarchicalInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_MaxInstancesPerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bAutoClusterInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ClusterDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUsePerInstanceVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ScaleVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_RotationVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseColorVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_BaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ColorVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseLODOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_LODScreenSizes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_LODScreenSizes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_bUseImpostors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::NewProp_ImpostorDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGInstancedMeshGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGInstancedMeshGeneratorSettings);
UAuracronPCGInstancedMeshGeneratorSettings::~UAuracronPCGInstancedMeshGeneratorSettings() {}
// ********** End Class UAuracronPCGInstancedMeshGeneratorSettings *********************************

// ********** Begin Class UAuracronPCGProceduralMeshCreatorSettings ********************************
void UAuracronPCGProceduralMeshCreatorSettings::StaticRegisterNativesUAuracronPCGProceduralMeshCreatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings;
UClass* UAuracronPCGProceduralMeshCreatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGProceduralMeshCreatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGProceduralMeshCreatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGProceduralMeshCreatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_NoRegister()
{
	return UAuracronPCGProceduralMeshCreatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMeshGeneration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshDescriptors_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh generation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh generation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateFromPrimitives_MetaData[] = {
		{ "Category", "Primitives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Primitive generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Primitive generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateBoxes_MetaData[] = {
		{ "Category", "Primitives" },
		{ "EditCondition", "bGenerateFromPrimitives" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateSpheres_MetaData[] = {
		{ "Category", "Primitives" },
		{ "EditCondition", "bGenerateFromPrimitives" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateCylinders_MetaData[] = {
		{ "Category", "Primitives" },
		{ "EditCondition", "bGenerateFromPrimitives" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGeneratePlanes_MetaData[] = {
		{ "Category", "Primitives" },
		{ "EditCondition", "bGenerateFromPrimitives" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoxSize_MetaData[] = {
		{ "Category", "Box Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Box generation settings\n" },
#endif
		{ "EditCondition", "bGenerateBoxes" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Box generation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAttributeForBoxSize_MetaData[] = {
		{ "Category", "Box Generation" },
		{ "EditCondition", "bGenerateBoxes" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoxSizeAttribute_MetaData[] = {
		{ "Category", "Box Generation" },
		{ "EditCondition", "bUseAttributeForBoxSize" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SphereRadius_MetaData[] = {
		{ "Category", "Sphere Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sphere generation settings\n" },
#endif
		{ "EditCondition", "bGenerateSpheres" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sphere generation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SphereSegments_MetaData[] = {
		{ "Category", "Sphere Generation" },
		{ "EditCondition", "bGenerateSpheres" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAttributeForRadius_MetaData[] = {
		{ "Category", "Sphere Generation" },
		{ "EditCondition", "bGenerateSpheres" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RadiusAttribute_MetaData[] = {
		{ "Category", "Sphere Generation" },
		{ "EditCondition", "bUseAttributeForRadius" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeMesh_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWeldVertices_MetaData[] = {
		{ "Category", "Optimization" },
		{ "EditCondition", "bOptimizeMesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeldThreshold_MetaData[] = {
		{ "Category", "Optimization" },
		{ "EditCondition", "bOptimizeMesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRemoveDegenerateTriangles_MetaData[] = {
		{ "Category", "Optimization" },
		{ "EditCondition", "bOptimizeMesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateUVs_MetaData[] = {
		{ "Category", "UV Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UV generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVScale_MetaData[] = {
		{ "Category", "UV Generation" },
		{ "EditCondition", "bGenerateUVs" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWorldSpaceUVs_MetaData[] = {
		{ "Category", "UV Generation" },
		{ "EditCondition", "bGenerateUVs" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultMaterial_MetaData[] = {
		{ "Category", "Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePerPointMaterials_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialAttribute_MetaData[] = {
		{ "Category", "Materials" },
		{ "EditCondition", "bUsePerPointMaterials" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshDescriptors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshDescriptors;
	static void NewProp_bGenerateFromPrimitives_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateFromPrimitives;
	static void NewProp_bGenerateBoxes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateBoxes;
	static void NewProp_bGenerateSpheres_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateSpheres;
	static void NewProp_bGenerateCylinders_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateCylinders;
	static void NewProp_bGeneratePlanes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGeneratePlanes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BoxSize;
	static void NewProp_bUseAttributeForBoxSize_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAttributeForBoxSize;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BoxSizeAttribute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SphereRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SphereSegments;
	static void NewProp_bUseAttributeForRadius_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAttributeForRadius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RadiusAttribute;
	static void NewProp_bOptimizeMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeMesh;
	static void NewProp_bWeldVertices_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWeldVertices;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeldThreshold;
	static void NewProp_bRemoveDegenerateTriangles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRemoveDegenerateTriangles;
	static void NewProp_bGenerateUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateUVs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UVScale;
	static void NewProp_bUseWorldSpaceUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWorldSpaceUVs;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DefaultMaterial;
	static void NewProp_bUsePerPointMaterials_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePerPointMaterials;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGProceduralMeshCreatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_MeshDescriptors_Inner = { "MeshDescriptors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_MeshDescriptors = { "MeshDescriptors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, MeshDescriptors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshDescriptors_MetaData), NewProp_MeshDescriptors_MetaData) }; // 3203202716
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateFromPrimitives_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bGenerateFromPrimitives = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateFromPrimitives = { "bGenerateFromPrimitives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateFromPrimitives_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateFromPrimitives_MetaData), NewProp_bGenerateFromPrimitives_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateBoxes_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bGenerateBoxes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateBoxes = { "bGenerateBoxes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateBoxes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateBoxes_MetaData), NewProp_bGenerateBoxes_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateSpheres_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bGenerateSpheres = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateSpheres = { "bGenerateSpheres", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateSpheres_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateSpheres_MetaData), NewProp_bGenerateSpheres_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateCylinders_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bGenerateCylinders = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateCylinders = { "bGenerateCylinders", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateCylinders_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateCylinders_MetaData), NewProp_bGenerateCylinders_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGeneratePlanes_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bGeneratePlanes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGeneratePlanes = { "bGeneratePlanes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGeneratePlanes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGeneratePlanes_MetaData), NewProp_bGeneratePlanes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_BoxSize = { "BoxSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, BoxSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoxSize_MetaData), NewProp_BoxSize_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForBoxSize_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bUseAttributeForBoxSize = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForBoxSize = { "bUseAttributeForBoxSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForBoxSize_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAttributeForBoxSize_MetaData), NewProp_bUseAttributeForBoxSize_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_BoxSizeAttribute = { "BoxSizeAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, BoxSizeAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoxSizeAttribute_MetaData), NewProp_BoxSizeAttribute_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_SphereRadius = { "SphereRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, SphereRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SphereRadius_MetaData), NewProp_SphereRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_SphereSegments = { "SphereSegments", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, SphereSegments), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SphereSegments_MetaData), NewProp_SphereSegments_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForRadius_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bUseAttributeForRadius = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForRadius = { "bUseAttributeForRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForRadius_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAttributeForRadius_MetaData), NewProp_bUseAttributeForRadius_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_RadiusAttribute = { "RadiusAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, RadiusAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RadiusAttribute_MetaData), NewProp_RadiusAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bOptimizeMesh_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bOptimizeMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bOptimizeMesh = { "bOptimizeMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bOptimizeMesh_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeMesh_MetaData), NewProp_bOptimizeMesh_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bWeldVertices_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bWeldVertices = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bWeldVertices = { "bWeldVertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bWeldVertices_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWeldVertices_MetaData), NewProp_bWeldVertices_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_WeldThreshold = { "WeldThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, WeldThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeldThreshold_MetaData), NewProp_WeldThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bRemoveDegenerateTriangles_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bRemoveDegenerateTriangles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bRemoveDegenerateTriangles = { "bRemoveDegenerateTriangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bRemoveDegenerateTriangles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRemoveDegenerateTriangles_MetaData), NewProp_bRemoveDegenerateTriangles_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateUVs_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bGenerateUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateUVs = { "bGenerateUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateUVs_MetaData), NewProp_bGenerateUVs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_UVScale = { "UVScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, UVScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVScale_MetaData), NewProp_UVScale_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseWorldSpaceUVs_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bUseWorldSpaceUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseWorldSpaceUVs = { "bUseWorldSpaceUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseWorldSpaceUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWorldSpaceUVs_MetaData), NewProp_bUseWorldSpaceUVs_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_DefaultMaterial = { "DefaultMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, DefaultMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultMaterial_MetaData), NewProp_DefaultMaterial_MetaData) };
void Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUsePerPointMaterials_SetBit(void* Obj)
{
	((UAuracronPCGProceduralMeshCreatorSettings*)Obj)->bUsePerPointMaterials = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUsePerPointMaterials = { "bUsePerPointMaterials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGProceduralMeshCreatorSettings), &Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUsePerPointMaterials_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePerPointMaterials_MetaData), NewProp_bUsePerPointMaterials_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_MaterialAttribute = { "MaterialAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProceduralMeshCreatorSettings, MaterialAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialAttribute_MetaData), NewProp_MaterialAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_MeshDescriptors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_MeshDescriptors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateFromPrimitives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateBoxes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateSpheres,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateCylinders,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGeneratePlanes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_BoxSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForBoxSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_BoxSizeAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_SphereRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_SphereSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseAttributeForRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_RadiusAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bOptimizeMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bWeldVertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_WeldThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bRemoveDegenerateTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bGenerateUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_UVScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUseWorldSpaceUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_DefaultMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_bUsePerPointMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::NewProp_MaterialAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::ClassParams = {
	&UAuracronPCGProceduralMeshCreatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGProceduralMeshCreatorSettings);
UAuracronPCGProceduralMeshCreatorSettings::~UAuracronPCGProceduralMeshCreatorSettings() {}
// ********** End Class UAuracronPCGProceduralMeshCreatorSettings **********************************

// ********** Begin Class UAuracronPCGMeshCombinerSettings *****************************************
void UAuracronPCGMeshCombinerSettings::StaticRegisterNativesUAuracronPCGMeshCombinerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings;
UClass* UAuracronPCGMeshCombinerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGMeshCombinerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGMeshCombinerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGMeshCombinerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_NoRegister()
{
	return UAuracronPCGMeshCombinerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMeshGeneration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombineMode_MetaData[] = {
		{ "Category", "Combination" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combination settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combination settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveMaterials_MetaData[] = {
		{ "Category", "Combination" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveUVs_MetaData[] = {
		{ "Category", "Combination" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveVertexColors_MetaData[] = {
		{ "Category", "Combination" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWeldVerticesOnMerge_MetaData[] = {
		{ "Category", "Merge Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Merge settings\n" },
#endif
		{ "EditCondition", "CombineMode == EAuracronPCGMeshCombineMode::Merge" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Merge settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MergeWeldThreshold_MetaData[] = {
		{ "Category", "Merge Settings" },
		{ "EditCondition", "bWeldVerticesOnMerge" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeAfterMerge_MetaData[] = {
		{ "Category", "Merge Settings" },
		{ "EditCondition", "CombineMode == EAuracronPCGMeshCombineMode::Merge" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BooleanTolerance_MetaData[] = {
		{ "Category", "Boolean Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Boolean operation settings\n" },
#endif
		{ "EditCondition", "CombineMode == EAuracronPCGMeshCombineMode::Union || CombineMode == EAuracronPCGMeshCombineMode::Subtract || CombineMode == EAuracronPCGMeshCombineMode::Intersect" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Boolean operation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFillHoles_MetaData[] = {
		{ "Category", "Boolean Operations" },
		{ "EditCondition", "CombineMode == EAuracronPCGMeshCombineMode::Union || CombineMode == EAuracronPCGMeshCombineMode::Subtract || CombineMode == EAuracronPCGMeshCombineMode::Intersect" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationRatio_MetaData[] = {
		{ "Category", "Simplification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simplification settings\n" },
#endif
		{ "EditCondition", "CombineMode == EAuracronPCGMeshCombineMode::Simplify" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simplification settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveBoundaries_MetaData[] = {
		{ "Category", "Simplification" },
		{ "EditCondition", "CombineMode == EAuracronPCGMeshCombineMode::Simplify" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveUVBoundaries_MetaData[] = {
		{ "Category", "Simplification" },
		{ "EditCondition", "CombineMode == EAuracronPCGMeshCombineMode::Simplify" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateCollision_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMode_MetaData[] = {
		{ "Category", "Output" },
		{ "EditCondition", "bGenerateCollision" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateLightmapUVs_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightmapResolution_MetaData[] = {
		{ "Category", "Output" },
		{ "EditCondition", "bGenerateLightmapUVs" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultithreading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentOperations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "16" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CombineMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CombineMode;
	static void NewProp_bPreserveMaterials_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveMaterials;
	static void NewProp_bPreserveUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveUVs;
	static void NewProp_bPreserveVertexColors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveVertexColors;
	static void NewProp_bWeldVerticesOnMerge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWeldVerticesOnMerge;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MergeWeldThreshold;
	static void NewProp_bOptimizeAfterMerge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeAfterMerge;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BooleanTolerance;
	static void NewProp_bFillHoles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFillHoles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimplificationRatio;
	static void NewProp_bPreserveBoundaries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveBoundaries;
	static void NewProp_bPreserveUVBoundaries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveUVBoundaries;
	static void NewProp_bGenerateCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateCollision;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionMode;
	static void NewProp_bGenerateLightmapUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateLightmapUVs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LightmapResolution;
	static void NewProp_bUseMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGMeshCombinerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CombineMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CombineMode = { "CombineMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshCombinerSettings, CombineMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombineMode_MetaData), NewProp_CombineMode_MetaData) }; // 1113132920
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveMaterials_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bPreserveMaterials = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveMaterials = { "bPreserveMaterials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveMaterials_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveMaterials_MetaData), NewProp_bPreserveMaterials_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVs_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bPreserveUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVs = { "bPreserveUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveUVs_MetaData), NewProp_bPreserveUVs_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveVertexColors_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bPreserveVertexColors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveVertexColors = { "bPreserveVertexColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveVertexColors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveVertexColors_MetaData), NewProp_bPreserveVertexColors_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bWeldVerticesOnMerge_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bWeldVerticesOnMerge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bWeldVerticesOnMerge = { "bWeldVerticesOnMerge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bWeldVerticesOnMerge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWeldVerticesOnMerge_MetaData), NewProp_bWeldVerticesOnMerge_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_MergeWeldThreshold = { "MergeWeldThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshCombinerSettings, MergeWeldThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MergeWeldThreshold_MetaData), NewProp_MergeWeldThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bOptimizeAfterMerge_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bOptimizeAfterMerge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bOptimizeAfterMerge = { "bOptimizeAfterMerge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bOptimizeAfterMerge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeAfterMerge_MetaData), NewProp_bOptimizeAfterMerge_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_BooleanTolerance = { "BooleanTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshCombinerSettings, BooleanTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BooleanTolerance_MetaData), NewProp_BooleanTolerance_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bFillHoles_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bFillHoles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bFillHoles = { "bFillHoles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bFillHoles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFillHoles_MetaData), NewProp_bFillHoles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_SimplificationRatio = { "SimplificationRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshCombinerSettings, SimplificationRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationRatio_MetaData), NewProp_SimplificationRatio_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveBoundaries_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bPreserveBoundaries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveBoundaries = { "bPreserveBoundaries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveBoundaries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveBoundaries_MetaData), NewProp_bPreserveBoundaries_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVBoundaries_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bPreserveUVBoundaries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVBoundaries = { "bPreserveUVBoundaries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVBoundaries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveUVBoundaries_MetaData), NewProp_bPreserveUVBoundaries_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateCollision_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bGenerateCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateCollision = { "bGenerateCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateCollision_MetaData), NewProp_bGenerateCollision_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CollisionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CollisionMode = { "CollisionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshCombinerSettings, CollisionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMode_MetaData), NewProp_CollisionMode_MetaData) }; // 1493187721
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateLightmapUVs_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bGenerateLightmapUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateLightmapUVs = { "bGenerateLightmapUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateLightmapUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateLightmapUVs_MetaData), NewProp_bGenerateLightmapUVs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_LightmapResolution = { "LightmapResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshCombinerSettings, LightmapResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightmapResolution_MetaData), NewProp_LightmapResolution_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bUseMultithreading_SetBit(void* Obj)
{
	((UAuracronPCGMeshCombinerSettings*)Obj)->bUseMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bUseMultithreading = { "bUseMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshCombinerSettings), &Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bUseMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultithreading_MetaData), NewProp_bUseMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_MaxConcurrentOperations = { "MaxConcurrentOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshCombinerSettings, MaxConcurrentOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentOperations_MetaData), NewProp_MaxConcurrentOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CombineMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CombineMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveVertexColors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bWeldVerticesOnMerge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_MergeWeldThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bOptimizeAfterMerge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_BooleanTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bFillHoles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_SimplificationRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveBoundaries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bPreserveUVBoundaries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CollisionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_CollisionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bGenerateLightmapUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_LightmapResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_bUseMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::NewProp_MaxConcurrentOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::ClassParams = {
	&UAuracronPCGMeshCombinerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGMeshCombinerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGMeshCombinerSettings);
UAuracronPCGMeshCombinerSettings::~UAuracronPCGMeshCombinerSettings() {}
// ********** End Class UAuracronPCGMeshCombinerSettings *******************************************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function CombineMeshes *******************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms
	{
		TArray<FAuracronPCGProceduralMeshDescriptor> InputMeshes;
		FAuracronPCGProceduralMeshDescriptor OutCombinedMesh;
		EAuracronPCGMeshCombineMode CombineMode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh combination\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh combination" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputMeshes_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InputMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputMeshes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutCombinedMesh;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CombineMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CombineMode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_InputMeshes_Inner = { "InputMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_InputMeshes = { "InputMeshes", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms, InputMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputMeshes_MetaData), NewProp_InputMeshes_MetaData) }; // 3203202716
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_OutCombinedMesh = { "OutCombinedMesh", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms, OutCombinedMesh), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_CombineMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_CombineMode = { "CombineMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms, CombineMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshCombineMode, METADATA_PARAMS(0, nullptr) }; // 1113132920
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_InputMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_InputMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_OutCombinedMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_CombineMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_CombineMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "CombineMeshes", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::AuracronPCGMeshGenerationUtils_eventCombineMeshes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execCombineMeshes)
{
	P_GET_TARRAY_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_InputMeshes);
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_OutCombinedMesh);
	P_GET_ENUM(EAuracronPCGMeshCombineMode,Z_Param_CombineMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::CombineMeshes(Z_Param_Out_InputMeshes,Z_Param_Out_OutCombinedMesh,EAuracronPCGMeshCombineMode(Z_Param_CombineMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function CombineMeshes *********************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateBoxMesh *****************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateBoxMesh_Parms
	{
		FVector Size;
		FAuracronPCGProceduralMeshDescriptor OutMeshDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Procedural mesh generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural mesh generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Size_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Size;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutMeshDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_Size = { "Size", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateBoxMesh_Parms, Size), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Size_MetaData), NewProp_Size_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_OutMeshDescriptor = { "OutMeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateBoxMesh_Parms, OutMeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateBoxMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateBoxMesh_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_Size,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_OutMeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateBoxMesh", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::AuracronPCGMeshGenerationUtils_eventGenerateBoxMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::AuracronPCGMeshGenerationUtils_eventGenerateBoxMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateBoxMesh)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Size);
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_OutMeshDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateBoxMesh(Z_Param_Out_Size,Z_Param_Out_OutMeshDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateBoxMesh *******************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateCollision ***************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateCollision_Parms
	{
		UStaticMesh* StaticMesh;
		EAuracronPCGCollisionMode CollisionMode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision generation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionMode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateCollision_Parms, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_CollisionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_CollisionMode = { "CollisionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateCollision_Parms, CollisionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionMode, METADATA_PARAMS(0, nullptr) }; // 1493187721
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateCollision_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateCollision_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_CollisionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_CollisionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateCollision", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::AuracronPCGMeshGenerationUtils_eventGenerateCollision_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::AuracronPCGMeshGenerationUtils_eventGenerateCollision_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateCollision)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_StaticMesh);
	P_GET_ENUM(EAuracronPCGCollisionMode,Z_Param_CollisionMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateCollision(Z_Param_StaticMesh,EAuracronPCGCollisionMode(Z_Param_CollisionMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateCollision *****************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateCylinderMesh ************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms
	{
		float Radius;
		float Height;
		int32 Segments;
		FAuracronPCGProceduralMeshDescriptor OutMeshDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Segments;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutMeshDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_Segments = { "Segments", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms, Segments), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_OutMeshDescriptor = { "OutMeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms, OutMeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_Segments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_OutMeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateCylinderMesh", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::AuracronPCGMeshGenerationUtils_eventGenerateCylinderMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateCylinderMesh)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_GET_PROPERTY(FIntProperty,Z_Param_Segments);
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_OutMeshDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateCylinderMesh(Z_Param_Radius,Z_Param_Height,Z_Param_Segments,Z_Param_Out_OutMeshDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateCylinderMesh **************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateLODs ********************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms
	{
		UStaticMesh* StaticMesh;
		TArray<float> LODDistances;
		int32 MaxLODCount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD generation\n" },
#endif
		{ "CPP_Default_MaxLODCount", "4" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODCount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_MaxLODCount = { "MaxLODCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms, MaxLODCount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_MaxLODCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateLODs", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::AuracronPCGMeshGenerationUtils_eventGenerateLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateLODs)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_StaticMesh);
	P_GET_TARRAY_REF(float,Z_Param_Out_LODDistances);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxLODCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateLODs(Z_Param_StaticMesh,Z_Param_Out_LODDistances,Z_Param_MaxLODCount);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateLODs **********************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateNormals *****************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms
	{
		FAuracronPCGProceduralMeshDescriptor MeshDescriptor;
		bool bSmoothNormals;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "CPP_Default_bSmoothNormals", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshDescriptor;
	static void NewProp_bSmoothNormals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSmoothNormals;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_MeshDescriptor = { "MeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms, MeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_bSmoothNormals_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms*)Obj)->bSmoothNormals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_bSmoothNormals = { "bSmoothNormals", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_bSmoothNormals_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_MeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_bSmoothNormals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateNormals", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::AuracronPCGMeshGenerationUtils_eventGenerateNormals_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateNormals)
{
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_MeshDescriptor);
	P_GET_UBOOL(Z_Param_bSmoothNormals);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateNormals(Z_Param_Out_MeshDescriptor,Z_Param_bSmoothNormals);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateNormals *******************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GeneratePlaneMesh ***************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms
	{
		FVector2D Size;
		FIntPoint Subdivisions;
		FAuracronPCGProceduralMeshDescriptor OutMeshDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Size_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Subdivisions_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Size;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Subdivisions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutMeshDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_Size = { "Size", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms, Size), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Size_MetaData), NewProp_Size_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_Subdivisions = { "Subdivisions", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms, Subdivisions), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Subdivisions_MetaData), NewProp_Subdivisions_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_OutMeshDescriptor = { "OutMeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms, OutMeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_Size,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_Subdivisions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_OutMeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GeneratePlaneMesh", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::AuracronPCGMeshGenerationUtils_eventGeneratePlaneMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGeneratePlaneMesh)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_Size);
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_Subdivisions);
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_OutMeshDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GeneratePlaneMesh(Z_Param_Out_Size,Z_Param_Out_Subdivisions,Z_Param_Out_OutMeshDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GeneratePlaneMesh *****************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateSphereMesh **************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms
	{
		float Radius;
		int32 Segments;
		FAuracronPCGProceduralMeshDescriptor OutMeshDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Segments;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutMeshDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_Segments = { "Segments", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms, Segments), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_OutMeshDescriptor = { "OutMeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms, OutMeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_Segments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_OutMeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateSphereMesh", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::AuracronPCGMeshGenerationUtils_eventGenerateSphereMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateSphereMesh)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FIntProperty,Z_Param_Segments);
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_OutMeshDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateSphereMesh(Z_Param_Radius,Z_Param_Segments,Z_Param_Out_OutMeshDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateSphereMesh ****************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateTangents ****************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateTangents_Parms
	{
		FAuracronPCGProceduralMeshDescriptor MeshDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::NewProp_MeshDescriptor = { "MeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateTangents_Parms, MeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateTangents_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateTangents_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::NewProp_MeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateTangents", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::AuracronPCGMeshGenerationUtils_eventGenerateTangents_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::AuracronPCGMeshGenerationUtils_eventGenerateTangents_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateTangents)
{
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_MeshDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateTangents(Z_Param_Out_MeshDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateTangents ******************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GenerateUVs *********************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms
	{
		FAuracronPCGProceduralMeshDescriptor MeshDescriptor;
		float UVScale;
		bool bWorldSpace;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "CPP_Default_bWorldSpace", "false" },
		{ "CPP_Default_UVScale", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshDescriptor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UVScale;
	static void NewProp_bWorldSpace_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWorldSpace;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_MeshDescriptor = { "MeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms, MeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_UVScale = { "UVScale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms, UVScale), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_bWorldSpace_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms*)Obj)->bWorldSpace = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_bWorldSpace = { "bWorldSpace", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_bWorldSpace_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_MeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_UVScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_bWorldSpace,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GenerateUVs", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::AuracronPCGMeshGenerationUtils_eventGenerateUVs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGenerateUVs)
{
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_MeshDescriptor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_UVScale);
	P_GET_UBOOL(Z_Param_bWorldSpace);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GenerateUVs(Z_Param_Out_MeshDescriptor,Z_Param_UVScale,Z_Param_bWorldSpace);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GenerateUVs ***********************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function GetOptimalInstanceCount *********
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventGetOptimalInstanceCount_Parms
	{
		int32 TotalInstances;
		int32 MaxInstancesPerComponent;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance utilities\n" },
#endif
		{ "CPP_Default_MaxInstancesPerComponent", "1000" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::NewProp_TotalInstances = { "TotalInstances", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGetOptimalInstanceCount_Parms, TotalInstances), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::NewProp_MaxInstancesPerComponent = { "MaxInstancesPerComponent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGetOptimalInstanceCount_Parms, MaxInstancesPerComponent), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventGetOptimalInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::NewProp_TotalInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::NewProp_MaxInstancesPerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "GetOptimalInstanceCount", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::AuracronPCGMeshGenerationUtils_eventGetOptimalInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::AuracronPCGMeshGenerationUtils_eventGetOptimalInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execGetOptimalInstanceCount)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TotalInstances);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxInstancesPerComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::GetOptimalInstanceCount(Z_Param_TotalInstances,Z_Param_MaxInstancesPerComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function GetOptimalInstanceCount ***********

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function OptimizeMesh ********************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms
	{
		FAuracronPCGProceduralMeshDescriptor MeshDescriptor;
		bool bWeldVertices;
		float WeldThreshold;
		bool bRemoveDegenerateTriangles;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh optimization\n" },
#endif
		{ "CPP_Default_bRemoveDegenerateTriangles", "true" },
		{ "CPP_Default_bWeldVertices", "true" },
		{ "CPP_Default_WeldThreshold", "0.010000" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshDescriptor;
	static void NewProp_bWeldVertices_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWeldVertices;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeldThreshold;
	static void NewProp_bRemoveDegenerateTriangles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRemoveDegenerateTriangles;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_MeshDescriptor = { "MeshDescriptor", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms, MeshDescriptor), Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 3203202716
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bWeldVertices_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms*)Obj)->bWeldVertices = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bWeldVertices = { "bWeldVertices", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bWeldVertices_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_WeldThreshold = { "WeldThreshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms, WeldThreshold), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bRemoveDegenerateTriangles_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms*)Obj)->bRemoveDegenerateTriangles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bRemoveDegenerateTriangles = { "bRemoveDegenerateTriangles", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bRemoveDegenerateTriangles_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_MeshDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bWeldVertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_WeldThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_bRemoveDegenerateTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "OptimizeMesh", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::AuracronPCGMeshGenerationUtils_eventOptimizeMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execOptimizeMesh)
{
	P_GET_STRUCT_REF(FAuracronPCGProceduralMeshDescriptor,Z_Param_Out_MeshDescriptor);
	P_GET_UBOOL(Z_Param_bWeldVertices);
	P_GET_PROPERTY(FFloatProperty,Z_Param_WeldThreshold);
	P_GET_UBOOL(Z_Param_bRemoveDegenerateTriangles);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::OptimizeMesh(Z_Param_Out_MeshDescriptor,Z_Param_bWeldVertices,Z_Param_WeldThreshold,Z_Param_bRemoveDegenerateTriangles);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function OptimizeMesh **********************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function SelectMeshIndex *****************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms
	{
		TArray<FAuracronPCGMeshEntry> MeshEntries;
		FPCGPoint Point;
		const UPCGMetadata* Metadata;
		EAuracronPCGMeshSelectionMode SelectionMode;
		FString SelectionAttribute;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh selection utilities\n" },
#endif
		{ "CPP_Default_SelectionAttribute", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh selection utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshEntries_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionAttribute_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshEntries;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SelectionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SelectionMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectionAttribute;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_MeshEntries_Inner = { "MeshEntries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMeshEntry, METADATA_PARAMS(0, nullptr) }; // 492763619
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_MeshEntries = { "MeshEntries", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms, MeshEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshEntries_MetaData), NewProp_MeshEntries_MetaData) }; // 492763619
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_SelectionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_SelectionMode = { "SelectionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms, SelectionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSelectionMode, METADATA_PARAMS(0, nullptr) }; // 232537351
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_SelectionAttribute = { "SelectionAttribute", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms, SelectionAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionAttribute_MetaData), NewProp_SelectionAttribute_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_MeshEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_MeshEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_SelectionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_SelectionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_SelectionAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "SelectMeshIndex", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::AuracronPCGMeshGenerationUtils_eventSelectMeshIndex_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execSelectMeshIndex)
{
	P_GET_TARRAY_REF(FAuracronPCGMeshEntry,Z_Param_Out_MeshEntries);
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_ENUM(EAuracronPCGMeshSelectionMode,Z_Param_SelectionMode);
	P_GET_PROPERTY(FStrProperty,Z_Param_SelectionAttribute);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::SelectMeshIndex(Z_Param_Out_MeshEntries,Z_Param_Out_Point,Z_Param_Metadata,EAuracronPCGMeshSelectionMode(Z_Param_SelectionMode),Z_Param_SelectionAttribute);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function SelectMeshIndex *******************

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function ShouldUseHierarchicalInstancing *
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventShouldUseHierarchicalInstancing_Parms
	{
		int32 InstanceCount;
		int32 Threshold;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "CPP_Default_Threshold", "100" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Threshold;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_InstanceCount = { "InstanceCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventShouldUseHierarchicalInstancing_Parms, InstanceCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventShouldUseHierarchicalInstancing_Parms, Threshold), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventShouldUseHierarchicalInstancing_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventShouldUseHierarchicalInstancing_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_InstanceCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_Threshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "ShouldUseHierarchicalInstancing", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::AuracronPCGMeshGenerationUtils_eventShouldUseHierarchicalInstancing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::AuracronPCGMeshGenerationUtils_eventShouldUseHierarchicalInstancing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execShouldUseHierarchicalInstancing)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceCount);
	P_GET_PROPERTY(FIntProperty,Z_Param_Threshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::ShouldUseHierarchicalInstancing(Z_Param_InstanceCount,Z_Param_Threshold);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function ShouldUseHierarchicalInstancing ***

// ********** Begin Class UAuracronPCGMeshGenerationUtils Function ValidateMeshEntry ***************
struct Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics
{
	struct AuracronPCGMeshGenerationUtils_eventValidateMeshEntry_Parms
	{
		FAuracronPCGMeshEntry MeshEntry;
		FString ValidationError;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshEntry_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshEntry;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationError;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_MeshEntry = { "MeshEntry", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventValidateMeshEntry_Parms, MeshEntry), Z_Construct_UScriptStruct_FAuracronPCGMeshEntry, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshEntry_MetaData), NewProp_MeshEntry_MetaData) }; // 492763619
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_ValidationError = { "ValidationError", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMeshGenerationUtils_eventValidateMeshEntry_Parms, ValidationError), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMeshGenerationUtils_eventValidateMeshEntry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMeshGenerationUtils_eventValidateMeshEntry_Parms), &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_MeshEntry,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_ValidationError,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, nullptr, "ValidateMeshEntry", Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::AuracronPCGMeshGenerationUtils_eventValidateMeshEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::AuracronPCGMeshGenerationUtils_eventValidateMeshEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMeshGenerationUtils::execValidateMeshEntry)
{
	P_GET_STRUCT_REF(FAuracronPCGMeshEntry,Z_Param_Out_MeshEntry);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_ValidationError);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMeshGenerationUtils::ValidateMeshEntry(Z_Param_Out_MeshEntry,Z_Param_Out_ValidationError);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMeshGenerationUtils Function ValidateMeshEntry *****************

// ********** Begin Class UAuracronPCGMeshGenerationUtils ******************************************
void UAuracronPCGMeshGenerationUtils::StaticRegisterNativesUAuracronPCGMeshGenerationUtils()
{
	UClass* Class = UAuracronPCGMeshGenerationUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CombineMeshes", &UAuracronPCGMeshGenerationUtils::execCombineMeshes },
		{ "GenerateBoxMesh", &UAuracronPCGMeshGenerationUtils::execGenerateBoxMesh },
		{ "GenerateCollision", &UAuracronPCGMeshGenerationUtils::execGenerateCollision },
		{ "GenerateCylinderMesh", &UAuracronPCGMeshGenerationUtils::execGenerateCylinderMesh },
		{ "GenerateLODs", &UAuracronPCGMeshGenerationUtils::execGenerateLODs },
		{ "GenerateNormals", &UAuracronPCGMeshGenerationUtils::execGenerateNormals },
		{ "GeneratePlaneMesh", &UAuracronPCGMeshGenerationUtils::execGeneratePlaneMesh },
		{ "GenerateSphereMesh", &UAuracronPCGMeshGenerationUtils::execGenerateSphereMesh },
		{ "GenerateTangents", &UAuracronPCGMeshGenerationUtils::execGenerateTangents },
		{ "GenerateUVs", &UAuracronPCGMeshGenerationUtils::execGenerateUVs },
		{ "GetOptimalInstanceCount", &UAuracronPCGMeshGenerationUtils::execGetOptimalInstanceCount },
		{ "OptimizeMesh", &UAuracronPCGMeshGenerationUtils::execOptimizeMesh },
		{ "SelectMeshIndex", &UAuracronPCGMeshGenerationUtils::execSelectMeshIndex },
		{ "ShouldUseHierarchicalInstancing", &UAuracronPCGMeshGenerationUtils::execShouldUseHierarchicalInstancing },
		{ "ValidateMeshEntry", &UAuracronPCGMeshGenerationUtils::execValidateMeshEntry },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils;
UClass* UAuracronPCGMeshGenerationUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGMeshGenerationUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGMeshGenerationUtils"),
			Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGMeshGenerationUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_NoRegister()
{
	return UAuracronPCGMeshGenerationUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Mesh Generation Utilities\n * Utility functions for advanced mesh generation operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGMeshGeneration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMeshGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh Generation Utilities\nUtility functions for advanced mesh generation operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_CombineMeshes, "CombineMeshes" }, // 3657947637
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateBoxMesh, "GenerateBoxMesh" }, // 458450347
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCollision, "GenerateCollision" }, // 312288804
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateCylinderMesh, "GenerateCylinderMesh" }, // 102035412
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateLODs, "GenerateLODs" }, // 3725378339
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateNormals, "GenerateNormals" }, // 3958769289
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GeneratePlaneMesh, "GeneratePlaneMesh" }, // 3297365561
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateSphereMesh, "GenerateSphereMesh" }, // 1859285570
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateTangents, "GenerateTangents" }, // 1514969417
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GenerateUVs, "GenerateUVs" }, // 4252228625
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_GetOptimalInstanceCount, "GetOptimalInstanceCount" }, // 3858284389
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_OptimizeMesh, "OptimizeMesh" }, // 3865234891
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_SelectMeshIndex, "SelectMeshIndex" }, // 3148379525
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ShouldUseHierarchicalInstancing, "ShouldUseHierarchicalInstancing" }, // 1698568100
		{ &Z_Construct_UFunction_UAuracronPCGMeshGenerationUtils_ValidateMeshEntry, "ValidateMeshEntry" }, // 3921983276
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGMeshGenerationUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics::ClassParams = {
	&UAuracronPCGMeshGenerationUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGMeshGenerationUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils.OuterSingleton;
}
UAuracronPCGMeshGenerationUtils::UAuracronPCGMeshGenerationUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGMeshGenerationUtils);
UAuracronPCGMeshGenerationUtils::~UAuracronPCGMeshGenerationUtils() {}
// ********** End Class UAuracronPCGMeshGenerationUtils ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGMeshGenerationType_StaticEnum, TEXT("EAuracronPCGMeshGenerationType"), &Z_Registration_Info_UEnum_EAuracronPCGMeshGenerationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1309368601U) },
		{ EAuracronPCGMeshSelectionMode_StaticEnum, TEXT("EAuracronPCGMeshSelectionMode"), &Z_Registration_Info_UEnum_EAuracronPCGMeshSelectionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 232537351U) },
		{ EAuracronPCGMeshCombineMode_StaticEnum, TEXT("EAuracronPCGMeshCombineMode"), &Z_Registration_Info_UEnum_EAuracronPCGMeshCombineMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1113132920U) },
		{ EAuracronPCGMeshLODGenerationMode_StaticEnum, TEXT("EAuracronPCGMeshLODGenerationMode"), &Z_Registration_Info_UEnum_EAuracronPCGMeshLODGenerationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3962011530U) },
		{ EAuracronPCGCollisionMode_StaticEnum, TEXT("EAuracronPCGCollisionMode"), &Z_Registration_Info_UEnum_EAuracronPCGCollisionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1493187721U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGMeshGenerationEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics::NewStructOps, TEXT("AuracronPCGMeshGenerationEntry"), &Z_Registration_Info_UScriptStruct_FAuracronPCGMeshGenerationEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGMeshGenerationEntry), 1557237214U) },
		{ FAuracronPCGProceduralMeshDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics::NewStructOps, TEXT("AuracronPCGProceduralMeshDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGProceduralMeshDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGProceduralMeshDescriptor), 3203202716U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings, UAuracronPCGAdvancedStaticMeshSpawnerSettings::StaticClass, TEXT("UAuracronPCGAdvancedStaticMeshSpawnerSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedStaticMeshSpawnerSettings), 3996913116U) },
		{ Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings, UAuracronPCGInstancedMeshGeneratorSettings::StaticClass, TEXT("UAuracronPCGInstancedMeshGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGInstancedMeshGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGInstancedMeshGeneratorSettings), 2091182483U) },
		{ Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings, UAuracronPCGProceduralMeshCreatorSettings::StaticClass, TEXT("UAuracronPCGProceduralMeshCreatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGProceduralMeshCreatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGProceduralMeshCreatorSettings), 2526679551U) },
		{ Z_Construct_UClass_UAuracronPCGMeshCombinerSettings, UAuracronPCGMeshCombinerSettings::StaticClass, TEXT("UAuracronPCGMeshCombinerSettings"), &Z_Registration_Info_UClass_UAuracronPCGMeshCombinerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGMeshCombinerSettings), 2984469326U) },
		{ Z_Construct_UClass_UAuracronPCGMeshGenerationUtils, UAuracronPCGMeshGenerationUtils::StaticClass, TEXT("UAuracronPCGMeshGenerationUtils"), &Z_Registration_Info_UClass_UAuracronPCGMeshGenerationUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGMeshGenerationUtils), 3071949030U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_3648186276(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
