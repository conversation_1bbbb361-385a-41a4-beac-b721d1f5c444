// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Streaming System Header
// Bridge 3.3: World Partition - Streaming System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// World Partition Streaming includes for UE5.6
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/WorldPartitionLevelStreamingDynamic.h"
#include "WorldPartition/WorldPartitionStreamingGeneration.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/LevelStreaming.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Async/AsyncWork.h"
#include "Containers/Queue.h"
#include "Templates/SharedPointer.h"

#include "AuracronWorldPartitionStreaming.generated.h"

// Forward declarations
class UAuracronWorldPartitionStreamingManager;
class UAuracronStreamingPolicyManager;
class FAsyncStreamingTask;

// =============================================================================
// STREAMING TYPES AND ENUMS
// =============================================================================

// Streaming priorities
UENUM(BlueprintType)
enum class EAuracronStreamingPriority : uint8
{
    Lowest                  UMETA(DisplayName = "Lowest"),
    Low                     UMETA(DisplayName = "Low"),
    Normal                  UMETA(DisplayName = "Normal"),
    High                    UMETA(DisplayName = "High"),
    Highest                 UMETA(DisplayName = "Highest"),
    Critical                UMETA(DisplayName = "Critical")
};

// Streaming request types
UENUM(BlueprintType)
enum class EAuracronStreamingRequestType : uint8
{
    Load                    UMETA(DisplayName = "Load"),
    Unload                  UMETA(DisplayName = "Unload"),
    Preload                 UMETA(DisplayName = "Preload"),
    ForceLoad               UMETA(DisplayName = "Force Load"),
    ForceUnload             UMETA(DisplayName = "Force Unload")
};

// Streaming states
UENUM(BlueprintType)
enum class EAuracronStreamingRequestState : uint8
{
    Pending                 UMETA(DisplayName = "Pending"),
    Processing              UMETA(DisplayName = "Processing"),
    Completed               UMETA(DisplayName = "Completed"),
    Failed                  UMETA(DisplayName = "Failed"),
    Cancelled               UMETA(DisplayName = "Cancelled"),
    Timeout                 UMETA(DisplayName = "Timeout")
};

// Memory management strategies
UENUM(BlueprintType)
enum class EAuracronMemoryManagementStrategy : uint8
{
    Conservative            UMETA(DisplayName = "Conservative"),
    Balanced                UMETA(DisplayName = "Balanced"),
    Aggressive              UMETA(DisplayName = "Aggressive"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// STREAMING CONFIGURATION
// =============================================================================

/**
 * Streaming Configuration
 * Configuration settings for world partition streaming system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronStreamingConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    bool bEnableStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    bool bEnableDistanceBasedStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float StreamingDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float UnloadingDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float PreloadDistance = 20000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentStreamingRequests = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxCellsPerFrame = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float StreamingTimeSliceMs = 16.0f; // 16ms per frame

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    EAuracronMemoryManagementStrategy MemoryStrategy = EAuracronMemoryManagementStrategy::Balanced;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MaxMemoryUsageMB = 2048.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MemoryPressureThreshold = 0.8f; // 80%

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority")
    bool bEnablePrioritySystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority")
    float PriorityUpdateInterval = 1.0f; // 1 second

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    bool bEnableAsyncStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    int32 AsyncWorkerThreads = 2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableStreamingDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogStreamingOperations = false;

    FAuracronStreamingConfiguration()
    {
        bEnableStreaming = true;
        bEnableDistanceBasedStreaming = true;
        StreamingDistance = 10000.0f;
        UnloadingDistance = 15000.0f;
        PreloadDistance = 20000.0f;
        MaxConcurrentStreamingRequests = 8;
        MaxCellsPerFrame = 4;
        StreamingTimeSliceMs = 16.0f;
        MemoryStrategy = EAuracronMemoryManagementStrategy::Balanced;
        MaxMemoryUsageMB = 2048.0f;
        MemoryPressureThreshold = 0.8f;
        bEnablePrioritySystem = true;
        PriorityUpdateInterval = 1.0f;
        bEnableAsyncStreaming = true;
        AsyncWorkerThreads = 2;
        bEnableStreamingDebug = false;
        bLogStreamingOperations = false;
    }
};

// =============================================================================
// STREAMING REQUEST
// =============================================================================

/**
 * Streaming Request
 * Represents a single streaming request
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronStreamingRequest
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FString RequestId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    EAuracronStreamingRequestType RequestType = EAuracronStreamingRequestType::Load;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    EAuracronStreamingPriority Priority = EAuracronStreamingPriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    EAuracronStreamingRequestState State = EAuracronStreamingRequestState::Pending;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FVector StreamingSourceLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    float DistanceFromSource = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    float RequestTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    float ProcessingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FString ErrorMessage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FDateTime CompletionTime;

    FAuracronStreamingRequest()
    {
        RequestType = EAuracronStreamingRequestType::Load;
        Priority = EAuracronStreamingPriority::Normal;
        State = EAuracronStreamingRequestState::Pending;
        StreamingSourceLocation = FVector::ZeroVector;
        DistanceFromSource = 0.0f;
        RequestTime = 0.0f;
        ProcessingTime = 0.0f;
        CreationTime = FDateTime::Now();
    }

    // Calculate priority score based on distance and type
    float CalculatePriorityScore() const;

    // Check if request is expired
    bool IsExpired(float TimeoutSeconds) const;
};

// =============================================================================
// STREAMING SOURCE
// =============================================================================

/**
 * Streaming Source
 * Represents a source for streaming operations (player, camera, etc.)
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronStreamingSource
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    FString SourceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    FVector Velocity = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    float StreamingRadius = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    float Priority = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    bool bEnablePrediction = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    float PredictionTime = 2.0f; // 2 seconds ahead

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    FDateTime LastUpdateTime;

    FAuracronStreamingSource()
    {
        Location = FVector::ZeroVector;
        Velocity = FVector::ZeroVector;
        StreamingRadius = 10000.0f;
        Priority = 1.0f;
        bIsActive = true;
        bEnablePrediction = true;
        PredictionTime = 2.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Get predicted location based on velocity
    FVector GetPredictedLocation() const;

    // Check if location is within streaming radius
    bool IsLocationInRange(const FVector& TestLocation) const;
};

// =============================================================================
// STREAMING STATISTICS
// =============================================================================

/**
 * Streaming Statistics
 * Performance and usage statistics for streaming system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronStreamingBridgeStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalStreamingRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 CompletedRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 PendingRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageUnloadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float CurrentMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float PeakMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MemoryPressure = 0.0f; // 0.0 to 1.0

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float StreamingEfficiency = 0.0f; // Success rate

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CellsLoaded = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CellsUnloaded = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronStreamingStatistics()
    {
        TotalStreamingRequests = 0;
        CompletedRequests = 0;
        FailedRequests = 0;
        PendingRequests = 0;
        AverageLoadingTime = 0.0f;
        AverageUnloadingTime = 0.0f;
        CurrentMemoryUsageMB = 0.0f;
        PeakMemoryUsageMB = 0.0f;
        MemoryPressure = 0.0f;
        StreamingEfficiency = 0.0f;
        CellsLoaded = 0;
        CellsUnloaded = 0;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION STREAMING MANAGER
// =============================================================================

/**
 * World Partition Streaming Manager
 * Central manager for world partition streaming operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronWorldPartitionStreamingManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    static UAuracronWorldPartitionStreamingManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void Initialize(const FAuracronStreamingConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void Tick(float DeltaTime);

    // Streaming operations
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    FString RequestCellLoading(const FString& CellId, EAuracronStreamingPriority Priority = EAuracronStreamingPriority::Normal);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    FString RequestCellUnloading(const FString& CellId, EAuracronStreamingPriority Priority = EAuracronStreamingPriority::Normal);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    FString RequestCellPreloading(const FString& CellId, EAuracronStreamingPriority Priority = EAuracronStreamingPriority::Low);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    bool CancelStreamingRequest(const FString& RequestId);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void ProcessStreamingRequests();

    // Streaming sources management
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    FString AddStreamingSource(const FAuracronStreamingSource& StreamingSource);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    bool RemoveStreamingSource(const FString& SourceId);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void UpdateStreamingSource(const FString& SourceId, const FVector& Location, const FVector& Velocity);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    TArray<FAuracronStreamingSource> GetActiveStreamingSources() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void UpdateStreamingSourcesFromWorld(UWorld* World);

    // Distance-based streaming
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void UpdateDistanceBasedStreaming();

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    TArray<FString> GetCellsInStreamingRange(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    float CalculateCellPriority(const FString& CellId, const FVector& SourceLocation) const;

    // Memory management
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void UpdateMemoryManagement();

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    bool IsMemoryPressureHigh() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void ForceMemoryCleanup();

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    float GetCurrentMemoryUsage() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    float GetMemoryPressure() const;

    // Request management
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    FAuracronStreamingRequest GetStreamingRequest(const FString& RequestId) const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    TArray<FAuracronStreamingRequest> GetPendingRequests() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    TArray<FAuracronStreamingRequest> GetActiveRequests() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void ClearCompletedRequests();

    // Priority system
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void UpdateRequestPriorities();

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void SetRequestPriority(const FString& RequestId, EAuracronStreamingPriority Priority);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    TArray<FAuracronStreamingRequest> GetRequestsByPriority(EAuracronStreamingPriority Priority) const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void SetConfiguration(const FAuracronStreamingConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    FAuracronStreamingConfiguration GetConfiguration() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void SetStreamingDistance(float Distance);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    float GetStreamingDistance() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    FAuracronStreamingBridgeStatistics GetStreamingStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    int32 GetPendingRequestCount() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    int32 GetActiveRequestCount() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    float GetStreamingEfficiency() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void EnableStreamingDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    bool IsStreamingDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void DrawDebugStreamingInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Manager")
    void LogStreamingState() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCellStreamingStarted, FString, CellId, EAuracronStreamingRequestType, RequestType);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnCellStreamingCompleted, FString, CellId, EAuracronStreamingRequestType, RequestType, float, ProcessingTime);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnCellStreamingFailed, FString, CellId, EAuracronStreamingRequestType, RequestType, FString, ErrorMessage);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMemoryPressureChanged, float, MemoryPressure);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellStreamingStarted OnCellStreamingStarted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellStreamingCompleted OnCellStreamingCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellStreamingFailed OnCellStreamingFailed;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnMemoryPressureChanged OnMemoryPressureChanged;

private:
    static UAuracronWorldPartitionStreamingManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronStreamingConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Request management
    TMap<FString, FAuracronStreamingRequest> StreamingRequests;
    TQueue<FString> PendingRequestQueue;
    TSet<FString> ActiveRequests;

    // Streaming sources
    TMap<FString, FAuracronStreamingSource> StreamingSources;

    // Statistics
    FAuracronStreamingBridgeStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection StreamingLock;

    // Timing
    float LastPriorityUpdateTime = 0.0f;
    float LastMemoryUpdateTime = 0.0f;

    // Internal functions
    void ProcessSingleRequest(const FString& RequestId);
    void UpdateStatistics();
    FString GenerateRequestId() const;
    bool ShouldProcessRequest(const FAuracronStreamingRequest& Request) const;
    void OnRequestStarted(const FString& RequestId);
    void OnRequestCompleted(const FString& RequestId, bool bSuccess, const FString& ErrorMessage = TEXT(""));
    void ValidateConfiguration();
    float CalculateMemoryUsage() const;
    void HandleMemoryPressure();
    TArray<FString> GetCellsToUnloadForMemory() const;
};
