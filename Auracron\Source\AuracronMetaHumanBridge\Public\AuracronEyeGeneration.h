#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Math/Vector.h"
#include "Math/Color.h"
#include "Engine/StaticMesh.h"
#include "Engine/SkeletalMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/Texture2D.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"

// Forward declarations
class UStaticMesh;
class USkeletalMesh;
class UMaterialInterface;
class UMaterialInstanceDynamic;
class UTexture2D;

/**
 * Enumeration for different eye types
 */
UENUM(BlueprintType)
enum class EEyeType : uint8
{
    Human       UMETA(DisplayName = "Human"),
    Creature    UMETA(DisplayName = "Creature"),
    Robotic     UMETA(DisplayName = "Robotic"),
    Fantasy     UMETA(DisplayName = "Fantasy"),
    Custom      UMETA(DisplayName = "Custom")
};

/**
 * Enumeration for eye colors
 */
UENUM(BlueprintType)
enum class EEyeColor : uint8
{
    Brown       UMETA(DisplayName = "Brown"),
    Blue        UMETA(DisplayName = "Blue"),
    Green       UMETA(DisplayName = "Green"),
    Hazel       UMETA(DisplayName = "Hazel"),
    Gray        UMETA(DisplayName = "Gray"),
    Amber       UMETA(DisplayName = "Amber"),
    Red         UMETA(DisplayName = "Red"),
    Purple      UMETA(DisplayName = "Purple"),
    Custom      UMETA(DisplayName = "Custom")
};

/**
 * Structure for eye geometry data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeGeometryData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float EyeballRadius = 1.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float CorneaRadius = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float IrisRadius = 0.6f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float PupilRadius = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float CorneaHeight = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    int32 EyeballSubdivisions = 32;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    int32 CorneaSubdivisions = 16;

    FEyeGeometryData()
    {
        EyeballRadius = 1.2f;
        CorneaRadius = 0.8f;
        IrisRadius = 0.6f;
        PupilRadius = 0.2f;
        CorneaHeight = 0.1f;
        EyeballSubdivisions = 32;
        CorneaSubdivisions = 16;
    }
};

/**
 * Structure for eye material data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeMaterialData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UMaterialInterface* EyeballMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UMaterialInterface* CorneaMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* IrisTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* ScleraTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FLinearColor IrisColor = FLinearColor::Blue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FLinearColor ScleraColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float IrisRoughness = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float CorneaRoughness = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float EyeballMetallic = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float CorneaRefraction = 1.376f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float SubsurfaceScattering = 0.3f;

    FEyeMaterialData()
    {
        EyeballMaterial = nullptr;
        CorneaMaterial = nullptr;
        IrisTexture = nullptr;
        ScleraTexture = nullptr;
        IrisColor = FLinearColor::Blue;
        ScleraColor = FLinearColor::White;
        IrisRoughness = 0.1f;
        CorneaRoughness = 0.0f;
        EyeballMetallic = 0.0f;
        CorneaRefraction = 1.376f;
        SubsurfaceScattering = 0.3f;
    }
};

/**
 * Structure for eye animation data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeAnimationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableBlinking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float BlinkFrequency = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float BlinkDuration = 0.15f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableEyeTracking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float EyeTrackingSpeed = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float MaxEyeRotation = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnablePupilDilation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float PupilDilationSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float MinPupilSize = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float MaxPupilSize = 0.4f;

    FEyeAnimationData()
    {
        bEnableBlinking = true;
        BlinkFrequency = 3.0f;
        BlinkDuration = 0.15f;
        bEnableEyeTracking = true;
        EyeTrackingSpeed = 2.0f;
        MaxEyeRotation = 30.0f;
        bEnablePupilDilation = true;
        PupilDilationSpeed = 1.0f;
        MinPupilSize = 0.1f;
        MaxPupilSize = 0.4f;
    }
};

/**
 * Structure for eye generation parameters
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    FString EyeName = TEXT("DefaultEye");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    EEyeType EyeType = EEyeType::Human;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    EEyeColor EyeColor = EEyeColor::Brown;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    FEyeGeometryData GeometryData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FEyeMaterialData MaterialData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableAnimation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    FEyeAnimationData AnimationData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bGenerateLODs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxLODLevels = 3;

    FEyeGenerationParameters()
    {
        EyeName = TEXT("DefaultEye");
        EyeType = EEyeType::Human;
        EyeColor = EEyeColor::Brown;
        bEnableAnimation = true;
        bGenerateLODs = true;
        MaxLODLevels = 3;
    }
};

/**
 * Structure for generated eye data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FGeneratedEyeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UStaticMesh* EyeballMesh = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UStaticMesh* CorneaMesh = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UMaterialInstanceDynamic* EyeballMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UMaterialInstanceDynamic* CorneaMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    TArray<UStaticMesh*> LODMeshes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    FString GenerationHash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    float GenerationTime = 0.0f;

    FGeneratedEyeData()
    {
        EyeballMesh = nullptr;
        CorneaMesh = nullptr;
        EyeballMaterial = nullptr;
        CorneaMaterial = nullptr;
        GenerationTime = 0.0f;
    }
};

/**
 * Eye generation system for MetaHuman Bridge
 * Provides advanced eye generation with realistic materials and animation support
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronEyeGeneration
{
public:
    FAuracronEyeGeneration();
    ~FAuracronEyeGeneration();

    // ========================================
    // Core Eye Generation Methods
    // ========================================

    /**
     * Generate eye assets with the specified parameters
     * @param Parameters - Eye generation parameters
     * @return Generated eye data or empty struct if failed
     */
    FGeneratedEyeData GenerateEyeAssets(const FEyeGenerationParameters& Parameters);

    /**
     * Generate eye texture based on color and type
     * @param EyeColor - The desired eye color
     * @param EyeType - The type of eye
     * @param TextureSize - Size of the generated texture
     * @return Generated eye texture or nullptr if failed
     */
    UTexture2D* GenerateEyeTexture(EEyeColor EyeColor, EEyeType EyeType, int32 TextureSize = 512);

    // ========================================
    // Cache Management
    // ========================================

    /**
     * Clear the eye asset cache
     */
    void ClearEyeAssetCache();

    /**
     * Get cache memory usage in bytes
     */
    int32 GetEyeAssetCacheMemoryUsage() const { return EyeAssetCacheMemoryUsage; }

    /**
     * Get total eye generation time
     */
    float GetTotalEyeGenerationTime() const { return TotalEyeGenerationTime; }

private:
    // ========================================
    // Internal Implementation Methods
    // ========================================

    UStaticMesh* GenerateEyeballMesh(const FEyeGeometryData& GeometryData);
    UStaticMesh* GenerateCorneaMesh(const FEyeGeometryData& GeometryData);
    UMaterialInstanceDynamic* CreateEyeballMaterial(const FEyeMaterialData& MaterialData);
    UMaterialInstanceDynamic* CreateCorneaMaterial(const FEyeMaterialData& MaterialData);
    bool GenerateEyeLODs(FGeneratedEyeData& EyeData, const FEyeGenerationParameters& Parameters);

    // Helper methods
    bool ValidateEyeGenerationParameters(const FEyeGenerationParameters& Parameters, FString& OutError);
    FString CalculateEyeGenerationHash(const FEyeGenerationParameters& Parameters);
    FLinearColor GetEyeColorFromEnum(EEyeColor EyeColor);
    UMaterialInterface* GetDefaultEyeballMaterial();
    UMaterialInterface* GetDefaultCorneaMaterial();
    void UpdateEyeAssetCacheStats();
    int32 EstimateEyeAssetMemoryUsage(const FGeneratedEyeData& EyeData);
    void UpdateEyeGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess);

    // ========================================
    // Member Variables
    // ========================================

    mutable FCriticalSection EyeGenerationMutex;
    TMap<FString, FGeneratedEyeData> EyeAssetCache;
    TMap<FString, TWeakObjectPtr<UTexture2D>> EyeTextureCache;
    TMap<FString, FString> EyeGenerationStats;
    int32 EyeAssetCacheMemoryUsage;
    float TotalEyeGenerationTime;
};
