// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Collision System Implementation
// Bridge 2.10: PCG Framework - Collision e Physics

#include "AuracronPCGCollisionSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "PhysicsEngine/BodyInstance.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Async/ParallelFor.h"

// =============================================================================
// COLLISION TESTER IMPLEMENTATION
// =============================================================================

UAuracronPCGCollisionTesterSettings::UAuracronPCGCollisionTesterSettings()
{
    NodeMetadata.NodeName = TEXT("Collision Tester");
    NodeMetadata.NodeDescription = TEXT("Tests collision and physics properties for point placement validation");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Sampler;
    NodeMetadata.Tags.Add(TEXT("Collision"));
    NodeMetadata.Tags.Add(TEXT("Physics"));
    NodeMetadata.Tags.Add(TEXT("Validation"));
    NodeMetadata.Tags.Add(TEXT("Placement"));
    NodeMetadata.Tags.Add(TEXT("Testing"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.2f);
}

void UAuracronPCGCollisionTesterSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGCollisionTesterSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputHitResults)
    {
        FPCGPinProperties& HitPin = OutputPins.Emplace_GetRef();
        HitPin.Label = TEXT("Hit Results");
        HitPin.AllowedTypes = EPCGDataType::Attribute;
        HitPin.bAdvancedPin = true;
    }

    if (bOutputCollisionInfo)
    {
        FPCGPinProperties& CollisionPin = OutputPins.Emplace_GetRef();
        CollisionPin.Label = TEXT("Collision Info");
        CollisionPin.AllowedTypes = EPCGDataType::Attribute;
        CollisionPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGCollisionTesterElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                          FPCGDataCollection& OutputData, 
                                                                          const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGCollisionTesterSettings* Settings = GetTypedSettings<UAuracronPCGCollisionTesterSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Collision Tester");
            return Result;
        }

        // Get world context
        UWorld* World = GetWorld(Parameters);
        if (!World)
        {
            Result.ErrorMessage = TEXT("No valid world context for collision testing");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 ValidPlacements = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint> ValidPoints;
            ValidPoints.Reserve(InputPoints.Num());

            // Test collision for each point
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                FPCGPoint OutputPoint = InputPoint;
                bool bIsValidPlacement = TestPointPlacement(World, OutputPoint, Settings);
                
                if (bIsValidPlacement)
                {
                    ValidPlacements++;
                    ValidPoints.Add(OutputPoint);
                }
                else if (!Settings->bFilterInvalidPoints)
                {
                    // Keep invalid points but mark them
                    SetPointValidationAttribute(OutputPoint, false, Settings);
                    ValidPoints.Add(OutputPoint);
                }

                TotalProcessed++;
            }

            OutputPointData->GetMutablePoints() = ValidPoints;
            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Collision Tester processed %d points, %d valid placements"), 
                                  TotalProcessed, ValidPlacements);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Collision Tester error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGCollisionTesterElement::TestPointPlacement(UWorld* World, FPCGPoint& Point, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    FVector Location = Point.Transform.GetLocation();
    bool bValidPlacement = true;

    // Project to surface if requested
    if (Settings->bProjectToSurface)
    {
        FVector ProjectedLocation = UAuracronPCGCollisionSystemUtils::ProjectToSurface(
            World, Location, Settings->ProjectionDistance, Settings->CollisionDescriptor);
        
        if (ProjectedLocation != Location)
        {
            Point.Transform.SetLocation(ProjectedLocation);
            Location = ProjectedLocation;
        }
    }

    // Perform validation based on mode
    switch (Settings->ValidationMode)
    {
        case EAuracronPCGPlacementValidation::GroundCheck:
            bValidPlacement = ValidateGroundPlacement(World, Location, Settings);
            break;
        case EAuracronPCGPlacementValidation::ClearanceCheck:
            bValidPlacement = ValidateClearance(World, Location, Settings);
            break;
        case EAuracronPCGPlacementValidation::StabilityCheck:
            bValidPlacement = ValidateStability(World, Location, Settings);
            break;
        case EAuracronPCGPlacementValidation::AllChecks:
            bValidPlacement = ValidateGroundPlacement(World, Location, Settings) &&
                             ValidateClearance(World, Location, Settings) &&
                             ValidateStability(World, Location, Settings);
            break;
        default:
            bValidPlacement = UAuracronPCGCollisionSystemUtils::ValidatePlacement(
                World, Location, Settings->CollisionDescriptor, Settings->ValidationMode);
            break;
    }

    // Set validation attributes
    SetPointValidationAttribute(Point, bValidPlacement, Settings);

    return bValidPlacement;
}

bool FAuracronPCGCollisionTesterElement::ValidateGroundPlacement(UWorld* World, const FVector& Location, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    FHitResult HitResult;
    FVector TraceStart = Location + FVector(0.0f, 0.0f, 100.0f);
    FVector TraceEnd = Location + Settings->CollisionDescriptor.TraceDirection * Settings->ProjectionDistance;

    bool bHit = UAuracronPCGCollisionSystemUtils::PerformCollisionTest(
        World, TraceStart, TraceEnd, Settings->CollisionDescriptor, HitResult);

    if (!bHit)
    {
        return false;
    }

    // Check ground angle
    float GroundAngle = UAuracronPCGCollisionSystemUtils::CalculateGroundAngle(HitResult.Normal);
    if (GroundAngle < Settings->MinGroundAngle || GroundAngle > Settings->MaxGroundAngle)
    {
        return false;
    }

    return true;
}

bool FAuracronPCGCollisionTesterElement::ValidateClearance(UWorld* World, const FVector& Location, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    return UAuracronPCGCollisionSystemUtils::CheckClearance(
        World, Location, Settings->ClearanceRadius, Settings->ClearanceHeight, Settings->CollisionDescriptor);
}

bool FAuracronPCGCollisionTesterElement::ValidateStability(UWorld* World, const FVector& Location, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    float StabilityScore = UAuracronPCGCollisionSystemUtils::CalculateStabilityScore(
        World, Location, Settings->ClearanceRadius, 8, Settings->CollisionDescriptor);
    
    return StabilityScore >= 0.7f; // Minimum stability threshold
}

void FAuracronPCGCollisionTesterElement::SetPointValidationAttribute(FPCGPoint& Point, bool bIsValid, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    // Real implementation: Use proper UE5.6 PCG metadata system for attribute setting
    Point.Density = bIsValid ? 1.0f : 0.0f;
    
    // Set validation state using proper metadata
    if (Point.MetadataEntry != PCGInvalidEntryKey)
    {
        // Create or get metadata for validation attributes
        if (UPCGMetadata* Metadata = Point.GetMetadata())
        {
            // Set validation status attribute
            const FName ValidationAttr = TEXT("IsValid");
            if (!Metadata->HasAttribute(ValidationAttr))
            {
                Metadata->CreateBoolAttribute(ValidationAttr, false, /*bAllowsInterpolation=*/false);
            }
            Metadata->SetAttributeValue<bool>(Point.MetadataEntry, ValidationAttr, bIsValid);
            
            // Set collision test result attribute
            const FName CollisionResultAttr = TEXT("CollisionTestResult");
            if (!Metadata->HasAttribute(CollisionResultAttr))
            {
                Metadata->CreateStringAttribute(CollisionResultAttr, TEXT(""), /*bAllowsInterpolation=*/false);
            }
            FString ResultString = bIsValid ? TEXT("Valid") : TEXT("Invalid");
            Metadata->SetAttributeValue<FString>(Point.MetadataEntry, CollisionResultAttr, ResultString);
            
            // Set validation confidence score
            const FName ConfidenceAttr = TEXT("ValidationConfidence");
            if (!Metadata->HasAttribute(ConfidenceAttr))
            {
                Metadata->CreateFloatAttribute(ConfidenceAttr, 0.0f, /*bAllowsInterpolation=*/true);
            }
            float Confidence = bIsValid ? 1.0f : 0.0f;
            Metadata->SetAttributeValue<float>(Point.MetadataEntry, ConfidenceAttr, Confidence);
        }
    }
    
    // Set visual feedback colors
    if (bIsValid)
    {
        Point.Color = FVector4(0.0f, 1.0f, 0.0f, 1.0f); // Green for valid
    }
    else
    {
        Point.Color = FVector4(1.0f, 0.0f, 0.0f, 1.0f); // Red for invalid
    }
    
    // Set steepness based on validation for additional visual feedback
    Point.Steepness = bIsValid ? 0.0f : 1.0f;
}

UWorld* FAuracronPCGCollisionTesterElement::GetWorld(const FAuracronPCGElementParams& Parameters) const
{
    // Real implementation: Get world from proper UE5.6 PCG context
    if (Parameters.Context)
    {
        // Try to get world from PCG context
        if (UWorld* ContextWorld = Parameters.Context->GetWorld())
        {
            return ContextWorld;
        }
        
        // Try to get world from source component
        if (UPCGComponent* SourceComponent = Parameters.Context->GetSourceComponent())
        {
            if (UWorld* ComponentWorld = SourceComponent->GetWorld())
            {
                return ComponentWorld;
            }
            
            // Try to get world from component's owner
            if (AActor* Owner = SourceComponent->GetOwner())
            {
                if (UWorld* OwnerWorld = Owner->GetWorld())
                {
                    return OwnerWorld;
                }
            }
        }
        
        // Try to get world from input data if available
        if (Parameters.InputData && Parameters.InputData->GetInputs().Num() > 0)
        {
            for (const FPCGTaggedData& TaggedData : Parameters.InputData->GetInputs())
            {
                if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(TaggedData.Data))
                {
                    if (AActor* TargetActor = SpatialData->GetTargetActor())
                    {
                        if (UWorld* ActorWorld = TargetActor->GetWorld())
                        {
                            return ActorWorld;
                        }
                    }
                }
            }
        }
    }
    
    // Fallback to GWorld as last resort
    return GWorld;
}

// =============================================================================
// PHYSICS SIMULATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGPhysicsSimulatorSettings::UAuracronPCGPhysicsSimulatorSettings()
{
    NodeMetadata.NodeName = TEXT("Physics Simulator");
    NodeMetadata.NodeDescription = TEXT("Simulates physics properties and applies physics-based transformations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Physics"));
    NodeMetadata.Tags.Add(TEXT("Simulation"));
    NodeMetadata.Tags.Add(TEXT("Dynamics"));
    NodeMetadata.Tags.Add(TEXT("Forces"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.2f);
}

void UAuracronPCGPhysicsSimulatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bApplyInitialForce || bApplyInitialImpulse)
    {
        FPCGPinProperties& ForcePin = InputPins.Emplace_GetRef();
        ForcePin.Label = TEXT("Forces");
        ForcePin.AllowedTypes = EPCGDataType::Point;
        ForcePin.bAdvancedPin = true;
    }
}

void UAuracronPCGPhysicsSimulatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputVelocity)
    {
        FPCGPinProperties& VelocityPin = OutputPins.Emplace_GetRef();
        VelocityPin.Label = TEXT("Velocity");
        VelocityPin.AllowedTypes = EPCGDataType::Attribute;
        VelocityPin.bAdvancedPin = true;
    }

    if (bOutputPhysicsState)
    {
        FPCGPinProperties& StatePin = OutputPins.Emplace_GetRef();
        StatePin.Label = TEXT("Physics State");
        StatePin.AllowedTypes = EPCGDataType::Attribute;
        StatePin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGPhysicsSimulatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                           FPCGDataCollection& OutputData, 
                                                                           const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGPhysicsSimulatorSettings* Settings = GetTypedSettings<UAuracronPCGPhysicsSimulatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Physics Simulator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 PointsSimulated = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Simulate physics for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                if (Settings->bRunSimulation)
                {
                    SimulatePointPhysics(OutputPoint, Settings);
                    PointsSimulated++;
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Physics Simulator processed %d points, simulated %d points"), 
                                  TotalProcessed, PointsSimulated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Physics Simulator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGPhysicsSimulatorElement::SimulatePointPhysics(FPCGPoint& Point, const UAuracronPCGPhysicsSimulatorSettings* Settings) const
{
    FVector Position = Point.Transform.GetLocation();
    FVector Velocity = FVector::ZeroVector;
    FVector AngularVelocity = FVector::ZeroVector;

    // Apply initial forces/impulses
    if (Settings->bApplyInitialForce)
    {
        FVector Force = Settings->InitialForce / Settings->PhysicsDescriptor.Mass;
        Velocity += Force * Settings->TimeStep;
    }

    if (Settings->bApplyInitialImpulse)
    {
        Velocity += Settings->InitialImpulse / Settings->PhysicsDescriptor.Mass;
    }

    // Simulate physics steps
    float CurrentTime = 0.0f;
    int32 Iterations = 0;
    
    while (CurrentTime < Settings->SimulationTime && Iterations < Settings->MaxIterations)
    {
        // Apply gravity
        if (Settings->bApplyGravity)
        {
            FVector GravityForce = Settings->GravityOverride;
            if (Settings->PhysicsDescriptor.bEnableGravity)
            {
                Velocity += GravityForce * Settings->TimeStep;
            }
        }

        // Apply damping
        Velocity *= (1.0f - Settings->PhysicsDescriptor.LinearDamping * Settings->TimeStep);
        AngularVelocity *= (1.0f - Settings->PhysicsDescriptor.AngularDamping * Settings->TimeStep);

        // Update position
        Position += Velocity * Settings->TimeStep;

        // Apply constraints
        if (Settings->bUseConstraints)
        {
            ApplyConstraints(Position, Velocity, Settings);
        }

        // Check for stability
        if (Settings->bStabilizeResults && 
            UAuracronPCGCollisionSystemUtils::IsPhysicsStable(Velocity, AngularVelocity))
        {
            break;
        }

        CurrentTime += Settings->TimeStep;
        Iterations++;
    }

    // Update point transform
    Point.Transform.SetLocation(Position);
    
    // Set velocity attributes if requested using proper PCG metadata system
    if (Settings->bOutputVelocity)
    {
        if (UPCGMetadata* Metadata = Point.MetadataEntry != PCGInvalidEntryKey ? 
            const_cast<UPCGMetadata*>(Point.Metadata) : nullptr)
        {
            // Create velocity attributes
            FName VelocityXName = TEXT("VelocityX");
            FName VelocityYName = TEXT("VelocityY");
            FName VelocityZName = TEXT("VelocityZ");
            FName VelocityMagnitudeName = TEXT("VelocityMagnitude");
            
            // Create or get existing velocity attributes
            FPCGMetadataAttribute<float>* VelXAttr = Metadata->GetMutableTypedAttribute<float>(VelocityXName);
            FPCGMetadataAttribute<float>* VelYAttr = Metadata->GetMutableTypedAttribute<float>(VelocityYName);
            FPCGMetadataAttribute<float>* VelZAttr = Metadata->GetMutableTypedAttribute<float>(VelocityZName);
            FPCGMetadataAttribute<float>* VelMagAttr = Metadata->GetMutableTypedAttribute<float>(VelocityMagnitudeName);
            
            if (!VelXAttr) VelXAttr = Metadata->CreateAttribute<float>(VelocityXName, 0.0f, /*bAllowsInterpolation=*/true);
            if (!VelYAttr) VelYAttr = Metadata->CreateAttribute<float>(VelocityYName, 0.0f, /*bAllowsInterpolation=*/true);
            if (!VelZAttr) VelZAttr = Metadata->CreateAttribute<float>(VelocityZName, 0.0f, /*bAllowsInterpolation=*/true);
            if (!VelMagAttr) VelMagAttr = Metadata->CreateAttribute<float>(VelocityMagnitudeName, 0.0f, /*bAllowsInterpolation=*/true);
            
            // Store velocity components and magnitude
            if (VelXAttr && VelYAttr && VelZAttr && VelMagAttr)
            {
                VelXAttr->SetValue(Point.MetadataEntry, Velocity.X);
                VelYAttr->SetValue(Point.MetadataEntry, Velocity.Y);
                VelZAttr->SetValue(Point.MetadataEntry, Velocity.Z);
                VelMagAttr->SetValue(Point.MetadataEntry, Velocity.Size());
                
                AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Stored velocity (%.3f, %.3f, %.3f) magnitude %.3f for point at %s"),
                    Velocity.X, Velocity.Y, Velocity.Z, Velocity.Size(), *Position.ToString());
            }
        }
    }
}

void FAuracronPCGPhysicsSimulatorElement::ApplyConstraints(FVector& Position, FVector& Velocity, const UAuracronPCGPhysicsSimulatorSettings* Settings) const
{
    FVector ToCenter = Settings->ConstraintCenter - Position;
    float Distance = ToCenter.Size();
    
    if (Distance > Settings->ConstraintRadius)
    {
        // Push back inside constraint
        FVector Direction = ToCenter.GetSafeNormal();
        Position = Settings->ConstraintCenter - Direction * Settings->ConstraintRadius;
        
        if (Settings->bBounceOffConstraints)
        {
            // Reflect velocity
            FVector Normal = -Direction;
            Velocity = Velocity - 2.0f * FVector::DotProduct(Velocity, Normal) * Normal;
            Velocity *= Settings->PhysicsDescriptor.Restitution;
        }
        else
        {
            // Stop at constraint
            Velocity = FVector::ZeroVector;
        }
    }
}

// =============================================================================
// SPATIAL QUERY PROCESSOR IMPLEMENTATION
// =============================================================================

UAuracronPCGSpatialQuerySettings::UAuracronPCGSpatialQuerySettings()
{
    NodeMetadata.NodeName = TEXT("Spatial Query Processor");
    NodeMetadata.NodeDescription = TEXT("Performs spatial queries and proximity testing for point relationships");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Sampler;
    NodeMetadata.Tags.Add(TEXT("Spatial"));
    NodeMetadata.Tags.Add(TEXT("Query"));
    NodeMetadata.Tags.Add(TEXT("Proximity"));
    NodeMetadata.Tags.Add(TEXT("Relationships"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.2f, 0.8f);
}

void UAuracronPCGSpatialQuerySettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseInputAsTargets)
    {
        FPCGPinProperties& TargetPin = InputPins.Emplace_GetRef();
        TargetPin.Label = TEXT("Targets");
        TargetPin.AllowedTypes = EPCGDataType::Point;
        TargetPin.bAdvancedPin = true;
    }
}

void UAuracronPCGSpatialQuerySettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputQueryResults)
    {
        FPCGPinProperties& QueryPin = OutputPins.Emplace_GetRef();
        QueryPin.Label = TEXT("Query Results");
        QueryPin.AllowedTypes = EPCGDataType::Point;
        QueryPin.bAdvancedPin = true;
    }

    if (bOutputRelationshipData)
    {
        FPCGPinProperties& RelationshipPin = OutputPins.Emplace_GetRef();
        RelationshipPin.Label = TEXT("Relationships");
        RelationshipPin.AllowedTypes = EPCGDataType::Attribute;
        RelationshipPin.bAdvancedPin = true;
    }

    if (bOutputClusterData)
    {
        FPCGPinProperties& ClusterPin = OutputPins.Emplace_GetRef();
        ClusterPin.Label = TEXT("Clusters");
        ClusterPin.AllowedTypes = EPCGDataType::Attribute;
        ClusterPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGSpatialQueryElement::ProcessData(const FPCGDataCollection& InputData,
                                                                       FPCGDataCollection& OutputData,
                                                                       const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGSpatialQuerySettings* Settings = GetTypedSettings<UAuracronPCGSpatialQuerySettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Spatial Query Processor");
            return Result;
        }

        // Get world context
        UWorld* World = GetWorld(Parameters);
        if (!World)
        {
            Result.ErrorMessage = TEXT("No valid world context for spatial queries");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 QueriesPerformed = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Perform spatial queries for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Perform spatial query
                PerformSpatialQueryForPoint(World, OutputPoint, Settings);
                QueriesPerformed++;

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Perform clustering if requested
        if (Settings->bPerformClustering)
        {
            PerformClustering(ProcessedPointData, Settings);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Spatial Query Processor processed %d points, performed %d queries"),
                                  TotalProcessed, QueriesPerformed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Spatial Query Processor error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGSpatialQueryElement::PerformSpatialQueryForPoint(UWorld* World, FPCGPoint& Point, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    FVector Location = Point.Transform.GetLocation();

    // Perform spatial query based on type
    TArray<AActor*> FoundActors;

    if (Settings->bQueryWorldActors)
    {
        FoundActors = UAuracronPCGCollisionSystemUtils::PerformSpatialQuery(World, Location, Settings->QueryDescriptor);
    }

    // Calculate relationships if requested
    if (Settings->bAnalyzeRelationships && FoundActors.Num() > 0)
    {
        AnalyzePointRelationships(Point, FoundActors, Settings);
    }

    // Set query result attributes
    SetQueryResultAttributes(Point, FoundActors, Settings);
}

void FAuracronPCGSpatialQueryElement::AnalyzePointRelationships(FPCGPoint& Point, const TArray<AActor*>& FoundActors, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    FVector Location = Point.Transform.GetLocation();

    if (Settings->bCalculateDistances && FoundActors.Num() > 0)
    {
        // Find nearest actor
        float NearestDistance = FLT_MAX;
        for (AActor* Actor : FoundActors)
        {
            if (Actor)
            {
                float Distance = FVector::Dist(Location, Actor->GetActorLocation());
                if (Distance < NearestDistance)
                {
                    NearestDistance = Distance;
                }
            }
        }

        // Set nearest distance attribute using proper PCG metadata system
        if (UPCGMetadata* Metadata = Point.MetadataEntry != PCGInvalidEntryKey ? 
            const_cast<UPCGMetadata*>(Point.Metadata) : nullptr)
        {
            FName NearestDistanceName = TEXT("NearestDistance");
            FPCGMetadataAttribute<float>* DistanceAttr = Metadata->GetMutableTypedAttribute<float>(NearestDistanceName);
            
            if (!DistanceAttr)
            {
                DistanceAttr = Metadata->CreateAttribute<float>(NearestDistanceName, 0.0f, /*bAllowsInterpolation=*/true);
            }
            
            if (DistanceAttr)
            {
                DistanceAttr->SetValue(Point.MetadataEntry, NearestDistance);
                AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Stored nearest distance %.3f for point at %s"),
                    NearestDistance, *Point.Transform.GetLocation().ToString());
            }
        }
    }

    if (Settings->bCalculateAngles)
    {
        // Calculate angles to nearby objects
        float MinAngle = FLT_MAX;
        float MaxAngle = -FLT_MAX;
        float AvgAngle = 0.0f;
        int32 ValidAngles = 0;
        
        FVector PointLocation = Point.Transform.GetLocation();
        FVector ForwardVector = Point.Transform.GetRotation().GetForwardVector();
        
        for (const AActor* Actor : FoundActors)
        {
            if (Actor)
            {
                FVector ToActor = (Actor->GetActorLocation() - PointLocation).GetSafeNormal();
                float DotProduct = FVector::DotProduct(ForwardVector, ToActor);
                float AngleRadians = FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f));
                float AngleDegrees = FMath::RadiansToDegrees(AngleRadians);
                
                MinAngle = FMath::Min(MinAngle, AngleDegrees);
                MaxAngle = FMath::Max(MaxAngle, AngleDegrees);
                AvgAngle += AngleDegrees;
                ValidAngles++;
            }
        }
        
        if (ValidAngles > 0)
        {
            AvgAngle /= ValidAngles;
            Point.SetAttribute(TEXT("MinAngle"), MinAngle);
            Point.SetAttribute(TEXT("MaxAngle"), MaxAngle);
            Point.SetAttribute(TEXT("AvgAngle"), AvgAngle);
            Point.SetAttribute(TEXT("AngleCount"), ValidAngles);
        }
    }

    if (Settings->bCalculateVisibility)
    {
        // Check line of sight to nearby objects
        int32 VisibleObjects = 0;
        int32 OccludedObjects = 0;
        float VisibilityScore = 0.0f;
        
        FVector PointLocation = Point.Transform.GetLocation();
        UWorld* World = FoundActors.Num() > 0 ? FoundActors[0]->GetWorld() : nullptr;
        
        if (World)
        {
            for (const AActor* Actor : FoundActors)
            {
                if (Actor)
                {
                    FVector ActorLocation = Actor->GetActorLocation();
                    FVector Direction = (ActorLocation - PointLocation).GetSafeNormal();
                    float Distance = FVector::Dist(PointLocation, ActorLocation);
                    
                    // Perform line trace
                    FHitResult HitResult;
                    FCollisionQueryParams QueryParams;
                    QueryParams.AddIgnoredActor(Actor);
                    QueryParams.bTraceComplex = false;
                    
                    bool bHit = World->LineTraceSingleByChannel(
                        HitResult,
                        PointLocation,
                        ActorLocation,
                        ECC_Visibility,
                        QueryParams
                    );
                    
                    if (!bHit || HitResult.Distance >= Distance * 0.95f)
                    {
                        VisibleObjects++;
                        VisibilityScore += 1.0f;
                    }
                    else
                    {
                        OccludedObjects++;
                        // Partial visibility based on occlusion distance
                        float PartialVisibility = FMath::Clamp(HitResult.Distance / Distance, 0.0f, 1.0f);
                        VisibilityScore += PartialVisibility * 0.5f;
                    }
                }
            }
            
            int32 TotalObjects = VisibleObjects + OccludedObjects;
            if (TotalObjects > 0)
            {
                float VisibilityPercentage = (VisibilityScore / TotalObjects) * 100.0f;
                Point.SetAttribute(TEXT("VisibleObjects"), VisibleObjects);
                Point.SetAttribute(TEXT("OccludedObjects"), OccludedObjects);
                Point.SetAttribute(TEXT("VisibilityScore"), VisibilityScore);
                Point.SetAttribute(TEXT("VisibilityPercentage"), VisibilityPercentage);
            }
        }
    }
}

void FAuracronPCGSpatialQueryElement::SetQueryResultAttributes(FPCGPoint& Point, const TArray<AActor*>& FoundActors, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronPCGSpatialQueryElement::SetQueryResultAttributes);

    if (!Settings)
    {
        return;
    }

    // Get neighbor count
    int32 NeighborCount = FoundActors.Num();

    // Calculate various spatial metrics
    FVector PointLocation = Point.Transform.GetLocation();
    float TotalDistance = 0.0f;
    float MinDistance = MAX_FLT;
    float MaxDistance = 0.0f;
    float AverageDistance = 0.0f;

    // Calculate distance metrics
    for (const AActor* Actor : FoundActors)
    {
        if (Actor)
        {
            float Distance = FVector::Dist(PointLocation, Actor->GetActorLocation());
            TotalDistance += Distance;
            MinDistance = FMath::Min(MinDistance, Distance);
            MaxDistance = FMath::Max(MaxDistance, Distance);
        }
    }

    if (NeighborCount > 0)
    {
        AverageDistance = TotalDistance / static_cast<float>(NeighborCount);
        if (MinDistance == MAX_FLT)
        {
            MinDistance = 0.0f;
        }
    }

    // Set density based on neighbor count and search radius
    float SearchRadius = Settings->SearchRadius > 0.0f ? Settings->SearchRadius : 1000.0f;
    float DensityFactor = static_cast<float>(NeighborCount) / FMath::Max(1.0f, SearchRadius / 100.0f);
    Point.Density = FMath::Clamp(DensityFactor / 10.0f, 0.0f, 1.0f);

    // Set color based on density and distance metrics
    float ColorIntensity = Point.Density;
    float DistanceNormalized = AverageDistance / FMath::Max(1.0f, SearchRadius);

    // Create color gradient: Blue (low density) -> Green (medium) -> Red (high density)
    FLinearColor ResultColor;
    if (ColorIntensity < 0.5f)
    {
        // Blue to Green
        float T = ColorIntensity * 2.0f;
        ResultColor = FLinearColor::LerpUsingHSV(FLinearColor::Blue, FLinearColor::Green, T);
    }
    else
    {
        // Green to Red
        float T = (ColorIntensity - 0.5f) * 2.0f;
        ResultColor = FLinearColor::LerpUsingHSV(FLinearColor::Green, FLinearColor::Red, T);
    }

    // Adjust alpha based on distance
    ResultColor.A = FMath::Clamp(1.0f - DistanceNormalized, 0.1f, 1.0f);
    Point.Color = FVector4(ResultColor.R, ResultColor.G, ResultColor.B, ResultColor.A);

    // Set scale based on neighbor density
    float ScaleFactor = FMath::Clamp(1.0f + (Point.Density * 2.0f), 0.1f, 3.0f);
    Point.Transform.SetScale3D(FVector(ScaleFactor));

    // Set steepness based on spatial distribution
    float SpatialVariance = 0.0f;
    if (NeighborCount > 1)
    {
        for (const AActor* Actor : FoundActors)
        {
            if (Actor)
            {
                float Distance = FVector::Dist(PointLocation, Actor->GetActorLocation());
                float Deviation = Distance - AverageDistance;
                SpatialVariance += Deviation * Deviation;
            }
        }
        SpatialVariance /= static_cast<float>(NeighborCount - 1);
        SpatialVariance = FMath::Sqrt(SpatialVariance);
    }

    Point.Steepness = FMath::Clamp(SpatialVariance / SearchRadius, 0.0f, 1.0f);

    // Store additional metadata in the point's metadata if available
    if (Settings->bStoreDetailedMetrics)
    {
        // Create custom attributes for detailed analysis
        Point.SetLocalBounds(FBox(PointLocation - FVector(MinDistance), PointLocation + FVector(MaxDistance)));

        // Store neighbor count as seed for reproducible operations
        Point.Seed = NeighborCount;
    }

    // Apply custom attribute modifiers based on found actors
    if (Settings->bApplyActorTypeModifiers)
    {
        TMap<UClass*, int32> ActorTypeCounts;
        for (const AActor* Actor : FoundActors)
        {
            if (Actor)
            {
                UClass* ActorClass = Actor->GetClass();
                int32* Count = ActorTypeCounts.Find(ActorClass);
                ActorTypeCounts.Add(ActorClass, Count ? (*Count + 1) : 1);
            }
        }

        // Modify point properties based on actor type diversity
        float TypeDiversity = static_cast<float>(ActorTypeCounts.Num()) / FMath::Max(1.0f, static_cast<float>(NeighborCount));
        Point.Density *= (1.0f + TypeDiversity * 0.5f);
        Point.Density = FMath::Clamp(Point.Density, 0.0f, 1.0f);
    }

    // Apply distance-based falloff if specified
    if (Settings->bApplyDistanceFalloff && AverageDistance > 0.0f)
    {
        float FalloffFactor = 1.0f - FMath::Clamp(AverageDistance / SearchRadius, 0.0f, 1.0f);
        Point.Density *= FalloffFactor;

        // Adjust color alpha based on falloff
        Point.Color.W *= FalloffFactor;
    }
}

void FAuracronPCGSpatialQueryElement::PerformClustering(TArray<UPCGPointData*>& PointDataArray, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    // Simple clustering implementation
    for (UPCGPointData* PointData : PointDataArray)
    {
        if (!PointData)
        {
            continue;
        }

        TArray<FPCGPoint>& Points = PointData->GetMutablePoints();
        TArray<int32> ClusterIDs;
        ClusterIDs.SetNumZeroed(Points.Num());

        int32 CurrentClusterID = 0;

        for (int32 i = 0; i < Points.Num(); i++)
        {
            if (ClusterIDs[i] != 0) // Already assigned to cluster
            {
                continue;
            }

            // Start new cluster
            CurrentClusterID++;
            TArray<int32> ClusterPoints;
            ClusterPoints.Add(i);
            ClusterIDs[i] = CurrentClusterID;

            // Find nearby points for this cluster
            for (int32 j = i + 1; j < Points.Num(); j++)
            {
                if (ClusterIDs[j] != 0) // Already assigned
                {
                    continue;
                }

                float Distance = FVector::Dist(Points[i].Transform.GetLocation(), Points[j].Transform.GetLocation());
                if (Distance <= Settings->ClusterRadius)
                {
                    ClusterPoints.Add(j);
                    ClusterIDs[j] = CurrentClusterID;
                }
            }

            // Validate cluster size
            if (ClusterPoints.Num() < Settings->MinClusterSize || ClusterPoints.Num() > Settings->MaxClusterSize)
            {
                // Remove cluster assignment
                for (int32 PointIndex : ClusterPoints)
                {
                    ClusterIDs[PointIndex] = 0;
                }
                CurrentClusterID--;
            }
        }

        // Apply cluster IDs to points using proper PCG metadata system
        for (int32 i = 0; i < Points.Num(); i++)
        {
            if (ClusterIDs[i] > 0)
            {
                FPCGPoint& Point = Points[i];
                if (UPCGMetadata* Metadata = Point.MetadataEntry != PCGInvalidEntryKey ? 
                    const_cast<UPCGMetadata*>(Point.Metadata) : nullptr)
                {
                    // Create cluster ID attribute
                    FName ClusterIDName = TEXT("ClusterID");
                    FPCGMetadataAttribute<int32>* ClusterAttr = Metadata->GetMutableTypedAttribute<int32>(ClusterIDName);
                    
                    if (!ClusterAttr)
                    {
                        ClusterAttr = Metadata->CreateAttribute<int32>(ClusterIDName, 0, /*bAllowsInterpolation=*/false);
                    }
                    
                    if (ClusterAttr)
                    {
                        ClusterAttr->SetValue(Point.MetadataEntry, ClusterIDs[i]);
                        
                        // Also set visual color for debugging
                        float ClusterColorValue = static_cast<float>(ClusterIDs[i]) / static_cast<float>(CurrentClusterID);
                        Point.Color = FVector4(ClusterColorValue, ClusterColorValue, 1.0f, 1.0f);
                        
                        AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Assigned cluster ID %d to point at %s"),
                            ClusterIDs[i], *Point.Transform.GetLocation().ToString());
                    }
                }
            }
        }
    }
}

UWorld* FAuracronPCGSpatialQueryElement::GetWorld(const FAuracronPCGElementParams& Parameters) const
{
    // Get world from proper PCG context using UE5.6 APIs
    if (Parameters.Context)
    {
        if (UPCGComponent* PCGComponent = Parameters.Context->GetSourceComponent())
        {
            if (AActor* Owner = PCGComponent->GetOwner())
            {
                return Owner->GetWorld();
            }
        }
        
        // Fallback: try to get world from context's input data
        if (Parameters.Context->GetInputData().TaggedData.Num() > 0)
        {
            for (const FPCGTaggedData& TaggedData : Parameters.Context->GetInputData().TaggedData)
            {
                if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(TaggedData.Data))
                {
                    if (UWorld* World = SpatialData->GetWorld())
                    {
                        return World;
                    }
                }
            }
        }
    }
    
    // Final fallback to GWorld if context is not available
    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Could not get world from PCG context, falling back to GWorld"));
    return GWorld;
}
