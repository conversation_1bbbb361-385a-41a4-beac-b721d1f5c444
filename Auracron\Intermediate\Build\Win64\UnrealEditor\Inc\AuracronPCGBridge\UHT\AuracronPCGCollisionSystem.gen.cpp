// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGCollisionSystem.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGCollisionSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionSystemUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionTesterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSpatialQuerySettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_ECollisionChannel();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_EObjectTypeQuery();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGCollisionTestType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType;
static UEnum* EAuracronPCGCollisionTestType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCollisionTestType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCollisionTestType>()
{
	return EAuracronPCGCollisionTestType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "BoxTrace.DisplayName", "Box Trace" },
		{ "BoxTrace.Name", "EAuracronPCGCollisionTestType::BoxTrace" },
		{ "CapsuleTrace.DisplayName", "Capsule Trace" },
		{ "CapsuleTrace.Name", "EAuracronPCGCollisionTestType::CapsuleTrace" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision test types\n" },
#endif
		{ "ConvexTrace.DisplayName", "Convex Trace" },
		{ "ConvexTrace.Name", "EAuracronPCGCollisionTestType::ConvexTrace" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGCollisionTestType::Custom" },
		{ "LineTrace.DisplayName", "Line Trace" },
		{ "LineTrace.Name", "EAuracronPCGCollisionTestType::LineTrace" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
		{ "OverlapBox.DisplayName", "Overlap Box" },
		{ "OverlapBox.Name", "EAuracronPCGCollisionTestType::OverlapBox" },
		{ "OverlapCapsule.DisplayName", "Overlap Capsule" },
		{ "OverlapCapsule.Name", "EAuracronPCGCollisionTestType::OverlapCapsule" },
		{ "OverlapConvex.DisplayName", "Overlap Convex" },
		{ "OverlapConvex.Name", "EAuracronPCGCollisionTestType::OverlapConvex" },
		{ "OverlapSphere.DisplayName", "Overlap Sphere" },
		{ "OverlapSphere.Name", "EAuracronPCGCollisionTestType::OverlapSphere" },
		{ "RaycastMultiple.DisplayName", "Raycast Multiple" },
		{ "RaycastMultiple.Name", "EAuracronPCGCollisionTestType::RaycastMultiple" },
		{ "RaycastSingle.DisplayName", "Raycast Single" },
		{ "RaycastSingle.Name", "EAuracronPCGCollisionTestType::RaycastSingle" },
		{ "SphereTrace.DisplayName", "Sphere Trace" },
		{ "SphereTrace.Name", "EAuracronPCGCollisionTestType::SphereTrace" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision test types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCollisionTestType::LineTrace", (int64)EAuracronPCGCollisionTestType::LineTrace },
		{ "EAuracronPCGCollisionTestType::SphereTrace", (int64)EAuracronPCGCollisionTestType::SphereTrace },
		{ "EAuracronPCGCollisionTestType::BoxTrace", (int64)EAuracronPCGCollisionTestType::BoxTrace },
		{ "EAuracronPCGCollisionTestType::CapsuleTrace", (int64)EAuracronPCGCollisionTestType::CapsuleTrace },
		{ "EAuracronPCGCollisionTestType::ConvexTrace", (int64)EAuracronPCGCollisionTestType::ConvexTrace },
		{ "EAuracronPCGCollisionTestType::OverlapSphere", (int64)EAuracronPCGCollisionTestType::OverlapSphere },
		{ "EAuracronPCGCollisionTestType::OverlapBox", (int64)EAuracronPCGCollisionTestType::OverlapBox },
		{ "EAuracronPCGCollisionTestType::OverlapCapsule", (int64)EAuracronPCGCollisionTestType::OverlapCapsule },
		{ "EAuracronPCGCollisionTestType::OverlapConvex", (int64)EAuracronPCGCollisionTestType::OverlapConvex },
		{ "EAuracronPCGCollisionTestType::RaycastSingle", (int64)EAuracronPCGCollisionTestType::RaycastSingle },
		{ "EAuracronPCGCollisionTestType::RaycastMultiple", (int64)EAuracronPCGCollisionTestType::RaycastMultiple },
		{ "EAuracronPCGCollisionTestType::Custom", (int64)EAuracronPCGCollisionTestType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCollisionTestType",
	"EAuracronPCGCollisionTestType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType.InnerSingleton;
}
// ********** End Enum EAuracronPCGCollisionTestType ***********************************************

// ********** Begin Enum EAuracronPCGCollisionResponse *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse;
static UEnum* EAuracronPCGCollisionResponse_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCollisionResponse"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCollisionResponse>()
{
	return EAuracronPCGCollisionResponse_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Block.DisplayName", "Block" },
		{ "Block.Name", "EAuracronPCGCollisionResponse::Block" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision response types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGCollisionResponse::Custom" },
		{ "Ignore.DisplayName", "Ignore" },
		{ "Ignore.Name", "EAuracronPCGCollisionResponse::Ignore" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
		{ "Overlap.DisplayName", "Overlap" },
		{ "Overlap.Name", "EAuracronPCGCollisionResponse::Overlap" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision response types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCollisionResponse::Ignore", (int64)EAuracronPCGCollisionResponse::Ignore },
		{ "EAuracronPCGCollisionResponse::Overlap", (int64)EAuracronPCGCollisionResponse::Overlap },
		{ "EAuracronPCGCollisionResponse::Block", (int64)EAuracronPCGCollisionResponse::Block },
		{ "EAuracronPCGCollisionResponse::Custom", (int64)EAuracronPCGCollisionResponse::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCollisionResponse",
	"EAuracronPCGCollisionResponse",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse.InnerSingleton;
}
// ********** End Enum EAuracronPCGCollisionResponse ***********************************************

// ********** Begin Enum EAuracronPCGPhysicsSimulationType *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType;
static UEnum* EAuracronPCGPhysicsSimulationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPhysicsSimulationType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPhysicsSimulationType>()
{
	return EAuracronPCGPhysicsSimulationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics simulation types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGPhysicsSimulationType::Custom" },
		{ "Dynamic.DisplayName", "Dynamic" },
		{ "Dynamic.Name", "EAuracronPCGPhysicsSimulationType::Dynamic" },
		{ "Kinematic.DisplayName", "Kinematic" },
		{ "Kinematic.Name", "EAuracronPCGPhysicsSimulationType::Kinematic" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGPhysicsSimulationType::None" },
		{ "Simulated.DisplayName", "Simulated" },
		{ "Simulated.Name", "EAuracronPCGPhysicsSimulationType::Simulated" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronPCGPhysicsSimulationType::Static" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics simulation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPhysicsSimulationType::None", (int64)EAuracronPCGPhysicsSimulationType::None },
		{ "EAuracronPCGPhysicsSimulationType::Static", (int64)EAuracronPCGPhysicsSimulationType::Static },
		{ "EAuracronPCGPhysicsSimulationType::Kinematic", (int64)EAuracronPCGPhysicsSimulationType::Kinematic },
		{ "EAuracronPCGPhysicsSimulationType::Dynamic", (int64)EAuracronPCGPhysicsSimulationType::Dynamic },
		{ "EAuracronPCGPhysicsSimulationType::Simulated", (int64)EAuracronPCGPhysicsSimulationType::Simulated },
		{ "EAuracronPCGPhysicsSimulationType::Custom", (int64)EAuracronPCGPhysicsSimulationType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPhysicsSimulationType",
	"EAuracronPCGPhysicsSimulationType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType.InnerSingleton;
}
// ********** End Enum EAuracronPCGPhysicsSimulationType *******************************************

// ********** Begin Enum EAuracronPCGSpatialQueryType **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType;
static UEnum* EAuracronPCGSpatialQueryType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSpatialQueryType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSpatialQueryType>()
{
	return EAuracronPCGSpatialQueryType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Accessibility.DisplayName", "Accessibility" },
		{ "Accessibility.Name", "EAuracronPCGSpatialQueryType::Accessibility" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial query types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGSpatialQueryType::Custom" },
		{ "LineOfSight.DisplayName", "Line of Sight" },
		{ "LineOfSight.Name", "EAuracronPCGSpatialQueryType::LineOfSight" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
		{ "Nearest.DisplayName", "Nearest" },
		{ "Nearest.Name", "EAuracronPCGSpatialQueryType::Nearest" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial query types" },
#endif
		{ "Visibility.DisplayName", "Visibility" },
		{ "Visibility.Name", "EAuracronPCGSpatialQueryType::Visibility" },
		{ "WithinBox.DisplayName", "Within Box" },
		{ "WithinBox.Name", "EAuracronPCGSpatialQueryType::WithinBox" },
		{ "WithinCone.DisplayName", "Within Cone" },
		{ "WithinCone.Name", "EAuracronPCGSpatialQueryType::WithinCone" },
		{ "WithinRadius.DisplayName", "Within Radius" },
		{ "WithinRadius.Name", "EAuracronPCGSpatialQueryType::WithinRadius" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSpatialQueryType::Nearest", (int64)EAuracronPCGSpatialQueryType::Nearest },
		{ "EAuracronPCGSpatialQueryType::WithinRadius", (int64)EAuracronPCGSpatialQueryType::WithinRadius },
		{ "EAuracronPCGSpatialQueryType::WithinBox", (int64)EAuracronPCGSpatialQueryType::WithinBox },
		{ "EAuracronPCGSpatialQueryType::WithinCone", (int64)EAuracronPCGSpatialQueryType::WithinCone },
		{ "EAuracronPCGSpatialQueryType::LineOfSight", (int64)EAuracronPCGSpatialQueryType::LineOfSight },
		{ "EAuracronPCGSpatialQueryType::Visibility", (int64)EAuracronPCGSpatialQueryType::Visibility },
		{ "EAuracronPCGSpatialQueryType::Accessibility", (int64)EAuracronPCGSpatialQueryType::Accessibility },
		{ "EAuracronPCGSpatialQueryType::Custom", (int64)EAuracronPCGSpatialQueryType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSpatialQueryType",
	"EAuracronPCGSpatialQueryType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType.InnerSingleton;
}
// ********** End Enum EAuracronPCGSpatialQueryType ************************************************

// ********** Begin Enum EAuracronPCGPlacementValidation *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation;
static UEnum* EAuracronPCGPlacementValidation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPlacementValidation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPlacementValidation>()
{
	return EAuracronPCGPlacementValidation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AccessibilityCheck.DisplayName", "Accessibility Check" },
		{ "AccessibilityCheck.Name", "EAuracronPCGPlacementValidation::AccessibilityCheck" },
		{ "AllChecks.DisplayName", "All Checks" },
		{ "AllChecks.Name", "EAuracronPCGPlacementValidation::AllChecks" },
		{ "BlueprintType", "true" },
		{ "ClearanceCheck.DisplayName", "Clearance Check" },
		{ "ClearanceCheck.Name", "EAuracronPCGPlacementValidation::ClearanceCheck" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Placement validation modes\n" },
#endif
		{ "CustomCheck.DisplayName", "Custom Check" },
		{ "CustomCheck.Name", "EAuracronPCGPlacementValidation::CustomCheck" },
		{ "GroundCheck.DisplayName", "Ground Check" },
		{ "GroundCheck.Name", "EAuracronPCGPlacementValidation::GroundCheck" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGPlacementValidation::None" },
		{ "ProximityCheck.DisplayName", "Proximity Check" },
		{ "ProximityCheck.Name", "EAuracronPCGPlacementValidation::ProximityCheck" },
		{ "StabilityCheck.DisplayName", "Stability Check" },
		{ "StabilityCheck.Name", "EAuracronPCGPlacementValidation::StabilityCheck" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Placement validation modes" },
#endif
		{ "VisibilityCheck.DisplayName", "Visibility Check" },
		{ "VisibilityCheck.Name", "EAuracronPCGPlacementValidation::VisibilityCheck" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPlacementValidation::None", (int64)EAuracronPCGPlacementValidation::None },
		{ "EAuracronPCGPlacementValidation::GroundCheck", (int64)EAuracronPCGPlacementValidation::GroundCheck },
		{ "EAuracronPCGPlacementValidation::ClearanceCheck", (int64)EAuracronPCGPlacementValidation::ClearanceCheck },
		{ "EAuracronPCGPlacementValidation::StabilityCheck", (int64)EAuracronPCGPlacementValidation::StabilityCheck },
		{ "EAuracronPCGPlacementValidation::AccessibilityCheck", (int64)EAuracronPCGPlacementValidation::AccessibilityCheck },
		{ "EAuracronPCGPlacementValidation::VisibilityCheck", (int64)EAuracronPCGPlacementValidation::VisibilityCheck },
		{ "EAuracronPCGPlacementValidation::ProximityCheck", (int64)EAuracronPCGPlacementValidation::ProximityCheck },
		{ "EAuracronPCGPlacementValidation::CustomCheck", (int64)EAuracronPCGPlacementValidation::CustomCheck },
		{ "EAuracronPCGPlacementValidation::AllChecks", (int64)EAuracronPCGPlacementValidation::AllChecks },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPlacementValidation",
	"EAuracronPCGPlacementValidation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation.InnerSingleton;
}
// ********** End Enum EAuracronPCGPlacementValidation *********************************************

// ********** Begin ScriptStruct FAuracronPCGCollisionDescriptor ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor;
class UScriptStruct* FAuracronPCGCollisionDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGCollisionDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision Descriptor\n * Describes parameters for collision detection and physics simulation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Descriptor\nDescribes parameters for collision detection and physics simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionTestType_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionChannel_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionResponse_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TraceDistance_MetaData[] = {
		{ "Category", "Trace Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TraceDirection_MetaData[] = {
		{ "Category", "Trace Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SphereRadius_MetaData[] = {
		{ "Category", "Shape Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoxExtent_MetaData[] = {
		{ "Category", "Shape Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CapsuleRadius_MetaData[] = {
		{ "Category", "Shape Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CapsuleHalfHeight_MetaData[] = {
		{ "Category", "Shape Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTraceComplex_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoreSelf_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReturnPhysicalMaterial_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectTypes_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorsToIgnore_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByTag_MetaData[] = {
		{ "Category", "Filtering" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByTag" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByClass_MetaData[] = {
		{ "Category", "Filtering" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedClasses_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByClass" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionTestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionTestType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionChannel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionResponse_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionResponse;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TraceDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TraceDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SphereRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BoxExtent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CapsuleRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CapsuleHalfHeight;
	static void NewProp_bTraceComplex_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTraceComplex;
	static void NewProp_bIgnoreSelf_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoreSelf;
	static void NewProp_bReturnPhysicalMaterial_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReturnPhysicalMaterial;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObjectTypes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorsToIgnore_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActorsToIgnore;
	static void NewProp_bFilterByTag_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByTag;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredTags;
	static void NewProp_bFilterByClass_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AllowedClasses_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedClasses;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGCollisionDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionTestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionTestType = { "CollisionTestType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, CollisionTestType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionTestType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionTestType_MetaData), NewProp_CollisionTestType_MetaData) }; // 3784044577
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionChannel = { "CollisionChannel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, CollisionChannel), Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionChannel_MetaData), NewProp_CollisionChannel_MetaData) }; // 756624936
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionResponse_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionResponse = { "CollisionResponse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, CollisionResponse), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCollisionResponse, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionResponse_MetaData), NewProp_CollisionResponse_MetaData) }; // 567043474
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_TraceDistance = { "TraceDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, TraceDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TraceDistance_MetaData), NewProp_TraceDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_TraceDirection = { "TraceDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, TraceDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TraceDirection_MetaData), NewProp_TraceDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_SphereRadius = { "SphereRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, SphereRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SphereRadius_MetaData), NewProp_SphereRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_BoxExtent = { "BoxExtent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, BoxExtent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoxExtent_MetaData), NewProp_BoxExtent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CapsuleRadius = { "CapsuleRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, CapsuleRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CapsuleRadius_MetaData), NewProp_CapsuleRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CapsuleHalfHeight = { "CapsuleHalfHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, CapsuleHalfHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CapsuleHalfHeight_MetaData), NewProp_CapsuleHalfHeight_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bTraceComplex_SetBit(void* Obj)
{
	((FAuracronPCGCollisionDescriptor*)Obj)->bTraceComplex = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bTraceComplex = { "bTraceComplex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCollisionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bTraceComplex_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTraceComplex_MetaData), NewProp_bTraceComplex_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bIgnoreSelf_SetBit(void* Obj)
{
	((FAuracronPCGCollisionDescriptor*)Obj)->bIgnoreSelf = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bIgnoreSelf = { "bIgnoreSelf", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCollisionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bIgnoreSelf_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoreSelf_MetaData), NewProp_bIgnoreSelf_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bReturnPhysicalMaterial_SetBit(void* Obj)
{
	((FAuracronPCGCollisionDescriptor*)Obj)->bReturnPhysicalMaterial = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bReturnPhysicalMaterial = { "bReturnPhysicalMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCollisionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bReturnPhysicalMaterial_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReturnPhysicalMaterial_MetaData), NewProp_bReturnPhysicalMaterial_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ObjectTypes_Inner = { "ObjectTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_EObjectTypeQuery, METADATA_PARAMS(0, nullptr) }; // 1798967895
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ObjectTypes = { "ObjectTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, ObjectTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectTypes_MetaData), NewProp_ObjectTypes_MetaData) }; // 1798967895
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ActorsToIgnore_Inner = { "ActorsToIgnore", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ActorsToIgnore = { "ActorsToIgnore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, ActorsToIgnore), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorsToIgnore_MetaData), NewProp_ActorsToIgnore_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByTag_SetBit(void* Obj)
{
	((FAuracronPCGCollisionDescriptor*)Obj)->bFilterByTag = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByTag = { "bFilterByTag", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCollisionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByTag_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByTag_MetaData), NewProp_bFilterByTag_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_RequiredTags_Inner = { "RequiredTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, RequiredTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByClass_SetBit(void* Obj)
{
	((FAuracronPCGCollisionDescriptor*)Obj)->bFilterByClass = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByClass = { "bFilterByClass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCollisionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByClass_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByClass_MetaData), NewProp_bFilterByClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_AllowedClasses_Inner = { "AllowedClasses", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_AllowedClasses = { "AllowedClasses", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCollisionDescriptor, AllowedClasses), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedClasses_MetaData), NewProp_AllowedClasses_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionTestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionTestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionChannel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionResponse_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CollisionResponse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_TraceDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_TraceDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_SphereRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_BoxExtent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CapsuleRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_CapsuleHalfHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bTraceComplex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bIgnoreSelf,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bReturnPhysicalMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ObjectTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ObjectTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ActorsToIgnore_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_ActorsToIgnore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_RequiredTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_bFilterByClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_AllowedClasses_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewProp_AllowedClasses,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGCollisionDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGCollisionDescriptor),
	alignof(FAuracronPCGCollisionDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGCollisionDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGPhysicsDescriptor *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor;
class UScriptStruct* FAuracronPCGPhysicsDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGPhysicsDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Physics Descriptor\n * Describes parameters for physics simulation and body setup\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Descriptor\nDescribes parameters for physics simulation and body setup" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationType_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGravity_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mass_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinearDamping_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularDamping_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Friction_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Restitution_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLockXTranslation_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLockYTranslation_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLockZTranslation_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLockXRotation_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLockYRotation_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLockZRotation_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStartAwake_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateWakeEvents_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SleepThresholdMultiplier_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StabilizationThresholdMultiplier_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SimulationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SimulationType;
	static void NewProp_bEnableGravity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGravity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LinearDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngularDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Friction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Restitution;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static void NewProp_bLockXTranslation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLockXTranslation;
	static void NewProp_bLockYTranslation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLockYTranslation;
	static void NewProp_bLockZTranslation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLockZTranslation;
	static void NewProp_bLockXRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLockXRotation;
	static void NewProp_bLockYRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLockYRotation;
	static void NewProp_bLockZRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLockZRotation;
	static void NewProp_bStartAwake_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStartAwake;
	static void NewProp_bGenerateWakeEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateWakeEvents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SleepThresholdMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StabilizationThresholdMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGPhysicsDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_SimulationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_SimulationType = { "SimulationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, SimulationType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPhysicsSimulationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationType_MetaData), NewProp_SimulationType_MetaData) }; // 600303698
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bEnableGravity_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bEnableGravity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bEnableGravity = { "bEnableGravity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bEnableGravity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGravity_MetaData), NewProp_bEnableGravity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Mass = { "Mass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, Mass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mass_MetaData), NewProp_Mass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_LinearDamping = { "LinearDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, LinearDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinearDamping_MetaData), NewProp_LinearDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_AngularDamping = { "AngularDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, AngularDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularDamping_MetaData), NewProp_AngularDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Friction = { "Friction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, Friction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Friction_MetaData), NewProp_Friction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Restitution = { "Restitution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, Restitution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Restitution_MetaData), NewProp_Restitution_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXTranslation_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bLockXTranslation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXTranslation = { "bLockXTranslation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXTranslation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLockXTranslation_MetaData), NewProp_bLockXTranslation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYTranslation_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bLockYTranslation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYTranslation = { "bLockYTranslation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYTranslation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLockYTranslation_MetaData), NewProp_bLockYTranslation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZTranslation_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bLockZTranslation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZTranslation = { "bLockZTranslation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZTranslation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLockZTranslation_MetaData), NewProp_bLockZTranslation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXRotation_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bLockXRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXRotation = { "bLockXRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLockXRotation_MetaData), NewProp_bLockXRotation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYRotation_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bLockYRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYRotation = { "bLockYRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLockYRotation_MetaData), NewProp_bLockYRotation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZRotation_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bLockZRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZRotation = { "bLockZRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLockZRotation_MetaData), NewProp_bLockZRotation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bStartAwake_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bStartAwake = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bStartAwake = { "bStartAwake", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bStartAwake_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStartAwake_MetaData), NewProp_bStartAwake_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bGenerateWakeEvents_SetBit(void* Obj)
{
	((FAuracronPCGPhysicsDescriptor*)Obj)->bGenerateWakeEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bGenerateWakeEvents = { "bGenerateWakeEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPhysicsDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bGenerateWakeEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateWakeEvents_MetaData), NewProp_bGenerateWakeEvents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_SleepThresholdMultiplier = { "SleepThresholdMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, SleepThresholdMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SleepThresholdMultiplier_MetaData), NewProp_SleepThresholdMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_StabilizationThresholdMultiplier = { "StabilizationThresholdMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPhysicsDescriptor, StabilizationThresholdMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StabilizationThresholdMultiplier_MetaData), NewProp_StabilizationThresholdMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_SimulationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_SimulationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bEnableGravity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Mass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_LinearDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_AngularDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Friction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Restitution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXTranslation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYTranslation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZTranslation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockXRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockYRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bLockZRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bStartAwake,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_bGenerateWakeEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_SleepThresholdMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewProp_StabilizationThresholdMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGPhysicsDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGPhysicsDescriptor),
	alignof(FAuracronPCGPhysicsDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGPhysicsDescriptor ***************************************

// ********** Begin ScriptStruct FAuracronPCGSpatialQueryDescriptor ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor;
class UScriptStruct* FAuracronPCGSpatialQueryDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGSpatialQueryDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spatial Query Descriptor\n * Describes parameters for spatial queries and proximity testing\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial Query Descriptor\nDescribes parameters for spatial queries and proximity testing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryType_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchRadius_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchExtent_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConeAngle_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConeDirection_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxResults_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSortByDistance_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByType_MetaData[] = {
		{ "Category", "Filtering" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetTypes_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByType" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByAttribute_MetaData[] = {
		{ "Category", "Filtering" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByAttribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeValue_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByAttribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseComplexCollision_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoreBlockingHits_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QueryType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QueryType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SearchRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SearchExtent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConeAngle;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConeDirection;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static void NewProp_bSortByDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSortByDistance;
	static void NewProp_bFilterByType_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByType;
	static const UECodeGen_Private::FClassPropertyParams NewProp_TargetTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetTypes;
	static void NewProp_bFilterByAttribute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeValue;
	static void NewProp_bUseComplexCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseComplexCollision;
	static void NewProp_bIgnoreBlockingHits_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoreBlockingHits;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGSpatialQueryDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_QueryType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_QueryType = { "QueryType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, QueryType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSpatialQueryType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryType_MetaData), NewProp_QueryType_MetaData) }; // 3858762667
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_SearchRadius = { "SearchRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, SearchRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchRadius_MetaData), NewProp_SearchRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_SearchExtent = { "SearchExtent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, SearchExtent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchExtent_MetaData), NewProp_SearchExtent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_ConeAngle = { "ConeAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, ConeAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConeAngle_MetaData), NewProp_ConeAngle_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_ConeDirection = { "ConeDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, ConeDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConeDirection_MetaData), NewProp_ConeDirection_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, MaxResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxResults_MetaData), NewProp_MaxResults_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bSortByDistance_SetBit(void* Obj)
{
	((FAuracronPCGSpatialQueryDescriptor*)Obj)->bSortByDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bSortByDistance = { "bSortByDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSpatialQueryDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bSortByDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSortByDistance_MetaData), NewProp_bSortByDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByType_SetBit(void* Obj)
{
	((FAuracronPCGSpatialQueryDescriptor*)Obj)->bFilterByType = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByType = { "bFilterByType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSpatialQueryDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByType_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByType_MetaData), NewProp_bFilterByType_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_TargetTypes_Inner = { "TargetTypes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_TargetTypes = { "TargetTypes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, TargetTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetTypes_MetaData), NewProp_TargetTypes_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByAttribute_SetBit(void* Obj)
{
	((FAuracronPCGSpatialQueryDescriptor*)Obj)->bFilterByAttribute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByAttribute = { "bFilterByAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSpatialQueryDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByAttribute_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByAttribute_MetaData), NewProp_bFilterByAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_AttributeValue = { "AttributeValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSpatialQueryDescriptor, AttributeValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeValue_MetaData), NewProp_AttributeValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bUseComplexCollision_SetBit(void* Obj)
{
	((FAuracronPCGSpatialQueryDescriptor*)Obj)->bUseComplexCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bUseComplexCollision = { "bUseComplexCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSpatialQueryDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bUseComplexCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseComplexCollision_MetaData), NewProp_bUseComplexCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bIgnoreBlockingHits_SetBit(void* Obj)
{
	((FAuracronPCGSpatialQueryDescriptor*)Obj)->bIgnoreBlockingHits = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bIgnoreBlockingHits = { "bIgnoreBlockingHits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSpatialQueryDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bIgnoreBlockingHits_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoreBlockingHits_MetaData), NewProp_bIgnoreBlockingHits_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_QueryType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_QueryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_SearchRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_SearchExtent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_ConeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_ConeDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_MaxResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bSortByDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_TargetTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_TargetTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bFilterByAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_AttributeValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bUseComplexCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewProp_bIgnoreBlockingHits,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGSpatialQueryDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGSpatialQueryDescriptor),
	alignof(FAuracronPCGSpatialQueryDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGSpatialQueryDescriptor **********************************

// ********** Begin Class UAuracronPCGCollisionTesterSettings **************************************
void UAuracronPCGCollisionTesterSettings::StaticRegisterNativesUAuracronPCGCollisionTesterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings;
UClass* UAuracronPCGCollisionTesterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCollisionTesterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCollisionTesterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCollisionTesterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_NoRegister()
{
	return UAuracronPCGCollisionTesterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGCollisionSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "Category", "Collision" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationMode_MetaData[] = {
		{ "Category", "Placement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Placement validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Placement validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProjectToSurface_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectionDistance_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinGroundAngle_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGroundAngle_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClearanceRadius_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClearanceHeight_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputHitResults_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputCollisionInfo_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterInvalidPoints_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitDistanceAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitNormalAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidPlacementAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationMode;
	static void NewProp_bProjectToSurface_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProjectToSurface;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProjectionDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinGroundAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxGroundAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClearanceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClearanceHeight;
	static void NewProp_bOutputHitResults_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputHitResults;
	static void NewProp_bOutputCollisionInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputCollisionInfo;
	static void NewProp_bFilterInvalidPoints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterInvalidPoints;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HitDistanceAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HitNormalAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidPlacementAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCollisionTesterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ValidationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ValidationMode = { "ValidationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, ValidationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationMode_MetaData), NewProp_ValidationMode_MetaData) }; // 2543609643
void Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bProjectToSurface_SetBit(void* Obj)
{
	((UAuracronPCGCollisionTesterSettings*)Obj)->bProjectToSurface = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bProjectToSurface = { "bProjectToSurface", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGCollisionTesterSettings), &Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bProjectToSurface_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProjectToSurface_MetaData), NewProp_bProjectToSurface_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ProjectionDistance = { "ProjectionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, ProjectionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectionDistance_MetaData), NewProp_ProjectionDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_MinGroundAngle = { "MinGroundAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, MinGroundAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinGroundAngle_MetaData), NewProp_MinGroundAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_MaxGroundAngle = { "MaxGroundAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, MaxGroundAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGroundAngle_MetaData), NewProp_MaxGroundAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ClearanceRadius = { "ClearanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, ClearanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClearanceRadius_MetaData), NewProp_ClearanceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ClearanceHeight = { "ClearanceHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, ClearanceHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClearanceHeight_MetaData), NewProp_ClearanceHeight_MetaData) };
void Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputHitResults_SetBit(void* Obj)
{
	((UAuracronPCGCollisionTesterSettings*)Obj)->bOutputHitResults = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputHitResults = { "bOutputHitResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGCollisionTesterSettings), &Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputHitResults_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputHitResults_MetaData), NewProp_bOutputHitResults_MetaData) };
void Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputCollisionInfo_SetBit(void* Obj)
{
	((UAuracronPCGCollisionTesterSettings*)Obj)->bOutputCollisionInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputCollisionInfo = { "bOutputCollisionInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGCollisionTesterSettings), &Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputCollisionInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputCollisionInfo_MetaData), NewProp_bOutputCollisionInfo_MetaData) };
void Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bFilterInvalidPoints_SetBit(void* Obj)
{
	((UAuracronPCGCollisionTesterSettings*)Obj)->bFilterInvalidPoints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bFilterInvalidPoints = { "bFilterInvalidPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGCollisionTesterSettings), &Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bFilterInvalidPoints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterInvalidPoints_MetaData), NewProp_bFilterInvalidPoints_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_HitDistanceAttribute = { "HitDistanceAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, HitDistanceAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitDistanceAttribute_MetaData), NewProp_HitDistanceAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_HitNormalAttribute = { "HitNormalAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, HitNormalAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitNormalAttribute_MetaData), NewProp_HitNormalAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ValidPlacementAttribute = { "ValidPlacementAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCollisionTesterSettings, ValidPlacementAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidPlacementAttribute_MetaData), NewProp_ValidPlacementAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ValidationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ValidationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bProjectToSurface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ProjectionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_MinGroundAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_MaxGroundAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ClearanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ClearanceHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputHitResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bOutputCollisionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_bFilterInvalidPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_HitDistanceAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_HitNormalAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::NewProp_ValidPlacementAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::ClassParams = {
	&UAuracronPCGCollisionTesterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCollisionTesterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCollisionTesterSettings);
UAuracronPCGCollisionTesterSettings::~UAuracronPCGCollisionTesterSettings() {}
// ********** End Class UAuracronPCGCollisionTesterSettings ****************************************

// ********** Begin Class UAuracronPCGPhysicsSimulatorSettings *************************************
void UAuracronPCGPhysicsSimulatorSettings::StaticRegisterNativesUAuracronPCGPhysicsSimulatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings;
UClass* UAuracronPCGPhysicsSimulatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPhysicsSimulatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPhysicsSimulatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPhysicsSimulatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_NoRegister()
{
	return UAuracronPCGPhysicsSimulatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGCollisionSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsDescriptor_MetaData[] = {
		{ "Category", "Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRunSimulation_MetaData[] = {
		{ "Category", "Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simulation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simulation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationTime_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeStep_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxIterations_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStabilizeResults_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bApplyInitialForce_MetaData[] = {
		{ "Category", "Forces" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Forces and impulses\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Forces and impulses" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialForce_MetaData[] = {
		{ "Category", "Forces" },
		{ "EditCondition", "bApplyInitialForce" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bApplyInitialImpulse_MetaData[] = {
		{ "Category", "Forces" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialImpulse_MetaData[] = {
		{ "Category", "Forces" },
		{ "EditCondition", "bApplyInitialImpulse" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bApplyGravity_MetaData[] = {
		{ "Category", "Forces" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityOverride_MetaData[] = {
		{ "Category", "Forces" },
		{ "EditCondition", "bApplyGravity" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseConstraints_MetaData[] = {
		{ "Category", "Constraints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Constraints\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Constraints" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintCenter_MetaData[] = {
		{ "Category", "Constraints" },
		{ "EditCondition", "bUseConstraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintRadius_MetaData[] = {
		{ "Category", "Constraints" },
		{ "EditCondition", "bUseConstraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBounceOffConstraints_MetaData[] = {
		{ "Category", "Constraints" },
		{ "EditCondition", "bUseConstraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputVelocity_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputAngularVelocity_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputPhysicsState_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VelocityAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularVelocityAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsDescriptor;
	static void NewProp_bRunSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRunSimulation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimulationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeStep;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxIterations;
	static void NewProp_bStabilizeResults_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStabilizeResults;
	static void NewProp_bApplyInitialForce_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bApplyInitialForce;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InitialForce;
	static void NewProp_bApplyInitialImpulse_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bApplyInitialImpulse;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InitialImpulse;
	static void NewProp_bApplyGravity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bApplyGravity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GravityOverride;
	static void NewProp_bUseConstraints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseConstraints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConstraintCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConstraintRadius;
	static void NewProp_bBounceOffConstraints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBounceOffConstraints;
	static void NewProp_bOutputVelocity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputVelocity;
	static void NewProp_bOutputAngularVelocity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputAngularVelocity;
	static void NewProp_bOutputPhysicsState_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputPhysicsState;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VelocityAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AngularVelocityAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPhysicsSimulatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_PhysicsDescriptor = { "PhysicsDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, PhysicsDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsDescriptor_MetaData), NewProp_PhysicsDescriptor_MetaData) }; // 429190624
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bRunSimulation_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bRunSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bRunSimulation = { "bRunSimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bRunSimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRunSimulation_MetaData), NewProp_bRunSimulation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_SimulationTime = { "SimulationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, SimulationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationTime_MetaData), NewProp_SimulationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_TimeStep = { "TimeStep", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, TimeStep), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeStep_MetaData), NewProp_TimeStep_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_MaxIterations = { "MaxIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, MaxIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxIterations_MetaData), NewProp_MaxIterations_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bStabilizeResults_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bStabilizeResults = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bStabilizeResults = { "bStabilizeResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bStabilizeResults_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStabilizeResults_MetaData), NewProp_bStabilizeResults_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialForce_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bApplyInitialForce = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialForce = { "bApplyInitialForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialForce_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bApplyInitialForce_MetaData), NewProp_bApplyInitialForce_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_InitialForce = { "InitialForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, InitialForce), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialForce_MetaData), NewProp_InitialForce_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialImpulse_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bApplyInitialImpulse = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialImpulse = { "bApplyInitialImpulse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialImpulse_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bApplyInitialImpulse_MetaData), NewProp_bApplyInitialImpulse_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_InitialImpulse = { "InitialImpulse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, InitialImpulse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialImpulse_MetaData), NewProp_InitialImpulse_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyGravity_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bApplyGravity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyGravity = { "bApplyGravity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyGravity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bApplyGravity_MetaData), NewProp_bApplyGravity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_GravityOverride = { "GravityOverride", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, GravityOverride), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityOverride_MetaData), NewProp_GravityOverride_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bUseConstraints_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bUseConstraints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bUseConstraints = { "bUseConstraints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bUseConstraints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseConstraints_MetaData), NewProp_bUseConstraints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_ConstraintCenter = { "ConstraintCenter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, ConstraintCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintCenter_MetaData), NewProp_ConstraintCenter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_ConstraintRadius = { "ConstraintRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, ConstraintRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintRadius_MetaData), NewProp_ConstraintRadius_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bBounceOffConstraints_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bBounceOffConstraints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bBounceOffConstraints = { "bBounceOffConstraints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bBounceOffConstraints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBounceOffConstraints_MetaData), NewProp_bBounceOffConstraints_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputVelocity_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bOutputVelocity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputVelocity = { "bOutputVelocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputVelocity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputVelocity_MetaData), NewProp_bOutputVelocity_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputAngularVelocity_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bOutputAngularVelocity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputAngularVelocity = { "bOutputAngularVelocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputAngularVelocity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputAngularVelocity_MetaData), NewProp_bOutputAngularVelocity_MetaData) };
void Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputPhysicsState_SetBit(void* Obj)
{
	((UAuracronPCGPhysicsSimulatorSettings*)Obj)->bOutputPhysicsState = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputPhysicsState = { "bOutputPhysicsState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPhysicsSimulatorSettings), &Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputPhysicsState_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputPhysicsState_MetaData), NewProp_bOutputPhysicsState_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_VelocityAttribute = { "VelocityAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, VelocityAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VelocityAttribute_MetaData), NewProp_VelocityAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_AngularVelocityAttribute = { "AngularVelocityAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPhysicsSimulatorSettings, AngularVelocityAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularVelocityAttribute_MetaData), NewProp_AngularVelocityAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_PhysicsDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bRunSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_SimulationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_TimeStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_MaxIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bStabilizeResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_InitialForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyInitialImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_InitialImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bApplyGravity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_GravityOverride,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bUseConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_ConstraintCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_ConstraintRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bBounceOffConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputAngularVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_bOutputPhysicsState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_VelocityAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::NewProp_AngularVelocityAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::ClassParams = {
	&UAuracronPCGPhysicsSimulatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPhysicsSimulatorSettings);
UAuracronPCGPhysicsSimulatorSettings::~UAuracronPCGPhysicsSimulatorSettings() {}
// ********** End Class UAuracronPCGPhysicsSimulatorSettings ***************************************

// ********** Begin Class UAuracronPCGSpatialQuerySettings *****************************************
void UAuracronPCGSpatialQuerySettings::StaticRegisterNativesUAuracronPCGSpatialQuerySettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings;
UClass* UAuracronPCGSpatialQuerySettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSpatialQuerySettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSpatialQuerySettings"),
			Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSpatialQuerySettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_NoRegister()
{
	return UAuracronPCGSpatialQuerySettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGCollisionSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryDescriptor_MetaData[] = {
		{ "Category", "Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Query configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Query configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseInputAsTargets_MetaData[] = {
		{ "Category", "Targets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target selection\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target selection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bQueryWorldActors_MetaData[] = {
		{ "Category", "Targets" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bQueryOtherPoints_MetaData[] = {
		{ "Category", "Targets" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAnalyzeRelationships_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Relationship analysis\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Relationship analysis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCalculateDistances_MetaData[] = {
		{ "Category", "Analysis" },
		{ "EditCondition", "bAnalyzeRelationships" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCalculateAngles_MetaData[] = {
		{ "Category", "Analysis" },
		{ "EditCondition", "bAnalyzeRelationships" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCalculateVisibility_MetaData[] = {
		{ "Category", "Analysis" },
		{ "EditCondition", "bAnalyzeRelationships" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCalculateAccessibility_MetaData[] = {
		{ "Category", "Analysis" },
		{ "EditCondition", "bAnalyzeRelationships" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPerformClustering_MetaData[] = {
		{ "Category", "Clustering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Clustering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clustering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterRadius_MetaData[] = {
		{ "Category", "Clustering" },
		{ "EditCondition", "bPerformClustering" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinClusterSize_MetaData[] = {
		{ "Category", "Clustering" },
		{ "EditCondition", "bPerformClustering" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxClusterSize_MetaData[] = {
		{ "Category", "Clustering" },
		{ "EditCondition", "bPerformClustering" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputQueryResults_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputRelationshipData_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputClusterData_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NearestDistanceAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NeighborCountAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterIDAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryDescriptor;
	static void NewProp_bUseInputAsTargets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseInputAsTargets;
	static void NewProp_bQueryWorldActors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bQueryWorldActors;
	static void NewProp_bQueryOtherPoints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bQueryOtherPoints;
	static void NewProp_bAnalyzeRelationships_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAnalyzeRelationships;
	static void NewProp_bCalculateDistances_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCalculateDistances;
	static void NewProp_bCalculateAngles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCalculateAngles;
	static void NewProp_bCalculateVisibility_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCalculateVisibility;
	static void NewProp_bCalculateAccessibility_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCalculateAccessibility;
	static void NewProp_bPerformClustering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPerformClustering;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinClusterSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxClusterSize;
	static void NewProp_bOutputQueryResults_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputQueryResults;
	static void NewProp_bOutputRelationshipData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputRelationshipData;
	static void NewProp_bOutputClusterData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputClusterData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NearestDistanceAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NeighborCountAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClusterIDAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSpatialQuerySettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_QueryDescriptor = { "QueryDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSpatialQuerySettings, QueryDescriptor), Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryDescriptor_MetaData), NewProp_QueryDescriptor_MetaData) }; // 3802687609
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bUseInputAsTargets_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bUseInputAsTargets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bUseInputAsTargets = { "bUseInputAsTargets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bUseInputAsTargets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseInputAsTargets_MetaData), NewProp_bUseInputAsTargets_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryWorldActors_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bQueryWorldActors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryWorldActors = { "bQueryWorldActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryWorldActors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bQueryWorldActors_MetaData), NewProp_bQueryWorldActors_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryOtherPoints_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bQueryOtherPoints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryOtherPoints = { "bQueryOtherPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryOtherPoints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bQueryOtherPoints_MetaData), NewProp_bQueryOtherPoints_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bAnalyzeRelationships_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bAnalyzeRelationships = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bAnalyzeRelationships = { "bAnalyzeRelationships", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bAnalyzeRelationships_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAnalyzeRelationships_MetaData), NewProp_bAnalyzeRelationships_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateDistances_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bCalculateDistances = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateDistances = { "bCalculateDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateDistances_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCalculateDistances_MetaData), NewProp_bCalculateDistances_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAngles_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bCalculateAngles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAngles = { "bCalculateAngles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAngles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCalculateAngles_MetaData), NewProp_bCalculateAngles_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateVisibility_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bCalculateVisibility = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateVisibility = { "bCalculateVisibility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateVisibility_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCalculateVisibility_MetaData), NewProp_bCalculateVisibility_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAccessibility_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bCalculateAccessibility = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAccessibility = { "bCalculateAccessibility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAccessibility_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCalculateAccessibility_MetaData), NewProp_bCalculateAccessibility_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bPerformClustering_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bPerformClustering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bPerformClustering = { "bPerformClustering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bPerformClustering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPerformClustering_MetaData), NewProp_bPerformClustering_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_ClusterRadius = { "ClusterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSpatialQuerySettings, ClusterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterRadius_MetaData), NewProp_ClusterRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_MinClusterSize = { "MinClusterSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSpatialQuerySettings, MinClusterSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinClusterSize_MetaData), NewProp_MinClusterSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_MaxClusterSize = { "MaxClusterSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSpatialQuerySettings, MaxClusterSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxClusterSize_MetaData), NewProp_MaxClusterSize_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputQueryResults_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bOutputQueryResults = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputQueryResults = { "bOutputQueryResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputQueryResults_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputQueryResults_MetaData), NewProp_bOutputQueryResults_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputRelationshipData_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bOutputRelationshipData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputRelationshipData = { "bOutputRelationshipData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputRelationshipData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputRelationshipData_MetaData), NewProp_bOutputRelationshipData_MetaData) };
void Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputClusterData_SetBit(void* Obj)
{
	((UAuracronPCGSpatialQuerySettings*)Obj)->bOutputClusterData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputClusterData = { "bOutputClusterData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSpatialQuerySettings), &Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputClusterData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputClusterData_MetaData), NewProp_bOutputClusterData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_NearestDistanceAttribute = { "NearestDistanceAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSpatialQuerySettings, NearestDistanceAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NearestDistanceAttribute_MetaData), NewProp_NearestDistanceAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_NeighborCountAttribute = { "NeighborCountAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSpatialQuerySettings, NeighborCountAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NeighborCountAttribute_MetaData), NewProp_NeighborCountAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_ClusterIDAttribute = { "ClusterIDAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSpatialQuerySettings, ClusterIDAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterIDAttribute_MetaData), NewProp_ClusterIDAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_QueryDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bUseInputAsTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryWorldActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bQueryOtherPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bAnalyzeRelationships,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAngles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateVisibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bCalculateAccessibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bPerformClustering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_ClusterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_MinClusterSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_MaxClusterSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputQueryResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputRelationshipData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_bOutputClusterData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_NearestDistanceAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_NeighborCountAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::NewProp_ClusterIDAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::ClassParams = {
	&UAuracronPCGSpatialQuerySettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSpatialQuerySettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSpatialQuerySettings);
UAuracronPCGSpatialQuerySettings::~UAuracronPCGSpatialQuerySettings() {}
// ********** End Class UAuracronPCGSpatialQuerySettings *******************************************

// ********** Begin Class UAuracronPCGPlacementValidatorSettings ***********************************
void UAuracronPCGPlacementValidatorSettings::StaticRegisterNativesUAuracronPCGPlacementValidatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings;
UClass* UAuracronPCGPlacementValidatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPlacementValidatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPlacementValidatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPlacementValidatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_NoRegister()
{
	return UAuracronPCGPlacementValidatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGCollisionSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationMode_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpatialQueryDescriptor_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequireGroundContact_MetaData[] = {
		{ "Category", "Ground Check" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ground validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ground validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGroundDistance_MetaData[] = {
		{ "Category", "Ground Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSlopeAngle_MetaData[] = {
		{ "Category", "Ground Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlopeAngle_MetaData[] = {
		{ "Category", "Ground Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSnapToGround_MetaData[] = {
		{ "Category", "Ground Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClearanceRadius_MetaData[] = {
		{ "Category", "Clearance Check" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Clearance validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clearance validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClearanceHeight_MetaData[] = {
		{ "Category", "Clearance Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckOverhead_MetaData[] = {
		{ "Category", "Clearance Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverheadClearance_MetaData[] = {
		{ "Category", "Clearance Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckStability_MetaData[] = {
		{ "Category", "Stability Check" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Stability validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stability validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StabilityRadius_MetaData[] = {
		{ "Category", "Stability Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StabilitySamples_MetaData[] = {
		{ "Category", "Stability Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinStabilityScore_MetaData[] = {
		{ "Category", "Stability Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinProximityDistance_MetaData[] = {
		{ "Category", "Proximity Check" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Proximity validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Proximity validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxProximityDistance_MetaData[] = {
		{ "Category", "Proximity Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAvoidOverlap_MetaData[] = {
		{ "Category", "Proximity Check" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputValidationScores_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputFailureReasons_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationScoreAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailureReasonAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpatialQueryDescriptor;
	static void NewProp_bRequireGroundContact_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequireGroundContact;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxGroundDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSlopeAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlopeAngle;
	static void NewProp_bSnapToGround_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSnapToGround;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClearanceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClearanceHeight;
	static void NewProp_bCheckOverhead_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckOverhead;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OverheadClearance;
	static void NewProp_bCheckStability_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckStability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StabilityRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StabilitySamples;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinStabilityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinProximityDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxProximityDistance;
	static void NewProp_bAvoidOverlap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvoidOverlap;
	static void NewProp_bOutputValidationScores_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputValidationScores;
	static void NewProp_bOutputFailureReasons_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputFailureReasons;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationScoreAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FailureReasonAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPlacementValidatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ValidationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ValidationMode = { "ValidationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, ValidationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationMode_MetaData), NewProp_ValidationMode_MetaData) }; // 2543609643
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_SpatialQueryDescriptor = { "SpatialQueryDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, SpatialQueryDescriptor), Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpatialQueryDescriptor_MetaData), NewProp_SpatialQueryDescriptor_MetaData) }; // 3802687609
void Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bRequireGroundContact_SetBit(void* Obj)
{
	((UAuracronPCGPlacementValidatorSettings*)Obj)->bRequireGroundContact = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bRequireGroundContact = { "bRequireGroundContact", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPlacementValidatorSettings), &Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bRequireGroundContact_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequireGroundContact_MetaData), NewProp_bRequireGroundContact_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MaxGroundDistance = { "MaxGroundDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, MaxGroundDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGroundDistance_MetaData), NewProp_MaxGroundDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MinSlopeAngle = { "MinSlopeAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, MinSlopeAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSlopeAngle_MetaData), NewProp_MinSlopeAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MaxSlopeAngle = { "MaxSlopeAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, MaxSlopeAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlopeAngle_MetaData), NewProp_MaxSlopeAngle_MetaData) };
void Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bSnapToGround_SetBit(void* Obj)
{
	((UAuracronPCGPlacementValidatorSettings*)Obj)->bSnapToGround = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bSnapToGround = { "bSnapToGround", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPlacementValidatorSettings), &Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bSnapToGround_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSnapToGround_MetaData), NewProp_bSnapToGround_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ClearanceRadius = { "ClearanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, ClearanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClearanceRadius_MetaData), NewProp_ClearanceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ClearanceHeight = { "ClearanceHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, ClearanceHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClearanceHeight_MetaData), NewProp_ClearanceHeight_MetaData) };
void Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckOverhead_SetBit(void* Obj)
{
	((UAuracronPCGPlacementValidatorSettings*)Obj)->bCheckOverhead = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckOverhead = { "bCheckOverhead", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPlacementValidatorSettings), &Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckOverhead_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckOverhead_MetaData), NewProp_bCheckOverhead_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_OverheadClearance = { "OverheadClearance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, OverheadClearance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverheadClearance_MetaData), NewProp_OverheadClearance_MetaData) };
void Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckStability_SetBit(void* Obj)
{
	((UAuracronPCGPlacementValidatorSettings*)Obj)->bCheckStability = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckStability = { "bCheckStability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPlacementValidatorSettings), &Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckStability_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckStability_MetaData), NewProp_bCheckStability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_StabilityRadius = { "StabilityRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, StabilityRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StabilityRadius_MetaData), NewProp_StabilityRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_StabilitySamples = { "StabilitySamples", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, StabilitySamples), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StabilitySamples_MetaData), NewProp_StabilitySamples_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MinStabilityScore = { "MinStabilityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, MinStabilityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinStabilityScore_MetaData), NewProp_MinStabilityScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MinProximityDistance = { "MinProximityDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, MinProximityDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinProximityDistance_MetaData), NewProp_MinProximityDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MaxProximityDistance = { "MaxProximityDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, MaxProximityDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxProximityDistance_MetaData), NewProp_MaxProximityDistance_MetaData) };
void Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bAvoidOverlap_SetBit(void* Obj)
{
	((UAuracronPCGPlacementValidatorSettings*)Obj)->bAvoidOverlap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bAvoidOverlap = { "bAvoidOverlap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPlacementValidatorSettings), &Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bAvoidOverlap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAvoidOverlap_MetaData), NewProp_bAvoidOverlap_MetaData) };
void Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputValidationScores_SetBit(void* Obj)
{
	((UAuracronPCGPlacementValidatorSettings*)Obj)->bOutputValidationScores = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputValidationScores = { "bOutputValidationScores", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPlacementValidatorSettings), &Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputValidationScores_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputValidationScores_MetaData), NewProp_bOutputValidationScores_MetaData) };
void Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputFailureReasons_SetBit(void* Obj)
{
	((UAuracronPCGPlacementValidatorSettings*)Obj)->bOutputFailureReasons = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputFailureReasons = { "bOutputFailureReasons", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPlacementValidatorSettings), &Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputFailureReasons_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputFailureReasons_MetaData), NewProp_bOutputFailureReasons_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ValidationScoreAttribute = { "ValidationScoreAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, ValidationScoreAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationScoreAttribute_MetaData), NewProp_ValidationScoreAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_FailureReasonAttribute = { "FailureReasonAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPlacementValidatorSettings, FailureReasonAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailureReasonAttribute_MetaData), NewProp_FailureReasonAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ValidationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ValidationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_SpatialQueryDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bRequireGroundContact,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MaxGroundDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MinSlopeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MaxSlopeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bSnapToGround,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ClearanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ClearanceHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckOverhead,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_OverheadClearance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bCheckStability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_StabilityRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_StabilitySamples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MinStabilityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MinProximityDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_MaxProximityDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bAvoidOverlap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputValidationScores,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_bOutputFailureReasons,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_ValidationScoreAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::NewProp_FailureReasonAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::ClassParams = {
	&UAuracronPCGPlacementValidatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPlacementValidatorSettings);
UAuracronPCGPlacementValidatorSettings::~UAuracronPCGPlacementValidatorSettings() {}
// ********** End Class UAuracronPCGPlacementValidatorSettings *************************************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function CalculateAngle *****************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventCalculateAngle_Parms
	{
		FVector PointA;
		FVector PointB;
		FVector Reference;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointB_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reference;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_PointA = { "PointA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateAngle_Parms, PointA), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointA_MetaData), NewProp_PointA_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_PointB = { "PointB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateAngle_Parms, PointB), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointB_MetaData), NewProp_PointB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_Reference = { "Reference", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateAngle_Parms, Reference), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reference_MetaData), NewProp_Reference_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateAngle_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_PointA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_PointB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_Reference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "CalculateAngle", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::AuracronPCGCollisionSystemUtils_eventCalculateAngle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::AuracronPCGCollisionSystemUtils_eventCalculateAngle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execCalculateAngle)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PointA);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PointB);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Reference);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::CalculateAngle(Z_Param_Out_PointA,Z_Param_Out_PointB,Z_Param_Out_Reference);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function CalculateAngle *******************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function CalculateDistance **************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventCalculateDistance_Parms
	{
		FVector PointA;
		FVector PointB;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::NewProp_PointA = { "PointA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateDistance_Parms, PointA), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointA_MetaData), NewProp_PointA_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::NewProp_PointB = { "PointB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateDistance_Parms, PointB), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointB_MetaData), NewProp_PointB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateDistance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::NewProp_PointA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::NewProp_PointB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "CalculateDistance", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::AuracronPCGCollisionSystemUtils_eventCalculateDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::AuracronPCGCollisionSystemUtils_eventCalculateDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execCalculateDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PointA);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PointB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::CalculateDistance(Z_Param_Out_PointA,Z_Param_Out_PointB);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function CalculateDistance ****************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function CalculateGravityForce **********
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventCalculateGravityForce_Parms
	{
		FAuracronPCGPhysicsDescriptor PhysicsDescriptor;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::NewProp_PhysicsDescriptor = { "PhysicsDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateGravityForce_Parms, PhysicsDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsDescriptor_MetaData), NewProp_PhysicsDescriptor_MetaData) }; // 429190624
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateGravityForce_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::NewProp_PhysicsDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "CalculateGravityForce", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::AuracronPCGCollisionSystemUtils_eventCalculateGravityForce_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::AuracronPCGCollisionSystemUtils_eventCalculateGravityForce_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execCalculateGravityForce)
{
	P_GET_STRUCT_REF(FAuracronPCGPhysicsDescriptor,Z_Param_Out_PhysicsDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::CalculateGravityForce(Z_Param_Out_PhysicsDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function CalculateGravityForce ************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function CalculateGroundAngle ***********
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventCalculateGroundAngle_Parms
	{
		FVector Normal;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normal_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::NewProp_Normal = { "Normal", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateGroundAngle_Parms, Normal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normal_MetaData), NewProp_Normal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateGroundAngle_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::NewProp_Normal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "CalculateGroundAngle", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::AuracronPCGCollisionSystemUtils_eventCalculateGroundAngle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::AuracronPCGCollisionSystemUtils_eventCalculateGroundAngle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execCalculateGroundAngle)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Normal);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::CalculateGroundAngle(Z_Param_Out_Normal);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function CalculateGroundAngle *************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function CalculateStabilityScore ********
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms
	{
		UWorld* World;
		FVector Location;
		float Radius;
		int32 Samples;
		FAuracronPCGCollisionDescriptor CollisionDescriptor;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Samples;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_Samples = { "Samples", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms, Samples), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_Samples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "CalculateStabilityScore", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::AuracronPCGCollisionSystemUtils_eventCalculateStabilityScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execCalculateStabilityScore)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FIntProperty,Z_Param_Samples);
	P_GET_STRUCT_REF(FAuracronPCGCollisionDescriptor,Z_Param_Out_CollisionDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::CalculateStabilityScore(Z_Param_World,Z_Param_Out_Location,Z_Param_Radius,Z_Param_Samples,Z_Param_Out_CollisionDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function CalculateStabilityScore **********

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function CheckClearance *****************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms
	{
		UWorld* World;
		FVector Location;
		float Radius;
		float Height;
		FAuracronPCGCollisionDescriptor CollisionDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
void Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms), &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "CheckClearance", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::AuracronPCGCollisionSystemUtils_eventCheckClearance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execCheckClearance)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_GET_STRUCT_REF(FAuracronPCGCollisionDescriptor,Z_Param_Out_CollisionDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::CheckClearance(Z_Param_World,Z_Param_Out_Location,Z_Param_Radius,Z_Param_Height,Z_Param_Out_CollisionDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function CheckClearance *******************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function CheckLineOfSight ***************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms
	{
		UWorld* World;
		FVector Start;
		FVector End;
		FAuracronPCGCollisionDescriptor CollisionDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
void Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms), &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "CheckLineOfSight", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::AuracronPCGCollisionSystemUtils_eventCheckLineOfSight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execCheckLineOfSight)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_STRUCT_REF(FAuracronPCGCollisionDescriptor,Z_Param_Out_CollisionDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::CheckLineOfSight(Z_Param_World,Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Out_CollisionDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function CheckLineOfSight *****************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function IsPhysicsStable ****************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms
	{
		FVector Velocity;
		FVector AngularVelocity;
		float Threshold;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "CPP_Default_Threshold", "0.100000" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularVelocity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AngularVelocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Threshold;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_AngularVelocity = { "AngularVelocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms, AngularVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularVelocity_MetaData), NewProp_AngularVelocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms, Threshold), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms), &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_AngularVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_Threshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "IsPhysicsStable", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::AuracronPCGCollisionSystemUtils_eventIsPhysicsStable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execIsPhysicsStable)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Velocity);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_AngularVelocity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Threshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::IsPhysicsStable(Z_Param_Out_Velocity,Z_Param_Out_AngularVelocity,Z_Param_Threshold);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function IsPhysicsStable ******************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function PerformCollisionTest ***********
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms
	{
		UWorld* World;
		FVector Start;
		FVector End;
		FAuracronPCGCollisionDescriptor CollisionDescriptor;
		FHitResult OutHitResult;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision testing utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision testing utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutHitResult;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_OutHitResult = { "OutHitResult", nullptr, (EPropertyFlags)0x0010008000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms, OutHitResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(0, nullptr) }; // 267591329
void Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms), &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_OutHitResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "PerformCollisionTest", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::AuracronPCGCollisionSystemUtils_eventPerformCollisionTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execPerformCollisionTest)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_STRUCT_REF(FAuracronPCGCollisionDescriptor,Z_Param_Out_CollisionDescriptor);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_OutHitResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::PerformCollisionTest(Z_Param_World,Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Out_CollisionDescriptor,Z_Param_Out_OutHitResult);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function PerformCollisionTest *************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function PerformSpatialQuery ************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventPerformSpatialQuery_Parms
	{
		UWorld* World;
		FVector Location;
		FAuracronPCGSpatialQueryDescriptor QueryDescriptor;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial query utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial query utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryDescriptor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformSpatialQuery_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformSpatialQuery_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_QueryDescriptor = { "QueryDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformSpatialQuery_Parms, QueryDescriptor), Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryDescriptor_MetaData), NewProp_QueryDescriptor_MetaData) }; // 3802687609
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventPerformSpatialQuery_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_QueryDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "PerformSpatialQuery", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::AuracronPCGCollisionSystemUtils_eventPerformSpatialQuery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::AuracronPCGCollisionSystemUtils_eventPerformSpatialQuery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execPerformSpatialQuery)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronPCGSpatialQueryDescriptor,Z_Param_Out_QueryDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::PerformSpatialQuery(Z_Param_World,Z_Param_Out_Location,Z_Param_Out_QueryDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function PerformSpatialQuery **************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function ProjectToSurface ***************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms
	{
		UWorld* World;
		FVector Location;
		float ProjectionDistance;
		FAuracronPCGCollisionDescriptor CollisionDescriptor;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProjectionDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_ProjectionDistance = { "ProjectionDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms, ProjectionDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_ProjectionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "ProjectToSurface", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::AuracronPCGCollisionSystemUtils_eventProjectToSurface_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execProjectToSurface)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ProjectionDistance);
	P_GET_STRUCT_REF(FAuracronPCGCollisionDescriptor,Z_Param_Out_CollisionDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::ProjectToSurface(Z_Param_World,Z_Param_Out_Location,Z_Param_ProjectionDistance,Z_Param_Out_CollisionDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function ProjectToSurface *****************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function SimulatePhysicsStep ************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms
	{
		FVector Position;
		FVector Velocity;
		FAuracronPCGPhysicsDescriptor PhysicsDescriptor;
		float DeltaTime;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics simulation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics simulation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsDescriptor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_PhysicsDescriptor = { "PhysicsDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms, PhysicsDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsDescriptor_MetaData), NewProp_PhysicsDescriptor_MetaData) }; // 429190624
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_PhysicsDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_DeltaTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "SimulatePhysicsStep", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::AuracronPCGCollisionSystemUtils_eventSimulatePhysicsStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execSimulatePhysicsStep)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Velocity);
	P_GET_STRUCT_REF(FAuracronPCGPhysicsDescriptor,Z_Param_Out_PhysicsDescriptor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::SimulatePhysicsStep(Z_Param_Out_Position,Z_Param_Out_Velocity,Z_Param_Out_PhysicsDescriptor,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function SimulatePhysicsStep **************

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function ValidateCollisionDescriptor ****
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventValidateCollisionDescriptor_Parms
	{
		FAuracronPCGCollisionDescriptor CollisionDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventValidateCollisionDescriptor_Parms, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
void Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCollisionSystemUtils_eventValidateCollisionDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCollisionSystemUtils_eventValidateCollisionDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "ValidateCollisionDescriptor", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::AuracronPCGCollisionSystemUtils_eventValidateCollisionDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::AuracronPCGCollisionSystemUtils_eventValidateCollisionDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execValidateCollisionDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGCollisionDescriptor,Z_Param_Out_CollisionDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::ValidateCollisionDescriptor(Z_Param_Out_CollisionDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function ValidateCollisionDescriptor ******

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function ValidatePhysicsDescriptor ******
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventValidatePhysicsDescriptor_Parms
	{
		FAuracronPCGPhysicsDescriptor PhysicsDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::NewProp_PhysicsDescriptor = { "PhysicsDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventValidatePhysicsDescriptor_Parms, PhysicsDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsDescriptor_MetaData), NewProp_PhysicsDescriptor_MetaData) }; // 429190624
void Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCollisionSystemUtils_eventValidatePhysicsDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCollisionSystemUtils_eventValidatePhysicsDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::NewProp_PhysicsDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "ValidatePhysicsDescriptor", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::AuracronPCGCollisionSystemUtils_eventValidatePhysicsDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::AuracronPCGCollisionSystemUtils_eventValidatePhysicsDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execValidatePhysicsDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGPhysicsDescriptor,Z_Param_Out_PhysicsDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::ValidatePhysicsDescriptor(Z_Param_Out_PhysicsDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function ValidatePhysicsDescriptor ********

// ********** Begin Class UAuracronPCGCollisionSystemUtils Function ValidatePlacement **************
struct Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics
{
	struct AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms
	{
		UWorld* World;
		FVector Location;
		FAuracronPCGCollisionDescriptor CollisionDescriptor;
		EAuracronPCGPlacementValidation ValidationMode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionDescriptor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationMode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_CollisionDescriptor = { "CollisionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms, CollisionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDescriptor_MetaData), NewProp_CollisionDescriptor_MetaData) }; // 575925180
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ValidationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ValidationMode = { "ValidationMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms, ValidationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPlacementValidation, METADATA_PARAMS(0, nullptr) }; // 2543609643
void Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms), &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_CollisionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ValidationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ValidationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, nullptr, "ValidatePlacement", Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::AuracronPCGCollisionSystemUtils_eventValidatePlacement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCollisionSystemUtils::execValidatePlacement)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronPCGCollisionDescriptor,Z_Param_Out_CollisionDescriptor);
	P_GET_ENUM(EAuracronPCGPlacementValidation,Z_Param_ValidationMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCollisionSystemUtils::ValidatePlacement(Z_Param_World,Z_Param_Out_Location,Z_Param_Out_CollisionDescriptor,EAuracronPCGPlacementValidation(Z_Param_ValidationMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCollisionSystemUtils Function ValidatePlacement ****************

// ********** Begin Class UAuracronPCGCollisionSystemUtils *****************************************
void UAuracronPCGCollisionSystemUtils::StaticRegisterNativesUAuracronPCGCollisionSystemUtils()
{
	UClass* Class = UAuracronPCGCollisionSystemUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateAngle", &UAuracronPCGCollisionSystemUtils::execCalculateAngle },
		{ "CalculateDistance", &UAuracronPCGCollisionSystemUtils::execCalculateDistance },
		{ "CalculateGravityForce", &UAuracronPCGCollisionSystemUtils::execCalculateGravityForce },
		{ "CalculateGroundAngle", &UAuracronPCGCollisionSystemUtils::execCalculateGroundAngle },
		{ "CalculateStabilityScore", &UAuracronPCGCollisionSystemUtils::execCalculateStabilityScore },
		{ "CheckClearance", &UAuracronPCGCollisionSystemUtils::execCheckClearance },
		{ "CheckLineOfSight", &UAuracronPCGCollisionSystemUtils::execCheckLineOfSight },
		{ "IsPhysicsStable", &UAuracronPCGCollisionSystemUtils::execIsPhysicsStable },
		{ "PerformCollisionTest", &UAuracronPCGCollisionSystemUtils::execPerformCollisionTest },
		{ "PerformSpatialQuery", &UAuracronPCGCollisionSystemUtils::execPerformSpatialQuery },
		{ "ProjectToSurface", &UAuracronPCGCollisionSystemUtils::execProjectToSurface },
		{ "SimulatePhysicsStep", &UAuracronPCGCollisionSystemUtils::execSimulatePhysicsStep },
		{ "ValidateCollisionDescriptor", &UAuracronPCGCollisionSystemUtils::execValidateCollisionDescriptor },
		{ "ValidatePhysicsDescriptor", &UAuracronPCGCollisionSystemUtils::execValidatePhysicsDescriptor },
		{ "ValidatePlacement", &UAuracronPCGCollisionSystemUtils::execValidatePlacement },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils;
UClass* UAuracronPCGCollisionSystemUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCollisionSystemUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCollisionSystemUtils"),
			Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCollisionSystemUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_NoRegister()
{
	return UAuracronPCGCollisionSystemUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision System Utilities\n * Utility functions for collision detection and physics simulation\n */" },
#endif
		{ "IncludePath", "AuracronPCGCollisionSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCollisionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision System Utilities\nUtility functions for collision detection and physics simulation" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateAngle, "CalculateAngle" }, // 632286540
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateDistance, "CalculateDistance" }, // 1861574659
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGravityForce, "CalculateGravityForce" }, // 1611054477
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateGroundAngle, "CalculateGroundAngle" }, // 331415078
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CalculateStabilityScore, "CalculateStabilityScore" }, // 2927053995
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckClearance, "CheckClearance" }, // 579916164
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_CheckLineOfSight, "CheckLineOfSight" }, // 1094977483
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_IsPhysicsStable, "IsPhysicsStable" }, // 1115567868
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformCollisionTest, "PerformCollisionTest" }, // 2941697500
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_PerformSpatialQuery, "PerformSpatialQuery" }, // 1034532873
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ProjectToSurface, "ProjectToSurface" }, // 3299667217
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_SimulatePhysicsStep, "SimulatePhysicsStep" }, // 1576268949
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidateCollisionDescriptor, "ValidateCollisionDescriptor" }, // 2218720835
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePhysicsDescriptor, "ValidatePhysicsDescriptor" }, // 2956137572
		{ &Z_Construct_UFunction_UAuracronPCGCollisionSystemUtils_ValidatePlacement, "ValidatePlacement" }, // 1373544149
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCollisionSystemUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics::ClassParams = {
	&UAuracronPCGCollisionSystemUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCollisionSystemUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils.OuterSingleton;
}
UAuracronPCGCollisionSystemUtils::UAuracronPCGCollisionSystemUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCollisionSystemUtils);
UAuracronPCGCollisionSystemUtils::~UAuracronPCGCollisionSystemUtils() {}
// ********** End Class UAuracronPCGCollisionSystemUtils *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGCollisionTestType_StaticEnum, TEXT("EAuracronPCGCollisionTestType"), &Z_Registration_Info_UEnum_EAuracronPCGCollisionTestType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3784044577U) },
		{ EAuracronPCGCollisionResponse_StaticEnum, TEXT("EAuracronPCGCollisionResponse"), &Z_Registration_Info_UEnum_EAuracronPCGCollisionResponse, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 567043474U) },
		{ EAuracronPCGPhysicsSimulationType_StaticEnum, TEXT("EAuracronPCGPhysicsSimulationType"), &Z_Registration_Info_UEnum_EAuracronPCGPhysicsSimulationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 600303698U) },
		{ EAuracronPCGSpatialQueryType_StaticEnum, TEXT("EAuracronPCGSpatialQueryType"), &Z_Registration_Info_UEnum_EAuracronPCGSpatialQueryType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3858762667U) },
		{ EAuracronPCGPlacementValidation_StaticEnum, TEXT("EAuracronPCGPlacementValidation"), &Z_Registration_Info_UEnum_EAuracronPCGPlacementValidation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2543609643U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGCollisionDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics::NewStructOps, TEXT("AuracronPCGCollisionDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGCollisionDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGCollisionDescriptor), 575925180U) },
		{ FAuracronPCGPhysicsDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics::NewStructOps, TEXT("AuracronPCGPhysicsDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGPhysicsDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGPhysicsDescriptor), 429190624U) },
		{ FAuracronPCGSpatialQueryDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics::NewStructOps, TEXT("AuracronPCGSpatialQueryDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGSpatialQueryDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGSpatialQueryDescriptor), 3802687609U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGCollisionTesterSettings, UAuracronPCGCollisionTesterSettings::StaticClass, TEXT("UAuracronPCGCollisionTesterSettings"), &Z_Registration_Info_UClass_UAuracronPCGCollisionTesterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCollisionTesterSettings), 2293963771U) },
		{ Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings, UAuracronPCGPhysicsSimulatorSettings::StaticClass, TEXT("UAuracronPCGPhysicsSimulatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGPhysicsSimulatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPhysicsSimulatorSettings), 1596667042U) },
		{ Z_Construct_UClass_UAuracronPCGSpatialQuerySettings, UAuracronPCGSpatialQuerySettings::StaticClass, TEXT("UAuracronPCGSpatialQuerySettings"), &Z_Registration_Info_UClass_UAuracronPCGSpatialQuerySettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSpatialQuerySettings), 68070372U) },
		{ Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings, UAuracronPCGPlacementValidatorSettings::StaticClass, TEXT("UAuracronPCGPlacementValidatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGPlacementValidatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPlacementValidatorSettings), 4064698118U) },
		{ Z_Construct_UClass_UAuracronPCGCollisionSystemUtils, UAuracronPCGCollisionSystemUtils::StaticClass, TEXT("UAuracronPCGCollisionSystemUtils"), &Z_Registration_Info_UClass_UAuracronPCGCollisionSystemUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCollisionSystemUtils), 665505701U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_3818853029(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
