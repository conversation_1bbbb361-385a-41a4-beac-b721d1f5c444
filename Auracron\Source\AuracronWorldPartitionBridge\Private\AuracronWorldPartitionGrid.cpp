// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Grid System Implementation
// Bridge 3.2: World Partition - Grid System

#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionBridge.h"

// World Partition includes
#include "WorldPartition/WorldPartitionRuntimeSpatialHash.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"

// Engine includes
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "HAL/PlatformMemory.h"
#include "Misc/DateTime.h"

// =============================================================================
// GRID CELL IMPLEMENTATION
// =============================================================================

void FAuracronGridCell::UpdateDensity()
{
    if (Bounds.GetVolume() > 0.0f)
    {
        Density = static_cast<float>(ActorCount) / Bounds.GetVolume();
    }
    else
    {
        Density = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

bool FAuracronGridCell::ShouldSubdivide(int32 MaxActorsPerCell) const
{
    return ActorCount > MaxActorsPerCell && SubdivisionLevel < 8; // Max 8 levels
}

FVector FAuracronGridCell::GetCenter() const
{
    return Bounds.GetCenter();
}

FVector FAuracronGridCell::GetSize() const
{
    return Bounds.GetSize();
}

// =============================================================================
// GRID STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronGridStatistics::UpdateCalculatedFields()
{
    if (TotalCells > 0)
    {
        AverageCellDensity = static_cast<float>(TotalActors) / static_cast<float>(TotalCells);
    }
    else
    {
        AverageCellDensity = 0.0f;
    }
    
    if (TotalQueries > 0)
    {
        QuerySuccessRate = static_cast<float>(SuccessfulQueries) / static_cast<float>(TotalQueries);
    }
    else
    {
        QuerySuccessRate = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION GRID MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionGridManager* UAuracronWorldPartitionGridManager::Instance = nullptr;

UAuracronWorldPartitionGridManager* UAuracronWorldPartitionGridManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionGridManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionGridManager::Initialize(const FAuracronGridConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Grid Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize statistics
    Statistics = FAuracronGridStatistics();
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Grid Manager initialized with cell size: %d"), Configuration.CellSize);
}

void UAuracronWorldPartitionGridManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all grid data
    GridCells.Empty();
    ActorToCellMap.Empty();
    CoordinateToCellMap.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Grid Manager shutdown completed"));
}

bool UAuracronWorldPartitionGridManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionGridManager::CreateGrid(UWorld* World)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot create grid: Manager not initialized"));
        return;
    }

    if (!World)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot create grid: World is null"));
        return;
    }

    ManagedWorld = World;
    
    // Clear existing grid
    GridCells.Empty();
    ActorToCellMap.Empty();
    CoordinateToCellMap.Empty();

    // Create initial grid based on world bounds
    FBox WorldBounds = Configuration.WorldBounds;
    int32 CellSize = Configuration.CellSize;
    
    // Calculate grid dimensions
    FVector WorldSize = WorldBounds.GetSize();
    int32 GridWidth = FMath::CeilToInt(WorldSize.X / CellSize);
    int32 GridHeight = FMath::CeilToInt(WorldSize.Y / CellSize);
    int32 GridDepth = FMath::CeilToInt(WorldSize.Z / CellSize);
    
    AURACRON_WP_LOG_INFO(TEXT("Creating grid: %dx%dx%d cells"), GridWidth, GridHeight, GridDepth);
    
    // Create base level cells
    for (int32 X = 0; X < GridWidth; X++)
    {
        for (int32 Y = 0; Y < GridHeight; Y++)
        {
            for (int32 Z = 0; Z < GridDepth; Z++)
            {
                FIntVector Coordinates(X, Y, Z);
                FAuracronGridCell Cell = CreateCell(Coordinates, 0);
                
                GridCells.Add(Cell.CellId, Cell);
                CoordinateToCellMap.Add(Coordinates, Cell.CellId);
            }
        }
    }
    
    UpdateStatistics();
    
    AURACRON_WP_LOG_INFO(TEXT("Grid created with %d cells"), GridCells.Num());
}

void UAuracronWorldPartitionGridManager::DestroyGrid()
{
    FScopeLock Lock(&GridLock);
    
    GridCells.Empty();
    ActorToCellMap.Empty();
    CoordinateToCellMap.Empty();
    
    UpdateStatistics();
    
    AURACRON_WP_LOG_INFO(TEXT("Grid destroyed"));
}

void UAuracronWorldPartitionGridManager::RebuildGrid()
{
    if (UWorld* World = ManagedWorld.Get())
    {
        DestroyGrid();
        CreateGrid(World);
        
        AURACRON_WP_LOG_INFO(TEXT("Grid rebuilt"));
    }
}

void UAuracronWorldPartitionGridManager::OptimizeGrid()
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&GridLock);
    
    // Auto-subdivide overcrowded cells
    if (Configuration.bEnableAdaptiveSubdivision)
    {
        AutoSubdivideCells();
    }
    
    // Auto-merge underpopulated cells
    AutoMergeCells();
    
    UpdateStatistics();
    
    AURACRON_WP_LOG_INFO(TEXT("Grid optimization completed"));
}

FAuracronGridCell UAuracronWorldPartitionGridManager::CreateCell(const FIntVector& Coordinates, int32 SubdivisionLevel)
{
    FAuracronGridCell Cell;
    Cell.CellId = CoordinatesToCellId(Coordinates, SubdivisionLevel);
    Cell.Coordinates = Coordinates;
    Cell.SubdivisionLevel = SubdivisionLevel;
    Cell.Bounds = CalculateCellBounds(Coordinates, SubdivisionLevel);
    Cell.State = EAuracronGridCellState::Empty;
    Cell.CreationTime = FDateTime::Now();
    Cell.LastUpdateTime = Cell.CreationTime;
    
    OnCellCreatedInternal(Cell);
    
    return Cell;
}

bool UAuracronWorldPartitionGridManager::RemoveCell(const FString& CellId)
{
    FScopeLock Lock(&GridLock);
    
    FAuracronGridCell* Cell = GridCells.Find(CellId);
    if (!Cell)
    {
        return false;
    }
    
    // Remove actors from this cell
    for (const FString& ActorId : Cell->ActorIds)
    {
        ActorToCellMap.Remove(ActorId);
    }
    
    // Remove from coordinate mapping
    CoordinateToCellMap.Remove(Cell->Coordinates);
    
    // Remove cell
    GridCells.Remove(CellId);
    
    OnCellRemovedInternal(CellId);
    UpdateStatistics();
    
    return true;
}

FAuracronGridCell UAuracronWorldPartitionGridManager::GetCell(const FString& CellId) const
{
    FScopeLock Lock(&GridLock);
    
    const FAuracronGridCell* Cell = GridCells.Find(CellId);
    if (Cell)
    {
        return *Cell;
    }
    
    return FAuracronGridCell();
}

FAuracronGridCell UAuracronWorldPartitionGridManager::GetCellAtLocation(const FVector& Location) const
{
    FIntVector Coordinates = WorldToGridCoordinates(Location);
    FString CellId = CoordinatesToCellId(Coordinates, 0);
    
    return GetCell(CellId);
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::GetCellsInRadius(const FVector& Location, float Radius) const
{
    FScopeLock Lock(&GridLock);
    
    TArray<FAuracronGridCell> CellsInRadius;
    
    for (const auto& CellPair : GridCells)
    {
        const FAuracronGridCell& Cell = CellPair.Value;
        FVector CellCenter = Cell.GetCenter();
        
        if (FVector::Dist(Location, CellCenter) <= Radius)
        {
            CellsInRadius.Add(Cell);
        }
    }
    
    return CellsInRadius;
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::GetCellsInBox(const FBox& Box) const
{
    FScopeLock Lock(&GridLock);
    
    TArray<FAuracronGridCell> CellsInBox;
    
    for (const auto& CellPair : GridCells)
    {
        const FAuracronGridCell& Cell = CellPair.Value;
        
        if (Box.Intersect(Cell.Bounds))
        {
            CellsInBox.Add(Cell);
        }
    }
    
    return CellsInBox;
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::GetAllCells() const
{
    FScopeLock Lock(&GridLock);
    
    TArray<FAuracronGridCell> AllCells;
    GridCells.GenerateValueArray(AllCells);
    
    return AllCells;
}

void UAuracronWorldPartitionGridManager::AddActorToGrid(const FString& ActorId, const FVector& Location)
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&GridLock);
    
    // Remove from previous cell if exists
    FString* PreviousCellId = ActorToCellMap.Find(ActorId);
    if (PreviousCellId)
    {
        FAuracronGridCell* PreviousCell = GridCells.Find(*PreviousCellId);
        if (PreviousCell)
        {
            PreviousCell->ActorIds.Remove(ActorId);
            PreviousCell->ActorCount = PreviousCell->ActorIds.Num();
            PreviousCell->UpdateDensity();
        }
    }
    
    // Find target cell
    FIntVector Coordinates = WorldToGridCoordinates(Location);
    FString CellId = CoordinatesToCellId(Coordinates, 0);
    
    FAuracronGridCell* Cell = GridCells.Find(CellId);
    if (!Cell)
    {
        // Create cell if it doesn't exist
        FAuracronGridCell NewCell = CreateCell(Coordinates, 0);
        GridCells.Add(NewCell.CellId, NewCell);
        CoordinateToCellMap.Add(Coordinates, NewCell.CellId);
        Cell = GridCells.Find(CellId);
    }
    
    if (Cell)
    {
        // Add actor to cell
        Cell->ActorIds.AddUnique(ActorId);
        Cell->ActorCount = Cell->ActorIds.Num();
        Cell->State = EAuracronGridCellState::Populated;
        Cell->UpdateDensity();
        
        // Update mapping
        ActorToCellMap.Add(ActorId, CellId);
        
        OnActorAddedToCell.Broadcast(ActorId, CellId);
        
        AURACRON_WP_LOG_VERBOSE(TEXT("Actor %s added to cell %s"), *ActorId, *CellId);
    }
    
    UpdateStatistics();
}

void UAuracronWorldPartitionGridManager::RemoveActorFromGrid(const FString& ActorId)
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&GridLock);

    FString* CellId = ActorToCellMap.Find(ActorId);
    if (!CellId)
    {
        return;
    }

    FAuracronGridCell* Cell = GridCells.Find(*CellId);
    if (Cell)
    {
        Cell->ActorIds.Remove(ActorId);
        Cell->ActorCount = Cell->ActorIds.Num();
        Cell->UpdateDensity();

        if (Cell->ActorCount == 0)
        {
            Cell->State = EAuracronGridCellState::Empty;
        }
    }

    ActorToCellMap.Remove(ActorId);
    UpdateStatistics();

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor %s removed from grid"), *ActorId);
}

void UAuracronWorldPartitionGridManager::UpdateActorLocation(const FString& ActorId, const FVector& NewLocation)
{
    // Simply remove and re-add at new location
    RemoveActorFromGrid(ActorId);
    AddActorToGrid(ActorId, NewLocation);
}

TArray<FString> UAuracronWorldPartitionGridManager::GetActorsInCell(const FString& CellId) const
{
    FScopeLock Lock(&GridLock);

    const FAuracronGridCell* Cell = GridCells.Find(CellId);
    if (Cell)
    {
        return Cell->ActorIds;
    }

    return TArray<FString>();
}

TArray<FString> UAuracronWorldPartitionGridManager::GetActorsInRadius(const FVector& Location, float Radius) const
{
    TArray<FAuracronGridCell> CellsInRadius = GetCellsInRadius(Location, Radius);
    TArray<FString> ActorsInRadius;

    for (const FAuracronGridCell& Cell : CellsInRadius)
    {
        ActorsInRadius.Append(Cell.ActorIds);
    }

    return ActorsInRadius;
}

FAuracronSpatialQueryResult UAuracronWorldPartitionGridManager::ExecuteSpatialQuery(const FAuracronSpatialQueryParams& QueryParams) const
{
    FAuracronSpatialQueryResult Result;
    FDateTime StartTime = FDateTime::Now();

    try
    {
        switch (QueryParams.QueryType)
        {
            case EAuracronSpatialQueryType::Point:
                Result.FoundCells = QueryCellsByPoint(QueryParams.QueryLocation);
                break;

            case EAuracronSpatialQueryType::Sphere:
                Result.FoundCells = QueryCellsBySphere(QueryParams.QueryLocation, QueryParams.QueryRadius);
                break;

            case EAuracronSpatialQueryType::Box:
                Result.FoundCells = QueryCellsByBox(QueryParams.QueryBox);
                break;

            case EAuracronSpatialQueryType::Ray:
                Result.FoundCells = QueryCellsByRay(QueryParams.QueryLocation, QueryParams.QueryDirection, QueryParams.QueryDistance);
                break;

            default:
                Result.ErrorMessage = TEXT("Unsupported query type");
                break;
        }

        // Collect actors from found cells
        for (const FAuracronGridCell& Cell : Result.FoundCells)
        {
            if (QueryParams.bIncludeEmptyCells || Cell.ActorCount > 0)
            {
                Result.FoundActors.Append(Cell.ActorIds);
            }
        }

        // Apply result limit
        if (QueryParams.MaxResults > 0 && Result.FoundActors.Num() > QueryParams.MaxResults)
        {
            Result.FoundActors.SetNum(QueryParams.MaxResults);
        }

        Result.TotalResults = Result.FoundActors.Num();
        Result.bQuerySuccessful = true;
    }
    catch (...)
    {
        Result.bQuerySuccessful = false;
        Result.ErrorMessage = TEXT("Query execution failed");
    }

    FDateTime EndTime = FDateTime::Now();
    Result.QueryTime = (EndTime - StartTime).GetTotalSeconds();

    // Update statistics
    {
        FScopeLock Lock(&StatisticsLock);
        Statistics.TotalQueries++;
        if (Result.bQuerySuccessful)
        {
            Statistics.SuccessfulQueries++;
        }
        Statistics.AverageQueryTime = (Statistics.AverageQueryTime + Result.QueryTime) / 2.0f;
    }

    return Result;
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::QueryCellsByPoint(const FVector& Point) const
{
    TArray<FAuracronGridCell> Result;
    FAuracronGridCell Cell = GetCellAtLocation(Point);

    if (!Cell.CellId.IsEmpty())
    {
        Result.Add(Cell);
    }

    return Result;
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::QueryCellsBySphere(const FVector& Center, float Radius) const
{
    return GetCellsInRadius(Center, Radius);
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::QueryCellsByBox(const FBox& Box) const
{
    return GetCellsInBox(Box);
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::QueryCellsByRay(const FVector& Origin, const FVector& Direction, float Distance) const
{
    TArray<FAuracronGridCell> Result;

    // Simple ray traversal through grid
    FVector CurrentPos = Origin;
    FVector Step = Direction.GetSafeNormal() * (Configuration.CellSize * 0.5f);
    float TraveledDistance = 0.0f;

    TSet<FString> VisitedCells;

    while (TraveledDistance < Distance)
    {
        FAuracronGridCell Cell = GetCellAtLocation(CurrentPos);

        if (!Cell.CellId.IsEmpty() && !VisitedCells.Contains(Cell.CellId))
        {
            Result.Add(Cell);
            VisitedCells.Add(Cell.CellId);
        }

        CurrentPos += Step;
        TraveledDistance += Step.Size();
    }

    return Result;
}

FIntVector UAuracronWorldPartitionGridManager::WorldToGridCoordinates(const FVector& WorldLocation) const
{
    FVector RelativeLocation = WorldLocation - Configuration.WorldOrigin;

    int32 X = FMath::FloorToInt(RelativeLocation.X / Configuration.CellSize);
    int32 Y = FMath::FloorToInt(RelativeLocation.Y / Configuration.CellSize);
    int32 Z = FMath::FloorToInt(RelativeLocation.Z / Configuration.CellSize);

    return FIntVector(X, Y, Z);
}

FVector UAuracronWorldPartitionGridManager::GridToWorldCoordinates(const FIntVector& GridCoordinates) const
{
    FVector WorldLocation;
    WorldLocation.X = GridCoordinates.X * Configuration.CellSize + (Configuration.CellSize * 0.5f);
    WorldLocation.Y = GridCoordinates.Y * Configuration.CellSize + (Configuration.CellSize * 0.5f);
    WorldLocation.Z = GridCoordinates.Z * Configuration.CellSize + (Configuration.CellSize * 0.5f);

    return WorldLocation + Configuration.WorldOrigin;
}

FString UAuracronWorldPartitionGridManager::CoordinatesToCellId(const FIntVector& Coordinates, int32 SubdivisionLevel) const
{
    return FString::Printf(TEXT("Cell_%d_%d_%d_L%d"), Coordinates.X, Coordinates.Y, Coordinates.Z, SubdivisionLevel);
}

FIntVector UAuracronWorldPartitionGridManager::CellIdToCoordinates(const FString& CellId) const
{
    // Parse cell ID format: "Cell_X_Y_Z_LLevel"
    TArray<FString> Parts;
    CellId.ParseIntoArray(Parts, TEXT("_"));

    if (Parts.Num() >= 4)
    {
        int32 X = FCString::Atoi(*Parts[1]);
        int32 Y = FCString::Atoi(*Parts[2]);
        int32 Z = FCString::Atoi(*Parts[3]);

        return FIntVector(X, Y, Z);
    }

    return FIntVector::ZeroValue;
}

void UAuracronWorldPartitionGridManager::SetConfiguration(const FAuracronGridConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Grid configuration updated"));
}

FAuracronGridConfiguration UAuracronWorldPartitionGridManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronWorldPartitionGridManager::SetCellSize(int32 NewCellSize)
{
    Configuration.CellSize = FMath::Clamp(NewCellSize, Configuration.MinCellSize, Configuration.MaxCellSize);

    AURACRON_WP_LOG_INFO(TEXT("Cell size set to: %d"), Configuration.CellSize);
}

int32 UAuracronWorldPartitionGridManager::GetCellSize() const
{
    return Configuration.CellSize;
}

FAuracronGridStatistics UAuracronWorldPartitionGridManager::GetGridStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronGridStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionGridManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronGridStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Grid statistics reset"));
}

int32 UAuracronWorldPartitionGridManager::GetTotalCellCount() const
{
    FScopeLock Lock(&GridLock);
    return GridCells.Num();
}

int32 UAuracronWorldPartitionGridManager::GetPopulatedCellCount() const
{
    FScopeLock Lock(&GridLock);

    int32 PopulatedCount = 0;
    for (const auto& CellPair : GridCells)
    {
        if (CellPair.Value.ActorCount > 0)
        {
            PopulatedCount++;
        }
    }

    return PopulatedCount;
}

float UAuracronWorldPartitionGridManager::GetAverageCellDensity() const
{
    FScopeLock Lock(&GridLock);

    if (GridCells.Num() == 0)
    {
        return 0.0f;
    }

    float TotalDensity = 0.0f;
    for (const auto& CellPair : GridCells)
    {
        TotalDensity += CellPair.Value.Density;
    }

    return TotalDensity / GridCells.Num();
}

void UAuracronWorldPartitionGridManager::AutoSubdivideCells()
{
    TArray<FString> CellsToSubdivide;

    // Find cells that need subdivision
    for (const auto& CellPair : GridCells)
    {
        const FAuracronGridCell& Cell = CellPair.Value;
        if (Cell.ShouldSubdivide(Configuration.MaxActorsPerCell))
        {
            CellsToSubdivide.Add(Cell.CellId);
        }
    }

    // Subdivide cells
    for (const FString& CellId : CellsToSubdivide)
    {
        SubdivideCell(CellId);
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Auto-subdivided %d cells"), CellsToSubdivide.Num());
}

void UAuracronWorldPartitionGridManager::AutoMergeCells()
{
    // Implementation for auto-merging underpopulated cells
    FScopeLock Lock(&GridLock);
    
    TArray<FString> CellsToMerge;
    TMap<FString, TArray<FString>> MergeCandidates;
    
    // Find underpopulated cells that are candidates for merging
    for (const auto& CellPair : GridCells)
    {
        const FAuracronGridCell& Cell = CellPair.Value;
        
        // Check if cell is underpopulated (below merge threshold)
        float DensityThreshold = GridConfiguration.MergeDensityThreshold;
        if (Cell.ActorCount < DensityThreshold && Cell.SubdivisionLevel > 0)
        {
            CellsToMerge.Add(CellPair.Key);
        }
    }
    
    // Group adjacent underpopulated cells for merging
    for (const FString& CellId : CellsToMerge)
    {
        const FAuracronGridCell* Cell = GridCells.Find(CellId);
        if (!Cell) continue;
        
        // Find adjacent cells at the same subdivision level
        TArray<FString> AdjacentCells = FindAdjacentCells(CellId);
        TArray<FString> MergeGroup;
        
        for (const FString& AdjacentId : AdjacentCells)
        {
            const FAuracronGridCell* AdjacentCell = GridCells.Find(AdjacentId);
            if (AdjacentCell && 
                AdjacentCell->SubdivisionLevel == Cell->SubdivisionLevel &&
                AdjacentCell->ActorCount < GridConfiguration.MergeDensityThreshold)
            {
                MergeGroup.Add(AdjacentId);
            }
        }
        
        // Only merge if we have enough adjacent underpopulated cells
        if (MergeGroup.Num() >= 2)
        {
            MergeGroup.Add(CellId);
            MergeCandidates.Add(CellId, MergeGroup);
        }
    }
    
    // Execute merging operations
    int32 MergedCellCount = 0;
    for (const auto& MergePair : MergeCandidates)
    {
        const TArray<FString>& CellsToMergeGroup = MergePair.Value;
        
        if (CellsToMergeGroup.Num() < 2) continue;
        
        // Create merged cell from the group
        FAuracronGridCell MergedCell = CreateMergedCell(CellsToMergeGroup);
        
        // Remove individual cells and add merged cell
        for (const FString& CellToRemove : CellsToMergeGroup)
        {
            GridCells.Remove(CellToRemove);
        }
        
        GridCells.Add(MergedCell.CellId, MergedCell);
        MergedCellCount++;
        
        // Update spatial hash
        UpdateSpatialHash(MergedCell);
        
        // Notify listeners of cell merge
        OnCellMerged.Broadcast(MergedCell.CellId, CellsToMergeGroup);
    }
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Auto-merged %d cell groups, processed %d total cells"), MergedCellCount, CellsToMerge.Num());
}

TArray<FAuracronGridCell> UAuracronWorldPartitionGridManager::SubdivideCell(const FString& CellId)
{
    TArray<FAuracronGridCell> ChildCells;

    FScopeLock Lock(&GridLock);

    FAuracronGridCell* ParentCell = GridCells.Find(CellId);
    if (!ParentCell)
    {
        return ChildCells;
    }

    if (ParentCell->SubdivisionLevel >= Configuration.MaxSubdivisionLevels)
    {
        return ChildCells;
    }

    // Create 8 child cells (2x2x2 subdivision)
    int32 ChildLevel = ParentCell->SubdivisionLevel + 1;
    FIntVector BaseCoords = ParentCell->Coordinates * 2; // Double coordinates for next level

    for (int32 X = 0; X < 2; X++)
    {
        for (int32 Y = 0; Y < 2; Y++)
        {
            for (int32 Z = 0; Z < 2; Z++)
            {
                FIntVector ChildCoords = BaseCoords + FIntVector(X, Y, Z);
                FAuracronGridCell ChildCell = CreateCell(ChildCoords, ChildLevel);
                ChildCell.ParentCell = CellId;

                ChildCells.Add(ChildCell);
                GridCells.Add(ChildCell.CellId, ChildCell);
                CoordinateToCellMap.Add(ChildCoords, ChildCell.CellId);

                ParentCell->ChildCells.Add(ChildCell.CellId);
            }
        }
    }

    OnCellSubdivided.Broadcast(CellId, ChildCells);
    UpdateStatistics();

    AURACRON_WP_LOG_VERBOSE(TEXT("Cell %s subdivided into %d child cells"), *CellId, ChildCells.Num());

    return ChildCells;
}

void UAuracronWorldPartitionGridManager::UpdateStatistics()
{
    FScopeLock Lock(&StatisticsLock);

    Statistics.TotalCells = GridCells.Num();
    Statistics.PopulatedCells = GetPopulatedCellCount();
    Statistics.EmptyCells = Statistics.TotalCells - Statistics.PopulatedCells;

    // Calculate max subdivision level
    Statistics.MaxSubdivisionLevel = 0;
    Statistics.TotalActors = 0;

    for (const auto& CellPair : GridCells)
    {
        const FAuracronGridCell& Cell = CellPair.Value;
        Statistics.MaxSubdivisionLevel = FMath::Max(Statistics.MaxSubdivisionLevel, Cell.SubdivisionLevel);
        Statistics.TotalActors += Cell.ActorCount;
    }

    // Calculate memory usage (rough estimate)
    Statistics.MemoryUsageMB = (GridCells.Num() * sizeof(FAuracronGridCell) +
                               ActorToCellMap.Num() * (sizeof(FString) * 2) +
                               CoordinateToCellMap.Num() * (sizeof(FIntVector) + sizeof(FString))) / (1024.0f * 1024.0f);

    Statistics.UpdateCalculatedFields();
}

FBox UAuracronWorldPartitionGridManager::CalculateCellBounds(const FIntVector& Coordinates, int32 SubdivisionLevel) const
{
    float CellSize = Configuration.CellSize / FMath::Pow(2.0f, SubdivisionLevel);

    FVector CellMin = Configuration.WorldOrigin + FVector(Coordinates) * CellSize;
    FVector CellMax = CellMin + FVector(CellSize);

    return FBox(CellMin, CellMax);
}

bool UAuracronWorldPartitionGridManager::IsValidCoordinate(const FIntVector& Coordinates) const
{
    FVector WorldLocation = GridToWorldCoordinates(Coordinates);
    return Configuration.WorldBounds.IsInside(WorldLocation);
}

void UAuracronWorldPartitionGridManager::OnCellCreatedInternal(const FAuracronGridCell& Cell)
{
    OnCellCreated.Broadcast(Cell);
    AURACRON_WP_LOG_VERBOSE(TEXT("Cell created: %s"), *Cell.CellId);
}

void UAuracronWorldPartitionGridManager::OnCellRemovedInternal(const FString& CellId)
{
    OnCellRemoved.Broadcast(CellId);
    AURACRON_WP_LOG_VERBOSE(TEXT("Cell removed: %s"), *CellId);
}

void UAuracronWorldPartitionGridManager::ValidateConfiguration()
{
    // Validate cell sizes
    Configuration.CellSize = FMath::Clamp(Configuration.CellSize, Configuration.MinCellSize, Configuration.MaxCellSize);

    // Validate subdivision levels
    Configuration.MaxSubdivisionLevels = FMath::Clamp(Configuration.MaxSubdivisionLevels, 1, 16);

    // Validate max actors per cell
    if (Configuration.MaxActorsPerCell <= 0)
    {
        Configuration.MaxActorsPerCell = 1000;
    }

    // Validate concurrent operations
    if (Configuration.MaxConcurrentOperations <= 0)
    {
        Configuration.MaxConcurrentOperations = 4;
    }
}

// =============================================================================
// GRID MANAGEMENT HELPER FUNCTIONS
// =============================================================================

TArray<FString> UAuracronWorldPartitionGridManager::FindAdjacentCells(const FString& CellId) const
{
    TArray<FString> AdjacentCells;
    
    const FAuracronGridCell* Cell = GridCells.Find(CellId);
    if (!Cell)
    {
        return AdjacentCells;
    }
    
    // Convert cell ID back to coordinates
    FIntVector CellCoords = CellIdToCoordinates(CellId);
    int32 SubdivisionLevel = Cell->SubdivisionLevel;
    
    // Check all 26 adjacent positions in 3D space (including diagonals)
    for (int32 X = -1; X <= 1; X++)
    {
        for (int32 Y = -1; Y <= 1; Y++)
        {
            for (int32 Z = -1; Z <= 1; Z++)
            {
                // Skip the center cell (itself)
                if (X == 0 && Y == 0 && Z == 0)
                {
                    continue;
                }
                
                FIntVector AdjacentCoords = CellCoords + FIntVector(X, Y, Z);
                
                // Check if coordinates are valid
                if (IsValidCoordinate(AdjacentCoords))
                {
                    FString AdjacentCellId = CoordinatesToCellId(AdjacentCoords, SubdivisionLevel);
                    
                    // Check if the adjacent cell exists
                    if (GridCells.Contains(AdjacentCellId))
                    {
                        AdjacentCells.Add(AdjacentCellId);
                    }
                }
            }
        }
    }
    
    return AdjacentCells;
}

FAuracronGridCell UAuracronWorldPartitionGridManager::CreateMergedCell(const TArray<FString>& CellsToMerge)
{
    if (CellsToMerge.Num() == 0)
    {
        return FAuracronGridCell();
    }
    
    // Get the first cell as reference
    const FAuracronGridCell* FirstCell = GridCells.Find(CellsToMerge[0]);
    if (!FirstCell)
    {
        return FAuracronGridCell();
    }
    
    // Calculate merged bounds and properties
    FBox MergedBounds = FirstCell->Bounds;
    int32 TotalActorCount = FirstCell->ActorCount;
    int32 NewSubdivisionLevel = FMath::Max(0, FirstCell->SubdivisionLevel - 1);
    TArray<FString> MergedActorIds = FirstCell->ActorIds;
    
    // Merge all cells
    for (int32 i = 1; i < CellsToMerge.Num(); i++)
    {
        const FAuracronGridCell* CellToMerge = GridCells.Find(CellsToMerge[i]);
        if (CellToMerge)
        {
            MergedBounds += CellToMerge->Bounds;
            TotalActorCount += CellToMerge->ActorCount;
            
            // Merge actor lists
            for (const FString& ActorId : CellToMerge->ActorIds)
            {
                MergedActorIds.AddUnique(ActorId);
            }
        }
    }
    
    // Create new merged cell
    FAuracronGridCell MergedCell;
    MergedCell.CellId = FGuid::NewGuid().ToString(); // Generate new unique ID
    MergedCell.Coordinates = WorldToGridCoordinates(MergedBounds.GetCenter());
    MergedCell.SubdivisionLevel = NewSubdivisionLevel;
    MergedCell.Bounds = MergedBounds;
    MergedCell.ActorCount = MergedActorIds.Num(); // Use actual count from merged list
    MergedCell.ActorIds = MergedActorIds;
    MergedCell.State = EAuracronGridCellState::Populated;
    MergedCell.CreationTime = FDateTime::Now();
    MergedCell.LastUpdateTime = FDateTime::Now();
    
    // Update density
    MergedCell.UpdateDensity();
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Created merged cell %s from %d cells with %d actors"), 
                           *MergedCell.CellId, CellsToMerge.Num(), MergedCell.ActorCount);
    
    return MergedCell;
}

void UAuracronWorldPartitionGridManager::UpdateSpatialHash(const FAuracronGridCell& Cell)
{
    FScopeLock Lock(&SpatialHashLock);
    
    // Remove old entries for this cell
    for (auto It = SpatialHashMap.CreateIterator(); It; ++It)
    {
        TArray<FString>& CellList = It.Value();
        CellList.RemoveAll([&Cell](const FString& CellId)
        {
            return CellId == Cell.CellId;
        });
        
        // Remove empty entries
        if (CellList.Num() == 0)
        {
            It.RemoveCurrent();
        }
    }
    
    // Add new entry
    FIntVector HashKey = Cell.Coordinates;
    
    if (!SpatialHashMap.Contains(HashKey))
    {
        SpatialHashMap.Add(HashKey, TArray<FString>());
    }
    
    SpatialHashMap[HashKey].AddUnique(Cell.CellId);
    
    AURACRON_WP_LOG_VeryVerbose(TEXT("Updated spatial hash for cell %s at coordinates (%d, %d, %d)"), 
                                *Cell.CellId, HashKey.X, HashKey.Y, HashKey.Z);
}
