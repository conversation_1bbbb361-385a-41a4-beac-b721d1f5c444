# Script para corrigir duplicados entre módulos internos

Write-Host "Corrigindo duplicados entre módulos internos..."

# 1. Renomear EAuracronFoliagePlacementMode para EAuracronFoliageBridgePlacementMode em AuracronFoliageBridge.h
$file1 = "Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageBridge.h"
$content1 = Get-Content $file1 -Raw
$content1 = $content1 -replace "enum class EAuracronFoliagePlacementMode", "enum class EAuracronFoliageBridgePlacementMode"
$content1 = $content1 -replace "EAuracronFoliagePlacementMode::", "EAuracronFoliageBridgePlacementMode::"
$content1 = $content1 -replace "EAuracronFoliagePlacementMode ", "EAuracronFoliageBridgePlacementMode "
Set-Content -Path $file1 -Value $content1 -NoNewline
Write-Host "Renomeado EAuracronFoliagePlacementMode para EAuracronFoliageBridgePlacementMode"

# 2. Renomear EAuracronFoliageLODMode para EAuracronFoliageBridgeLODMode em AuracronFoliageBridge.h
$content1 = Get-Content $file1 -Raw
$content1 = $content1 -replace "enum class EAuracronFoliageLODMode", "enum class EAuracronFoliageBridgeLODMode"
$content1 = $content1 -replace "EAuracronFoliageLODMode::", "EAuracronFoliageBridgeLODMode::"
$content1 = $content1 -replace "EAuracronFoliageLODMode ", "EAuracronFoliageBridgeLODMode "
Set-Content -Path $file1 -Value $content1 -NoNewline
Write-Host "Renomeado EAuracronFoliageLODMode para EAuracronFoliageBridgeLODMode"

# 3. Renomear EAuracronAudioType para EAuracronWorldPartitionAudioType em AuracronWorldPartitionAudio.h
$file2 = "Auracron\Source\AuracronWorldPartitionBridge\Public\AuracronWorldPartitionAudio.h"
$content2 = Get-Content $file2 -Raw
$content2 = $content2 -replace "enum class EAuracronAudioType", "enum class EAuracronWorldPartitionAudioType"
$content2 = $content2 -replace "EAuracronAudioType::", "EAuracronWorldPartitionAudioType::"
$content2 = $content2 -replace "EAuracronAudioType ", "EAuracronWorldPartitionAudioType "
Set-Content -Path $file2 -Value $content2 -NoNewline
Write-Host "Renomeado EAuracronAudioType para EAuracronWorldPartitionAudioType"

# 4. Renomear FAuracronAudioConfiguration para FAuracronWorldPartitionAudioConfiguration em AuracronWorldPartitionAudio.h
$content2 = Get-Content $file2 -Raw
$content2 = $content2 -replace "struct FAuracronAudioConfiguration", "struct FAuracronWorldPartitionAudioConfiguration"
$content2 = $content2 -replace "FAuracronAudioConfiguration ", "FAuracronWorldPartitionAudioConfiguration "
Set-Content -Path $file2 -Value $content2 -NoNewline
Write-Host "Renomeado FAuracronAudioConfiguration para FAuracronWorldPartitionAudioConfiguration"

# 5. Renomear EAuracronSpatialQueryType para EAuracronGridSpatialQueryType em AuracronWorldPartitionGrid.h
$file3 = "Auracron\Source\AuracronWorldPartitionBridge\Public\AuracronWorldPartitionGrid.h"
$content3 = Get-Content $file3 -Raw
$content3 = $content3 -replace "enum class EAuracronSpatialQueryType", "enum class EAuracronGridSpatialQueryType"
$content3 = $content3 -replace "EAuracronSpatialQueryType::", "EAuracronGridSpatialQueryType::"
$content3 = $content3 -replace "EAuracronSpatialQueryType ", "EAuracronGridSpatialQueryType "
Set-Content -Path $file3 -Value $content3 -NoNewline
Write-Host "Renomeado EAuracronSpatialQueryType para EAuracronGridSpatialQueryType"

# 6. Renomear FAuracronSpatialQueryResult para FAuracronGridSpatialQueryResult em AuracronWorldPartitionGrid.h
$content3 = Get-Content $file3 -Raw
$content3 = $content3 -replace "struct FAuracronSpatialQueryResult", "struct FAuracronGridSpatialQueryResult"
$content3 = $content3 -replace "FAuracronSpatialQueryResult ", "FAuracronGridSpatialQueryResult "
Set-Content -Path $file3 -Value $content3 -NoNewline
Write-Host "Renomeado FAuracronSpatialQueryResult para FAuracronGridSpatialQueryResult"

Write-Host "Correções de duplicados internos aplicadas com sucesso!"
