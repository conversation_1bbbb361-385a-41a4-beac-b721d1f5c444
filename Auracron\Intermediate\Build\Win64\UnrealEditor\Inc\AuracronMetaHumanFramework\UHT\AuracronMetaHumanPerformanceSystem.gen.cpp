// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/Systems/AuracronMetaHumanPerformanceSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanPerformanceSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric();
AURACRONMETAHUMANFRAMEWORK_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature();
AURACRONMETAHUMANFRAMEWORK_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronOptimizationConfig();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceSample();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceStats();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPerformanceMetric ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPerformanceMetric;
static UEnum* EAuracronPerformanceMetric_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceMetric.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPerformanceMetric.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronPerformanceMetric"));
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceMetric.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronPerformanceMetric>()
{
	return EAuracronPerformanceMetric_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AnimationCost.DisplayName", "Animation Cost" },
		{ "AnimationCost.Name", "EAuracronPerformanceMetric::AnimationCost" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Monitoring Types\n */" },
#endif
		{ "CPUUsage.DisplayName", "CPU Usage" },
		{ "CPUUsage.Name", "EAuracronPerformanceMetric::CPUUsage" },
		{ "DrawCalls.DisplayName", "Draw Calls" },
		{ "DrawCalls.Name", "EAuracronPerformanceMetric::DrawCalls" },
		{ "FrameRate.DisplayName", "Frame Rate" },
		{ "FrameRate.Name", "EAuracronPerformanceMetric::FrameRate" },
		{ "GPUUsage.DisplayName", "GPU Usage" },
		{ "GPUUsage.Name", "EAuracronPerformanceMetric::GPUUsage" },
		{ "LoadingTime.DisplayName", "Loading Time" },
		{ "LoadingTime.Name", "EAuracronPerformanceMetric::LoadingTime" },
		{ "MemoryUsage.DisplayName", "Memory Usage" },
		{ "MemoryUsage.Name", "EAuracronPerformanceMetric::MemoryUsage" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
		{ "NetworkLatency.DisplayName", "Network Latency" },
		{ "NetworkLatency.Name", "EAuracronPerformanceMetric::NetworkLatency" },
		{ "PhysicsCost.DisplayName", "Physics Cost" },
		{ "PhysicsCost.Name", "EAuracronPerformanceMetric::PhysicsCost" },
		{ "RenderingCost.DisplayName", "Rendering Cost" },
		{ "RenderingCost.Name", "EAuracronPerformanceMetric::RenderingCost" },
		{ "TextureMemory.DisplayName", "Texture Memory" },
		{ "TextureMemory.Name", "EAuracronPerformanceMetric::TextureMemory" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Monitoring Types" },
#endif
		{ "Triangles.DisplayName", "Triangle Count" },
		{ "Triangles.Name", "EAuracronPerformanceMetric::Triangles" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPerformanceMetric::FrameRate", (int64)EAuracronPerformanceMetric::FrameRate },
		{ "EAuracronPerformanceMetric::MemoryUsage", (int64)EAuracronPerformanceMetric::MemoryUsage },
		{ "EAuracronPerformanceMetric::CPUUsage", (int64)EAuracronPerformanceMetric::CPUUsage },
		{ "EAuracronPerformanceMetric::GPUUsage", (int64)EAuracronPerformanceMetric::GPUUsage },
		{ "EAuracronPerformanceMetric::DrawCalls", (int64)EAuracronPerformanceMetric::DrawCalls },
		{ "EAuracronPerformanceMetric::Triangles", (int64)EAuracronPerformanceMetric::Triangles },
		{ "EAuracronPerformanceMetric::TextureMemory", (int64)EAuracronPerformanceMetric::TextureMemory },
		{ "EAuracronPerformanceMetric::AnimationCost", (int64)EAuracronPerformanceMetric::AnimationCost },
		{ "EAuracronPerformanceMetric::PhysicsCost", (int64)EAuracronPerformanceMetric::PhysicsCost },
		{ "EAuracronPerformanceMetric::RenderingCost", (int64)EAuracronPerformanceMetric::RenderingCost },
		{ "EAuracronPerformanceMetric::LoadingTime", (int64)EAuracronPerformanceMetric::LoadingTime },
		{ "EAuracronPerformanceMetric::NetworkLatency", (int64)EAuracronPerformanceMetric::NetworkLatency },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronPerformanceMetric",
	"EAuracronPerformanceMetric",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceMetric.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPerformanceMetric.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPerformanceMetric_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceMetric.InnerSingleton;
}
// ********** End Enum EAuracronPerformanceMetric **************************************************

// ********** Begin Enum EAuracronOptimizationLevel ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronOptimizationLevel;
static UEnum* EAuracronOptimizationLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronOptimizationLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronOptimizationLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronOptimizationLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronOptimizationLevel.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronOptimizationLevel>()
{
	return EAuracronOptimizationLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAuracronOptimizationLevel::Aggressive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EAuracronOptimizationLevel::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Optimization Levels\n */" },
#endif
		{ "Conservative.DisplayName", "Conservative" },
		{ "Conservative.Name", "EAuracronOptimizationLevel::Conservative" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronOptimizationLevel::Custom" },
		{ "Maximum.DisplayName", "Maximum" },
		{ "Maximum.Name", "EAuracronOptimizationLevel::Maximum" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
		{ "None.DisplayName", "No Optimization" },
		{ "None.Name", "EAuracronOptimizationLevel::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Optimization Levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronOptimizationLevel::None", (int64)EAuracronOptimizationLevel::None },
		{ "EAuracronOptimizationLevel::Conservative", (int64)EAuracronOptimizationLevel::Conservative },
		{ "EAuracronOptimizationLevel::Balanced", (int64)EAuracronOptimizationLevel::Balanced },
		{ "EAuracronOptimizationLevel::Aggressive", (int64)EAuracronOptimizationLevel::Aggressive },
		{ "EAuracronOptimizationLevel::Maximum", (int64)EAuracronOptimizationLevel::Maximum },
		{ "EAuracronOptimizationLevel::Custom", (int64)EAuracronOptimizationLevel::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronOptimizationLevel",
	"EAuracronOptimizationLevel",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronOptimizationLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronOptimizationLevel.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronOptimizationLevel.InnerSingleton;
}
// ********** End Enum EAuracronOptimizationLevel **************************************************

// ********** Begin ScriptStruct FAuracronPerformanceSample ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample;
class UScriptStruct* FAuracronPerformanceSample::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPerformanceSample, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronPerformanceSample"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Sample Data\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Sample Data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp of the sample */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp of the sample" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameRate_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frame rate in FPS */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frame rate in FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTimeMS_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frame time in milliseconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frame time in milliseconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Memory usage in MB */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory usage in MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUUsagePercent_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** CPU usage percentage */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CPU usage percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUUsagePercent_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GPU usage percentage */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU usage percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Draw call count */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Draw call count" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Triangles_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Triangle count */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Triangle count" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureMemoryMB_MetaData[] = {
		{ "Category", "Sample" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Texture memory usage in MB */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture memory usage in MB" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTimeMS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUUsagePercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUUsagePercent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Triangles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TextureMemoryMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPerformanceSample>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_FrameRate = { "FrameRate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, FrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameRate_MetaData), NewProp_FrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_FrameTimeMS = { "FrameTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, FrameTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTimeMS_MetaData), NewProp_FrameTimeMS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_CPUUsagePercent = { "CPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, CPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUUsagePercent_MetaData), NewProp_CPUUsagePercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_GPUUsagePercent = { "GPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, GPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUUsagePercent_MetaData), NewProp_GPUUsagePercent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_Triangles = { "Triangles", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, Triangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Triangles_MetaData), NewProp_Triangles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_TextureMemoryMB = { "TextureMemoryMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceSample, TextureMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureMemoryMB_MetaData), NewProp_TextureMemoryMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_FrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_FrameTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_CPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_GPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_Triangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewProp_TextureMemoryMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronPerformanceSample",
	Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::PropPointers),
	sizeof(FAuracronPerformanceSample),
	alignof(FAuracronPerformanceSample),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceSample()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPerformanceSample ******************************************

// ********** Begin ScriptStruct FAuracronPerformanceStats *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats;
class UScriptStruct* FAuracronPerformanceStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPerformanceStats, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronPerformanceStats"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Statistics\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameRate_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Average frame rate */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Average frame rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinFrameRate_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum frame rate */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum frame rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFrameRate_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum frame rate */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum frame rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageMemoryUsageMB_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Average memory usage in MB */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Average memory usage in MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakMemoryUsageMB_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Peak memory usage in MB */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Peak memory usage in MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageCPUUsage_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Average CPU usage percentage */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Average CPU usage percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakCPUUsage_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Peak CPU usage percentage */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Peak CPU usage percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageGPUUsage_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Average GPU usage percentage */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Average GPU usage percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakGPUUsage_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Peak GPU usage percentage */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Peak GPU usage percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalSamples_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total samples collected */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total samples collected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoringDurationSeconds_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Monitoring duration in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitoring duration in seconds" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageCPUUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakCPUUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageGPUUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakGPUUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalSamples;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MonitoringDurationSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPerformanceStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageFrameRate = { "AverageFrameRate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, AverageFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameRate_MetaData), NewProp_AverageFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_MinFrameRate = { "MinFrameRate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, MinFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinFrameRate_MetaData), NewProp_MinFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_MaxFrameRate = { "MaxFrameRate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, MaxFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFrameRate_MetaData), NewProp_MaxFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageMemoryUsageMB = { "AverageMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, AverageMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageMemoryUsageMB_MetaData), NewProp_AverageMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_PeakMemoryUsageMB = { "PeakMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, PeakMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakMemoryUsageMB_MetaData), NewProp_PeakMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageCPUUsage = { "AverageCPUUsage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, AverageCPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageCPUUsage_MetaData), NewProp_AverageCPUUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_PeakCPUUsage = { "PeakCPUUsage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, PeakCPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakCPUUsage_MetaData), NewProp_PeakCPUUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageGPUUsage = { "AverageGPUUsage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, AverageGPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageGPUUsage_MetaData), NewProp_AverageGPUUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_PeakGPUUsage = { "PeakGPUUsage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, PeakGPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakGPUUsage_MetaData), NewProp_PeakGPUUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_TotalSamples = { "TotalSamples", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, TotalSamples), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalSamples_MetaData), NewProp_TotalSamples_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_MonitoringDurationSeconds = { "MonitoringDurationSeconds", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceStats, MonitoringDurationSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoringDurationSeconds_MetaData), NewProp_MonitoringDurationSeconds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_MinFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_MaxFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_PeakMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageCPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_PeakCPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_AverageGPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_PeakGPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_TotalSamples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewProp_MonitoringDurationSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronPerformanceStats",
	Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::PropPointers),
	sizeof(FAuracronPerformanceStats),
	alignof(FAuracronPerformanceStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceStats()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPerformanceStats *******************************************

// ********** Begin ScriptStruct FAuracronOptimizationConfig ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig;
class UScriptStruct* FAuracronOptimizationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronOptimizationConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronOptimizationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Optimization Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationLevel_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimization level */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetFrameRate_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ClampMax", "120" },
		{ "ClampMin", "30" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target frame rate */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target frame rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryUsageMB_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ClampMax", "8192" },
		{ "ClampMin", "512" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum memory usage in MB */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum memory usage in MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODOptimization_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable LOD optimization */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable LOD optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTextureStreamingOptimization_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable texture streaming optimization */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable texture streaming optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAnimationOptimization_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable animation optimization */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable animation optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCullingOptimization_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable culling optimization */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable culling optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUOptimization_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable GPU optimization */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable GPU optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetFrameRate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMemoryUsageMB;
	static void NewProp_bEnableLODOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODOptimization;
	static void NewProp_bEnableTextureStreamingOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTextureStreamingOptimization;
	static void NewProp_bEnableAnimationOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAnimationOptimization;
	static void NewProp_bEnableCullingOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCullingOptimization;
	static void NewProp_bEnableGPUOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUOptimization;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronOptimizationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_OptimizationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_OptimizationLevel = { "OptimizationLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOptimizationConfig, OptimizationLevel), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronOptimizationLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationLevel_MetaData), NewProp_OptimizationLevel_MetaData) }; // 1451672887
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_TargetFrameRate = { "TargetFrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOptimizationConfig, TargetFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetFrameRate_MetaData), NewProp_TargetFrameRate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_MaxMemoryUsageMB = { "MaxMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOptimizationConfig, MaxMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryUsageMB_MetaData), NewProp_MaxMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableLODOptimization_SetBit(void* Obj)
{
	((FAuracronOptimizationConfig*)Obj)->bEnableLODOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableLODOptimization = { "bEnableLODOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronOptimizationConfig), &Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableLODOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODOptimization_MetaData), NewProp_bEnableLODOptimization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableTextureStreamingOptimization_SetBit(void* Obj)
{
	((FAuracronOptimizationConfig*)Obj)->bEnableTextureStreamingOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableTextureStreamingOptimization = { "bEnableTextureStreamingOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronOptimizationConfig), &Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableTextureStreamingOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTextureStreamingOptimization_MetaData), NewProp_bEnableTextureStreamingOptimization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableAnimationOptimization_SetBit(void* Obj)
{
	((FAuracronOptimizationConfig*)Obj)->bEnableAnimationOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableAnimationOptimization = { "bEnableAnimationOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronOptimizationConfig), &Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableAnimationOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAnimationOptimization_MetaData), NewProp_bEnableAnimationOptimization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableCullingOptimization_SetBit(void* Obj)
{
	((FAuracronOptimizationConfig*)Obj)->bEnableCullingOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableCullingOptimization = { "bEnableCullingOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronOptimizationConfig), &Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableCullingOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCullingOptimization_MetaData), NewProp_bEnableCullingOptimization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableGPUOptimization_SetBit(void* Obj)
{
	((FAuracronOptimizationConfig*)Obj)->bEnableGPUOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableGPUOptimization = { "bEnableGPUOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronOptimizationConfig), &Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableGPUOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUOptimization_MetaData), NewProp_bEnableGPUOptimization_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_OptimizationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_OptimizationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_TargetFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_MaxMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableLODOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableTextureStreamingOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableAnimationOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableCullingOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewProp_bEnableGPUOptimization,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronOptimizationConfig",
	Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::PropPointers),
	sizeof(FAuracronOptimizationConfig),
	alignof(FAuracronOptimizationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronOptimizationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronOptimizationConfig *****************************************

// ********** Begin Delegate FAuracronPerformanceAlert *********************************************
struct Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronPerformanceAlert_Parms
	{
		FString AlertMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlertMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AlertMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::NewProp_AlertMessage = { "AlertMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronPerformanceAlert_Parms, AlertMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlertMessage_MetaData), NewProp_AlertMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::NewProp_AlertMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework, nullptr, "AuracronPerformanceAlert__DelegateSignature", Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronPerformanceAlert_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronPerformanceAlert_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FAuracronPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& AuracronPerformanceAlert, const FString& AlertMessage)
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronPerformanceAlert_Parms
	{
		FString AlertMessage;
	};
	_Script_AuracronMetaHumanFramework_eventAuracronPerformanceAlert_Parms Parms;
	Parms.AlertMessage=AlertMessage;
	AuracronPerformanceAlert.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FAuracronPerformanceAlert ***********************************************

// ********** Begin Delegate FAuracronOptimizationComplete *****************************************
struct Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronOptimizationComplete_Parms
	{
		FString OptimizationResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OptimizationResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::NewProp_OptimizationResult = { "OptimizationResult", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronOptimizationComplete_Parms, OptimizationResult), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationResult_MetaData), NewProp_OptimizationResult_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::NewProp_OptimizationResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework, nullptr, "AuracronOptimizationComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronOptimizationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronOptimizationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FAuracronOptimizationComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronOptimizationComplete, const FString& OptimizationResult)
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronOptimizationComplete_Parms
	{
		FString OptimizationResult;
	};
	_Script_AuracronMetaHumanFramework_eventAuracronOptimizationComplete_Parms Parms;
	Parms.OptimizationResult=OptimizationResult;
	AuracronOptimizationComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FAuracronOptimizationComplete *******************************************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function ApplyPerformanceOptimization 
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventApplyPerformanceOptimization_Parms
	{
		FAuracronOptimizationConfig Config;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply performance optimization\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply performance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventApplyPerformanceOptimization_Parms, Config), Z_Construct_UScriptStruct_FAuracronOptimizationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 4046093474
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventApplyPerformanceOptimization_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventApplyPerformanceOptimization_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "ApplyPerformanceOptimization", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::AuracronMetaHumanPerformanceSystem_eventApplyPerformanceOptimization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::AuracronMetaHumanPerformanceSystem_eventApplyPerformanceOptimization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execApplyPerformanceOptimization)
{
	P_GET_STRUCT_REF(FAuracronOptimizationConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyPerformanceOptimization(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function ApplyPerformanceOptimization **

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function EnablePerformanceAlerts *****
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventEnablePerformanceAlerts_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Alerts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable performance alerts\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance alerts" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventEnablePerformanceAlerts_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventEnablePerformanceAlerts_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "EnablePerformanceAlerts", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::AuracronMetaHumanPerformanceSystem_eventEnablePerformanceAlerts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::AuracronMetaHumanPerformanceSystem_eventEnablePerformanceAlerts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execEnablePerformanceAlerts)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnablePerformanceAlerts(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function EnablePerformanceAlerts *******

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function ForceGarbageCollection ******
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ForceGarbageCollection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Force garbage collection\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Force garbage collection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ForceGarbageCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "ForceGarbageCollection", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ForceGarbageCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ForceGarbageCollection_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ForceGarbageCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ForceGarbageCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execForceGarbageCollection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceGarbageCollection();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function ForceGarbageCollection ********

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function GetCurrentPerformanceSample *
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventGetCurrentPerformanceSample_Parms
	{
		FAuracronPerformanceSample ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get current performance sample\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current performance sample" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventGetCurrentPerformanceSample_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPerformanceSample, METADATA_PARAMS(0, nullptr) }; // 719725561
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "GetCurrentPerformanceSample", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::AuracronMetaHumanPerformanceSystem_eventGetCurrentPerformanceSample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::AuracronMetaHumanPerformanceSystem_eventGetCurrentPerformanceSample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execGetCurrentPerformanceSample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPerformanceSample*)Z_Param__Result=P_THIS->GetCurrentPerformanceSample();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function GetCurrentPerformanceSample ***

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function GetGPUPerformanceMetrics ****
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventGetGPUPerformanceMetrics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get GPU performance metrics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get GPU performance metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventGetGPUPerformanceMetrics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "GetGPUPerformanceMetrics", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::AuracronMetaHumanPerformanceSystem_eventGetGPUPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::AuracronMetaHumanPerformanceSystem_eventGetGPUPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execGetGPUPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetGPUPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function GetGPUPerformanceMetrics ******

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function GetMemoryUsageBreakdown *****
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventGetMemoryUsageBreakdown_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get memory usage breakdown\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get memory usage breakdown" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventGetMemoryUsageBreakdown_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "GetMemoryUsageBreakdown", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::AuracronMetaHumanPerformanceSystem_eventGetMemoryUsageBreakdown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::AuracronMetaHumanPerformanceSystem_eventGetMemoryUsageBreakdown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execGetMemoryUsageBreakdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetMemoryUsageBreakdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function GetMemoryUsageBreakdown *******

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function GetPerformanceStatistics ****
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventGetPerformanceStatistics_Parms
	{
		FAuracronPerformanceStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get performance statistics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get performance statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventGetPerformanceStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPerformanceStats, METADATA_PARAMS(0, nullptr) }; // 3135539801
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "GetPerformanceStatistics", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::AuracronMetaHumanPerformanceSystem_eventGetPerformanceStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::AuracronMetaHumanPerformanceSystem_eventGetPerformanceStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execGetPerformanceStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPerformanceStats*)Z_Param__Result=P_THIS->GetPerformanceStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function GetPerformanceStatistics ******

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function GetSystemPerformanceCapabilities 
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventGetSystemPerformanceCapabilities_Parms
	{
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get system performance capabilities\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get system performance capabilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventGetSystemPerformanceCapabilities_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "GetSystemPerformanceCapabilities", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::AuracronMetaHumanPerformanceSystem_eventGetSystemPerformanceCapabilities_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::AuracronMetaHumanPerformanceSystem_eventGetSystemPerformanceCapabilities_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execGetSystemPerformanceCapabilities)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=P_THIS->GetSystemPerformanceCapabilities();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function GetSystemPerformanceCapabilities 

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function Initialize ******************
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventInitialize_Parms
	{
		UAuracronMetaHumanFramework* InFramework;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize performance system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize performance system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InFramework;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::NewProp_InFramework = { "InFramework", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventInitialize_Parms, InFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventInitialize_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventInitialize_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::NewProp_InFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "Initialize", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::AuracronMetaHumanPerformanceSystem_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::AuracronMetaHumanPerformanceSystem_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execInitialize)
{
	P_GET_OBJECT(UAuracronMetaHumanFramework,Z_Param_InFramework);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Initialize(Z_Param_InFramework);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function Initialize ********************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function IsInitialized ***************
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if performance system is initialized\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if performance system is initialized" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::AuracronMetaHumanPerformanceSystem_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::AuracronMetaHumanPerformanceSystem_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function IsInitialized *****************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function OptimizeAnimationPerformance 
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventOptimizeAnimationPerformance_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize animation performance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize animation performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventOptimizeAnimationPerformance_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventOptimizeAnimationPerformance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventOptimizeAnimationPerformance_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "OptimizeAnimationPerformance", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeAnimationPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeAnimationPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execOptimizeAnimationPerformance)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeAnimationPerformance(Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function OptimizeAnimationPerformance **

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function OptimizeGPUPerformance ******
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventOptimizeGPUPerformance_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize GPU performance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize GPU performance" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventOptimizeGPUPerformance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventOptimizeGPUPerformance_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "OptimizeGPUPerformance", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeGPUPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeGPUPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execOptimizeGPUPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeGPUPerformance();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function OptimizeGPUPerformance ********

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function OptimizeMemoryUsage *********
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventOptimizeMemoryUsage_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize memory usage\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize memory usage" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventOptimizeMemoryUsage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventOptimizeMemoryUsage_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "OptimizeMemoryUsage", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execOptimizeMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function OptimizeMemoryUsage ***********

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function OptimizeMetaHumanLODs *******
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventOptimizeMetaHumanLODs_Parms
	{
		AActor* MetaHumanActor;
		int32 TargetLODLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize MetaHuman LODs\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize MetaHuman LODs" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MetaHumanActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetLODLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_MetaHumanActor = { "MetaHumanActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventOptimizeMetaHumanLODs_Parms, MetaHumanActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_TargetLODLevel = { "TargetLODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventOptimizeMetaHumanLODs_Parms, TargetLODLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventOptimizeMetaHumanLODs_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventOptimizeMetaHumanLODs_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_MetaHumanActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_TargetLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "OptimizeMetaHumanLODs", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeMetaHumanLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeMetaHumanLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execOptimizeMetaHumanLODs)
{
	P_GET_OBJECT(AActor,Z_Param_MetaHumanActor);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetLODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeMetaHumanLODs(Z_Param_MetaHumanActor,Z_Param_TargetLODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function OptimizeMetaHumanLODs *********

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function OptimizeTextureStreaming ****
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventOptimizeTextureStreaming_Parms
	{
		TArray<UTexture*> Textures;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize texture streaming\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize texture streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Textures_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Textures_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Textures;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_Textures_Inner = { "Textures", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UTexture_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_Textures = { "Textures", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventOptimizeTextureStreaming_Parms, Textures), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Textures_MetaData), NewProp_Textures_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventOptimizeTextureStreaming_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventOptimizeTextureStreaming_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_Textures_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_Textures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "OptimizeTextureStreaming", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeTextureStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::AuracronMetaHumanPerformanceSystem_eventOptimizeTextureStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execOptimizeTextureStreaming)
{
	P_GET_TARRAY_REF(UTexture*,Z_Param_Out_Textures);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeTextureStreaming(Z_Param_Out_Textures);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function OptimizeTextureStreaming ******

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function ResetPerformanceStatistics **
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ResetPerformanceStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reset performance statistics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reset performance statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ResetPerformanceStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "ResetPerformanceStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ResetPerformanceStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ResetPerformanceStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ResetPerformanceStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ResetPerformanceStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execResetPerformanceStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetPerformanceStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function ResetPerformanceStatistics ****

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function SetGPUBudget ****************
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventSetGPUBudget_Parms
	{
		float BudgetMS;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set GPU budget\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set GPU budget" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BudgetMS;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::NewProp_BudgetMS = { "BudgetMS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventSetGPUBudget_Parms, BudgetMS), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventSetGPUBudget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventSetGPUBudget_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::NewProp_BudgetMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "SetGPUBudget", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::AuracronMetaHumanPerformanceSystem_eventSetGPUBudget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::AuracronMetaHumanPerformanceSystem_eventSetGPUBudget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execSetGPUBudget)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_BudgetMS);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetGPUBudget(Z_Param_BudgetMS);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function SetGPUBudget ******************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function SetMemoryBudget *************
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventSetMemoryBudget_Parms
	{
		int32 BudgetMB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set memory budget\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set memory budget" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_BudgetMB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::NewProp_BudgetMB = { "BudgetMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventSetMemoryBudget_Parms, BudgetMB), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventSetMemoryBudget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventSetMemoryBudget_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::NewProp_BudgetMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "SetMemoryBudget", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::AuracronMetaHumanPerformanceSystem_eventSetMemoryBudget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::AuracronMetaHumanPerformanceSystem_eventSetMemoryBudget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execSetMemoryBudget)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_BudgetMB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetMemoryBudget(Z_Param_BudgetMB);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function SetMemoryBudget ***************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function SetPerformanceAlertThresholds 
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventSetPerformanceAlertThresholds_Parms
	{
		float MinFrameRate;
		float MaxMemoryMB;
		float MaxCPUPercent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Alerts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set performance alert thresholds\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set performance alert thresholds" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxCPUPercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::NewProp_MinFrameRate = { "MinFrameRate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventSetPerformanceAlertThresholds_Parms, MinFrameRate), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::NewProp_MaxMemoryMB = { "MaxMemoryMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventSetPerformanceAlertThresholds_Parms, MaxMemoryMB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::NewProp_MaxCPUPercent = { "MaxCPUPercent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventSetPerformanceAlertThresholds_Parms, MaxCPUPercent), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::NewProp_MinFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::NewProp_MaxMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::NewProp_MaxCPUPercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "SetPerformanceAlertThresholds", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::AuracronMetaHumanPerformanceSystem_eventSetPerformanceAlertThresholds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::AuracronMetaHumanPerformanceSystem_eventSetPerformanceAlertThresholds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execSetPerformanceAlertThresholds)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinFrameRate);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxMemoryMB);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxCPUPercent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPerformanceAlertThresholds(Z_Param_MinFrameRate,Z_Param_MaxMemoryMB,Z_Param_MaxCPUPercent);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function SetPerformanceAlertThresholds *

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function Shutdown ********************
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown performance system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown performance system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function Shutdown **********************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function StartCPUProfiling ***********
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventStartCPUProfiling_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Profiling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Start CPU profiling\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start CPU profiling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventStartCPUProfiling_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventStartCPUProfiling_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "StartCPUProfiling", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStartCPUProfiling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStartCPUProfiling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execStartCPUProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartCPUProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function StartCPUProfiling *************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function StartMemoryProfiling ********
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventStartMemoryProfiling_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Profiling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Start memory profiling\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start memory profiling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventStartMemoryProfiling_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventStartMemoryProfiling_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "StartMemoryProfiling", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStartMemoryProfiling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStartMemoryProfiling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execStartMemoryProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartMemoryProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function StartMemoryProfiling **********

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function StartPerformanceMonitoring **
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventStartPerformanceMonitoring_Parms
	{
		float SampleIntervalSeconds;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Start performance monitoring\n     */" },
#endif
		{ "CPP_Default_SampleIntervalSeconds", "1.000000" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SampleIntervalSeconds;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::NewProp_SampleIntervalSeconds = { "SampleIntervalSeconds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventStartPerformanceMonitoring_Parms, SampleIntervalSeconds), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPerformanceSystem_eventStartPerformanceMonitoring_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPerformanceSystem_eventStartPerformanceMonitoring_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::NewProp_SampleIntervalSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "StartPerformanceMonitoring", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::AuracronMetaHumanPerformanceSystem_eventStartPerformanceMonitoring_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::AuracronMetaHumanPerformanceSystem_eventStartPerformanceMonitoring_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execStartPerformanceMonitoring)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SampleIntervalSeconds);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartPerformanceMonitoring(Z_Param_SampleIntervalSeconds);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function StartPerformanceMonitoring ****

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function StopCPUProfiling ************
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventStopCPUProfiling_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Profiling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Stop CPU profiling\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop CPU profiling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventStopCPUProfiling_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "StopCPUProfiling", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStopCPUProfiling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStopCPUProfiling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execStopCPUProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->StopCPUProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function StopCPUProfiling **************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function StopMemoryProfiling *********
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics
{
	struct AuracronMetaHumanPerformanceSystem_eventStopMemoryProfiling_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Profiling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Stop memory profiling\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop memory profiling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPerformanceSystem_eventStopMemoryProfiling_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "StopMemoryProfiling", Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStopMemoryProfiling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::AuracronMetaHumanPerformanceSystem_eventStopMemoryProfiling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execStopMemoryProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->StopMemoryProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function StopMemoryProfiling ***********

// ********** Begin Class UAuracronMetaHumanPerformanceSystem Function StopPerformanceMonitoring ***
struct Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopPerformanceMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Stop performance monitoring\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopPerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, nullptr, "StopPerformanceMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopPerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopPerformanceMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopPerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopPerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPerformanceSystem::execStopPerformanceMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopPerformanceMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPerformanceSystem Function StopPerformanceMonitoring *****

// ********** Begin Class UAuracronMetaHumanPerformanceSystem **************************************
void UAuracronMetaHumanPerformanceSystem::StaticRegisterNativesUAuracronMetaHumanPerformanceSystem()
{
	UClass* Class = UAuracronMetaHumanPerformanceSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyPerformanceOptimization", &UAuracronMetaHumanPerformanceSystem::execApplyPerformanceOptimization },
		{ "EnablePerformanceAlerts", &UAuracronMetaHumanPerformanceSystem::execEnablePerformanceAlerts },
		{ "ForceGarbageCollection", &UAuracronMetaHumanPerformanceSystem::execForceGarbageCollection },
		{ "GetCurrentPerformanceSample", &UAuracronMetaHumanPerformanceSystem::execGetCurrentPerformanceSample },
		{ "GetGPUPerformanceMetrics", &UAuracronMetaHumanPerformanceSystem::execGetGPUPerformanceMetrics },
		{ "GetMemoryUsageBreakdown", &UAuracronMetaHumanPerformanceSystem::execGetMemoryUsageBreakdown },
		{ "GetPerformanceStatistics", &UAuracronMetaHumanPerformanceSystem::execGetPerformanceStatistics },
		{ "GetSystemPerformanceCapabilities", &UAuracronMetaHumanPerformanceSystem::execGetSystemPerformanceCapabilities },
		{ "Initialize", &UAuracronMetaHumanPerformanceSystem::execInitialize },
		{ "IsInitialized", &UAuracronMetaHumanPerformanceSystem::execIsInitialized },
		{ "OptimizeAnimationPerformance", &UAuracronMetaHumanPerformanceSystem::execOptimizeAnimationPerformance },
		{ "OptimizeGPUPerformance", &UAuracronMetaHumanPerformanceSystem::execOptimizeGPUPerformance },
		{ "OptimizeMemoryUsage", &UAuracronMetaHumanPerformanceSystem::execOptimizeMemoryUsage },
		{ "OptimizeMetaHumanLODs", &UAuracronMetaHumanPerformanceSystem::execOptimizeMetaHumanLODs },
		{ "OptimizeTextureStreaming", &UAuracronMetaHumanPerformanceSystem::execOptimizeTextureStreaming },
		{ "ResetPerformanceStatistics", &UAuracronMetaHumanPerformanceSystem::execResetPerformanceStatistics },
		{ "SetGPUBudget", &UAuracronMetaHumanPerformanceSystem::execSetGPUBudget },
		{ "SetMemoryBudget", &UAuracronMetaHumanPerformanceSystem::execSetMemoryBudget },
		{ "SetPerformanceAlertThresholds", &UAuracronMetaHumanPerformanceSystem::execSetPerformanceAlertThresholds },
		{ "Shutdown", &UAuracronMetaHumanPerformanceSystem::execShutdown },
		{ "StartCPUProfiling", &UAuracronMetaHumanPerformanceSystem::execStartCPUProfiling },
		{ "StartMemoryProfiling", &UAuracronMetaHumanPerformanceSystem::execStartMemoryProfiling },
		{ "StartPerformanceMonitoring", &UAuracronMetaHumanPerformanceSystem::execStartPerformanceMonitoring },
		{ "StopCPUProfiling", &UAuracronMetaHumanPerformanceSystem::execStopCPUProfiling },
		{ "StopMemoryProfiling", &UAuracronMetaHumanPerformanceSystem::execStopMemoryProfiling },
		{ "StopPerformanceMonitoring", &UAuracronMetaHumanPerformanceSystem::execStopPerformanceMonitoring },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem;
UClass* UAuracronMetaHumanPerformanceSystem::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanPerformanceSystem;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanPerformanceSystem"),
			Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanPerformanceSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister()
{
	return UAuracronMetaHumanPerformanceSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|MetaHuman|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AURACRON MetaHuman Performance System\n * Advanced performance monitoring and optimization system for UE 5.6\n */" },
#endif
		{ "IncludePath", "Systems/AuracronMetaHumanPerformanceSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AURACRON MetaHuman Performance System\nAdvanced performance monitoring and optimization system for UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceAlert_MetaData[] = {
		{ "Category", "AURACRON MetaHuman|Performance|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when a performance alert is triggered */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when a performance alert is triggered" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnOptimizationComplete_MetaData[] = {
		{ "Category", "AURACRON MetaHuman|Performance|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when optimization is complete */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when optimization is complete" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerFramework_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Internal State ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Internal State ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMonitoring_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SampleInterval_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastErrorMessage_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPerformanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceAlert;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnOptimizationComplete;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerFramework;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bIsMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMonitoring;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SampleInterval;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ApplyPerformanceOptimization, "ApplyPerformanceOptimization" }, // 1231181570
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_EnablePerformanceAlerts, "EnablePerformanceAlerts" }, // 64357435
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ForceGarbageCollection, "ForceGarbageCollection" }, // 1561660486
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetCurrentPerformanceSample, "GetCurrentPerformanceSample" }, // 1823519237
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetGPUPerformanceMetrics, "GetGPUPerformanceMetrics" }, // 1558691366
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetMemoryUsageBreakdown, "GetMemoryUsageBreakdown" }, // 525930397
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetPerformanceStatistics, "GetPerformanceStatistics" }, // 1981238324
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_GetSystemPerformanceCapabilities, "GetSystemPerformanceCapabilities" }, // 3516300201
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Initialize, "Initialize" }, // 1156683892
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_IsInitialized, "IsInitialized" }, // 2991217188
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeAnimationPerformance, "OptimizeAnimationPerformance" }, // 1132340283
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeGPUPerformance, "OptimizeGPUPerformance" }, // 2052692491
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMemoryUsage, "OptimizeMemoryUsage" }, // 608217752
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeMetaHumanLODs, "OptimizeMetaHumanLODs" }, // 1568591688
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_OptimizeTextureStreaming, "OptimizeTextureStreaming" }, // 184600429
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_ResetPerformanceStatistics, "ResetPerformanceStatistics" }, // 1096932039
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetGPUBudget, "SetGPUBudget" }, // 739616222
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetMemoryBudget, "SetMemoryBudget" }, // 237680436
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_SetPerformanceAlertThresholds, "SetPerformanceAlertThresholds" }, // 3093995860
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_Shutdown, "Shutdown" }, // 2872014765
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartCPUProfiling, "StartCPUProfiling" }, // 2115701410
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartMemoryProfiling, "StartMemoryProfiling" }, // 4272872430
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StartPerformanceMonitoring, "StartPerformanceMonitoring" }, // 1434176455
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopCPUProfiling, "StopCPUProfiling" }, // 2085570219
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopMemoryProfiling, "StopMemoryProfiling" }, // 1856014972
		{ &Z_Construct_UFunction_UAuracronMetaHumanPerformanceSystem_StopPerformanceMonitoring, "StopPerformanceMonitoring" }, // 2883199227
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanPerformanceSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_OnPerformanceAlert = { "OnPerformanceAlert", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPerformanceSystem, OnPerformanceAlert), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPerformanceAlert__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceAlert_MetaData), NewProp_OnPerformanceAlert_MetaData) }; // 1751323783
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_OnOptimizationComplete = { "OnOptimizationComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPerformanceSystem, OnOptimizationComplete), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronOptimizationComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnOptimizationComplete_MetaData), NewProp_OnOptimizationComplete_MetaData) }; // 2794519660
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_OwnerFramework = { "OwnerFramework", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPerformanceSystem, OwnerFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerFramework_MetaData), NewProp_OwnerFramework_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanPerformanceSystem*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanPerformanceSystem), &Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsMonitoring_SetBit(void* Obj)
{
	((UAuracronMetaHumanPerformanceSystem*)Obj)->bIsMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsMonitoring = { "bIsMonitoring", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanPerformanceSystem), &Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMonitoring_MetaData), NewProp_bIsMonitoring_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_SampleInterval = { "SampleInterval", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPerformanceSystem, SampleInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SampleInterval_MetaData), NewProp_SampleInterval_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_LastErrorMessage = { "LastErrorMessage", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPerformanceSystem, LastErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastErrorMessage_MetaData), NewProp_LastErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_OnPerformanceAlert,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_OnOptimizationComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_OwnerFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_bIsMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_SampleInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::NewProp_LastErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::ClassParams = {
	&UAuracronMetaHumanPerformanceSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanPerformanceSystem);
// ********** End Class UAuracronMetaHumanPerformanceSystem ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPerformanceMetric_StaticEnum, TEXT("EAuracronPerformanceMetric"), &Z_Registration_Info_UEnum_EAuracronPerformanceMetric, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1462646676U) },
		{ EAuracronOptimizationLevel_StaticEnum, TEXT("EAuracronOptimizationLevel"), &Z_Registration_Info_UEnum_EAuracronOptimizationLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1451672887U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPerformanceSample::StaticStruct, Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics::NewStructOps, TEXT("AuracronPerformanceSample"), &Z_Registration_Info_UScriptStruct_FAuracronPerformanceSample, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPerformanceSample), 719725561U) },
		{ FAuracronPerformanceStats::StaticStruct, Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics::NewStructOps, TEXT("AuracronPerformanceStats"), &Z_Registration_Info_UScriptStruct_FAuracronPerformanceStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPerformanceStats), 3135539801U) },
		{ FAuracronOptimizationConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics::NewStructOps, TEXT("AuracronOptimizationConfig"), &Z_Registration_Info_UScriptStruct_FAuracronOptimizationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronOptimizationConfig), 4046093474U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem, UAuracronMetaHumanPerformanceSystem::StaticClass, TEXT("UAuracronMetaHumanPerformanceSystem"), &Z_Registration_Info_UClass_UAuracronMetaHumanPerformanceSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanPerformanceSystem), 3024517811U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_154791336(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
