// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageStreaming.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageStreaming() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageStreamingManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageChunkData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronDataLayerManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronFoliageStreamingStrategy *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy;
static UEnum* EAuracronFoliageStreamingStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageStreamingStrategy"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageStreamingStrategy>()
{
	return EAuracronFoliageStreamingStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CellBased.DisplayName", "Cell Based" },
		{ "CellBased.Name", "EAuracronFoliageStreamingStrategy::CellBased" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage streaming strategy\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronFoliageStreamingStrategy::Custom" },
		{ "DataLayerBased.DisplayName", "Data Layer Based" },
		{ "DataLayerBased.Name", "EAuracronFoliageStreamingStrategy::DataLayerBased" },
		{ "DistanceBased.DisplayName", "Distance Based" },
		{ "DistanceBased.Name", "EAuracronFoliageStreamingStrategy::DistanceBased" },
		{ "Hybrid.DisplayName", "Hybrid Strategy" },
		{ "Hybrid.Name", "EAuracronFoliageStreamingStrategy::Hybrid" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronFoliageStreamingStrategy::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage streaming strategy" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageStreamingStrategy::None", (int64)EAuracronFoliageStreamingStrategy::None },
		{ "EAuracronFoliageStreamingStrategy::DistanceBased", (int64)EAuracronFoliageStreamingStrategy::DistanceBased },
		{ "EAuracronFoliageStreamingStrategy::CellBased", (int64)EAuracronFoliageStreamingStrategy::CellBased },
		{ "EAuracronFoliageStreamingStrategy::DataLayerBased", (int64)EAuracronFoliageStreamingStrategy::DataLayerBased },
		{ "EAuracronFoliageStreamingStrategy::Hybrid", (int64)EAuracronFoliageStreamingStrategy::Hybrid },
		{ "EAuracronFoliageStreamingStrategy::Custom", (int64)EAuracronFoliageStreamingStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageStreamingStrategy",
	"EAuracronFoliageStreamingStrategy",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy.InnerSingleton;
}
// ********** End Enum EAuracronFoliageStreamingStrategy *******************************************

// ********** Begin Enum EAuracronFoliageMemoryStrategy ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy;
static UEnum* EAuracronFoliageMemoryStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageMemoryStrategy"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageMemoryStrategy>()
{
	return EAuracronFoliageMemoryStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronFoliageMemoryStrategy::Adaptive" },
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAuracronFoliageMemoryStrategy::Aggressive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EAuracronFoliageMemoryStrategy::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage memory management strategy\n" },
#endif
		{ "Conservative.DisplayName", "Conservative" },
		{ "Conservative.Name", "EAuracronFoliageMemoryStrategy::Conservative" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronFoliageMemoryStrategy::Custom" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage memory management strategy" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageMemoryStrategy::Conservative", (int64)EAuracronFoliageMemoryStrategy::Conservative },
		{ "EAuracronFoliageMemoryStrategy::Balanced", (int64)EAuracronFoliageMemoryStrategy::Balanced },
		{ "EAuracronFoliageMemoryStrategy::Aggressive", (int64)EAuracronFoliageMemoryStrategy::Aggressive },
		{ "EAuracronFoliageMemoryStrategy::Adaptive", (int64)EAuracronFoliageMemoryStrategy::Adaptive },
		{ "EAuracronFoliageMemoryStrategy::Custom", (int64)EAuracronFoliageMemoryStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageMemoryStrategy",
	"EAuracronFoliageMemoryStrategy",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy.InnerSingleton;
}
// ********** End Enum EAuracronFoliageMemoryStrategy **********************************************

// ********** Begin Enum EAuracronFoliageLoadingPriority *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority;
static UEnum* EAuracronFoliageLoadingPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageLoadingPriority"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageLoadingPriority>()
{
	return EAuracronFoliageLoadingPriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage loading priority\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronFoliageLoadingPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronFoliageLoadingPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronFoliageLoadingPriority::Low" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronFoliageLoadingPriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage loading priority" },
#endif
		{ "VeryHigh.DisplayName", "Very High" },
		{ "VeryHigh.Name", "EAuracronFoliageLoadingPriority::VeryHigh" },
		{ "VeryLow.DisplayName", "Very Low" },
		{ "VeryLow.Name", "EAuracronFoliageLoadingPriority::VeryLow" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageLoadingPriority::VeryLow", (int64)EAuracronFoliageLoadingPriority::VeryLow },
		{ "EAuracronFoliageLoadingPriority::Low", (int64)EAuracronFoliageLoadingPriority::Low },
		{ "EAuracronFoliageLoadingPriority::Normal", (int64)EAuracronFoliageLoadingPriority::Normal },
		{ "EAuracronFoliageLoadingPriority::High", (int64)EAuracronFoliageLoadingPriority::High },
		{ "EAuracronFoliageLoadingPriority::VeryHigh", (int64)EAuracronFoliageLoadingPriority::VeryHigh },
		{ "EAuracronFoliageLoadingPriority::Critical", (int64)EAuracronFoliageLoadingPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageLoadingPriority",
	"EAuracronFoliageLoadingPriority",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority.InnerSingleton;
}
// ********** End Enum EAuracronFoliageLoadingPriority *********************************************

// ********** Begin Enum EAuracronFoliageStreamingState ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageStreamingState;
static UEnum* EAuracronFoliageStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageStreamingState.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageStreamingState>()
{
	return EAuracronFoliageStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cached.DisplayName", "Cached" },
		{ "Cached.Name", "EAuracronFoliageStreamingState::Cached" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage streaming state\n" },
#endif
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronFoliageStreamingState::Error" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronFoliageStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronFoliageStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage streaming state" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronFoliageStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronFoliageStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageStreamingState::Unloaded", (int64)EAuracronFoliageStreamingState::Unloaded },
		{ "EAuracronFoliageStreamingState::Loading", (int64)EAuracronFoliageStreamingState::Loading },
		{ "EAuracronFoliageStreamingState::Loaded", (int64)EAuracronFoliageStreamingState::Loaded },
		{ "EAuracronFoliageStreamingState::Unloading", (int64)EAuracronFoliageStreamingState::Unloading },
		{ "EAuracronFoliageStreamingState::Error", (int64)EAuracronFoliageStreamingState::Error },
		{ "EAuracronFoliageStreamingState::Cached", (int64)EAuracronFoliageStreamingState::Cached },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageStreamingState",
	"EAuracronFoliageStreamingState",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronFoliageStreamingState **********************************************

// ********** Begin ScriptStruct FAuracronFoliageStreamingConfiguration ****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration;
class UScriptStruct* FAuracronFoliageStreamingConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageStreamingConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Streaming Configuration\n * Configuration for foliage streaming system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Streaming Configuration\nConfiguration for foliage streaming system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFoliageStreaming_MetaData[] = {
		{ "Category", "Streaming System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingStrategy_MetaData[] = {
		{ "Category", "Streaming System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageStreamingDistance_MetaData[] = {
		{ "Category", "Distance Streaming" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageUnloadingDistance_MetaData[] = {
		{ "Category", "Distance Streaming" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliagePreloadDistance_MetaData[] = {
		{ "Category", "Distance Streaming" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCellBasedStreaming_MetaData[] = {
		{ "Category", "Cell Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithWorldPartition_MetaData[] = {
		{ "Category", "Cell Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellStreamingBuffer_MetaData[] = {
		{ "Category", "Cell Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDataLayerIntegration_MetaData[] = {
		{ "Category", "Data Layer Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRespectDataLayerStates_MetaData[] = {
		{ "Category", "Data Layer Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryStrategy_MetaData[] = {
		{ "Category", "Memory Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFoliageMemoryMB_MetaData[] = {
		{ "Category", "Memory Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPressureThreshold_MetaData[] = {
		{ "Category", "Memory Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstancePooling_MetaData[] = {
		{ "Category", "Memory Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancePoolSize_MetaData[] = {
		{ "Category", "Memory Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncFoliageLoading_MetaData[] = {
		{ "Category", "Async Loading" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentFoliageLoads_MetaData[] = {
		{ "Category", "Async Loading" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageLoadingTimeSliceMs_MetaData[] = {
		{ "Category", "Async Loading" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AsyncWorkerThreads_MetaData[] = {
		{ "Category", "Async Loading" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFoliageInstancesPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFoliageLODStreaming_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFrustumCulling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOcclusionCulling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFoliageCaching_MetaData[] = {
		{ "Category", "Caching" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheRetentionTime_MetaData[] = {
		{ "Category", "Caching" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCachedFoliageChunks_MetaData[] = {
		{ "Category", "Caching" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreamingDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogFoliageStreaming_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDrawStreamingBounds_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableFoliageStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFoliageStreaming;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingStrategy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FoliageStreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FoliageUnloadingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FoliagePreloadDistance;
	static void NewProp_bEnableCellBasedStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCellBasedStreaming;
	static void NewProp_bIntegrateWithWorldPartition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithWorldPartition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CellStreamingBuffer;
	static void NewProp_bEnableDataLayerIntegration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDataLayerIntegration;
	static void NewProp_bRespectDataLayerStates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRespectDataLayerStates;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MemoryStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MemoryStrategy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFoliageMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryPressureThreshold;
	static void NewProp_bEnableInstancePooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstancePooling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstancePoolSize;
	static void NewProp_bEnableAsyncFoliageLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncFoliageLoading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentFoliageLoads;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FoliageLoadingTimeSliceMs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AsyncWorkerThreads;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxFoliageInstancesPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingUpdateInterval;
	static void NewProp_bEnableFoliageLODStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFoliageLODStreaming;
	static void NewProp_bEnableFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFrustumCulling;
	static void NewProp_bEnableOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusionCulling;
	static void NewProp_bEnableFoliageCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFoliageCaching;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CacheRetentionTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCachedFoliageChunks;
	static void NewProp_bEnableStreamingDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreamingDebug;
	static void NewProp_bLogFoliageStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogFoliageStreaming;
	static void NewProp_bDrawStreamingBounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDrawStreamingBounds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageStreamingConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageStreaming_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableFoliageStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageStreaming = { "bEnableFoliageStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFoliageStreaming_MetaData), NewProp_bEnableFoliageStreaming_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_StreamingStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_StreamingStrategy = { "StreamingStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, StreamingStrategy), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingStrategy_MetaData), NewProp_StreamingStrategy_MetaData) }; // 202874679
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliageStreamingDistance = { "FoliageStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, FoliageStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageStreamingDistance_MetaData), NewProp_FoliageStreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliageUnloadingDistance = { "FoliageUnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, FoliageUnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageUnloadingDistance_MetaData), NewProp_FoliageUnloadingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliagePreloadDistance = { "FoliagePreloadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, FoliagePreloadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliagePreloadDistance_MetaData), NewProp_FoliagePreloadDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableCellBasedStreaming_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableCellBasedStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableCellBasedStreaming = { "bEnableCellBasedStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableCellBasedStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCellBasedStreaming_MetaData), NewProp_bEnableCellBasedStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bIntegrateWithWorldPartition_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bIntegrateWithWorldPartition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bIntegrateWithWorldPartition = { "bIntegrateWithWorldPartition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bIntegrateWithWorldPartition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithWorldPartition_MetaData), NewProp_bIntegrateWithWorldPartition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_CellStreamingBuffer = { "CellStreamingBuffer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, CellStreamingBuffer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellStreamingBuffer_MetaData), NewProp_CellStreamingBuffer_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableDataLayerIntegration_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableDataLayerIntegration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableDataLayerIntegration = { "bEnableDataLayerIntegration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableDataLayerIntegration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDataLayerIntegration_MetaData), NewProp_bEnableDataLayerIntegration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bRespectDataLayerStates_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bRespectDataLayerStates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bRespectDataLayerStates = { "bRespectDataLayerStates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bRespectDataLayerStates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRespectDataLayerStates_MetaData), NewProp_bRespectDataLayerStates_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MemoryStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MemoryStrategy = { "MemoryStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, MemoryStrategy), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageMemoryStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryStrategy_MetaData), NewProp_MemoryStrategy_MetaData) }; // 2314153493
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxFoliageMemoryMB = { "MaxFoliageMemoryMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, MaxFoliageMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFoliageMemoryMB_MetaData), NewProp_MaxFoliageMemoryMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MemoryPressureThreshold = { "MemoryPressureThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, MemoryPressureThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPressureThreshold_MetaData), NewProp_MemoryPressureThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableInstancePooling_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableInstancePooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableInstancePooling = { "bEnableInstancePooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableInstancePooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstancePooling_MetaData), NewProp_bEnableInstancePooling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_InstancePoolSize = { "InstancePoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, InstancePoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancePoolSize_MetaData), NewProp_InstancePoolSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableAsyncFoliageLoading_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableAsyncFoliageLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableAsyncFoliageLoading = { "bEnableAsyncFoliageLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableAsyncFoliageLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncFoliageLoading_MetaData), NewProp_bEnableAsyncFoliageLoading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxConcurrentFoliageLoads = { "MaxConcurrentFoliageLoads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, MaxConcurrentFoliageLoads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentFoliageLoads_MetaData), NewProp_MaxConcurrentFoliageLoads_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliageLoadingTimeSliceMs = { "FoliageLoadingTimeSliceMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, FoliageLoadingTimeSliceMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageLoadingTimeSliceMs_MetaData), NewProp_FoliageLoadingTimeSliceMs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_AsyncWorkerThreads = { "AsyncWorkerThreads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, AsyncWorkerThreads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AsyncWorkerThreads_MetaData), NewProp_AsyncWorkerThreads_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxFoliageInstancesPerFrame = { "MaxFoliageInstancesPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, MaxFoliageInstancesPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFoliageInstancesPerFrame_MetaData), NewProp_MaxFoliageInstancesPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_StreamingUpdateInterval = { "StreamingUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, StreamingUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingUpdateInterval_MetaData), NewProp_StreamingUpdateInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageLODStreaming_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableFoliageLODStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageLODStreaming = { "bEnableFoliageLODStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageLODStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFoliageLODStreaming_MetaData), NewProp_bEnableFoliageLODStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFrustumCulling_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFrustumCulling = { "bEnableFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFrustumCulling_MetaData), NewProp_bEnableFrustumCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableOcclusionCulling_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableOcclusionCulling = { "bEnableOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOcclusionCulling_MetaData), NewProp_bEnableOcclusionCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageCaching_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableFoliageCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageCaching = { "bEnableFoliageCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFoliageCaching_MetaData), NewProp_bEnableFoliageCaching_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_CacheRetentionTime = { "CacheRetentionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, CacheRetentionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheRetentionTime_MetaData), NewProp_CacheRetentionTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxCachedFoliageChunks = { "MaxCachedFoliageChunks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingConfiguration, MaxCachedFoliageChunks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCachedFoliageChunks_MetaData), NewProp_MaxCachedFoliageChunks_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bEnableStreamingDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug = { "bEnableStreamingDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreamingDebug_MetaData), NewProp_bEnableStreamingDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bLogFoliageStreaming_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bLogFoliageStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bLogFoliageStreaming = { "bLogFoliageStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bLogFoliageStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogFoliageStreaming_MetaData), NewProp_bLogFoliageStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bDrawStreamingBounds_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingConfiguration*)Obj)->bDrawStreamingBounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bDrawStreamingBounds = { "bDrawStreamingBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bDrawStreamingBounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDrawStreamingBounds_MetaData), NewProp_bDrawStreamingBounds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_StreamingStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_StreamingStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliageStreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliageUnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliagePreloadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableCellBasedStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bIntegrateWithWorldPartition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_CellStreamingBuffer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableDataLayerIntegration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bRespectDataLayerStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MemoryStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MemoryStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxFoliageMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MemoryPressureThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableInstancePooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_InstancePoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableAsyncFoliageLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxConcurrentFoliageLoads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_FoliageLoadingTimeSliceMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_AsyncWorkerThreads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxFoliageInstancesPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_StreamingUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageLODStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableFoliageCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_CacheRetentionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_MaxCachedFoliageChunks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bLogFoliageStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewProp_bDrawStreamingBounds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageStreamingConfiguration",
	Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::PropPointers),
	sizeof(FAuracronFoliageStreamingConfiguration),
	alignof(FAuracronFoliageStreamingConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageStreamingConfiguration ******************************

// ********** Begin ScriptStruct FAuracronFoliageChunkData *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData;
class UScriptStruct* FAuracronFoliageChunkData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageChunkData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageChunkData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Chunk Data\n * Represents a chunk of foliage instances for streaming\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Chunk Data\nRepresents a chunk of foliage instances for streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkId_MetaData[] = {
		{ "Category", "Chunk Data" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Chunk Data" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerId_MetaData[] = {
		{ "Category", "Chunk Data" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkBounds_MetaData[] = {
		{ "Category", "Chunk Data" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeIds_MetaData[] = {
		{ "Category", "Chunk Data" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalInstanceCount_MetaData[] = {
		{ "Category", "Chunk Data" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedMemoryMB_MetaData[] = {
		{ "Category", "Chunk Data" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingPriority_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingProgress_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAsyncLoading_MetaData[] = {
		{ "Category", "Async" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AsyncRequestId_MetaData[] = {
		{ "Category", "Async" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayerId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChunkBounds;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FoliageTypeIds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalInstanceCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedMemoryMB;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LoadingPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LoadingPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingProgress;
	static void NewProp_bIsAsyncLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAsyncLoading;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AsyncRequestId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageChunkData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, ChunkId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkId_MetaData), NewProp_ChunkId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_DataLayerId = { "DataLayerId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, DataLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerId_MetaData), NewProp_DataLayerId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_ChunkBounds = { "ChunkBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, ChunkBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkBounds_MetaData), NewProp_ChunkBounds_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_FoliageTypeIds_Inner = { "FoliageTypeIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_FoliageTypeIds = { "FoliageTypeIds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, FoliageTypeIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeIds_MetaData), NewProp_FoliageTypeIds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_TotalInstanceCount = { "TotalInstanceCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, TotalInstanceCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalInstanceCount_MetaData), NewProp_TotalInstanceCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_EstimatedMemoryMB = { "EstimatedMemoryMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, EstimatedMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedMemoryMB_MetaData), NewProp_EstimatedMemoryMB_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, StreamingState), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 2929610450
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LoadingPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LoadingPriority = { "LoadingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, LoadingPriority), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingPriority_MetaData), NewProp_LoadingPriority_MetaData) }; // 2394233910
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, LastAccessTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LoadingProgress = { "LoadingProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, LoadingProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingProgress_MetaData), NewProp_LoadingProgress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_bIsAsyncLoading_SetBit(void* Obj)
{
	((FAuracronFoliageChunkData*)Obj)->bIsAsyncLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_bIsAsyncLoading = { "bIsAsyncLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageChunkData), &Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_bIsAsyncLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAsyncLoading_MetaData), NewProp_bIsAsyncLoading_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_AsyncRequestId = { "AsyncRequestId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageChunkData, AsyncRequestId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AsyncRequestId_MetaData), NewProp_AsyncRequestId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_DataLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_ChunkBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_FoliageTypeIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_FoliageTypeIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_TotalInstanceCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_EstimatedMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LoadingPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LoadingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LastAccessTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_LoadingProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_bIsAsyncLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewProp_AsyncRequestId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageChunkData",
	Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::PropPointers),
	sizeof(FAuracronFoliageChunkData),
	alignof(FAuracronFoliageChunkData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageChunkData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageChunkData *******************************************

// ********** Begin ScriptStruct FAuracronFoliageStreamingRequest **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest;
class UScriptStruct* FAuracronFoliageStreamingRequest::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageStreamingRequest"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Streaming Request\n * Request for loading/unloading foliage chunks\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Streaming Request\nRequest for loading/unloading foliage chunks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestId_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkId_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLoadRequest_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestLocation_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestDistance_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsProcessing_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingStartTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static void NewProp_bIsLoadRequest_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoadRequest;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequestLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RequestDistance;
	static void NewProp_bIsProcessing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsProcessing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RequestTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingStartTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageStreamingRequest>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestId = { "RequestId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingRequest, RequestId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestId_MetaData), NewProp_RequestId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingRequest, ChunkId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkId_MetaData), NewProp_ChunkId_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsLoadRequest_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingRequest*)Obj)->bIsLoadRequest = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsLoadRequest = { "bIsLoadRequest", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingRequest), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsLoadRequest_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLoadRequest_MetaData), NewProp_bIsLoadRequest_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingRequest, Priority), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 2394233910
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestLocation = { "RequestLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingRequest, RequestLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestLocation_MetaData), NewProp_RequestLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestDistance = { "RequestDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingRequest, RequestDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestDistance_MetaData), NewProp_RequestDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsProcessing_SetBit(void* Obj)
{
	((FAuracronFoliageStreamingRequest*)Obj)->bIsProcessing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsProcessing = { "bIsProcessing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageStreamingRequest), &Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsProcessing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsProcessing_MetaData), NewProp_bIsProcessing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestTime = { "RequestTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingRequest, RequestTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestTime_MetaData), NewProp_RequestTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_ProcessingStartTime = { "ProcessingStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingRequest, ProcessingStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingStartTime_MetaData), NewProp_ProcessingStartTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsLoadRequest,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_bIsProcessing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_RequestTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewProp_ProcessingStartTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageStreamingRequest",
	Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::PropPointers),
	sizeof(FAuracronFoliageStreamingRequest),
	alignof(FAuracronFoliageStreamingRequest),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageStreamingRequest ************************************

// ********** Begin ScriptStruct FAuracronFoliageMemoryPool ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool;
class UScriptStruct* FAuracronFoliageMemoryPool::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageMemoryPool"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Memory Pool\n * Memory pool for foliage instance data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Memory Pool\nMemory pool for foliage instance data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolId_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolSize_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UsedInstances_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableInstances_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryMB_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastCleanupTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PoolId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UsedInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AvailableInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMemoryMB;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastCleanupTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageMemoryPool>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_PoolId = { "PoolId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMemoryPool, PoolId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolId_MetaData), NewProp_PoolId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_PoolSize = { "PoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMemoryPool, PoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolSize_MetaData), NewProp_PoolSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_UsedInstances = { "UsedInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMemoryPool, UsedInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UsedInstances_MetaData), NewProp_UsedInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_AvailableInstances = { "AvailableInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMemoryPool, AvailableInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableInstances_MetaData), NewProp_AvailableInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMemoryPool, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_MaxMemoryMB = { "MaxMemoryMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMemoryPool, MaxMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryMB_MetaData), NewProp_MaxMemoryMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronFoliageMemoryPool*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMemoryPool), &Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_LastCleanupTime = { "LastCleanupTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMemoryPool, LastCleanupTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastCleanupTime_MetaData), NewProp_LastCleanupTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_PoolId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_PoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_UsedInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_AvailableInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_MaxMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewProp_LastCleanupTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageMemoryPool",
	Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::PropPointers),
	sizeof(FAuracronFoliageMemoryPool),
	alignof(FAuracronFoliageMemoryPool),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageMemoryPool ******************************************

// ********** Begin ScriptStruct FAuracronFoliageStreamingPerformanceData **************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData;
class UScriptStruct* FAuracronFoliageStreamingPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageStreamingPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Streaming Performance Data\n * Performance metrics for foliage streaming\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Streaming Performance Data\nPerformance metrics for foliage streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalFoliageChunks_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedChunks_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingChunks_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedChunks_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalFoliageInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamedInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageMemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AsyncLoadingTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PendingRequests_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedRequests_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedRequests_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalFoliageChunks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedChunks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadingChunks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CachedChunks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalFoliageInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamedInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FoliageMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AsyncLoadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PendingRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletedRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedRequests;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageStreamingPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_TotalFoliageChunks = { "TotalFoliageChunks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, TotalFoliageChunks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalFoliageChunks_MetaData), NewProp_TotalFoliageChunks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_LoadedChunks = { "LoadedChunks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, LoadedChunks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedChunks_MetaData), NewProp_LoadedChunks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_LoadingChunks = { "LoadingChunks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, LoadingChunks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingChunks_MetaData), NewProp_LoadingChunks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_CachedChunks = { "CachedChunks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, CachedChunks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedChunks_MetaData), NewProp_CachedChunks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_TotalFoliageInstances = { "TotalFoliageInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, TotalFoliageInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalFoliageInstances_MetaData), NewProp_TotalFoliageInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_StreamedInstances = { "StreamedInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, StreamedInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamedInstances_MetaData), NewProp_StreamedInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_FoliageMemoryUsageMB = { "FoliageMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, FoliageMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageMemoryUsageMB_MetaData), NewProp_FoliageMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_StreamingUpdateTime = { "StreamingUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, StreamingUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingUpdateTime_MetaData), NewProp_StreamingUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_AsyncLoadingTime = { "AsyncLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, AsyncLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AsyncLoadingTime_MetaData), NewProp_AsyncLoadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_PendingRequests = { "PendingRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, PendingRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PendingRequests_MetaData), NewProp_PendingRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_CompletedRequests = { "CompletedRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, CompletedRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedRequests_MetaData), NewProp_CompletedRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_FailedRequests = { "FailedRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageStreamingPerformanceData, FailedRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedRequests_MetaData), NewProp_FailedRequests_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_TotalFoliageChunks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_LoadedChunks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_LoadingChunks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_CachedChunks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_TotalFoliageInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_StreamedInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_FoliageMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_StreamingUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_AsyncLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_PendingRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_CompletedRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewProp_FailedRequests,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageStreamingPerformanceData",
	Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::PropPointers),
	sizeof(FAuracronFoliageStreamingPerformanceData),
	alignof(FAuracronFoliageStreamingPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageStreamingPerformanceData ****************************

// ********** Begin Delegate FOnFoliageChunkLoaded *************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics
{
	struct AuracronFoliageStreamingManager_eventOnFoliageChunkLoaded_Parms
	{
		FString ChunkId;
		int32 InstanceCount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventOnFoliageChunkLoaded_Parms, ChunkId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::NewProp_InstanceCount = { "InstanceCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventOnFoliageChunkLoaded_Parms, InstanceCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::NewProp_InstanceCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "OnFoliageChunkLoaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageChunkLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageChunkLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageStreamingManager::FOnFoliageChunkLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageChunkLoaded, const FString& ChunkId, int32 InstanceCount)
{
	struct AuracronFoliageStreamingManager_eventOnFoliageChunkLoaded_Parms
	{
		FString ChunkId;
		int32 InstanceCount;
	};
	AuracronFoliageStreamingManager_eventOnFoliageChunkLoaded_Parms Parms;
	Parms.ChunkId=ChunkId;
	Parms.InstanceCount=InstanceCount;
	OnFoliageChunkLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageChunkLoaded ***************************************************

// ********** Begin Delegate FOnFoliageChunkUnloaded ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics
{
	struct AuracronFoliageStreamingManager_eventOnFoliageChunkUnloaded_Parms
	{
		FString ChunkId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventOnFoliageChunkUnloaded_Parms, ChunkId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::NewProp_ChunkId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "OnFoliageChunkUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageChunkUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageChunkUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageStreamingManager::FOnFoliageChunkUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageChunkUnloaded, const FString& ChunkId)
{
	struct AuracronFoliageStreamingManager_eventOnFoliageChunkUnloaded_Parms
	{
		FString ChunkId;
	};
	AuracronFoliageStreamingManager_eventOnFoliageChunkUnloaded_Parms Parms;
	Parms.ChunkId=ChunkId;
	OnFoliageChunkUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageChunkUnloaded *************************************************

// ********** Begin Delegate FOnFoliageStreamingStateChanged ***************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics
{
	struct AuracronFoliageStreamingManager_eventOnFoliageStreamingStateChanged_Parms
	{
		FString ChunkId;
		EAuracronFoliageStreamingState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventOnFoliageStreamingStateChanged_Parms, ChunkId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventOnFoliageStreamingStateChanged_Parms, NewState), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageStreamingState, METADATA_PARAMS(0, nullptr) }; // 2929610450
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "OnFoliageStreamingStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageStreamingStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageStreamingStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageStreamingManager::FOnFoliageStreamingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageStreamingStateChanged, const FString& ChunkId, EAuracronFoliageStreamingState NewState)
{
	struct AuracronFoliageStreamingManager_eventOnFoliageStreamingStateChanged_Parms
	{
		FString ChunkId;
		EAuracronFoliageStreamingState NewState;
	};
	AuracronFoliageStreamingManager_eventOnFoliageStreamingStateChanged_Parms Parms;
	Parms.ChunkId=ChunkId;
	Parms.NewState=NewState;
	OnFoliageStreamingStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageStreamingStateChanged *****************************************

// ********** Begin Delegate FOnFoliageMemoryPressure **********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics
{
	struct AuracronFoliageStreamingManager_eventOnFoliageMemoryPressure_Parms
	{
		float MemoryUsagePercent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsagePercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::NewProp_MemoryUsagePercent = { "MemoryUsagePercent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventOnFoliageMemoryPressure_Parms, MemoryUsagePercent), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::NewProp_MemoryUsagePercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "OnFoliageMemoryPressure__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageMemoryPressure_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::AuracronFoliageStreamingManager_eventOnFoliageMemoryPressure_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageStreamingManager::FOnFoliageMemoryPressure_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageMemoryPressure, float MemoryUsagePercent)
{
	struct AuracronFoliageStreamingManager_eventOnFoliageMemoryPressure_Parms
	{
		float MemoryUsagePercent;
	};
	AuracronFoliageStreamingManager_eventOnFoliageMemoryPressure_Parms Parms;
	Parms.MemoryUsagePercent=MemoryUsagePercent;
	OnFoliageMemoryPressure.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageMemoryPressure ************************************************

// ********** Begin Class UAuracronFoliageStreamingManager Function CancelFoliageStreamingRequest **
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics
{
	struct AuracronFoliageStreamingManager_eventCancelFoliageStreamingRequest_Parms
	{
		FString RequestId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::NewProp_RequestId = { "RequestId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCancelFoliageStreamingRequest_Parms, RequestId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestId_MetaData), NewProp_RequestId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventCancelFoliageStreamingRequest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventCancelFoliageStreamingRequest_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::NewProp_RequestId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "CancelFoliageStreamingRequest", Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::AuracronFoliageStreamingManager_eventCancelFoliageStreamingRequest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::AuracronFoliageStreamingManager_eventCancelFoliageStreamingRequest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execCancelFoliageStreamingRequest)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RequestId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelFoliageStreamingRequest(Z_Param_RequestId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function CancelFoliageStreamingRequest ****

// ********** Begin Class UAuracronFoliageStreamingManager Function CleanupUnusedMemory ************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_CleanupUnusedMemory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CleanupUnusedMemory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "CleanupUnusedMemory", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CleanupUnusedMemory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_CleanupUnusedMemory_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_CleanupUnusedMemory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_CleanupUnusedMemory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execCleanupUnusedMemory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupUnusedMemory();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function CleanupUnusedMemory **************

// ********** Begin Class UAuracronFoliageStreamingManager Function CreateFoliageChunk *************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics
{
	struct AuracronFoliageStreamingManager_eventCreateFoliageChunk_Parms
	{
		FBox ChunkBounds;
		FString CellId;
		FString DataLayerId;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Chunk management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chunk management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkBounds_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChunkBounds;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_ChunkBounds = { "ChunkBounds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateFoliageChunk_Parms, ChunkBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkBounds_MetaData), NewProp_ChunkBounds_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateFoliageChunk_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_DataLayerId = { "DataLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateFoliageChunk_Parms, DataLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerId_MetaData), NewProp_DataLayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateFoliageChunk_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_ChunkBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_DataLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "CreateFoliageChunk", Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::AuracronFoliageStreamingManager_eventCreateFoliageChunk_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::AuracronFoliageStreamingManager_eventCreateFoliageChunk_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execCreateFoliageChunk)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_ChunkBounds);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_PROPERTY(FStrProperty,Z_Param_DataLayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateFoliageChunk(Z_Param_Out_ChunkBounds,Z_Param_CellId,Z_Param_DataLayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function CreateFoliageChunk ***************

// ********** Begin Class UAuracronFoliageStreamingManager Function CreateMemoryPool ***************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics
{
	struct AuracronFoliageStreamingManager_eventCreateMemoryPool_Parms
	{
		FString PoolId;
		int32 PoolSize;
		float MaxMemoryMB;
		FAuracronFoliageMemoryPool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PoolId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMemoryMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_PoolId = { "PoolId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateMemoryPool_Parms, PoolId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolId_MetaData), NewProp_PoolId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_PoolSize = { "PoolSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateMemoryPool_Parms, PoolSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_MaxMemoryMB = { "MaxMemoryMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateMemoryPool_Parms, MaxMemoryMB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventCreateMemoryPool_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool, METADATA_PARAMS(0, nullptr) }; // 2257613911
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_PoolId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_PoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_MaxMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "CreateMemoryPool", Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::AuracronFoliageStreamingManager_eventCreateMemoryPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::AuracronFoliageStreamingManager_eventCreateMemoryPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execCreateMemoryPool)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PoolId);
	P_GET_PROPERTY(FIntProperty,Z_Param_PoolSize);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxMemoryMB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageMemoryPool*)Z_Param__Result=P_THIS->CreateMemoryPool(Z_Param_PoolId,Z_Param_PoolSize,Z_Param_MaxMemoryMB);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function CreateMemoryPool *****************

// ********** Begin Class UAuracronFoliageStreamingManager Function DrawDebugStreamingInfo *********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics
{
	struct AuracronFoliageStreamingManager_eventDrawDebugStreamingInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventDrawDebugStreamingInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "DrawDebugStreamingInfo", Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::AuracronFoliageStreamingManager_eventDrawDebugStreamingInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::AuracronFoliageStreamingManager_eventDrawDebugStreamingInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execDrawDebugStreamingInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugStreamingInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function DrawDebugStreamingInfo ***********

// ********** Begin Class UAuracronFoliageStreamingManager Function EnableAsyncFoliageLoading ******
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics
{
	struct AuracronFoliageStreamingManager_eventEnableAsyncFoliageLoading_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Async loading\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async loading" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventEnableAsyncFoliageLoading_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventEnableAsyncFoliageLoading_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "EnableAsyncFoliageLoading", Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::AuracronFoliageStreamingManager_eventEnableAsyncFoliageLoading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::AuracronFoliageStreamingManager_eventEnableAsyncFoliageLoading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execEnableAsyncFoliageLoading)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableAsyncFoliageLoading(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function EnableAsyncFoliageLoading ********

// ********** Begin Class UAuracronFoliageStreamingManager Function EnableDebugVisualization *******
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageStreamingManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::AuracronFoliageStreamingManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::AuracronFoliageStreamingManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function EnableDebugVisualization *********

// ********** Begin Class UAuracronFoliageStreamingManager Function GetActiveAsyncLoadingCount *****
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics
{
	struct AuracronFoliageStreamingManager_eventGetActiveAsyncLoadingCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetActiveAsyncLoadingCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetActiveAsyncLoadingCount", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::AuracronFoliageStreamingManager_eventGetActiveAsyncLoadingCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::AuracronFoliageStreamingManager_eventGetActiveAsyncLoadingCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetActiveAsyncLoadingCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveAsyncLoadingCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetActiveAsyncLoadingCount *******

// ********** Begin Class UAuracronFoliageStreamingManager Function GetAllFoliageChunks ************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics
{
	struct AuracronFoliageStreamingManager_eventGetAllFoliageChunks_Parms
	{
		TArray<FAuracronFoliageChunkData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageChunkData, METADATA_PARAMS(0, nullptr) }; // 901826293
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetAllFoliageChunks_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 901826293
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetAllFoliageChunks", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::AuracronFoliageStreamingManager_eventGetAllFoliageChunks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::AuracronFoliageStreamingManager_eventGetAllFoliageChunks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetAllFoliageChunks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronFoliageChunkData>*)Z_Param__Result=P_THIS->GetAllFoliageChunks();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetAllFoliageChunks **************

// ********** Begin Class UAuracronFoliageStreamingManager Function GetChunksInRange ***************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics
{
	struct AuracronFoliageStreamingManager_eventGetChunksInRange_Parms
	{
		FVector Location;
		float Radius;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetChunksInRange_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetChunksInRange_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetChunksInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetChunksInRange", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::AuracronFoliageStreamingManager_eventGetChunksInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::AuracronFoliageStreamingManager_eventGetChunksInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetChunksInRange)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetChunksInRange(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetChunksInRange *****************

// ********** Begin Class UAuracronFoliageStreamingManager Function GetConfiguration ***************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics
{
	struct AuracronFoliageStreamingManager_eventGetConfiguration_Parms
	{
		FAuracronFoliageStreamingConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration, METADATA_PARAMS(0, nullptr) }; // 1670971137
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::AuracronFoliageStreamingManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::AuracronFoliageStreamingManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageStreamingConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetConfiguration *****************

// ********** Begin Class UAuracronFoliageStreamingManager Function GetFoliageChunk ****************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics
{
	struct AuracronFoliageStreamingManager_eventGetFoliageChunk_Parms
	{
		FString ChunkId;
		FAuracronFoliageChunkData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetFoliageChunk_Parms, ChunkId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkId_MetaData), NewProp_ChunkId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetFoliageChunk_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageChunkData, METADATA_PARAMS(0, nullptr) }; // 901826293
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetFoliageChunk", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::AuracronFoliageStreamingManager_eventGetFoliageChunk_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::AuracronFoliageStreamingManager_eventGetFoliageChunk_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetFoliageChunk)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChunkId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageChunkData*)Z_Param__Result=P_THIS->GetFoliageChunk(Z_Param_ChunkId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetFoliageChunk ******************

// ********** Begin Class UAuracronFoliageStreamingManager Function GetFoliageChunksInDataLayer ****
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics
{
	struct AuracronFoliageStreamingManager_eventGetFoliageChunksInDataLayer_Parms
	{
		FString DataLayerId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::NewProp_DataLayerId = { "DataLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetFoliageChunksInDataLayer_Parms, DataLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerId_MetaData), NewProp_DataLayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetFoliageChunksInDataLayer_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::NewProp_DataLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetFoliageChunksInDataLayer", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::AuracronFoliageStreamingManager_eventGetFoliageChunksInDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::AuracronFoliageStreamingManager_eventGetFoliageChunksInDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetFoliageChunksInDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DataLayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetFoliageChunksInDataLayer(Z_Param_DataLayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetFoliageChunksInDataLayer ******

// ********** Begin Class UAuracronFoliageStreamingManager Function GetFoliageMemoryUsage **********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics
{
	struct AuracronFoliageStreamingManager_eventGetFoliageMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetFoliageMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetFoliageMemoryUsage", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::AuracronFoliageStreamingManager_eventGetFoliageMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::AuracronFoliageStreamingManager_eventGetFoliageMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetFoliageMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFoliageMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetFoliageMemoryUsage ************

// ********** Begin Class UAuracronFoliageStreamingManager Function GetInstance ********************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics
{
	struct AuracronFoliageStreamingManager_eventGetInstance_Parms
	{
		UAuracronFoliageStreamingManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::AuracronFoliageStreamingManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::AuracronFoliageStreamingManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageStreamingManager**)Z_Param__Result=UAuracronFoliageStreamingManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetInstance **********************

// ********** Begin Class UAuracronFoliageStreamingManager Function GetLoadedChunkCount ************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics
{
	struct AuracronFoliageStreamingManager_eventGetLoadedChunkCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetLoadedChunkCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetLoadedChunkCount", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::AuracronFoliageStreamingManager_eventGetLoadedChunkCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::AuracronFoliageStreamingManager_eventGetLoadedChunkCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetLoadedChunkCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedChunkCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetLoadedChunkCount **************

// ********** Begin Class UAuracronFoliageStreamingManager Function GetPerformanceData *************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics
{
	struct AuracronFoliageStreamingManager_eventGetPerformanceData_Parms
	{
		FAuracronFoliageStreamingPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData, METADATA_PARAMS(0, nullptr) }; // 3841958052
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::AuracronFoliageStreamingManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::AuracronFoliageStreamingManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageStreamingPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function GetPerformanceData ***************

// ********** Begin Class UAuracronFoliageStreamingManager Function Initialize *********************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics
{
	struct AuracronFoliageStreamingManager_eventInitialize_Parms
	{
		FAuracronFoliageStreamingConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1670971137
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::AuracronFoliageStreamingManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::AuracronFoliageStreamingManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronFoliageStreamingConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function Initialize ***********************

// ********** Begin Class UAuracronFoliageStreamingManager Function InitializeMemoryPools **********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_InitializeMemoryPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_InitializeMemoryPools_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "InitializeMemoryPools", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_InitializeMemoryPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_InitializeMemoryPools_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_InitializeMemoryPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_InitializeMemoryPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execInitializeMemoryPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeMemoryPools();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function InitializeMemoryPools ************

// ********** Begin Class UAuracronFoliageStreamingManager Function IntegrateWithDataLayers ********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics
{
	struct AuracronFoliageStreamingManager_eventIntegrateWithDataLayers_Parms
	{
		UAuracronDataLayerManager* DataLayerManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DataLayerManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::NewProp_DataLayerManager = { "DataLayerManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventIntegrateWithDataLayers_Parms, DataLayerManager), Z_Construct_UClass_UAuracronDataLayerManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::NewProp_DataLayerManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "IntegrateWithDataLayers", Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::AuracronFoliageStreamingManager_eventIntegrateWithDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::AuracronFoliageStreamingManager_eventIntegrateWithDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execIntegrateWithDataLayers)
{
	P_GET_OBJECT(UAuracronDataLayerManager,Z_Param_DataLayerManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithDataLayers(Z_Param_DataLayerManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function IntegrateWithDataLayers **********

// ********** Begin Class UAuracronFoliageStreamingManager Function IntegrateWithWorldPartition ****
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics
{
	struct AuracronFoliageStreamingManager_eventIntegrateWithWorldPartition_Parms
	{
		UAuracronWorldPartitionStreamingManager* WorldPartitionManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World Partition integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition integration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventIntegrateWithWorldPartition_Parms, WorldPartitionManager), Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "IntegrateWithWorldPartition", Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::AuracronFoliageStreamingManager_eventIntegrateWithWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::AuracronFoliageStreamingManager_eventIntegrateWithWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execIntegrateWithWorldPartition)
{
	P_GET_OBJECT(UAuracronWorldPartitionStreamingManager,Z_Param_WorldPartitionManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithWorldPartition(Z_Param_WorldPartitionManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function IntegrateWithWorldPartition ******

// ********** Begin Class UAuracronFoliageStreamingManager Function IsAsyncFoliageLoadingEnabled ***
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics
{
	struct AuracronFoliageStreamingManager_eventIsAsyncFoliageLoadingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventIsAsyncFoliageLoadingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventIsAsyncFoliageLoadingEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "IsAsyncFoliageLoadingEnabled", Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::AuracronFoliageStreamingManager_eventIsAsyncFoliageLoadingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::AuracronFoliageStreamingManager_eventIsAsyncFoliageLoadingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execIsAsyncFoliageLoadingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAsyncFoliageLoadingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function IsAsyncFoliageLoadingEnabled *****

// ********** Begin Class UAuracronFoliageStreamingManager Function IsDebugVisualizationEnabled ****
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageStreamingManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageStreamingManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageStreamingManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function IsDebugVisualizationEnabled ******

// ********** Begin Class UAuracronFoliageStreamingManager Function IsInitialized ******************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics
{
	struct AuracronFoliageStreamingManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::AuracronFoliageStreamingManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::AuracronFoliageStreamingManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function IsInitialized ********************

// ********** Begin Class UAuracronFoliageStreamingManager Function IsMemoryPressureHigh ***********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics
{
	struct AuracronFoliageStreamingManager_eventIsMemoryPressureHigh_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventIsMemoryPressureHigh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventIsMemoryPressureHigh_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "IsMemoryPressureHigh", Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::AuracronFoliageStreamingManager_eventIsMemoryPressureHigh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::AuracronFoliageStreamingManager_eventIsMemoryPressureHigh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execIsMemoryPressureHigh)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMemoryPressureHigh();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function IsMemoryPressureHigh *************

// ********** Begin Class UAuracronFoliageStreamingManager Function LogStreamingStatistics *********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_LogStreamingStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_LogStreamingStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "LogStreamingStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_LogStreamingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_LogStreamingStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_LogStreamingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_LogStreamingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execLogStreamingStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogStreamingStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function LogStreamingStatistics ***********

// ********** Begin Class UAuracronFoliageStreamingManager Function OnDataLayerStateChanged ********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics
{
	struct AuracronFoliageStreamingManager_eventOnDataLayerStateChanged_Parms
	{
		FString DataLayerId;
		bool bIsLoaded;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayerId;
	static void NewProp_bIsLoaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoaded;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::NewProp_DataLayerId = { "DataLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventOnDataLayerStateChanged_Parms, DataLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerId_MetaData), NewProp_DataLayerId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::NewProp_bIsLoaded_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventOnDataLayerStateChanged_Parms*)Obj)->bIsLoaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::NewProp_bIsLoaded = { "bIsLoaded", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventOnDataLayerStateChanged_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::NewProp_bIsLoaded_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::NewProp_DataLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::NewProp_bIsLoaded,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "OnDataLayerStateChanged", Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::AuracronFoliageStreamingManager_eventOnDataLayerStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::AuracronFoliageStreamingManager_eventOnDataLayerStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execOnDataLayerStateChanged)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DataLayerId);
	P_GET_UBOOL(Z_Param_bIsLoaded);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnDataLayerStateChanged(Z_Param_DataLayerId,Z_Param_bIsLoaded);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function OnDataLayerStateChanged **********

// ********** Begin Class UAuracronFoliageStreamingManager Function ProcessStreamingRequests *******
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_ProcessStreamingRequests_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_ProcessStreamingRequests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "ProcessStreamingRequests", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_ProcessStreamingRequests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_ProcessStreamingRequests_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_ProcessStreamingRequests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_ProcessStreamingRequests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execProcessStreamingRequests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessStreamingRequests();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function ProcessStreamingRequests *********

// ********** Begin Class UAuracronFoliageStreamingManager Function RegisterFoliageWithCell ********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics
{
	struct AuracronFoliageStreamingManager_eventRegisterFoliageWithCell_Parms
	{
		FString CellId;
		FString FoliageTypeId;
		TArray<FTransform> Instances;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Instances_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Instances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Instances;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRegisterFoliageWithCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRegisterFoliageWithCell_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_Instances_Inner = { "Instances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_Instances = { "Instances", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRegisterFoliageWithCell_Parms, Instances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Instances_MetaData), NewProp_Instances_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_Instances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::NewProp_Instances,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "RegisterFoliageWithCell", Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::AuracronFoliageStreamingManager_eventRegisterFoliageWithCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::AuracronFoliageStreamingManager_eventRegisterFoliageWithCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execRegisterFoliageWithCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Instances);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterFoliageWithCell(Z_Param_CellId,Z_Param_FoliageTypeId,Z_Param_Out_Instances);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function RegisterFoliageWithCell **********

// ********** Begin Class UAuracronFoliageStreamingManager Function RemoveFoliageChunk *************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics
{
	struct AuracronFoliageStreamingManager_eventRemoveFoliageChunk_Parms
	{
		FString ChunkId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRemoveFoliageChunk_Parms, ChunkId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkId_MetaData), NewProp_ChunkId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventRemoveFoliageChunk_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventRemoveFoliageChunk_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "RemoveFoliageChunk", Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::AuracronFoliageStreamingManager_eventRemoveFoliageChunk_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::AuracronFoliageStreamingManager_eventRemoveFoliageChunk_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execRemoveFoliageChunk)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChunkId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveFoliageChunk(Z_Param_ChunkId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function RemoveFoliageChunk ***************

// ********** Begin Class UAuracronFoliageStreamingManager Function RequestFoliageChunkLoading *****
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics
{
	struct AuracronFoliageStreamingManager_eventRequestFoliageChunkLoading_Parms
	{
		FString ChunkId;
		EAuracronFoliageLoadingPriority Priority;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRequestFoliageChunkLoading_Parms, ChunkId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkId_MetaData), NewProp_ChunkId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRequestFoliageChunkLoading_Parms, Priority), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLoadingPriority, METADATA_PARAMS(0, nullptr) }; // 2394233910
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRequestFoliageChunkLoading_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "RequestFoliageChunkLoading", Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::AuracronFoliageStreamingManager_eventRequestFoliageChunkLoading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::AuracronFoliageStreamingManager_eventRequestFoliageChunkLoading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execRequestFoliageChunkLoading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChunkId);
	P_GET_ENUM(EAuracronFoliageLoadingPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->RequestFoliageChunkLoading(Z_Param_ChunkId,EAuracronFoliageLoadingPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function RequestFoliageChunkLoading *******

// ********** Begin Class UAuracronFoliageStreamingManager Function RequestFoliageChunkUnloading ***
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics
{
	struct AuracronFoliageStreamingManager_eventRequestFoliageChunkUnloading_Parms
	{
		FString ChunkId;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRequestFoliageChunkUnloading_Parms, ChunkId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkId_MetaData), NewProp_ChunkId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventRequestFoliageChunkUnloading_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "RequestFoliageChunkUnloading", Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::AuracronFoliageStreamingManager_eventRequestFoliageChunkUnloading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::AuracronFoliageStreamingManager_eventRequestFoliageChunkUnloading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execRequestFoliageChunkUnloading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChunkId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->RequestFoliageChunkUnloading(Z_Param_ChunkId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function RequestFoliageChunkUnloading *****

// ********** Begin Class UAuracronFoliageStreamingManager Function SetConfiguration ***************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics
{
	struct AuracronFoliageStreamingManager_eventSetConfiguration_Parms
	{
		FAuracronFoliageStreamingConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1670971137
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::AuracronFoliageStreamingManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::AuracronFoliageStreamingManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronFoliageStreamingConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function SetConfiguration *****************

// ********** Begin Class UAuracronFoliageStreamingManager Function Shutdown ***********************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function Shutdown *************************

// ********** Begin Class UAuracronFoliageStreamingManager Function Tick ***************************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics
{
	struct AuracronFoliageStreamingManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::AuracronFoliageStreamingManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::AuracronFoliageStreamingManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function Tick *****************************

// ********** Begin Class UAuracronFoliageStreamingManager Function UnregisterFoliageFromCell ******
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics
{
	struct AuracronFoliageStreamingManager_eventUnregisterFoliageFromCell_Parms
	{
		FString CellId;
		FString FoliageTypeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUnregisterFoliageFromCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUnregisterFoliageFromCell_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::NewProp_FoliageTypeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UnregisterFoliageFromCell", Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::AuracronFoliageStreamingManager_eventUnregisterFoliageFromCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::AuracronFoliageStreamingManager_eventUnregisterFoliageFromCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUnregisterFoliageFromCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterFoliageFromCell(Z_Param_CellId,Z_Param_FoliageTypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UnregisterFoliageFromCell ********

// ********** Begin Class UAuracronFoliageStreamingManager Function UpdateAsyncLoadingOperations ***
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics
{
	struct AuracronFoliageStreamingManager_eventUpdateAsyncLoadingOperations_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUpdateAsyncLoadingOperations_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UpdateAsyncLoadingOperations", Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::AuracronFoliageStreamingManager_eventUpdateAsyncLoadingOperations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::AuracronFoliageStreamingManager_eventUpdateAsyncLoadingOperations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUpdateAsyncLoadingOperations)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAsyncLoadingOperations(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UpdateAsyncLoadingOperations *****

// ********** Begin Class UAuracronFoliageStreamingManager Function UpdateDataLayerBasedStreaming **
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDataLayerBasedStreaming_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDataLayerBasedStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UpdateDataLayerBasedStreaming", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDataLayerBasedStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDataLayerBasedStreaming_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDataLayerBasedStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDataLayerBasedStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUpdateDataLayerBasedStreaming)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDataLayerBasedStreaming();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UpdateDataLayerBasedStreaming ****

// ********** Begin Class UAuracronFoliageStreamingManager Function UpdateDistanceBasedStreaming ***
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics
{
	struct AuracronFoliageStreamingManager_eventUpdateDistanceBasedStreaming_Parms
	{
		TArray<FVector> StreamingSources;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingSources_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingSources_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StreamingSources;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::NewProp_StreamingSources_Inner = { "StreamingSources", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::NewProp_StreamingSources = { "StreamingSources", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUpdateDistanceBasedStreaming_Parms, StreamingSources), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingSources_MetaData), NewProp_StreamingSources_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::NewProp_StreamingSources_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::NewProp_StreamingSources,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UpdateDistanceBasedStreaming", Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::AuracronFoliageStreamingManager_eventUpdateDistanceBasedStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::AuracronFoliageStreamingManager_eventUpdateDistanceBasedStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUpdateDistanceBasedStreaming)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_StreamingSources);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedStreaming(Z_Param_Out_StreamingSources);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UpdateDistanceBasedStreaming *****

// ********** Begin Class UAuracronFoliageStreamingManager Function UpdateFoliageChunk *************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics
{
	struct AuracronFoliageStreamingManager_eventUpdateFoliageChunk_Parms
	{
		FString ChunkId;
		FAuracronFoliageChunkData ChunkData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChunkData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChunkId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChunkData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ChunkId = { "ChunkId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUpdateFoliageChunk_Parms, ChunkId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkId_MetaData), NewProp_ChunkId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ChunkData = { "ChunkData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUpdateFoliageChunk_Parms, ChunkData), Z_Construct_UScriptStruct_FAuracronFoliageChunkData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChunkData_MetaData), NewProp_ChunkData_MetaData) }; // 901826293
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventUpdateFoliageChunk_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventUpdateFoliageChunk_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ChunkId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ChunkData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UpdateFoliageChunk", Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::AuracronFoliageStreamingManager_eventUpdateFoliageChunk_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::AuracronFoliageStreamingManager_eventUpdateFoliageChunk_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUpdateFoliageChunk)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChunkId);
	P_GET_STRUCT_REF(FAuracronFoliageChunkData,Z_Param_Out_ChunkData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateFoliageChunk(Z_Param_ChunkId,Z_Param_Out_ChunkData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UpdateFoliageChunk ***************

// ********** Begin Class UAuracronFoliageStreamingManager Function UpdateMemoryManagement *********
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryManagement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UpdateMemoryManagement", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryManagement_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUpdateMemoryManagement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMemoryManagement();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UpdateMemoryManagement ***********

// ********** Begin Class UAuracronFoliageStreamingManager Function UpdateMemoryPool ***************
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics
{
	struct AuracronFoliageStreamingManager_eventUpdateMemoryPool_Parms
	{
		FString PoolId;
		FAuracronFoliageMemoryPool PoolData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PoolId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PoolData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_PoolId = { "PoolId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUpdateMemoryPool_Parms, PoolId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolId_MetaData), NewProp_PoolId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_PoolData = { "PoolData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageStreamingManager_eventUpdateMemoryPool_Parms, PoolData), Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolData_MetaData), NewProp_PoolData_MetaData) }; // 2257613911
void Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageStreamingManager_eventUpdateMemoryPool_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageStreamingManager_eventUpdateMemoryPool_Parms), &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_PoolId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_PoolData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UpdateMemoryPool", Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::AuracronFoliageStreamingManager_eventUpdateMemoryPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::AuracronFoliageStreamingManager_eventUpdateMemoryPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUpdateMemoryPool)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PoolId);
	P_GET_STRUCT_REF(FAuracronFoliageMemoryPool,Z_Param_Out_PoolData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateMemoryPool(Z_Param_PoolId,Z_Param_Out_PoolData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UpdateMemoryPool *****************

// ********** Begin Class UAuracronFoliageStreamingManager Function UpdatePerformanceMetrics *******
struct Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageStreamingManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageStreamingManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageStreamingManager Function UpdatePerformanceMetrics *********

// ********** Begin Class UAuracronFoliageStreamingManager *****************************************
void UAuracronFoliageStreamingManager::StaticRegisterNativesUAuracronFoliageStreamingManager()
{
	UClass* Class = UAuracronFoliageStreamingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CancelFoliageStreamingRequest", &UAuracronFoliageStreamingManager::execCancelFoliageStreamingRequest },
		{ "CleanupUnusedMemory", &UAuracronFoliageStreamingManager::execCleanupUnusedMemory },
		{ "CreateFoliageChunk", &UAuracronFoliageStreamingManager::execCreateFoliageChunk },
		{ "CreateMemoryPool", &UAuracronFoliageStreamingManager::execCreateMemoryPool },
		{ "DrawDebugStreamingInfo", &UAuracronFoliageStreamingManager::execDrawDebugStreamingInfo },
		{ "EnableAsyncFoliageLoading", &UAuracronFoliageStreamingManager::execEnableAsyncFoliageLoading },
		{ "EnableDebugVisualization", &UAuracronFoliageStreamingManager::execEnableDebugVisualization },
		{ "GetActiveAsyncLoadingCount", &UAuracronFoliageStreamingManager::execGetActiveAsyncLoadingCount },
		{ "GetAllFoliageChunks", &UAuracronFoliageStreamingManager::execGetAllFoliageChunks },
		{ "GetChunksInRange", &UAuracronFoliageStreamingManager::execGetChunksInRange },
		{ "GetConfiguration", &UAuracronFoliageStreamingManager::execGetConfiguration },
		{ "GetFoliageChunk", &UAuracronFoliageStreamingManager::execGetFoliageChunk },
		{ "GetFoliageChunksInDataLayer", &UAuracronFoliageStreamingManager::execGetFoliageChunksInDataLayer },
		{ "GetFoliageMemoryUsage", &UAuracronFoliageStreamingManager::execGetFoliageMemoryUsage },
		{ "GetInstance", &UAuracronFoliageStreamingManager::execGetInstance },
		{ "GetLoadedChunkCount", &UAuracronFoliageStreamingManager::execGetLoadedChunkCount },
		{ "GetPerformanceData", &UAuracronFoliageStreamingManager::execGetPerformanceData },
		{ "Initialize", &UAuracronFoliageStreamingManager::execInitialize },
		{ "InitializeMemoryPools", &UAuracronFoliageStreamingManager::execInitializeMemoryPools },
		{ "IntegrateWithDataLayers", &UAuracronFoliageStreamingManager::execIntegrateWithDataLayers },
		{ "IntegrateWithWorldPartition", &UAuracronFoliageStreamingManager::execIntegrateWithWorldPartition },
		{ "IsAsyncFoliageLoadingEnabled", &UAuracronFoliageStreamingManager::execIsAsyncFoliageLoadingEnabled },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageStreamingManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageStreamingManager::execIsInitialized },
		{ "IsMemoryPressureHigh", &UAuracronFoliageStreamingManager::execIsMemoryPressureHigh },
		{ "LogStreamingStatistics", &UAuracronFoliageStreamingManager::execLogStreamingStatistics },
		{ "OnDataLayerStateChanged", &UAuracronFoliageStreamingManager::execOnDataLayerStateChanged },
		{ "ProcessStreamingRequests", &UAuracronFoliageStreamingManager::execProcessStreamingRequests },
		{ "RegisterFoliageWithCell", &UAuracronFoliageStreamingManager::execRegisterFoliageWithCell },
		{ "RemoveFoliageChunk", &UAuracronFoliageStreamingManager::execRemoveFoliageChunk },
		{ "RequestFoliageChunkLoading", &UAuracronFoliageStreamingManager::execRequestFoliageChunkLoading },
		{ "RequestFoliageChunkUnloading", &UAuracronFoliageStreamingManager::execRequestFoliageChunkUnloading },
		{ "SetConfiguration", &UAuracronFoliageStreamingManager::execSetConfiguration },
		{ "Shutdown", &UAuracronFoliageStreamingManager::execShutdown },
		{ "Tick", &UAuracronFoliageStreamingManager::execTick },
		{ "UnregisterFoliageFromCell", &UAuracronFoliageStreamingManager::execUnregisterFoliageFromCell },
		{ "UpdateAsyncLoadingOperations", &UAuracronFoliageStreamingManager::execUpdateAsyncLoadingOperations },
		{ "UpdateDataLayerBasedStreaming", &UAuracronFoliageStreamingManager::execUpdateDataLayerBasedStreaming },
		{ "UpdateDistanceBasedStreaming", &UAuracronFoliageStreamingManager::execUpdateDistanceBasedStreaming },
		{ "UpdateFoliageChunk", &UAuracronFoliageStreamingManager::execUpdateFoliageChunk },
		{ "UpdateMemoryManagement", &UAuracronFoliageStreamingManager::execUpdateMemoryManagement },
		{ "UpdateMemoryPool", &UAuracronFoliageStreamingManager::execUpdateMemoryPool },
		{ "UpdatePerformanceMetrics", &UAuracronFoliageStreamingManager::execUpdatePerformanceMetrics },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageStreamingManager;
UClass* UAuracronFoliageStreamingManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageStreamingManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageStreamingManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageStreamingManager"),
			Z_Registration_Info_UClass_UAuracronFoliageStreamingManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageStreamingManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageStreamingManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister()
{
	return UAuracronFoliageStreamingManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Streaming Manager\n * Manager for foliage streaming integration with World Partition\n */" },
#endif
		{ "IncludePath", "AuracronFoliageStreaming.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Streaming Manager\nManager for foliage streaming integration with World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageChunkLoaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageChunkUnloaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageStreamingStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageMemoryPressure_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionManager_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageChunkLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageChunkUnloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageStreamingStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageMemoryPressure;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_DataLayerManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_CancelFoliageStreamingRequest, "CancelFoliageStreamingRequest" }, // 2600318160
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_CleanupUnusedMemory, "CleanupUnusedMemory" }, // 1919494130
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateFoliageChunk, "CreateFoliageChunk" }, // 3305183460
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_CreateMemoryPool, "CreateMemoryPool" }, // 2345360436
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_DrawDebugStreamingInfo, "DrawDebugStreamingInfo" }, // 2604058697
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableAsyncFoliageLoading, "EnableAsyncFoliageLoading" }, // 731310668
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 3522694651
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetActiveAsyncLoadingCount, "GetActiveAsyncLoadingCount" }, // 2836667612
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetAllFoliageChunks, "GetAllFoliageChunks" }, // 2758536337
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetChunksInRange, "GetChunksInRange" }, // 3065091440
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetConfiguration, "GetConfiguration" }, // 4193229325
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunk, "GetFoliageChunk" }, // 2282387023
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageChunksInDataLayer, "GetFoliageChunksInDataLayer" }, // 3462756532
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetFoliageMemoryUsage, "GetFoliageMemoryUsage" }, // 4009330252
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetInstance, "GetInstance" }, // 2943782181
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetLoadedChunkCount, "GetLoadedChunkCount" }, // 1161312460
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_GetPerformanceData, "GetPerformanceData" }, // 661525182
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_Initialize, "Initialize" }, // 4258356515
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_InitializeMemoryPools, "InitializeMemoryPools" }, // 1555088193
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithDataLayers, "IntegrateWithDataLayers" }, // 612049098
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IntegrateWithWorldPartition, "IntegrateWithWorldPartition" }, // 2081056657
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsAsyncFoliageLoadingEnabled, "IsAsyncFoliageLoadingEnabled" }, // 1815921739
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 3955258010
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsInitialized, "IsInitialized" }, // 138890666
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_IsMemoryPressureHigh, "IsMemoryPressureHigh" }, // 1762402807
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_LogStreamingStatistics, "LogStreamingStatistics" }, // 1271229182
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_OnDataLayerStateChanged, "OnDataLayerStateChanged" }, // 1717699743
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature, "OnFoliageChunkLoaded__DelegateSignature" }, // 2875772649
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature, "OnFoliageChunkUnloaded__DelegateSignature" }, // 239481707
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature, "OnFoliageMemoryPressure__DelegateSignature" }, // 2591582573
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature, "OnFoliageStreamingStateChanged__DelegateSignature" }, // 1692397190
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_ProcessStreamingRequests, "ProcessStreamingRequests" }, // 1169210015
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_RegisterFoliageWithCell, "RegisterFoliageWithCell" }, // 1990746180
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_RemoveFoliageChunk, "RemoveFoliageChunk" }, // 505703597
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkLoading, "RequestFoliageChunkLoading" }, // 3929758630
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_RequestFoliageChunkUnloading, "RequestFoliageChunkUnloading" }, // 2925879318
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_SetConfiguration, "SetConfiguration" }, // 1280705093
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_Shutdown, "Shutdown" }, // 3264058850
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_Tick, "Tick" }, // 2700689696
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UnregisterFoliageFromCell, "UnregisterFoliageFromCell" }, // 3180692715
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateAsyncLoadingOperations, "UpdateAsyncLoadingOperations" }, // 3815076011
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDataLayerBasedStreaming, "UpdateDataLayerBasedStreaming" }, // 1942839386
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateDistanceBasedStreaming, "UpdateDistanceBasedStreaming" }, // 3237364615
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateFoliageChunk, "UpdateFoliageChunk" }, // 1641444283
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryManagement, "UpdateMemoryManagement" }, // 3327398302
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdateMemoryPool, "UpdateMemoryPool" }, // 1139931309
		{ &Z_Construct_UFunction_UAuracronFoliageStreamingManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 1383884752
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageStreamingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageChunkLoaded = { "OnFoliageChunkLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, OnFoliageChunkLoaded), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageChunkLoaded_MetaData), NewProp_OnFoliageChunkLoaded_MetaData) }; // 2875772649
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageChunkUnloaded = { "OnFoliageChunkUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, OnFoliageChunkUnloaded), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageChunkUnloaded_MetaData), NewProp_OnFoliageChunkUnloaded_MetaData) }; // 239481707
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageStreamingStateChanged = { "OnFoliageStreamingStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, OnFoliageStreamingStateChanged), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageStreamingStateChanged_MetaData), NewProp_OnFoliageStreamingStateChanged_MetaData) }; // 1692397190
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageMemoryPressure = { "OnFoliageMemoryPressure", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, OnFoliageMemoryPressure), Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageMemoryPressure_MetaData), NewProp_OnFoliageMemoryPressure_MetaData) }; // 2591582573
void Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageStreamingManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageStreamingManager), &Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1670971137
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, WorldPartitionManager), Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionManager_MetaData), NewProp_WorldPartitionManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_DataLayerManager = { "DataLayerManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageStreamingManager, DataLayerManager), Z_Construct_UClass_UAuracronDataLayerManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerManager_MetaData), NewProp_DataLayerManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageChunkLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageChunkUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageStreamingStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_OnFoliageMemoryPressure,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_WorldPartitionManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::NewProp_DataLayerManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::ClassParams = {
	&UAuracronFoliageStreamingManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageStreamingManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageStreamingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageStreamingManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageStreamingManager.OuterSingleton;
}
UAuracronFoliageStreamingManager::UAuracronFoliageStreamingManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageStreamingManager);
UAuracronFoliageStreamingManager::~UAuracronFoliageStreamingManager() {}
// ********** End Class UAuracronFoliageStreamingManager *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronFoliageStreamingStrategy_StaticEnum, TEXT("EAuracronFoliageStreamingStrategy"), &Z_Registration_Info_UEnum_EAuracronFoliageStreamingStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 202874679U) },
		{ EAuracronFoliageMemoryStrategy_StaticEnum, TEXT("EAuracronFoliageMemoryStrategy"), &Z_Registration_Info_UEnum_EAuracronFoliageMemoryStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2314153493U) },
		{ EAuracronFoliageLoadingPriority_StaticEnum, TEXT("EAuracronFoliageLoadingPriority"), &Z_Registration_Info_UEnum_EAuracronFoliageLoadingPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2394233910U) },
		{ EAuracronFoliageStreamingState_StaticEnum, TEXT("EAuracronFoliageStreamingState"), &Z_Registration_Info_UEnum_EAuracronFoliageStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2929610450U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronFoliageStreamingConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics::NewStructOps, TEXT("AuracronFoliageStreamingConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageStreamingConfiguration), 1670971137U) },
		{ FAuracronFoliageChunkData::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics::NewStructOps, TEXT("AuracronFoliageChunkData"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageChunkData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageChunkData), 901826293U) },
		{ FAuracronFoliageStreamingRequest::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics::NewStructOps, TEXT("AuracronFoliageStreamingRequest"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingRequest, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageStreamingRequest), 2631051185U) },
		{ FAuracronFoliageMemoryPool::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics::NewStructOps, TEXT("AuracronFoliageMemoryPool"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageMemoryPool, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageMemoryPool), 2257613911U) },
		{ FAuracronFoliageStreamingPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics::NewStructOps, TEXT("AuracronFoliageStreamingPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageStreamingPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageStreamingPerformanceData), 3841958052U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageStreamingManager, UAuracronFoliageStreamingManager::StaticClass, TEXT("UAuracronFoliageStreamingManager"), &Z_Registration_Info_UClass_UAuracronFoliageStreamingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageStreamingManager), 2960702316U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_1427153699(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
