// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGPointProcessing.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGPointProcessing_generated_h
#error "AuracronPCGPointProcessing.generated.h already included, missing '#pragma once' in AuracronPCGPointProcessing.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGPointProcessing_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UPCGMetadata;
enum class EAuracronPCGMathOperation : uint8;
enum class EAuracronPCGProcessingOperation : uint8;
enum class EAuracronPCGSortCriteria : uint8;
struct FPCGPoint;

// ********** Begin Class UAuracronPCGAdvancedPointFilterSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_131_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedPointFilterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedPointFilterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedPointFilterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_131_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedPointFilterSettings(UAuracronPCGAdvancedPointFilterSettings&&) = delete; \
	UAuracronPCGAdvancedPointFilterSettings(const UAuracronPCGAdvancedPointFilterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedPointFilterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedPointFilterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedPointFilterSettings) \
	NO_API virtual ~UAuracronPCGAdvancedPointFilterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_128_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_131_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_131_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_131_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedPointFilterSettings;

// ********** End Class UAuracronPCGAdvancedPointFilterSettings ************************************

// ********** Begin Class UAuracronPCGAdvancedPointTransformerSettings *****************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_210_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedPointTransformerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedPointTransformerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedPointTransformerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_210_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedPointTransformerSettings(UAuracronPCGAdvancedPointTransformerSettings&&) = delete; \
	UAuracronPCGAdvancedPointTransformerSettings(const UAuracronPCGAdvancedPointTransformerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedPointTransformerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedPointTransformerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedPointTransformerSettings) \
	NO_API virtual ~UAuracronPCGAdvancedPointTransformerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_207_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_210_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_210_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_210_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedPointTransformerSettings;

// ********** End Class UAuracronPCGAdvancedPointTransformerSettings *******************************

// ********** Begin Class UAuracronPCGAdvancedPointMergerSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_294_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedPointMergerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedPointMergerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedPointMergerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_294_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedPointMergerSettings(UAuracronPCGAdvancedPointMergerSettings&&) = delete; \
	UAuracronPCGAdvancedPointMergerSettings(const UAuracronPCGAdvancedPointMergerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedPointMergerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedPointMergerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedPointMergerSettings) \
	NO_API virtual ~UAuracronPCGAdvancedPointMergerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_291_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_294_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_294_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_294_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedPointMergerSettings;

// ********** End Class UAuracronPCGAdvancedPointMergerSettings ************************************

// ********** Begin Class UAuracronPCGAdvancedPointSplitterSettings ********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_357_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedPointSplitterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedPointSplitterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedPointSplitterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_357_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedPointSplitterSettings(UAuracronPCGAdvancedPointSplitterSettings&&) = delete; \
	UAuracronPCGAdvancedPointSplitterSettings(const UAuracronPCGAdvancedPointSplitterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedPointSplitterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedPointSplitterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedPointSplitterSettings) \
	NO_API virtual ~UAuracronPCGAdvancedPointSplitterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_354_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_357_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_357_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_357_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedPointSplitterSettings;

// ********** End Class UAuracronPCGAdvancedPointSplitterSettings **********************************

// ********** Begin Class UAuracronPCGAdvancedPointSorterSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_424_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedPointSorterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedPointSorterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedPointSorterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_424_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedPointSorterSettings(UAuracronPCGAdvancedPointSorterSettings&&) = delete; \
	UAuracronPCGAdvancedPointSorterSettings(const UAuracronPCGAdvancedPointSorterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedPointSorterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedPointSorterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedPointSorterSettings) \
	NO_API virtual ~UAuracronPCGAdvancedPointSorterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_421_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_424_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_424_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_424_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedPointSorterSettings;

// ********** End Class UAuracronPCGAdvancedPointSorterSettings ************************************

// ********** Begin Class UAuracronPCGPointProcessingUtils *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_501_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execShouldUseParallelProcessing); \
	DECLARE_FUNCTION(execGetOptimalBatchSize); \
	DECLARE_FUNCTION(execProcessPointsBatch); \
	DECLARE_FUNCTION(execSetPointAttributeValue); \
	DECLARE_FUNCTION(execGetPointAttributeValue); \
	DECLARE_FUNCTION(execIsPointInRegion); \
	DECLARE_FUNCTION(execCalculatePointDistance); \
	DECLARE_FUNCTION(execComparePoints); \
	DECLARE_FUNCTION(execApplyMathOperationVector); \
	DECLARE_FUNCTION(execApplyMathOperation);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointProcessingUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_501_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPointProcessingUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointProcessingUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPointProcessingUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPointProcessingUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPointProcessingUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_501_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGPointProcessingUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPointProcessingUtils(UAuracronPCGPointProcessingUtils&&) = delete; \
	UAuracronPCGPointProcessingUtils(const UAuracronPCGPointProcessingUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPointProcessingUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPointProcessingUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGPointProcessingUtils) \
	NO_API virtual ~UAuracronPCGPointProcessingUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_498_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_501_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_501_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_501_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h_501_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPointProcessingUtils;

// ********** End Class UAuracronPCGPointProcessingUtils *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h

// ********** Begin Enum EAuracronPCGProcessingOperation *******************************************
#define FOREACH_ENUM_EAURACRONPCGPROCESSINGOPERATION(op) \
	op(EAuracronPCGProcessingOperation::Filter) \
	op(EAuracronPCGProcessingOperation::Transform) \
	op(EAuracronPCGProcessingOperation::Merge) \
	op(EAuracronPCGProcessingOperation::Split) \
	op(EAuracronPCGProcessingOperation::Sort) \
	op(EAuracronPCGProcessingOperation::Duplicate) \
	op(EAuracronPCGProcessingOperation::Prune) \
	op(EAuracronPCGProcessingOperation::Cluster) 

enum class EAuracronPCGProcessingOperation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGProcessingOperation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGProcessingOperation>();
// ********** End Enum EAuracronPCGProcessingOperation *********************************************

// ********** Begin Enum EAuracronPCGMathOperation *************************************************
#define FOREACH_ENUM_EAURACRONPCGMATHOPERATION(op) \
	op(EAuracronPCGMathOperation::Add) \
	op(EAuracronPCGMathOperation::Subtract) \
	op(EAuracronPCGMathOperation::Multiply) \
	op(EAuracronPCGMathOperation::Divide) \
	op(EAuracronPCGMathOperation::Power) \
	op(EAuracronPCGMathOperation::Modulo) \
	op(EAuracronPCGMathOperation::Min) \
	op(EAuracronPCGMathOperation::Max) \
	op(EAuracronPCGMathOperation::Average) \
	op(EAuracronPCGMathOperation::Lerp) \
	op(EAuracronPCGMathOperation::Clamp) \
	op(EAuracronPCGMathOperation::Normalize) \
	op(EAuracronPCGMathOperation::Abs) \
	op(EAuracronPCGMathOperation::Sign) \
	op(EAuracronPCGMathOperation::Floor) \
	op(EAuracronPCGMathOperation::Ceil) \
	op(EAuracronPCGMathOperation::Round) \
	op(EAuracronPCGMathOperation::Frac) \
	op(EAuracronPCGMathOperation::Sqrt) \
	op(EAuracronPCGMathOperation::Log) \
	op(EAuracronPCGMathOperation::Exp) \
	op(EAuracronPCGMathOperation::Sin) \
	op(EAuracronPCGMathOperation::Cos) \
	op(EAuracronPCGMathOperation::Tan) 

enum class EAuracronPCGMathOperation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMathOperation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMathOperation>();
// ********** End Enum EAuracronPCGMathOperation ***************************************************

// ********** Begin Enum EAuracronPCGSortCriteria **************************************************
#define FOREACH_ENUM_EAURACRONPCGSORTCRITERIA(op) \
	op(EAuracronPCGSortCriteria::Position) \
	op(EAuracronPCGSortCriteria::Distance) \
	op(EAuracronPCGSortCriteria::Density) \
	op(EAuracronPCGSortCriteria::Scale) \
	op(EAuracronPCGSortCriteria::Rotation) \
	op(EAuracronPCGSortCriteria::Attribute) \
	op(EAuracronPCGSortCriteria::Random) \
	op(EAuracronPCGSortCriteria::Index) 

enum class EAuracronPCGSortCriteria : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSortCriteria> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSortCriteria>();
// ********** End Enum EAuracronPCGSortCriteria ****************************************************

// ********** Begin Enum EAuracronPCGMergeStrategy *************************************************
#define FOREACH_ENUM_EAURACRONPCGMERGESTRATEGY(op) \
	op(EAuracronPCGMergeStrategy::Append) \
	op(EAuracronPCGMergeStrategy::Interleave) \
	op(EAuracronPCGMergeStrategy::Weighted) \
	op(EAuracronPCGMergeStrategy::Spatial) \
	op(EAuracronPCGMergeStrategy::Attribute) \
	op(EAuracronPCGMergeStrategy::Priority) 

enum class EAuracronPCGMergeStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMergeStrategy> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMergeStrategy>();
// ********** End Enum EAuracronPCGMergeStrategy ***************************************************

// ********** Begin Enum EAuracronPCGSplitCriteria *************************************************
#define FOREACH_ENUM_EAURACRONPCGSPLITCRITERIA(op) \
	op(EAuracronPCGSplitCriteria::Count) \
	op(EAuracronPCGSplitCriteria::Percentage) \
	op(EAuracronPCGSplitCriteria::Attribute) \
	op(EAuracronPCGSplitCriteria::Spatial) \
	op(EAuracronPCGSplitCriteria::Random) \
	op(EAuracronPCGSplitCriteria::Pattern) 

enum class EAuracronPCGSplitCriteria : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSplitCriteria> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplitCriteria>();
// ********** End Enum EAuracronPCGSplitCriteria ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
