# 🌟 AURACRON - GAME DESIGN DOCUMENT UNIFICADO _(formerly Nexus Realms)_
**Versão**: 2.0 - Documento Unificado  
**Data**: Janeiro de 2025  
**Plataforma**: Mobile (Android/iOS) + PC  
**Engine**: Unreal Engine 5.6  
**Tagline**: _"Domine as três camadas. Desperte o Auracron."_

---

## 📋 **ÍNDICE**
1. [Visão Geral](#visão-geral)
2. [Análise Competitiva](#análise-competitiva)
3. [Mecânicas Inovadoras](#mecânicas-inovadoras)
4. [<PERSON><PERSON><PERSON> Técnicos](#sistemas-técnicos)
5. [Progressão e Monetização](#progressão-e-monetização)
6. [Roadmap de Desenvolvimento](#roadmap-de-desenvolvimento)
7. [Análise de Riscos](#análise-de-riscos)
8. [Métricas de Sucesso](#métricas-de-sucesso)
9. [Próximos Pass<PERSON>](#próximos-passos)
10. [Conclusão](#conclusão)
11. [Histórico de Atualizações](#histórico-de-atualizações)

---

## 🎯 **VISÃO GERAL**

### **Conceito Central**
**AURACRON** é um MOBA 5v5 revolucionário que combina elementos tradicionais com **mapas dinâmicos multidimensionais** e **IA adaptativa**. O diferencial está na capacidade do mapa evoluir durante a partida, criando layers verticais de combate e objetivos procedurais únicos.

### **Público-Alvo**
- **Primário**: Players de MOBA mobile (18-35 anos)
- **Secundário**: Gamers PC buscando inovação no gênero
- **Terciário**: Streamers/criadores de conteúdo

### **Pillars de Design**
1. **📐 EVOLUÇÃO CONSTANTE**: Mapas que mudam, estratégias que adaptam
2. **🎮 ACESSIBILIDADE INTELIGENTE**: Complexo para mestres, simples para iniciantes
3. **🤝 COOPERAÇÃO AMPLIADA**: Mecânicas que recompensam teamwork criativo
4. **⚡ INOVAÇÃO TECNOLÓGICA**: IA, procedural generation, physics avançada

### **Rebrand Global**
- Codename/brand oficial: **AURACRON**
- Referências a "Nexus Realms" ficam como nome de engine/lore interno
- Domínios reservados: auracron.com / auracron.gg / auracron.game

---

## ⚔️ **ANÁLISE COMPETITIVA**

### **Wild Rift vs AURACRON**

| **ASPECTO** | **WILD RIFT** | **AURACRON** | **VANTAGEM** |
|-------------|---------------|---------------|--------------|
| **Mapa** | Estático, 3 lanes fixas | Dinâmico, 3 realms evolutivos | 🟢 **NOSSA** |
| **Combate** | 2D horizontal | 3D vertical (3 layers) | 🟢 **NOSSA** |
| **Objectives** | Fixos e previsíveis | Procedurais + IA adaptativa | 🟢 **NOSSA** |
| **Champions** | 164 fixos | Sistema de Sígilos Auracron | 🟢 **NOSSA** |
| **Jungle** | Spawn patterns fixos | IA que aprende e adapta | 🟢 **NOSSA** |
| **Reconhecimento** | ✅ Brand estabelecido | ❌ Brand novo | 🔴 **DELES** |
| **Player Base** | ✅ 50M+ players | ❌ Zero players | 🔴 **DELES** |
| **Recursos** | ✅ Budget Riot Games | ❌ Indie/Startup | 🔴 **DELES** |

### **Outros Competidores**
- **Mobile Legends**: Nosso diferencial é superior tecnicamente
- **Arena of Valor**: Inovação vs established gameplay
- **Heroes Evolved**: Superamos em todos os aspectos técnicos

---

## 🚀 **MECÂNICAS INOVADORAS**

### **1. DYNAMIC REALM SYSTEM** 🌍

#### **Estrutura de Camadas (Nomenclatura Própria)**
| Camada | Nome Interno | Função | Objetivos Exclusivos |
|--------|--------------|--------|----------------------|
| I | **Planície Radiante** | Campo base, acessível; três Rotas (Trilho Solar, Trilho Axis, Trilho Lunar) com vegetação "*Canopy*" para emboscadas. | Guardião Prismal (empurra rota) • Torre Prisma (aura de dano em área) |
| II | **Firmamento Zephyr** | Plataformas flutuantes a 70 m; domínio de visão vertical. | Núcleo de Tempestade (buff ofensivo) • Santuários dos Ventos (reduz recarga de mobilidade) |
| III | **Abismo Umbrio** | Rede de túneis bioluminescentes; foco em furtividade. | Leviatã Umbrático (lifesteal + penetração) • Altares da Sombra (porta unidirecional) |

#### **Timeline da Partida**

**00:00-10:00 → REALM TERRESTRE (Planície Radiante)**
- Mapa tradicional 3-trilho
- Jungle padrão com adaptação IA
- Foco em laning phase clássico

**10:00-15:00 → PORTAL PHASE**
- Portais dimensionais aparecem
- Escolhas estratégicas de realm
- Split decisions entre equipes

**15:00-20:00 → REALM CELESTIAL (Firmamento Zephyr)**
- Plataformas flutuantes ativam
- Air champions ganham vantagem
- Nova layer de combate vertical
- Objetivos aéreos únicos

**20:00-25:00 → REALM ABYSSAL (Abismo Umbrio)**
- Túneis subterrâneos abrem
- Stealth champions dominam
- Underground objectives
- Rotas de flank secretas

**25:00+ → NEXUS FUSION**
- Todos os realms simultâneos
- Combate 3D completo
- Máxima complexidade estratégica
- Endgame épico e único

#### **Conectores Verticais**
1. **Portais de Ânima** – permanentes nas bases
2. **Fendas Fluxo** – rasgam o solo e permitem descida/ascensão temporária
3. **Cipós Astria** – cordas vegetais escaláveis
4. **Elevadores de Vórtice** – plataformas mágicas de transporte vertical
4. **Elevadores de Vórtice** – colunas de vento que transportam tropas e heróis

#### **Impacto Estratégico**
- **Early Game**: Foco em farming e positioning clássico
- **Mid Game**: Decisões de realm criam vantagens posicionais
- **Late Game**: Maestria 3D separa players casuais de pros

### **2. SISTEMA DE SÍGILOS AURACRON** 👥 _(Fusion 2.0)_

#### **Mecânica Central**
- A fusão deixa de exigir dois jogadores
- Durante a **tela de seleção de campeões**, cada player escolhe **1 de 3 "Sígilos Auracron"** (Tank, Damage, Utility)
- O Sigilo funde-se ao campeão aos 6 min, desbloqueando árvore de habilidades alternativa
- Pode ser re-forjado no Nexus uma vez por partida (cooldown global de 2 min)
- Cria combinatória de 50 campeões × 3 Sígilos = 150 arquétipos sem depender de cooperação específica

#### **Tipos de Sígilos**
| Sigilo | Bônus Passivo | Habilidade Exclusiva | Arquétipo-chave |
|--------|--------------|----------------------|-----------------|
| **Aegis** (Tank) | +15% HP, Armadura adaptativa | "Murallion" – cria barreira circular 3s | Frontliner / Iniciado |
| **Ruin** (Damage) | +12% ATK / AP adaptativo | "Fracasso Prismal" – reset parcial de CD | Burst / Skirmisher |
| **Vesper** (Utility) | +10% Vel. Move + 8% Cooldown | "Sopro de Fluxo" – dash aliado + shield | Roamer / Suporte |

#### **Impacto em Balanceamento**
- Remove gargalo de _matchmaking_ de fusão 2-jogadores
- Incentiva expressão individual (paralelo às Runas de LoL)
- Mantém identidade "fusão" como power-spike temático

### **3. VERTICAL COMBAT LAYERS** ⬆️

#### **Surface Layer (Camada Terrestre)**
- **Características**: Combate tradicional de MOBA
- **Alcance**: Padrão (800 unidades)
- **Área de Efeito**: Média (300 unidades)
- **Interação Vertical**: Limitada à própria camada

#### **Sky Layer (Camada Celestial)**
- **Características**: Combate aéreo com vantagem posicional
- **Alcance**: Estendido (1200 unidades)
- **Área de Efeito**: Ampliada (400 unidades)
- **Interação Vertical**: Pode afetar camada terrestre com redução de dano
- **Vantagem**: Visão superior e alcance aumentado

#### **Underground Layer (Camada Subterrânea)**
- **Características**: Combate furtivo com foco em emboscadas
- **Alcance**: Reduzido (600 unidades)
- **Área de Efeito**: Compacta (250 unidades)
- **Interação Vertical**: Stealth natural e bônus de emboscada
- **Vantagem**: Invisibilidade e dano aumentado em ataques surpresa

### **4. ADAPTIVE AI JUNGLE** 🤖

#### **Sistema de Aprendizado Adaptativo**
O sistema de IA da selva utiliza machine learning para analisar padrões de comportamento dos jogadores e adaptar dinamicamente o ambiente de jogo:

- **Análise de Padrões**: Monitora comportamentos individuais e estratégias de equipe
- **Adaptação de Spawns**: Ajusta dificuldade e timing dos camps baseado nos padrões de clear
- **Objetivos Dinâmicos**: Cria contra-objetivos quando detecta foco excessivo na selva
- **Previsão Estratégica**: Antecipa estratégias baseadas na composição de equipe e histórico

#### **Adaptive Elements**
- **Camp Spawns**: Baseado em clear patterns
- **Objective Timing**: Adaptado ao ritmo da partida
- **Creature Behavior**: "Lembram" de encontros anteriores
- **Reward Scaling**: Balanceamento dinâmico baseado em performance

### **5. PROCEDURAL OBJECTIVES** 🎲

#### **Sistema de Geração Dinâmica**
Os objetivos são gerados proceduralmente baseados no estado atual da partida:

- **Análise de Estado**: Monitora tempo de jogo, diferença de kills e ouro entre equipes
- **Objetivos de Recuperação**: Spawnam quando uma equipe está significativamente atrás
- **Recompensas de Agressão**: Incentivam combate precoce e ativo
- **Forçadores de Engajamento**: Criam situações que obrigam team fights quando o jogo está muito passivo

#### **Tipos de Objetivos Procedurais**
1. **Nexus Fragments**: Scattered mini-objectives que buildam para major buff
2. **Temporal Rifts**: Permite "rewind" de 10 segundos em área específica
3. **Realm Anchors**: Controlam qual realm está ativo
4. **Fusion Catalysts**: Reduzem cooldown de Sígilos Auracron
5. **Vertical Bridges**: Conectam temporariamente as layers

#### **Categorização**
- **Core**: Sempre presentes
- **Catch-up**: Ativados quando uma equipe está >10% atrás em ouro/kills

### **6. SISTEMA DE LORE DINÂMICO**

1. **Fragmentos de Crônica** – dropam após marcos (1ª torre, 1ª fusão, etc.); coletados ➜ revelam trechos de história no **Codex Nexus**
2. **Vínculos Ocultos** – duplas de heróis concedem fala + buff de 2% velocidade por 8s (ex.: Irmãos Astria). Válido apenas se estiverem a ≤900u
3. **Missões de Temporada** – desafios semanais (Ex.: Caçar 5 Relíquias Etéreas) que liberam variante de cor de skin
4. **Eco de Fusão** – primeira fusão de par específico grava memória no Codex e concede ícone exclusivo

### **7. TERMINOLOGIA PADRONIZADA**

| Antigo termo | Novo termo Nexus |
|--------------|-----------------|
| Lane | Trilho |
| Brush | Canopy |
| Ward | Baliza |
| River | Fluxo |
| Baron/Herald | Guardião Prismal / Núcleo de Tempestade |
| Dragon | Leviatã Umbrático |

---

## 🔧 **SISTEMAS TÉCNICOS**

### **Engine & Frameworks**

#### **🛠️ TECH STACK**

**Core Engine: Unreal Engine 5.6**
- **Lumen**: Sistema de iluminação global dinâmica
- **Nanite**: Geometria virtualizada para alta fidelidade
- **Chaos Physics**: Sistema de física avançado
- **MetaHuman**: Criação de personagens realistas

**Backend Services**
- **Unreal Engine Multiplayer**: Servidor autoritativo
- **Firebase**: Dados de usuário e analytics
- **Epic Online Services**: Funcionalidades cross-platform
- **Vivox**: Comunicação por voz

**Integração de Plataforma**
- **Google Play Games**: Conquistas Android
- **Game Center**: Conquistas iOS
- **Steam**: Distribuição PC

**Analytics & Balanceamento**
- **Unreal Analytics**: Comportamento de jogadores
- **Telemetria Customizada**: Dados de balanceamento
- **Framework A/B Testing**: Testes de funcionalidades

### **Performance Targets**
| **Platform** | **FPS** | **Resolution** | **Memory** | **Storage** |
|--------------|---------|----------------|------------|-------------|
| **Flagship Mobile** | 60 FPS | 1080p+ | <4GB RAM | <8GB |
| **Mid-range Mobile** | 30 FPS | 720p | <3GB RAM | <6GB |
| **PC** | 120 FPS | 1440p+ | <8GB RAM | <15GB |

### **Arquitetura de Rede**
- **Servidor Autoritativo**: Previne cheating através de validação server-side
- **Validação de Ações**: Todas as ações de jogador são verificadas antes da aplicação
- **Previsão Client-Side**: Reduz latência percebida através de predição local
- **Sincronização de Estado**: Snapshots regulares mantêm consistência entre clientes
- **Detecção de Atividade Suspeita**: Sistema de logging para identificar possíveis cheats

---

## 💰 **PROGRESSÃO E MONETIZAÇÃO**

### **Modelo de Monetização Ética**

#### **Battle Pass Evoluído**

**🎁 ADAPTIVE BATTLE PASS:**
- **Traditional Track**: Progressão linear padrão
- **Role-Specific Tracks**: Trilhas específicas por função (Tank, DPS, Support, Jungle, Mid)
- **Playstyle Tracks**: Trilhas por estilo de jogo (Agressivo, Defensivo, Estratégico)
- **Community Tracks**: Desbloqueadas através de objetivos comunitários

**Exemplo de Funcionamento:**
Jogador que atua principalmente como Support desbloqueia a Support Track:
- Skins exclusivas para campeões de suporte
- Customizações de Baliza
- Variações de VFX para cura e escudo
- Emotes e voice lines específicos de suporte

#### **Champion Acquisition**
- **Free Rotation**: 15 champions/week (vs 10 do Wild Rift)
- **Earn Rate**: 1 novo champion/semana jogando casual
- **Currency**: Blue Essence (earned) + Realm Crystals (premium)
- **No P2W**: Champions purchasable apenas com earned currency

#### **Cosmetics Premium**

**✨ FUNCTIONAL COSMETICS:**

**Champion Skins**
- Alterações de modelo
- Customização de VFX
- Variações de voice pack
- Alterações na aparência dos Sígilos

**Map Themes (Votação Comunitária)**
- Aparências sazonais dos realms
- Efeitos climáticos
- Pacotes de som ambiente

**Elementos Customizáveis**
- Cores de partículas de habilidades
- Animações de recall
- Celebrações de vitória/derrota

### **Progression Systems**

#### **Account Level (1-500)**

**MARCOS DE PROGRESSÃO:**
- **Level 10**: Desbloqueio do modo ranqueado
- **Level 25**: Desbloqueio dos Sígilos Auracron
- **Level 50**: Rastreamento de maestria de realm
- **Level 100**: Criação de lobbies customizados
- **Level 200**: Privilégios de beta tester
- **Level 500**: Status lendário + recompensas únicas

#### **Champion Mastery (1-10)**

**PROGRESSÃO POR CAMPEÃO:**
- **Maestria 1-3**: Recompensas cosméticas básicas
- **Maestria 4-6**: Chromas avançados de skin
- **Maestria 7-8**: Emotes e animações exclusivos
- **Maestria 9-10**: Título de campeão + borda
- **Maestria 10**: Nomes customizados de habilidades + recompensas raras

#### **Realm Mastery (Sistema Novo)**

**PROGRESSÃO ESPECÍFICA POR REALM:**

**Terrestrial Realm - Expertise em Combate Terrestre**
- Bônus de XP na fase de lanes
- Eficiência no clear da selva
- Prioridade em objetivos terrestres

**Celestial Realm - Maestria em Combate Aéreo**
- Bônus de dano na camada celestial
- Consciência de posicionamento vertical
- Sinergia com unidades voadoras

**Abyssal Realm - Expertise Subterrânea**
- Raio de detecção de stealth
- Velocidade de navegação em túneis
- Multiplicador de dano de emboscada

---

## 📈 **ROADMAP DE DESENVOLVIMENTO**

### **FASE 0: POC (Meses 1-3)**

**🔬 PROOF OF CONCEPT:**
- ✅ Setup do Unreal Engine 5.6
- ⏳ Grey-box de 1 lane vertical
- ⏳ 6 campeões básicos
- ⏳ Perfil de CPU/GPU
- ⏳ Networking básico

**DELIVERABLES:**
- Protótipo jogável vertical slice
- Performance baseline estabelecida
- Validação técnica das inovações core

### **FASE 1: VERTICAL SLICE (Meses 4-8)**

**🏗️ CORE SYSTEMS:**
- ⏳ Transição Terra→Céu
- ⏳ Fusão Tank+Mage (Sigilo Aegis)
- ⏳ Tutorial completo
- ⏳ Arquitetura do sistema de campeões
- ⏳ UI/UX básico
- ⏳ Protótipo de realm único

**DELIVERABLES:**
- Vertical slice jogável (1 realm, 5v5)
- Habilidades core dos campeões funcionando
- Sistema básico de progressão
- Build alpha para testes internos

### **FASE 2: CLOSED BETA (Meses 9-15)**

**🚀 UNIQUE FEATURES:**
- ⏳ Realm Abyssal
- ⏳ Sistema de honra
- ⏳ 20 campeões
- ⏳ Soft-launch regional
- ⏳ IA adaptativa da selva
- ⏳ Objetivos procedurais
- ⏳ Networking avançado

**DELIVERABLES:**
- Todas as inovações core implementadas
- Sígilos em todos os 20 campeões
- Closed beta com 1000 jogadores
- Sistema de coleta de dados de balanceamento

### **FASE 3: LAUNCH (Meses 16-18)**

**⚡ FINALIZAÇÃO:**
- ⏳ Monetização ética
- ⏳ Modo espectador
- ⏳ 50+ campeões
- ⏳ Campanha de marketing
- ⏳ Otimização de performance
- ⏳ Refinamento de UI/UX
- ⏳ Otimização para plataforma mobile
- ⏳ Sistemas anti-cheat
- ⏳ Sistema de tutorial abrangente

**DELIVERABLES:**
- Build pronto para produção
- 50+ campeões no lançamento
- Lançamento global (soft launch → mundial)
- Ferramenta de telemetria para Sígilos

### **FASE 4: POST-LAUNCH (Meses 19+)**

**🔄 LIVE SERVICE:**
- ⏳ Atualizações de conteúdo sazonal
- ⏳ Introdução de novos realms
- ⏳ Infraestrutura de esports
- ⏳ Funcionalidades comunitárias
- ⏳ Expansão de plataforma (PC, Console)

**DELIVERABLES:**
- Atualizações principais trimestrais
- Lançamentos mensais de campeões
- Expansões anuais de realm
- Estabelecimento da cena competitiva

---

## ⚠️ **ANÁLISE DE RISCOS**

### **RISCOS TÉCNICOS**

#### **🔴 ALTO RISCO**
1. **Complexidade 3D em Mobile**
   - **Problema**: Performance em devices low-end
   - **Solução**: Scalable quality settings, 2D fallback mode
   - **Mitigation**: Extensive device testing, cloud gaming option

2. **Networking Complexity**
   - **Problema**: 3D positioning sync, realm transitions
   - **Solução**: Predictive networking, authoritative server
   - **Mitigation**: Dedicated server infrastructure, regional servers

#### **🟡 MÉDIO RISCO**
1. **AI Jungle Balance**
   - **Problema**: IA muito forte/fraca, exploits
   - **Solução**: Extensive playtesting, gradual learning
   - **Mitigation**: Manual override system, community feedback

2. **Sigilo Balance**
   - **Problema**: Combinations OP, meta stagnation
   - **Solução**: Data-driven nerfs/buffs, rotation restrictions
   - **Mitigation**: Regular balance patches, pro player input

### **RISCOS DE MERCADO**

#### **🔴 ALTO RISCO**
1. **Competição com Wild Rift**
   - **Problema**: Brand recognition, established playerbase
   - **Solução**: Focus em inovação, influencer partnerships
   - **Mitigation**: Unique value proposition, superior tech

2. **Monetização Sustentável**
   - **Problema**: F2P market saturation, whale dependency
   - **Solução**: Ethical monetization, broad appeal cosmetics
   - **Mitigation**: Multiple revenue streams, community support

#### **🟡 MÉDIO RISCO**
1. **Player Adoption**
   - **Problema**: Learning curve das inovações
   - **Solução**: Gradual feature introduction, excellent tutorials
   - **Mitigation**: Progressive complexity, casual modes

---

## 🎯 **MÉTRICAS DE SUCESSO**

### **KPIs Pré-Launch**
- **Alpha Retention**: >40% D7, >20% D30
- **Beta Feedback Score**: >4.2/5.0
- **Performance**: 60fps em 80% dos devices testados
- **Bug Reports**: <5 critical bugs per build

### **KPIs Post-Launch**
- **DAU**: 100K+ em 6 meses
- **Player Retention**: >30% D30, >10% D90
- **Revenue**: $1M+ revenue em ano 1
- **Community**: 50K+ Discord members, 100K+ Reddit

### **KPIs Long-term**
- **Esports**: Tournament com $100K+ prize pool
- **Global Reach**: Lançamento em 10+ países
- **Platform Expansion**: PC/Console versions
- **Brand Recognition**: Top 10 mobile MOBA rankings

---

## 📞 **PRÓXIMOS PASSOS**

### **IMEDIATOS (1-2 semanas)**
1. **📋 Refinar este documento** baseado em feedback
2. **👥 Formar core team** (Lead Designer, Tech Lead, Artist Lead)
3. **💰 Preparar pitch deck** para investidores/publishers
4. **🔬 Research aprofundado** de mercado e competição

### **CURTO PRAZO (1-3 meses)**
1. **🎮 Protótipo vertical** de realm transition
2. **🤖 PoC do sistema de IA** adaptativa
3. **📊 Validar interesse** via surveys/focus groups
4. **🏢 Buscar funding** inicial para desenvolvimento

### **MÉDIO PRAZO (3-6 meses)**
1. **⚡ Alpha build** com todas as inovações core
2. **🧪 Playtesting** extensivo com target audience
3. **📈 Iteração** baseada em feedback e dados
4. **🎨 Finalizar art direction** e brand identity

### **Próximos Passos Imediatos Específicos**
1. Prototipar sub-zona de transição "Portal Preview"
2. Implementar protótipo de AI Mentor usando Unreal Engine 5.6
3. Ajustar GDD de habilidades para arquétipos de Sígilos reduzidos

---

## 📝 **CONCLUSÃO**

**AURACRON** representa uma evolução natural do gênero MOBA, mantendo a acessibilidade que tornou Wild Rift popular enquanto introduz inovações significativas que podem redefinir o competitive gaming mobile.

**Diferenciais Únicos:**
- ✅ Mapas dinâmicos que evoluem durante a partida
- ✅ Combate 3D vertical em plataforma mobile
- ✅ IA adaptativa que aprende e desafia players
- ✅ Sistema de Sígilos que recompensa expressão individual
- ✅ Objetivos procedurais únicos a cada match
- ✅ Terminologia própria que cria identidade única
- ✅ Sistema de lore dinâmico integrado ao gameplay

**Desafios Principais:**
- ⚠️ Complexidade técnica vs performance mobile
- ⚠️ Balanceamento de mecânicas inovadoras
- ⚠️ Competição com IPs estabelecidos
- ⚠️ Sustainable monetization em mercado saturado

**Potencial de Sucesso:**
Com execução adequada e recursos suficientes, **AURACRON** tem potencial para se tornar o "next-gen MOBA" que a comunidade busca, oferecendo profundidade estratégica sem sacrificar acessibilidade.

### **Onboarding & Acessibilidade**
- **Progressive Onboarding**: tutorial de 5 min na Surface Layer → PVE "Academy Realm" → primeira partida real com IA fraca
- **AI Mentor (Navigator Drone)**: NPC de voz/ícones que responde dúvidas e faz *reboarding* de jogadores ausentes
- **DDA Light**: ajuste dinâmico de dano recebido nos 10 primeiros níveis de conta

### **Comunidade Saudável**
- Chat/voice **opt-in desativado** até nível 10
- **Bônus de honra coletiva**: todo o time recebe +5% XP se ninguém for mutado/reportado
- Detecção em tempo real de linguagem tóxica ⇒ silenciamento automático 15 min

---

## 📚 **HISTÓRICO DE ATUALIZAÇÕES**

### 🔄 **Atualização 29/06/2025 – Refinamentos de Design**
> Resultado de pesquisa interna + benchmarking (Inworld AI, Riot UX, BBC GEL) e discussões de equipe.

#### 1. Essência do Produto
- **Mapa Dinâmico ✔️** continua sendo o diferencial, mas agora as transições de realm começam como "sub-zonas" experimentais antes da mudança global (reduz sobrecarga cognitiva)
- **Fusão de Campeões 🔧** passa a ter apenas 3 arquétipos por temporada (ex.: Tank + Mage, Assassin + Support, ADC + Jungle) para simplificar balanceamento
- **Objetivos Procedurais 🎲** divididos em "Core" (sempre presentes) e "Catch-up" (ativados quando uma equipe está >10% atrás em ouro/kills)

### 🔄 **Atualização 30/06/2025 – Identidade de Mapas & Narrativa**
> Esta revisão elimina nomenclaturas populares de outros MOBAs e aprofunda as mecânicas de lore in-game.

- **Planície Radiante** → trilhos Solar / Axis / Lunar, vegetação *Canopy*, balizas de visão
- **Firmamento Zephyr** → Núcleo de Tempestade, Santuários dos Ventos
- **Abismo Umbrio** → Leviatã Umbrático, Altares da Sombra
- Terminologia padronizada: Trilho, Canopy, Baliza, Fluxo, Guardião Prismal
- Sistema de **Fragmentos de Crônica** + **Vínculos Ocultos** + **Eco de Fusão** integrado ao Codex

### 🔄 **Atualização 05/07/2025 – Rebrand & Fusion 2.0 (Sigil System)**

#### 1. Rebrand Global
- Codename/brand oficial passa a ser **AURACRON**; referências a "Nexus Realms" ficam como nome de engine/lore interno
- Tagline de marketing: _"Domine as três camadas. Desperte o Auracron."_
- Domínios reservados: auracron.com / auracron.gg / auracron.game

#### 2. Fusion 2.0 → Sistema de **SÍGILOS**
- A fusão deixa de exigir dois jogadores
- Durante a **tela de seleção de campeões**, cada player escolhe **1 de 3 "Sígilos Auracron"** (Tank, Damage, Utility)
- O Sigilo funde-se ao campeão aos 6 min, desbloqueando árvore de habilidades alternativa
- Pode ser re-forjado no Nexus uma vez por partida (cooldown global de 2 min)
- Cria combinatória de 50 campeões × 3 Sígilos = 150 arquétipos sem depender de cooperação específica

### 🔄 **Atualização Janeiro 2025 – Documento Unificado**
> Unificação completa dos documentos de design, migração para Unreal Engine 5.6 e refinamentos finais.

- Migração de Unity 6.2 para **Unreal Engine 5.6**
- Unificação de todos os elementos dos documentos anteriores
- Refinamento da terminologia e mecânicas
- Estruturação final do roadmap de desenvolvimento
- Consolidação das métricas de sucesso e análise de riscos

---

*Este documento é um living document e será atualizado conforme o desenvolvimento do conceito e feedback da equipe.*

**Nota importante**: Toda documentação futura deve utilizar a nomenclatura "Sígilos Auracron" ao invés de "Champion Fusion" e seguir a terminologia padronizada estabelecida neste documento.