// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGElementLibraryExtended.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGElementLibraryExtended() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointTransformerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointTransformerSettings_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMeshEntry();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UAuracronPCGPointTransformerSettings *************************************
void UAuracronPCGPointTransformerSettings::StaticRegisterNativesUAuracronPCGPointTransformerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings;
UClass* UAuracronPCGPointTransformerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPointTransformerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPointTransformerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPointTransformerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPointTransformerSettings_NoRegister()
{
	return UAuracronPCGPointTransformerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibraryExtended.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTransformPosition_MetaData[] = {
		{ "Category", "Transform Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transform modes\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transform modes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTransformRotation_MetaData[] = {
		{ "Category", "Transform Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTransformScale_MetaData[] = {
		{ "Category", "Transform Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionOffset_MetaData[] = {
		{ "Category", "Position Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Position transformation\n" },
#endif
		{ "EditCondition", "bTransformPosition" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Position transformation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionMin_MetaData[] = {
		{ "Category", "Position Transform" },
		{ "EditCondition", "bTransformPosition" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionMax_MetaData[] = {
		{ "Category", "Position Transform" },
		{ "EditCondition", "bTransformPosition" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationOffset_MetaData[] = {
		{ "Category", "Rotation Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rotation transformation\n" },
#endif
		{ "EditCondition", "bTransformRotation" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rotation transformation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationMin_MetaData[] = {
		{ "Category", "Rotation Transform" },
		{ "EditCondition", "bTransformRotation" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationMax_MetaData[] = {
		{ "Category", "Rotation Transform" },
		{ "EditCondition", "bTransformRotation" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAbsoluteRotation_MetaData[] = {
		{ "Category", "Rotation Transform" },
		{ "EditCondition", "bTransformRotation" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleOffset_MetaData[] = {
		{ "Category", "Scale Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Scale transformation\n" },
#endif
		{ "EditCondition", "bTransformScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Scale transformation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMin_MetaData[] = {
		{ "Category", "Scale Transform" },
		{ "EditCondition", "bTransformScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMax_MetaData[] = {
		{ "Category", "Scale Transform" },
		{ "EditCondition", "bTransformScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUniformScale_MetaData[] = {
		{ "Category", "Scale Transform" },
		{ "EditCondition", "bTransformScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNoiseTransform_MetaData[] = {
		{ "Category", "Noise Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise-based transformation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise-based transformation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise Transform" },
		{ "EditCondition", "bUseNoiseTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise Transform" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.001" },
		{ "EditCondition", "bUseNoiseTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseIntensity_MetaData[] = {
		{ "Category", "Noise Transform" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "EditCondition", "bUseNoiseTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bTransformPosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTransformPosition;
	static void NewProp_bTransformRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTransformRotation;
	static void NewProp_bTransformScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTransformScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionMin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionMax;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotationOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotationMin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotationMax;
	static void NewProp_bAbsoluteRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAbsoluteRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleMin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleMax;
	static void NewProp_bUniformScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUniformScale;
	static void NewProp_bUseNoiseTransform_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNoiseTransform;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPointTransformerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformPosition_SetBit(void* Obj)
{
	((UAuracronPCGPointTransformerSettings*)Obj)->bTransformPosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformPosition = { "bTransformPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformPosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTransformPosition_MetaData), NewProp_bTransformPosition_MetaData) };
void Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformRotation_SetBit(void* Obj)
{
	((UAuracronPCGPointTransformerSettings*)Obj)->bTransformRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformRotation = { "bTransformRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTransformRotation_MetaData), NewProp_bTransformRotation_MetaData) };
void Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformScale_SetBit(void* Obj)
{
	((UAuracronPCGPointTransformerSettings*)Obj)->bTransformScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformScale = { "bTransformScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTransformScale_MetaData), NewProp_bTransformScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_PositionOffset = { "PositionOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, PositionOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionOffset_MetaData), NewProp_PositionOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_PositionMin = { "PositionMin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, PositionMin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionMin_MetaData), NewProp_PositionMin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_PositionMax = { "PositionMax", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, PositionMax), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionMax_MetaData), NewProp_PositionMax_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_RotationOffset = { "RotationOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, RotationOffset), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationOffset_MetaData), NewProp_RotationOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_RotationMin = { "RotationMin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, RotationMin), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationMin_MetaData), NewProp_RotationMin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_RotationMax = { "RotationMax", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, RotationMax), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationMax_MetaData), NewProp_RotationMax_MetaData) };
void Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bAbsoluteRotation_SetBit(void* Obj)
{
	((UAuracronPCGPointTransformerSettings*)Obj)->bAbsoluteRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bAbsoluteRotation = { "bAbsoluteRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bAbsoluteRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAbsoluteRotation_MetaData), NewProp_bAbsoluteRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_ScaleOffset = { "ScaleOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, ScaleOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleOffset_MetaData), NewProp_ScaleOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_ScaleMin = { "ScaleMin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, ScaleMin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMin_MetaData), NewProp_ScaleMin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_ScaleMax = { "ScaleMax", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, ScaleMax), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMax_MetaData), NewProp_ScaleMax_MetaData) };
void Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUniformScale_SetBit(void* Obj)
{
	((UAuracronPCGPointTransformerSettings*)Obj)->bUniformScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUniformScale = { "bUniformScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUniformScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUniformScale_MetaData), NewProp_bUniformScale_MetaData) };
void Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUseNoiseTransform_SetBit(void* Obj)
{
	((UAuracronPCGPointTransformerSettings*)Obj)->bUseNoiseTransform = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUseNoiseTransform = { "bUseNoiseTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUseNoiseTransform_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNoiseTransform_MetaData), NewProp_bUseNoiseTransform_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, NoiseType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 2308707076
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseIntensity = { "NoiseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointTransformerSettings, NoiseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseIntensity_MetaData), NewProp_NoiseIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bTransformScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_PositionOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_PositionMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_PositionMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_RotationOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_RotationMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_RotationMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bAbsoluteRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_ScaleOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_ScaleMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_ScaleMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUniformScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_bUseNoiseTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::NewProp_NoiseIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::ClassParams = {
	&UAuracronPCGPointTransformerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPointTransformerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPointTransformerSettings);
UAuracronPCGPointTransformerSettings::~UAuracronPCGPointTransformerSettings() {}
// ********** End Class UAuracronPCGPointTransformerSettings ***************************************

// ********** Begin ScriptStruct FAuracronPCGMeshEntry *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry;
class UScriptStruct* FAuracronPCGMeshEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGMeshEntry, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGMeshEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mesh_MetaData[] = {
		{ "Category", "Mesh Entry" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weight_MetaData[] = {
		{ "Category", "Mesh Entry" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMultiplier_MetaData[] = {
		{ "Category", "Mesh Entry" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialOverrides_MetaData[] = {
		{ "Category", "Mesh Entry" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCollision_MetaData[] = {
		{ "Category", "Mesh Entry" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastShadows_MetaData[] = {
		{ "Category", "Mesh Entry" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleMultiplier;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialOverrides_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialOverrides;
	static void NewProp_bUseCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCollision;
	static void NewProp_bCastShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastShadows;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGMeshEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshEntry, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mesh_MetaData), NewProp_Mesh_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_Weight = { "Weight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshEntry, Weight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weight_MetaData), NewProp_Weight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_ScaleMultiplier = { "ScaleMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshEntry, ScaleMultiplier), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMultiplier_MetaData), NewProp_ScaleMultiplier_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_MaterialOverrides_Inner = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_MaterialOverrides = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMeshEntry, MaterialOverrides), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialOverrides_MetaData), NewProp_MaterialOverrides_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bUseCollision_SetBit(void* Obj)
{
	((FAuracronPCGMeshEntry*)Obj)->bUseCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bUseCollision = { "bUseCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMeshEntry), &Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bUseCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCollision_MetaData), NewProp_bUseCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bCastShadows_SetBit(void* Obj)
{
	((FAuracronPCGMeshEntry*)Obj)->bCastShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bCastShadows = { "bCastShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMeshEntry), &Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bCastShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastShadows_MetaData), NewProp_bCastShadows_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_Weight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_ScaleMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_MaterialOverrides_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_MaterialOverrides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bUseCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewProp_bCastShadows,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGMeshEntry",
	Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::PropPointers),
	sizeof(FAuracronPCGMeshEntry),
	alignof(FAuracronPCGMeshEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMeshEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGMeshEntry ***********************************************

// ********** Begin Class UAuracronPCGAdvancedMeshSpawnerSettings **********************************
void UAuracronPCGAdvancedMeshSpawnerSettings::StaticRegisterNativesUAuracronPCGAdvancedMeshSpawnerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings;
UClass* UAuracronPCGAdvancedMeshSpawnerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedMeshSpawnerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedMeshSpawnerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedMeshSpawnerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_NoRegister()
{
	return UAuracronPCGAdvancedMeshSpawnerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibraryExtended.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshEntries_MetaData[] = {
		{ "Category", "Mesh Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh entries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh entries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnProbability_MetaData[] = {
		{ "Category", "Spawning Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spawning settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawning settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToSurface_MetaData[] = {
		{ "Category", "Spawning Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bScaleByDensity_MetaData[] = {
		{ "Category", "Spawning Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLOD_MetaData[] = {
		{ "Category", "LOD Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "LOD Settings" },
		{ "EditCondition", "bUseLOD" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODMeshes_MetaData[] = {
		{ "Category", "LOD Settings" },
		{ "EditCondition", "bUseLOD" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCulling_MetaData[] = {
		{ "Category", "Culling Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Culling Settings" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "100.0" },
		{ "EditCondition", "bUseCulling" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseInstancing_MetaData[] = {
		{ "Category", "Instance Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerComponent_MetaData[] = {
		{ "Category", "Instance Settings" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "1" },
		{ "EditCondition", "bUseInstancing" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshEntries;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnProbability;
	static void NewProp_bAlignToSurface_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToSurface;
	static void NewProp_bScaleByDensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bScaleByDensity;
	static void NewProp_bUseLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LODMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODMeshes;
	static void NewProp_bUseCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static void NewProp_bUseInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseInstancing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedMeshSpawnerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_MeshEntries_Inner = { "MeshEntries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMeshEntry, METADATA_PARAMS(0, nullptr) }; // 492763619
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_MeshEntries = { "MeshEntries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMeshSpawnerSettings, MeshEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshEntries_MetaData), NewProp_MeshEntries_MetaData) }; // 492763619
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_SpawnProbability = { "SpawnProbability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMeshSpawnerSettings, SpawnProbability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnProbability_MetaData), NewProp_SpawnProbability_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bAlignToSurface_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMeshSpawnerSettings*)Obj)->bAlignToSurface = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bAlignToSurface = { "bAlignToSurface", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bAlignToSurface_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToSurface_MetaData), NewProp_bAlignToSurface_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bScaleByDensity_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMeshSpawnerSettings*)Obj)->bScaleByDensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bScaleByDensity = { "bScaleByDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bScaleByDensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bScaleByDensity_MetaData), NewProp_bScaleByDensity_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseLOD_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMeshSpawnerSettings*)Obj)->bUseLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseLOD = { "bUseLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLOD_MetaData), NewProp_bUseLOD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMeshSpawnerSettings, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODMeshes_Inner = { "LODMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODMeshes = { "LODMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMeshSpawnerSettings, LODMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODMeshes_MetaData), NewProp_LODMeshes_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseCulling_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMeshSpawnerSettings*)Obj)->bUseCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseCulling = { "bUseCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCulling_MetaData), NewProp_bUseCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMeshSpawnerSettings, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseInstancing_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMeshSpawnerSettings*)Obj)->bUseInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseInstancing = { "bUseInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMeshSpawnerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseInstancing_MetaData), NewProp_bUseInstancing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_MaxInstancesPerComponent = { "MaxInstancesPerComponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMeshSpawnerSettings, MaxInstancesPerComponent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerComponent_MetaData), NewProp_MaxInstancesPerComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_MeshEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_MeshEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_SpawnProbability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bAlignToSurface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bScaleByDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_LODMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_bUseInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::NewProp_MaxInstancesPerComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedMeshSpawnerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedMeshSpawnerSettings);
UAuracronPCGAdvancedMeshSpawnerSettings::~UAuracronPCGAdvancedMeshSpawnerSettings() {}
// ********** End Class UAuracronPCGAdvancedMeshSpawnerSettings ************************************

// ********** Begin Enum EAuracronPCGAttributeOperation ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation;
static UEnum* EAuracronPCGAttributeOperation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAttributeOperation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeOperation>()
{
	return EAuracronPCGAttributeOperation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Add.DisplayName", "Add" },
		{ "Add.Name", "EAuracronPCGAttributeOperation::Add" },
		{ "BlueprintType", "true" },
		{ "Clamp.DisplayName", "Clamp" },
		{ "Clamp.Name", "EAuracronPCGAttributeOperation::Clamp" },
		{ "Invert.DisplayName", "Invert" },
		{ "Invert.Name", "EAuracronPCGAttributeOperation::Invert" },
		{ "Lerp.DisplayName", "Lerp" },
		{ "Lerp.Name", "EAuracronPCGAttributeOperation::Lerp" },
		{ "Max.DisplayName", "Max" },
		{ "Max.Name", "EAuracronPCGAttributeOperation::Max" },
		{ "Min.DisplayName", "Min" },
		{ "Min.Name", "EAuracronPCGAttributeOperation::Min" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "EAuracronPCGAttributeOperation::Multiply" },
		{ "Normalize.DisplayName", "Normalize" },
		{ "Normalize.Name", "EAuracronPCGAttributeOperation::Normalize" },
		{ "Set.DisplayName", "Set" },
		{ "Set.Name", "EAuracronPCGAttributeOperation::Set" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAttributeOperation::Set", (int64)EAuracronPCGAttributeOperation::Set },
		{ "EAuracronPCGAttributeOperation::Add", (int64)EAuracronPCGAttributeOperation::Add },
		{ "EAuracronPCGAttributeOperation::Multiply", (int64)EAuracronPCGAttributeOperation::Multiply },
		{ "EAuracronPCGAttributeOperation::Min", (int64)EAuracronPCGAttributeOperation::Min },
		{ "EAuracronPCGAttributeOperation::Max", (int64)EAuracronPCGAttributeOperation::Max },
		{ "EAuracronPCGAttributeOperation::Lerp", (int64)EAuracronPCGAttributeOperation::Lerp },
		{ "EAuracronPCGAttributeOperation::Clamp", (int64)EAuracronPCGAttributeOperation::Clamp },
		{ "EAuracronPCGAttributeOperation::Normalize", (int64)EAuracronPCGAttributeOperation::Normalize },
		{ "EAuracronPCGAttributeOperation::Invert", (int64)EAuracronPCGAttributeOperation::Invert },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAttributeOperation",
	"EAuracronPCGAttributeOperation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation.InnerSingleton;
}
// ********** End Enum EAuracronPCGAttributeOperation **********************************************

// ********** Begin Class UAuracronPCGExtendedAttributeModifierSettings ****************************
void UAuracronPCGExtendedAttributeModifierSettings::StaticRegisterNativesUAuracronPCGExtendedAttributeModifierSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings;
UClass* UAuracronPCGExtendedAttributeModifierSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGExtendedAttributeModifierSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGExtendedAttributeModifierSettings"),
			Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGExtendedAttributeModifierSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_NoRegister()
{
	return UAuracronPCGExtendedAttributeModifierSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibraryExtended.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "Category", "Attribute Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Operation_MetaData[] = {
		{ "Category", "Attribute Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "Category", "Attribute Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorValue_MetaData[] = {
		{ "Category", "Attribute Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVectorValue_MetaData[] = {
		{ "Category", "Attribute Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinValue_MetaData[] = {
		{ "Category", "Range Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Range settings (for clamp and lerp operations)\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Range settings (for clamp and lerp operations)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValue_MetaData[] = {
		{ "Category", "Range Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNoise_MetaData[] = {
		{ "Category", "Noise Modulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise modulation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise modulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise Modulation" },
		{ "EditCondition", "bUseNoise" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise Modulation" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.001" },
		{ "EditCondition", "bUseNoise" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseInfluence_MetaData[] = {
		{ "Category", "Noise Modulation" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "EditCondition", "bUseNoise" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Operation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Operation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorValue;
	static void NewProp_bUseVectorValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVectorValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static void NewProp_bUseNoise_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNoise;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseInfluence;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGExtendedAttributeModifierSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_Operation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_Operation = { "Operation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, Operation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeOperation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Operation_MetaData), NewProp_Operation_MetaData) }; // 2352391035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_VectorValue = { "VectorValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, VectorValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorValue_MetaData), NewProp_VectorValue_MetaData) };
void Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseVectorValue_SetBit(void* Obj)
{
	((UAuracronPCGExtendedAttributeModifierSettings*)Obj)->bUseVectorValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseVectorValue = { "bUseVectorValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExtendedAttributeModifierSettings), &Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseVectorValue_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVectorValue_MetaData), NewProp_bUseVectorValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_MinValue = { "MinValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, MinValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinValue_MetaData), NewProp_MinValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, MaxValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValue_MetaData), NewProp_MaxValue_MetaData) };
void Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseNoise_SetBit(void* Obj)
{
	((UAuracronPCGExtendedAttributeModifierSettings*)Obj)->bUseNoise = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseNoise = { "bUseNoise", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExtendedAttributeModifierSettings), &Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseNoise_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNoise_MetaData), NewProp_bUseNoise_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, NoiseType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 2308707076
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseInfluence = { "NoiseInfluence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExtendedAttributeModifierSettings, NoiseInfluence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseInfluence_MetaData), NewProp_NoiseInfluence_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_Operation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_Operation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_VectorValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseVectorValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_MinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_bUseNoise,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::NewProp_NoiseInfluence,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::ClassParams = {
	&UAuracronPCGExtendedAttributeModifierSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings.OuterSingleton;
}
UAuracronPCGExtendedAttributeModifierSettings::UAuracronPCGExtendedAttributeModifierSettings() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGExtendedAttributeModifierSettings);
UAuracronPCGExtendedAttributeModifierSettings::~UAuracronPCGExtendedAttributeModifierSettings() {}
// ********** End Class UAuracronPCGExtendedAttributeModifierSettings ******************************

// ********** Begin Enum EAuracronPCGDebugVisualizationMode ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode;
static UEnum* EAuracronPCGDebugVisualizationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGDebugVisualizationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDebugVisualizationMode>()
{
	return EAuracronPCGDebugVisualizationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Attributes.DisplayName", "Attributes" },
		{ "Attributes.Name", "EAuracronPCGDebugVisualizationMode::Attributes" },
		{ "BlueprintType", "true" },
		{ "Bounds.DisplayName", "Bounds" },
		{ "Bounds.Name", "EAuracronPCGDebugVisualizationMode::Bounds" },
		{ "Connections.DisplayName", "Connections" },
		{ "Connections.Name", "EAuracronPCGDebugVisualizationMode::Connections" },
		{ "Density.DisplayName", "Density" },
		{ "Density.Name", "EAuracronPCGDebugVisualizationMode::Density" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
		{ "Normals.DisplayName", "Normals" },
		{ "Normals.Name", "EAuracronPCGDebugVisualizationMode::Normals" },
		{ "Points.DisplayName", "Points" },
		{ "Points.Name", "EAuracronPCGDebugVisualizationMode::Points" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGDebugVisualizationMode::Points", (int64)EAuracronPCGDebugVisualizationMode::Points },
		{ "EAuracronPCGDebugVisualizationMode::Bounds", (int64)EAuracronPCGDebugVisualizationMode::Bounds },
		{ "EAuracronPCGDebugVisualizationMode::Normals", (int64)EAuracronPCGDebugVisualizationMode::Normals },
		{ "EAuracronPCGDebugVisualizationMode::Density", (int64)EAuracronPCGDebugVisualizationMode::Density },
		{ "EAuracronPCGDebugVisualizationMode::Attributes", (int64)EAuracronPCGDebugVisualizationMode::Attributes },
		{ "EAuracronPCGDebugVisualizationMode::Connections", (int64)EAuracronPCGDebugVisualizationMode::Connections },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGDebugVisualizationMode",
	"EAuracronPCGDebugVisualizationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGDebugVisualizationMode ******************************************

// ********** Begin Class UAuracronPCGDebugVisualizerSettings **************************************
void UAuracronPCGDebugVisualizerSettings::StaticRegisterNativesUAuracronPCGDebugVisualizerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings;
UClass* UAuracronPCGDebugVisualizerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGDebugVisualizerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGDebugVisualizerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGDebugVisualizerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_NoRegister()
{
	return UAuracronPCGDebugVisualizerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibraryExtended.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualizationMode_MetaData[] = {
		{ "Category", "Visualization Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visualization settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visualization settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInEditor_MetaData[] = {
		{ "Category", "Visualization Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInGame_MetaData[] = {
		{ "Category", "Visualization Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebugColor_MetaData[] = {
		{ "Category", "Visualization Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebugSize_MetaData[] = {
		{ "Category", "Visualization Settings" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeToVisualize_MetaData[] = {
		{ "Category", "Attribute Visualization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute visualization\n" },
#endif
		{ "EditCondition", "VisualizationMode == EAuracronPCGDebugVisualizationMode::Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute visualization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinColor_MetaData[] = {
		{ "Category", "Attribute Visualization" },
		{ "EditCondition", "VisualizationMode == EAuracronPCGDebugVisualizationMode::Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxColor_MetaData[] = {
		{ "Category", "Attribute Visualization" },
		{ "EditCondition", "VisualizationMode == EAuracronPCGDebugVisualizationMode::Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPointsToVisualize_MetaData[] = {
		{ "Category", "Performance Settings" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualizationDuration_MetaData[] = {
		{ "Category", "Performance Settings" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibraryExtended.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VisualizationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VisualizationMode;
	static void NewProp_bShowInEditor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInEditor;
	static void NewProp_bShowInGame_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInGame;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DebugColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DebugSize;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeToVisualize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MinColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaxColor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPointsToVisualize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisualizationDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGDebugVisualizerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_VisualizationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_VisualizationMode = { "VisualizationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, VisualizationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDebugVisualizationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualizationMode_MetaData), NewProp_VisualizationMode_MetaData) }; // 1908839244
void Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInEditor_SetBit(void* Obj)
{
	((UAuracronPCGDebugVisualizerSettings*)Obj)->bShowInEditor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInEditor = { "bShowInEditor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDebugVisualizerSettings), &Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInEditor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInEditor_MetaData), NewProp_bShowInEditor_MetaData) };
void Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInGame_SetBit(void* Obj)
{
	((UAuracronPCGDebugVisualizerSettings*)Obj)->bShowInGame = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInGame = { "bShowInGame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDebugVisualizerSettings), &Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInGame_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInGame_MetaData), NewProp_bShowInGame_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_DebugColor = { "DebugColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, DebugColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebugColor_MetaData), NewProp_DebugColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_DebugSize = { "DebugSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, DebugSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebugSize_MetaData), NewProp_DebugSize_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_AttributeToVisualize = { "AttributeToVisualize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, AttributeToVisualize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeToVisualize_MetaData), NewProp_AttributeToVisualize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_MinColor = { "MinColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, MinColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinColor_MetaData), NewProp_MinColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_MaxColor = { "MaxColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, MaxColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxColor_MetaData), NewProp_MaxColor_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_MaxPointsToVisualize = { "MaxPointsToVisualize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, MaxPointsToVisualize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPointsToVisualize_MetaData), NewProp_MaxPointsToVisualize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_VisualizationDuration = { "VisualizationDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDebugVisualizerSettings, VisualizationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualizationDuration_MetaData), NewProp_VisualizationDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_VisualizationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_VisualizationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInEditor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_bShowInGame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_DebugColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_DebugSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_AttributeToVisualize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_MinColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_MaxColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_MaxPointsToVisualize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::NewProp_VisualizationDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::ClassParams = {
	&UAuracronPCGDebugVisualizerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGDebugVisualizerSettings);
UAuracronPCGDebugVisualizerSettings::~UAuracronPCGDebugVisualizerSettings() {}
// ********** End Class UAuracronPCGDebugVisualizerSettings ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGAttributeOperation_StaticEnum, TEXT("EAuracronPCGAttributeOperation"), &Z_Registration_Info_UEnum_EAuracronPCGAttributeOperation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2352391035U) },
		{ EAuracronPCGDebugVisualizationMode_StaticEnum, TEXT("EAuracronPCGDebugVisualizationMode"), &Z_Registration_Info_UEnum_EAuracronPCGDebugVisualizationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1908839244U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGMeshEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics::NewStructOps, TEXT("AuracronPCGMeshEntry"), &Z_Registration_Info_UScriptStruct_FAuracronPCGMeshEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGMeshEntry), 492763619U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGPointTransformerSettings, UAuracronPCGPointTransformerSettings::StaticClass, TEXT("UAuracronPCGPointTransformerSettings"), &Z_Registration_Info_UClass_UAuracronPCGPointTransformerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPointTransformerSettings), 2487224746U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings, UAuracronPCGAdvancedMeshSpawnerSettings::StaticClass, TEXT("UAuracronPCGAdvancedMeshSpawnerSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedMeshSpawnerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedMeshSpawnerSettings), 730753215U) },
		{ Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings, UAuracronPCGExtendedAttributeModifierSettings::StaticClass, TEXT("UAuracronPCGExtendedAttributeModifierSettings"), &Z_Registration_Info_UClass_UAuracronPCGExtendedAttributeModifierSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGExtendedAttributeModifierSettings), 4082659474U) },
		{ Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings, UAuracronPCGDebugVisualizerSettings::StaticClass, TEXT("UAuracronPCGDebugVisualizerSettings"), &Z_Registration_Info_UClass_UAuracronPCGDebugVisualizerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGDebugVisualizerSettings), 2019000499U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_2178372011(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
