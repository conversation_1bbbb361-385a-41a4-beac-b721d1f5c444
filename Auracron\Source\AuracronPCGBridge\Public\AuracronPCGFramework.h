// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Main Module Header
// Bridge 2.1: PCG Framework - Core Infrastructure

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Logging/LogMacros.h"
#include "Stats/Stats.h"

// PCG Framework includes for UE5.6
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGContext.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "PCGParamData.h"
#include "PCGSubsystem.h"
#include "Elements/PCGExecuteBlueprint.h"
#include "Elements/PCGStaticMeshSpawner.h"
#include "Elements/PCGSurfaceSampler.h"
#include "Elements/PCGTransformPoints.h"

// Engine includes
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"
#include "GameFramework/Actor.h"
#include "UObject/ObjectMacros.h"
#include "UObject/UObjectGlobals.h"

// Editor includes
#if WITH_EDITOR
#include "Editor.h"
#include "EditorModeManager.h"
#include "LevelEditor.h"
#include "Framework/Commands/UICommandList.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Widgets/Docking/SDockTab.h"
#include "WorkspaceMenuStructure.h"
#include "WorkspaceMenuStructureModule.h"
#endif

// Forward declarations
class UAuracronPCGManager;
class UAuracronPCGElementBase;
class UAuracronPCGGeneratorBase;
class FAuracronPCGLogger;

// Module API macro
#ifndef AURACRONPCGFRAMEWORK_API
#define AURACRONPCGFRAMEWORK_API DLLEXPORT
#endif

// Logging categories
DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPCG, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPCGCore, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPCGElements, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPCGGenerators, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPCGPerformance, Log, All);

// Stats categories
DECLARE_STATS_GROUP(TEXT("AuracronPCG"), STATGROUP_AuracronPCG, STATCAT_Advanced);
DECLARE_CYCLE_STAT_EXTERN(TEXT("PCG Element Execution"), STAT_AuracronPCG_ElementExecution, STATGROUP_AuracronPCG, AURACRONPCGFRAMEWORK_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("PCG Graph Generation"), STAT_AuracronPCG_GraphGeneration, STATGROUP_AuracronPCG, AURACRONPCGFRAMEWORK_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("PCG Point Processing"), STAT_AuracronPCG_PointProcessing, STATGROUP_AuracronPCG, AURACRONPCGFRAMEWORK_API);
DECLARE_DWORD_COUNTER_STAT_EXTERN(TEXT("PCG Points Generated"), STAT_AuracronPCG_PointsGenerated, STATGROUP_AuracronPCG, AURACRONPCGFRAMEWORK_API);
DECLARE_MEMORY_STAT_EXTERN(TEXT("PCG Memory Usage"), STAT_AuracronPCG_MemoryUsage, STATGROUP_AuracronPCG, AURACRONPCGFRAMEWORK_API);

// Version information
#define AURACRON_PCG_VERSION_MAJOR 2
#define AURACRON_PCG_VERSION_MINOR 1
#define AURACRON_PCG_VERSION_PATCH 0
#define AURACRON_PCG_VERSION_STRING TEXT("2.1.0")

// Configuration constants
namespace AuracronPCGConfig
{
    // Performance settings
    constexpr int32 DefaultThreadCount = 4;
    constexpr int32 MaxPointsPerBatch = 10000;
    constexpr int32 DefaultMemoryPoolSizeMB = 512;
    
    // Quality settings
    constexpr float DefaultPointDensity = 1.0f;
    constexpr float MinPointDensity = 0.001f;
    constexpr float MaxPointDensity = 100.0f;
    
    // Timing settings
    constexpr float DefaultTimeoutSeconds = 30.0f;
    constexpr float MaxExecutionTimeSeconds = 300.0f;
    
    // Debug settings
    constexpr bool DefaultEnableDebugVisualization = true;
    constexpr bool DefaultEnablePerformanceLogging = true;
}

// Error codes for PCG operations
UENUM(BlueprintType)
enum class EAuracronPCGErrorCode : uint8
{
    None                    UMETA(DisplayName = "No Error"),
    InvalidInput           UMETA(DisplayName = "Invalid Input"),
    InvalidGraph           UMETA(DisplayName = "Invalid Graph"),
    InvalidElement         UMETA(DisplayName = "Invalid Element"),
    ExecutionTimeout       UMETA(DisplayName = "Execution Timeout"),
    MemoryAllocationFailed UMETA(DisplayName = "Memory Allocation Failed"),
    InvalidPointData       UMETA(DisplayName = "Invalid Point Data"),
    InvalidSpatialData     UMETA(DisplayName = "Invalid Spatial Data"),
    InvalidMetadata        UMETA(DisplayName = "Invalid Metadata"),
    GenerationFailed       UMETA(DisplayName = "Generation Failed"),
    UnknownError          UMETA(DisplayName = "Unknown Error")
};

// PCG execution modes
UENUM(BlueprintType)
enum class EAuracronPCGExecutionMode : uint8
{
    Synchronous     UMETA(DisplayName = "Synchronous"),
    Asynchronous    UMETA(DisplayName = "Asynchronous"),
    Threaded        UMETA(DisplayName = "Threaded"),
    GPU             UMETA(DisplayName = "GPU Accelerated")
};

// PCG quality levels
UENUM(BlueprintType)
enum class EAuracronPCGQualityLevel : uint8
{
    Low         UMETA(DisplayName = "Low Quality"),
    Medium      UMETA(DisplayName = "Medium Quality"),
    High        UMETA(DisplayName = "High Quality"),
    Ultra       UMETA(DisplayName = "Ultra Quality"),
    Custom      UMETA(DisplayName = "Custom Quality")
};

// PCG generation types
UENUM(BlueprintType)
enum class EAuracronPCGGenerationType : uint8
{
    Points          UMETA(DisplayName = "Point Generation"),
    Meshes          UMETA(DisplayName = "Mesh Generation"),
    Landscapes      UMETA(DisplayName = "Landscape Generation"),
    Foliage         UMETA(DisplayName = "Foliage Generation"),
    Buildings       UMETA(DisplayName = "Building Generation"),
    Roads           UMETA(DisplayName = "Road Generation"),
    Terrain         UMETA(DisplayName = "Terrain Generation"),
    Biomes          UMETA(DisplayName = "Biome Generation"),
    Custom          UMETA(DisplayName = "Custom Generation")
};

// Error information structure
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGErrorInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    EAuracronPCGErrorCode ErrorCode = EAuracronPCGErrorCode::None;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString ErrorContext;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FDateTime Timestamp;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString SourceElement;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    int32 LineNumber = 0;

    FAuracronPCGErrorInfo()
    {
        Timestamp = FDateTime::Now();
    }

    FAuracronPCGErrorInfo(EAuracronPCGErrorCode InErrorCode, const FString& InErrorMessage, const FString& InErrorContext = TEXT(""))
        : ErrorCode(InErrorCode)
        , ErrorMessage(InErrorMessage)
        , ErrorContext(InErrorContext)
        , Timestamp(FDateTime::Now())
    {
    }
};

// Performance metrics structure
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGPerformanceMetrics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float ExecutionTimeSeconds = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 PointsGenerated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 ElementsExecuted = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float CPUUsagePercent = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float GPUUsagePercent = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 ThreadsUsed = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    FDateTime StartTime;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    FDateTime EndTime;

    FAuracronPCGPerformanceMetrics()
    {
        StartTime = FDateTime::Now();
        EndTime = StartTime;
    }
};

// PCG configuration structure
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    EAuracronPCGExecutionMode ExecutionMode = EAuracronPCGExecutionMode::Synchronous;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronPCGQualityLevel QualityLevel = EAuracronPCGQualityLevel::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ThreadCount = AuracronPCGConfig::DefaultThreadCount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxPointsPerBatch = AuracronPCGConfig::MaxPointsPerBatch;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MemoryPoolSizeMB = AuracronPCGConfig::DefaultMemoryPoolSizeMB;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float TimeoutSeconds = AuracronPCGConfig::DefaultTimeoutSeconds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDebugVisualization = AuracronPCGConfig::DefaultEnableDebugVisualization;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnablePerformanceLogging = AuracronPCGConfig::DefaultEnablePerformanceLogging;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDetailedLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU")
    bool bEnableGPUAcceleration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU")
    bool bPreferGPUProcessing = false;
};

/**
 * Main module class for the AURACRON PCG Framework
 * Provides core infrastructure and management for procedural content generation
 */
class AURACRONPCGFRAMEWORK_API FAuracronPCGFrameworkModule : public IModuleInterface
{
public:
    // IModuleInterface implementation
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    // Module management
    static FAuracronPCGFrameworkModule& Get();
    static bool IsAvailable();

    // Core functionality
    UAuracronPCGManager* GetPCGManager() const { return PCGManager; }
    FAuracronPCGLogger* GetLogger() const { return Logger.Get(); }

    // Configuration management
    const FAuracronPCGConfiguration& GetConfiguration() const { return Configuration; }
    void SetConfiguration(const FAuracronPCGConfiguration& NewConfiguration);

    // Performance monitoring
    const FAuracronPCGPerformanceMetrics& GetPerformanceMetrics() const { return PerformanceMetrics; }
    void ResetPerformanceMetrics();

    // Error handling
    void ReportError(const FAuracronPCGErrorInfo& ErrorInfo);
    TArray<FAuracronPCGErrorInfo> GetErrorHistory(int32 MaxEntries = 10) const;
    void ClearErrorHistory();

    // Version information
    FString GetVersionString() const { return AURACRON_PCG_VERSION_STRING; }
    void GetVersionNumbers(int32& Major, int32& Minor, int32& Patch) const;

private:
    // Core components
    UPROPERTY()
    UAuracronPCGManager* PCGManager;

    TUniquePtr<FAuracronPCGLogger> Logger;

    // Configuration and state
    FAuracronPCGConfiguration Configuration;
    FAuracronPCGPerformanceMetrics PerformanceMetrics;
    TArray<FAuracronPCGErrorInfo> ErrorHistory;

    // Internal methods
    void InitializeCore();
    void InitializeEditor();
    void ShutdownCore();
    void ShutdownEditor();

#if WITH_EDITOR
    // Editor integration
    void RegisterMenuExtensions();
    void UnregisterMenuExtensions();
    TSharedRef<SDockTab> SpawnPCGTab(const FSpawnTabArgs& Args);
    
    TSharedPtr<FUICommandList> CommandList;
    static const FName PCGTabName;
#endif
};

// Convenience macros for logging
#define AURACRON_PCG_LOG(Verbosity, Format, ...) \
    UE_LOG(LogAuracronPCG, Verbosity, Format, ##__VA_ARGS__)

#define AURACRON_PCG_LOG_CORE(Verbosity, Format, ...) \
    UE_LOG(LogAuracronPCGCore, Verbosity, Format, ##__VA_ARGS__)

#define AURACRON_PCG_LOG_ELEMENTS(Verbosity, Format, ...) \
    UE_LOG(LogAuracronPCGElements, Verbosity, Format, ##__VA_ARGS__)

#define AURACRON_PCG_LOG_GENERATORS(Verbosity, Format, ...) \
    UE_LOG(LogAuracronPCGGenerators, Verbosity, Format, ##__VA_ARGS__)

#define AURACRON_PCG_LOG_PERFORMANCE(Verbosity, Format, ...) \
    UE_LOG(LogAuracronPCGPerformance, Verbosity, Format, ##__VA_ARGS__)

// Convenience macros for stats
#define AURACRON_PCG_SCOPE_CYCLE_COUNTER(StatId) \
    SCOPE_CYCLE_COUNTER(StatId)

#define AURACRON_PCG_SET_DWORD_STAT(StatId, Value) \
    SET_DWORD_STAT(StatId, Value)

#define AURACRON_PCG_INC_DWORD_STAT(StatId) \
    INC_DWORD_STAT(StatId)

#define AURACRON_PCG_SET_MEMORY_STAT(StatId, Value) \
    SET_MEMORY_STAT(StatId, Value)
