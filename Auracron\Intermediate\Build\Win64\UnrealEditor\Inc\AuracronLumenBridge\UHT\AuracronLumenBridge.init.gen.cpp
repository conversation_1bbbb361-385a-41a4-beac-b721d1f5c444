// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronLumenBridge_init() {}
	AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature();
	AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature();
	AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature();
	AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronLumenBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronLumenBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronLumenBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronLumenBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x81E4007E,
				0x9D817531,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronLumenBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronLumenBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronLumenBridge(Z_Construct_UPackage__Script_AuracronLumenBridge, TEXT("/Script/AuracronLumenBridge"), Z_Registration_Info_UPackage__Script_AuracronLumenBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x81E4007E, 0x9D817531));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
