// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGMeshGeneration.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGMeshGeneration_generated_h
#error "AuracronPCGMeshGeneration.generated.h already included, missing '#pragma once' in AuracronPCGMeshGeneration.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGMeshGeneration_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPCGMetadata;
class UStaticMesh;
enum class EAuracronPCGCollisionMode : uint8;
enum class EAuracronPCGMeshCombineMode : uint8;
enum class EAuracronPCGMeshSelectionMode : uint8;
struct FAuracronPCGMeshEntry;
struct FAuracronPCGProceduralMeshDescriptor;
struct FPCGPoint;

// ********** Begin ScriptStruct FAuracronPCGMeshGenerationEntry ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_114_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGMeshGenerationEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGMeshGenerationEntry;
// ********** End ScriptStruct FAuracronPCGMeshGenerationEntry *************************************

// ********** Begin ScriptStruct FAuracronPCGProceduralMeshDescriptor ******************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_186_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGProceduralMeshDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGProceduralMeshDescriptor;
// ********** End ScriptStruct FAuracronPCGProceduralMeshDescriptor ********************************

// ********** Begin Class UAuracronPCGAdvancedStaticMeshSpawnerSettings ****************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_239_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedStaticMeshSpawnerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedStaticMeshSpawnerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedStaticMeshSpawnerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedStaticMeshSpawnerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_239_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedStaticMeshSpawnerSettings(UAuracronPCGAdvancedStaticMeshSpawnerSettings&&) = delete; \
	UAuracronPCGAdvancedStaticMeshSpawnerSettings(const UAuracronPCGAdvancedStaticMeshSpawnerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedStaticMeshSpawnerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedStaticMeshSpawnerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedStaticMeshSpawnerSettings) \
	NO_API virtual ~UAuracronPCGAdvancedStaticMeshSpawnerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_236_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_239_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_239_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_239_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedStaticMeshSpawnerSettings;

// ********** End Class UAuracronPCGAdvancedStaticMeshSpawnerSettings ******************************

// ********** Begin Class UAuracronPCGInstancedMeshGeneratorSettings *******************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_317_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGInstancedMeshGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGInstancedMeshGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGInstancedMeshGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGInstancedMeshGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_317_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGInstancedMeshGeneratorSettings(UAuracronPCGInstancedMeshGeneratorSettings&&) = delete; \
	UAuracronPCGInstancedMeshGeneratorSettings(const UAuracronPCGInstancedMeshGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGInstancedMeshGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGInstancedMeshGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGInstancedMeshGeneratorSettings) \
	NO_API virtual ~UAuracronPCGInstancedMeshGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_314_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_317_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_317_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_317_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGInstancedMeshGeneratorSettings;

// ********** End Class UAuracronPCGInstancedMeshGeneratorSettings *********************************

// ********** Begin Class UAuracronPCGProceduralMeshCreatorSettings ********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_391_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGProceduralMeshCreatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGProceduralMeshCreatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGProceduralMeshCreatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGProceduralMeshCreatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_391_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGProceduralMeshCreatorSettings(UAuracronPCGProceduralMeshCreatorSettings&&) = delete; \
	UAuracronPCGProceduralMeshCreatorSettings(const UAuracronPCGProceduralMeshCreatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGProceduralMeshCreatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGProceduralMeshCreatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGProceduralMeshCreatorSettings) \
	NO_API virtual ~UAuracronPCGProceduralMeshCreatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_388_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_391_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_391_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_391_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGProceduralMeshCreatorSettings;

// ********** End Class UAuracronPCGProceduralMeshCreatorSettings **********************************

// ********** Begin Class UAuracronPCGMeshCombinerSettings *****************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_492_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGMeshCombinerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGMeshCombinerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGMeshCombinerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGMeshCombinerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_492_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGMeshCombinerSettings(UAuracronPCGMeshCombinerSettings&&) = delete; \
	UAuracronPCGMeshCombinerSettings(const UAuracronPCGMeshCombinerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGMeshCombinerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGMeshCombinerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGMeshCombinerSettings) \
	NO_API virtual ~UAuracronPCGMeshCombinerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_489_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_492_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_492_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_492_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGMeshCombinerSettings;

// ********** End Class UAuracronPCGMeshCombinerSettings *******************************************

// ********** Begin Class UAuracronPCGMeshGenerationUtils ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_575_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execShouldUseHierarchicalInstancing); \
	DECLARE_FUNCTION(execGetOptimalInstanceCount); \
	DECLARE_FUNCTION(execGenerateCollision); \
	DECLARE_FUNCTION(execGenerateLODs); \
	DECLARE_FUNCTION(execCombineMeshes); \
	DECLARE_FUNCTION(execGenerateTangents); \
	DECLARE_FUNCTION(execGenerateUVs); \
	DECLARE_FUNCTION(execGenerateNormals); \
	DECLARE_FUNCTION(execOptimizeMesh); \
	DECLARE_FUNCTION(execGeneratePlaneMesh); \
	DECLARE_FUNCTION(execGenerateCylinderMesh); \
	DECLARE_FUNCTION(execGenerateSphereMesh); \
	DECLARE_FUNCTION(execGenerateBoxMesh); \
	DECLARE_FUNCTION(execValidateMeshEntry); \
	DECLARE_FUNCTION(execSelectMeshIndex);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_575_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGMeshGenerationUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGMeshGenerationUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGMeshGenerationUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGMeshGenerationUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_575_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGMeshGenerationUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGMeshGenerationUtils(UAuracronPCGMeshGenerationUtils&&) = delete; \
	UAuracronPCGMeshGenerationUtils(const UAuracronPCGMeshGenerationUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGMeshGenerationUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGMeshGenerationUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGMeshGenerationUtils) \
	NO_API virtual ~UAuracronPCGMeshGenerationUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_572_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_575_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_575_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_575_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h_575_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGMeshGenerationUtils;

// ********** End Class UAuracronPCGMeshGenerationUtils ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMeshGeneration_h

// ********** Begin Enum EAuracronPCGMeshGenerationType ********************************************
#define FOREACH_ENUM_EAURACRONPCGMESHGENERATIONTYPE(op) \
	op(EAuracronPCGMeshGenerationType::StaticMesh) \
	op(EAuracronPCGMeshGenerationType::InstancedMesh) \
	op(EAuracronPCGMeshGenerationType::HierarchicalMesh) \
	op(EAuracronPCGMeshGenerationType::ProceduralMesh) \
	op(EAuracronPCGMeshGenerationType::SplineMesh) \
	op(EAuracronPCGMeshGenerationType::SkeletalMesh) 

enum class EAuracronPCGMeshGenerationType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMeshGenerationType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshGenerationType>();
// ********** End Enum EAuracronPCGMeshGenerationType **********************************************

// ********** Begin Enum EAuracronPCGMeshSelectionMode *********************************************
#define FOREACH_ENUM_EAURACRONPCGMESHSELECTIONMODE(op) \
	op(EAuracronPCGMeshSelectionMode::Random) \
	op(EAuracronPCGMeshSelectionMode::ByAttribute) \
	op(EAuracronPCGMeshSelectionMode::ByDensity) \
	op(EAuracronPCGMeshSelectionMode::ByDistance) \
	op(EAuracronPCGMeshSelectionMode::ByNormal) \
	op(EAuracronPCGMeshSelectionMode::Sequential) \
	op(EAuracronPCGMeshSelectionMode::Weighted) \
	op(EAuracronPCGMeshSelectionMode::Custom) 

enum class EAuracronPCGMeshSelectionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMeshSelectionMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshSelectionMode>();
// ********** End Enum EAuracronPCGMeshSelectionMode ***********************************************

// ********** Begin Enum EAuracronPCGMeshCombineMode ***********************************************
#define FOREACH_ENUM_EAURACRONPCGMESHCOMBINEMODE(op) \
	op(EAuracronPCGMeshCombineMode::Merge) \
	op(EAuracronPCGMeshCombineMode::Union) \
	op(EAuracronPCGMeshCombineMode::Subtract) \
	op(EAuracronPCGMeshCombineMode::Intersect) \
	op(EAuracronPCGMeshCombineMode::Append) \
	op(EAuracronPCGMeshCombineMode::Weld) \
	op(EAuracronPCGMeshCombineMode::Simplify) 

enum class EAuracronPCGMeshCombineMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMeshCombineMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshCombineMode>();
// ********** End Enum EAuracronPCGMeshCombineMode *************************************************

// ********** Begin Enum EAuracronPCGMeshLODGenerationMode *****************************************
#define FOREACH_ENUM_EAURACRONPCGMESHLODGENERATIONMODE(op) \
	op(EAuracronPCGMeshLODGenerationMode::None) \
	op(EAuracronPCGMeshLODGenerationMode::Automatic) \
	op(EAuracronPCGMeshLODGenerationMode::Distance) \
	op(EAuracronPCGMeshLODGenerationMode::Density) \
	op(EAuracronPCGMeshLODGenerationMode::Custom) 

enum class EAuracronPCGMeshLODGenerationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMeshLODGenerationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshLODGenerationMode>();
// ********** End Enum EAuracronPCGMeshLODGenerationMode *******************************************

// ********** Begin Enum EAuracronPCGCollisionMode *************************************************
#define FOREACH_ENUM_EAURACRONPCGCOLLISIONMODE(op) \
	op(EAuracronPCGCollisionMode::None) \
	op(EAuracronPCGCollisionMode::Simple) \
	op(EAuracronPCGCollisionMode::Complex) \
	op(EAuracronPCGCollisionMode::ConvexHull) \
	op(EAuracronPCGCollisionMode::BoundingBox) \
	op(EAuracronPCGCollisionMode::BoundingSphere) \
	op(EAuracronPCGCollisionMode::Custom) 

enum class EAuracronPCGCollisionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCollisionMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCollisionMode>();
// ********** End Enum EAuracronPCGCollisionMode ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
