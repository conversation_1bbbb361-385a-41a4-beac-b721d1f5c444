// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Noise System Utilities Implementation
// Bridge 2.9: PCG Framework - Noise e Randomization

#include "AuracronPCGNoiseSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// =============================================================================
// NOISE SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

float UAuracronPCGNoiseSystemUtils::GenerateNoise(const FVector& Position, const FAuracronPCGNoiseDescriptor& NoiseDescriptor)
{
    float NoiseValue = 0.0f;

    switch (NoiseDescriptor.NoiseType)
    {
        case EAuracronPCGNoiseType::Perlin:
            NoiseValue = GeneratePerlinNoise(Position, NoiseDescriptor.Frequency, NoiseDescriptor.Octaves, NoiseDescriptor.Persistence, NoiseDescriptor.Seed);
            break;
        case EAuracronPCGNoiseType::Simplex:
            NoiseValue = GenerateSimplexNoise(Position, NoiseDescriptor.Frequency, NoiseDescriptor.Octaves, NoiseDescriptor.Persistence, NoiseDescriptor.Seed);
            break;
        case EAuracronPCGNoiseType::Worley:
            NoiseValue = GenerateWorleyNoise(Position, NoiseDescriptor.Frequency, NoiseDescriptor.DistanceFunction, NoiseDescriptor.Jitter, NoiseDescriptor.Seed);
            break;
        case EAuracronPCGNoiseType::Ridge:
            NoiseValue = GenerateRidgeNoise(Position, NoiseDescriptor.Frequency, NoiseDescriptor.Octaves, NoiseDescriptor.Persistence, NoiseDescriptor.Seed);
            break;
        case EAuracronPCGNoiseType::Value:
            NoiseValue = AuracronPCGNoiseSystemUtils::ValueNoise3D(Position.X * NoiseDescriptor.Frequency, Position.Y * NoiseDescriptor.Frequency, Position.Z * NoiseDescriptor.Frequency, NoiseDescriptor.Seed);
            break;
        case EAuracronPCGNoiseType::Gradient:
            NoiseValue = AuracronPCGNoiseSystemUtils::GradientNoise3D(Position.X * NoiseDescriptor.Frequency, Position.Y * NoiseDescriptor.Frequency, Position.Z * NoiseDescriptor.Frequency, NoiseDescriptor.Seed);
            break;
        default:
            NoiseValue = GeneratePerlinNoise(Position, NoiseDescriptor.Frequency, NoiseDescriptor.Octaves, NoiseDescriptor.Persistence, NoiseDescriptor.Seed);
            break;
    }

    // Apply fractal if specified
    if (NoiseDescriptor.FractalType != EAuracronPCGFractalType::None)
    {
        NoiseValue = AuracronPCGNoiseSystemUtils::ApplyFractal(NoiseValue, NoiseDescriptor.FractalType, NoiseDescriptor);
    }

    // Apply amplitude
    NoiseValue *= NoiseDescriptor.Amplitude;

    return NoiseValue;
}

FVector UAuracronPCGNoiseSystemUtils::GenerateVectorNoise(const FVector& Position, const FAuracronPCGNoiseDescriptor& NoiseDescriptorX, const FAuracronPCGNoiseDescriptor& NoiseDescriptorY, const FAuracronPCGNoiseDescriptor& NoiseDescriptorZ)
{
    float NoiseX = GenerateNoise(Position, NoiseDescriptorX);
    float NoiseY = GenerateNoise(Position + FVector(1000.0f, 0.0f, 0.0f), NoiseDescriptorY);
    float NoiseZ = GenerateNoise(Position + FVector(0.0f, 1000.0f, 0.0f), NoiseDescriptorZ);
    
    return FVector(NoiseX, NoiseY, NoiseZ);
}

float UAuracronPCGNoiseSystemUtils::GeneratePerlinNoise(const FVector& Position, float Frequency, int32 Octaves, float Persistence, int32 Seed)
{
    float NoiseValue = 0.0f;
    float Amplitude = 1.0f;
    float MaxValue = 0.0f;
    float CurrentFrequency = Frequency;

    for (int32 i = 0; i < Octaves; i++)
    {
        NoiseValue += AuracronPCGNoiseSystemUtils::PerlinNoise3D(
            Position.X * CurrentFrequency, 
            Position.Y * CurrentFrequency, 
            Position.Z * CurrentFrequency, 
            Seed + i) * Amplitude;
        
        MaxValue += Amplitude;
        Amplitude *= Persistence;
        CurrentFrequency *= 2.0f;
    }

    return NoiseValue / MaxValue;
}

float UAuracronPCGNoiseSystemUtils::GenerateSimplexNoise(const FVector& Position, float Frequency, int32 Octaves, float Persistence, int32 Seed)
{
    float NoiseValue = 0.0f;
    float Amplitude = 1.0f;
    float MaxValue = 0.0f;
    float CurrentFrequency = Frequency;

    for (int32 i = 0; i < Octaves; i++)
    {
        NoiseValue += AuracronPCGNoiseSystemUtils::SimplexNoise3D(
            Position.X * CurrentFrequency, 
            Position.Y * CurrentFrequency, 
            Position.Z * CurrentFrequency, 
            Seed + i) * Amplitude;
        
        MaxValue += Amplitude;
        Amplitude *= Persistence;
        CurrentFrequency *= 2.0f;
    }

    return NoiseValue / MaxValue;
}

float UAuracronPCGNoiseSystemUtils::GenerateWorleyNoise(const FVector& Position, float Frequency, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 Seed)
{
    return AuracronPCGNoiseSystemUtils::WorleyNoise3D(
        Position.X * Frequency, 
        Position.Y * Frequency, 
        Position.Z * Frequency, 
        DistanceFunction, 
        Jitter, 
        Seed);
}

float UAuracronPCGNoiseSystemUtils::GenerateRidgeNoise(const FVector& Position, float Frequency, int32 Octaves, float Persistence, int32 Seed)
{
    float NoiseValue = 0.0f;
    float Amplitude = 1.0f;
    float MaxValue = 0.0f;
    float CurrentFrequency = Frequency;

    for (int32 i = 0; i < Octaves; i++)
    {
        float OctaveNoise = AuracronPCGNoiseSystemUtils::PerlinNoise3D(
            Position.X * CurrentFrequency, 
            Position.Y * CurrentFrequency, 
            Position.Z * CurrentFrequency, 
            Seed + i);
        
        // Ridge noise is created by taking the absolute value and inverting
        OctaveNoise = 1.0f - FMath::Abs(OctaveNoise);
        NoiseValue += OctaveNoise * Amplitude;
        
        MaxValue += Amplitude;
        Amplitude *= Persistence;
        CurrentFrequency *= 2.0f;
    }

    return NoiseValue / MaxValue;
}

float UAuracronPCGNoiseSystemUtils::GenerateRandomFloat(int32 Seed, float Min, float Max)
{
    FRandomStream RandomStream(Seed);
    return RandomStream.FRandRange(Min, Max);
}

FVector UAuracronPCGNoiseSystemUtils::GenerateRandomVector(int32 Seed, const FVector& Min, const FVector& Max)
{
    FRandomStream RandomStream(Seed);
    return FVector(
        RandomStream.FRandRange(Min.X, Max.X),
        RandomStream.FRandRange(Min.Y, Max.Y),
        RandomStream.FRandRange(Min.Z, Max.Z)
    );
}

int32 UAuracronPCGNoiseSystemUtils::GenerateRandomInt(int32 Seed, int32 Min, int32 Max)
{
    FRandomStream RandomStream(Seed);
    return RandomStream.RandRange(Min, Max);
}

float UAuracronPCGNoiseSystemUtils::GenerateGaussianRandom(int32 Seed, float Mean, float StdDev)
{
    FRandomStream RandomStream(Seed);
    
    // Box-Muller transform for Gaussian distribution
    static bool bHasSpare = false;
    static float Spare;
    
    if (bHasSpare)
    {
        bHasSpare = false;
        return Spare * StdDev + Mean;
    }
    
    bHasSpare = true;
    float u = RandomStream.FRand();
    float v = RandomStream.FRand();
    float mag = StdDev * FMath::Sqrt(-2.0f * FMath::Loge(u));
    Spare = mag * FMath::Cos(2.0f * PI * v);
    
    return mag * FMath::Sin(2.0f * PI * v) + Mean;
}

float UAuracronPCGNoiseSystemUtils::CombineNoiseValues(const TArray<float>& NoiseValues, const TArray<float>& Weights, EAuracronPCGNoiseCombineMode CombineMode)
{
    if (NoiseValues.Num() == 0)
    {
        return 0.0f;
    }

    if (NoiseValues.Num() == 1)
    {
        return NoiseValues[0];
    }

    float Result = NoiseValues[0];
    float Weight0 = Weights.Num() > 0 ? Weights[0] : 1.0f;

    for (int32 i = 1; i < NoiseValues.Num(); i++)
    {
        float NoiseValue = NoiseValues[i];
        float Weight = Weights.Num() > i ? Weights[i] : 1.0f;
        
        switch (CombineMode)
        {
            case EAuracronPCGNoiseCombineMode::Add:
                Result += NoiseValue * Weight;
                break;
            case EAuracronPCGNoiseCombineMode::Subtract:
                Result -= NoiseValue * Weight;
                break;
            case EAuracronPCGNoiseCombineMode::Multiply:
                Result *= NoiseValue * Weight;
                break;
            case EAuracronPCGNoiseCombineMode::Divide:
                if (FMath::Abs(NoiseValue * Weight) > SMALL_NUMBER)
                {
                    Result /= NoiseValue * Weight;
                }
                break;
            case EAuracronPCGNoiseCombineMode::Min:
                Result = FMath::Min(Result, NoiseValue * Weight);
                break;
            case EAuracronPCGNoiseCombineMode::Max:
                Result = FMath::Max(Result, NoiseValue * Weight);
                break;
            case EAuracronPCGNoiseCombineMode::Average:
                Result = (Result + NoiseValue * Weight) * 0.5f;
                break;
            case EAuracronPCGNoiseCombineMode::Screen:
                Result = 1.0f - (1.0f - Result) * (1.0f - NoiseValue * Weight);
                break;
            case EAuracronPCGNoiseCombineMode::Overlay:
                if (Result < 0.5f)
                {
                    Result = 2.0f * Result * NoiseValue * Weight;
                }
                else
                {
                    Result = 1.0f - 2.0f * (1.0f - Result) * (1.0f - NoiseValue * Weight);
                }
                break;
            default:
                Result += NoiseValue * Weight;
                break;
        }
    }

    return Result;
}

float UAuracronPCGNoiseSystemUtils::ApplyNoiseMask(float NoiseValue, float MaskValue, bool bInvertMask)
{
    float Mask = bInvertMask ? (1.0f - MaskValue) : MaskValue;
    return NoiseValue * Mask;
}

float UAuracronPCGNoiseSystemUtils::RemapNoiseValue(float NoiseValue, float InputMin, float InputMax, float OutputMin, float OutputMax)
{
    float NormalizedValue = (NoiseValue - InputMin) / (InputMax - InputMin);
    return FMath::Lerp(OutputMin, OutputMax, NormalizedValue);
}

float UAuracronPCGNoiseSystemUtils::ApplyNoiseInterpolation(float Value, EAuracronPCGNoiseInterpolation InterpolationType)
{
    switch (InterpolationType)
    {
        case EAuracronPCGNoiseInterpolation::Linear:
            return Value;
        case EAuracronPCGNoiseInterpolation::Hermite:
            return AuracronPCGNoiseSystemUtils::InterpolateHermite(0.0f, 1.0f, Value);
        case EAuracronPCGNoiseInterpolation::Quintic:
            return AuracronPCGNoiseSystemUtils::InterpolateQuintic(0.0f, 1.0f, Value);
        case EAuracronPCGNoiseInterpolation::Cosine:
            return AuracronPCGNoiseSystemUtils::InterpolateCosine(0.0f, 1.0f, Value);
        case EAuracronPCGNoiseInterpolation::Smoothstep:
            return FMath::SmoothStep(0.0f, 1.0f, Value);
        case EAuracronPCGNoiseInterpolation::Smootherstep:
            return Value * Value * Value * (Value * (Value * 6.0f - 15.0f) + 10.0f);
        default:
            return Value;
    }
}

bool UAuracronPCGNoiseSystemUtils::ValidateNoiseDescriptor(const FAuracronPCGNoiseDescriptor& NoiseDescriptor)
{
    // Validate frequency
    if (NoiseDescriptor.Frequency <= 0.0f)
    {
        return false;
    }

    // Validate octaves
    if (NoiseDescriptor.Octaves < 1 || NoiseDescriptor.Octaves > 16)
    {
        return false;
    }

    // Validate persistence
    if (NoiseDescriptor.Persistence < 0.0f || NoiseDescriptor.Persistence > 1.0f)
    {
        return false;
    }

    // Validate lacunarity
    if (NoiseDescriptor.Lacunarity <= 1.0f)
    {
        return false;
    }

    // Validate output range
    if (NoiseDescriptor.OutputMin >= NoiseDescriptor.OutputMax)
    {
        return false;
    }

    return true;
}

FAuracronPCGNoiseDescriptor UAuracronPCGNoiseSystemUtils::CreateDefaultNoiseDescriptor(EAuracronPCGNoiseType NoiseType)
{
    FAuracronPCGNoiseDescriptor Descriptor;
    Descriptor.NoiseType = NoiseType;
    
    switch (NoiseType)
    {
        case EAuracronPCGNoiseType::Perlin:
            Descriptor.Frequency = 0.01f;
            Descriptor.Octaves = 4;
            Descriptor.Persistence = 0.5f;
            Descriptor.Lacunarity = 2.0f;
            break;
        case EAuracronPCGNoiseType::Simplex:
            Descriptor.Frequency = 0.01f;
            Descriptor.Octaves = 4;
            Descriptor.Persistence = 0.5f;
            Descriptor.Lacunarity = 2.0f;
            break;
        case EAuracronPCGNoiseType::Worley:
            Descriptor.Frequency = 0.05f;
            Descriptor.Jitter = 1.0f;
            Descriptor.DistanceFunction = EAuracronPCGWorleyDistanceFunction::Euclidean;
            break;
        case EAuracronPCGNoiseType::Ridge:
            Descriptor.Frequency = 0.01f;
            Descriptor.Octaves = 6;
            Descriptor.Persistence = 0.6f;
            Descriptor.Lacunarity = 2.0f;
            break;
        default:
            Descriptor.Frequency = 0.01f;
            Descriptor.Octaves = 4;
            Descriptor.Persistence = 0.5f;
            Descriptor.Lacunarity = 2.0f;
            break;
    }
    
    return Descriptor;
}
