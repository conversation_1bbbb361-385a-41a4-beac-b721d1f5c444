// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronSigilosBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronSigilosBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilosBridge();
AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilosBridge_NoRegister();
AURACRONSIGILOSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState();
AURACRONSIGILOSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType();
AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature();
AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature();
AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature();
AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature();
AURACRONSIGILOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects();
AURACRONSIGILOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloConfiguration();
AURACRONSIGILOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry();
AURACRONSIGILOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility();
AURACRONSIGILOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses();
AURACRONSIGILOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloProperties();
AURACRONSIGILOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTimelineComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
METASOUNDENGINE_API UClass* Z_Construct_UClass_UMetaSoundSource_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronSigilosBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronSigiloType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSigiloType;
static UEnum* EAuracronSigiloType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSigiloType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSigiloType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("EAuracronSigiloType"));
	}
	return Z_Registration_Info_UEnum_EAuracronSigiloType.OuterSingleton;
}
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronSigiloType>()
{
	return EAuracronSigiloType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aegis.DisplayName", "Aegis (Tank)" },
		{ "Aegis.Name", "EAuracronSigiloType::Aegis" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de S\xc3\x83\xc2\xadgilos Auracron\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronSigiloType::None" },
		{ "Ruin.DisplayName", "Ruin (Damage)" },
		{ "Ruin.Name", "EAuracronSigiloType::Ruin" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de S\xc3\x83\xc2\xadgilos Auracron" },
#endif
		{ "Vesper.DisplayName", "Vesper (Utility)" },
		{ "Vesper.Name", "EAuracronSigiloType::Vesper" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSigiloType::None", (int64)EAuracronSigiloType::None },
		{ "EAuracronSigiloType::Aegis", (int64)EAuracronSigiloType::Aegis },
		{ "EAuracronSigiloType::Ruin", (int64)EAuracronSigiloType::Ruin },
		{ "EAuracronSigiloType::Vesper", (int64)EAuracronSigiloType::Vesper },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	"EAuracronSigiloType",
	"EAuracronSigiloType",
	Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType()
{
	if (!Z_Registration_Info_UEnum_EAuracronSigiloType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSigiloType.InnerSingleton, Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSigiloType.InnerSingleton;
}
// ********** End Enum EAuracronSigiloType *********************************************************

// ********** Begin Enum EAuracronSigiloFusionState ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSigiloFusionState;
static UEnum* EAuracronSigiloFusionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSigiloFusionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSigiloFusionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("EAuracronSigiloFusionState"));
	}
	return Z_Registration_Info_UEnum_EAuracronSigiloFusionState.OuterSingleton;
}
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronSigiloFusionState>()
{
	return EAuracronSigiloFusionState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EAuracronSigiloFusionState::Active" },
		{ "BlueprintType", "true" },
		{ "Charging.DisplayName", "Charging" },
		{ "Charging.Name", "EAuracronSigiloFusionState::Charging" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados de fus\xc3\x83\xc2\xa3o do Sigilo\n */" },
#endif
		{ "Cooldown.DisplayName", "Cooldown" },
		{ "Cooldown.Name", "EAuracronSigiloFusionState::Cooldown" },
		{ "Inactive.DisplayName", "Inactive" },
		{ "Inactive.Name", "EAuracronSigiloFusionState::Inactive" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
		{ "Reforging.DisplayName", "Reforging" },
		{ "Reforging.Name", "EAuracronSigiloFusionState::Reforging" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados de fus\xc3\x83\xc2\xa3o do Sigilo" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSigiloFusionState::Inactive", (int64)EAuracronSigiloFusionState::Inactive },
		{ "EAuracronSigiloFusionState::Charging", (int64)EAuracronSigiloFusionState::Charging },
		{ "EAuracronSigiloFusionState::Active", (int64)EAuracronSigiloFusionState::Active },
		{ "EAuracronSigiloFusionState::Cooldown", (int64)EAuracronSigiloFusionState::Cooldown },
		{ "EAuracronSigiloFusionState::Reforging", (int64)EAuracronSigiloFusionState::Reforging },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	"EAuracronSigiloFusionState",
	"EAuracronSigiloFusionState",
	Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState()
{
	if (!Z_Registration_Info_UEnum_EAuracronSigiloFusionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSigiloFusionState.InnerSingleton, Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSigiloFusionState.InnerSingleton;
}
// ********** End Enum EAuracronSigiloFusionState **************************************************

// ********** Begin ScriptStruct FAuracronSigiloProperties *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties;
class UScriptStruct* FAuracronSigiloProperties::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSigiloProperties, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("AuracronSigiloProperties"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para propriedades base de um Sigilo\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para propriedades base de um Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloType_MetaData[] = {
		{ "Category", "Sigilo Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloName_MetaData[] = {
		{ "Category", "Sigilo Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloDescription_MetaData[] = {
		{ "Category", "Sigilo Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloIcon_MetaData[] = {
		{ "Category", "Sigilo Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8d""cone do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8d""cone do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryColor_MetaData[] = {
		{ "Category", "Sigilo Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor prim\xc3\x83\xc2\xa1ria do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor prim\xc3\x83\xc2\xa1ria do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryColor_MetaData[] = {
		{ "Category", "Sigilo Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor secund\xc3\x83\xc2\xa1ria do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor secund\xc3\x83\xc2\xa1ria do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTime_MetaData[] = {
		{ "Category", "Sigilo Properties" },
		{ "ClampMax", "600.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo para fus\xc3\x83\xc2\xa3o (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo para fus\xc3\x83\xc2\xa3o (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeCooldown_MetaData[] = {
		{ "Category", "Sigilo Properties" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "30.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown para re-forjamento (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown para re-forjamento (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloLevel_MetaData[] = {
		{ "Category", "Sigilo Properties" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloExperience_MetaData[] = {
		{ "Category", "Sigilo Properties" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceToNextLevel_MetaData[] = {
		{ "Category", "Sigilo Properties" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia necess\xc3\x83\xc2\xa1ria para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia necess\xc3\x83\xc2\xa1ria para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SigiloName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SigiloDescription;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SigiloIcon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrimaryColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SecondaryColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReforgeCooldown;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigiloLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigiloExperience;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceToNextLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSigiloProperties>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloType_MetaData), NewProp_SigiloType_MetaData) }; // 2707057007
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloName = { "SigiloName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, SigiloName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloName_MetaData), NewProp_SigiloName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloDescription = { "SigiloDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, SigiloDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloDescription_MetaData), NewProp_SigiloDescription_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloIcon = { "SigiloIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, SigiloIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloIcon_MetaData), NewProp_SigiloIcon_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_PrimaryColor = { "PrimaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, PrimaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryColor_MetaData), NewProp_PrimaryColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SecondaryColor = { "SecondaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, SecondaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryColor_MetaData), NewProp_SecondaryColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_FusionTime = { "FusionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, FusionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTime_MetaData), NewProp_FusionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_ReforgeCooldown = { "ReforgeCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, ReforgeCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeCooldown_MetaData), NewProp_ReforgeCooldown_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloLevel = { "SigiloLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, SigiloLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloLevel_MetaData), NewProp_SigiloLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloExperience = { "SigiloExperience", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, SigiloExperience), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloExperience_MetaData), NewProp_SigiloExperience_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_ExperienceToNextLevel = { "ExperienceToNextLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloProperties, ExperienceToNextLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceToNextLevel_MetaData), NewProp_ExperienceToNextLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_PrimaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SecondaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_FusionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_ReforgeCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_SigiloExperience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewProp_ExperienceToNextLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	&NewStructOps,
	"AuracronSigiloProperties",
	Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::PropPointers),
	sizeof(FAuracronSigiloProperties),
	alignof(FAuracronSigiloProperties),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloProperties()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSigiloProperties *******************************************

// ********** Begin ScriptStruct FAuracronSigiloPassiveBonuses *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses;
class UScriptStruct* FAuracronSigiloPassiveBonuses::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("AuracronSigiloPassiveBonuses"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para b\xc3\x83\xc2\xb4nus passivos do Sigilo\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para b\xc3\x83\xc2\xb4nus passivos do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthBonus_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\x83\xc2\xb4nus de HP (percentual) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\x83\xc2\xb4nus de HP (percentual)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArmorBonus_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\x83\xc2\xb4nus de Armadura (percentual) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\x83\xc2\xb4nus de Armadura (percentual)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackBonus_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\x83\xc2\xb4nus de Ataque (percentual) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\x83\xc2\xb4nus de Ataque (percentual)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityPowerBonus_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\x83\xc2\xb4nus de Poder de Habilidade (percentual) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\x83\xc2\xb4nus de Poder de Habilidade (percentual)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedBonus_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\x83\xc2\xb4nus de Velocidade de Movimento (percentual) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\x83\xc2\xb4nus de Velocidade de Movimento (percentual)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReduction_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "0.5" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Redu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Cooldown (percentual) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Redu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Cooldown (percentual)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaRegeneration_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Mana (por segundo) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Mana (por segundo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageResistance_MetaData[] = {
		{ "Category", "Passive Bonuses" },
		{ "ClampMax", "0.8" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resist\xc3\x83\xc2\xaancia a Dano (percentual) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resist\xc3\x83\xc2\xaancia a Dano (percentual)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ArmorBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityPowerBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownReduction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaRegeneration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageResistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSigiloPassiveBonuses>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_HealthBonus = { "HealthBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, HealthBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthBonus_MetaData), NewProp_HealthBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_ArmorBonus = { "ArmorBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, ArmorBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArmorBonus_MetaData), NewProp_ArmorBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_AttackBonus = { "AttackBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, AttackBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackBonus_MetaData), NewProp_AttackBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_AbilityPowerBonus = { "AbilityPowerBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, AbilityPowerBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityPowerBonus_MetaData), NewProp_AbilityPowerBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_MovementSpeedBonus = { "MovementSpeedBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, MovementSpeedBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedBonus_MetaData), NewProp_MovementSpeedBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_CooldownReduction = { "CooldownReduction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, CooldownReduction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReduction_MetaData), NewProp_CooldownReduction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_ManaRegeneration = { "ManaRegeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, ManaRegeneration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaRegeneration_MetaData), NewProp_ManaRegeneration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_DamageResistance = { "DamageResistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloPassiveBonuses, DamageResistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageResistance_MetaData), NewProp_DamageResistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_HealthBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_ArmorBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_AttackBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_AbilityPowerBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_MovementSpeedBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_CooldownReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_ManaRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewProp_DamageResistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	&NewStructOps,
	"AuracronSigiloPassiveBonuses",
	Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::PropPointers),
	sizeof(FAuracronSigiloPassiveBonuses),
	alignof(FAuracronSigiloPassiveBonuses),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSigiloPassiveBonuses ***************************************

// ********** Begin ScriptStruct FAuracronSigiloConfiguration **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration;
class UScriptStruct* FAuracronSigiloConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSigiloConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("AuracronSigiloConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura completa de configura\xc3\xa7\xc3\xa3o de um Sigilo (moved before FAuracronSigiloConfigurationEntry)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura completa de configura\xc3\xa7\xc3\xa3o de um Sigilo (moved before FAuracronSigiloConfigurationEntry)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Propriedades b\xc3\xa1sicas do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades b\xc3\xa1sicas do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PassiveBonuses_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus passivos do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus passivos do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusiveAbility_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade exclusiva do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade exclusiva do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualEffects_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos visuais do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos visuais do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioEffects_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos sonoros do Sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos sonoros do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionGameplayEffects_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GameplayEffects aplicados durante a fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffects aplicados durante a fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTags_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GameplayTags adicionados durante a fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayTags adicionados durante a fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlockedTags_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GameplayTags bloqueados durante a fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayTags bloqueados durante a fus\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Properties;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PassiveBonuses;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExclusiveAbility;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisualEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AudioEffects;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_FusionGameplayEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FusionGameplayEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlockedTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSigiloConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, Properties), Z_Construct_UScriptStruct_FAuracronSigiloProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) }; // 3024668222
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_PassiveBonuses = { "PassiveBonuses", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, PassiveBonuses), Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PassiveBonuses_MetaData), NewProp_PassiveBonuses_MetaData) }; // 4259013295
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_ExclusiveAbility = { "ExclusiveAbility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, ExclusiveAbility), Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusiveAbility_MetaData), NewProp_ExclusiveAbility_MetaData) }; // 0
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_VisualEffects = { "VisualEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, VisualEffects), Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualEffects_MetaData), NewProp_VisualEffects_MetaData) }; // 0
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_AudioEffects = { "AudioEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, AudioEffects), Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioEffects_MetaData), NewProp_AudioEffects_MetaData) }; // 0
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_FusionGameplayEffects_Inner = { "FusionGameplayEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_FusionGameplayEffects = { "FusionGameplayEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, FusionGameplayEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionGameplayEffects_MetaData), NewProp_FusionGameplayEffects_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_FusionTags = { "FusionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, FusionTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTags_MetaData), NewProp_FusionTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_BlockedTags = { "BlockedTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfiguration, BlockedTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlockedTags_MetaData), NewProp_BlockedTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_PassiveBonuses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_ExclusiveAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_VisualEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_AudioEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_FusionGameplayEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_FusionGameplayEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_FusionTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewProp_BlockedTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	&NewStructOps,
	"AuracronSigiloConfiguration",
	Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::PropPointers),
	sizeof(FAuracronSigiloConfiguration),
	alignof(FAuracronSigiloConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSigiloConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronSigiloConfigurationEntry *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry;
class UScriptStruct* FAuracronSigiloConfigurationEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("AuracronSigiloConfigurationEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de Sigilo (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de Sigilo (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloType_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "Category", "Sigilo Configuration" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSigiloConfigurationEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfigurationEntry, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloType_MetaData), NewProp_SigiloType_MetaData) }; // 2707057007
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloConfigurationEntry, Configuration), Z_Construct_UScriptStruct_FAuracronSigiloConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4254436308
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::NewProp_SigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	&NewStructOps,
	"AuracronSigiloConfigurationEntry",
	Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::PropPointers),
	sizeof(FAuracronSigiloConfigurationEntry),
	alignof(FAuracronSigiloConfigurationEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSigiloConfigurationEntry ***********************************

// ********** Begin ScriptStruct FAuracronSigiloExclusiveAbility ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility;
class UScriptStruct* FAuracronSigiloExclusiveAbility::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("AuracronSigiloExclusiveAbility"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para habilidade exclusiva do Sigilo\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para habilidade exclusiva do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityClass_MetaData[] = {
		{ "Category", "Exclusive Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Classe da habilidade exclusiva */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe da habilidade exclusiva" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityName_MetaData[] = {
		{ "Category", "Exclusive Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityDescription_MetaData[] = {
		{ "Category", "Exclusive Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityIcon_MetaData[] = {
		{ "Category", "Exclusive Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8d""cone da habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8d""cone da habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityCooldown_MetaData[] = {
		{ "Category", "Exclusive Ability" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown da habilidade (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown da habilidade (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaCost_MetaData[] = {
		{ "Category", "Exclusive Ability" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custo de mana da habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custo de mana da habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityDuration_MetaData[] = {
		{ "Category", "Exclusive Ability" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da habilidade (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da habilidade (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityRange_MetaData[] = {
		{ "Category", "Exclusive Ability" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alcance da habilidade (em unidades) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alcance da habilidade (em unidades)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaOfEffect_MetaData[] = {
		{ "Category", "Exclusive Ability" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x81rea de efeito (em unidades) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x81rea de efeito (em unidades)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_AbilityClass;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AbilityName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AbilityDescription;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilityIcon;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaOfEffect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSigiloExclusiveAbility>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityClass = { "AbilityClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AbilityClass), Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityClass_MetaData), NewProp_AbilityClass_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityName = { "AbilityName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AbilityName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityName_MetaData), NewProp_AbilityName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityDescription = { "AbilityDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AbilityDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityDescription_MetaData), NewProp_AbilityDescription_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityIcon = { "AbilityIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AbilityIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityIcon_MetaData), NewProp_AbilityIcon_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityCooldown = { "AbilityCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AbilityCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityCooldown_MetaData), NewProp_AbilityCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_ManaCost = { "ManaCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, ManaCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaCost_MetaData), NewProp_ManaCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityDuration = { "AbilityDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AbilityDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityDuration_MetaData), NewProp_AbilityDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityRange = { "AbilityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AbilityRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityRange_MetaData), NewProp_AbilityRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AreaOfEffect = { "AreaOfEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloExclusiveAbility, AreaOfEffect), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaOfEffect_MetaData), NewProp_AreaOfEffect_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_ManaCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AbilityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewProp_AreaOfEffect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	&NewStructOps,
	"AuracronSigiloExclusiveAbility",
	Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::PropPointers),
	sizeof(FAuracronSigiloExclusiveAbility),
	alignof(FAuracronSigiloExclusiveAbility),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSigiloExclusiveAbility *************************************

// ********** Begin ScriptStruct FAuracronSigiloVisualEffects **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects;
class UScriptStruct* FAuracronSigiloVisualEffects::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("AuracronSigiloVisualEffects"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para efeitos visuais do Sigilo\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para efeitos visuais do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionParticleSystem_MetaData[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\x83\xc2\xad""culas de fus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\x83\xc2\xad""culas de fus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveParticleSystem_MetaData[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\x83\xc2\xad""culas ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\x83\xc2\xad""culas ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityParticleSystem_MetaData[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\x83\xc2\xad""culas de habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\x83\xc2\xad""culas de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionOverlayMaterial_MetaData[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material de overlay do campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material de overlay do campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialParameterCollection_MetaData[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de par\xc3\x83\xc2\xa2metros de material */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de par\xc3\x83\xc2\xa2metros de material" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionIntensityCurve_MetaData[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Curva de intensidade da fus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Curva de intensidade da fus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectScale_MetaData[] = {
		{ "Category", "Visual Effects" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala do efeito visual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala do efeito visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectIntensity_MetaData[] = {
		{ "Category", "Visual Effects" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FusionParticleSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ActiveParticleSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilityParticleSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ChampionOverlayMaterial;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialParameterCollection;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FusionIntensityCurve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSigiloVisualEffects>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_FusionParticleSystem = { "FusionParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, FusionParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionParticleSystem_MetaData), NewProp_FusionParticleSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_ActiveParticleSystem = { "ActiveParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, ActiveParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveParticleSystem_MetaData), NewProp_ActiveParticleSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_AbilityParticleSystem = { "AbilityParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, AbilityParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityParticleSystem_MetaData), NewProp_AbilityParticleSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_ChampionOverlayMaterial = { "ChampionOverlayMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, ChampionOverlayMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionOverlayMaterial_MetaData), NewProp_ChampionOverlayMaterial_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_MaterialParameterCollection = { "MaterialParameterCollection", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, MaterialParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialParameterCollection_MetaData), NewProp_MaterialParameterCollection_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_FusionIntensityCurve = { "FusionIntensityCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, FusionIntensityCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionIntensityCurve_MetaData), NewProp_FusionIntensityCurve_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_EffectScale = { "EffectScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, EffectScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectScale_MetaData), NewProp_EffectScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_EffectIntensity = { "EffectIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloVisualEffects, EffectIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectIntensity_MetaData), NewProp_EffectIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_FusionParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_ActiveParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_AbilityParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_ChampionOverlayMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_MaterialParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_FusionIntensityCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_EffectScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewProp_EffectIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	&NewStructOps,
	"AuracronSigiloVisualEffects",
	Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::PropPointers),
	sizeof(FAuracronSigiloVisualEffects),
	alignof(FAuracronSigiloVisualEffects),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSigiloVisualEffects ****************************************

// ********** Begin ScriptStruct FAuracronSigiloAudioEffects ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects;
class UScriptStruct* FAuracronSigiloAudioEffects::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects, (UObject*)Z_Construct_UPackage__Script_AuracronSigilosBridge(), TEXT("AuracronSigiloAudioEffects"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para efeitos sonoros do Sigilo\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para efeitos sonoros do Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionSound_MetaData[] = {
		{ "Category", "Audio Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de fus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de fus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationSound_MetaData[] = {
		{ "Category", "Audio Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de ativa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de ativa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySound_MetaData[] = {
		{ "Category", "Audio Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de habilidade exclusiva */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de habilidade exclusiva" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeSound_MetaData[] = {
		{ "Category", "Audio Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de re-forjamento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de re-forjamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoundVolume_MetaData[] = {
		{ "Category", "Audio Effects" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume dos efeitos sonoros */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume dos efeitos sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoundPitch_MetaData[] = {
		{ "Category", "Audio Effects" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pitch dos efeitos sonoros */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pitch dos efeitos sonoros" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FusionSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ActivationSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilitySound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ReforgeSound;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoundVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoundPitch;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSigiloAudioEffects>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_FusionSound = { "FusionSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloAudioEffects, FusionSound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionSound_MetaData), NewProp_FusionSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_ActivationSound = { "ActivationSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloAudioEffects, ActivationSound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationSound_MetaData), NewProp_ActivationSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_AbilitySound = { "AbilitySound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloAudioEffects, AbilitySound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySound_MetaData), NewProp_AbilitySound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_ReforgeSound = { "ReforgeSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloAudioEffects, ReforgeSound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeSound_MetaData), NewProp_ReforgeSound_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_SoundVolume = { "SoundVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloAudioEffects, SoundVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoundVolume_MetaData), NewProp_SoundVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_SoundPitch = { "SoundPitch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSigiloAudioEffects, SoundPitch), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoundPitch_MetaData), NewProp_SoundPitch_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_FusionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_ActivationSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_AbilitySound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_ReforgeSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_SoundVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewProp_SoundPitch,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
	nullptr,
	&NewStructOps,
	"AuracronSigiloAudioEffects",
	Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::PropPointers),
	sizeof(FAuracronSigiloAudioEffects),
	alignof(FAuracronSigiloAudioEffects),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSigiloAudioEffects *****************************************

// ********** Begin Delegate FOnSigiloSelected *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics
{
	struct AuracronSigilosBridge_eventOnSigiloSelected_Parms
	{
		EAuracronSigiloType SigiloType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Sigilo \xc3\x83\xc2\xa9 selecionado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Sigilo \xc3\x83\xc2\xa9 selecionado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventOnSigiloSelected_Parms, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::NewProp_SigiloType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "OnSigiloSelected__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::AuracronSigilosBridge_eventOnSigiloSelected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::AuracronSigilosBridge_eventOnSigiloSelected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronSigilosBridge::FOnSigiloSelected_DelegateWrapper(const FMulticastScriptDelegate& OnSigiloSelected, EAuracronSigiloType SigiloType)
{
	struct AuracronSigilosBridge_eventOnSigiloSelected_Parms
	{
		EAuracronSigiloType SigiloType;
	};
	AuracronSigilosBridge_eventOnSigiloSelected_Parms Parms;
	Parms.SigiloType=SigiloType;
	OnSigiloSelected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigiloSelected *******************************************************

// ********** Begin Delegate FOnFusionStarted ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando fus\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 iniciada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando fus\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 iniciada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "OnFusionStarted__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronSigilosBridge::FOnFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnFusionStarted)
{
	OnFusionStarted.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnFusionStarted ********************************************************

// ********** Begin Delegate FOnFusionCompleted ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics
{
	struct AuracronSigilosBridge_eventOnFusionCompleted_Parms
	{
		EAuracronSigiloType SigiloType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando fus\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando fus\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventOnFusionCompleted_Parms, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::NewProp_SigiloType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "OnFusionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::AuracronSigilosBridge_eventOnFusionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::AuracronSigilosBridge_eventOnFusionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronSigilosBridge::FOnFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnFusionCompleted, EAuracronSigiloType SigiloType)
{
	struct AuracronSigilosBridge_eventOnFusionCompleted_Parms
	{
		EAuracronSigiloType SigiloType;
	};
	AuracronSigilosBridge_eventOnFusionCompleted_Parms Parms;
	Parms.SigiloType=SigiloType;
	OnFusionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFusionCompleted ******************************************************

// ********** Begin Delegate FOnSigiloReforged *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics
{
	struct AuracronSigilosBridge_eventOnSigiloReforged_Parms
	{
		EAuracronSigiloType OldSigilo;
		EAuracronSigiloType NewSigilo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Sigilo \xc3\x83\xc2\xa9 re-forjado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Sigilo \xc3\x83\xc2\xa9 re-forjado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldSigilo_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldSigilo;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewSigilo_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewSigilo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_OldSigilo_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_OldSigilo = { "OldSigilo", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventOnSigiloReforged_Parms, OldSigilo), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_NewSigilo_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_NewSigilo = { "NewSigilo", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventOnSigiloReforged_Parms, NewSigilo), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_OldSigilo_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_OldSigilo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_NewSigilo_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::NewProp_NewSigilo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "OnSigiloReforged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::AuracronSigilosBridge_eventOnSigiloReforged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::AuracronSigilosBridge_eventOnSigiloReforged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronSigilosBridge::FOnSigiloReforged_DelegateWrapper(const FMulticastScriptDelegate& OnSigiloReforged, EAuracronSigiloType OldSigilo, EAuracronSigiloType NewSigilo)
{
	struct AuracronSigilosBridge_eventOnSigiloReforged_Parms
	{
		EAuracronSigiloType OldSigilo;
		EAuracronSigiloType NewSigilo;
	};
	AuracronSigilosBridge_eventOnSigiloReforged_Parms Parms;
	Parms.OldSigilo=OldSigilo;
	Parms.NewSigilo=NewSigilo;
	OnSigiloReforged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigiloReforged *******************************************************

// ********** Begin Class UAuracronSigilosBridge Function ActivateExclusiveAbility *****************
struct Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics
{
	struct AuracronSigilosBridge_eventActivateExclusiveAbility_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar habilidade exclusiva do Sigilo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar habilidade exclusiva do Sigilo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventActivateExclusiveAbility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventActivateExclusiveAbility_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "ActivateExclusiveAbility", Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::AuracronSigilosBridge_eventActivateExclusiveAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::AuracronSigilosBridge_eventActivateExclusiveAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execActivateExclusiveAbility)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateExclusiveAbility();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function ActivateExclusiveAbility *******************

// ********** Begin Class UAuracronSigilosBridge Function ApplyPassiveBonuses **********************
struct Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics
{
	struct AuracronSigilosBridge_eventApplyPassiveBonuses_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Bonuses" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar b\xc3\x83\xc2\xb4nus passivos do Sigilo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar b\xc3\x83\xc2\xb4nus passivos do Sigilo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventApplyPassiveBonuses_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventApplyPassiveBonuses_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "ApplyPassiveBonuses", Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::AuracronSigilosBridge_eventApplyPassiveBonuses_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::AuracronSigilosBridge_eventApplyPassiveBonuses_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execApplyPassiveBonuses)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyPassiveBonuses();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function ApplyPassiveBonuses ************************

// ********** Begin Class UAuracronSigilosBridge Function CancelSigiloFusion ***********************
struct Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics
{
	struct AuracronSigilosBridge_eventCancelSigiloFusion_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Cancelar fus\xc3\x83\xc2\xa3o em andamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancelar fus\xc3\x83\xc2\xa3o em andamento" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventCancelSigiloFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventCancelSigiloFusion_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "CancelSigiloFusion", Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::AuracronSigilosBridge_eventCancelSigiloFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::AuracronSigilosBridge_eventCancelSigiloFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execCancelSigiloFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelSigiloFusion();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function CancelSigiloFusion *************************

// ********** Begin Class UAuracronSigilosBridge Function CanReforge *******************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics
{
	struct AuracronSigilosBridge_eventCanReforge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se pode re-forjar\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se pode re-forjar" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventCanReforge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventCanReforge_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "CanReforge", Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::AuracronSigilosBridge_eventCanReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::AuracronSigilosBridge_eventCanReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execCanReforge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanReforge();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function CanReforge *********************************

// ********** Begin Class UAuracronSigilosBridge Function CompleteSigiloFusion *********************
struct Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics
{
	struct AuracronSigilosBridge_eventCompleteSigiloFusion_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Completar fus\xc3\x83\xc2\xa3o do Sigilo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completar fus\xc3\x83\xc2\xa3o do Sigilo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventCompleteSigiloFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventCompleteSigiloFusion_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "CompleteSigiloFusion", Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::AuracronSigilosBridge_eventCompleteSigiloFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::AuracronSigilosBridge_eventCompleteSigiloFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execCompleteSigiloFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CompleteSigiloFusion();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function CompleteSigiloFusion ***********************

// ********** Begin Class UAuracronSigilosBridge Function GetAlternativeAbilityTree ****************
struct Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics
{
	struct AuracronSigilosBridge_eventGetAlternativeAbilityTree_Parms
	{
		TArray<TSubclassOf<UGameplayAbility>> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter \xc3\x83\xc2\xa1rvore de habilidades alternativas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter \xc3\x83\xc2\xa1rvore de habilidades alternativas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventGetAlternativeAbilityTree_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "GetAlternativeAbilityTree", Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::AuracronSigilosBridge_eventGetAlternativeAbilityTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::AuracronSigilosBridge_eventGetAlternativeAbilityTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execGetAlternativeAbilityTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<TSubclassOf<UGameplayAbility>>*)Z_Param__Result=P_THIS->GetAlternativeAbilityTree();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function GetAlternativeAbilityTree ******************

// ********** Begin Class UAuracronSigilosBridge Function GetFusionState ***************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics
{
	struct AuracronSigilosBridge_eventGetFusionState_Parms
	{
		EAuracronSigiloFusionState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estado atual da fus\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estado atual da fus\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventGetFusionState_Parms, ReturnValue), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState, METADATA_PARAMS(0, nullptr) }; // 822310332
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "GetFusionState", Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::AuracronSigilosBridge_eventGetFusionState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::AuracronSigilosBridge_eventGetFusionState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execGetFusionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronSigiloFusionState*)Z_Param__Result=P_THIS->GetFusionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function GetFusionState *****************************

// ********** Begin Class UAuracronSigilosBridge Function GetReforgeCooldownRemaining **************
struct Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics
{
	struct AuracronSigilosBridge_eventGetReforgeCooldownRemaining_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter tempo restante de cooldown para re-forjamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tempo restante de cooldown para re-forjamento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventGetReforgeCooldownRemaining_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "GetReforgeCooldownRemaining", Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::AuracronSigilosBridge_eventGetReforgeCooldownRemaining_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::AuracronSigilosBridge_eventGetReforgeCooldownRemaining_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execGetReforgeCooldownRemaining)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetReforgeCooldownRemaining();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function GetReforgeCooldownRemaining ****************

// ********** Begin Class UAuracronSigilosBridge Function GetSelectedSigilo ************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics
{
	struct AuracronSigilosBridge_eventGetSelectedSigilo_Parms
	{
		EAuracronSigiloType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter Sigilo atualmente selecionado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter Sigilo atualmente selecionado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventGetSelectedSigilo_Parms, ReturnValue), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "GetSelectedSigilo", Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::AuracronSigilosBridge_eventGetSelectedSigilo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::AuracronSigilosBridge_eventGetSelectedSigilo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execGetSelectedSigilo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronSigiloType*)Z_Param__Result=P_THIS->GetSelectedSigilo();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function GetSelectedSigilo **************************

// ********** Begin Class UAuracronSigilosBridge Function GetSigiloConfiguration *******************
struct Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics
{
	struct AuracronSigilosBridge_eventGetSigiloConfiguration_Parms
	{
		EAuracronSigiloType SigiloType;
		FAuracronSigiloConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Sigilo espec\xc3\x83\xc2\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Sigilo espec\xc3\x83\xc2\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventGetSigiloConfiguration_Parms, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventGetSigiloConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronSigiloConfiguration, METADATA_PARAMS(0, nullptr) }; // 4254436308
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::NewProp_SigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "GetSigiloConfiguration", Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::AuracronSigilosBridge_eventGetSigiloConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::AuracronSigilosBridge_eventGetSigiloConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execGetSigiloConfiguration)
{
	P_GET_ENUM(EAuracronSigiloType,Z_Param_SigiloType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronSigiloConfiguration*)Z_Param__Result=P_THIS->GetSigiloConfiguration(EAuracronSigiloType(Z_Param_SigiloType));
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function GetSigiloConfiguration *********************

// ********** Begin Class UAuracronSigilosBridge Function GetTimeToFusion **************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics
{
	struct AuracronSigilosBridge_eventGetTimeToFusion_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter tempo restante para fus\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tempo restante para fus\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventGetTimeToFusion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "GetTimeToFusion", Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::AuracronSigilosBridge_eventGetTimeToFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::AuracronSigilosBridge_eventGetTimeToFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execGetTimeToFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeToFusion();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function GetTimeToFusion ****************************

// ********** Begin Class UAuracronSigilosBridge Function LoadDefaultSigiloConfigurations **********
struct Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics
{
	struct AuracronSigilosBridge_eventLoadDefaultSigiloConfigurations_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es padr\xc3\x83\xc2\xa3o dos S\xc3\x83\xc2\xadgilos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es padr\xc3\x83\xc2\xa3o dos S\xc3\x83\xc2\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventLoadDefaultSigiloConfigurations_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventLoadDefaultSigiloConfigurations_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "LoadDefaultSigiloConfigurations", Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::AuracronSigilosBridge_eventLoadDefaultSigiloConfigurations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::AuracronSigilosBridge_eventLoadDefaultSigiloConfigurations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execLoadDefaultSigiloConfigurations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadDefaultSigiloConfigurations();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function LoadDefaultSigiloConfigurations ************

// ********** Begin Class UAuracronSigilosBridge Function OnRep_FusionState ************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_FusionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_FusionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "OnRep_FusionState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_FusionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_FusionState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_FusionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_FusionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execOnRep_FusionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_FusionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function OnRep_FusionState **************************

// ********** Begin Class UAuracronSigilosBridge Function OnRep_SelectedSigiloType *****************
struct Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_SelectedSigiloType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_SelectedSigiloType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "OnRep_SelectedSigiloType", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_SelectedSigiloType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_SelectedSigiloType_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_SelectedSigiloType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_SelectedSigiloType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execOnRep_SelectedSigiloType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SelectedSigiloType();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function OnRep_SelectedSigiloType *******************

// ********** Begin Class UAuracronSigilosBridge Function ReforgeSigilo ****************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics
{
	struct AuracronSigilosBridge_eventReforgeSigilo_Parms
	{
		EAuracronSigiloType NewSigiloType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Reforge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Re-forjar Sigilo no Nexus\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Re-forjar Sigilo no Nexus" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewSigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewSigiloType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_NewSigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_NewSigiloType = { "NewSigiloType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventReforgeSigilo_Parms, NewSigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
void Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventReforgeSigilo_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventReforgeSigilo_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_NewSigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_NewSigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "ReforgeSigilo", Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::AuracronSigilosBridge_eventReforgeSigilo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::AuracronSigilosBridge_eventReforgeSigilo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execReforgeSigilo)
{
	P_GET_ENUM(EAuracronSigiloType,Z_Param_NewSigiloType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReforgeSigilo(EAuracronSigiloType(Z_Param_NewSigiloType));
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function ReforgeSigilo ******************************

// ********** Begin Class UAuracronSigilosBridge Function RemovePassiveBonuses *********************
struct Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics
{
	struct AuracronSigilosBridge_eventRemovePassiveBonuses_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Bonuses" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover b\xc3\x83\xc2\xb4nus passivos do Sigilo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover b\xc3\x83\xc2\xb4nus passivos do Sigilo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventRemovePassiveBonuses_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventRemovePassiveBonuses_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "RemovePassiveBonuses", Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::AuracronSigilosBridge_eventRemovePassiveBonuses_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::AuracronSigilosBridge_eventRemovePassiveBonuses_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execRemovePassiveBonuses)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemovePassiveBonuses();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function RemovePassiveBonuses ***********************

// ********** Begin Class UAuracronSigilosBridge Function SelectSigilo *****************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics
{
	struct AuracronSigilosBridge_eventSelectSigilo_Parms
	{
		EAuracronSigiloType SigiloType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Selecionar Sigilo durante a fase de sele\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de campe\xc3\x83\xc2\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selecionar Sigilo durante a fase de sele\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de campe\xc3\x83\xc2\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventSelectSigilo_Parms, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
void Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventSelectSigilo_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventSelectSigilo_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_SigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "SelectSigilo", Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::AuracronSigilosBridge_eventSelectSigilo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::AuracronSigilosBridge_eventSelectSigilo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execSelectSigilo)
{
	P_GET_ENUM(EAuracronSigiloType,Z_Param_SigiloType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SelectSigilo(EAuracronSigiloType(Z_Param_SigiloType));
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function SelectSigilo *******************************

// ********** Begin Class UAuracronSigilosBridge Function SetSigiloConfiguration *******************
struct Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics
{
	struct AuracronSigilosBridge_eventSetSigiloConfiguration_Parms
	{
		EAuracronSigiloType SigiloType;
		FAuracronSigiloConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Sigilo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventSetSigiloConfiguration_Parms, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(0, nullptr) }; // 2707057007
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilosBridge_eventSetSigiloConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronSigiloConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4254436308
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::NewProp_SigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "SetSigiloConfiguration", Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::AuracronSigilosBridge_eventSetSigiloConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::AuracronSigilosBridge_eventSetSigiloConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execSetSigiloConfiguration)
{
	P_GET_ENUM(EAuracronSigiloType,Z_Param_SigiloType);
	P_GET_STRUCT_REF(FAuracronSigiloConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSigiloConfiguration(EAuracronSigiloType(Z_Param_SigiloType),Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function SetSigiloConfiguration *********************

// ********** Begin Class UAuracronSigilosBridge Function StartSigiloFusion ************************
struct Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics
{
	struct AuracronSigilosBridge_eventStartSigiloFusion_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Sigilos|Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar processo de fus\xc3\x83\xc2\xa3o do Sigilo (aos 6 minutos)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar processo de fus\xc3\x83\xc2\xa3o do Sigilo (aos 6 minutos)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronSigilosBridge_eventStartSigiloFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronSigilosBridge_eventStartSigiloFusion_Parms), &Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilosBridge, nullptr, "StartSigiloFusion", Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::AuracronSigilosBridge_eventStartSigiloFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::AuracronSigilosBridge_eventStartSigiloFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilosBridge::execStartSigiloFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartSigiloFusion();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilosBridge Function StartSigiloFusion **************************

// ********** Begin Class UAuracronSigilosBridge ***************************************************
void UAuracronSigilosBridge::StaticRegisterNativesUAuracronSigilosBridge()
{
	UClass* Class = UAuracronSigilosBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateExclusiveAbility", &UAuracronSigilosBridge::execActivateExclusiveAbility },
		{ "ApplyPassiveBonuses", &UAuracronSigilosBridge::execApplyPassiveBonuses },
		{ "CancelSigiloFusion", &UAuracronSigilosBridge::execCancelSigiloFusion },
		{ "CanReforge", &UAuracronSigilosBridge::execCanReforge },
		{ "CompleteSigiloFusion", &UAuracronSigilosBridge::execCompleteSigiloFusion },
		{ "GetAlternativeAbilityTree", &UAuracronSigilosBridge::execGetAlternativeAbilityTree },
		{ "GetFusionState", &UAuracronSigilosBridge::execGetFusionState },
		{ "GetReforgeCooldownRemaining", &UAuracronSigilosBridge::execGetReforgeCooldownRemaining },
		{ "GetSelectedSigilo", &UAuracronSigilosBridge::execGetSelectedSigilo },
		{ "GetSigiloConfiguration", &UAuracronSigilosBridge::execGetSigiloConfiguration },
		{ "GetTimeToFusion", &UAuracronSigilosBridge::execGetTimeToFusion },
		{ "LoadDefaultSigiloConfigurations", &UAuracronSigilosBridge::execLoadDefaultSigiloConfigurations },
		{ "OnRep_FusionState", &UAuracronSigilosBridge::execOnRep_FusionState },
		{ "OnRep_SelectedSigiloType", &UAuracronSigilosBridge::execOnRep_SelectedSigiloType },
		{ "ReforgeSigilo", &UAuracronSigilosBridge::execReforgeSigilo },
		{ "RemovePassiveBonuses", &UAuracronSigilosBridge::execRemovePassiveBonuses },
		{ "SelectSigilo", &UAuracronSigilosBridge::execSelectSigilo },
		{ "SetSigiloConfiguration", &UAuracronSigilosBridge::execSetSigiloConfiguration },
		{ "StartSigiloFusion", &UAuracronSigilosBridge::execStartSigiloFusion },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronSigilosBridge;
UClass* UAuracronSigilosBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronSigilosBridge;
	if (!Z_Registration_Info_UClass_UAuracronSigilosBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronSigilosBridge"),
			Z_Registration_Info_UClass_UAuracronSigilosBridge.InnerSingleton,
			StaticRegisterNativesUAuracronSigilosBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronSigilosBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronSigilosBridge_NoRegister()
{
	return UAuracronSigilosBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronSigilosBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Sigilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de S\xc3\x83\xc2\xadgilos Auracron\n * Respons\xc3\x83\xc2\xa1vel pela fus\xc3\x83\xc2\xa3o de campe\xc3\x83\xc2\xb5""es e gerenciamento de habilidades alternativas\n */" },
#endif
		{ "DisplayName", "AURACRON Sigilos Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronSigilosBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de S\xc3\x83\xc2\xadgilos Auracron\nRespons\xc3\x83\xc2\xa1vel pela fus\xc3\x83\xc2\xa3o de campe\xc3\x83\xc2\xb5""es e gerenciamento de habilidades alternativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloConfigurations_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es dos S\xc3\x83\xc2\xadgilos dispon\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es dos S\xc3\x83\xc2\xadgilos dispon\xc3\x83\xc2\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedSigiloType_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sigilo atualmente selecionado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sigilo atualmente selecionado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFusionState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual da fus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual da fus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionStartTime_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de jogo quando a fus\xc3\x83\xc2\xa3o foi iniciada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de jogo quando a fus\xc3\x83\xc2\xa3o foi iniciada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastReforgeTime_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo quando o \xc3\x83\xc2\xbaltimo re-forjamento foi feito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo quando o \xc3\x83\xc2\xbaltimo re-forjamento foi feito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTimeline_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de timeline para anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de timeline para anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigiloSelected_MetaData[] = {
		{ "Category", "AURACRON Sigilos|Events" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFusionStarted_MetaData[] = {
		{ "Category", "AURACRON Sigilos|Events" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFusionCompleted_MetaData[] = {
		{ "Category", "AURACRON Sigilos|Events" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigiloReforged_MetaData[] = {
		{ "Category", "AURACRON Sigilos|Events" },
		{ "ModuleRelativePath", "Public/AuracronSigilosBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigiloConfigurations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SigiloConfigurations;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SelectedSigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SelectedSigiloType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentFusionState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentFusionState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastReforgeTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusionTimeline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigiloSelected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFusionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFusionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigiloReforged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_ActivateExclusiveAbility, "ActivateExclusiveAbility" }, // 2696445869
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_ApplyPassiveBonuses, "ApplyPassiveBonuses" }, // 3807377010
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_CancelSigiloFusion, "CancelSigiloFusion" }, // 217925815
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_CanReforge, "CanReforge" }, // 2762706123
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_CompleteSigiloFusion, "CompleteSigiloFusion" }, // 1627994585
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_GetAlternativeAbilityTree, "GetAlternativeAbilityTree" }, // 1564380799
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_GetFusionState, "GetFusionState" }, // 2149550883
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_GetReforgeCooldownRemaining, "GetReforgeCooldownRemaining" }, // 3654831624
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_GetSelectedSigilo, "GetSelectedSigilo" }, // 3954605721
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_GetSigiloConfiguration, "GetSigiloConfiguration" }, // 50115321
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_GetTimeToFusion, "GetTimeToFusion" }, // 335972386
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_LoadDefaultSigiloConfigurations, "LoadDefaultSigiloConfigurations" }, // 1710269245
		{ &Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature, "OnFusionCompleted__DelegateSignature" }, // 3103771444
		{ &Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature, "OnFusionStarted__DelegateSignature" }, // 3324791630
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_FusionState, "OnRep_FusionState" }, // 3875200832
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_OnRep_SelectedSigiloType, "OnRep_SelectedSigiloType" }, // 1473910549
		{ &Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature, "OnSigiloReforged__DelegateSignature" }, // 3475555133
		{ &Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature, "OnSigiloSelected__DelegateSignature" }, // 644635176
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_ReforgeSigilo, "ReforgeSigilo" }, // 2770726120
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_RemovePassiveBonuses, "RemovePassiveBonuses" }, // 1891000465
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_SelectSigilo, "SelectSigilo" }, // 1687106365
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_SetSigiloConfiguration, "SetSigiloConfiguration" }, // 804384369
		{ &Z_Construct_UFunction_UAuracronSigilosBridge_StartSigiloFusion, "StartSigiloFusion" }, // 3382057803
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronSigilosBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SigiloConfigurations_Inner = { "SigiloConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry, METADATA_PARAMS(0, nullptr) }; // 323459298
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SigiloConfigurations = { "SigiloConfigurations", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, SigiloConfigurations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloConfigurations_MetaData), NewProp_SigiloConfigurations_MetaData) }; // 323459298
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SelectedSigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SelectedSigiloType = { "SelectedSigiloType", "OnRep_SelectedSigiloType", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, SelectedSigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedSigiloType_MetaData), NewProp_SelectedSigiloType_MetaData) }; // 2707057007
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_CurrentFusionState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_CurrentFusionState = { "CurrentFusionState", "OnRep_FusionState", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, CurrentFusionState), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloFusionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFusionState_MetaData), NewProp_CurrentFusionState_MetaData) }; // 822310332
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_FusionStartTime = { "FusionStartTime", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, FusionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionStartTime_MetaData), NewProp_FusionStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_LastReforgeTime = { "LastReforgeTime", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, LastReforgeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastReforgeTime_MetaData), NewProp_LastReforgeTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_FusionTimeline = { "FusionTimeline", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, FusionTimeline), Z_Construct_UClass_UTimelineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTimeline_MetaData), NewProp_FusionTimeline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnSigiloSelected = { "OnSigiloSelected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, OnSigiloSelected), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigiloSelected_MetaData), NewProp_OnSigiloSelected_MetaData) }; // 644635176
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnFusionStarted = { "OnFusionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, OnFusionStarted), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFusionStarted_MetaData), NewProp_OnFusionStarted_MetaData) }; // 3324791630
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnFusionCompleted = { "OnFusionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, OnFusionCompleted), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFusionCompleted_MetaData), NewProp_OnFusionCompleted_MetaData) }; // 3103771444
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnSigiloReforged = { "OnSigiloReforged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilosBridge, OnSigiloReforged), Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigiloReforged_MetaData), NewProp_OnSigiloReforged_MetaData) }; // 3475555133
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronSigilosBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SigiloConfigurations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SigiloConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SelectedSigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_SelectedSigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_CurrentFusionState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_CurrentFusionState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_FusionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_LastReforgeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_FusionTimeline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnSigiloSelected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnFusionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnFusionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilosBridge_Statics::NewProp_OnSigiloReforged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilosBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronSigilosBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilosBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronSigilosBridge_Statics::ClassParams = {
	&UAuracronSigilosBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronSigilosBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilosBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilosBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronSigilosBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronSigilosBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronSigilosBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronSigilosBridge.OuterSingleton, Z_Construct_UClass_UAuracronSigilosBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronSigilosBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronSigilosBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_SigiloConfigurations(TEXT("SigiloConfigurations"));
	static FName Name_SelectedSigiloType(TEXT("SelectedSigiloType"));
	static FName Name_CurrentFusionState(TEXT("CurrentFusionState"));
	static FName Name_FusionStartTime(TEXT("FusionStartTime"));
	static FName Name_LastReforgeTime(TEXT("LastReforgeTime"));
	const bool bIsValid = true
		&& Name_SigiloConfigurations == ClassReps[(int32)ENetFields_Private::SigiloConfigurations].Property->GetFName()
		&& Name_SelectedSigiloType == ClassReps[(int32)ENetFields_Private::SelectedSigiloType].Property->GetFName()
		&& Name_CurrentFusionState == ClassReps[(int32)ENetFields_Private::CurrentFusionState].Property->GetFName()
		&& Name_FusionStartTime == ClassReps[(int32)ENetFields_Private::FusionStartTime].Property->GetFName()
		&& Name_LastReforgeTime == ClassReps[(int32)ENetFields_Private::LastReforgeTime].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronSigilosBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronSigilosBridge);
UAuracronSigilosBridge::~UAuracronSigilosBridge() {}
// ********** End Class UAuracronSigilosBridge *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronSigiloType_StaticEnum, TEXT("EAuracronSigiloType"), &Z_Registration_Info_UEnum_EAuracronSigiloType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2707057007U) },
		{ EAuracronSigiloFusionState_StaticEnum, TEXT("EAuracronSigiloFusionState"), &Z_Registration_Info_UEnum_EAuracronSigiloFusionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 822310332U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronSigiloProperties::StaticStruct, Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics::NewStructOps, TEXT("AuracronSigiloProperties"), &Z_Registration_Info_UScriptStruct_FAuracronSigiloProperties, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSigiloProperties), 3024668222U) },
		{ FAuracronSigiloPassiveBonuses::StaticStruct, Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics::NewStructOps, TEXT("AuracronSigiloPassiveBonuses"), &Z_Registration_Info_UScriptStruct_FAuracronSigiloPassiveBonuses, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSigiloPassiveBonuses), 4259013295U) },
		{ FAuracronSigiloConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics::NewStructOps, TEXT("AuracronSigiloConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronSigiloConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSigiloConfiguration), 4254436308U) },
		{ FAuracronSigiloConfigurationEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics::NewStructOps, TEXT("AuracronSigiloConfigurationEntry"), &Z_Registration_Info_UScriptStruct_FAuracronSigiloConfigurationEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSigiloConfigurationEntry), 323459298U) },
		{ FAuracronSigiloExclusiveAbility::StaticStruct, Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics::NewStructOps, TEXT("AuracronSigiloExclusiveAbility"), &Z_Registration_Info_UScriptStruct_FAuracronSigiloExclusiveAbility, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSigiloExclusiveAbility), 3758181075U) },
		{ FAuracronSigiloVisualEffects::StaticStruct, Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics::NewStructOps, TEXT("AuracronSigiloVisualEffects"), &Z_Registration_Info_UScriptStruct_FAuracronSigiloVisualEffects, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSigiloVisualEffects), 1405659607U) },
		{ FAuracronSigiloAudioEffects::StaticStruct, Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics::NewStructOps, TEXT("AuracronSigiloAudioEffects"), &Z_Registration_Info_UScriptStruct_FAuracronSigiloAudioEffects, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSigiloAudioEffects), 2780692919U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronSigilosBridge, UAuracronSigilosBridge::StaticClass, TEXT("UAuracronSigilosBridge"), &Z_Registration_Info_UClass_UAuracronSigilosBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronSigilosBridge), 2088596554U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_663625693(TEXT("/Script/AuracronSigilosBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h__Script_AuracronSigilosBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
