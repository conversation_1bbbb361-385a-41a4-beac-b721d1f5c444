// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageInteraction.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageInteraction() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInteractionManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInteractionManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageBendingData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlayerInteractionData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRecoverySimulationData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPlayerInteractionType ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPlayerInteractionType;
static UEnum* EAuracronPlayerInteractionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPlayerInteractionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPlayerInteractionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronPlayerInteractionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPlayerInteractionType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPlayerInteractionType>()
{
	return EAuracronPlayerInteractionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Attack.DisplayName", "Attack" },
		{ "Attack.Name", "EAuracronPlayerInteractionType::Attack" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player interaction types\n" },
#endif
		{ "Crouch.DisplayName", "Crouch Near" },
		{ "Crouch.Name", "EAuracronPlayerInteractionType::Crouch" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPlayerInteractionType::Custom" },
		{ "Harvest.DisplayName", "Harvest" },
		{ "Harvest.Name", "EAuracronPlayerInteractionType::Harvest" },
		{ "Jump.DisplayName", "Jump On" },
		{ "Jump.Name", "EAuracronPlayerInteractionType::Jump" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPlayerInteractionType::None" },
		{ "Push.DisplayName", "Push" },
		{ "Push.Name", "EAuracronPlayerInteractionType::Push" },
		{ "Run.DisplayName", "Run Through" },
		{ "Run.Name", "EAuracronPlayerInteractionType::Run" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player interaction types" },
#endif
		{ "Touch.DisplayName", "Touch" },
		{ "Touch.Name", "EAuracronPlayerInteractionType::Touch" },
		{ "Walk.DisplayName", "Walk Through" },
		{ "Walk.Name", "EAuracronPlayerInteractionType::Walk" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPlayerInteractionType::None", (int64)EAuracronPlayerInteractionType::None },
		{ "EAuracronPlayerInteractionType::Touch", (int64)EAuracronPlayerInteractionType::Touch },
		{ "EAuracronPlayerInteractionType::Push", (int64)EAuracronPlayerInteractionType::Push },
		{ "EAuracronPlayerInteractionType::Walk", (int64)EAuracronPlayerInteractionType::Walk },
		{ "EAuracronPlayerInteractionType::Run", (int64)EAuracronPlayerInteractionType::Run },
		{ "EAuracronPlayerInteractionType::Jump", (int64)EAuracronPlayerInteractionType::Jump },
		{ "EAuracronPlayerInteractionType::Crouch", (int64)EAuracronPlayerInteractionType::Crouch },
		{ "EAuracronPlayerInteractionType::Attack", (int64)EAuracronPlayerInteractionType::Attack },
		{ "EAuracronPlayerInteractionType::Harvest", (int64)EAuracronPlayerInteractionType::Harvest },
		{ "EAuracronPlayerInteractionType::Custom", (int64)EAuracronPlayerInteractionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronPlayerInteractionType",
	"EAuracronPlayerInteractionType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPlayerInteractionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPlayerInteractionType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPlayerInteractionType.InnerSingleton;
}
// ********** End Enum EAuracronPlayerInteractionType **********************************************

// ********** Begin Enum EAuracronFoliageBendingType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageBendingType;
static UEnum* EAuracronFoliageBendingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageBendingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageBendingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageBendingType"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageBendingType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageBendingType>()
{
	return EAuracronFoliageBendingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage bending types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronFoliageBendingType::Custom" },
		{ "Directional.DisplayName", "Directional Bend" },
		{ "Directional.Name", "EAuracronFoliageBendingType::Directional" },
		{ "Extreme.DisplayName", "Extreme Bend" },
		{ "Extreme.Name", "EAuracronFoliageBendingType::Extreme" },
		{ "Gentle.DisplayName", "Gentle Bend" },
		{ "Gentle.Name", "EAuracronFoliageBendingType::Gentle" },
		{ "Moderate.DisplayName", "Moderate Bend" },
		{ "Moderate.Name", "EAuracronFoliageBendingType::Moderate" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronFoliageBendingType::None" },
		{ "Radial.DisplayName", "Radial Bend" },
		{ "Radial.Name", "EAuracronFoliageBendingType::Radial" },
		{ "Strong.DisplayName", "Strong Bend" },
		{ "Strong.Name", "EAuracronFoliageBendingType::Strong" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage bending types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageBendingType::None", (int64)EAuracronFoliageBendingType::None },
		{ "EAuracronFoliageBendingType::Gentle", (int64)EAuracronFoliageBendingType::Gentle },
		{ "EAuracronFoliageBendingType::Moderate", (int64)EAuracronFoliageBendingType::Moderate },
		{ "EAuracronFoliageBendingType::Strong", (int64)EAuracronFoliageBendingType::Strong },
		{ "EAuracronFoliageBendingType::Extreme", (int64)EAuracronFoliageBendingType::Extreme },
		{ "EAuracronFoliageBendingType::Directional", (int64)EAuracronFoliageBendingType::Directional },
		{ "EAuracronFoliageBendingType::Radial", (int64)EAuracronFoliageBendingType::Radial },
		{ "EAuracronFoliageBendingType::Custom", (int64)EAuracronFoliageBendingType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageBendingType",
	"EAuracronFoliageBendingType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageBendingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageBendingType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageBendingType.InnerSingleton;
}
// ********** End Enum EAuracronFoliageBendingType *************************************************

// ********** Begin Enum EAuracronRecoverySimulationType *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronRecoverySimulationType;
static UEnum* EAuracronRecoverySimulationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronRecoverySimulationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronRecoverySimulationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronRecoverySimulationType"));
	}
	return Z_Registration_Info_UEnum_EAuracronRecoverySimulationType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronRecoverySimulationType>()
{
	return EAuracronRecoverySimulationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Recovery simulation types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronRecoverySimulationType::Custom" },
		{ "Damped.DisplayName", "Damped Recovery" },
		{ "Damped.Name", "EAuracronRecoverySimulationType::Damped" },
		{ "Elastic.DisplayName", "Elastic Recovery" },
		{ "Elastic.Name", "EAuracronRecoverySimulationType::Elastic" },
		{ "Exponential.DisplayName", "Exponential Recovery" },
		{ "Exponential.Name", "EAuracronRecoverySimulationType::Exponential" },
		{ "Instant.DisplayName", "Instant Recovery" },
		{ "Instant.Name", "EAuracronRecoverySimulationType::Instant" },
		{ "Linear.DisplayName", "Linear Recovery" },
		{ "Linear.Name", "EAuracronRecoverySimulationType::Linear" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronRecoverySimulationType::None" },
		{ "Spring.DisplayName", "Spring Recovery" },
		{ "Spring.Name", "EAuracronRecoverySimulationType::Spring" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recovery simulation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronRecoverySimulationType::None", (int64)EAuracronRecoverySimulationType::None },
		{ "EAuracronRecoverySimulationType::Instant", (int64)EAuracronRecoverySimulationType::Instant },
		{ "EAuracronRecoverySimulationType::Linear", (int64)EAuracronRecoverySimulationType::Linear },
		{ "EAuracronRecoverySimulationType::Exponential", (int64)EAuracronRecoverySimulationType::Exponential },
		{ "EAuracronRecoverySimulationType::Spring", (int64)EAuracronRecoverySimulationType::Spring },
		{ "EAuracronRecoverySimulationType::Elastic", (int64)EAuracronRecoverySimulationType::Elastic },
		{ "EAuracronRecoverySimulationType::Damped", (int64)EAuracronRecoverySimulationType::Damped },
		{ "EAuracronRecoverySimulationType::Custom", (int64)EAuracronRecoverySimulationType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronRecoverySimulationType",
	"EAuracronRecoverySimulationType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType()
{
	if (!Z_Registration_Info_UEnum_EAuracronRecoverySimulationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronRecoverySimulationType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronRecoverySimulationType.InnerSingleton;
}
// ********** End Enum EAuracronRecoverySimulationType *********************************************

// ********** Begin Enum EAuracronInteractionResponseType ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronInteractionResponseType;
static UEnum* EAuracronInteractionResponseType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronInteractionResponseType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronInteractionResponseType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronInteractionResponseType"));
	}
	return Z_Registration_Info_UEnum_EAuracronInteractionResponseType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInteractionResponseType>()
{
	return EAuracronInteractionResponseType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Audio.DisplayName", "Audio Response" },
		{ "Audio.Name", "EAuracronInteractionResponseType::Audio" },
		{ "BlueprintType", "true" },
		{ "Combined.DisplayName", "Combined Response" },
		{ "Combined.Name", "EAuracronInteractionResponseType::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interaction response types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronInteractionResponseType::Custom" },
		{ "Gameplay.DisplayName", "Gameplay Effect" },
		{ "Gameplay.Name", "EAuracronInteractionResponseType::Gameplay" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronInteractionResponseType::None" },
		{ "Particle.DisplayName", "Particle Effects" },
		{ "Particle.Name", "EAuracronInteractionResponseType::Particle" },
		{ "Physics.DisplayName", "Physics Response" },
		{ "Physics.Name", "EAuracronInteractionResponseType::Physics" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interaction response types" },
#endif
		{ "Visual.DisplayName", "Visual Only" },
		{ "Visual.Name", "EAuracronInteractionResponseType::Visual" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronInteractionResponseType::None", (int64)EAuracronInteractionResponseType::None },
		{ "EAuracronInteractionResponseType::Visual", (int64)EAuracronInteractionResponseType::Visual },
		{ "EAuracronInteractionResponseType::Physics", (int64)EAuracronInteractionResponseType::Physics },
		{ "EAuracronInteractionResponseType::Audio", (int64)EAuracronInteractionResponseType::Audio },
		{ "EAuracronInteractionResponseType::Particle", (int64)EAuracronInteractionResponseType::Particle },
		{ "EAuracronInteractionResponseType::Combined", (int64)EAuracronInteractionResponseType::Combined },
		{ "EAuracronInteractionResponseType::Gameplay", (int64)EAuracronInteractionResponseType::Gameplay },
		{ "EAuracronInteractionResponseType::Custom", (int64)EAuracronInteractionResponseType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronInteractionResponseType",
	"EAuracronInteractionResponseType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType()
{
	if (!Z_Registration_Info_UEnum_EAuracronInteractionResponseType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronInteractionResponseType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronInteractionResponseType.InnerSingleton;
}
// ********** End Enum EAuracronInteractionResponseType ********************************************

// ********** Begin ScriptStruct FAuracronFoliageInteractionConfiguration **************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration;
class UScriptStruct* FAuracronFoliageInteractionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageInteractionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Interaction Configuration\n * Configuration for foliage interaction system behavior\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Interaction Configuration\nConfiguration for foliage interaction system behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInteractionSystem_MetaData[] = {
		{ "Category", "Interaction System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultInteractionType_MetaData[] = {
		{ "Category", "Interaction System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultResponseType_MetaData[] = {
		{ "Category", "Interaction System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionRadius_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionForce_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionSensitivity_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFoliageBending_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultBendingType_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingStrength_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingRadius_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBendAngle_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRecoverySimulation_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultRecoveryType_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryTime_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoverySpeed_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryDamping_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdvancedTrampling_MetaData[] = {
		{ "Category", "Trampling Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingThreshold_MetaData[] = {
		{ "Category", "Trampling Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingDuration_MetaData[] = {
		{ "Category", "Trampling Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncInteractionUpdates_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInteractionUpdatesPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInteractionDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInteractionAudio_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioVolumeMultiplier_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioPitchVariation_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableParticleEffects_MetaData[] = {
		{ "Category", "Visual Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticleIntensity_MetaData[] = {
		{ "Category", "Visual Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticleLifetime_MetaData[] = {
		{ "Category", "Visual Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableInteractionSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInteractionSystem;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultInteractionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultInteractionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultResponseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultResponseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionSensitivity;
	static void NewProp_bEnableFoliageBending_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFoliageBending;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultBendingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultBendingType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxBendAngle;
	static void NewProp_bEnableRecoverySimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRecoverySimulation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultRecoveryType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultRecoveryType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoverySpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryDamping;
	static void NewProp_bEnableAdvancedTrampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedTrampling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingDuration;
	static void NewProp_bEnableAsyncInteractionUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncInteractionUpdates;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInteractionUpdatesPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxInteractionDistance;
	static void NewProp_bEnableInteractionAudio_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInteractionAudio;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AudioVolumeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AudioPitchVariation;
	static void NewProp_bEnableParticleEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableParticleEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ParticleIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ParticleLifetime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageInteractionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionSystem_SetBit(void* Obj)
{
	((FAuracronFoliageInteractionConfiguration*)Obj)->bEnableInteractionSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionSystem = { "bEnableInteractionSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInteractionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInteractionSystem_MetaData), NewProp_bEnableInteractionSystem_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultInteractionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultInteractionType = { "DefaultInteractionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, DefaultInteractionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultInteractionType_MetaData), NewProp_DefaultInteractionType_MetaData) }; // 628606094
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultResponseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultResponseType = { "DefaultResponseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, DefaultResponseType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInteractionResponseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultResponseType_MetaData), NewProp_DefaultResponseType_MetaData) }; // 2236845358
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionRadius = { "InteractionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, InteractionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionRadius_MetaData), NewProp_InteractionRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionForce = { "InteractionForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, InteractionForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionForce_MetaData), NewProp_InteractionForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionSensitivity = { "InteractionSensitivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, InteractionSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionSensitivity_MetaData), NewProp_InteractionSensitivity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableFoliageBending_SetBit(void* Obj)
{
	((FAuracronFoliageInteractionConfiguration*)Obj)->bEnableFoliageBending = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableFoliageBending = { "bEnableFoliageBending", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInteractionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableFoliageBending_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFoliageBending_MetaData), NewProp_bEnableFoliageBending_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultBendingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultBendingType = { "DefaultBendingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, DefaultBendingType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultBendingType_MetaData), NewProp_DefaultBendingType_MetaData) }; // 2808574338
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_BendingStrength = { "BendingStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, BendingStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingStrength_MetaData), NewProp_BendingStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_BendingRadius = { "BendingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, BendingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingRadius_MetaData), NewProp_BendingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_MaxBendAngle = { "MaxBendAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, MaxBendAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBendAngle_MetaData), NewProp_MaxBendAngle_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableRecoverySimulation_SetBit(void* Obj)
{
	((FAuracronFoliageInteractionConfiguration*)Obj)->bEnableRecoverySimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableRecoverySimulation = { "bEnableRecoverySimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInteractionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableRecoverySimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRecoverySimulation_MetaData), NewProp_bEnableRecoverySimulation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultRecoveryType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultRecoveryType = { "DefaultRecoveryType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, DefaultRecoveryType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultRecoveryType_MetaData), NewProp_DefaultRecoveryType_MetaData) }; // 1803980840
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_RecoveryTime = { "RecoveryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, RecoveryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryTime_MetaData), NewProp_RecoveryTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_RecoverySpeed = { "RecoverySpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, RecoverySpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoverySpeed_MetaData), NewProp_RecoverySpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_RecoveryDamping = { "RecoveryDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, RecoveryDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryDamping_MetaData), NewProp_RecoveryDamping_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAdvancedTrampling_SetBit(void* Obj)
{
	((FAuracronFoliageInteractionConfiguration*)Obj)->bEnableAdvancedTrampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAdvancedTrampling = { "bEnableAdvancedTrampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInteractionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAdvancedTrampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdvancedTrampling_MetaData), NewProp_bEnableAdvancedTrampling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_TramplingThreshold = { "TramplingThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, TramplingThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingThreshold_MetaData), NewProp_TramplingThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_TramplingDuration = { "TramplingDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, TramplingDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingDuration_MetaData), NewProp_TramplingDuration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAsyncInteractionUpdates_SetBit(void* Obj)
{
	((FAuracronFoliageInteractionConfiguration*)Obj)->bEnableAsyncInteractionUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAsyncInteractionUpdates = { "bEnableAsyncInteractionUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInteractionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAsyncInteractionUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncInteractionUpdates_MetaData), NewProp_bEnableAsyncInteractionUpdates_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_MaxInteractionUpdatesPerFrame = { "MaxInteractionUpdatesPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, MaxInteractionUpdatesPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInteractionUpdatesPerFrame_MetaData), NewProp_MaxInteractionUpdatesPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionUpdateInterval = { "InteractionUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, InteractionUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionUpdateInterval_MetaData), NewProp_InteractionUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_MaxInteractionDistance = { "MaxInteractionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, MaxInteractionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInteractionDistance_MetaData), NewProp_MaxInteractionDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionAudio_SetBit(void* Obj)
{
	((FAuracronFoliageInteractionConfiguration*)Obj)->bEnableInteractionAudio = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionAudio = { "bEnableInteractionAudio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInteractionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionAudio_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInteractionAudio_MetaData), NewProp_bEnableInteractionAudio_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_AudioVolumeMultiplier = { "AudioVolumeMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, AudioVolumeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioVolumeMultiplier_MetaData), NewProp_AudioVolumeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_AudioPitchVariation = { "AudioPitchVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, AudioPitchVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioPitchVariation_MetaData), NewProp_AudioPitchVariation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableParticleEffects_SetBit(void* Obj)
{
	((FAuracronFoliageInteractionConfiguration*)Obj)->bEnableParticleEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableParticleEffects = { "bEnableParticleEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInteractionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableParticleEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableParticleEffects_MetaData), NewProp_bEnableParticleEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_ParticleIntensity = { "ParticleIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, ParticleIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticleIntensity_MetaData), NewProp_ParticleIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_ParticleLifetime = { "ParticleLifetime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInteractionConfiguration, ParticleLifetime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticleLifetime_MetaData), NewProp_ParticleLifetime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultInteractionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultInteractionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultResponseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultResponseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableFoliageBending,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultBendingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultBendingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_BendingStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_BendingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_MaxBendAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableRecoverySimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultRecoveryType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_DefaultRecoveryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_RecoveryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_RecoverySpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_RecoveryDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAdvancedTrampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_TramplingThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_TramplingDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableAsyncInteractionUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_MaxInteractionUpdatesPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_InteractionUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_MaxInteractionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableInteractionAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_AudioVolumeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_AudioPitchVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_bEnableParticleEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_ParticleIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewProp_ParticleLifetime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageInteractionConfiguration",
	Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::PropPointers),
	sizeof(FAuracronFoliageInteractionConfiguration),
	alignof(FAuracronFoliageInteractionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageInteractionConfiguration ****************************

// ********** Begin ScriptStruct FAuracronPlayerInteractionData ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData;
class UScriptStruct* FAuracronPlayerInteractionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPlayerInteractionData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronPlayerInteractionData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Player Interaction Data\n * Data for player interactions with foliage\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player Interaction Data\nData for player interactions with foliage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionId_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractingPlayer_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionType_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionLocation_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionDirection_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionForce_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionRadius_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionIntensity_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerVelocity_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerMass_MetaData[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedFoliageInstances_MetaData[] = {
		{ "Category", "Affected Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionStartTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionDuration_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InteractionId;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_InteractingPlayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InteractionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InteractionType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractionLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractionDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerVelocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerMass;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AffectedFoliageInstances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AffectedFoliageInstances;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractionStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPlayerInteractionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionId = { "InteractionId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionId_MetaData), NewProp_InteractionId_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractingPlayer = { "InteractingPlayer", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractingPlayer), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractingPlayer_MetaData), NewProp_InteractingPlayer_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionType = { "InteractionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionType_MetaData), NewProp_InteractionType_MetaData) }; // 628606094
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionLocation = { "InteractionLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionLocation_MetaData), NewProp_InteractionLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionDirection = { "InteractionDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionDirection_MetaData), NewProp_InteractionDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionForce = { "InteractionForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionForce_MetaData), NewProp_InteractionForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionRadius = { "InteractionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionRadius_MetaData), NewProp_InteractionRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionIntensity = { "InteractionIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionIntensity_MetaData), NewProp_InteractionIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_PlayerVelocity = { "PlayerVelocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, PlayerVelocity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerVelocity_MetaData), NewProp_PlayerVelocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_PlayerMass = { "PlayerMass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, PlayerMass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerMass_MetaData), NewProp_PlayerMass_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_AffectedFoliageInstances_Inner = { "AffectedFoliageInstances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_AffectedFoliageInstances = { "AffectedFoliageInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, AffectedFoliageInstances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedFoliageInstances_MetaData), NewProp_AffectedFoliageInstances_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronPlayerInteractionData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPlayerInteractionData), &Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionStartTime = { "InteractionStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionStartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionStartTime_MetaData), NewProp_InteractionStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionDuration = { "InteractionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInteractionData, InteractionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionDuration_MetaData), NewProp_InteractionDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractingPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_PlayerVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_PlayerMass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_AffectedFoliageInstances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_AffectedFoliageInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewProp_InteractionDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronPlayerInteractionData",
	Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::PropPointers),
	sizeof(FAuracronPlayerInteractionData),
	alignof(FAuracronPlayerInteractionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlayerInteractionData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPlayerInteractionData **************************************

// ********** Begin ScriptStruct FAuracronFoliageBendingData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData;
class UScriptStruct* FAuracronFoliageBendingData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageBendingData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageBendingData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Bending Data\n * Data for foliage bending effects\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Bending Data\nData for foliage bending effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingId_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingType_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingDirection_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingAngle_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBendingAngle_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingStrength_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingSpeed_MetaData[] = {
		{ "Category", "Foliage Bending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalTransform_MetaData[] = {
		{ "Category", "Original State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalRotation_MetaData[] = {
		{ "Category", "Original State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransform_MetaData[] = {
		{ "Category", "Current State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBendProgress_MetaData[] = {
		{ "Category", "Current State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRecovering_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryProgress_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingStartTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BendingId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BendingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BendingType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BendingDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxBendingAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalTransform;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentTransform;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentBendProgress;
	static void NewProp_bIsRecovering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRecovering;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryProgress;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BendingStartTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageBendingData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingId = { "BendingId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, BendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingId_MetaData), NewProp_BendingId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingType = { "BendingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, BendingType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageBendingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingType_MetaData), NewProp_BendingType_MetaData) }; // 2808574338
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingDirection = { "BendingDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, BendingDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingDirection_MetaData), NewProp_BendingDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingAngle = { "BendingAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, BendingAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingAngle_MetaData), NewProp_BendingAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_MaxBendingAngle = { "MaxBendingAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, MaxBendingAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBendingAngle_MetaData), NewProp_MaxBendingAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingStrength = { "BendingStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, BendingStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingStrength_MetaData), NewProp_BendingStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingSpeed = { "BendingSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, BendingSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingSpeed_MetaData), NewProp_BendingSpeed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_OriginalTransform = { "OriginalTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, OriginalTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalTransform_MetaData), NewProp_OriginalTransform_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_OriginalRotation = { "OriginalRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, OriginalRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalRotation_MetaData), NewProp_OriginalRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_CurrentTransform = { "CurrentTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, CurrentTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransform_MetaData), NewProp_CurrentTransform_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_CurrentBendProgress = { "CurrentBendProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, CurrentBendProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBendProgress_MetaData), NewProp_CurrentBendProgress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsRecovering_SetBit(void* Obj)
{
	((FAuracronFoliageBendingData*)Obj)->bIsRecovering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsRecovering = { "bIsRecovering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageBendingData), &Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsRecovering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRecovering_MetaData), NewProp_bIsRecovering_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_RecoveryProgress = { "RecoveryProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, RecoveryProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryProgress_MetaData), NewProp_RecoveryProgress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronFoliageBendingData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageBendingData), &Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingStartTime = { "BendingStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageBendingData, BendingStartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingStartTime_MetaData), NewProp_BendingStartTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_MaxBendingAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_OriginalTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_OriginalRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_CurrentTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_CurrentBendProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsRecovering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_RecoveryProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewProp_BendingStartTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageBendingData",
	Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::PropPointers),
	sizeof(FAuracronFoliageBendingData),
	alignof(FAuracronFoliageBendingData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageBendingData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageBendingData *****************************************

// ********** Begin ScriptStruct FAuracronRecoverySimulationData ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData;
class UScriptStruct* FAuracronRecoverySimulationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRecoverySimulationData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronRecoverySimulationData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Recovery Simulation Data\n * Data for foliage recovery simulation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recovery Simulation Data\nData for foliage recovery simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryId_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryType_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryTime_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoverySpeed_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryDamping_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpringConstant_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElasticityFactor_MetaData[] = {
		{ "Category", "Recovery Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetTransform_MetaData[] = {
		{ "Category", "Target State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetRotation_MetaData[] = {
		{ "Category", "Target State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransform_MetaData[] = {
		{ "Category", "Current State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryProgress_MetaData[] = {
		{ "Category", "Current State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "Category", "Current State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Acceleration_MetaData[] = {
		{ "Category", "Current State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRecovering_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryStartTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecoveryId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RecoveryType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RecoveryType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoverySpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpringConstant;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElasticityFactor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetTransform;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentTransform;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Acceleration;
	static void NewProp_bIsRecovering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRecovering;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RecoveryStartTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRecoverySimulationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryId = { "RecoveryId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, RecoveryId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryId_MetaData), NewProp_RecoveryId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryType = { "RecoveryType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, RecoveryType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryType_MetaData), NewProp_RecoveryType_MetaData) }; // 1803980840
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryTime = { "RecoveryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, RecoveryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryTime_MetaData), NewProp_RecoveryTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoverySpeed = { "RecoverySpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, RecoverySpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoverySpeed_MetaData), NewProp_RecoverySpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryDamping = { "RecoveryDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, RecoveryDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryDamping_MetaData), NewProp_RecoveryDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_SpringConstant = { "SpringConstant", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, SpringConstant), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpringConstant_MetaData), NewProp_SpringConstant_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_ElasticityFactor = { "ElasticityFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, ElasticityFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElasticityFactor_MetaData), NewProp_ElasticityFactor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_TargetTransform = { "TargetTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, TargetTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetTransform_MetaData), NewProp_TargetTransform_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_TargetRotation = { "TargetRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, TargetRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetRotation_MetaData), NewProp_TargetRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_CurrentTransform = { "CurrentTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, CurrentTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransform_MetaData), NewProp_CurrentTransform_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryProgress = { "RecoveryProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, RecoveryProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryProgress_MetaData), NewProp_RecoveryProgress_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_Acceleration = { "Acceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, Acceleration), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Acceleration_MetaData), NewProp_Acceleration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_bIsRecovering_SetBit(void* Obj)
{
	((FAuracronRecoverySimulationData*)Obj)->bIsRecovering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_bIsRecovering = { "bIsRecovering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRecoverySimulationData), &Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_bIsRecovering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRecovering_MetaData), NewProp_bIsRecovering_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryStartTime = { "RecoveryStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRecoverySimulationData, RecoveryStartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryStartTime_MetaData), NewProp_RecoveryStartTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoverySpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_SpringConstant,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_ElasticityFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_TargetTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_TargetRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_CurrentTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_Acceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_bIsRecovering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewProp_RecoveryStartTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronRecoverySimulationData",
	Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::PropPointers),
	sizeof(FAuracronRecoverySimulationData),
	alignof(FAuracronRecoverySimulationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRecoverySimulationData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRecoverySimulationData *************************************

// ********** Begin ScriptStruct FAuracronInteractionPerformanceData *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData;
class UScriptStruct* FAuracronInteractionPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronInteractionPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Interaction Performance Data\n * Performance metrics for interaction system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interaction Performance Data\nPerformance metrics for interaction system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalInteractions_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveInteractions_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveringInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerInteractions_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalInteractions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveInteractions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BendingInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RecoveringInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryUpdateTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerInteractions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronInteractionPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_TotalInteractions = { "TotalInteractions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, TotalInteractions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalInteractions_MetaData), NewProp_TotalInteractions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_ActiveInteractions = { "ActiveInteractions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, ActiveInteractions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveInteractions_MetaData), NewProp_ActiveInteractions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_BendingInstances = { "BendingInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, BendingInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingInstances_MetaData), NewProp_BendingInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_RecoveringInstances = { "RecoveringInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, RecoveringInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveringInstances_MetaData), NewProp_RecoveringInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_InteractionUpdateTime = { "InteractionUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, InteractionUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionUpdateTime_MetaData), NewProp_InteractionUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_BendingUpdateTime = { "BendingUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, BendingUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingUpdateTime_MetaData), NewProp_BendingUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_RecoveryUpdateTime = { "RecoveryUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, RecoveryUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryUpdateTime_MetaData), NewProp_RecoveryUpdateTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_PlayerInteractions = { "PlayerInteractions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, PlayerInteractions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerInteractions_MetaData), NewProp_PlayerInteractions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInteractionPerformanceData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_TotalInteractions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_ActiveInteractions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_BendingInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_RecoveringInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_InteractionUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_BendingUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_RecoveryUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_PlayerInteractions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronInteractionPerformanceData",
	Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::PropPointers),
	sizeof(FAuracronInteractionPerformanceData),
	alignof(FAuracronInteractionPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronInteractionPerformanceData *********************************

// ********** Begin Delegate FOnPlayerInteractionStarted *******************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics
{
	struct AuracronFoliageInteractionManager_eventOnPlayerInteractionStarted_Parms
	{
		FString InteractionId;
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InteractionId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::NewProp_InteractionId = { "InteractionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventOnPlayerInteractionStarted_Parms, InteractionId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventOnPlayerInteractionStarted_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::NewProp_InteractionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "OnPlayerInteractionStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnPlayerInteractionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnPlayerInteractionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInteractionManager::FOnPlayerInteractionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerInteractionStarted, const FString& InteractionId, APawn* Player)
{
	struct AuracronFoliageInteractionManager_eventOnPlayerInteractionStarted_Parms
	{
		FString InteractionId;
		APawn* Player;
	};
	AuracronFoliageInteractionManager_eventOnPlayerInteractionStarted_Parms Parms;
	Parms.InteractionId=InteractionId;
	Parms.Player=Player;
	OnPlayerInteractionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerInteractionStarted *********************************************

// ********** Begin Delegate FOnPlayerInteractionEnded *********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics
{
	struct AuracronFoliageInteractionManager_eventOnPlayerInteractionEnded_Parms
	{
		FString InteractionId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InteractionId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::NewProp_InteractionId = { "InteractionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventOnPlayerInteractionEnded_Parms, InteractionId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::NewProp_InteractionId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "OnPlayerInteractionEnded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnPlayerInteractionEnded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnPlayerInteractionEnded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInteractionManager::FOnPlayerInteractionEnded_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerInteractionEnded, const FString& InteractionId)
{
	struct AuracronFoliageInteractionManager_eventOnPlayerInteractionEnded_Parms
	{
		FString InteractionId;
	};
	AuracronFoliageInteractionManager_eventOnPlayerInteractionEnded_Parms Parms;
	Parms.InteractionId=InteractionId;
	OnPlayerInteractionEnded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerInteractionEnded ***********************************************

// ********** Begin Delegate FOnFoliageBendingStarted **********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics
{
	struct AuracronFoliageInteractionManager_eventOnFoliageBendingStarted_Parms
	{
		FString BendingId;
		FString FoliageInstanceId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BendingId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::NewProp_BendingId = { "BendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventOnFoliageBendingStarted_Parms, BendingId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventOnFoliageBendingStarted_Parms, FoliageInstanceId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::NewProp_BendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::NewProp_FoliageInstanceId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "OnFoliageBendingStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnFoliageBendingStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnFoliageBendingStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInteractionManager::FOnFoliageBendingStarted_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageBendingStarted, const FString& BendingId, const FString& FoliageInstanceId)
{
	struct AuracronFoliageInteractionManager_eventOnFoliageBendingStarted_Parms
	{
		FString BendingId;
		FString FoliageInstanceId;
	};
	AuracronFoliageInteractionManager_eventOnFoliageBendingStarted_Parms Parms;
	Parms.BendingId=BendingId;
	Parms.FoliageInstanceId=FoliageInstanceId;
	OnFoliageBendingStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageBendingStarted ************************************************

// ********** Begin Delegate FOnFoliageRecoveryCompleted *******************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics
{
	struct AuracronFoliageInteractionManager_eventOnFoliageRecoveryCompleted_Parms
	{
		FString RecoveryId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecoveryId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::NewProp_RecoveryId = { "RecoveryId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventOnFoliageRecoveryCompleted_Parms, RecoveryId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::NewProp_RecoveryId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "OnFoliageRecoveryCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnFoliageRecoveryCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::AuracronFoliageInteractionManager_eventOnFoliageRecoveryCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInteractionManager::FOnFoliageRecoveryCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageRecoveryCompleted, const FString& RecoveryId)
{
	struct AuracronFoliageInteractionManager_eventOnFoliageRecoveryCompleted_Parms
	{
		FString RecoveryId;
	};
	AuracronFoliageInteractionManager_eventOnFoliageRecoveryCompleted_Parms Parms;
	Parms.RecoveryId=RecoveryId;
	OnFoliageRecoveryCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageRecoveryCompleted *********************************************

// ********** Begin Class UAuracronFoliageInteractionManager Function ApplyAdvancedTrampling *******
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics
{
	struct AuracronFoliageInteractionManager_eventApplyAdvancedTrampling_Parms
	{
		FVector Location;
		float Radius;
		float Force;
		APawn* CausingPlayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced trampling effects\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced trampling effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CausingPlayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplyAdvancedTrampling_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplyAdvancedTrampling_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplyAdvancedTrampling_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_CausingPlayer = { "CausingPlayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplyAdvancedTrampling_Parms, CausingPlayer), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::NewProp_CausingPlayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "ApplyAdvancedTrampling", Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::AuracronFoliageInteractionManager_eventApplyAdvancedTrampling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::AuracronFoliageInteractionManager_eventApplyAdvancedTrampling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execApplyAdvancedTrampling)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_OBJECT(APawn,Z_Param_CausingPlayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyAdvancedTrampling(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Force,Z_Param_CausingPlayer);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function ApplyAdvancedTrampling *********

// ********** Begin Class UAuracronFoliageInteractionManager Function ApplyBendingToFoliageInstance 
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics
{
	struct AuracronFoliageInteractionManager_eventApplyBendingToFoliageInstance_Parms
	{
		UHierarchicalInstancedStaticMeshComponent* Component;
		int32 InstanceIndex;
		FAuracronFoliageBendingData BendingData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BendingData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplyBendingToFoliageInstance_Parms, Component), Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplyBendingToFoliageInstance_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::NewProp_BendingData = { "BendingData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplyBendingToFoliageInstance_Parms, BendingData), Z_Construct_UScriptStruct_FAuracronFoliageBendingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingData_MetaData), NewProp_BendingData_MetaData) }; // 3194768359
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::NewProp_InstanceIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::NewProp_BendingData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "ApplyBendingToFoliageInstance", Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::AuracronFoliageInteractionManager_eventApplyBendingToFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::AuracronFoliageInteractionManager_eventApplyBendingToFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execApplyBendingToFoliageInstance)
{
	P_GET_OBJECT(UHierarchicalInstancedStaticMeshComponent,Z_Param_Component);
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceIndex);
	P_GET_STRUCT_REF(FAuracronFoliageBendingData,Z_Param_Out_BendingData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyBendingToFoliageInstance(Z_Param_Component,Z_Param_InstanceIndex,Z_Param_Out_BendingData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function ApplyBendingToFoliageInstance **

// ********** Begin Class UAuracronFoliageInteractionManager Function ApplySeasonalInteractionEffects 
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics
{
	struct AuracronFoliageInteractionManager_eventApplySeasonalInteractionEffects_Parms
	{
		EAuracronSeasonType Season;
		float SeasonProgress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Season_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Season;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonProgress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::NewProp_Season_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::NewProp_Season = { "Season", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplySeasonalInteractionEffects_Parms, Season), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, METADATA_PARAMS(0, nullptr) }; // 1660267735
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::NewProp_SeasonProgress = { "SeasonProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventApplySeasonalInteractionEffects_Parms, SeasonProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::NewProp_Season_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::NewProp_Season,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::NewProp_SeasonProgress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "ApplySeasonalInteractionEffects", Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::AuracronFoliageInteractionManager_eventApplySeasonalInteractionEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::AuracronFoliageInteractionManager_eventApplySeasonalInteractionEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execApplySeasonalInteractionEffects)
{
	P_GET_ENUM(EAuracronSeasonType,Z_Param_Season);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SeasonProgress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplySeasonalInteractionEffects(EAuracronSeasonType(Z_Param_Season),Z_Param_SeasonProgress);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function ApplySeasonalInteractionEffects 

// ********** Begin Class UAuracronFoliageInteractionManager Function CreateFoliageBending *********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics
{
	struct AuracronFoliageInteractionManager_eventCreateFoliageBending_Parms
	{
		FString FoliageInstanceId;
		FVector BendDirection;
		float BendStrength;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage bending\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage bending" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BendDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendStrength;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreateFoliageBending_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_BendDirection = { "BendDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreateFoliageBending_Parms, BendDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendDirection_MetaData), NewProp_BendDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_BendStrength = { "BendStrength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreateFoliageBending_Parms, BendStrength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreateFoliageBending_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_BendDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_BendStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "CreateFoliageBending", Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::AuracronFoliageInteractionManager_eventCreateFoliageBending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::AuracronFoliageInteractionManager_eventCreateFoliageBending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execCreateFoliageBending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_BendDirection);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BendStrength);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateFoliageBending(Z_Param_FoliageInstanceId,Z_Param_Out_BendDirection,Z_Param_BendStrength);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function CreateFoliageBending ***********

// ********** Begin Class UAuracronFoliageInteractionManager Function CreatePlayerInteraction ******
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics
{
	struct AuracronFoliageInteractionManager_eventCreatePlayerInteraction_Parms
	{
		APawn* Player;
		FVector Location;
		EAuracronPlayerInteractionType InteractionType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player interaction\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player interaction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InteractionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InteractionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreatePlayerInteraction_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreatePlayerInteraction_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_InteractionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_InteractionType = { "InteractionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreatePlayerInteraction_Parms, InteractionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlayerInteractionType, METADATA_PARAMS(0, nullptr) }; // 628606094
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreatePlayerInteraction_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_InteractionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_InteractionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "CreatePlayerInteraction", Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::AuracronFoliageInteractionManager_eventCreatePlayerInteraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::AuracronFoliageInteractionManager_eventCreatePlayerInteraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execCreatePlayerInteraction)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_ENUM(EAuracronPlayerInteractionType,Z_Param_InteractionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreatePlayerInteraction(Z_Param_Player,Z_Param_Out_Location,EAuracronPlayerInteractionType(Z_Param_InteractionType));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function CreatePlayerInteraction ********

// ********** Begin Class UAuracronFoliageInteractionManager Function CreateRecoverySimulation *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics
{
	struct AuracronFoliageInteractionManager_eventCreateRecoverySimulation_Parms
	{
		FString FoliageInstanceId;
		EAuracronRecoverySimulationType RecoveryType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Recovery simulation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recovery simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RecoveryType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RecoveryType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreateRecoverySimulation_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_RecoveryType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_RecoveryType = { "RecoveryType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreateRecoverySimulation_Parms, RecoveryType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronRecoverySimulationType, METADATA_PARAMS(0, nullptr) }; // 1803980840
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventCreateRecoverySimulation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_RecoveryType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_RecoveryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "CreateRecoverySimulation", Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventCreateRecoverySimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventCreateRecoverySimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execCreateRecoverySimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_GET_ENUM(EAuracronRecoverySimulationType,Z_Param_RecoveryType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateRecoverySimulation(Z_Param_FoliageInstanceId,EAuracronRecoverySimulationType(Z_Param_RecoveryType));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function CreateRecoverySimulation *******

// ********** Begin Class UAuracronFoliageInteractionManager Function DrawDebugInteractionInfo *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics
{
	struct AuracronFoliageInteractionManager_eventDrawDebugInteractionInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventDrawDebugInteractionInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "DrawDebugInteractionInfo", Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::AuracronFoliageInteractionManager_eventDrawDebugInteractionInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::AuracronFoliageInteractionManager_eventDrawDebugInteractionInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execDrawDebugInteractionInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugInteractionInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function DrawDebugInteractionInfo *******

// ********** Begin Class UAuracronFoliageInteractionManager Function EnableDebugVisualization *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageInteractionManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::AuracronFoliageInteractionManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::AuracronFoliageInteractionManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function EnableDebugVisualization *******

// ********** Begin Class UAuracronFoliageInteractionManager Function GetActiveInteractionCount ****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics
{
	struct AuracronFoliageInteractionManager_eventGetActiveInteractionCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetActiveInteractionCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetActiveInteractionCount", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::AuracronFoliageInteractionManager_eventGetActiveInteractionCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::AuracronFoliageInteractionManager_eventGetActiveInteractionCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetActiveInteractionCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveInteractionCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetActiveInteractionCount ******

// ********** Begin Class UAuracronFoliageInteractionManager Function GetAllFoliageBending *********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics
{
	struct AuracronFoliageInteractionManager_eventGetAllFoliageBending_Parms
	{
		TArray<FAuracronFoliageBendingData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageBendingData, METADATA_PARAMS(0, nullptr) }; // 3194768359
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetAllFoliageBending_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3194768359
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetAllFoliageBending", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::AuracronFoliageInteractionManager_eventGetAllFoliageBending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::AuracronFoliageInteractionManager_eventGetAllFoliageBending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetAllFoliageBending)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronFoliageBendingData>*)Z_Param__Result=P_THIS->GetAllFoliageBending();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetAllFoliageBending ***********

// ********** Begin Class UAuracronFoliageInteractionManager Function GetAllPlayerInteractions *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics
{
	struct AuracronFoliageInteractionManager_eventGetAllPlayerInteractions_Parms
	{
		TArray<FAuracronPlayerInteractionData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPlayerInteractionData, METADATA_PARAMS(0, nullptr) }; // 2284389212
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetAllPlayerInteractions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2284389212
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetAllPlayerInteractions", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::AuracronFoliageInteractionManager_eventGetAllPlayerInteractions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::AuracronFoliageInteractionManager_eventGetAllPlayerInteractions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetAllPlayerInteractions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPlayerInteractionData>*)Z_Param__Result=P_THIS->GetAllPlayerInteractions();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetAllPlayerInteractions *******

// ********** Begin Class UAuracronFoliageInteractionManager Function GetAllRecoverySimulations ****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics
{
	struct AuracronFoliageInteractionManager_eventGetAllRecoverySimulations_Parms
	{
		TArray<FAuracronRecoverySimulationData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronRecoverySimulationData, METADATA_PARAMS(0, nullptr) }; // 4121289519
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetAllRecoverySimulations_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4121289519
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetAllRecoverySimulations", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::AuracronFoliageInteractionManager_eventGetAllRecoverySimulations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::AuracronFoliageInteractionManager_eventGetAllRecoverySimulations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetAllRecoverySimulations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronRecoverySimulationData>*)Z_Param__Result=P_THIS->GetAllRecoverySimulations();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetAllRecoverySimulations ******

// ********** Begin Class UAuracronFoliageInteractionManager Function GetBendingInstanceCount ******
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics
{
	struct AuracronFoliageInteractionManager_eventGetBendingInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetBendingInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetBendingInstanceCount", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::AuracronFoliageInteractionManager_eventGetBendingInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::AuracronFoliageInteractionManager_eventGetBendingInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetBendingInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetBendingInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetBendingInstanceCount ********

// ********** Begin Class UAuracronFoliageInteractionManager Function GetConfiguration *************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics
{
	struct AuracronFoliageInteractionManager_eventGetConfiguration_Parms
	{
		FAuracronFoliageInteractionConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration, METADATA_PARAMS(0, nullptr) }; // 2735401450
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::AuracronFoliageInteractionManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::AuracronFoliageInteractionManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageInteractionConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetConfiguration ***************

// ********** Begin Class UAuracronFoliageInteractionManager Function GetFoliageBending ************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics
{
	struct AuracronFoliageInteractionManager_eventGetFoliageBending_Parms
	{
		FString BendingId;
		FAuracronFoliageBendingData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BendingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::NewProp_BendingId = { "BendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetFoliageBending_Parms, BendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingId_MetaData), NewProp_BendingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetFoliageBending_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageBendingData, METADATA_PARAMS(0, nullptr) }; // 3194768359
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::NewProp_BendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetFoliageBending", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::AuracronFoliageInteractionManager_eventGetFoliageBending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::AuracronFoliageInteractionManager_eventGetFoliageBending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetFoliageBending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BendingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageBendingData*)Z_Param__Result=P_THIS->GetFoliageBending(Z_Param_BendingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetFoliageBending **************

// ********** Begin Class UAuracronFoliageInteractionManager Function GetInstance ******************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics
{
	struct AuracronFoliageInteractionManager_eventGetInstance_Parms
	{
		UAuracronFoliageInteractionManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageInteractionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::AuracronFoliageInteractionManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::AuracronFoliageInteractionManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageInteractionManager**)Z_Param__Result=UAuracronFoliageInteractionManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetInstance ********************

// ********** Begin Class UAuracronFoliageInteractionManager Function GetPerformanceData ***********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics
{
	struct AuracronFoliageInteractionManager_eventGetPerformanceData_Parms
	{
		FAuracronInteractionPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData, METADATA_PARAMS(0, nullptr) }; // 3569848297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::AuracronFoliageInteractionManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::AuracronFoliageInteractionManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronInteractionPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetPerformanceData *************

// ********** Begin Class UAuracronFoliageInteractionManager Function GetPlayerInteraction *********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics
{
	struct AuracronFoliageInteractionManager_eventGetPlayerInteraction_Parms
	{
		FString InteractionId;
		FAuracronPlayerInteractionData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InteractionId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::NewProp_InteractionId = { "InteractionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetPlayerInteraction_Parms, InteractionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionId_MetaData), NewProp_InteractionId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetPlayerInteraction_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPlayerInteractionData, METADATA_PARAMS(0, nullptr) }; // 2284389212
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::NewProp_InteractionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetPlayerInteraction", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::AuracronFoliageInteractionManager_eventGetPlayerInteraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::AuracronFoliageInteractionManager_eventGetPlayerInteraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetPlayerInteraction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InteractionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPlayerInteractionData*)Z_Param__Result=P_THIS->GetPlayerInteraction(Z_Param_InteractionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetPlayerInteraction ***********

// ********** Begin Class UAuracronFoliageInteractionManager Function GetRecoverySimulation ********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics
{
	struct AuracronFoliageInteractionManager_eventGetRecoverySimulation_Parms
	{
		FString RecoveryId;
		FAuracronRecoverySimulationData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecoveryId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::NewProp_RecoveryId = { "RecoveryId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetRecoverySimulation_Parms, RecoveryId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryId_MetaData), NewProp_RecoveryId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventGetRecoverySimulation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronRecoverySimulationData, METADATA_PARAMS(0, nullptr) }; // 4121289519
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::NewProp_RecoveryId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "GetRecoverySimulation", Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventGetRecoverySimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventGetRecoverySimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execGetRecoverySimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RecoveryId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronRecoverySimulationData*)Z_Param__Result=P_THIS->GetRecoverySimulation(Z_Param_RecoveryId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function GetRecoverySimulation **********

// ********** Begin Class UAuracronFoliageInteractionManager Function Initialize *******************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics
{
	struct AuracronFoliageInteractionManager_eventInitialize_Parms
	{
		FAuracronFoliageInteractionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2735401450
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::AuracronFoliageInteractionManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::AuracronFoliageInteractionManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronFoliageInteractionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function Initialize *********************

// ********** Begin Class UAuracronFoliageInteractionManager Function IntegrateWithCollisionSystem *
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics
{
	struct AuracronFoliageInteractionManager_eventIntegrateWithCollisionSystem_Parms
	{
		UAuracronFoliageCollisionManager* CollisionManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::NewProp_CollisionManager = { "CollisionManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventIntegrateWithCollisionSystem_Parms, CollisionManager), Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::NewProp_CollisionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "IntegrateWithCollisionSystem", Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::AuracronFoliageInteractionManager_eventIntegrateWithCollisionSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::AuracronFoliageInteractionManager_eventIntegrateWithCollisionSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execIntegrateWithCollisionSystem)
{
	P_GET_OBJECT(UAuracronFoliageCollisionManager,Z_Param_CollisionManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithCollisionSystem(Z_Param_CollisionManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function IntegrateWithCollisionSystem ***

// ********** Begin Class UAuracronFoliageInteractionManager Function IsDebugVisualizationEnabled **
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageInteractionManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageInteractionManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageInteractionManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function IsDebugVisualizationEnabled ****

// ********** Begin Class UAuracronFoliageInteractionManager Function IsInitialized ****************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics
{
	struct AuracronFoliageInteractionManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::AuracronFoliageInteractionManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::AuracronFoliageInteractionManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function IsInitialized ******************

// ********** Begin Class UAuracronFoliageInteractionManager Function LogInteractionStatistics *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_LogInteractionStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_LogInteractionStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "LogInteractionStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_LogInteractionStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_LogInteractionStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_LogInteractionStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_LogInteractionStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execLogInteractionStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogInteractionStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function LogInteractionStatistics *******

// ********** Begin Class UAuracronFoliageInteractionManager Function ProcessPlayerMovement ********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics
{
	struct AuracronFoliageInteractionManager_eventProcessPlayerMovement_Parms
	{
		APawn* Player;
		FVector OldLocation;
		FVector NewLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventProcessPlayerMovement_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::NewProp_OldLocation = { "OldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventProcessPlayerMovement_Parms, OldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldLocation_MetaData), NewProp_OldLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::NewProp_NewLocation = { "NewLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventProcessPlayerMovement_Parms, NewLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewLocation_MetaData), NewProp_NewLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::NewProp_OldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::NewProp_NewLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "ProcessPlayerMovement", Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::AuracronFoliageInteractionManager_eventProcessPlayerMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::AuracronFoliageInteractionManager_eventProcessPlayerMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execProcessPlayerMovement)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_OldLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessPlayerMovement(Z_Param_Player,Z_Param_Out_OldLocation,Z_Param_Out_NewLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function ProcessPlayerMovement **********

// ********** Begin Class UAuracronFoliageInteractionManager Function RemoveFoliageBending *********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics
{
	struct AuracronFoliageInteractionManager_eventRemoveFoliageBending_Parms
	{
		FString BendingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BendingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::NewProp_BendingId = { "BendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventRemoveFoliageBending_Parms, BendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingId_MetaData), NewProp_BendingId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventRemoveFoliageBending_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventRemoveFoliageBending_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::NewProp_BendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "RemoveFoliageBending", Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::AuracronFoliageInteractionManager_eventRemoveFoliageBending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::AuracronFoliageInteractionManager_eventRemoveFoliageBending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execRemoveFoliageBending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BendingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveFoliageBending(Z_Param_BendingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function RemoveFoliageBending ***********

// ********** Begin Class UAuracronFoliageInteractionManager Function RemovePlayerInteraction ******
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics
{
	struct AuracronFoliageInteractionManager_eventRemovePlayerInteraction_Parms
	{
		FString InteractionId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InteractionId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::NewProp_InteractionId = { "InteractionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventRemovePlayerInteraction_Parms, InteractionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionId_MetaData), NewProp_InteractionId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventRemovePlayerInteraction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventRemovePlayerInteraction_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::NewProp_InteractionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "RemovePlayerInteraction", Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::AuracronFoliageInteractionManager_eventRemovePlayerInteraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::AuracronFoliageInteractionManager_eventRemovePlayerInteraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execRemovePlayerInteraction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InteractionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemovePlayerInteraction(Z_Param_InteractionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function RemovePlayerInteraction ********

// ********** Begin Class UAuracronFoliageInteractionManager Function RemoveRecoverySimulation *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics
{
	struct AuracronFoliageInteractionManager_eventRemoveRecoverySimulation_Parms
	{
		FString RecoveryId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecoveryId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::NewProp_RecoveryId = { "RecoveryId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventRemoveRecoverySimulation_Parms, RecoveryId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryId_MetaData), NewProp_RecoveryId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventRemoveRecoverySimulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventRemoveRecoverySimulation_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::NewProp_RecoveryId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "RemoveRecoverySimulation", Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventRemoveRecoverySimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventRemoveRecoverySimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execRemoveRecoverySimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RecoveryId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveRecoverySimulation(Z_Param_RecoveryId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function RemoveRecoverySimulation *******

// ********** Begin Class UAuracronFoliageInteractionManager Function SetBendingParameters *********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics
{
	struct AuracronFoliageInteractionManager_eventSetBendingParameters_Parms
	{
		FString FoliageTypeId;
		float Stiffness;
		float Damping;
		float RestoreForce;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Stiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RestoreForce;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventSetBendingParameters_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_Stiffness = { "Stiffness", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventSetBendingParameters_Parms, Stiffness), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_Damping = { "Damping", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventSetBendingParameters_Parms, Damping), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_RestoreForce = { "RestoreForce", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventSetBendingParameters_Parms, RestoreForce), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_Stiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_Damping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::NewProp_RestoreForce,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "SetBendingParameters", Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::AuracronFoliageInteractionManager_eventSetBendingParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::AuracronFoliageInteractionManager_eventSetBendingParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execSetBendingParameters)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Stiffness);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damping);
	P_GET_PROPERTY(FFloatProperty,Z_Param_RestoreForce);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBendingParameters(Z_Param_FoliageTypeId,Z_Param_Stiffness,Z_Param_Damping,Z_Param_RestoreForce);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function SetBendingParameters ***********

// ********** Begin Class UAuracronFoliageInteractionManager Function SetConfiguration *************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics
{
	struct AuracronFoliageInteractionManager_eventSetConfiguration_Parms
	{
		FAuracronFoliageInteractionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2735401450
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::AuracronFoliageInteractionManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::AuracronFoliageInteractionManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronFoliageInteractionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function SetConfiguration ***************

// ********** Begin Class UAuracronFoliageInteractionManager Function Shutdown *********************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function Shutdown ***********************

// ********** Begin Class UAuracronFoliageInteractionManager Function StartRecoveryForFoliage ******
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics
{
	struct AuracronFoliageInteractionManager_eventStartRecoveryForFoliage_Parms
	{
		FString FoliageInstanceId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventStartRecoveryForFoliage_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::NewProp_FoliageInstanceId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "StartRecoveryForFoliage", Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::AuracronFoliageInteractionManager_eventStartRecoveryForFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::AuracronFoliageInteractionManager_eventStartRecoveryForFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execStartRecoveryForFoliage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartRecoveryForFoliage(Z_Param_FoliageInstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function StartRecoveryForFoliage ********

// ********** Begin Class UAuracronFoliageInteractionManager Function SynchronizeWithWindSystem ****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics
{
	struct AuracronFoliageInteractionManager_eventSynchronizeWithWindSystem_Parms
	{
		FVector WindDirection;
		float WindStrength;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindStrength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::NewProp_WindDirection = { "WindDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventSynchronizeWithWindSystem_Parms, WindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirection_MetaData), NewProp_WindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::NewProp_WindStrength = { "WindStrength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventSynchronizeWithWindSystem_Parms, WindStrength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::NewProp_WindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::NewProp_WindStrength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "SynchronizeWithWindSystem", Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::AuracronFoliageInteractionManager_eventSynchronizeWithWindSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::AuracronFoliageInteractionManager_eventSynchronizeWithWindSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execSynchronizeWithWindSystem)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WindDirection);
	P_GET_PROPERTY(FFloatProperty,Z_Param_WindStrength);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithWindSystem(Z_Param_Out_WindDirection,Z_Param_WindStrength);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function SynchronizeWithWindSystem ******

// ********** Begin Class UAuracronFoliageInteractionManager Function Tick *************************
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics
{
	struct AuracronFoliageInteractionManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::AuracronFoliageInteractionManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::AuracronFoliageInteractionManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function Tick ***************************

// ********** Begin Class UAuracronFoliageInteractionManager Function UpdateAdvancedTramplingEffects 
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics
{
	struct AuracronFoliageInteractionManager_eventUpdateAdvancedTramplingEffects_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdateAdvancedTramplingEffects_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "UpdateAdvancedTramplingEffects", Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::AuracronFoliageInteractionManager_eventUpdateAdvancedTramplingEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::AuracronFoliageInteractionManager_eventUpdateAdvancedTramplingEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execUpdateAdvancedTramplingEffects)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAdvancedTramplingEffects(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function UpdateAdvancedTramplingEffects *

// ********** Begin Class UAuracronFoliageInteractionManager Function UpdateFoliageBending *********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics
{
	struct AuracronFoliageInteractionManager_eventUpdateFoliageBending_Parms
	{
		FString BendingId;
		FAuracronFoliageBendingData BendingData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BendingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BendingData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_BendingId = { "BendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdateFoliageBending_Parms, BendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingId_MetaData), NewProp_BendingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_BendingData = { "BendingData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdateFoliageBending_Parms, BendingData), Z_Construct_UScriptStruct_FAuracronFoliageBendingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingData_MetaData), NewProp_BendingData_MetaData) }; // 3194768359
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventUpdateFoliageBending_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventUpdateFoliageBending_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_BendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_BendingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "UpdateFoliageBending", Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::AuracronFoliageInteractionManager_eventUpdateFoliageBending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::AuracronFoliageInteractionManager_eventUpdateFoliageBending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execUpdateFoliageBending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BendingId);
	P_GET_STRUCT_REF(FAuracronFoliageBendingData,Z_Param_Out_BendingData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateFoliageBending(Z_Param_BendingId,Z_Param_Out_BendingData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function UpdateFoliageBending ***********

// ********** Begin Class UAuracronFoliageInteractionManager Function UpdatePerformanceMetrics *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function UpdatePerformanceMetrics *******

// ********** Begin Class UAuracronFoliageInteractionManager Function UpdatePlayerInteraction ******
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics
{
	struct AuracronFoliageInteractionManager_eventUpdatePlayerInteraction_Parms
	{
		FString InteractionId;
		FAuracronPlayerInteractionData InteractionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InteractionId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_InteractionId = { "InteractionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdatePlayerInteraction_Parms, InteractionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionId_MetaData), NewProp_InteractionId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_InteractionData = { "InteractionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdatePlayerInteraction_Parms, InteractionData), Z_Construct_UScriptStruct_FAuracronPlayerInteractionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionData_MetaData), NewProp_InteractionData_MetaData) }; // 2284389212
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventUpdatePlayerInteraction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventUpdatePlayerInteraction_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_InteractionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_InteractionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "UpdatePlayerInteraction", Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::AuracronFoliageInteractionManager_eventUpdatePlayerInteraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::AuracronFoliageInteractionManager_eventUpdatePlayerInteraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execUpdatePlayerInteraction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InteractionId);
	P_GET_STRUCT_REF(FAuracronPlayerInteractionData,Z_Param_Out_InteractionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdatePlayerInteraction(Z_Param_InteractionId,Z_Param_Out_InteractionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function UpdatePlayerInteraction ********

// ********** Begin Class UAuracronFoliageInteractionManager Function UpdateRealTimeBending ********
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics
{
	struct AuracronFoliageInteractionManager_eventUpdateRealTimeBending_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Real-time bending\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Real-time bending" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdateRealTimeBending_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "UpdateRealTimeBending", Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::AuracronFoliageInteractionManager_eventUpdateRealTimeBending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::AuracronFoliageInteractionManager_eventUpdateRealTimeBending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execUpdateRealTimeBending)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRealTimeBending(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function UpdateRealTimeBending **********

// ********** Begin Class UAuracronFoliageInteractionManager Function UpdateRecoverySimulation *****
struct Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics
{
	struct AuracronFoliageInteractionManager_eventUpdateRecoverySimulation_Parms
	{
		FString RecoveryId;
		FAuracronRecoverySimulationData RecoveryData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecoveryId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RecoveryData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_RecoveryId = { "RecoveryId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdateRecoverySimulation_Parms, RecoveryId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryId_MetaData), NewProp_RecoveryId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_RecoveryData = { "RecoveryData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInteractionManager_eventUpdateRecoverySimulation_Parms, RecoveryData), Z_Construct_UScriptStruct_FAuracronRecoverySimulationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryData_MetaData), NewProp_RecoveryData_MetaData) }; // 4121289519
void Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInteractionManager_eventUpdateRecoverySimulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInteractionManager_eventUpdateRecoverySimulation_Parms), &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_RecoveryId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_RecoveryData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInteractionManager, nullptr, "UpdateRecoverySimulation", Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventUpdateRecoverySimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::AuracronFoliageInteractionManager_eventUpdateRecoverySimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInteractionManager::execUpdateRecoverySimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RecoveryId);
	P_GET_STRUCT_REF(FAuracronRecoverySimulationData,Z_Param_Out_RecoveryData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateRecoverySimulation(Z_Param_RecoveryId,Z_Param_Out_RecoveryData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInteractionManager Function UpdateRecoverySimulation *******

// ********** Begin Class UAuracronFoliageInteractionManager ***************************************
void UAuracronFoliageInteractionManager::StaticRegisterNativesUAuracronFoliageInteractionManager()
{
	UClass* Class = UAuracronFoliageInteractionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyAdvancedTrampling", &UAuracronFoliageInteractionManager::execApplyAdvancedTrampling },
		{ "ApplyBendingToFoliageInstance", &UAuracronFoliageInteractionManager::execApplyBendingToFoliageInstance },
		{ "ApplySeasonalInteractionEffects", &UAuracronFoliageInteractionManager::execApplySeasonalInteractionEffects },
		{ "CreateFoliageBending", &UAuracronFoliageInteractionManager::execCreateFoliageBending },
		{ "CreatePlayerInteraction", &UAuracronFoliageInteractionManager::execCreatePlayerInteraction },
		{ "CreateRecoverySimulation", &UAuracronFoliageInteractionManager::execCreateRecoverySimulation },
		{ "DrawDebugInteractionInfo", &UAuracronFoliageInteractionManager::execDrawDebugInteractionInfo },
		{ "EnableDebugVisualization", &UAuracronFoliageInteractionManager::execEnableDebugVisualization },
		{ "GetActiveInteractionCount", &UAuracronFoliageInteractionManager::execGetActiveInteractionCount },
		{ "GetAllFoliageBending", &UAuracronFoliageInteractionManager::execGetAllFoliageBending },
		{ "GetAllPlayerInteractions", &UAuracronFoliageInteractionManager::execGetAllPlayerInteractions },
		{ "GetAllRecoverySimulations", &UAuracronFoliageInteractionManager::execGetAllRecoverySimulations },
		{ "GetBendingInstanceCount", &UAuracronFoliageInteractionManager::execGetBendingInstanceCount },
		{ "GetConfiguration", &UAuracronFoliageInteractionManager::execGetConfiguration },
		{ "GetFoliageBending", &UAuracronFoliageInteractionManager::execGetFoliageBending },
		{ "GetInstance", &UAuracronFoliageInteractionManager::execGetInstance },
		{ "GetPerformanceData", &UAuracronFoliageInteractionManager::execGetPerformanceData },
		{ "GetPlayerInteraction", &UAuracronFoliageInteractionManager::execGetPlayerInteraction },
		{ "GetRecoverySimulation", &UAuracronFoliageInteractionManager::execGetRecoverySimulation },
		{ "Initialize", &UAuracronFoliageInteractionManager::execInitialize },
		{ "IntegrateWithCollisionSystem", &UAuracronFoliageInteractionManager::execIntegrateWithCollisionSystem },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageInteractionManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageInteractionManager::execIsInitialized },
		{ "LogInteractionStatistics", &UAuracronFoliageInteractionManager::execLogInteractionStatistics },
		{ "ProcessPlayerMovement", &UAuracronFoliageInteractionManager::execProcessPlayerMovement },
		{ "RemoveFoliageBending", &UAuracronFoliageInteractionManager::execRemoveFoliageBending },
		{ "RemovePlayerInteraction", &UAuracronFoliageInteractionManager::execRemovePlayerInteraction },
		{ "RemoveRecoverySimulation", &UAuracronFoliageInteractionManager::execRemoveRecoverySimulation },
		{ "SetBendingParameters", &UAuracronFoliageInteractionManager::execSetBendingParameters },
		{ "SetConfiguration", &UAuracronFoliageInteractionManager::execSetConfiguration },
		{ "Shutdown", &UAuracronFoliageInteractionManager::execShutdown },
		{ "StartRecoveryForFoliage", &UAuracronFoliageInteractionManager::execStartRecoveryForFoliage },
		{ "SynchronizeWithWindSystem", &UAuracronFoliageInteractionManager::execSynchronizeWithWindSystem },
		{ "Tick", &UAuracronFoliageInteractionManager::execTick },
		{ "UpdateAdvancedTramplingEffects", &UAuracronFoliageInteractionManager::execUpdateAdvancedTramplingEffects },
		{ "UpdateFoliageBending", &UAuracronFoliageInteractionManager::execUpdateFoliageBending },
		{ "UpdatePerformanceMetrics", &UAuracronFoliageInteractionManager::execUpdatePerformanceMetrics },
		{ "UpdatePlayerInteraction", &UAuracronFoliageInteractionManager::execUpdatePlayerInteraction },
		{ "UpdateRealTimeBending", &UAuracronFoliageInteractionManager::execUpdateRealTimeBending },
		{ "UpdateRecoverySimulation", &UAuracronFoliageInteractionManager::execUpdateRecoverySimulation },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageInteractionManager;
UClass* UAuracronFoliageInteractionManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageInteractionManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageInteractionManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageInteractionManager"),
			Z_Registration_Info_UClass_UAuracronFoliageInteractionManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageInteractionManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageInteractionManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageInteractionManager_NoRegister()
{
	return UAuracronFoliageInteractionManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Interaction Manager\n * Manager for the foliage interaction system including player interaction, bending, and recovery simulation\n */" },
#endif
		{ "IncludePath", "AuracronFoliageInteraction.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Interaction Manager\nManager for the foliage interaction system including player interaction, bending, and recovery simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerInteractionStarted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerInteractionEnded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageBendingStarted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageRecoveryCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionManager_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInteraction.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerInteractionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerInteractionEnded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageBendingStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageRecoveryCompleted;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_CollisionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyAdvancedTrampling, "ApplyAdvancedTrampling" }, // 3524672421
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplyBendingToFoliageInstance, "ApplyBendingToFoliageInstance" }, // 1746574399
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_ApplySeasonalInteractionEffects, "ApplySeasonalInteractionEffects" }, // 2737207301
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateFoliageBending, "CreateFoliageBending" }, // 2198607009
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreatePlayerInteraction, "CreatePlayerInteraction" }, // 3094784350
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_CreateRecoverySimulation, "CreateRecoverySimulation" }, // 417536042
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_DrawDebugInteractionInfo, "DrawDebugInteractionInfo" }, // 4061032411
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 2023186570
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetActiveInteractionCount, "GetActiveInteractionCount" }, // 2672480695
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllFoliageBending, "GetAllFoliageBending" }, // 3005514690
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllPlayerInteractions, "GetAllPlayerInteractions" }, // 2445091122
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetAllRecoverySimulations, "GetAllRecoverySimulations" }, // 690479143
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetBendingInstanceCount, "GetBendingInstanceCount" }, // 1969971490
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetConfiguration, "GetConfiguration" }, // 740311948
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetFoliageBending, "GetFoliageBending" }, // 2159733770
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetInstance, "GetInstance" }, // 1661173310
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPerformanceData, "GetPerformanceData" }, // 1484886843
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetPlayerInteraction, "GetPlayerInteraction" }, // 3024928851
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_GetRecoverySimulation, "GetRecoverySimulation" }, // 865213361
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_Initialize, "Initialize" }, // 2407266278
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_IntegrateWithCollisionSystem, "IntegrateWithCollisionSystem" }, // 330004678
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 2763093775
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_IsInitialized, "IsInitialized" }, // 2909455244
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_LogInteractionStatistics, "LogInteractionStatistics" }, // 3658278855
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature, "OnFoliageBendingStarted__DelegateSignature" }, // 1093526905
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature, "OnFoliageRecoveryCompleted__DelegateSignature" }, // 778956110
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature, "OnPlayerInteractionEnded__DelegateSignature" }, // 753211521
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature, "OnPlayerInteractionStarted__DelegateSignature" }, // 1667562002
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_ProcessPlayerMovement, "ProcessPlayerMovement" }, // 4094996332
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveFoliageBending, "RemoveFoliageBending" }, // 3354490073
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemovePlayerInteraction, "RemovePlayerInteraction" }, // 1937803916
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_RemoveRecoverySimulation, "RemoveRecoverySimulation" }, // 2632621930
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetBendingParameters, "SetBendingParameters" }, // 1430593118
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_SetConfiguration, "SetConfiguration" }, // 4197933443
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_Shutdown, "Shutdown" }, // 2059734496
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_StartRecoveryForFoliage, "StartRecoveryForFoliage" }, // 3485093414
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_SynchronizeWithWindSystem, "SynchronizeWithWindSystem" }, // 568481313
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_Tick, "Tick" }, // 1045238899
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateAdvancedTramplingEffects, "UpdateAdvancedTramplingEffects" }, // 3470311484
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateFoliageBending, "UpdateFoliageBending" }, // 830817375
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 400435580
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdatePlayerInteraction, "UpdatePlayerInteraction" }, // 2537865474
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRealTimeBending, "UpdateRealTimeBending" }, // 2620946504
		{ &Z_Construct_UFunction_UAuracronFoliageInteractionManager_UpdateRecoverySimulation, "UpdateRecoverySimulation" }, // 1462315039
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageInteractionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnPlayerInteractionStarted = { "OnPlayerInteractionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInteractionManager, OnPlayerInteractionStarted), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerInteractionStarted_MetaData), NewProp_OnPlayerInteractionStarted_MetaData) }; // 1667562002
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnPlayerInteractionEnded = { "OnPlayerInteractionEnded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInteractionManager, OnPlayerInteractionEnded), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerInteractionEnded_MetaData), NewProp_OnPlayerInteractionEnded_MetaData) }; // 753211521
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnFoliageBendingStarted = { "OnFoliageBendingStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInteractionManager, OnFoliageBendingStarted), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageBendingStarted_MetaData), NewProp_OnFoliageBendingStarted_MetaData) }; // 1093526905
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnFoliageRecoveryCompleted = { "OnFoliageRecoveryCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInteractionManager, OnFoliageRecoveryCompleted), Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageRecoveryCompleted_MetaData), NewProp_OnFoliageRecoveryCompleted_MetaData) }; // 778956110
void Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageInteractionManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageInteractionManager), &Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInteractionManager, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2735401450
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInteractionManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_CollisionManager = { "CollisionManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInteractionManager, CollisionManager), Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionManager_MetaData), NewProp_CollisionManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnPlayerInteractionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnPlayerInteractionEnded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnFoliageBendingStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_OnFoliageRecoveryCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::NewProp_CollisionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::ClassParams = {
	&UAuracronFoliageInteractionManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageInteractionManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageInteractionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageInteractionManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageInteractionManager.OuterSingleton;
}
UAuracronFoliageInteractionManager::UAuracronFoliageInteractionManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageInteractionManager);
UAuracronFoliageInteractionManager::~UAuracronFoliageInteractionManager() {}
// ********** End Class UAuracronFoliageInteractionManager *****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPlayerInteractionType_StaticEnum, TEXT("EAuracronPlayerInteractionType"), &Z_Registration_Info_UEnum_EAuracronPlayerInteractionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 628606094U) },
		{ EAuracronFoliageBendingType_StaticEnum, TEXT("EAuracronFoliageBendingType"), &Z_Registration_Info_UEnum_EAuracronFoliageBendingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2808574338U) },
		{ EAuracronRecoverySimulationType_StaticEnum, TEXT("EAuracronRecoverySimulationType"), &Z_Registration_Info_UEnum_EAuracronRecoverySimulationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1803980840U) },
		{ EAuracronInteractionResponseType_StaticEnum, TEXT("EAuracronInteractionResponseType"), &Z_Registration_Info_UEnum_EAuracronInteractionResponseType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2236845358U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronFoliageInteractionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics::NewStructOps, TEXT("AuracronFoliageInteractionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageInteractionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageInteractionConfiguration), 2735401450U) },
		{ FAuracronPlayerInteractionData::StaticStruct, Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics::NewStructOps, TEXT("AuracronPlayerInteractionData"), &Z_Registration_Info_UScriptStruct_FAuracronPlayerInteractionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPlayerInteractionData), 2284389212U) },
		{ FAuracronFoliageBendingData::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics::NewStructOps, TEXT("AuracronFoliageBendingData"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageBendingData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageBendingData), 3194768359U) },
		{ FAuracronRecoverySimulationData::StaticStruct, Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics::NewStructOps, TEXT("AuracronRecoverySimulationData"), &Z_Registration_Info_UScriptStruct_FAuracronRecoverySimulationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRecoverySimulationData), 4121289519U) },
		{ FAuracronInteractionPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics::NewStructOps, TEXT("AuracronInteractionPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronInteractionPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronInteractionPerformanceData), 3569848297U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageInteractionManager, UAuracronFoliageInteractionManager::StaticClass, TEXT("UAuracronFoliageInteractionManager"), &Z_Registration_Info_UClass_UAuracronFoliageInteractionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageInteractionManager), 3658173614U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_1555436266(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
