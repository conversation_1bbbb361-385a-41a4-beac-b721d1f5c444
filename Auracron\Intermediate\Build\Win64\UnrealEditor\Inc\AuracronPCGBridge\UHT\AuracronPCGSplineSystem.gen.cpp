// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGSplineSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGSplineSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineSystemUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineSystemUtils_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveVector_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGPointData_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGSplineCreationMode ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode;
static UEnum* EAuracronPCGSplineCreationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSplineCreationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineCreationMode>()
{
	return EAuracronPCGSplineCreationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Bezier.DisplayName", "Bezier" },
		{ "Bezier.Name", "EAuracronPCGSplineCreationMode::Bezier" },
		{ "BlueprintType", "true" },
		{ "CatmullRom.DisplayName", "Catmull-Rom" },
		{ "CatmullRom.Name", "EAuracronPCGSplineCreationMode::CatmullRom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline creation modes\n" },
#endif
		{ "FromCurve.DisplayName", "From Curve" },
		{ "FromCurve.Name", "EAuracronPCGSplineCreationMode::FromCurve" },
		{ "FromPath.DisplayName", "From Path" },
		{ "FromPath.Name", "EAuracronPCGSplineCreationMode::FromPath" },
		{ "FromPoints.DisplayName", "From Points" },
		{ "FromPoints.Name", "EAuracronPCGSplineCreationMode::FromPoints" },
		{ "Linear.DisplayName", "Linear" },
		{ "Linear.Name", "EAuracronPCGSplineCreationMode::Linear" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "EAuracronPCGSplineCreationMode::Procedural" },
		{ "Smooth.DisplayName", "Smooth" },
		{ "Smooth.Name", "EAuracronPCGSplineCreationMode::Smooth" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline creation modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSplineCreationMode::FromPoints", (int64)EAuracronPCGSplineCreationMode::FromPoints },
		{ "EAuracronPCGSplineCreationMode::FromCurve", (int64)EAuracronPCGSplineCreationMode::FromCurve },
		{ "EAuracronPCGSplineCreationMode::FromPath", (int64)EAuracronPCGSplineCreationMode::FromPath },
		{ "EAuracronPCGSplineCreationMode::Procedural", (int64)EAuracronPCGSplineCreationMode::Procedural },
		{ "EAuracronPCGSplineCreationMode::Bezier", (int64)EAuracronPCGSplineCreationMode::Bezier },
		{ "EAuracronPCGSplineCreationMode::CatmullRom", (int64)EAuracronPCGSplineCreationMode::CatmullRom },
		{ "EAuracronPCGSplineCreationMode::Linear", (int64)EAuracronPCGSplineCreationMode::Linear },
		{ "EAuracronPCGSplineCreationMode::Smooth", (int64)EAuracronPCGSplineCreationMode::Smooth },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSplineCreationMode",
	"EAuracronPCGSplineCreationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGSplineCreationMode **********************************************

// ********** Begin Enum EAuracronPCGSplineDistributionMode ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode;
static UEnum* EAuracronPCGSplineDistributionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSplineDistributionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineDistributionMode>()
{
	return EAuracronPCGSplineDistributionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronPCGSplineDistributionMode::Adaptive" },
		{ "BlueprintType", "true" },
		{ "ByDistance.DisplayName", "By Distance" },
		{ "ByDistance.Name", "EAuracronPCGSplineDistributionMode::ByDistance" },
		{ "ByParameter.DisplayName", "By Parameter" },
		{ "ByParameter.Name", "EAuracronPCGSplineDistributionMode::ByParameter" },
		{ "BySegment.DisplayName", "By Segment" },
		{ "BySegment.Name", "EAuracronPCGSplineDistributionMode::BySegment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline distribution modes\n" },
#endif
		{ "Curve.DisplayName", "Curve Based" },
		{ "Curve.Name", "EAuracronPCGSplineDistributionMode::Curve" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
		{ "Noise.DisplayName", "Noise Based" },
		{ "Noise.Name", "EAuracronPCGSplineDistributionMode::Noise" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EAuracronPCGSplineDistributionMode::Random" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline distribution modes" },
#endif
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "EAuracronPCGSplineDistributionMode::Uniform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSplineDistributionMode::Uniform", (int64)EAuracronPCGSplineDistributionMode::Uniform },
		{ "EAuracronPCGSplineDistributionMode::ByDistance", (int64)EAuracronPCGSplineDistributionMode::ByDistance },
		{ "EAuracronPCGSplineDistributionMode::BySegment", (int64)EAuracronPCGSplineDistributionMode::BySegment },
		{ "EAuracronPCGSplineDistributionMode::ByParameter", (int64)EAuracronPCGSplineDistributionMode::ByParameter },
		{ "EAuracronPCGSplineDistributionMode::Random", (int64)EAuracronPCGSplineDistributionMode::Random },
		{ "EAuracronPCGSplineDistributionMode::Noise", (int64)EAuracronPCGSplineDistributionMode::Noise },
		{ "EAuracronPCGSplineDistributionMode::Curve", (int64)EAuracronPCGSplineDistributionMode::Curve },
		{ "EAuracronPCGSplineDistributionMode::Adaptive", (int64)EAuracronPCGSplineDistributionMode::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSplineDistributionMode",
	"EAuracronPCGSplineDistributionMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGSplineDistributionMode ******************************************

// ********** Begin Enum EAuracronPCGSplineMeshMode ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode;
static UEnum* EAuracronPCGSplineMeshMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSplineMeshMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineMeshMode>()
{
	return EAuracronPCGSplineMeshMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AdaptiveMesh.DisplayName", "Adaptive Mesh" },
		{ "AdaptiveMesh.Name", "EAuracronPCGSplineMeshMode::AdaptiveMesh" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline mesh generation modes\n" },
#endif
		{ "CustomMesh.DisplayName", "Custom Mesh" },
		{ "CustomMesh.Name", "EAuracronPCGSplineMeshMode::CustomMesh" },
		{ "InstancedMesh.DisplayName", "Instanced Mesh" },
		{ "InstancedMesh.Name", "EAuracronPCGSplineMeshMode::InstancedMesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
		{ "ProceduralMesh.DisplayName", "Procedural Mesh" },
		{ "ProceduralMesh.Name", "EAuracronPCGSplineMeshMode::ProceduralMesh" },
		{ "SegmentMesh.DisplayName", "Segment Mesh" },
		{ "SegmentMesh.Name", "EAuracronPCGSplineMeshMode::SegmentMesh" },
		{ "SingleMesh.DisplayName", "Single Mesh" },
		{ "SingleMesh.Name", "EAuracronPCGSplineMeshMode::SingleMesh" },
		{ "SplineMeshComponent.DisplayName", "Spline Mesh Component" },
		{ "SplineMeshComponent.Name", "EAuracronPCGSplineMeshMode::SplineMeshComponent" },
		{ "TiledMesh.DisplayName", "Tiled Mesh" },
		{ "TiledMesh.Name", "EAuracronPCGSplineMeshMode::TiledMesh" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline mesh generation modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSplineMeshMode::SingleMesh", (int64)EAuracronPCGSplineMeshMode::SingleMesh },
		{ "EAuracronPCGSplineMeshMode::SegmentMesh", (int64)EAuracronPCGSplineMeshMode::SegmentMesh },
		{ "EAuracronPCGSplineMeshMode::TiledMesh", (int64)EAuracronPCGSplineMeshMode::TiledMesh },
		{ "EAuracronPCGSplineMeshMode::AdaptiveMesh", (int64)EAuracronPCGSplineMeshMode::AdaptiveMesh },
		{ "EAuracronPCGSplineMeshMode::ProceduralMesh", (int64)EAuracronPCGSplineMeshMode::ProceduralMesh },
		{ "EAuracronPCGSplineMeshMode::InstancedMesh", (int64)EAuracronPCGSplineMeshMode::InstancedMesh },
		{ "EAuracronPCGSplineMeshMode::SplineMeshComponent", (int64)EAuracronPCGSplineMeshMode::SplineMeshComponent },
		{ "EAuracronPCGSplineMeshMode::CustomMesh", (int64)EAuracronPCGSplineMeshMode::CustomMesh },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSplineMeshMode",
	"EAuracronPCGSplineMeshMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGSplineMeshMode **************************************************

// ********** Begin Enum EAuracronPCGSplineTangentMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode;
static UEnum* EAuracronPCGSplineTangentMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSplineTangentMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineTangentMode>()
{
	return EAuracronPCGSplineTangentMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Auto.DisplayName", "Auto" },
		{ "Auto.Name", "EAuracronPCGSplineTangentMode::Auto" },
		{ "BlueprintType", "true" },
		{ "Break.DisplayName", "Break" },
		{ "Break.Name", "EAuracronPCGSplineTangentMode::Break" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline tangent modes\n" },
#endif
		{ "Constant.DisplayName", "Constant" },
		{ "Constant.Name", "EAuracronPCGSplineTangentMode::Constant" },
		{ "CurveBreak.DisplayName", "Curve Break" },
		{ "CurveBreak.Name", "EAuracronPCGSplineTangentMode::CurveBreak" },
		{ "CurveClamped.DisplayName", "Curve Clamped" },
		{ "CurveClamped.Name", "EAuracronPCGSplineTangentMode::CurveClamped" },
		{ "Linear.DisplayName", "Linear" },
		{ "Linear.Name", "EAuracronPCGSplineTangentMode::Linear" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGSplineTangentMode::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline tangent modes" },
#endif
		{ "User.DisplayName", "User" },
		{ "User.Name", "EAuracronPCGSplineTangentMode::User" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSplineTangentMode::Auto", (int64)EAuracronPCGSplineTangentMode::Auto },
		{ "EAuracronPCGSplineTangentMode::User", (int64)EAuracronPCGSplineTangentMode::User },
		{ "EAuracronPCGSplineTangentMode::Break", (int64)EAuracronPCGSplineTangentMode::Break },
		{ "EAuracronPCGSplineTangentMode::Linear", (int64)EAuracronPCGSplineTangentMode::Linear },
		{ "EAuracronPCGSplineTangentMode::Constant", (int64)EAuracronPCGSplineTangentMode::Constant },
		{ "EAuracronPCGSplineTangentMode::CurveClamped", (int64)EAuracronPCGSplineTangentMode::CurveClamped },
		{ "EAuracronPCGSplineTangentMode::CurveBreak", (int64)EAuracronPCGSplineTangentMode::CurveBreak },
		{ "EAuracronPCGSplineTangentMode::None", (int64)EAuracronPCGSplineTangentMode::None },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSplineTangentMode",
	"EAuracronPCGSplineTangentMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGSplineTangentMode ***********************************************

// ********** Begin Enum EAuracronPCGPathFindingMode ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode;
static UEnum* EAuracronPCGPathFindingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPathFindingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPathFindingMode>()
{
	return EAuracronPCGPathFindingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AStar.DisplayName", "A* Algorithm" },
		{ "AStar.Name", "EAuracronPCGPathFindingMode::AStar" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Path finding integration modes\n" },
#endif
		{ "Custom.DisplayName", "Custom Algorithm" },
		{ "Custom.Name", "EAuracronPCGPathFindingMode::Custom" },
		{ "Dijkstra.DisplayName", "Dijkstra" },
		{ "Dijkstra.Name", "EAuracronPCGPathFindingMode::Dijkstra" },
		{ "FlowField.DisplayName", "Flow Field" },
		{ "FlowField.Name", "EAuracronPCGPathFindingMode::FlowField" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EAuracronPCGPathFindingMode::Hybrid" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
		{ "NavMesh.DisplayName", "NavMesh" },
		{ "NavMesh.Name", "EAuracronPCGPathFindingMode::NavMesh" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGPathFindingMode::None" },
		{ "RRT.DisplayName", "RRT (Rapidly-exploring Random Tree)" },
		{ "RRT.Name", "EAuracronPCGPathFindingMode::RRT" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path finding integration modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPathFindingMode::None", (int64)EAuracronPCGPathFindingMode::None },
		{ "EAuracronPCGPathFindingMode::AStar", (int64)EAuracronPCGPathFindingMode::AStar },
		{ "EAuracronPCGPathFindingMode::Dijkstra", (int64)EAuracronPCGPathFindingMode::Dijkstra },
		{ "EAuracronPCGPathFindingMode::FlowField", (int64)EAuracronPCGPathFindingMode::FlowField },
		{ "EAuracronPCGPathFindingMode::NavMesh", (int64)EAuracronPCGPathFindingMode::NavMesh },
		{ "EAuracronPCGPathFindingMode::Custom", (int64)EAuracronPCGPathFindingMode::Custom },
		{ "EAuracronPCGPathFindingMode::Hybrid", (int64)EAuracronPCGPathFindingMode::Hybrid },
		{ "EAuracronPCGPathFindingMode::RRT", (int64)EAuracronPCGPathFindingMode::RRT },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPathFindingMode",
	"EAuracronPCGPathFindingMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGPathFindingMode *************************************************

// ********** Begin Enum EAuracronPCGSplineDeformationMode *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode;
static UEnum* EAuracronPCGSplineDeformationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSplineDeformationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineDeformationMode>()
{
	return EAuracronPCGSplineDeformationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Bend.DisplayName", "Bend" },
		{ "Bend.Name", "EAuracronPCGSplineDeformationMode::Bend" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline deformation modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGSplineDeformationMode::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
		{ "Noise.DisplayName", "Noise" },
		{ "Noise.Name", "EAuracronPCGSplineDeformationMode::Noise" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGSplineDeformationMode::None" },
		{ "Scale.DisplayName", "Scale" },
		{ "Scale.Name", "EAuracronPCGSplineDeformationMode::Scale" },
		{ "Spiral.DisplayName", "Spiral" },
		{ "Spiral.Name", "EAuracronPCGSplineDeformationMode::Spiral" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline deformation modes" },
#endif
		{ "Twist.DisplayName", "Twist" },
		{ "Twist.Name", "EAuracronPCGSplineDeformationMode::Twist" },
		{ "Wave.DisplayName", "Wave" },
		{ "Wave.Name", "EAuracronPCGSplineDeformationMode::Wave" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSplineDeformationMode::None", (int64)EAuracronPCGSplineDeformationMode::None },
		{ "EAuracronPCGSplineDeformationMode::Bend", (int64)EAuracronPCGSplineDeformationMode::Bend },
		{ "EAuracronPCGSplineDeformationMode::Twist", (int64)EAuracronPCGSplineDeformationMode::Twist },
		{ "EAuracronPCGSplineDeformationMode::Scale", (int64)EAuracronPCGSplineDeformationMode::Scale },
		{ "EAuracronPCGSplineDeformationMode::Noise", (int64)EAuracronPCGSplineDeformationMode::Noise },
		{ "EAuracronPCGSplineDeformationMode::Wave", (int64)EAuracronPCGSplineDeformationMode::Wave },
		{ "EAuracronPCGSplineDeformationMode::Spiral", (int64)EAuracronPCGSplineDeformationMode::Spiral },
		{ "EAuracronPCGSplineDeformationMode::Custom", (int64)EAuracronPCGSplineDeformationMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSplineDeformationMode",
	"EAuracronPCGSplineDeformationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGSplineDeformationMode *******************************************

// ********** Begin ScriptStruct FAuracronPCGSplinePointDescriptor *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor;
class UScriptStruct* FAuracronPCGSplinePointDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGSplinePointDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spline Point Descriptor\n * Describes a spline point with all its properties\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline Point Descriptor\nDescribes a spline point with all its properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Point" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArriveTangent_MetaData[] = {
		{ "Category", "Point" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaveTangent_MetaData[] = {
		{ "Category", "Point" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Point" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Point" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TangentMode_MetaData[] = {
		{ "Category", "Point" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Speed_MetaData[] = {
		{ "Category", "Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointType_MetaData[] = {
		{ "Category", "Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomAttributes_MetaData[] = {
		{ "Category", "Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ArriveTangent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LeaveTangent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TangentMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TangentMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PointType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CustomAttributes_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomAttributes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomAttributes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGSplinePointDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_ArriveTangent = { "ArriveTangent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, ArriveTangent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArriveTangent_MetaData), NewProp_ArriveTangent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_LeaveTangent = { "LeaveTangent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, LeaveTangent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaveTangent_MetaData), NewProp_LeaveTangent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_TangentMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_TangentMode = { "TangentMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, TangentMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TangentMode_MetaData), NewProp_TangentMode_MetaData) }; // 1652141387
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, Speed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Speed_MetaData), NewProp_Speed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_PointType = { "PointType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, PointType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointType_MetaData), NewProp_PointType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_CustomAttributes_ValueProp = { "CustomAttributes", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_CustomAttributes_Key_KeyProp = { "CustomAttributes_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_CustomAttributes = { "CustomAttributes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplinePointDescriptor, CustomAttributes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomAttributes_MetaData), NewProp_CustomAttributes_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_ArriveTangent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_LeaveTangent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_TangentMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_TangentMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Speed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_PointType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_CustomAttributes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_CustomAttributes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewProp_CustomAttributes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGSplinePointDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGSplinePointDescriptor),
	alignof(FAuracronPCGSplinePointDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGSplinePointDescriptor ***********************************

// ********** Begin ScriptStruct FAuracronPCGSplineMeshDescriptor **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor;
class UScriptStruct* FAuracronPCGSplineMeshDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGSplineMeshDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spline Mesh Descriptor\n * Describes parameters for spline mesh generation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline Mesh Descriptor\nDescribes parameters for spline mesh generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StaticMesh_MetaData[] = {
		{ "Category", "Mesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshMode_MetaData[] = {
		{ "Category", "Mesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialOverrides_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTileMesh_MetaData[] = {
		{ "Category", "Tiling" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileLength_MetaData[] = {
		{ "Category", "Tiling" },
		{ "EditCondition", "bTileMesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStretchToFit_MetaData[] = {
		{ "Category", "Tiling" },
		{ "EditCondition", "bTileMesh" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeformationMode_MetaData[] = {
		{ "Category", "Deformation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeformationStrength_MetaData[] = {
		{ "Category", "Deformation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomScale_MetaData[] = {
		{ "Category", "Scaling" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleCurve_MetaData[] = {
		{ "Category", "Scaling" },
		{ "EditCondition", "bUseCustomScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateCollision_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bComplexCollision_MetaData[] = {
		{ "Category", "Collision" },
		{ "EditCondition", "bGenerateCollision" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateLODs_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODCount_MetaData[] = {
		{ "Category", "LOD" },
		{ "EditCondition", "bGenerateLODs" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MeshMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MeshMode;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialOverrides_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialOverrides;
	static void NewProp_bTileMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTileMesh;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TileLength;
	static void NewProp_bStretchToFit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStretchToFit;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DeformationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DeformationMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeformationStrength;
	static void NewProp_bUseCustomScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomScale;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ScaleCurve;
	static void NewProp_bGenerateCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateCollision;
	static void NewProp_bComplexCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bComplexCollision;
	static void NewProp_bGenerateLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGSplineMeshDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StaticMesh_MetaData), NewProp_StaticMesh_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MeshMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MeshMode = { "MeshMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, MeshMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineMeshMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshMode_MetaData), NewProp_MeshMode_MetaData) }; // 1530834170
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MaterialOverrides_Inner = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MaterialOverrides = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, MaterialOverrides), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialOverrides_MetaData), NewProp_MaterialOverrides_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bTileMesh_SetBit(void* Obj)
{
	((FAuracronPCGSplineMeshDescriptor*)Obj)->bTileMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bTileMesh = { "bTileMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSplineMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bTileMesh_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTileMesh_MetaData), NewProp_bTileMesh_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_TileLength = { "TileLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, TileLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileLength_MetaData), NewProp_TileLength_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bStretchToFit_SetBit(void* Obj)
{
	((FAuracronPCGSplineMeshDescriptor*)Obj)->bStretchToFit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bStretchToFit = { "bStretchToFit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSplineMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bStretchToFit_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStretchToFit_MetaData), NewProp_bStretchToFit_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_DeformationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_DeformationMode = { "DeformationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, DeformationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeformationMode_MetaData), NewProp_DeformationMode_MetaData) }; // 4083921318
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_DeformationStrength = { "DeformationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, DeformationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeformationStrength_MetaData), NewProp_DeformationStrength_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bUseCustomScale_SetBit(void* Obj)
{
	((FAuracronPCGSplineMeshDescriptor*)Obj)->bUseCustomScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bUseCustomScale = { "bUseCustomScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSplineMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bUseCustomScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomScale_MetaData), NewProp_bUseCustomScale_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_ScaleCurve = { "ScaleCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, ScaleCurve), Z_Construct_UClass_UCurveVector_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleCurve_MetaData), NewProp_ScaleCurve_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateCollision_SetBit(void* Obj)
{
	((FAuracronPCGSplineMeshDescriptor*)Obj)->bGenerateCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateCollision = { "bGenerateCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSplineMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateCollision_MetaData), NewProp_bGenerateCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bComplexCollision_SetBit(void* Obj)
{
	((FAuracronPCGSplineMeshDescriptor*)Obj)->bComplexCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bComplexCollision = { "bComplexCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSplineMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bComplexCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bComplexCollision_MetaData), NewProp_bComplexCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateLODs_SetBit(void* Obj)
{
	((FAuracronPCGSplineMeshDescriptor*)Obj)->bGenerateLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateLODs = { "bGenerateLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGSplineMeshDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateLODs_MetaData), NewProp_bGenerateLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_LODCount = { "LODCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGSplineMeshDescriptor, LODCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODCount_MetaData), NewProp_LODCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MeshMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MeshMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MaterialOverrides_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_MaterialOverrides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bTileMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_TileLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bStretchToFit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_DeformationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_DeformationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_DeformationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bUseCustomScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_ScaleCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bComplexCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_bGenerateLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewProp_LODCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGSplineMeshDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGSplineMeshDescriptor),
	alignof(FAuracronPCGSplineMeshDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGSplineMeshDescriptor ************************************

// ********** Begin ScriptStruct FAuracronPCGPathFindingDescriptor *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor;
class UScriptStruct* FAuracronPCGPathFindingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGPathFindingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Path Finding Descriptor\n * Describes parameters for path finding integration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path Finding Descriptor\nDescribes parameters for path finding integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathFindingMode_MetaData[] = {
		{ "Category", "Path Finding" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
		{ "Category", "Path Finding" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "Category", "Path Finding" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Waypoints_MetaData[] = {
		{ "Category", "Path Finding" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAvoidObstacles_MetaData[] = {
		{ "Category", "Obstacles" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObstacleAvoidanceRadius_MetaData[] = {
		{ "Category", "Obstacles" },
		{ "EditCondition", "bAvoidObstacles" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObstacleTags_MetaData[] = {
		{ "Category", "Obstacles" },
		{ "EditCondition", "bAvoidObstacles" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSmoothPath_MetaData[] = {
		{ "Category", "Smoothing" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SmoothingStrength_MetaData[] = {
		{ "Category", "Smoothing" },
		{ "EditCondition", "bSmoothPath" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SmoothingIterations_MetaData[] = {
		{ "Category", "Smoothing" },
		{ "EditCondition", "bSmoothPath" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlope_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinWidth_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStayOnNavMesh_MetaData[] = {
		{ "Category", "Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSearchNodes_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchTimeout_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PathFindingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PathFindingMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Waypoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Waypoints;
	static void NewProp_bAvoidObstacles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvoidObstacles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObstacleAvoidanceRadius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObstacleTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObstacleTags;
	static void NewProp_bSmoothPath_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSmoothPath;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SmoothingStrength;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SmoothingIterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlope;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinWidth;
	static void NewProp_bStayOnNavMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStayOnNavMesh;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSearchNodes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SearchTimeout;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGPathFindingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_PathFindingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_PathFindingMode = { "PathFindingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, PathFindingMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPathFindingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathFindingMode_MetaData), NewProp_PathFindingMode_MetaData) }; // 720469112
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_Waypoints_Inner = { "Waypoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_Waypoints = { "Waypoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, Waypoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Waypoints_MetaData), NewProp_Waypoints_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bAvoidObstacles_SetBit(void* Obj)
{
	((FAuracronPCGPathFindingDescriptor*)Obj)->bAvoidObstacles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bAvoidObstacles = { "bAvoidObstacles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPathFindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bAvoidObstacles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAvoidObstacles_MetaData), NewProp_bAvoidObstacles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_ObstacleAvoidanceRadius = { "ObstacleAvoidanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, ObstacleAvoidanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObstacleAvoidanceRadius_MetaData), NewProp_ObstacleAvoidanceRadius_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_ObstacleTags_Inner = { "ObstacleTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_ObstacleTags = { "ObstacleTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, ObstacleTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObstacleTags_MetaData), NewProp_ObstacleTags_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bSmoothPath_SetBit(void* Obj)
{
	((FAuracronPCGPathFindingDescriptor*)Obj)->bSmoothPath = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bSmoothPath = { "bSmoothPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPathFindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bSmoothPath_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSmoothPath_MetaData), NewProp_bSmoothPath_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_SmoothingStrength = { "SmoothingStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, SmoothingStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SmoothingStrength_MetaData), NewProp_SmoothingStrength_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_SmoothingIterations = { "SmoothingIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, SmoothingIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SmoothingIterations_MetaData), NewProp_SmoothingIterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_MaxSlope = { "MaxSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, MaxSlope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlope_MetaData), NewProp_MaxSlope_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_MinWidth = { "MinWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, MinWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinWidth_MetaData), NewProp_MinWidth_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bStayOnNavMesh_SetBit(void* Obj)
{
	((FAuracronPCGPathFindingDescriptor*)Obj)->bStayOnNavMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bStayOnNavMesh = { "bStayOnNavMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPathFindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bStayOnNavMesh_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStayOnNavMesh_MetaData), NewProp_bStayOnNavMesh_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_MaxSearchNodes = { "MaxSearchNodes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, MaxSearchNodes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSearchNodes_MetaData), NewProp_MaxSearchNodes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_SearchTimeout = { "SearchTimeout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPathFindingDescriptor, SearchTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchTimeout_MetaData), NewProp_SearchTimeout_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_PathFindingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_PathFindingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_Waypoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_Waypoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bAvoidObstacles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_ObstacleAvoidanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_ObstacleTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_ObstacleTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bSmoothPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_SmoothingStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_SmoothingIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_MaxSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_MinWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_bStayOnNavMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_MaxSearchNodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewProp_SearchTimeout,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGPathFindingDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGPathFindingDescriptor),
	alignof(FAuracronPCGPathFindingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGPathFindingDescriptor ***********************************

// ********** Begin Class UAuracronPCGAdvancedSplineCreatorSettings ********************************
void UAuracronPCGAdvancedSplineCreatorSettings::StaticRegisterNativesUAuracronPCGAdvancedSplineCreatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings;
UClass* UAuracronPCGAdvancedSplineCreatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedSplineCreatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedSplineCreatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedSplineCreatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_NoRegister()
{
	return UAuracronPCGAdvancedSplineCreatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGSplineSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationMode_MetaData[] = {
		{ "Category", "Creation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Creation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bClosedSpline_MetaData[] = {
		{ "Category", "Creation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateComponent_MetaData[] = {
		{ "Category", "Creation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateActor_MetaData[] = {
		{ "Category", "Creation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSortPointsByDistance_MetaData[] = {
		{ "Category", "Point Processing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Point processing\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Point processing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRemoveDuplicatePoints_MetaData[] = {
		{ "Category", "Point Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DuplicateThreshold_MetaData[] = {
		{ "Category", "Point Processing" },
		{ "EditCondition", "bRemoveDuplicatePoints" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSimplifySpline_MetaData[] = {
		{ "Category", "Point Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationTolerance_MetaData[] = {
		{ "Category", "Point Processing" },
		{ "EditCondition", "bSimplifySpline" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTangentMode_MetaData[] = {
		{ "Category", "Tangents" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tangent settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tangent settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TangentScale_MetaData[] = {
		{ "Category", "Tangents" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomTangents_MetaData[] = {
		{ "Category", "Tangents" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArriveTangentAttribute_MetaData[] = {
		{ "Category", "Tangents" },
		{ "EditCondition", "bUseCustomTangents" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaveTangentAttribute_MetaData[] = {
		{ "Category", "Tangents" },
		{ "EditCondition", "bUseCustomTangents" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralPointCount_MetaData[] = {
		{ "Category", "Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Procedural generation\n" },
#endif
		{ "EditCondition", "CreationMode == EAuracronPCGSplineCreationMode::Procedural" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralLength_MetaData[] = {
		{ "Category", "Procedural" },
		{ "EditCondition", "CreationMode == EAuracronPCGSplineCreationMode::Procedural" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralVariation_MetaData[] = {
		{ "Category", "Procedural" },
		{ "EditCondition", "CreationMode == EAuracronPCGSplineCreationMode::Procedural" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralSeed_MetaData[] = {
		{ "Category", "Procedural" },
		{ "EditCondition", "CreationMode == EAuracronPCGSplineCreationMode::Procedural" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathFindingDescriptor_MetaData[] = {
		{ "Category", "Path Finding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Path finding integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path finding integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputSplinePoints_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputDebugInfo_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CreationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CreationMode;
	static void NewProp_bClosedSpline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClosedSpline;
	static void NewProp_bCreateComponent_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateComponent;
	static void NewProp_bCreateActor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateActor;
	static void NewProp_bSortPointsByDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSortPointsByDistance;
	static void NewProp_bRemoveDuplicatePoints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRemoveDuplicatePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DuplicateThreshold;
	static void NewProp_bSimplifySpline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSimplifySpline;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimplificationTolerance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultTangentMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultTangentMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TangentScale;
	static void NewProp_bUseCustomTangents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomTangents;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ArriveTangentAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaveTangentAttribute;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ProceduralPointCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProceduralLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProceduralVariation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ProceduralSeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathFindingDescriptor;
	static void NewProp_bOutputSplinePoints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputSplinePoints;
	static void NewProp_bOutputDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputDebugInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedSplineCreatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_CreationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_CreationMode = { "CreationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, CreationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineCreationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationMode_MetaData), NewProp_CreationMode_MetaData) }; // 1427174248
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bClosedSpline_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bClosedSpline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bClosedSpline = { "bClosedSpline", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bClosedSpline_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bClosedSpline_MetaData), NewProp_bClosedSpline_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateComponent_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bCreateComponent = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateComponent = { "bCreateComponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateComponent_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateComponent_MetaData), NewProp_bCreateComponent_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateActor_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bCreateActor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateActor = { "bCreateActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateActor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateActor_MetaData), NewProp_bCreateActor_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSortPointsByDistance_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bSortPointsByDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSortPointsByDistance = { "bSortPointsByDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSortPointsByDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSortPointsByDistance_MetaData), NewProp_bSortPointsByDistance_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bRemoveDuplicatePoints_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bRemoveDuplicatePoints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bRemoveDuplicatePoints = { "bRemoveDuplicatePoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bRemoveDuplicatePoints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRemoveDuplicatePoints_MetaData), NewProp_bRemoveDuplicatePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_DuplicateThreshold = { "DuplicateThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, DuplicateThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DuplicateThreshold_MetaData), NewProp_DuplicateThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSimplifySpline_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bSimplifySpline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSimplifySpline = { "bSimplifySpline", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSimplifySpline_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSimplifySpline_MetaData), NewProp_bSimplifySpline_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_SimplificationTolerance = { "SimplificationTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, SimplificationTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationTolerance_MetaData), NewProp_SimplificationTolerance_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_DefaultTangentMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_DefaultTangentMode = { "DefaultTangentMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, DefaultTangentMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTangentMode_MetaData), NewProp_DefaultTangentMode_MetaData) }; // 1652141387
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_TangentScale = { "TangentScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, TangentScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TangentScale_MetaData), NewProp_TangentScale_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bUseCustomTangents_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bUseCustomTangents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bUseCustomTangents = { "bUseCustomTangents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bUseCustomTangents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomTangents_MetaData), NewProp_bUseCustomTangents_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ArriveTangentAttribute = { "ArriveTangentAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, ArriveTangentAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArriveTangentAttribute_MetaData), NewProp_ArriveTangentAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_LeaveTangentAttribute = { "LeaveTangentAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, LeaveTangentAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaveTangentAttribute_MetaData), NewProp_LeaveTangentAttribute_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralPointCount = { "ProceduralPointCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, ProceduralPointCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralPointCount_MetaData), NewProp_ProceduralPointCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralLength = { "ProceduralLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, ProceduralLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralLength_MetaData), NewProp_ProceduralLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralVariation = { "ProceduralVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, ProceduralVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralVariation_MetaData), NewProp_ProceduralVariation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralSeed = { "ProceduralSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, ProceduralSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralSeed_MetaData), NewProp_ProceduralSeed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_PathFindingDescriptor = { "PathFindingDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSplineCreatorSettings, PathFindingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathFindingDescriptor_MetaData), NewProp_PathFindingDescriptor_MetaData) }; // 3767220908
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputSplinePoints_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bOutputSplinePoints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputSplinePoints = { "bOutputSplinePoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputSplinePoints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputSplinePoints_MetaData), NewProp_bOutputSplinePoints_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputDebugInfo_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSplineCreatorSettings*)Obj)->bOutputDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputDebugInfo = { "bOutputDebugInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSplineCreatorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputDebugInfo_MetaData), NewProp_bOutputDebugInfo_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_CreationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_CreationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bClosedSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bCreateActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSortPointsByDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bRemoveDuplicatePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_DuplicateThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bSimplifySpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_SimplificationTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_DefaultTangentMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_DefaultTangentMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_TangentScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bUseCustomTangents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ArriveTangentAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_LeaveTangentAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralPointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_ProceduralSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_PathFindingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputSplinePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::NewProp_bOutputDebugInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedSplineCreatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedSplineCreatorSettings);
UAuracronPCGAdvancedSplineCreatorSettings::~UAuracronPCGAdvancedSplineCreatorSettings() {}
// ********** End Class UAuracronPCGAdvancedSplineCreatorSettings **********************************

// ********** Begin Class UAuracronPCGSplinePointDistributorSettings *******************************
void UAuracronPCGSplinePointDistributorSettings::StaticRegisterNativesUAuracronPCGSplinePointDistributorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings;
UClass* UAuracronPCGSplinePointDistributorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSplinePointDistributorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSplinePointDistributorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSplinePointDistributorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_NoRegister()
{
	return UAuracronPCGSplinePointDistributorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGSplineSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistributionMode_MetaData[] = {
		{ "Category", "Distribution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distribution settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distribution settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointCount_MetaData[] = {
		{ "Category", "Distribution" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointSpacing_MetaData[] = {
		{ "Category", "Distribution" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePointCountOverSpacing_MetaData[] = {
		{ "Category", "Distribution" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeSplineStart_MetaData[] = {
		{ "Category", "Uniform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Uniform distribution\n" },
#endif
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Uniform" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Uniform distribution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeSplineEnd_MetaData[] = {
		{ "Category", "Uniform" },
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Uniform" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomSeed_MetaData[] = {
		{ "Category", "Random" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Random distribution\n" },
#endif
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Random" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Random distribution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomVariation_MetaData[] = {
		{ "Category", "Random" },
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Random" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise distribution\n" },
#endif
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise distribution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseStrength_MetaData[] = {
		{ "Category", "Noise" },
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "Noise" },
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistributionCurve_MetaData[] = {
		{ "Category", "Curve" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Curve distribution\n" },
#endif
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Curve" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Curve distribution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurvatureThreshold_MetaData[] = {
		{ "Category", "Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Adaptive distribution\n" },
#endif
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Adaptive" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptive distribution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinPointSpacing_MetaData[] = {
		{ "Category", "Adaptive" },
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Adaptive" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPointSpacing_MetaData[] = {
		{ "Category", "Adaptive" },
		{ "EditCondition", "DistributionMode == EAuracronPCGSplineDistributionMode::Adaptive" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToSpline_MetaData[] = {
		{ "Category", "Point Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Point properties\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Point properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSplineWidth_MetaData[] = {
		{ "Category", "Point Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WidthAttribute_MetaData[] = {
		{ "Category", "Point Properties" },
		{ "EditCondition", "bUseSplineWidth" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProjectToSurface_MetaData[] = {
		{ "Category", "Point Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectionDistance_MetaData[] = {
		{ "Category", "Point Properties" },
		{ "EditCondition", "bProjectToSurface" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointOffset_MetaData[] = {
		{ "Category", "Offset" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Offset settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Offset settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeOffset_MetaData[] = {
		{ "Category", "Offset" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OffsetVariation_MetaData[] = {
		{ "Category", "Offset" },
		{ "EditCondition", "bRandomizeOffset" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterBySplineSegment_MetaData[] = {
		{ "Category", "Filtering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Filtering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedSegments_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterBySplineSegment" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByDistance_MetaData[] = {
		{ "Category", "Filtering" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceRange_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByDistance" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistributionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistributionMode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PointSpacing;
	static void NewProp_bUsePointCountOverSpacing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePointCountOverSpacing;
	static void NewProp_bIncludeSplineStart_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeSplineStart;
	static void NewProp_bIncludeSplineEnd_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeSplineEnd;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RandomSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RandomVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseStrength;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DistributionCurve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurvatureThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinPointSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxPointSpacing;
	static void NewProp_bAlignToSpline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToSpline;
	static void NewProp_bUseSplineWidth_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSplineWidth;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WidthAttribute;
	static void NewProp_bProjectToSurface_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProjectToSurface;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProjectionDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointOffset;
	static void NewProp_bRandomizeOffset_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OffsetVariation;
	static void NewProp_bFilterBySplineSegment_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterBySplineSegment;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AllowedSegments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedSegments;
	static void NewProp_bFilterByDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DistanceRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSplinePointDistributorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistributionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistributionMode = { "DistributionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, DistributionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistributionMode_MetaData), NewProp_DistributionMode_MetaData) }; // 1546632872
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_PointCount = { "PointCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, PointCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointCount_MetaData), NewProp_PointCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_PointSpacing = { "PointSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, PointSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointSpacing_MetaData), NewProp_PointSpacing_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUsePointCountOverSpacing_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bUsePointCountOverSpacing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUsePointCountOverSpacing = { "bUsePointCountOverSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUsePointCountOverSpacing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePointCountOverSpacing_MetaData), NewProp_bUsePointCountOverSpacing_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineStart_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bIncludeSplineStart = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineStart = { "bIncludeSplineStart", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineStart_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeSplineStart_MetaData), NewProp_bIncludeSplineStart_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineEnd_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bIncludeSplineEnd = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineEnd = { "bIncludeSplineEnd", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineEnd_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeSplineEnd_MetaData), NewProp_bIncludeSplineEnd_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_RandomSeed = { "RandomSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, RandomSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomSeed_MetaData), NewProp_RandomSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_RandomVariation = { "RandomVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, RandomVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomVariation_MetaData), NewProp_RandomVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_NoiseStrength = { "NoiseStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, NoiseStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseStrength_MetaData), NewProp_NoiseStrength_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistributionCurve = { "DistributionCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, DistributionCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistributionCurve_MetaData), NewProp_DistributionCurve_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_CurvatureThreshold = { "CurvatureThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, CurvatureThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurvatureThreshold_MetaData), NewProp_CurvatureThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_MinPointSpacing = { "MinPointSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, MinPointSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinPointSpacing_MetaData), NewProp_MinPointSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_MaxPointSpacing = { "MaxPointSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, MaxPointSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPointSpacing_MetaData), NewProp_MaxPointSpacing_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bAlignToSpline_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bAlignToSpline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bAlignToSpline = { "bAlignToSpline", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bAlignToSpline_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToSpline_MetaData), NewProp_bAlignToSpline_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUseSplineWidth_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bUseSplineWidth = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUseSplineWidth = { "bUseSplineWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUseSplineWidth_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSplineWidth_MetaData), NewProp_bUseSplineWidth_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_WidthAttribute = { "WidthAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, WidthAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WidthAttribute_MetaData), NewProp_WidthAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bProjectToSurface_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bProjectToSurface = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bProjectToSurface = { "bProjectToSurface", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bProjectToSurface_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProjectToSurface_MetaData), NewProp_bProjectToSurface_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_ProjectionDistance = { "ProjectionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, ProjectionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectionDistance_MetaData), NewProp_ProjectionDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_PointOffset = { "PointOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, PointOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointOffset_MetaData), NewProp_PointOffset_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bRandomizeOffset_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bRandomizeOffset = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bRandomizeOffset = { "bRandomizeOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bRandomizeOffset_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeOffset_MetaData), NewProp_bRandomizeOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_OffsetVariation = { "OffsetVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, OffsetVariation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OffsetVariation_MetaData), NewProp_OffsetVariation_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterBySplineSegment_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bFilterBySplineSegment = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterBySplineSegment = { "bFilterBySplineSegment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterBySplineSegment_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterBySplineSegment_MetaData), NewProp_bFilterBySplineSegment_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_AllowedSegments_Inner = { "AllowedSegments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_AllowedSegments = { "AllowedSegments", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, AllowedSegments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedSegments_MetaData), NewProp_AllowedSegments_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterByDistance_SetBit(void* Obj)
{
	((UAuracronPCGSplinePointDistributorSettings*)Obj)->bFilterByDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterByDistance = { "bFilterByDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePointDistributorSettings), &Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterByDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByDistance_MetaData), NewProp_bFilterByDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistanceRange = { "DistanceRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePointDistributorSettings, DistanceRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceRange_MetaData), NewProp_DistanceRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistributionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistributionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_PointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_PointSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUsePointCountOverSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bIncludeSplineEnd,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_RandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_RandomVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_NoiseStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistributionCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_CurvatureThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_MinPointSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_MaxPointSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bAlignToSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bUseSplineWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_WidthAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bProjectToSurface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_ProjectionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_PointOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bRandomizeOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_OffsetVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterBySplineSegment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_AllowedSegments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_AllowedSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_bFilterByDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::NewProp_DistanceRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::ClassParams = {
	&UAuracronPCGSplinePointDistributorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSplinePointDistributorSettings);
UAuracronPCGSplinePointDistributorSettings::~UAuracronPCGSplinePointDistributorSettings() {}
// ********** End Class UAuracronPCGSplinePointDistributorSettings *********************************

// ********** Begin Class UAuracronPCGSplineMeshGeneratorSettings **********************************
void UAuracronPCGSplineMeshGeneratorSettings::StaticRegisterNativesUAuracronPCGSplineMeshGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings;
UClass* UAuracronPCGSplineMeshGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSplineMeshGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSplineMeshGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSplineMeshGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_NoRegister()
{
	return UAuracronPCGSplineMeshGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGSplineSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshDescriptors_MetaData[] = {
		{ "Category", "Mesh Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh generation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh generation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateStartCap_MetaData[] = {
		{ "Category", "Mesh Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartCapMesh_MetaData[] = {
		{ "Category", "Mesh Generation" },
		{ "EditCondition", "bGenerateStartCap" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateEndCap_MetaData[] = {
		{ "Category", "Mesh Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndCapMesh_MetaData[] = {
		{ "Category", "Mesh Generation" },
		{ "EditCondition", "bGenerateEndCap" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomSegmentation_MetaData[] = {
		{ "Category", "Segmentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Segmentation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segmentation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SegmentLength_MetaData[] = {
		{ "Category", "Segmentation" },
		{ "EditCondition", "bUseCustomSegmentation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdaptiveSegmentation_MetaData[] = {
		{ "Category", "Segmentation" },
		{ "EditCondition", "bUseCustomSegmentation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSegmentAngle_MetaData[] = {
		{ "Category", "Segmentation" },
		{ "EditCondition", "bAdaptiveSegmentation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bApplyDeformation_MetaData[] = {
		{ "Category", "Deformation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Deformation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Deformation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeformationMode_MetaData[] = {
		{ "Category", "Deformation" },
		{ "EditCondition", "bApplyDeformation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeformationStrength_MetaData[] = {
		{ "Category", "Deformation" },
		{ "EditCondition", "bApplyDeformation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeformationFrequency_MetaData[] = {
		{ "Category", "Deformation" },
		{ "EditCondition", "bApplyDeformation" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateUVs_MetaData[] = {
		{ "Category", "UV Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UV settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVTiling_MetaData[] = {
		{ "Category", "UV Settings" },
		{ "EditCondition", "bGenerateUVs" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWorldSpaceUVs_MetaData[] = {
		{ "Category", "UV Settings" },
		{ "EditCondition", "bGenerateUVs" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseInstancedMeshes_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLODs_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "Performance" },
		{ "EditCondition", "bUseLODs" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCulling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "EditCondition", "bUseCulling" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshDescriptors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshDescriptors;
	static void NewProp_bGenerateStartCap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateStartCap;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_StartCapMesh;
	static void NewProp_bGenerateEndCap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateEndCap;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EndCapMesh;
	static void NewProp_bUseCustomSegmentation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomSegmentation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SegmentLength;
	static void NewProp_bAdaptiveSegmentation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdaptiveSegmentation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSegmentAngle;
	static void NewProp_bApplyDeformation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bApplyDeformation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DeformationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DeformationMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeformationStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeformationFrequency;
	static void NewProp_bGenerateUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateUVs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UVTiling;
	static void NewProp_bUseWorldSpaceUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWorldSpaceUVs;
	static void NewProp_bUseInstancedMeshes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseInstancedMeshes;
	static void NewProp_bUseLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLODs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static void NewProp_bUseCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSplineMeshGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_MeshDescriptors_Inner = { "MeshDescriptors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 2638548500
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_MeshDescriptors = { "MeshDescriptors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, MeshDescriptors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshDescriptors_MetaData), NewProp_MeshDescriptors_MetaData) }; // 2638548500
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateStartCap_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bGenerateStartCap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateStartCap = { "bGenerateStartCap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateStartCap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateStartCap_MetaData), NewProp_bGenerateStartCap_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_StartCapMesh = { "StartCapMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, StartCapMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartCapMesh_MetaData), NewProp_StartCapMesh_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateEndCap_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bGenerateEndCap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateEndCap = { "bGenerateEndCap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateEndCap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateEndCap_MetaData), NewProp_bGenerateEndCap_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_EndCapMesh = { "EndCapMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, EndCapMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndCapMesh_MetaData), NewProp_EndCapMesh_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCustomSegmentation_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bUseCustomSegmentation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCustomSegmentation = { "bUseCustomSegmentation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCustomSegmentation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomSegmentation_MetaData), NewProp_bUseCustomSegmentation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_SegmentLength = { "SegmentLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, SegmentLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SegmentLength_MetaData), NewProp_SegmentLength_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bAdaptiveSegmentation_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bAdaptiveSegmentation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bAdaptiveSegmentation = { "bAdaptiveSegmentation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bAdaptiveSegmentation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdaptiveSegmentation_MetaData), NewProp_bAdaptiveSegmentation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_MaxSegmentAngle = { "MaxSegmentAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, MaxSegmentAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSegmentAngle_MetaData), NewProp_MaxSegmentAngle_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bApplyDeformation_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bApplyDeformation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bApplyDeformation = { "bApplyDeformation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bApplyDeformation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bApplyDeformation_MetaData), NewProp_bApplyDeformation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationMode = { "DeformationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, DeformationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDeformationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeformationMode_MetaData), NewProp_DeformationMode_MetaData) }; // 4083921318
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationStrength = { "DeformationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, DeformationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeformationStrength_MetaData), NewProp_DeformationStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationFrequency = { "DeformationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, DeformationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeformationFrequency_MetaData), NewProp_DeformationFrequency_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateUVs_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bGenerateUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateUVs = { "bGenerateUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateUVs_MetaData), NewProp_bGenerateUVs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_UVTiling = { "UVTiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, UVTiling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVTiling_MetaData), NewProp_UVTiling_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseWorldSpaceUVs_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bUseWorldSpaceUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseWorldSpaceUVs = { "bUseWorldSpaceUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseWorldSpaceUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWorldSpaceUVs_MetaData), NewProp_bUseWorldSpaceUVs_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseInstancedMeshes_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bUseInstancedMeshes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseInstancedMeshes = { "bUseInstancedMeshes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseInstancedMeshes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseInstancedMeshes_MetaData), NewProp_bUseInstancedMeshes_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseLODs_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bUseLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseLODs = { "bUseLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLODs_MetaData), NewProp_bUseLODs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCulling_SetBit(void* Obj)
{
	((UAuracronPCGSplineMeshGeneratorSettings*)Obj)->bUseCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCulling = { "bUseCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplineMeshGeneratorSettings), &Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCulling_MetaData), NewProp_bUseCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplineMeshGeneratorSettings, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_MeshDescriptors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_MeshDescriptors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateStartCap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_StartCapMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateEndCap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_EndCapMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCustomSegmentation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_SegmentLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bAdaptiveSegmentation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_MaxSegmentAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bApplyDeformation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_DeformationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bGenerateUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_UVTiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseWorldSpaceUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseInstancedMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_bUseCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::NewProp_CullingDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGSplineMeshGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSplineMeshGeneratorSettings);
UAuracronPCGSplineMeshGeneratorSettings::~UAuracronPCGSplineMeshGeneratorSettings() {}
// ********** End Class UAuracronPCGSplineMeshGeneratorSettings ************************************

// ********** Begin Class UAuracronPCGSplinePathFinderSettings *************************************
void UAuracronPCGSplinePathFinderSettings::StaticRegisterNativesUAuracronPCGSplinePathFinderSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings;
UClass* UAuracronPCGSplinePathFinderSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSplinePathFinderSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSplinePathFinderSettings"),
			Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSplinePathFinderSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_NoRegister()
{
	return UAuracronPCGSplinePathFinderSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGSplineSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathFindingDescriptor_MetaData[] = {
		{ "Category", "Path Finding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Path finding configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path finding configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateMultiplePaths_MetaData[] = {
		{ "Category", "Multiple Paths" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multiple paths\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiple paths" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathCount_MetaData[] = {
		{ "Category", "Multiple Paths" },
		{ "EditCondition", "bGenerateMultiplePaths" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathVariation_MetaData[] = {
		{ "Category", "Multiple Paths" },
		{ "EditCondition", "bGenerateMultiplePaths" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseTerrainCost_MetaData[] = {
		{ "Category", "Cost Functions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cost functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cost functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeCostMultiplier_MetaData[] = {
		{ "Category", "Cost Functions" },
		{ "EditCondition", "bUseTerrainCost" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDistanceCost_MetaData[] = {
		{ "Category", "Cost Functions" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceCostMultiplier_MetaData[] = {
		{ "Category", "Cost Functions" },
		{ "EditCondition", "bUseDistanceCost" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomCost_MetaData[] = {
		{ "Category", "Cost Functions" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CostAttribute_MetaData[] = {
		{ "Category", "Cost Functions" },
		{ "EditCondition", "bUseCustomCost" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizePath_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationIterations_MetaData[] = {
		{ "Category", "Optimization" },
		{ "EditCondition", "bOptimizePath" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationStrength_MetaData[] = {
		{ "Category", "Optimization" },
		{ "EditCondition", "bOptimizePath" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowSearchNodes_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowPathCosts_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputSearchStatistics_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathFindingDescriptor;
	static void NewProp_bGenerateMultiplePaths_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateMultiplePaths;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PathCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathVariation;
	static void NewProp_bUseTerrainCost_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseTerrainCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SlopeCostMultiplier;
	static void NewProp_bUseDistanceCost_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDistanceCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceCostMultiplier;
	static void NewProp_bUseCustomCost_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomCost;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CostAttribute;
	static void NewProp_bOptimizePath_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizePath;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OptimizationIterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OptimizationStrength;
	static void NewProp_bShowSearchNodes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowSearchNodes;
	static void NewProp_bShowPathCosts_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowPathCosts;
	static void NewProp_bOutputSearchStatistics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputSearchStatistics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSplinePathFinderSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_PathFindingDescriptor = { "PathFindingDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, PathFindingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathFindingDescriptor_MetaData), NewProp_PathFindingDescriptor_MetaData) }; // 3767220908
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bGenerateMultiplePaths_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bGenerateMultiplePaths = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bGenerateMultiplePaths = { "bGenerateMultiplePaths", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bGenerateMultiplePaths_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateMultiplePaths_MetaData), NewProp_bGenerateMultiplePaths_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_PathCount = { "PathCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, PathCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathCount_MetaData), NewProp_PathCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_PathVariation = { "PathVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, PathVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathVariation_MetaData), NewProp_PathVariation_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseTerrainCost_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bUseTerrainCost = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseTerrainCost = { "bUseTerrainCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseTerrainCost_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseTerrainCost_MetaData), NewProp_bUseTerrainCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_SlopeCostMultiplier = { "SlopeCostMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, SlopeCostMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeCostMultiplier_MetaData), NewProp_SlopeCostMultiplier_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseDistanceCost_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bUseDistanceCost = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseDistanceCost = { "bUseDistanceCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseDistanceCost_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDistanceCost_MetaData), NewProp_bUseDistanceCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_DistanceCostMultiplier = { "DistanceCostMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, DistanceCostMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceCostMultiplier_MetaData), NewProp_DistanceCostMultiplier_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseCustomCost_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bUseCustomCost = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseCustomCost = { "bUseCustomCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseCustomCost_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomCost_MetaData), NewProp_bUseCustomCost_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_CostAttribute = { "CostAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, CostAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CostAttribute_MetaData), NewProp_CostAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOptimizePath_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bOptimizePath = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOptimizePath = { "bOptimizePath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOptimizePath_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizePath_MetaData), NewProp_bOptimizePath_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_OptimizationIterations = { "OptimizationIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, OptimizationIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationIterations_MetaData), NewProp_OptimizationIterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_OptimizationStrength = { "OptimizationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSplinePathFinderSettings, OptimizationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationStrength_MetaData), NewProp_OptimizationStrength_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowSearchNodes_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bShowSearchNodes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowSearchNodes = { "bShowSearchNodes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowSearchNodes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowSearchNodes_MetaData), NewProp_bShowSearchNodes_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowPathCosts_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bShowPathCosts = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowPathCosts = { "bShowPathCosts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowPathCosts_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowPathCosts_MetaData), NewProp_bShowPathCosts_MetaData) };
void Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOutputSearchStatistics_SetBit(void* Obj)
{
	((UAuracronPCGSplinePathFinderSettings*)Obj)->bOutputSearchStatistics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOutputSearchStatistics = { "bOutputSearchStatistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSplinePathFinderSettings), &Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOutputSearchStatistics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputSearchStatistics_MetaData), NewProp_bOutputSearchStatistics_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_PathFindingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bGenerateMultiplePaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_PathCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_PathVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseTerrainCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_SlopeCostMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseDistanceCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_DistanceCostMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bUseCustomCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_CostAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOptimizePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_OptimizationIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_OptimizationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowSearchNodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bShowPathCosts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::NewProp_bOutputSearchStatistics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::ClassParams = {
	&UAuracronPCGSplinePathFinderSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSplinePathFinderSettings);
UAuracronPCGSplinePathFinderSettings::~UAuracronPCGSplinePathFinderSettings() {}
// ********** End Class UAuracronPCGSplinePathFinderSettings ***************************************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function CalculateSplineCurvature **********
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics
{
	struct AuracronPCGSplineSystemUtils_eventCalculateSplineCurvature_Parms
	{
		USplineComponent* SplineComponent;
		float Distance;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline analysis utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline analysis utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCalculateSplineCurvature_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCalculateSplineCurvature_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCalculateSplineCurvature_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "CalculateSplineCurvature", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::AuracronPCGSplineSystemUtils_eventCalculateSplineCurvature_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::AuracronPCGSplineSystemUtils_eventCalculateSplineCurvature_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execCalculateSplineCurvature)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGSplineSystemUtils::CalculateSplineCurvature(Z_Param_SplineComponent,Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function CalculateSplineCurvature ************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function CalculateSplineLength *************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics
{
	struct AuracronPCGSplineSystemUtils_eventCalculateSplineLength_Parms
	{
		USplineComponent* SplineComponent;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCalculateSplineLength_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCalculateSplineLength_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "CalculateSplineLength", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::AuracronPCGSplineSystemUtils_eventCalculateSplineLength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::AuracronPCGSplineSystemUtils_eventCalculateSplineLength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execCalculateSplineLength)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGSplineSystemUtils::CalculateSplineLength(Z_Param_SplineComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function CalculateSplineLength ***************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function CreateSplineFromPointData *********
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics
{
	struct AuracronPCGSplineSystemUtils_eventCreateSplineFromPointData_Parms
	{
		const UPCGPointData* PointData;
		bool bClosedSpline;
		USplineComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "CPP_Default_bClosedSpline", "false" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointData_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PointData;
	static void NewProp_bClosedSpline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClosedSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_PointData = { "PointData", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineFromPointData_Parms, PointData), Z_Construct_UClass_UPCGPointData_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointData_MetaData), NewProp_PointData_MetaData) };
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_bClosedSpline_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventCreateSplineFromPointData_Parms*)Obj)->bClosedSpline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_bClosedSpline = { "bClosedSpline", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventCreateSplineFromPointData_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_bClosedSpline_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineFromPointData_Parms, ReturnValue), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_PointData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_bClosedSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "CreateSplineFromPointData", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::AuracronPCGSplineSystemUtils_eventCreateSplineFromPointData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::AuracronPCGSplineSystemUtils_eventCreateSplineFromPointData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execCreateSplineFromPointData)
{
	P_GET_OBJECT(UPCGPointData,Z_Param_PointData);
	P_GET_UBOOL(Z_Param_bClosedSpline);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(USplineComponent**)Z_Param__Result=UAuracronPCGSplineSystemUtils::CreateSplineFromPointData(Z_Param_PointData,Z_Param_bClosedSpline);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function CreateSplineFromPointData ***********

// ********** Begin Class UAuracronPCGSplineSystemUtils Function CreateSplineFromPoints ************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics
{
	struct AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms
	{
		TArray<FVector> Points;
		bool bClosedSpline;
		EAuracronPCGSplineTangentMode TangentMode;
		USplineComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline creation utilities\n" },
#endif
		{ "CPP_Default_bClosedSpline", "false" },
		{ "CPP_Default_TangentMode", "Auto" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline creation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static void NewProp_bClosedSpline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClosedSpline;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TangentMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TangentMode;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_bClosedSpline_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms*)Obj)->bClosedSpline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_bClosedSpline = { "bClosedSpline", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_bClosedSpline_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_TangentMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_TangentMode = { "TangentMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms, TangentMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineTangentMode, METADATA_PARAMS(0, nullptr) }; // 1652141387
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms, ReturnValue), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_bClosedSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_TangentMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_TangentMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "CreateSplineFromPoints", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::AuracronPCGSplineSystemUtils_eventCreateSplineFromPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execCreateSplineFromPoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_GET_UBOOL(Z_Param_bClosedSpline);
	P_GET_ENUM(EAuracronPCGSplineTangentMode,Z_Param_TangentMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(USplineComponent**)Z_Param__Result=UAuracronPCGSplineSystemUtils::CreateSplineFromPoints(Z_Param_Out_Points,Z_Param_bClosedSpline,EAuracronPCGSplineTangentMode(Z_Param_TangentMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function CreateSplineFromPoints **************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function CreateSplineMeshComponent *********
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics
{
	struct AuracronPCGSplineSystemUtils_eventCreateSplineMeshComponent_Parms
	{
		USplineComponent* SplineComponent;
		UStaticMesh* StaticMesh;
		USplineMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh generation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh generation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineMeshComponent_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineMeshComponent_Parms, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventCreateSplineMeshComponent_Parms, ReturnValue), Z_Construct_UClass_USplineMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "CreateSplineMeshComponent", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::AuracronPCGSplineSystemUtils_eventCreateSplineMeshComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::AuracronPCGSplineSystemUtils_eventCreateSplineMeshComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execCreateSplineMeshComponent)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_OBJECT(UStaticMesh,Z_Param_StaticMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(USplineMeshComponent**)Z_Param__Result=UAuracronPCGSplineSystemUtils::CreateSplineMeshComponent(Z_Param_SplineComponent,Z_Param_StaticMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function CreateSplineMeshComponent ***********

// ********** Begin Class UAuracronPCGSplineSystemUtils Function DistributePointsAlongSpline *******
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics
{
	struct AuracronPCGSplineSystemUtils_eventDistributePointsAlongSpline_Parms
	{
		USplineComponent* SplineComponent;
		int32 PointCount;
		EAuracronPCGSplineDistributionMode DistributionMode;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Point distribution utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Point distribution utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointCount;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistributionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistributionMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventDistributePointsAlongSpline_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_PointCount = { "PointCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventDistributePointsAlongSpline_Parms, PointCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_DistributionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_DistributionMode = { "DistributionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventDistributePointsAlongSpline_Parms, DistributionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplineDistributionMode, METADATA_PARAMS(0, nullptr) }; // 1546632872
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventDistributePointsAlongSpline_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_PointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_DistributionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_DistributionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "DistributePointsAlongSpline", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::AuracronPCGSplineSystemUtils_eventDistributePointsAlongSpline_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::AuracronPCGSplineSystemUtils_eventDistributePointsAlongSpline_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execDistributePointsAlongSpline)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_PointCount);
	P_GET_ENUM(EAuracronPCGSplineDistributionMode,Z_Param_DistributionMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAuracronPCGSplineSystemUtils::DistributePointsAlongSpline(Z_Param_SplineComponent,Z_Param_PointCount,EAuracronPCGSplineDistributionMode(Z_Param_DistributionMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function DistributePointsAlongSpline *********

// ********** Begin Class UAuracronPCGSplineSystemUtils Function DistributeTransformsAlongSpline ***
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics
{
	struct AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms
	{
		USplineComponent* SplineComponent;
		int32 PointCount;
		bool bAlignToSpline;
		TArray<FTransform> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "CPP_Default_bAlignToSpline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointCount;
	static void NewProp_bAlignToSpline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToSpline;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_PointCount = { "PointCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms, PointCount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_bAlignToSpline_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms*)Obj)->bAlignToSpline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_bAlignToSpline = { "bAlignToSpline", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_bAlignToSpline_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_PointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_bAlignToSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "DistributeTransformsAlongSpline", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::AuracronPCGSplineSystemUtils_eventDistributeTransformsAlongSpline_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execDistributeTransformsAlongSpline)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_PointCount);
	P_GET_UBOOL(Z_Param_bAlignToSpline);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FTransform>*)Z_Param__Result=UAuracronPCGSplineSystemUtils::DistributeTransformsAlongSpline(Z_Param_SplineComponent,Z_Param_PointCount,Z_Param_bAlignToSpline);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function DistributeTransformsAlongSpline *****

// ********** Begin Class UAuracronPCGSplineSystemUtils Function FindPath **************************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics
{
	struct AuracronPCGSplineSystemUtils_eventFindPath_Parms
	{
		FVector Start;
		FVector End;
		FAuracronPCGPathFindingDescriptor PathFindingDescriptor;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Path finding utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path finding utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathFindingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathFindingDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventFindPath_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventFindPath_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_PathFindingDescriptor = { "PathFindingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventFindPath_Parms, PathFindingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathFindingDescriptor_MetaData), NewProp_PathFindingDescriptor_MetaData) }; // 3767220908
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventFindPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_PathFindingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "FindPath", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::AuracronPCGSplineSystemUtils_eventFindPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::AuracronPCGSplineSystemUtils_eventFindPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execFindPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_STRUCT_REF(FAuracronPCGPathFindingDescriptor,Z_Param_Out_PathFindingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAuracronPCGSplineSystemUtils::FindPath(Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Out_PathFindingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function FindPath ****************************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function GenerateSplineMeshes **************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics
{
	struct AuracronPCGSplineSystemUtils_eventGenerateSplineMeshes_Parms
	{
		USplineComponent* SplineComponent;
		TArray<FAuracronPCGSplineMeshDescriptor> MeshDescriptors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshDescriptors_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshDescriptors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshDescriptors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGenerateSplineMeshes_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_MeshDescriptors_Inner = { "MeshDescriptors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor, METADATA_PARAMS(0, nullptr) }; // 2638548500
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_MeshDescriptors = { "MeshDescriptors", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGenerateSplineMeshes_Parms, MeshDescriptors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshDescriptors_MetaData), NewProp_MeshDescriptors_MetaData) }; // 2638548500
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventGenerateSplineMeshes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventGenerateSplineMeshes_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_MeshDescriptors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_MeshDescriptors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "GenerateSplineMeshes", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::AuracronPCGSplineSystemUtils_eventGenerateSplineMeshes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::AuracronPCGSplineSystemUtils_eventGenerateSplineMeshes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execGenerateSplineMeshes)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_TARRAY_REF(FAuracronPCGSplineMeshDescriptor,Z_Param_Out_MeshDescriptors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGSplineSystemUtils::GenerateSplineMeshes(Z_Param_SplineComponent,Z_Param_Out_MeshDescriptors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function GenerateSplineMeshes ****************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function GetOptimalPointCount **************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics
{
	struct AuracronPCGSplineSystemUtils_eventGetOptimalPointCount_Parms
	{
		float SplineLength;
		float TargetSpacing;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "CPP_Default_TargetSpacing", "100.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SplineLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetSpacing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::NewProp_SplineLength = { "SplineLength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetOptimalPointCount_Parms, SplineLength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::NewProp_TargetSpacing = { "TargetSpacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetOptimalPointCount_Parms, TargetSpacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetOptimalPointCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::NewProp_SplineLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::NewProp_TargetSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "GetOptimalPointCount", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::AuracronPCGSplineSystemUtils_eventGetOptimalPointCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::AuracronPCGSplineSystemUtils_eventGetOptimalPointCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execGetOptimalPointCount)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SplineLength);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TargetSpacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGSplineSystemUtils::GetOptimalPointCount(Z_Param_SplineLength,Z_Param_TargetSpacing);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function GetOptimalPointCount ****************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function GetSplineDirectionAtDistance ******
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics
{
	struct AuracronPCGSplineSystemUtils_eventGetSplineDirectionAtDistance_Parms
	{
		USplineComponent* SplineComponent;
		float Distance;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetSplineDirectionAtDistance_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetSplineDirectionAtDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetSplineDirectionAtDistance_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "GetSplineDirectionAtDistance", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::AuracronPCGSplineSystemUtils_eventGetSplineDirectionAtDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::AuracronPCGSplineSystemUtils_eventGetSplineDirectionAtDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execGetSplineDirectionAtDistance)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGSplineSystemUtils::GetSplineDirectionAtDistance(Z_Param_SplineComponent,Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function GetSplineDirectionAtDistance ********

// ********** Begin Class UAuracronPCGSplineSystemUtils Function GetSplineWidthAtDistance **********
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics
{
	struct AuracronPCGSplineSystemUtils_eventGetSplineWidthAtDistance_Parms
	{
		USplineComponent* SplineComponent;
		float Distance;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetSplineWidthAtDistance_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetSplineWidthAtDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventGetSplineWidthAtDistance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "GetSplineWidthAtDistance", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::AuracronPCGSplineSystemUtils_eventGetSplineWidthAtDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::AuracronPCGSplineSystemUtils_eventGetSplineWidthAtDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execGetSplineWidthAtDistance)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGSplineSystemUtils::GetSplineWidthAtDistance(Z_Param_SplineComponent,Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function GetSplineWidthAtDistance ************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function IsPathValid ***********************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics
{
	struct AuracronPCGSplineSystemUtils_eventIsPathValid_Parms
	{
		TArray<FVector> Path;
		FAuracronPCGPathFindingDescriptor PathFindingDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Path_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathFindingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Path_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Path;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathFindingDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_Path_Inner = { "Path", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_Path = { "Path", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventIsPathValid_Parms, Path), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Path_MetaData), NewProp_Path_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_PathFindingDescriptor = { "PathFindingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventIsPathValid_Parms, PathFindingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathFindingDescriptor_MetaData), NewProp_PathFindingDescriptor_MetaData) }; // 3767220908
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventIsPathValid_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventIsPathValid_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_Path_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_Path,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_PathFindingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "IsPathValid", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::AuracronPCGSplineSystemUtils_eventIsPathValid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::AuracronPCGSplineSystemUtils_eventIsPathValid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execIsPathValid)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Path);
	P_GET_STRUCT_REF(FAuracronPCGPathFindingDescriptor,Z_Param_Out_PathFindingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGSplineSystemUtils::IsPathValid(Z_Param_Out_Path,Z_Param_Out_PathFindingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function IsPathValid *************************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function OptimizeSpline ********************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics
{
	struct AuracronPCGSplineSystemUtils_eventOptimizeSpline_Parms
	{
		USplineComponent* SplineComponent;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Optimization utilities\n" },
#endif
		{ "CPP_Default_Tolerance", "10.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventOptimizeSpline_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventOptimizeSpline_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventOptimizeSpline_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventOptimizeSpline_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "OptimizeSpline", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::AuracronPCGSplineSystemUtils_eventOptimizeSpline_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::AuracronPCGSplineSystemUtils_eventOptimizeSpline_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execOptimizeSpline)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGSplineSystemUtils::OptimizeSpline(Z_Param_SplineComponent,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function OptimizeSpline **********************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function SimplifySpline ********************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics
{
	struct AuracronPCGSplineSystemUtils_eventSimplifySpline_Parms
	{
		USplineComponent* SplineComponent;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "CPP_Default_Tolerance", "10.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventSimplifySpline_Parms, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventSimplifySpline_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventSimplifySpline_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventSimplifySpline_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "SimplifySpline", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::AuracronPCGSplineSystemUtils_eventSimplifySpline_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::AuracronPCGSplineSystemUtils_eventSimplifySpline_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execSimplifySpline)
{
	P_GET_OBJECT(USplineComponent,Z_Param_SplineComponent);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGSplineSystemUtils::SimplifySpline(Z_Param_SplineComponent,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function SimplifySpline **********************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function SmoothPath ************************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics
{
	struct AuracronPCGSplineSystemUtils_eventSmoothPath_Parms
	{
		TArray<FVector> Path;
		float SmoothingStrength;
		int32 Iterations;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "CPP_Default_Iterations", "3" },
		{ "CPP_Default_SmoothingStrength", "0.500000" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Path_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Path_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Path;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SmoothingStrength;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Iterations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_Path_Inner = { "Path", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_Path = { "Path", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventSmoothPath_Parms, Path), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Path_MetaData), NewProp_Path_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_SmoothingStrength = { "SmoothingStrength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventSmoothPath_Parms, SmoothingStrength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_Iterations = { "Iterations", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventSmoothPath_Parms, Iterations), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventSmoothPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_Path_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_Path,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_SmoothingStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_Iterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "SmoothPath", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::AuracronPCGSplineSystemUtils_eventSmoothPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::AuracronPCGSplineSystemUtils_eventSmoothPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execSmoothPath)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Path);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SmoothingStrength);
	P_GET_PROPERTY(FIntProperty,Z_Param_Iterations);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAuracronPCGSplineSystemUtils::SmoothPath(Z_Param_Out_Path,Z_Param_SmoothingStrength,Z_Param_Iterations);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function SmoothPath **************************

// ********** Begin Class UAuracronPCGSplineSystemUtils Function ValidateSplinePoints **************
struct Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics
{
	struct AuracronPCGSplineSystemUtils_eventValidateSplinePoints_Parms
	{
		TArray<FVector> Points;
		float MinDistance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spline System Utils" },
		{ "CPP_Default_MinDistance", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventValidateSplinePoints_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_MinDistance = { "MinDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGSplineSystemUtils_eventValidateSplinePoints_Parms, MinDistance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGSplineSystemUtils_eventValidateSplinePoints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGSplineSystemUtils_eventValidateSplinePoints_Parms), &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_MinDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGSplineSystemUtils, nullptr, "ValidateSplinePoints", Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::AuracronPCGSplineSystemUtils_eventValidateSplinePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::AuracronPCGSplineSystemUtils_eventValidateSplinePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGSplineSystemUtils::execValidateSplinePoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGSplineSystemUtils::ValidateSplinePoints(Z_Param_Out_Points,Z_Param_MinDistance);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGSplineSystemUtils Function ValidateSplinePoints ****************

// ********** Begin Class UAuracronPCGSplineSystemUtils ********************************************
void UAuracronPCGSplineSystemUtils::StaticRegisterNativesUAuracronPCGSplineSystemUtils()
{
	UClass* Class = UAuracronPCGSplineSystemUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateSplineCurvature", &UAuracronPCGSplineSystemUtils::execCalculateSplineCurvature },
		{ "CalculateSplineLength", &UAuracronPCGSplineSystemUtils::execCalculateSplineLength },
		{ "CreateSplineFromPointData", &UAuracronPCGSplineSystemUtils::execCreateSplineFromPointData },
		{ "CreateSplineFromPoints", &UAuracronPCGSplineSystemUtils::execCreateSplineFromPoints },
		{ "CreateSplineMeshComponent", &UAuracronPCGSplineSystemUtils::execCreateSplineMeshComponent },
		{ "DistributePointsAlongSpline", &UAuracronPCGSplineSystemUtils::execDistributePointsAlongSpline },
		{ "DistributeTransformsAlongSpline", &UAuracronPCGSplineSystemUtils::execDistributeTransformsAlongSpline },
		{ "FindPath", &UAuracronPCGSplineSystemUtils::execFindPath },
		{ "GenerateSplineMeshes", &UAuracronPCGSplineSystemUtils::execGenerateSplineMeshes },
		{ "GetOptimalPointCount", &UAuracronPCGSplineSystemUtils::execGetOptimalPointCount },
		{ "GetSplineDirectionAtDistance", &UAuracronPCGSplineSystemUtils::execGetSplineDirectionAtDistance },
		{ "GetSplineWidthAtDistance", &UAuracronPCGSplineSystemUtils::execGetSplineWidthAtDistance },
		{ "IsPathValid", &UAuracronPCGSplineSystemUtils::execIsPathValid },
		{ "OptimizeSpline", &UAuracronPCGSplineSystemUtils::execOptimizeSpline },
		{ "SimplifySpline", &UAuracronPCGSplineSystemUtils::execSimplifySpline },
		{ "SmoothPath", &UAuracronPCGSplineSystemUtils::execSmoothPath },
		{ "ValidateSplinePoints", &UAuracronPCGSplineSystemUtils::execValidateSplinePoints },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils;
UClass* UAuracronPCGSplineSystemUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSplineSystemUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSplineSystemUtils"),
			Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSplineSystemUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSplineSystemUtils_NoRegister()
{
	return UAuracronPCGSplineSystemUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spline System Utilities\n * Utility functions for advanced spline system operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGSplineSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGSplineSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline System Utilities\nUtility functions for advanced spline system operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineCurvature, "CalculateSplineCurvature" }, // 316943231
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CalculateSplineLength, "CalculateSplineLength" }, // 3619394774
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPointData, "CreateSplineFromPointData" }, // 3901721336
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineFromPoints, "CreateSplineFromPoints" }, // 3125480923
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_CreateSplineMeshComponent, "CreateSplineMeshComponent" }, // 580184695
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributePointsAlongSpline, "DistributePointsAlongSpline" }, // 1025469787
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_DistributeTransformsAlongSpline, "DistributeTransformsAlongSpline" }, // 3644000201
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_FindPath, "FindPath" }, // 2697612566
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GenerateSplineMeshes, "GenerateSplineMeshes" }, // 241783366
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetOptimalPointCount, "GetOptimalPointCount" }, // 2173975935
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineDirectionAtDistance, "GetSplineDirectionAtDistance" }, // 928708518
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_GetSplineWidthAtDistance, "GetSplineWidthAtDistance" }, // 2650308156
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_IsPathValid, "IsPathValid" }, // 2916332145
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_OptimizeSpline, "OptimizeSpline" }, // 1718655581
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SimplifySpline, "SimplifySpline" }, // 4123177716
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_SmoothPath, "SmoothPath" }, // 2526733641
		{ &Z_Construct_UFunction_UAuracronPCGSplineSystemUtils_ValidateSplinePoints, "ValidateSplinePoints" }, // 2941929483
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSplineSystemUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics::ClassParams = {
	&UAuracronPCGSplineSystemUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSplineSystemUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils.OuterSingleton;
}
UAuracronPCGSplineSystemUtils::UAuracronPCGSplineSystemUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSplineSystemUtils);
UAuracronPCGSplineSystemUtils::~UAuracronPCGSplineSystemUtils() {}
// ********** End Class UAuracronPCGSplineSystemUtils **********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGSplineCreationMode_StaticEnum, TEXT("EAuracronPCGSplineCreationMode"), &Z_Registration_Info_UEnum_EAuracronPCGSplineCreationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1427174248U) },
		{ EAuracronPCGSplineDistributionMode_StaticEnum, TEXT("EAuracronPCGSplineDistributionMode"), &Z_Registration_Info_UEnum_EAuracronPCGSplineDistributionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1546632872U) },
		{ EAuracronPCGSplineMeshMode_StaticEnum, TEXT("EAuracronPCGSplineMeshMode"), &Z_Registration_Info_UEnum_EAuracronPCGSplineMeshMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1530834170U) },
		{ EAuracronPCGSplineTangentMode_StaticEnum, TEXT("EAuracronPCGSplineTangentMode"), &Z_Registration_Info_UEnum_EAuracronPCGSplineTangentMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1652141387U) },
		{ EAuracronPCGPathFindingMode_StaticEnum, TEXT("EAuracronPCGPathFindingMode"), &Z_Registration_Info_UEnum_EAuracronPCGPathFindingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 720469112U) },
		{ EAuracronPCGSplineDeformationMode_StaticEnum, TEXT("EAuracronPCGSplineDeformationMode"), &Z_Registration_Info_UEnum_EAuracronPCGSplineDeformationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4083921318U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGSplinePointDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics::NewStructOps, TEXT("AuracronPCGSplinePointDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGSplinePointDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGSplinePointDescriptor), 3328814068U) },
		{ FAuracronPCGSplineMeshDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics::NewStructOps, TEXT("AuracronPCGSplineMeshDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGSplineMeshDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGSplineMeshDescriptor), 2638548500U) },
		{ FAuracronPCGPathFindingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics::NewStructOps, TEXT("AuracronPCGPathFindingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGPathFindingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGPathFindingDescriptor), 3767220908U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings, UAuracronPCGAdvancedSplineCreatorSettings::StaticClass, TEXT("UAuracronPCGAdvancedSplineCreatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedSplineCreatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedSplineCreatorSettings), 111664578U) },
		{ Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings, UAuracronPCGSplinePointDistributorSettings::StaticClass, TEXT("UAuracronPCGSplinePointDistributorSettings"), &Z_Registration_Info_UClass_UAuracronPCGSplinePointDistributorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSplinePointDistributorSettings), 1848115008U) },
		{ Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings, UAuracronPCGSplineMeshGeneratorSettings::StaticClass, TEXT("UAuracronPCGSplineMeshGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGSplineMeshGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSplineMeshGeneratorSettings), 3528101084U) },
		{ Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings, UAuracronPCGSplinePathFinderSettings::StaticClass, TEXT("UAuracronPCGSplinePathFinderSettings"), &Z_Registration_Info_UClass_UAuracronPCGSplinePathFinderSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSplinePathFinderSettings), 3079154215U) },
		{ Z_Construct_UClass_UAuracronPCGSplineSystemUtils, UAuracronPCGSplineSystemUtils::StaticClass, TEXT("UAuracronPCGSplineSystemUtils"), &Z_Registration_Info_UClass_UAuracronPCGSplineSystemUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSplineSystemUtils), 3176924410U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_1813292776(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
