// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Landscape Integration Advanced Utilities Implementation
// Bridge 2.7: PCG Framework - Landscape Integration

#include "AuracronPCGLandscapeIntegration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Landscape includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeDataAccess.h"
#include "LandscapeEdit.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

namespace AuracronPCGLandscapeIntegrationUtils
{
    // =============================================================================
    // LANDSCAPE COMPONENT ACCESS
    // =============================================================================

    ULandscapeComponent* FindLandscapeComponentAtLocation(ALandscape* Landscape, const FVector& WorldLocation)
    {
        if (!Landscape)
        {
            return nullptr;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return nullptr;
        }

        // Convert world location to landscape coordinates
        FIntPoint LandscapeCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, WorldLocation);
        
        // Find the component that contains this coordinate
        return LandscapeInfo->XYtoComponentMap.FindRef(LandscapeCoord);
    }

    // =============================================================================
    // HEIGHT DATA ACCESS
    // =============================================================================

    bool GetLandscapeHeightData(ALandscape* Landscape, const FBox& Area, TArray<float>& OutHeightData, FIntPoint& OutDataSize)
    {
        if (!Landscape)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);
        FIntPoint MaxCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Max);

        // Calculate data size
        OutDataSize.X = MaxCoord.X - MinCoord.X + 1;
        OutDataSize.Y = MaxCoord.Y - MinCoord.Y + 1;

        // Allocate output array
        OutHeightData.SetNum(OutDataSize.X * OutDataSize.Y);

        // Access landscape data
        FLandscapeDataAccess DataAccess(LandscapeInfo);

        // Read height data
        for (int32 Y = 0; Y < OutDataSize.Y; Y++)
        {
            for (int32 X = 0; X < OutDataSize.X; X++)
            {
                int32 Index = Y * OutDataSize.X + X;
                int32 LandscapeX = MinCoord.X + X;
                int32 LandscapeY = MinCoord.Y + Y;

                uint16 HeightValue = 0;
                if (DataAccess.GetHeightData(LandscapeX, LandscapeY, HeightValue))
                {
                    OutHeightData[Index] = LandscapeDataAccess::GetLocalHeight(HeightValue);
                }
                else
                {
                    OutHeightData[Index] = 0.0f;
                }
            }
        }

        return true;
    }

    bool SetLandscapeHeightData(ALandscape* Landscape, const FBox& Area, const TArray<float>& HeightData, const FIntPoint& DataSize)
    {
        if (!Landscape || HeightData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);

        // Prepare height data for writing
        TArray<uint16> LandscapeHeightData;
        LandscapeHeightData.SetNum(HeightData.Num());

        for (int32 i = 0; i < HeightData.Num(); i++)
        {
            LandscapeHeightData[i] = LandscapeDataAccess::GetTexHeight(HeightData[i]);
        }

        // Write height data to landscape
        FLandscapeEditDataInterface LandscapeEdit(LandscapeInfo);
        LandscapeEdit.SetHeightData(MinCoord.X, MinCoord.Y, MinCoord.X + DataSize.X - 1, MinCoord.Y + DataSize.Y - 1, 
                                   LandscapeHeightData.GetData(), 0, true);

        return true;
    }

    // =============================================================================
    // LAYER DATA ACCESS
    // =============================================================================

    bool GetLandscapeLayerData(ALandscape* Landscape, const FString& LayerName, const FBox& Area, TArray<float>& OutLayerData, FIntPoint& OutDataSize)
    {
        if (!Landscape)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Find layer info object
        ULandscapeLayerInfoObject* LayerInfo = nullptr;
        for (const FLandscapeInfoLayerSettings& LayerSettings : LandscapeInfo->Layers)
        {
            if (LayerSettings.LayerInfoObj && LayerSettings.LayerName.ToString() == LayerName)
            {
                LayerInfo = LayerSettings.LayerInfoObj;
                break;
            }
        }

        if (!LayerInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);
        FIntPoint MaxCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Max);

        // Calculate data size
        OutDataSize.X = MaxCoord.X - MinCoord.X + 1;
        OutDataSize.Y = MaxCoord.Y - MinCoord.Y + 1;

        // Allocate output array
        OutLayerData.SetNum(OutDataSize.X * OutDataSize.Y);

        // Access landscape data
        FLandscapeDataAccess DataAccess(LandscapeInfo);

        // Read layer data
        for (int32 Y = 0; Y < OutDataSize.Y; Y++)
        {
            for (int32 X = 0; X < OutDataSize.X; X++)
            {
                int32 Index = Y * OutDataSize.X + X;
                int32 LandscapeX = MinCoord.X + X;
                int32 LandscapeY = MinCoord.Y + Y;

                uint8 WeightValue = 0;
                if (DataAccess.GetWeightData(LayerInfo, LandscapeX, LandscapeY, WeightValue))
                {
                    OutLayerData[Index] = static_cast<float>(WeightValue) / 255.0f;
                }
                else
                {
                    OutLayerData[Index] = 0.0f;
                }
            }
        }

        return true;
    }

    bool SetLandscapeLayerData(ALandscape* Landscape, const FString& LayerName, const FBox& Area, const TArray<float>& LayerData, const FIntPoint& DataSize)
    {
        if (!Landscape || LayerData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Find layer info object
        ULandscapeLayerInfoObject* LayerInfo = nullptr;
        for (const FLandscapeInfoLayerSettings& LayerSettings : LandscapeInfo->Layers)
        {
            if (LayerSettings.LayerInfoObj && LayerSettings.LayerName.ToString() == LayerName)
            {
                LayerInfo = LayerSettings.LayerInfoObj;
                break;
            }
        }

        if (!LayerInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);

        // Prepare layer data for writing
        TArray<uint8> LandscapeLayerData;
        LandscapeLayerData.SetNum(LayerData.Num());

        for (int32 i = 0; i < LayerData.Num(); i++)
        {
            LandscapeLayerData[i] = static_cast<uint8>(FMath::Clamp(LayerData[i] * 255.0f, 0.0f, 255.0f));
        }

        // Write layer data to landscape
        FLandscapeEditDataInterface LandscapeEdit(LandscapeInfo);
        LandscapeEdit.SetAlphaData(LayerInfo, MinCoord.X, MinCoord.Y, MinCoord.X + DataSize.X - 1, MinCoord.Y + DataSize.Y - 1, 
                                  LandscapeLayerData.GetData(), 0);

        return true;
    }

    // =============================================================================
    // ADVANCED CALCULATIONS
    // =============================================================================

    float CalculateLandscapeCurvature(ALandscape* Landscape, const FVector& WorldLocation, float SampleRadius)
    {
        if (!Landscape)
        {
            return 0.0f;
        }

        // Sample heights in a cross pattern
        float CenterHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation);
        float NorthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, SampleRadius, 0.0f));
        float SouthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, -SampleRadius, 0.0f));
        float EastHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(SampleRadius, 0.0f, 0.0f));
        float WestHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(-SampleRadius, 0.0f, 0.0f));

        // Calculate second derivatives (curvature)
        float CurvatureX = (EastHeight - 2.0f * CenterHeight + WestHeight) / (SampleRadius * SampleRadius);
        float CurvatureY = (NorthHeight - 2.0f * CenterHeight + SouthHeight) / (SampleRadius * SampleRadius);

        // Return mean curvature
        return (CurvatureX + CurvatureY) * 0.5f;
    }

    FVector CalculateLandscapeGradient(ALandscape* Landscape, const FVector& WorldLocation, float SampleRadius)
    {
        if (!Landscape)
        {
            return FVector::ZeroVector;
        }

        // Sample heights in cardinal directions
        float CenterHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation);
        float NorthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, SampleRadius, 0.0f));
        float SouthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, -SampleRadius, 0.0f));
        float EastHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(SampleRadius, 0.0f, 0.0f));
        float WestHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(-SampleRadius, 0.0f, 0.0f));

        // Calculate gradient
        float GradientX = (EastHeight - WestHeight) / (2.0f * SampleRadius);
        float GradientY = (NorthHeight - SouthHeight) / (2.0f * SampleRadius);

        return FVector(GradientX, GradientY, 0.0f);
    }

    // =============================================================================
    // VALIDATION AND OPTIMIZATION
    // =============================================================================

    bool ValidateLandscapeForModification(ALandscape* Landscape)
    {
        if (!Landscape)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Check if landscape is valid for editing
        if (LandscapeInfo->LandscapeComponents.Num() == 0)
        {
            return false;
        }

        // Check if landscape is not read-only
        // Additional validation can be added here

        return true;
    }

    void OptimizeLandscapeAfterModification(ALandscape* Landscape, const FBox& ModifiedArea)
    {
        if (!Landscape)
        {
            return;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return;
        }

        // Update collision for modified area
        // Find components that intersect with the modified area
        for (ULandscapeComponent* Component : LandscapeInfo->LandscapeComponents)
        {
            if (Component)
            {
                FBox ComponentBounds = Component->Bounds.GetBox();
                if (ComponentBounds.Intersect(ModifiedArea))
                {
                    // Update collision for this component
                    Component->UpdateCollisionData();
                }
            }
        }

        // Update navigation mesh if needed
        // This would typically be handled by the navigation system automatically
    }

    // =============================================================================
    // EROSION ALGORITHMS
    // =============================================================================

    bool PerformThermalErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor)
    {
        if (HeightData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        TArray<float> NewHeightData = HeightData;
        const float TalusAngleRadians = FMath::DegreesToRadians(ErosionDescriptor.TalusAngle);
        const float MaxSlope = FMath::Tan(TalusAngleRadians);

        for (int32 Iteration = 0; Iteration < ErosionDescriptor.Iterations; Iteration++)
        {
            for (int32 Y = 1; Y < DataSize.Y - 1; Y++)
            {
                for (int32 X = 1; X < DataSize.X - 1; X++)
                {
                    int32 Index = Y * DataSize.X + X;
                    float CenterHeight = HeightData[Index];

                    // Check all 8 neighbors
                    for (int32 DY = -1; DY <= 1; DY++)
                    {
                        for (int32 DX = -1; DX <= 1; DX++)
                        {
                            if (DX == 0 && DY == 0) continue;

                            int32 NeighborIndex = (Y + DY) * DataSize.X + (X + DX);
                            float NeighborHeight = HeightData[NeighborIndex];
                            float HeightDiff = CenterHeight - NeighborHeight;
                            float Distance = FMath::Sqrt(static_cast<float>(DX * DX + DY * DY));

                            if (HeightDiff > MaxSlope * Distance)
                            {
                                float Excess = HeightDiff - MaxSlope * Distance;
                                float Transfer = Excess * ErosionDescriptor.ThermalStrength * 0.5f;

                                NewHeightData[Index] -= Transfer;
                                NewHeightData[NeighborIndex] += Transfer;
                            }
                        }
                    }
                }
            }

            HeightData = NewHeightData;
        }

        return true;
    }

    bool PerformHydraulicErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor)
    {
        if (HeightData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        // Initialize water and sediment maps
        TArray<float> WaterMap;
        TArray<float> SedimentMap;
        WaterMap.SetNumZeroed(HeightData.Num());
        SedimentMap.SetNumZeroed(HeightData.Num());

        for (int32 Iteration = 0; Iteration < ErosionDescriptor.Iterations; Iteration++)
        {
            // Add rain
            for (int32 i = 0; i < HeightData.Num(); i++)
            {
                WaterMap[i] += ErosionDescriptor.RainAmount;
            }

            // Flow simulation and erosion
            for (int32 Y = 1; Y < DataSize.Y - 1; Y++)
            {
                for (int32 X = 1; X < DataSize.X - 1; X++)
                {
                    int32 Index = Y * DataSize.X + X;
                    float TotalHeight = HeightData[Index] + WaterMap[Index];

                    // Find steepest descent
                    float MaxHeightDiff = 0.0f;
                    int32 FlowTarget = -1;

                    for (int32 DY = -1; DY <= 1; DY++)
                    {
                        for (int32 DX = -1; DX <= 1; DX++)
                        {
                            if (DX == 0 && DY == 0) continue;

                            int32 NeighborIndex = (Y + DY) * DataSize.X + (X + DX);
                            float NeighborTotalHeight = HeightData[NeighborIndex] + WaterMap[NeighborIndex];
                            float HeightDiff = TotalHeight - NeighborTotalHeight;

                            if (HeightDiff > MaxHeightDiff)
                            {
                                MaxHeightDiff = HeightDiff;
                                FlowTarget = NeighborIndex;
                            }
                        }
                    }

                    // Perform erosion and deposition
                    if (FlowTarget >= 0 && MaxHeightDiff > 0.0f)
                    {
                        float Velocity = FMath::Sqrt(MaxHeightDiff);
                        float Capacity = ErosionDescriptor.SedimentCapacity * Velocity;
                        float CurrentSediment = SedimentMap[Index];

                        if (CurrentSediment > Capacity)
                        {
                            // Deposition
                            float Deposition = (CurrentSediment - Capacity) * ErosionDescriptor.Strength;
                            HeightData[Index] += Deposition;
                            SedimentMap[Index] -= Deposition;
                        }
                        else
                        {
                            // Erosion
                            float Erosion = FMath::Min((Capacity - CurrentSediment) * ErosionDescriptor.Strength, HeightData[Index]);
                            HeightData[Index] -= Erosion;
                            SedimentMap[Index] += Erosion;
                        }

                        // Flow water and sediment
                        float WaterFlow = FMath::Min(WaterMap[Index], MaxHeightDiff);
                        WaterMap[Index] -= WaterFlow;
                        WaterMap[FlowTarget] += WaterFlow;

                        float SedimentFlow = SedimentMap[Index] * (WaterFlow / (WaterMap[Index] + WaterFlow));
                        SedimentMap[Index] -= SedimentFlow;
                        SedimentMap[FlowTarget] += SedimentFlow;
                    }
                }
            }

            // Evaporation
            for (int32 i = 0; i < WaterMap.Num(); i++)
            {
                WaterMap[i] *= (1.0f - ErosionDescriptor.Evaporation);
            }
        }

        return true;
    }

    bool PerformWindErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor)
    {
        // Advanced wind erosion implementation using UE5.6 landscape processing
        
        if (HeightData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }
        
        FVector2D WindDir2D(ErosionDescriptor.WindDirection.X, ErosionDescriptor.WindDirection.Y);
        WindDir2D.Normalize();
        
        // Create temporary arrays for calculations
        TArray<float> ExposureMap;
        TArray<float> SedimentMap;
        TArray<float> VelocityMap;
        
        ExposureMap.SetNumZeroed(HeightData.Num());
        SedimentMap.SetNumZeroed(HeightData.Num());
        VelocityMap.SetNumZeroed(HeightData.Num());
        
        // Wind erosion parameters
        float SuspensionCapacity = ErosionDescriptor.WindStrength * 0.1f;
        float DepositionRate = 0.3f;
        float EvaporationRate = 0.01f;
        float MinSlope = 0.01f;
        
        for (int32 Iteration = 0; Iteration < ErosionDescriptor.Iterations; Iteration++)
        {
            // Calculate wind exposure for each point
            for (int32 Y = 1; Y < DataSize.Y - 1; Y++)
            {
                for (int32 X = 1; X < DataSize.X - 1; X++)
                {
                    int32 Index = Y * DataSize.X + X;
                    float CurrentHeight = HeightData[Index];
                    
                    // Calculate exposure based on upwind terrain
                    float Exposure = 1.0f;
                    float MaxExposureDistance = 10.0f; // Sample distance in grid units
                    
                    // Sample upwind direction
                    for (float Distance = 1.0f; Distance <= MaxExposureDistance; Distance += 1.0f)
                    {
                        float SampleX = X - WindDir2D.X * Distance;
                        float SampleY = Y - WindDir2D.Y * Distance;
                        
                        // Bounds check
                        if (SampleX >= 0 && SampleX < DataSize.X - 1 && SampleY >= 0 && SampleY < DataSize.Y - 1)
                        {
                            // Bilinear interpolation for height sampling
                            int32 X0 = FMath::FloorToInt(SampleX);
                            int32 Y0 = FMath::FloorToInt(SampleY);
                            int32 X1 = FMath::Min(X0 + 1, DataSize.X - 1);
                            int32 Y1 = FMath::Min(Y0 + 1, DataSize.Y - 1);
                            
                            float FracX = SampleX - X0;
                            float FracY = SampleY - Y0;
                            
                            float H00 = HeightData[Y0 * DataSize.X + X0];
                            float H10 = HeightData[Y0 * DataSize.X + X1];
                            float H01 = HeightData[Y1 * DataSize.X + X0];
                            float H11 = HeightData[Y1 * DataSize.X + X1];
                            
                            float SampleHeight = FMath::BiLerp(H00, H10, H01, H11, FracX, FracY);
                            
                            // Calculate shadow effect
                            float HeightDiff = SampleHeight - CurrentHeight;
                            float ShadowAngle = FMath::Atan2(HeightDiff, Distance);
                            
                            if (ShadowAngle > 0)
                            {
                                Exposure *= FMath::Max(0.1f, 1.0f - (ShadowAngle / (PI * 0.25f))); // Reduce exposure in shadow
                            }
                        }
                    }
                    
                    ExposureMap[Index] = FMath::Clamp(Exposure, 0.0f, 1.0f);
                    
                    // Calculate wind velocity based on terrain
                    float LocalSlope = 0.0f;
                    if (X > 0 && X < DataSize.X - 1 && Y > 0 && Y < DataSize.Y - 1)
                    {
                        float DX = HeightData[Y * DataSize.X + (X + 1)] - HeightData[Y * DataSize.X + (X - 1)];
                        float DY = HeightData[(Y + 1) * DataSize.X + X] - HeightData[(Y - 1) * DataSize.X + X];
                        LocalSlope = FMath::Sqrt(DX * DX + DY * DY) * 0.5f;
                    }
                    
                    // Wind accelerates over ridges, decelerates in valleys
                    float VelocityMultiplier = 1.0f + LocalSlope * 0.5f;
                    VelocityMap[Index] = ErosionDescriptor.WindStrength * VelocityMultiplier * Exposure;
                }
            }
            
            // Perform erosion and sediment transport
            for (int32 Y = 1; Y < DataSize.Y - 1; Y++)
            {
                for (int32 X = 1; X < DataSize.X - 1; X++)
                {
                    int32 Index = Y * DataSize.X + X;
                    
                    float WindVelocity = VelocityMap[Index];
                    float Exposure = ExposureMap[Index];
                    
                    // Calculate erosion capacity based on wind velocity and exposure
                    float ErosionCapacity = WindVelocity * WindVelocity * Exposure * ErosionDescriptor.Strength;
                    
                    // Calculate actual erosion (limited by material availability)
                    float MaxErosion = FMath::Max(0.0f, HeightData[Index] * 0.01f); // Limit to 1% of height per iteration
                    float ActualErosion = FMath::Min(ErosionCapacity, MaxErosion);
                    
                    // Apply erosion
                    HeightData[Index] -= ActualErosion;
                    SedimentMap[Index] += ActualErosion;
                    
                    // Transport sediment downwind
                    if (SedimentMap[Index] > 0.0f)
                    {
                        float TransportDistance = WindVelocity * 0.5f; // Distance sediment travels
                        
                        int32 TargetX = FMath::Clamp(FMath::RoundToInt(X + WindDir2D.X * TransportDistance), 0, DataSize.X - 1);
                        int32 TargetY = FMath::Clamp(FMath::RoundToInt(Y + WindDir2D.Y * TransportDistance), 0, DataSize.Y - 1);
                        int32 TargetIndex = TargetY * DataSize.X + TargetX;
                        
                        // Deposit sediment based on deposition rate and wind velocity
                        float DepositionAmount = SedimentMap[Index] * DepositionRate * (1.0f - WindVelocity / ErosionDescriptor.WindStrength);
                        DepositionAmount = FMath::Max(0.0f, DepositionAmount);
                        
                        if (TargetIndex != Index && TargetIndex >= 0 && TargetIndex < HeightData.Num())
                        {
                            HeightData[TargetIndex] += DepositionAmount;
                            SedimentMap[Index] -= DepositionAmount;
                        }
                        
                        // Evaporation/settling of remaining sediment
                        SedimentMap[Index] *= (1.0f - EvaporationRate);
                    }
                }
            }
            
            // Deposit remaining sediment
            for (int32 i = 0; i < HeightData.Num(); i++)
            {
                if (SedimentMap[i] > 0.0f)
                {
                    HeightData[i] += SedimentMap[i] * 0.1f; // Deposit 10% of remaining sediment
                    SedimentMap[i] *= 0.9f;
                }
            }
        }
        
        return true;
    }

    // =============================================================================
    // BLENDING FUNCTIONS
    // =============================================================================

    float ApplyBlendMode(float BaseValue, float BlendValue, EAuracronPCGLandscapeBlendMode BlendMode, float BlendStrength)
    {
        float Result = BaseValue;

        switch (BlendMode)
        {
            case EAuracronPCGLandscapeBlendMode::Replace:
                Result = BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Add:
                Result = BaseValue + BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Subtract:
                Result = BaseValue - BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Multiply:
                Result = BaseValue * BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Screen:
                Result = 1.0f - (1.0f - BaseValue) * (1.0f - BlendValue);
                break;
            case EAuracronPCGLandscapeBlendMode::Overlay:
                if (BaseValue < 0.5f)
                    Result = 2.0f * BaseValue * BlendValue;
                else
                    Result = 1.0f - 2.0f * (1.0f - BaseValue) * (1.0f - BlendValue);
                break;
            default:
                Result = BlendValue;
                break;
        }

        return FMath::Lerp(BaseValue, Result, BlendStrength);
    }
}
