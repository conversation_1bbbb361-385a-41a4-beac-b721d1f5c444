// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronRealmsBridge_init() {}
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature();
	AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronRealmsBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronRealmsBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronRealmsBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronRealmsBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x26DE2F8A,
				0xE57D7322,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronRealmsBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronRealmsBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronRealmsBridge(Z_Construct_UPackage__Script_AuracronRealmsBridge, TEXT("/Script/AuracronRealmsBridge"), Z_Registration_Info_UPackage__Script_AuracronRealmsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x26DE2F8A, 0xE57D7322));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
