// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGManager.h"
#include "AuracronPCGFramework.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGManager();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGManager_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType();
AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature();
AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature();
AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGConfiguration();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGErrorInfo();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGenerationResult();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSubsystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnPCGGenerationComplete **********************************************
struct Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics
{
	struct _Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms
	{
		bool bSuccess;
		FAuracronPCGPerformanceMetrics Metrics;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate declarations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate declarations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Metrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms), &Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::NewProp_Metrics = { "Metrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms, Metrics), Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metrics_MetaData), NewProp_Metrics_MetaData) }; // 68781111
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::NewProp_Metrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge, nullptr, "OnPCGGenerationComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::_Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::_Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGGenerationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationComplete, bool bSuccess, FAuracronPCGPerformanceMetrics const& Metrics)
{
	struct _Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms
	{
		bool bSuccess;
		FAuracronPCGPerformanceMetrics Metrics;
	};
	_Script_AuracronPCGBridge_eventOnPCGGenerationComplete_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	Parms.Metrics=Metrics;
	OnPCGGenerationComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGGenerationComplete ************************************************

// ********** Begin Delegate FOnPCGGenerationProgress **********************************************
struct Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics
{
	struct _Script_AuracronPCGBridge_eventOnPCGGenerationProgress_Parms
	{
		float Progress;
		FString CurrentOperation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentOperation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentOperation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronPCGBridge_eventOnPCGGenerationProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::NewProp_CurrentOperation = { "CurrentOperation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronPCGBridge_eventOnPCGGenerationProgress_Parms, CurrentOperation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentOperation_MetaData), NewProp_CurrentOperation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::NewProp_CurrentOperation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge, nullptr, "OnPCGGenerationProgress__DelegateSignature", Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::_Script_AuracronPCGBridge_eventOnPCGGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::_Script_AuracronPCGBridge_eventOnPCGGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGGenerationProgress_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationProgress, float Progress, const FString& CurrentOperation)
{
	struct _Script_AuracronPCGBridge_eventOnPCGGenerationProgress_Parms
	{
		float Progress;
		FString CurrentOperation;
	};
	_Script_AuracronPCGBridge_eventOnPCGGenerationProgress_Parms Parms;
	Parms.Progress=Progress;
	Parms.CurrentOperation=CurrentOperation;
	OnPCGGenerationProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGGenerationProgress ************************************************

// ********** Begin Delegate FOnPCGError ***********************************************************
struct Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics
{
	struct _Script_AuracronPCGBridge_eventOnPCGError_Parms
	{
		FAuracronPCGErrorInfo ErrorInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ErrorInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::NewProp_ErrorInfo = { "ErrorInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronPCGBridge_eventOnPCGError_Parms, ErrorInfo), Z_Construct_UScriptStruct_FAuracronPCGErrorInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorInfo_MetaData), NewProp_ErrorInfo_MetaData) }; // 1923069771
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::NewProp_ErrorInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge, nullptr, "OnPCGError__DelegateSignature", Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::_Script_AuracronPCGBridge_eventOnPCGError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::_Script_AuracronPCGBridge_eventOnPCGError_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGError_DelegateWrapper(const FMulticastScriptDelegate& OnPCGError, FAuracronPCGErrorInfo const& ErrorInfo)
{
	struct _Script_AuracronPCGBridge_eventOnPCGError_Parms
	{
		FAuracronPCGErrorInfo ErrorInfo;
	};
	_Script_AuracronPCGBridge_eventOnPCGError_Parms Parms;
	Parms.ErrorInfo=ErrorInfo;
	OnPCGError.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGError *************************************************************

// ********** Begin ScriptStruct FAuracronPCGGenerationRequest *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest;
class UScriptStruct* FAuracronPCGGenerationRequest::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGGenerationRequest"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG generation request structure\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG generation request structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationType_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraph_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActor_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationBounds_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAsynchronous_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Seed_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_GenerationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GenerationType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGGraph;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationBounds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_bAsynchronous_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAsynchronous;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGGenerationRequest>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_GenerationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_GenerationType = { "GenerationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationRequest, GenerationType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationType_MetaData), NewProp_GenerationType_MetaData) }; // 1055494586
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_PCGGraph = { "PCGGraph", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationRequest, PCGGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraph_MetaData), NewProp_PCGGraph_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationRequest, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActor_MetaData), NewProp_TargetActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_GenerationBounds = { "GenerationBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationRequest, GenerationBounds), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationBounds_MetaData), NewProp_GenerationBounds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationRequest, Configuration), Z_Construct_UScriptStruct_FAuracronPCGConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1384230652
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationRequest, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_bAsynchronous_SetBit(void* Obj)
{
	((FAuracronPCGGenerationRequest*)Obj)->bAsynchronous = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_bAsynchronous = { "bAsynchronous", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGGenerationRequest), &Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_bAsynchronous_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAsynchronous_MetaData), NewProp_bAsynchronous_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationRequest, Seed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Seed_MetaData), NewProp_Seed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_GenerationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_GenerationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_PCGGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_GenerationBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_bAsynchronous,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewProp_Seed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGGenerationRequest",
	Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::PropPointers),
	sizeof(FAuracronPCGGenerationRequest),
	alignof(FAuracronPCGGenerationRequest),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGGenerationRequest ***************************************

// ********** Begin ScriptStruct FAuracronPCGGenerationResult **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult;
class UScriptStruct* FAuracronPCGGenerationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGGenerationResult, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGGenerationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG generation result structure\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG generation result structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Errors_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedActors_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsGenerated_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshesGenerated_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationId_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Errors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Errors;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedActors;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsGenerated;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshesGenerated;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGGenerationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronPCGGenerationResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGGenerationResult), &Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationResult, PerformanceMetrics), Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) }; // 68781111
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_Errors_Inner = { "Errors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGErrorInfo, METADATA_PARAMS(0, nullptr) }; // 1923069771
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_Errors = { "Errors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationResult, Errors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Errors_MetaData), NewProp_Errors_MetaData) }; // 1923069771
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_GeneratedActors_Inner = { "GeneratedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_GeneratedActors = { "GeneratedActors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationResult, GeneratedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedActors_MetaData), NewProp_GeneratedActors_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_PointsGenerated = { "PointsGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationResult, PointsGenerated), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsGenerated_MetaData), NewProp_PointsGenerated_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_MeshesGenerated = { "MeshesGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationResult, MeshesGenerated), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshesGenerated_MetaData), NewProp_MeshesGenerated_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_GenerationId = { "GenerationId", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationResult, GenerationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationId_MetaData), NewProp_GenerationId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_CompletionTime = { "CompletionTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGenerationResult, CompletionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionTime_MetaData), NewProp_CompletionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_PerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_Errors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_Errors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_GeneratedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_GeneratedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_PointsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_MeshesGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_GenerationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewProp_CompletionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGGenerationResult",
	Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::PropPointers),
	sizeof(FAuracronPCGGenerationResult),
	alignof(FAuracronPCGGenerationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGenerationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGGenerationResult ****************************************

// ********** Begin Class UAuracronPCGManager Function ApplyConfiguration **************************
struct Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics
{
	struct AuracronPCGManager_eventApplyConfiguration_Parms
	{
		FAuracronPCGConfiguration NewConfiguration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfiguration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfiguration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::NewProp_NewConfiguration = { "NewConfiguration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventApplyConfiguration_Parms, NewConfiguration), Z_Construct_UScriptStruct_FAuracronPCGConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfiguration_MetaData), NewProp_NewConfiguration_MetaData) }; // 1384230652
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::NewProp_NewConfiguration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "ApplyConfiguration", Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::AuracronPCGManager_eventApplyConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::AuracronPCGManager_eventApplyConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execApplyConfiguration)
{
	P_GET_STRUCT_REF(FAuracronPCGConfiguration,Z_Param_Out_NewConfiguration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyConfiguration(Z_Param_Out_NewConfiguration);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function ApplyConfiguration ****************************

// ********** Begin Class UAuracronPCGManager Function CleanupGeneratedContent *********************
struct Z_Construct_UFunction_UAuracronPCGManager_CleanupGeneratedContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_CleanupGeneratedContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "CleanupGeneratedContent", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_CleanupGeneratedContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_CleanupGeneratedContent_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGManager_CleanupGeneratedContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_CleanupGeneratedContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execCleanupGeneratedContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupGeneratedContent();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function CleanupGeneratedContent ***********************

// ********** Begin Class UAuracronPCGManager Function ClearErrorHistory ***************************
struct Z_Construct_UFunction_UAuracronPCGManager_ClearErrorHistory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Error Handling" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_ClearErrorHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "ClearErrorHistory", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ClearErrorHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_ClearErrorHistory_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGManager_ClearErrorHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_ClearErrorHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execClearErrorHistory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearErrorHistory();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function ClearErrorHistory *****************************

// ********** Begin Class UAuracronPCGManager Function CreatePCGGraph ******************************
struct Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics
{
	struct AuracronPCGManager_eventCreatePCGGraph_Parms
	{
		FString GraphName;
		UPCGGraph* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Graph" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::NewProp_GraphName = { "GraphName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventCreatePCGGraph_Parms, GraphName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphName_MetaData), NewProp_GraphName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventCreatePCGGraph_Parms, ReturnValue), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::NewProp_GraphName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "CreatePCGGraph", Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::AuracronPCGManager_eventCreatePCGGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::AuracronPCGManager_eventCreatePCGGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execCreatePCGGraph)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GraphName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGGraph**)Z_Param__Result=P_THIS->CreatePCGGraph(Z_Param_GraphName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function CreatePCGGraph ********************************

// ********** Begin Class UAuracronPCGManager Function ExecutePCGGraph *****************************
struct Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics
{
	struct AuracronPCGManager_eventExecutePCGGraph_Parms
	{
		UPCGGraph* Graph;
		AActor* TargetActor;
		FAuracronPCGConfiguration Config;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Graph" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Graph;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_Graph = { "Graph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventExecutePCGGraph_Parms, Graph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventExecutePCGGraph_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventExecutePCGGraph_Parms, Config), Z_Construct_UScriptStruct_FAuracronPCGConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1384230652
void Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGManager_eventExecutePCGGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGManager_eventExecutePCGGraph_Parms), &Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_Graph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "ExecutePCGGraph", Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::AuracronPCGManager_eventExecutePCGGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::AuracronPCGManager_eventExecutePCGGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execExecutePCGGraph)
{
	P_GET_OBJECT(UPCGGraph,Z_Param_Graph);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FAuracronPCGConfiguration,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecutePCGGraph(Z_Param_Graph,Z_Param_TargetActor,Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function ExecutePCGGraph *******************************

// ********** Begin Class UAuracronPCGManager Function GetAvailableThreadCount *********************
struct Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics
{
	struct AuracronPCGManager_eventGetAvailableThreadCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "System Info" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetAvailableThreadCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetAvailableThreadCount", Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::AuracronPCGManager_eventGetAvailableThreadCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::AuracronPCGManager_eventGetAvailableThreadCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetAvailableThreadCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetAvailableThreadCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetAvailableThreadCount ***********************

// ********** Begin Class UAuracronPCGManager Function GetConfiguration ****************************
struct Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics
{
	struct AuracronPCGManager_eventGetConfiguration_Parms
	{
		FAuracronPCGConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000008000582, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) }; // 1384230652
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::AuracronPCGManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::AuracronPCGManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetConfiguration ******************************

// ********** Begin Class UAuracronPCGManager Function GetCurrentPerformanceMetrics ****************
struct Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics
{
	struct AuracronPCGManager_eventGetCurrentPerformanceMetrics_Parms
	{
		FAuracronPCGPerformanceMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetCurrentPerformanceMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 68781111
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetCurrentPerformanceMetrics", Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::AuracronPCGManager_eventGetCurrentPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::AuracronPCGManager_eventGetCurrentPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetCurrentPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPerformanceMetrics*)Z_Param__Result=P_THIS->GetCurrentPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetCurrentPerformanceMetrics ******************

// ********** Begin Class UAuracronPCGManager Function GetErrorHistory *****************************
struct Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics
{
	struct AuracronPCGManager_eventGetErrorHistory_Parms
	{
		int32 MaxEntries;
		TArray<FAuracronPCGErrorInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Error Handling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Error handling\n" },
#endif
		{ "CPP_Default_MaxEntries", "10" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Error handling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxEntries;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::NewProp_MaxEntries = { "MaxEntries", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetErrorHistory_Parms, MaxEntries), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGErrorInfo, METADATA_PARAMS(0, nullptr) }; // 1923069771
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetErrorHistory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1923069771
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::NewProp_MaxEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetErrorHistory", Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::AuracronPCGManager_eventGetErrorHistory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::AuracronPCGManager_eventGetErrorHistory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetErrorHistory)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxEntries);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGErrorInfo>*)Z_Param__Result=P_THIS->GetErrorHistory(Z_Param_MaxEntries);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetErrorHistory *******************************

// ********** Begin Class UAuracronPCGManager Function GetGenerationProgress ***********************
struct Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics
{
	struct AuracronPCGManager_eventGetGenerationProgress_Parms
	{
		FString GenerationId;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::NewProp_GenerationId = { "GenerationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetGenerationProgress_Parms, GenerationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationId_MetaData), NewProp_GenerationId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetGenerationProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::NewProp_GenerationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetGenerationProgress", Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::AuracronPCGManager_eventGetGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::AuracronPCGManager_eventGetGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetGenerationProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GenerationId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetGenerationProgress(Z_Param_GenerationId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetGenerationProgress *************************

// ********** Begin Class UAuracronPCGManager Function GetGenerationResult *************************
struct Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics
{
	struct AuracronPCGManager_eventGetGenerationResult_Parms
	{
		FString GenerationId;
		FAuracronPCGGenerationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::NewProp_GenerationId = { "GenerationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetGenerationResult_Parms, GenerationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationId_MetaData), NewProp_GenerationId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetGenerationResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGGenerationResult, METADATA_PARAMS(0, nullptr) }; // 4280132508
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::NewProp_GenerationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetGenerationResult", Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::AuracronPCGManager_eventGetGenerationResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::AuracronPCGManager_eventGetGenerationResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetGenerationResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GenerationId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGGenerationResult*)Z_Param__Result=P_THIS->GetGenerationResult(Z_Param_GenerationId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetGenerationResult ***************************

// ********** Begin Class UAuracronPCGManager Function GetPerformanceRecommendations ***************
struct Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics
{
	struct AuracronPCGManager_eventGetPerformanceRecommendations_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetPerformanceRecommendations_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetPerformanceRecommendations", Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::AuracronPCGManager_eventGetPerformanceRecommendations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::AuracronPCGManager_eventGetPerformanceRecommendations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetPerformanceRecommendations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPerformanceRecommendations();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetPerformanceRecommendations *****************

// ********** Begin Class UAuracronPCGManager Function GetRegisteredElements ***********************
struct Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics
{
	struct AuracronPCGManager_eventGetRegisteredElements_Parms
	{
		TArray<TSubclassOf<UAuracronPCGSettingsBase>> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Elements" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetRegisteredElements_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetRegisteredElements", Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::AuracronPCGManager_eventGetRegisteredElements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::AuracronPCGManager_eventGetRegisteredElements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetRegisteredElements)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<TSubclassOf<UAuracronPCGSettingsBase>>*)Z_Param__Result=P_THIS->GetRegisteredElements();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetRegisteredElements *************************

// ********** Begin Class UAuracronPCGManager Function GetSystemHealthScore ************************
struct Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics
{
	struct AuracronPCGManager_eventGetSystemHealthScore_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "System Info" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetSystemHealthScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetSystemHealthScore", Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::AuracronPCGManager_eventGetSystemHealthScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::AuracronPCGManager_eventGetSystemHealthScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetSystemHealthScore)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetSystemHealthScore();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetSystemHealthScore **************************

// ********** Begin Class UAuracronPCGManager Function GetVersionString ****************************
struct Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics
{
	struct AuracronPCGManager_eventGetVersionString_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "System Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// System information\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "System information" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventGetVersionString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "GetVersionString", Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::AuracronPCGManager_eventGetVersionString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::AuracronPCGManager_eventGetVersionString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_GetVersionString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_GetVersionString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execGetVersionString)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetVersionString();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function GetVersionString ******************************

// ********** Begin Class UAuracronPCGManager Function Initialize **********************************
struct Z_Construct_UFunction_UAuracronPCGManager_Initialize_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Initialization and shutdown\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialization and shutdown" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "Initialize", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_Initialize_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execInitialize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function Initialize ************************************

// ********** Begin Class UAuracronPCGManager Function IsGenerationActive **************************
struct Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics
{
	struct AuracronPCGManager_eventIsGenerationActive_Parms
	{
		FString GenerationId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::NewProp_GenerationId = { "GenerationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventIsGenerationActive_Parms, GenerationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationId_MetaData), NewProp_GenerationId_MetaData) };
void Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGManager_eventIsGenerationActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGManager_eventIsGenerationActive_Parms), &Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::NewProp_GenerationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "IsGenerationActive", Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::AuracronPCGManager_eventIsGenerationActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::AuracronPCGManager_eventIsGenerationActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execIsGenerationActive)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GenerationId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGenerationActive(Z_Param_GenerationId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function IsGenerationActive ****************************

// ********** Begin Class UAuracronPCGManager Function IsGPUAccelerationAvailable ******************
struct Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics
{
	struct AuracronPCGManager_eventIsGPUAccelerationAvailable_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "System Info" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGManager_eventIsGPUAccelerationAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGManager_eventIsGPUAccelerationAvailable_Parms), &Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "IsGPUAccelerationAvailable", Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::AuracronPCGManager_eventIsGPUAccelerationAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::AuracronPCGManager_eventIsGPUAccelerationAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execIsGPUAccelerationAvailable)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGPUAccelerationAvailable();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function IsGPUAccelerationAvailable ********************

// ********** Begin Class UAuracronPCGManager Function IsInitialized *******************************
struct Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics
{
	struct AuracronPCGManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::AuracronPCGManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::AuracronPCGManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function IsInitialized *********************************

// ********** Begin Class UAuracronPCGManager Function OptimizeMemoryUsage *************************
struct Z_Construct_UFunction_UAuracronPCGManager_OptimizeMemoryUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utilities" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_OptimizeMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "OptimizeMemoryUsage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGManager_OptimizeMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_OptimizeMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execOptimizeMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function OptimizeMemoryUsage ***************************

// ********** Begin Class UAuracronPCGManager Function RegisterCustomElement ***********************
struct Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics
{
	struct AuracronPCGManager_eventRegisterCustomElement_Parms
	{
		TSubclassOf<UAuracronPCGSettingsBase> ElementClass;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Elements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ElementClass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::NewProp_ElementClass = { "ElementClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventRegisterCustomElement_Parms, ElementClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::NewProp_ElementClass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "RegisterCustomElement", Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::AuracronPCGManager_eventRegisterCustomElement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::AuracronPCGManager_eventRegisterCustomElement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execRegisterCustomElement)
{
	P_GET_OBJECT(UClass,Z_Param_ElementClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterCustomElement(Z_Param_ElementClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function RegisterCustomElement *************************

// ********** Begin Class UAuracronPCGManager Function ReportError *********************************
struct Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics
{
	struct AuracronPCGManager_eventReportError_Parms
	{
		FAuracronPCGErrorInfo ErrorInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Error Handling" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ErrorInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::NewProp_ErrorInfo = { "ErrorInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventReportError_Parms, ErrorInfo), Z_Construct_UScriptStruct_FAuracronPCGErrorInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorInfo_MetaData), NewProp_ErrorInfo_MetaData) }; // 1923069771
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::NewProp_ErrorInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "ReportError", Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::AuracronPCGManager_eventReportError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::AuracronPCGManager_eventReportError_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_ReportError()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_ReportError_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execReportError)
{
	P_GET_STRUCT_REF(FAuracronPCGErrorInfo,Z_Param_Out_ErrorInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReportError(Z_Param_Out_ErrorInfo);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function ReportError ***********************************

// ********** Begin Class UAuracronPCGManager Function ResetPerformanceMetrics *********************
struct Z_Construct_UFunction_UAuracronPCGManager_ResetPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_ResetPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "ResetPerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ResetPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_ResetPerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGManager_ResetPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_ResetPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execResetPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function ResetPerformanceMetrics ***********************

// ********** Begin Class UAuracronPCGManager Function Shutdown ************************************
struct Z_Construct_UFunction_UAuracronPCGManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function Shutdown **************************************

// ********** Begin Class UAuracronPCGManager Function StartGeneration *****************************
struct Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics
{
	struct AuracronPCGManager_eventStartGeneration_Parms
	{
		FAuracronPCGGenerationRequest Request;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Request_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Request;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::NewProp_Request = { "Request", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventStartGeneration_Parms, Request), Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Request_MetaData), NewProp_Request_MetaData) }; // 132920414
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventStartGeneration_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::NewProp_Request,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "StartGeneration", Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::AuracronPCGManager_eventStartGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::AuracronPCGManager_eventStartGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_StartGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_StartGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execStartGeneration)
{
	P_GET_STRUCT_REF(FAuracronPCGGenerationRequest,Z_Param_Out_Request);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->StartGeneration(Z_Param_Out_Request);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function StartGeneration *******************************

// ********** Begin Class UAuracronPCGManager Function StopGeneration ******************************
struct Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics
{
	struct AuracronPCGManager_eventStopGeneration_Parms
	{
		FString GenerationId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::NewProp_GenerationId = { "GenerationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventStopGeneration_Parms, GenerationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationId_MetaData), NewProp_GenerationId_MetaData) };
void Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGManager_eventStopGeneration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGManager_eventStopGeneration_Parms), &Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::NewProp_GenerationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "StopGeneration", Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::AuracronPCGManager_eventStopGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::AuracronPCGManager_eventStopGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_StopGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_StopGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execStopGeneration)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GenerationId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopGeneration(Z_Param_GenerationId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function StopGeneration ********************************

// ********** Begin Class UAuracronPCGManager Function UnregisterCustomElement *********************
struct Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics
{
	struct AuracronPCGManager_eventUnregisterCustomElement_Parms
	{
		TSubclassOf<UAuracronPCGSettingsBase> ElementClass;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Elements" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ElementClass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::NewProp_ElementClass = { "ElementClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventUnregisterCustomElement_Parms, ElementClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::NewProp_ElementClass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "UnregisterCustomElement", Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::AuracronPCGManager_eventUnregisterCustomElement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::AuracronPCGManager_eventUnregisterCustomElement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execUnregisterCustomElement)
{
	P_GET_OBJECT(UClass,Z_Param_ElementClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterCustomElement(Z_Param_ElementClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function UnregisterCustomElement ***********************

// ********** Begin Class UAuracronPCGManager Function ValidatePCGGraph ****************************
struct Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics
{
	struct AuracronPCGManager_eventValidatePCGGraph_Parms
	{
		UPCGGraph* Graph;
		TArray<FString> ValidationErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Graph" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Graph;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_Graph = { "Graph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventValidatePCGGraph_Parms, Graph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ValidationErrors_Inner = { "ValidationErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ValidationErrors = { "ValidationErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventValidatePCGGraph_Parms, ValidationErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGManager_eventValidatePCGGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGManager_eventValidatePCGGraph_Parms), &Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_Graph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ValidationErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "ValidatePCGGraph", Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::AuracronPCGManager_eventValidatePCGGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::AuracronPCGManager_eventValidatePCGGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execValidatePCGGraph)
{
	P_GET_OBJECT(UPCGGraph,Z_Param_Graph);
	P_GET_TARRAY_REF(FString,Z_Param_Out_ValidationErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePCGGraph(Z_Param_Graph,Z_Param_Out_ValidationErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function ValidatePCGGraph ******************************

// ********** Begin Class UAuracronPCGManager Function ValidateSystemRequirements ******************
struct Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics
{
	struct AuracronPCGManager_eventValidateSystemRequirements_Parms
	{
		TArray<FString> ValidationMessages;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utilities" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationMessages;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ValidationMessages_Inner = { "ValidationMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ValidationMessages = { "ValidationMessages", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGManager_eventValidateSystemRequirements_Parms, ValidationMessages), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGManager_eventValidateSystemRequirements_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGManager_eventValidateSystemRequirements_Parms), &Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ValidationMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ValidationMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGManager, nullptr, "ValidateSystemRequirements", Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::AuracronPCGManager_eventValidateSystemRequirements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::AuracronPCGManager_eventValidateSystemRequirements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGManager::execValidateSystemRequirements)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_ValidationMessages);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSystemRequirements(Z_Param_Out_ValidationMessages);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGManager Function ValidateSystemRequirements ********************

// ********** Begin Class UAuracronPCGManager ******************************************************
void UAuracronPCGManager::StaticRegisterNativesUAuracronPCGManager()
{
	UClass* Class = UAuracronPCGManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyConfiguration", &UAuracronPCGManager::execApplyConfiguration },
		{ "CleanupGeneratedContent", &UAuracronPCGManager::execCleanupGeneratedContent },
		{ "ClearErrorHistory", &UAuracronPCGManager::execClearErrorHistory },
		{ "CreatePCGGraph", &UAuracronPCGManager::execCreatePCGGraph },
		{ "ExecutePCGGraph", &UAuracronPCGManager::execExecutePCGGraph },
		{ "GetAvailableThreadCount", &UAuracronPCGManager::execGetAvailableThreadCount },
		{ "GetConfiguration", &UAuracronPCGManager::execGetConfiguration },
		{ "GetCurrentPerformanceMetrics", &UAuracronPCGManager::execGetCurrentPerformanceMetrics },
		{ "GetErrorHistory", &UAuracronPCGManager::execGetErrorHistory },
		{ "GetGenerationProgress", &UAuracronPCGManager::execGetGenerationProgress },
		{ "GetGenerationResult", &UAuracronPCGManager::execGetGenerationResult },
		{ "GetPerformanceRecommendations", &UAuracronPCGManager::execGetPerformanceRecommendations },
		{ "GetRegisteredElements", &UAuracronPCGManager::execGetRegisteredElements },
		{ "GetSystemHealthScore", &UAuracronPCGManager::execGetSystemHealthScore },
		{ "GetVersionString", &UAuracronPCGManager::execGetVersionString },
		{ "Initialize", &UAuracronPCGManager::execInitialize },
		{ "IsGenerationActive", &UAuracronPCGManager::execIsGenerationActive },
		{ "IsGPUAccelerationAvailable", &UAuracronPCGManager::execIsGPUAccelerationAvailable },
		{ "IsInitialized", &UAuracronPCGManager::execIsInitialized },
		{ "OptimizeMemoryUsage", &UAuracronPCGManager::execOptimizeMemoryUsage },
		{ "RegisterCustomElement", &UAuracronPCGManager::execRegisterCustomElement },
		{ "ReportError", &UAuracronPCGManager::execReportError },
		{ "ResetPerformanceMetrics", &UAuracronPCGManager::execResetPerformanceMetrics },
		{ "Shutdown", &UAuracronPCGManager::execShutdown },
		{ "StartGeneration", &UAuracronPCGManager::execStartGeneration },
		{ "StopGeneration", &UAuracronPCGManager::execStopGeneration },
		{ "UnregisterCustomElement", &UAuracronPCGManager::execUnregisterCustomElement },
		{ "ValidatePCGGraph", &UAuracronPCGManager::execValidatePCGGraph },
		{ "ValidateSystemRequirements", &UAuracronPCGManager::execValidateSystemRequirements },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGManager;
UClass* UAuracronPCGManager::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGManager;
	if (!Z_Registration_Info_UClass_UAuracronPCGManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGManager"),
			Z_Registration_Info_UClass_UAuracronPCGManager.InnerSingleton,
			StaticRegisterNativesUAuracronPCGManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGManager_NoRegister()
{
	return UAuracronPCGManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Main manager class for AURACRON PCG Framework\n * Handles PCG graph execution, element management, and performance monitoring\n */" },
#endif
		{ "IncludePath", "AuracronPCGManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main manager class for AURACRON PCG Framework\nHandles PCG graph execution, element management, and performance monitoring" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGenerationComplete_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGenerationProgress_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnError_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveGenerations_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Generation management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedGenerations_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredElements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element registry\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element registry" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorHistory_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Error tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Error tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG subsystem reference\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG subsystem reference" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGenerationComplete;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGenerationProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnError;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveGenerations_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveGenerations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveGenerations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletedGenerations_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletedGenerations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CompletedGenerations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationProgress_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationProgress_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_GenerationProgress;
	static const UECodeGen_Private::FClassPropertyParams NewProp_RegisteredElements_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RegisteredElements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ErrorHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrorHistory;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGSubsystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGManager_ApplyConfiguration, "ApplyConfiguration" }, // 1078147244
		{ &Z_Construct_UFunction_UAuracronPCGManager_CleanupGeneratedContent, "CleanupGeneratedContent" }, // 2112210727
		{ &Z_Construct_UFunction_UAuracronPCGManager_ClearErrorHistory, "ClearErrorHistory" }, // 2279758541
		{ &Z_Construct_UFunction_UAuracronPCGManager_CreatePCGGraph, "CreatePCGGraph" }, // 3762809849
		{ &Z_Construct_UFunction_UAuracronPCGManager_ExecutePCGGraph, "ExecutePCGGraph" }, // 1527122299
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetAvailableThreadCount, "GetAvailableThreadCount" }, // 2379047348
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetConfiguration, "GetConfiguration" }, // 693841635
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetCurrentPerformanceMetrics, "GetCurrentPerformanceMetrics" }, // 2874692379
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetErrorHistory, "GetErrorHistory" }, // 896858175
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetGenerationProgress, "GetGenerationProgress" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetGenerationResult, "GetGenerationResult" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetPerformanceRecommendations, "GetPerformanceRecommendations" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetRegisteredElements, "GetRegisteredElements" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetSystemHealthScore, "GetSystemHealthScore" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_GetVersionString, "GetVersionString" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_Initialize, "Initialize" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_IsGenerationActive, "IsGenerationActive" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_IsGPUAccelerationAvailable, "IsGPUAccelerationAvailable" }, // 821237536
		{ &Z_Construct_UFunction_UAuracronPCGManager_IsInitialized, "IsInitialized" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_OptimizeMemoryUsage, "OptimizeMemoryUsage" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_RegisterCustomElement, "RegisterCustomElement" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_ReportError, "ReportError" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_ResetPerformanceMetrics, "ResetPerformanceMetrics" }, // **********
		{ &Z_Construct_UFunction_UAuracronPCGManager_Shutdown, "Shutdown" }, // 2645981039
		{ &Z_Construct_UFunction_UAuracronPCGManager_StartGeneration, "StartGeneration" }, // 3341630835
		{ &Z_Construct_UFunction_UAuracronPCGManager_StopGeneration, "StopGeneration" }, // 476637157
		{ &Z_Construct_UFunction_UAuracronPCGManager_UnregisterCustomElement, "UnregisterCustomElement" }, // 2658327171
		{ &Z_Construct_UFunction_UAuracronPCGManager_ValidatePCGGraph, "ValidatePCGGraph" }, // 3729035690
		{ &Z_Construct_UFunction_UAuracronPCGManager_ValidateSystemRequirements, "ValidateSystemRequirements" }, // 2700935779
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_OnGenerationComplete = { "OnGenerationComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, OnGenerationComplete), Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGenerationComplete_MetaData), NewProp_OnGenerationComplete_MetaData) }; // 2372701618
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_OnGenerationProgress = { "OnGenerationProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, OnGenerationProgress), Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGenerationProgress_MetaData), NewProp_OnGenerationProgress_MetaData) }; // 4131334778
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_OnError = { "OnError", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, OnError), Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnError_MetaData), NewProp_OnError_MetaData) }; // 1412816503
void Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronPCGManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGManager), &Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, Configuration), Z_Construct_UScriptStruct_FAuracronPCGConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1384230652
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, PerformanceMetrics), Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) }; // 68781111
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ActiveGenerations_ValueProp = { "ActiveGenerations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest, METADATA_PARAMS(0, nullptr) }; // 132920414
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ActiveGenerations_Key_KeyProp = { "ActiveGenerations_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ActiveGenerations = { "ActiveGenerations", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, ActiveGenerations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveGenerations_MetaData), NewProp_ActiveGenerations_MetaData) }; // 132920414
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_CompletedGenerations_ValueProp = { "CompletedGenerations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronPCGGenerationResult, METADATA_PARAMS(0, nullptr) }; // 4280132508
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_CompletedGenerations_Key_KeyProp = { "CompletedGenerations_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_CompletedGenerations = { "CompletedGenerations", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, CompletedGenerations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedGenerations_MetaData), NewProp_CompletedGenerations_MetaData) }; // 4280132508
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_GenerationProgress_ValueProp = { "GenerationProgress", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_GenerationProgress_Key_KeyProp = { "GenerationProgress_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_GenerationProgress = { "GenerationProgress", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, GenerationProgress), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationProgress_MetaData), NewProp_GenerationProgress_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_RegisteredElements_Inner = { "RegisteredElements", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_RegisteredElements = { "RegisteredElements", nullptr, (EPropertyFlags)0x0024080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, RegisteredElements), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredElements_MetaData), NewProp_RegisteredElements_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ErrorHistory_Inner = { "ErrorHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGErrorInfo, METADATA_PARAMS(0, nullptr) }; // 1923069771
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ErrorHistory = { "ErrorHistory", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, ErrorHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorHistory_MetaData), NewProp_ErrorHistory_MetaData) }; // 1923069771
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_PCGSubsystem = { "PCGSubsystem", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGManager, PCGSubsystem), Z_Construct_UClass_UPCGSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGSubsystem_MetaData), NewProp_PCGSubsystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_OnGenerationComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_OnGenerationProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_OnError,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_PerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ActiveGenerations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ActiveGenerations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ActiveGenerations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_CompletedGenerations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_CompletedGenerations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_CompletedGenerations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_GenerationProgress_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_GenerationProgress_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_GenerationProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_RegisteredElements_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_RegisteredElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ErrorHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_ErrorHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGManager_Statics::NewProp_PCGSubsystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGManager_Statics::ClassParams = {
	&UAuracronPCGManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGManager()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGManager.OuterSingleton, Z_Construct_UClass_UAuracronPCGManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGManager);
UAuracronPCGManager::~UAuracronPCGManager() {}
// ********** End Class UAuracronPCGManager ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGGenerationRequest::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics::NewStructOps, TEXT("AuracronPCGGenerationRequest"), &Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationRequest, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGGenerationRequest), 132920414U) },
		{ FAuracronPCGGenerationResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics::NewStructOps, TEXT("AuracronPCGGenerationResult"), &Z_Registration_Info_UScriptStruct_FAuracronPCGGenerationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGGenerationResult), 4280132508U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGManager, UAuracronPCGManager::StaticClass, TEXT("UAuracronPCGManager"), &Z_Registration_Info_UClass_UAuracronPCGManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGManager), 3146709272U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h__Script_AuracronPCGBridge_632335986(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
