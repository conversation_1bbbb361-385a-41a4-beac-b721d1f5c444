// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema Anti-Cheat Bridge Build Configuration
using UnrealBuildTool;
public class AuracronAntiCheatBridge : ModuleRules
{
    public AuracronAntiCheatBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "NetCore",
                "ReplicationGraph","OnlineSubsystemUtils","EOSShared",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "Sockets",
                "Networking",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "UnrealEd",
                "PropertyEditor",
                "KismetCompiler","BlueprintGraph",
                "Kismet",
                "ToolMenus",
                "ApplicationCore",
                "RenderCore",
                "RHI","Json","AudioMixer",
                "SignalProcessing","PlatformCrypto",
                "RSA",
                "SessionServices","DesktopPlatform",
                "LauncherServices",
                "GameplayDebugger",
                "NetcodeUnitTest",
                "AutomationController",
                "AutomationWorker",
                "AutomationMessages",
                "FunctionalTesting",
                "ScreenShotComparisonTools",
                "TraceInsights",
                "TraceLog",
                "TraceAnalysis",
                "TraceServices",
                "ApplicationCore",
                "Projects",
                "DesktopPlatform","InputDevice",
                "RawInput"
            }
        );
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_ANTI_CHEAT=1");
        PublicDefinitions.Add("WITH_SERVER_VALIDATION=1");
        PublicDefinitions.Add("WITH_CLIENT_PREDICTION=1");
        PublicDefinitions.Add("WITH_NETWORK_SECURITY=1");
        PublicDefinitions.Add("WITH_ENCRYPTION=1");
        PublicDefinitions.Add("WITH_INTEGRITY_CHECKS=1");
        PublicDefinitions.Add("WITH_BEHAVIOR_ANALYSIS=1");
        PublicDefinitions.Add("WITH_STATISTICAL_ANALYSIS=1");
        PublicDefinitions.Add("WITH_MACHINE_LEARNING=1");
        // Anti-cheat features
        PublicDefinitions.Add("AURACRON_MOVEMENT_VALIDATION=1");
        PublicDefinitions.Add("AURACRON_ABILITY_VALIDATION=1");
        PublicDefinitions.Add("AURACRON_DAMAGE_VALIDATION=1");
        PublicDefinitions.Add("AURACRON_POSITION_VALIDATION=1");
        PublicDefinitions.Add("AURACRON_TIMING_VALIDATION=1");
        PublicDefinitions.Add("AURACRON_INPUT_VALIDATION=1");
        PublicDefinitions.Add("AURACRON_MEMORY_PROTECTION=1");
        PublicDefinitions.Add("AURACRON_PROCESS_MONITORING=1");
        PublicDefinitions.Add("AURACRON_NETWORK_MONITORING=1");
        // Security levels
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_SECURITY_LEVEL=3"); // Maximum security
            PublicDefinitions.Add("AURACRON_OBFUSCATION=1");
            PublicDefinitions.Add("AURACRON_TAMPER_DETECTION=1");
        }
        else if (Target.Configuration == UnrealTargetConfiguration.Development)
        {
            PublicDefinitions.Add("AURACRON_SECURITY_LEVEL=2"); // Medium security
            PublicDefinitions.Add("AURACRON_OBFUSCATION=0");
            PublicDefinitions.Add("AURACRON_TAMPER_DETECTION=0");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_SECURITY_LEVEL=1"); // Low security
            PublicDefinitions.Add("AURACRON_OBFUSCATION=0");
            PublicDefinitions.Add("AURACRON_TAMPER_DETECTION=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_ANTICHEAT=1");
            PublicDefinitions.Add("AURACRON_DEVICE_VALIDATION=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_ANTICHEAT=0");
            PublicDefinitions.Add("AURACRON_DEVICE_VALIDATION=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_ANTICHEAT_DEBUG=1");
            PublicDefinitions.Add("AURACRON_SECURITY_LOGGING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_ANTICHEAT_DEBUG=0");
            PublicDefinitions.Add("AURACRON_SECURITY_LOGGING=0");
        }
    }
}
