// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronQABridge_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronQABridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronQABridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronQABridge.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronQABridge",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x05AB2269,
				0x707DB156,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronQABridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronQABridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronQABridge(Z_Construct_UPackage__Script_AuracronQABridge, TEXT("/Script/AuracronQABridge"), Z_Registration_Info_UPackage__Script_AuracronQABridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x05AB2269, 0x707DB156));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
