// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronMonetizationBridge_init() {}
	AURACRONMONETIZATIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature();
	AURACRONMONETIZATIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronMonetizationBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronMonetizationBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronMonetizationBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronMonetizationBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x92BB0ED6,
				0x12442EC2,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronMonetizationBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronMonetizationBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronMonetizationBridge(Z_Construct_UPackage__Script_AuracronMonetizationBridge, TEXT("/Script/AuracronMonetizationBridge"), Z_Registration_Info_UPackage__Script_AuracronMonetizationBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x92BB0ED6, 0x12442EC2));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
