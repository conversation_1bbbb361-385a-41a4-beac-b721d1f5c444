// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionGrid.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionGrid() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionGridManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionGridManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridCell();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridStatistics();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpatialQueryParams();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronGridSubdivisionType **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGridSubdivisionType;
static UEnum* EAuracronGridSubdivisionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridSubdivisionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGridSubdivisionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronGridSubdivisionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronGridSubdivisionType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridSubdivisionType>()
{
	return EAuracronGridSubdivisionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronGridSubdivisionType::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid subdivision types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronGridSubdivisionType::Custom" },
		{ "Hierarchical.DisplayName", "Hierarchical" },
		{ "Hierarchical.Name", "EAuracronGridSubdivisionType::Hierarchical" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
		{ "Octree.DisplayName", "Octree" },
		{ "Octree.Name", "EAuracronGridSubdivisionType::Octree" },
		{ "QuadTree.DisplayName", "QuadTree" },
		{ "QuadTree.Name", "EAuracronGridSubdivisionType::QuadTree" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid subdivision types" },
#endif
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "EAuracronGridSubdivisionType::Uniform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGridSubdivisionType::Uniform", (int64)EAuracronGridSubdivisionType::Uniform },
		{ "EAuracronGridSubdivisionType::Adaptive", (int64)EAuracronGridSubdivisionType::Adaptive },
		{ "EAuracronGridSubdivisionType::Hierarchical", (int64)EAuracronGridSubdivisionType::Hierarchical },
		{ "EAuracronGridSubdivisionType::QuadTree", (int64)EAuracronGridSubdivisionType::QuadTree },
		{ "EAuracronGridSubdivisionType::Octree", (int64)EAuracronGridSubdivisionType::Octree },
		{ "EAuracronGridSubdivisionType::Custom", (int64)EAuracronGridSubdivisionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronGridSubdivisionType",
	"EAuracronGridSubdivisionType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridSubdivisionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGridSubdivisionType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGridSubdivisionType.InnerSingleton;
}
// ********** End Enum EAuracronGridSubdivisionType ************************************************

// ********** Begin Enum EAuracronGridCellState ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGridCellState;
static UEnum* EAuracronGridCellState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridCellState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGridCellState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronGridCellState"));
	}
	return Z_Registration_Info_UEnum_EAuracronGridCellState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridCellState>()
{
	return EAuracronGridCellState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid cell states\n" },
#endif
		{ "Empty.DisplayName", "Empty" },
		{ "Empty.Name", "EAuracronGridCellState::Empty" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronGridCellState::Error" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronGridCellState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronGridCellState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
		{ "Populated.DisplayName", "Populated" },
		{ "Populated.Name", "EAuracronGridCellState::Populated" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid cell states" },
#endif
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronGridCellState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGridCellState::Empty", (int64)EAuracronGridCellState::Empty },
		{ "EAuracronGridCellState::Populated", (int64)EAuracronGridCellState::Populated },
		{ "EAuracronGridCellState::Loading", (int64)EAuracronGridCellState::Loading },
		{ "EAuracronGridCellState::Loaded", (int64)EAuracronGridCellState::Loaded },
		{ "EAuracronGridCellState::Unloading", (int64)EAuracronGridCellState::Unloading },
		{ "EAuracronGridCellState::Error", (int64)EAuracronGridCellState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronGridCellState",
	"EAuracronGridCellState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridCellState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGridCellState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGridCellState.InnerSingleton;
}
// ********** End Enum EAuracronGridCellState ******************************************************

// ********** Begin Enum EAuracronGridSpatialQueryType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType;
static UEnum* EAuracronGridSpatialQueryType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronGridSpatialQueryType"));
	}
	return Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridSpatialQueryType>()
{
	return EAuracronGridSpatialQueryType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EAuracronGridSpatialQueryType::Box" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial query types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronGridSpatialQueryType::Custom" },
		{ "Frustum.DisplayName", "Frustum" },
		{ "Frustum.Name", "EAuracronGridSpatialQueryType::Frustum" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
		{ "Point.DisplayName", "Point" },
		{ "Point.Name", "EAuracronGridSpatialQueryType::Point" },
		{ "Ray.DisplayName", "Ray" },
		{ "Ray.Name", "EAuracronGridSpatialQueryType::Ray" },
		{ "Sphere.DisplayName", "Sphere" },
		{ "Sphere.Name", "EAuracronGridSpatialQueryType::Sphere" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial query types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGridSpatialQueryType::Point", (int64)EAuracronGridSpatialQueryType::Point },
		{ "EAuracronGridSpatialQueryType::Sphere", (int64)EAuracronGridSpatialQueryType::Sphere },
		{ "EAuracronGridSpatialQueryType::Box", (int64)EAuracronGridSpatialQueryType::Box },
		{ "EAuracronGridSpatialQueryType::Frustum", (int64)EAuracronGridSpatialQueryType::Frustum },
		{ "EAuracronGridSpatialQueryType::Ray", (int64)EAuracronGridSpatialQueryType::Ray },
		{ "EAuracronGridSpatialQueryType::Custom", (int64)EAuracronGridSpatialQueryType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronGridSpatialQueryType",
	"EAuracronGridSpatialQueryType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType.InnerSingleton;
}
// ********** End Enum EAuracronGridSpatialQueryType ***********************************************

// ********** Begin Enum EAuracronGridOptimizationLevel ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel;
static UEnum* EAuracronGridOptimizationLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronGridOptimizationLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridOptimizationLevel>()
{
	return EAuracronGridOptimizationLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAuracronGridOptimizationLevel::Aggressive" },
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EAuracronGridOptimizationLevel::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid optimization levels\n" },
#endif
		{ "Maximum.DisplayName", "Maximum" },
		{ "Maximum.Name", "EAuracronGridOptimizationLevel::Maximum" },
		{ "Moderate.DisplayName", "Moderate" },
		{ "Moderate.Name", "EAuracronGridOptimizationLevel::Moderate" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronGridOptimizationLevel::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid optimization levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGridOptimizationLevel::None", (int64)EAuracronGridOptimizationLevel::None },
		{ "EAuracronGridOptimizationLevel::Basic", (int64)EAuracronGridOptimizationLevel::Basic },
		{ "EAuracronGridOptimizationLevel::Moderate", (int64)EAuracronGridOptimizationLevel::Moderate },
		{ "EAuracronGridOptimizationLevel::Aggressive", (int64)EAuracronGridOptimizationLevel::Aggressive },
		{ "EAuracronGridOptimizationLevel::Maximum", (int64)EAuracronGridOptimizationLevel::Maximum },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronGridOptimizationLevel",
	"EAuracronGridOptimizationLevel",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel.InnerSingleton;
}
// ********** End Enum EAuracronGridOptimizationLevel **********************************************

// ********** Begin ScriptStruct FAuracronGridConfiguration ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration;
class UScriptStruct* FAuracronGridConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGridConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronGridConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Grid Configuration\n * Configuration settings for world partition grid system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid Configuration\nConfiguration settings for world partition grid system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubdivisionType_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellSize_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinCellSize_MetaData[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 256m default\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "256m default" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCellSize_MetaData[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 64m minimum\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "64m minimum" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSubdivisionLevels_MetaData[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 1024m maximum\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "1024m maximum" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldOrigin_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldBounds_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationLevel_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptiveSubdivision_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSpatialIndexing_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActorsPerCell_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncProcessing_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentOperations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellProcessingTimeLimit_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 16ms\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "16ms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogGridOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubdivisionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubdivisionType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CellSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinCellSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCellSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSubdivisionLevels;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldOrigin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldBounds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationLevel;
	static void NewProp_bEnableAdaptiveSubdivision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptiveSubdivision;
	static void NewProp_bEnableSpatialIndexing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSpatialIndexing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActorsPerCell;
	static void NewProp_bEnableAsyncProcessing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncProcessing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CellProcessingTimeLimit;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bLogGridOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogGridOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGridConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_SubdivisionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_SubdivisionType = { "SubdivisionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, SubdivisionType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSubdivisionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubdivisionType_MetaData), NewProp_SubdivisionType_MetaData) }; // 2992809630
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_CellSize = { "CellSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, CellSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellSize_MetaData), NewProp_CellSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MinCellSize = { "MinCellSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, MinCellSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinCellSize_MetaData), NewProp_MinCellSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxCellSize = { "MaxCellSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, MaxCellSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCellSize_MetaData), NewProp_MaxCellSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxSubdivisionLevels = { "MaxSubdivisionLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, MaxSubdivisionLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSubdivisionLevels_MetaData), NewProp_MaxSubdivisionLevels_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_WorldOrigin = { "WorldOrigin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, WorldOrigin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldOrigin_MetaData), NewProp_WorldOrigin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_WorldBounds = { "WorldBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, WorldBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldBounds_MetaData), NewProp_WorldBounds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_OptimizationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_OptimizationLevel = { "OptimizationLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, OptimizationLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridOptimizationLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationLevel_MetaData), NewProp_OptimizationLevel_MetaData) }; // 3483478850
void Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAdaptiveSubdivision_SetBit(void* Obj)
{
	((FAuracronGridConfiguration*)Obj)->bEnableAdaptiveSubdivision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAdaptiveSubdivision = { "bEnableAdaptiveSubdivision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGridConfiguration), &Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAdaptiveSubdivision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptiveSubdivision_MetaData), NewProp_bEnableAdaptiveSubdivision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableSpatialIndexing_SetBit(void* Obj)
{
	((FAuracronGridConfiguration*)Obj)->bEnableSpatialIndexing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableSpatialIndexing = { "bEnableSpatialIndexing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGridConfiguration), &Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableSpatialIndexing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSpatialIndexing_MetaData), NewProp_bEnableSpatialIndexing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxActorsPerCell = { "MaxActorsPerCell", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, MaxActorsPerCell), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActorsPerCell_MetaData), NewProp_MaxActorsPerCell_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAsyncProcessing_SetBit(void* Obj)
{
	((FAuracronGridConfiguration*)Obj)->bEnableAsyncProcessing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAsyncProcessing = { "bEnableAsyncProcessing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGridConfiguration), &Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAsyncProcessing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncProcessing_MetaData), NewProp_bEnableAsyncProcessing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxConcurrentOperations = { "MaxConcurrentOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, MaxConcurrentOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentOperations_MetaData), NewProp_MaxConcurrentOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_CellProcessingTimeLimit = { "CellProcessingTimeLimit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridConfiguration, CellProcessingTimeLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellProcessingTimeLimit_MetaData), NewProp_CellProcessingTimeLimit_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FAuracronGridConfiguration*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGridConfiguration), &Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bLogGridOperations_SetBit(void* Obj)
{
	((FAuracronGridConfiguration*)Obj)->bLogGridOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bLogGridOperations = { "bLogGridOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGridConfiguration), &Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bLogGridOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogGridOperations_MetaData), NewProp_bLogGridOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_SubdivisionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_SubdivisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_CellSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MinCellSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxCellSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxSubdivisionLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_WorldOrigin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_WorldBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_OptimizationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_OptimizationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAdaptiveSubdivision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableSpatialIndexing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxActorsPerCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableAsyncProcessing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_MaxConcurrentOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_CellProcessingTimeLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewProp_bLogGridOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronGridConfiguration",
	Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::PropPointers),
	sizeof(FAuracronGridConfiguration),
	alignof(FAuracronGridConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGridConfiguration ******************************************

// ********** Begin ScriptStruct FAuracronGridCell *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGridCell;
class UScriptStruct* FAuracronGridCell::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridCell.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGridCell.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGridCell, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronGridCell"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridCell.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGridCell_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Grid Cell Data\n * Data structure representing a single grid cell\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid Cell Data\nData structure representing a single grid cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Coordinates_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Bounds_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubdivisionLevel_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorCount_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorIds_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChildCells_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParentCell_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Cell" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Coordinates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Bounds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SubdivisionLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActorCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActorIds;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChildCells_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChildCells;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParentCell;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Properties;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGridCell>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Coordinates = { "Coordinates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, Coordinates), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Coordinates_MetaData), NewProp_Coordinates_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Bounds = { "Bounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, Bounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Bounds_MetaData), NewProp_Bounds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, State), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridCellState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 3996164315
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_SubdivisionLevel = { "SubdivisionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, SubdivisionLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubdivisionLevel_MetaData), NewProp_SubdivisionLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ActorCount = { "ActorCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, ActorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorCount_MetaData), NewProp_ActorCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ActorIds_Inner = { "ActorIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ActorIds = { "ActorIds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, ActorIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorIds_MetaData), NewProp_ActorIds_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ChildCells_Inner = { "ChildCells", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ChildCells = { "ChildCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, ChildCells), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChildCells_MetaData), NewProp_ChildCells_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ParentCell = { "ParentCell", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, ParentCell), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParentCell_MetaData), NewProp_ParentCell_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Properties_ValueProp = { "Properties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Properties_Key_KeyProp = { "Properties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, Properties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridCell, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGridCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Coordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Bounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_SubdivisionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ActorCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ActorIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ActorIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ChildCells_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ChildCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_ParentCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Properties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Properties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGridCell_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronGridCell",
	Z_Construct_UScriptStruct_FAuracronGridCell_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridCell_Statics::PropPointers),
	sizeof(FAuracronGridCell),
	alignof(FAuracronGridCell),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridCell_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGridCell_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridCell()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridCell.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGridCell.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGridCell_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridCell.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGridCell ***************************************************

// ********** Begin ScriptStruct FAuracronSpatialQueryParams ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams;
class UScriptStruct* FAuracronSpatialQueryParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSpatialQueryParams, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronSpatialQueryParams"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spatial Query Parameters\n * Parameters for spatial queries in the grid system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial Query Parameters\nParameters for spatial queries in the grid system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryType_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryLocation_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryRadius_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryBox_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryDirection_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryDistance_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeChildCells_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeEmptyCells_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxResults_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterTags_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QueryType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QueryType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QueryRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryBox;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QueryDistance;
	static void NewProp_bIncludeChildCells_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeChildCells;
	static void NewProp_bIncludeEmptyCells_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeEmptyCells;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilterTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FilterTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSpatialQueryParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryType = { "QueryType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, QueryType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronGridSpatialQueryType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryType_MetaData), NewProp_QueryType_MetaData) }; // 1723139936
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryLocation = { "QueryLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, QueryLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryLocation_MetaData), NewProp_QueryLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryRadius = { "QueryRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, QueryRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryRadius_MetaData), NewProp_QueryRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryBox = { "QueryBox", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, QueryBox), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryBox_MetaData), NewProp_QueryBox_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryDirection = { "QueryDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, QueryDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryDirection_MetaData), NewProp_QueryDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryDistance = { "QueryDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, QueryDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryDistance_MetaData), NewProp_QueryDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeChildCells_SetBit(void* Obj)
{
	((FAuracronSpatialQueryParams*)Obj)->bIncludeChildCells = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeChildCells = { "bIncludeChildCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSpatialQueryParams), &Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeChildCells_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeChildCells_MetaData), NewProp_bIncludeChildCells_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeEmptyCells_SetBit(void* Obj)
{
	((FAuracronSpatialQueryParams*)Obj)->bIncludeEmptyCells = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeEmptyCells = { "bIncludeEmptyCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSpatialQueryParams), &Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeEmptyCells_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeEmptyCells_MetaData), NewProp_bIncludeEmptyCells_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, MaxResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxResults_MetaData), NewProp_MaxResults_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_FilterTags_Inner = { "FilterTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_FilterTags = { "FilterTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParams, FilterTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterTags_MetaData), NewProp_FilterTags_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_QueryDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeChildCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_bIncludeEmptyCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_MaxResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_FilterTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewProp_FilterTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronSpatialQueryParams",
	Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::PropPointers),
	sizeof(FAuracronSpatialQueryParams),
	alignof(FAuracronSpatialQueryParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpatialQueryParams()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSpatialQueryParams *****************************************

// ********** Begin ScriptStruct FAuracronGridSpatialQueryResult ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult;
class UScriptStruct* FAuracronGridSpatialQueryResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronGridSpatialQueryResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spatial Query Result\n * Result of a spatial query operation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial Query Result\nResult of a spatial query operation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoundCells_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoundActors_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalResults_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bQuerySuccessful_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FoundCells_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FoundCells;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoundActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FoundActors;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalResults;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QueryTime;
	static void NewProp_bQuerySuccessful_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bQuerySuccessful;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGridSpatialQueryResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundCells_Inner = { "FoundCells", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundCells = { "FoundCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridSpatialQueryResult, FoundCells), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoundCells_MetaData), NewProp_FoundCells_MetaData) }; // 454277303
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundActors_Inner = { "FoundActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundActors = { "FoundActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridSpatialQueryResult, FoundActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoundActors_MetaData), NewProp_FoundActors_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_TotalResults = { "TotalResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridSpatialQueryResult, TotalResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalResults_MetaData), NewProp_TotalResults_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_QueryTime = { "QueryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridSpatialQueryResult, QueryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryTime_MetaData), NewProp_QueryTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_bQuerySuccessful_SetBit(void* Obj)
{
	((FAuracronGridSpatialQueryResult*)Obj)->bQuerySuccessful = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_bQuerySuccessful = { "bQuerySuccessful", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGridSpatialQueryResult), &Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_bQuerySuccessful_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bQuerySuccessful_MetaData), NewProp_bQuerySuccessful_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridSpatialQueryResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundCells_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_FoundActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_TotalResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_QueryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_bQuerySuccessful,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronGridSpatialQueryResult",
	Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::PropPointers),
	sizeof(FAuracronGridSpatialQueryResult),
	alignof(FAuracronGridSpatialQueryResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGridSpatialQueryResult *************************************

// ********** Begin ScriptStruct FAuracronGridStatistics *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGridStatistics;
class UScriptStruct* FAuracronGridStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGridStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGridStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronGridStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Grid Statistics\n * Performance and usage statistics for the grid system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid Statistics\nPerformance and usage statistics for the grid system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalCells_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PopulatedCells_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmptyCells_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSubdivisionLevel_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageCellDensity_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalActors_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageQueryTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalQueries_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessfulQueries_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QuerySuccessRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalCells;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PopulatedCells;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EmptyCells;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSubdivisionLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageCellDensity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalActors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageQueryTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalQueries;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SuccessfulQueries;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QuerySuccessRate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGridStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_TotalCells = { "TotalCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, TotalCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalCells_MetaData), NewProp_TotalCells_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_PopulatedCells = { "PopulatedCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, PopulatedCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PopulatedCells_MetaData), NewProp_PopulatedCells_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_EmptyCells = { "EmptyCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, EmptyCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmptyCells_MetaData), NewProp_EmptyCells_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_MaxSubdivisionLevel = { "MaxSubdivisionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, MaxSubdivisionLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSubdivisionLevel_MetaData), NewProp_MaxSubdivisionLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_AverageCellDensity = { "AverageCellDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, AverageCellDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageCellDensity_MetaData), NewProp_AverageCellDensity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_TotalActors = { "TotalActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, TotalActors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalActors_MetaData), NewProp_TotalActors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_AverageQueryTime = { "AverageQueryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, AverageQueryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageQueryTime_MetaData), NewProp_AverageQueryTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_TotalQueries = { "TotalQueries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, TotalQueries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalQueries_MetaData), NewProp_TotalQueries_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_SuccessfulQueries = { "SuccessfulQueries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, SuccessfulQueries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessfulQueries_MetaData), NewProp_SuccessfulQueries_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_QuerySuccessRate = { "QuerySuccessRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, QuerySuccessRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QuerySuccessRate_MetaData), NewProp_QuerySuccessRate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGridStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_TotalCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_PopulatedCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_EmptyCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_MaxSubdivisionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_AverageCellDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_TotalActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_AverageQueryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_TotalQueries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_SuccessfulQueries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_QuerySuccessRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronGridStatistics",
	Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::PropPointers),
	sizeof(FAuracronGridStatistics),
	alignof(FAuracronGridStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGridStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGridStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGridStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGridStatistics *********************************************

// ********** Begin Delegate FOnCellCreated ********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics
{
	struct AuracronWorldPartitionGridManager_eventOnCellCreated_Parms
	{
		FAuracronGridCell Cell;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Cell;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::NewProp_Cell = { "Cell", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnCellCreated_Parms, Cell), Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::NewProp_Cell,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "OnCellCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionGridManager::FOnCellCreated_DelegateWrapper(const FMulticastScriptDelegate& OnCellCreated, FAuracronGridCell Cell)
{
	struct AuracronWorldPartitionGridManager_eventOnCellCreated_Parms
	{
		FAuracronGridCell Cell;
	};
	AuracronWorldPartitionGridManager_eventOnCellCreated_Parms Parms;
	Parms.Cell=Cell;
	OnCellCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellCreated **********************************************************

// ********** Begin Delegate FOnCellRemoved ********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics
{
	struct AuracronWorldPartitionGridManager_eventOnCellRemoved_Parms
	{
		FString CellId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnCellRemoved_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::NewProp_CellId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "OnCellRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionGridManager::FOnCellRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnCellRemoved, const FString& CellId)
{
	struct AuracronWorldPartitionGridManager_eventOnCellRemoved_Parms
	{
		FString CellId;
	};
	AuracronWorldPartitionGridManager_eventOnCellRemoved_Parms Parms;
	Parms.CellId=CellId;
	OnCellRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellRemoved **********************************************************

// ********** Begin Delegate FOnCellSubdivided *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics
{
	struct AuracronWorldPartitionGridManager_eventOnCellSubdivided_Parms
	{
		FString ParentCellId;
		TArray<FAuracronGridCell> ChildCells;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParentCellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChildCells_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChildCells;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::NewProp_ParentCellId = { "ParentCellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnCellSubdivided_Parms, ParentCellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::NewProp_ChildCells_Inner = { "ChildCells", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::NewProp_ChildCells = { "ChildCells", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnCellSubdivided_Parms, ChildCells), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::NewProp_ParentCellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::NewProp_ChildCells_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::NewProp_ChildCells,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "OnCellSubdivided__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellSubdivided_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellSubdivided_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionGridManager::FOnCellSubdivided_DelegateWrapper(const FMulticastScriptDelegate& OnCellSubdivided, const FString& ParentCellId, const TArray<FAuracronGridCell>& ChildCells)
{
	struct AuracronWorldPartitionGridManager_eventOnCellSubdivided_Parms
	{
		FString ParentCellId;
		TArray<FAuracronGridCell> ChildCells;
	};
	AuracronWorldPartitionGridManager_eventOnCellSubdivided_Parms Parms;
	Parms.ParentCellId=ParentCellId;
	Parms.ChildCells=ChildCells;
	OnCellSubdivided.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellSubdivided *******************************************************

// ********** Begin Delegate FOnActorAddedToCell ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics
{
	struct AuracronWorldPartitionGridManager_eventOnActorAddedToCell_Parms
	{
		FString ActorId;
		FString CellId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnActorAddedToCell_Parms, ActorId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnActorAddedToCell_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::NewProp_CellId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "OnActorAddedToCell__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnActorAddedToCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnActorAddedToCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionGridManager::FOnActorAddedToCell_DelegateWrapper(const FMulticastScriptDelegate& OnActorAddedToCell, const FString& ActorId, const FString& CellId)
{
	struct AuracronWorldPartitionGridManager_eventOnActorAddedToCell_Parms
	{
		FString ActorId;
		FString CellId;
	};
	AuracronWorldPartitionGridManager_eventOnActorAddedToCell_Parms Parms;
	Parms.ActorId=ActorId;
	Parms.CellId=CellId;
	OnActorAddedToCell.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnActorAddedToCell *****************************************************

// ********** Begin Delegate FOnCellMerged *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionGridManager_eventOnCellMerged_Parms
	{
		FString MergedCellId;
		TArray<FString> OriginalCellIds;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event declarations for merging\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event declarations for merging" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MergedCellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OriginalCellIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OriginalCellIds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::NewProp_MergedCellId = { "MergedCellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnCellMerged_Parms, MergedCellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::NewProp_OriginalCellIds_Inner = { "OriginalCellIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::NewProp_OriginalCellIds = { "OriginalCellIds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventOnCellMerged_Parms, OriginalCellIds), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::NewProp_MergedCellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::NewProp_OriginalCellIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::NewProp_OriginalCellIds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "OnCellMerged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellMerged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::AuracronWorldPartitionGridManager_eventOnCellMerged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionGridManager::FOnCellMerged_DelegateWrapper(const FMulticastScriptDelegate& OnCellMerged, const FString& MergedCellId, const TArray<FString>& OriginalCellIds)
{
	struct AuracronWorldPartitionGridManager_eventOnCellMerged_Parms
	{
		FString MergedCellId;
		TArray<FString> OriginalCellIds;
	};
	AuracronWorldPartitionGridManager_eventOnCellMerged_Parms Parms;
	Parms.MergedCellId=MergedCellId;
	Parms.OriginalCellIds=OriginalCellIds;
	OnCellMerged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellMerged ***********************************************************

// ********** Begin Class UAuracronWorldPartitionGridManager Function AddActorToGrid ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics
{
	struct AuracronWorldPartitionGridManager_eventAddActorToGrid_Parms
	{
		FString ActorId;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventAddActorToGrid_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventAddActorToGrid_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "AddActorToGrid", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::AuracronWorldPartitionGridManager_eventAddActorToGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::AuracronWorldPartitionGridManager_eventAddActorToGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execAddActorToGrid)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddActorToGrid(Z_Param_ActorId,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function AddActorToGrid *****************

// ********** Begin Class UAuracronWorldPartitionGridManager Function AutoMergeCells ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoMergeCells_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoMergeCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "AutoMergeCells", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoMergeCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoMergeCells_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoMergeCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoMergeCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execAutoMergeCells)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AutoMergeCells();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function AutoMergeCells *****************

// ********** Begin Class UAuracronWorldPartitionGridManager Function AutoSubdivideCells ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoSubdivideCells_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoSubdivideCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "AutoSubdivideCells", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoSubdivideCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoSubdivideCells_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoSubdivideCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoSubdivideCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execAutoSubdivideCells)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AutoSubdivideCells();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function AutoSubdivideCells *************

// ********** Begin Class UAuracronWorldPartitionGridManager Function CellIdToCoordinates **********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics
{
	struct AuracronWorldPartitionGridManager_eventCellIdToCoordinates_Parms
	{
		FString CellId;
		FIntVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCellIdToCoordinates_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCellIdToCoordinates_Parms, ReturnValue), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "CellIdToCoordinates", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::AuracronWorldPartitionGridManager_eventCellIdToCoordinates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::AuracronWorldPartitionGridManager_eventCellIdToCoordinates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execCellIdToCoordinates)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FIntVector*)Z_Param__Result=P_THIS->CellIdToCoordinates(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function CellIdToCoordinates ************

// ********** Begin Class UAuracronWorldPartitionGridManager Function CoordinatesToCellId **********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics
{
	struct AuracronWorldPartitionGridManager_eventCoordinatesToCellId_Parms
	{
		FIntVector Coordinates;
		int32 SubdivisionLevel;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "CPP_Default_SubdivisionLevel", "0" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Coordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Coordinates;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SubdivisionLevel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::NewProp_Coordinates = { "Coordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCoordinatesToCellId_Parms, Coordinates), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Coordinates_MetaData), NewProp_Coordinates_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::NewProp_SubdivisionLevel = { "SubdivisionLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCoordinatesToCellId_Parms, SubdivisionLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCoordinatesToCellId_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::NewProp_Coordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::NewProp_SubdivisionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "CoordinatesToCellId", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::AuracronWorldPartitionGridManager_eventCoordinatesToCellId_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::AuracronWorldPartitionGridManager_eventCoordinatesToCellId_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execCoordinatesToCellId)
{
	P_GET_STRUCT_REF(FIntVector,Z_Param_Out_Coordinates);
	P_GET_PROPERTY(FIntProperty,Z_Param_SubdivisionLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CoordinatesToCellId(Z_Param_Out_Coordinates,Z_Param_SubdivisionLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function CoordinatesToCellId ************

// ********** Begin Class UAuracronWorldPartitionGridManager Function CreateCell *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics
{
	struct AuracronWorldPartitionGridManager_eventCreateCell_Parms
	{
		FIntVector Coordinates;
		int32 SubdivisionLevel;
		FAuracronGridCell ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell operations\n" },
#endif
		{ "CPP_Default_SubdivisionLevel", "0" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Coordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Coordinates;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SubdivisionLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::NewProp_Coordinates = { "Coordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCreateCell_Parms, Coordinates), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Coordinates_MetaData), NewProp_Coordinates_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::NewProp_SubdivisionLevel = { "SubdivisionLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCreateCell_Parms, SubdivisionLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCreateCell_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::NewProp_Coordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::NewProp_SubdivisionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "CreateCell", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::AuracronWorldPartitionGridManager_eventCreateCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::AuracronWorldPartitionGridManager_eventCreateCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execCreateCell)
{
	P_GET_STRUCT_REF(FIntVector,Z_Param_Out_Coordinates);
	P_GET_PROPERTY(FIntProperty,Z_Param_SubdivisionLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGridCell*)Z_Param__Result=P_THIS->CreateCell(Z_Param_Out_Coordinates,Z_Param_SubdivisionLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function CreateCell *********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function CreateGrid *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics
{
	struct AuracronWorldPartitionGridManager_eventCreateGrid_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventCreateGrid_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "CreateGrid", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::AuracronWorldPartitionGridManager_eventCreateGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::AuracronWorldPartitionGridManager_eventCreateGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execCreateGrid)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateGrid(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function CreateGrid *********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function DestroyGrid ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DestroyGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DestroyGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "DestroyGrid", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DestroyGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DestroyGrid_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DestroyGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DestroyGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execDestroyGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DestroyGrid();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function DestroyGrid ********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function DrawDebugCell ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics
{
	struct AuracronWorldPartitionGridManager_eventDrawDebugCell_Parms
	{
		UWorld* World;
		FString CellId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventDrawDebugCell_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventDrawDebugCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::NewProp_CellId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "DrawDebugCell", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::AuracronWorldPartitionGridManager_eventDrawDebugCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::AuracronWorldPartitionGridManager_eventDrawDebugCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execDrawDebugCell)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugCell(Z_Param_World,Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function DrawDebugCell ******************

// ********** Begin Class UAuracronWorldPartitionGridManager Function DrawDebugGrid ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics
{
	struct AuracronWorldPartitionGridManager_eventDrawDebugGrid_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventDrawDebugGrid_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "DrawDebugGrid", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::AuracronWorldPartitionGridManager_eventDrawDebugGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::AuracronWorldPartitionGridManager_eventDrawDebugGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execDrawDebugGrid)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugGrid(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function DrawDebugGrid ******************

// ********** Begin Class UAuracronWorldPartitionGridManager Function EnableDebugVisualization *****
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics
{
	struct AuracronWorldPartitionGridManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionGridManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionGridManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::AuracronWorldPartitionGridManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::AuracronWorldPartitionGridManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function EnableDebugVisualization *******

// ********** Begin Class UAuracronWorldPartitionGridManager Function ExecuteSpatialQuery **********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics
{
	struct AuracronWorldPartitionGridManager_eventExecuteSpatialQuery_Parms
	{
		FAuracronSpatialQueryParams QueryParams;
		FAuracronGridSpatialQueryResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryParams_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryParams;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::NewProp_QueryParams = { "QueryParams", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventExecuteSpatialQuery_Parms, QueryParams), Z_Construct_UScriptStruct_FAuracronSpatialQueryParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryParams_MetaData), NewProp_QueryParams_MetaData) }; // 2185592208
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventExecuteSpatialQuery_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult, METADATA_PARAMS(0, nullptr) }; // 118800746
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::NewProp_QueryParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "ExecuteSpatialQuery", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::AuracronWorldPartitionGridManager_eventExecuteSpatialQuery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::AuracronWorldPartitionGridManager_eventExecuteSpatialQuery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execExecuteSpatialQuery)
{
	P_GET_STRUCT_REF(FAuracronSpatialQueryParams,Z_Param_Out_QueryParams);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGridSpatialQueryResult*)Z_Param__Result=P_THIS->ExecuteSpatialQuery(Z_Param_Out_QueryParams);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function ExecuteSpatialQuery ************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetActorsInCell **************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetActorsInCell_Parms
	{
		FString CellId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetActorsInCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetActorsInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetActorsInCell", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::AuracronWorldPartitionGridManager_eventGetActorsInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::AuracronWorldPartitionGridManager_eventGetActorsInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetActorsInCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActorsInCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetActorsInCell ****************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetActorsInRadius ************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetActorsInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetActorsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetActorsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetActorsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetActorsInRadius", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::AuracronWorldPartitionGridManager_eventGetActorsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::AuracronWorldPartitionGridManager_eventGetActorsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetActorsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActorsInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetActorsInRadius **************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetAllCells ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetAllCells_Parms
	{
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetAllCells_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetAllCells", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::AuracronWorldPartitionGridManager_eventGetAllCells_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::AuracronWorldPartitionGridManager_eventGetAllCells_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetAllCells)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->GetAllCells();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetAllCells ********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetAverageCellDensity ********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetAverageCellDensity_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetAverageCellDensity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetAverageCellDensity", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::AuracronWorldPartitionGridManager_eventGetAverageCellDensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::AuracronWorldPartitionGridManager_eventGetAverageCellDensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetAverageCellDensity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageCellDensity();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetAverageCellDensity **********

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetCell **********************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetCell_Parms
	{
		FString CellId;
		FAuracronGridCell ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCell_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetCell", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::AuracronWorldPartitionGridManager_eventGetCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::AuracronWorldPartitionGridManager_eventGetCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGridCell*)Z_Param__Result=P_THIS->GetCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetCell ************************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetCellAtLocation ************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetCellAtLocation_Parms
	{
		FVector Location;
		FAuracronGridCell ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellAtLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetCellAtLocation", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::AuracronWorldPartitionGridManager_eventGetCellAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::AuracronWorldPartitionGridManager_eventGetCellAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetCellAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGridCell*)Z_Param__Result=P_THIS->GetCellAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetCellAtLocation **************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetCellsInBox ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetCellsInBox_Parms
	{
		FBox Box;
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Box_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Box;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::NewProp_Box = { "Box", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellsInBox_Parms, Box), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Box_MetaData), NewProp_Box_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellsInBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::NewProp_Box,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetCellsInBox", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::AuracronWorldPartitionGridManager_eventGetCellsInBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::AuracronWorldPartitionGridManager_eventGetCellsInBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetCellsInBox)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Box);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->GetCellsInBox(Z_Param_Out_Box);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetCellsInBox ******************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetCellsInRadius *************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetCellsInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetCellsInRadius", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::AuracronWorldPartitionGridManager_eventGetCellsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::AuracronWorldPartitionGridManager_eventGetCellsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetCellsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->GetCellsInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetCellsInRadius ***************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetCellSize ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetCellSize_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetCellSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetCellSize", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::AuracronWorldPartitionGridManager_eventGetCellSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::AuracronWorldPartitionGridManager_eventGetCellSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetCellSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCellSize();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetCellSize ********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetConfiguration *************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetConfiguration_Parms
	{
		FAuracronGridConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGridConfiguration, METADATA_PARAMS(0, nullptr) }; // 3576381665
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::AuracronWorldPartitionGridManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::AuracronWorldPartitionGridManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGridConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetConfiguration ***************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetGridStatistics ************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetGridStatistics_Parms
	{
		FAuracronGridStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetGridStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGridStatistics, METADATA_PARAMS(0, nullptr) }; // 637501972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetGridStatistics", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::AuracronWorldPartitionGridManager_eventGetGridStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::AuracronWorldPartitionGridManager_eventGetGridStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetGridStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGridStatistics*)Z_Param__Result=P_THIS->GetGridStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetGridStatistics **************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetInstance ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionGridManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionGridManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::AuracronWorldPartitionGridManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::AuracronWorldPartitionGridManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionGridManager**)Z_Param__Result=UAuracronWorldPartitionGridManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetInstance ********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetPopulatedCellCount ********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetPopulatedCellCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetPopulatedCellCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetPopulatedCellCount", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::AuracronWorldPartitionGridManager_eventGetPopulatedCellCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::AuracronWorldPartitionGridManager_eventGetPopulatedCellCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetPopulatedCellCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPopulatedCellCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetPopulatedCellCount **********

// ********** Begin Class UAuracronWorldPartitionGridManager Function GetTotalCellCount ************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics
{
	struct AuracronWorldPartitionGridManager_eventGetTotalCellCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGetTotalCellCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GetTotalCellCount", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::AuracronWorldPartitionGridManager_eventGetTotalCellCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::AuracronWorldPartitionGridManager_eventGetTotalCellCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGetTotalCellCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalCellCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GetTotalCellCount **************

// ********** Begin Class UAuracronWorldPartitionGridManager Function GridToWorldCoordinates *******
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics
{
	struct AuracronWorldPartitionGridManager_eventGridToWorldCoordinates_Parms
	{
		FIntVector GridCoordinates;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridCoordinates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::NewProp_GridCoordinates = { "GridCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGridToWorldCoordinates_Parms, GridCoordinates), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridCoordinates_MetaData), NewProp_GridCoordinates_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventGridToWorldCoordinates_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::NewProp_GridCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "GridToWorldCoordinates", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::AuracronWorldPartitionGridManager_eventGridToWorldCoordinates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::AuracronWorldPartitionGridManager_eventGridToWorldCoordinates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execGridToWorldCoordinates)
{
	P_GET_STRUCT_REF(FIntVector,Z_Param_Out_GridCoordinates);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GridToWorldCoordinates(Z_Param_Out_GridCoordinates);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function GridToWorldCoordinates *********

// ********** Begin Class UAuracronWorldPartitionGridManager Function Initialize *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics
{
	struct AuracronWorldPartitionGridManager_eventInitialize_Parms
	{
		FAuracronGridConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronGridConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3576381665
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::AuracronWorldPartitionGridManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::AuracronWorldPartitionGridManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronGridConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function Initialize *********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function IsDebugVisualizationEnabled **
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronWorldPartitionGridManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionGridManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionGridManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::AuracronWorldPartitionGridManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::AuracronWorldPartitionGridManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function IsDebugVisualizationEnabled ****

// ********** Begin Class UAuracronWorldPartitionGridManager Function IsInitialized ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionGridManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionGridManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionGridManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::AuracronWorldPartitionGridManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::AuracronWorldPartitionGridManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function IsInitialized ******************

// ********** Begin Class UAuracronWorldPartitionGridManager Function LogGridState *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_LogGridState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_LogGridState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "LogGridState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_LogGridState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_LogGridState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_LogGridState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_LogGridState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execLogGridState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogGridState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function LogGridState *******************

// ********** Begin Class UAuracronWorldPartitionGridManager Function MergeCells *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics
{
	struct AuracronWorldPartitionGridManager_eventMergeCells_Parms
	{
		TArray<FString> CellIds;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellIds_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CellIds;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_CellIds_Inner = { "CellIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_CellIds = { "CellIds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventMergeCells_Parms, CellIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellIds_MetaData), NewProp_CellIds_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionGridManager_eventMergeCells_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionGridManager_eventMergeCells_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_CellIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_CellIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "MergeCells", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::AuracronWorldPartitionGridManager_eventMergeCells_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::AuracronWorldPartitionGridManager_eventMergeCells_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execMergeCells)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_CellIds);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MergeCells(Z_Param_Out_CellIds);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function MergeCells *********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function OptimizeGrid *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_OptimizeGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_OptimizeGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "OptimizeGrid", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_OptimizeGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_OptimizeGrid_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_OptimizeGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_OptimizeGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execOptimizeGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeGrid();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function OptimizeGrid *******************

// ********** Begin Class UAuracronWorldPartitionGridManager Function QueryCellsByBox **************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics
{
	struct AuracronWorldPartitionGridManager_eventQueryCellsByBox_Parms
	{
		FBox Box;
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Box_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Box;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::NewProp_Box = { "Box", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByBox_Parms, Box), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Box_MetaData), NewProp_Box_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::NewProp_Box,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "QueryCellsByBox", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::AuracronWorldPartitionGridManager_eventQueryCellsByBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::AuracronWorldPartitionGridManager_eventQueryCellsByBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execQueryCellsByBox)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Box);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->QueryCellsByBox(Z_Param_Out_Box);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function QueryCellsByBox ****************

// ********** Begin Class UAuracronWorldPartitionGridManager Function QueryCellsByPoint ************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics
{
	struct AuracronWorldPartitionGridManager_eventQueryCellsByPoint_Parms
	{
		FVector Point;
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByPoint_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByPoint_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "QueryCellsByPoint", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::AuracronWorldPartitionGridManager_eventQueryCellsByPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::AuracronWorldPartitionGridManager_eventQueryCellsByPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execQueryCellsByPoint)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->QueryCellsByPoint(Z_Param_Out_Point);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function QueryCellsByPoint **************

// ********** Begin Class UAuracronWorldPartitionGridManager Function QueryCellsByRay **************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics
{
	struct AuracronWorldPartitionGridManager_eventQueryCellsByRay_Parms
	{
		FVector Origin;
		FVector Direction;
		float Distance;
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Origin_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Direction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Origin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_Origin = { "Origin", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByRay_Parms, Origin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Origin_MetaData), NewProp_Origin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByRay_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Direction_MetaData), NewProp_Direction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByRay_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsByRay_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_Origin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "QueryCellsByRay", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::AuracronWorldPartitionGridManager_eventQueryCellsByRay_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::AuracronWorldPartitionGridManager_eventQueryCellsByRay_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execQueryCellsByRay)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Origin);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Direction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->QueryCellsByRay(Z_Param_Out_Origin,Z_Param_Out_Direction,Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function QueryCellsByRay ****************

// ********** Begin Class UAuracronWorldPartitionGridManager Function QueryCellsBySphere ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics
{
	struct AuracronWorldPartitionGridManager_eventQueryCellsBySphere_Parms
	{
		FVector Center;
		float Radius;
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsBySphere_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsBySphere_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventQueryCellsBySphere_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "QueryCellsBySphere", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::AuracronWorldPartitionGridManager_eventQueryCellsBySphere_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::AuracronWorldPartitionGridManager_eventQueryCellsBySphere_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execQueryCellsBySphere)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->QueryCellsBySphere(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function QueryCellsBySphere *************

// ********** Begin Class UAuracronWorldPartitionGridManager Function RebuildGrid ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RebuildGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RebuildGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "RebuildGrid", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RebuildGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RebuildGrid_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RebuildGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RebuildGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execRebuildGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RebuildGrid();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function RebuildGrid ********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function RemoveActorFromGrid **********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics
{
	struct AuracronWorldPartitionGridManager_eventRemoveActorFromGrid_Parms
	{
		FString ActorId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventRemoveActorFromGrid_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::NewProp_ActorId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "RemoveActorFromGrid", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::AuracronWorldPartitionGridManager_eventRemoveActorFromGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::AuracronWorldPartitionGridManager_eventRemoveActorFromGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execRemoveActorFromGrid)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveActorFromGrid(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function RemoveActorFromGrid ************

// ********** Begin Class UAuracronWorldPartitionGridManager Function RemoveCell *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics
{
	struct AuracronWorldPartitionGridManager_eventRemoveCell_Parms
	{
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventRemoveCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionGridManager_eventRemoveCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionGridManager_eventRemoveCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "RemoveCell", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::AuracronWorldPartitionGridManager_eventRemoveCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::AuracronWorldPartitionGridManager_eventRemoveCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execRemoveCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function RemoveCell *********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function ResetStatistics **************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function ResetStatistics ****************

// ********** Begin Class UAuracronWorldPartitionGridManager Function SetCellSize ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics
{
	struct AuracronWorldPartitionGridManager_eventSetCellSize_Parms
	{
		int32 NewCellSize;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewCellSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::NewProp_NewCellSize = { "NewCellSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventSetCellSize_Parms, NewCellSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::NewProp_NewCellSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "SetCellSize", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::AuracronWorldPartitionGridManager_eventSetCellSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::AuracronWorldPartitionGridManager_eventSetCellSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execSetCellSize)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewCellSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCellSize(Z_Param_NewCellSize);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function SetCellSize ********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function SetConfiguration *************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionGridManager_eventSetConfiguration_Parms
	{
		FAuracronGridConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronGridConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3576381665
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::AuracronWorldPartitionGridManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::AuracronWorldPartitionGridManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronGridConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function SetConfiguration ***************

// ********** Begin Class UAuracronWorldPartitionGridManager Function Shutdown *********************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function Shutdown ***********************

// ********** Begin Class UAuracronWorldPartitionGridManager Function SubdivideCell ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics
{
	struct AuracronWorldPartitionGridManager_eventSubdivideCell_Parms
	{
		FString CellId;
		TArray<FAuracronGridCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell subdivision\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell subdivision" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventSubdivideCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventSubdivideCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 454277303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "SubdivideCell", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::AuracronWorldPartitionGridManager_eventSubdivideCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::AuracronWorldPartitionGridManager_eventSubdivideCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execSubdivideCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronGridCell>*)Z_Param__Result=P_THIS->SubdivideCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function SubdivideCell ******************

// ********** Begin Class UAuracronWorldPartitionGridManager Function UpdateActorLocation **********
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics
{
	struct AuracronWorldPartitionGridManager_eventUpdateActorLocation_Parms
	{
		FString ActorId;
		FVector NewLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventUpdateActorLocation_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::NewProp_NewLocation = { "NewLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventUpdateActorLocation_Parms, NewLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewLocation_MetaData), NewProp_NewLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::NewProp_NewLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "UpdateActorLocation", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::AuracronWorldPartitionGridManager_eventUpdateActorLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::AuracronWorldPartitionGridManager_eventUpdateActorLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execUpdateActorLocation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateActorLocation(Z_Param_ActorId,Z_Param_Out_NewLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function UpdateActorLocation ************

// ********** Begin Class UAuracronWorldPartitionGridManager Function WorldToGridCoordinates *******
struct Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics
{
	struct AuracronWorldPartitionGridManager_eventWorldToGridCoordinates_Parms
	{
		FVector WorldLocation;
		FIntVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Coordinate mapping\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coordinate mapping" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventWorldToGridCoordinates_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionGridManager_eventWorldToGridCoordinates_Parms, ReturnValue), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionGridManager, nullptr, "WorldToGridCoordinates", Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::AuracronWorldPartitionGridManager_eventWorldToGridCoordinates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::AuracronWorldPartitionGridManager_eventWorldToGridCoordinates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionGridManager::execWorldToGridCoordinates)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FIntVector*)Z_Param__Result=P_THIS->WorldToGridCoordinates(Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionGridManager Function WorldToGridCoordinates *********

// ********** Begin Class UAuracronWorldPartitionGridManager ***************************************
void UAuracronWorldPartitionGridManager::StaticRegisterNativesUAuracronWorldPartitionGridManager()
{
	UClass* Class = UAuracronWorldPartitionGridManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddActorToGrid", &UAuracronWorldPartitionGridManager::execAddActorToGrid },
		{ "AutoMergeCells", &UAuracronWorldPartitionGridManager::execAutoMergeCells },
		{ "AutoSubdivideCells", &UAuracronWorldPartitionGridManager::execAutoSubdivideCells },
		{ "CellIdToCoordinates", &UAuracronWorldPartitionGridManager::execCellIdToCoordinates },
		{ "CoordinatesToCellId", &UAuracronWorldPartitionGridManager::execCoordinatesToCellId },
		{ "CreateCell", &UAuracronWorldPartitionGridManager::execCreateCell },
		{ "CreateGrid", &UAuracronWorldPartitionGridManager::execCreateGrid },
		{ "DestroyGrid", &UAuracronWorldPartitionGridManager::execDestroyGrid },
		{ "DrawDebugCell", &UAuracronWorldPartitionGridManager::execDrawDebugCell },
		{ "DrawDebugGrid", &UAuracronWorldPartitionGridManager::execDrawDebugGrid },
		{ "EnableDebugVisualization", &UAuracronWorldPartitionGridManager::execEnableDebugVisualization },
		{ "ExecuteSpatialQuery", &UAuracronWorldPartitionGridManager::execExecuteSpatialQuery },
		{ "GetActorsInCell", &UAuracronWorldPartitionGridManager::execGetActorsInCell },
		{ "GetActorsInRadius", &UAuracronWorldPartitionGridManager::execGetActorsInRadius },
		{ "GetAllCells", &UAuracronWorldPartitionGridManager::execGetAllCells },
		{ "GetAverageCellDensity", &UAuracronWorldPartitionGridManager::execGetAverageCellDensity },
		{ "GetCell", &UAuracronWorldPartitionGridManager::execGetCell },
		{ "GetCellAtLocation", &UAuracronWorldPartitionGridManager::execGetCellAtLocation },
		{ "GetCellsInBox", &UAuracronWorldPartitionGridManager::execGetCellsInBox },
		{ "GetCellsInRadius", &UAuracronWorldPartitionGridManager::execGetCellsInRadius },
		{ "GetCellSize", &UAuracronWorldPartitionGridManager::execGetCellSize },
		{ "GetConfiguration", &UAuracronWorldPartitionGridManager::execGetConfiguration },
		{ "GetGridStatistics", &UAuracronWorldPartitionGridManager::execGetGridStatistics },
		{ "GetInstance", &UAuracronWorldPartitionGridManager::execGetInstance },
		{ "GetPopulatedCellCount", &UAuracronWorldPartitionGridManager::execGetPopulatedCellCount },
		{ "GetTotalCellCount", &UAuracronWorldPartitionGridManager::execGetTotalCellCount },
		{ "GridToWorldCoordinates", &UAuracronWorldPartitionGridManager::execGridToWorldCoordinates },
		{ "Initialize", &UAuracronWorldPartitionGridManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronWorldPartitionGridManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronWorldPartitionGridManager::execIsInitialized },
		{ "LogGridState", &UAuracronWorldPartitionGridManager::execLogGridState },
		{ "MergeCells", &UAuracronWorldPartitionGridManager::execMergeCells },
		{ "OptimizeGrid", &UAuracronWorldPartitionGridManager::execOptimizeGrid },
		{ "QueryCellsByBox", &UAuracronWorldPartitionGridManager::execQueryCellsByBox },
		{ "QueryCellsByPoint", &UAuracronWorldPartitionGridManager::execQueryCellsByPoint },
		{ "QueryCellsByRay", &UAuracronWorldPartitionGridManager::execQueryCellsByRay },
		{ "QueryCellsBySphere", &UAuracronWorldPartitionGridManager::execQueryCellsBySphere },
		{ "RebuildGrid", &UAuracronWorldPartitionGridManager::execRebuildGrid },
		{ "RemoveActorFromGrid", &UAuracronWorldPartitionGridManager::execRemoveActorFromGrid },
		{ "RemoveCell", &UAuracronWorldPartitionGridManager::execRemoveCell },
		{ "ResetStatistics", &UAuracronWorldPartitionGridManager::execResetStatistics },
		{ "SetCellSize", &UAuracronWorldPartitionGridManager::execSetCellSize },
		{ "SetConfiguration", &UAuracronWorldPartitionGridManager::execSetConfiguration },
		{ "Shutdown", &UAuracronWorldPartitionGridManager::execShutdown },
		{ "SubdivideCell", &UAuracronWorldPartitionGridManager::execSubdivideCell },
		{ "UpdateActorLocation", &UAuracronWorldPartitionGridManager::execUpdateActorLocation },
		{ "WorldToGridCoordinates", &UAuracronWorldPartitionGridManager::execWorldToGridCoordinates },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager;
UClass* UAuracronWorldPartitionGridManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionGridManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionGridManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionGridManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionGridManager_NoRegister()
{
	return UAuracronWorldPartitionGridManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Grid Manager\n * Central manager for grid-based world partitioning\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionGrid.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Grid Manager\nCentral manager for grid-based world partitioning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellCreated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellSubdivided_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorAddedToCell_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellMerged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionGrid.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellSubdivided;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorAddedToCell;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellMerged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AddActorToGrid, "AddActorToGrid" }, // 3785488852
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoMergeCells, "AutoMergeCells" }, // 429429138
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_AutoSubdivideCells, "AutoSubdivideCells" }, // 720705513
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CellIdToCoordinates, "CellIdToCoordinates" }, // 176075471
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CoordinatesToCellId, "CoordinatesToCellId" }, // 3241491870
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateCell, "CreateCell" }, // 2281898580
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_CreateGrid, "CreateGrid" }, // 2577832170
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DestroyGrid, "DestroyGrid" }, // 4005242415
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugCell, "DrawDebugCell" }, // 3058898614
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_DrawDebugGrid, "DrawDebugGrid" }, // 1094316827
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 4111907376
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ExecuteSpatialQuery, "ExecuteSpatialQuery" }, // 849358302
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInCell, "GetActorsInCell" }, // 2062087083
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetActorsInRadius, "GetActorsInRadius" }, // 755224808
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAllCells, "GetAllCells" }, // 3773605046
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetAverageCellDensity, "GetAverageCellDensity" }, // 4283662697
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCell, "GetCell" }, // 3094976209
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellAtLocation, "GetCellAtLocation" }, // 2177039522
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInBox, "GetCellsInBox" }, // 3106996383
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellsInRadius, "GetCellsInRadius" }, // 2823526099
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetCellSize, "GetCellSize" }, // 3685781106
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetConfiguration, "GetConfiguration" }, // 3452106250
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetGridStatistics, "GetGridStatistics" }, // 1977707625
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetInstance, "GetInstance" }, // 3734456734
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetPopulatedCellCount, "GetPopulatedCellCount" }, // 3986860061
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GetTotalCellCount, "GetTotalCellCount" }, // 2675770460
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_GridToWorldCoordinates, "GridToWorldCoordinates" }, // 3389664908
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Initialize, "Initialize" }, // 3517307692
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 500636227
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_IsInitialized, "IsInitialized" }, // 4251121089
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_LogGridState, "LogGridState" }, // 2978289044
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_MergeCells, "MergeCells" }, // 951747905
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature, "OnActorAddedToCell__DelegateSignature" }, // 3376109223
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature, "OnCellCreated__DelegateSignature" }, // 4262813906
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature, "OnCellMerged__DelegateSignature" }, // 1866110532
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature, "OnCellRemoved__DelegateSignature" }, // 2394915649
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature, "OnCellSubdivided__DelegateSignature" }, // 3027674626
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_OptimizeGrid, "OptimizeGrid" }, // 2233900244
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByBox, "QueryCellsByBox" }, // 3153539307
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByPoint, "QueryCellsByPoint" }, // 801170257
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsByRay, "QueryCellsByRay" }, // 2276226971
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_QueryCellsBySphere, "QueryCellsBySphere" }, // 227554365
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RebuildGrid, "RebuildGrid" }, // 317177736
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveActorFromGrid, "RemoveActorFromGrid" }, // 864238546
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_RemoveCell, "RemoveCell" }, // 3944546968
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_ResetStatistics, "ResetStatistics" }, // 2451565665
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetCellSize, "SetCellSize" }, // 3085222566
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SetConfiguration, "SetConfiguration" }, // 412996517
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_Shutdown, "Shutdown" }, // 4213781737
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_SubdivideCell, "SubdivideCell" }, // 3839026886
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_UpdateActorLocation, "UpdateActorLocation" }, // 3485979265
		{ &Z_Construct_UFunction_UAuracronWorldPartitionGridManager_WorldToGridCoordinates, "WorldToGridCoordinates" }, // 2026985729
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionGridManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellCreated = { "OnCellCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionGridManager, OnCellCreated), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellCreated_MetaData), NewProp_OnCellCreated_MetaData) }; // 4262813906
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellRemoved = { "OnCellRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionGridManager, OnCellRemoved), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellRemoved_MetaData), NewProp_OnCellRemoved_MetaData) }; // 2394915649
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellSubdivided = { "OnCellSubdivided", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionGridManager, OnCellSubdivided), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellSubdivided_MetaData), NewProp_OnCellSubdivided_MetaData) }; // 3027674626
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnActorAddedToCell = { "OnActorAddedToCell", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionGridManager, OnActorAddedToCell), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorAddedToCell_MetaData), NewProp_OnActorAddedToCell_MetaData) }; // 3376109223
void Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionGridManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionGridManager), &Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionGridManager, Configuration), Z_Construct_UScriptStruct_FAuracronGridConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3576381665
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionGridManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellMerged = { "OnCellMerged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionGridManager, OnCellMerged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellMerged_MetaData), NewProp_OnCellMerged_MetaData) }; // 1866110532
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellSubdivided,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnActorAddedToCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::NewProp_OnCellMerged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::ClassParams = {
	&UAuracronWorldPartitionGridManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionGridManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager.OuterSingleton;
}
UAuracronWorldPartitionGridManager::UAuracronWorldPartitionGridManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionGridManager);
UAuracronWorldPartitionGridManager::~UAuracronWorldPartitionGridManager() {}
// ********** End Class UAuracronWorldPartitionGridManager *****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronGridSubdivisionType_StaticEnum, TEXT("EAuracronGridSubdivisionType"), &Z_Registration_Info_UEnum_EAuracronGridSubdivisionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2992809630U) },
		{ EAuracronGridCellState_StaticEnum, TEXT("EAuracronGridCellState"), &Z_Registration_Info_UEnum_EAuracronGridCellState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3996164315U) },
		{ EAuracronGridSpatialQueryType_StaticEnum, TEXT("EAuracronGridSpatialQueryType"), &Z_Registration_Info_UEnum_EAuracronGridSpatialQueryType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1723139936U) },
		{ EAuracronGridOptimizationLevel_StaticEnum, TEXT("EAuracronGridOptimizationLevel"), &Z_Registration_Info_UEnum_EAuracronGridOptimizationLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3483478850U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronGridConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics::NewStructOps, TEXT("AuracronGridConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronGridConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGridConfiguration), 3576381665U) },
		{ FAuracronGridCell::StaticStruct, Z_Construct_UScriptStruct_FAuracronGridCell_Statics::NewStructOps, TEXT("AuracronGridCell"), &Z_Registration_Info_UScriptStruct_FAuracronGridCell, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGridCell), 454277303U) },
		{ FAuracronSpatialQueryParams::StaticStruct, Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics::NewStructOps, TEXT("AuracronSpatialQueryParams"), &Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSpatialQueryParams), 2185592208U) },
		{ FAuracronGridSpatialQueryResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics::NewStructOps, TEXT("AuracronGridSpatialQueryResult"), &Z_Registration_Info_UScriptStruct_FAuracronGridSpatialQueryResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGridSpatialQueryResult), 118800746U) },
		{ FAuracronGridStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics::NewStructOps, TEXT("AuracronGridStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronGridStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGridStatistics), 637501972U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionGridManager, UAuracronWorldPartitionGridManager::StaticClass, TEXT("UAuracronWorldPartitionGridManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionGridManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionGridManager), 97840033U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_2997762285(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
