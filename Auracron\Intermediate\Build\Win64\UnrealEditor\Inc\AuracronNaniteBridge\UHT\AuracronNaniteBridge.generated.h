// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronNaniteBridge.h"

#ifdef AURACRONNANITEBRIDGE_AuracronNaniteBridge_generated_h
#error "AuracronNaniteBridge.generated.h already included, missing '#pragma once' in AuracronNaniteBridge.h"
#endif
#define AURACRONNANITEBRIDGE_AuracronNaniteBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UInstancedStaticMeshComponent;
class UProceduralMeshComponent;
class UStaticMesh;
class UStaticMeshComponent;
enum class EAuracronNaniteQuality : uint8;
struct FAuracronGeometryInstance;

// ********** Begin ScriptStruct FAuracronNaniteConfiguration **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_66_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronNaniteConfiguration;
// ********** End ScriptStruct FAuracronNaniteConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronRealmGeometryConfiguration *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_143_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmGeometryConfiguration;
// ********** End ScriptStruct FAuracronRealmGeometryConfiguration *********************************

// ********** Begin ScriptStruct FAuracronGeometryInstance *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_200_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGeometryInstance;
// ********** End ScriptStruct FAuracronGeometryInstance *******************************************

// ********** Begin Delegate FOnGeometrySpawned ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_463_DELEGATE \
static void FOnGeometrySpawned_DelegateWrapper(const FMulticastScriptDelegate& OnGeometrySpawned, UStaticMeshComponent* MeshComponent, FAuracronGeometryInstance GeometryConfig);


// ********** End Delegate FOnGeometrySpawned ******************************************************

// ********** Begin Delegate FOnNaniteQualityChanged ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_468_DELEGATE \
static void FOnNaniteQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnNaniteQualityChanged, EAuracronNaniteQuality OldQuality, EAuracronNaniteQuality NewQuality);


// ********** End Delegate FOnNaniteQualityChanged *************************************************

// ********** Begin Class UAuracronNaniteBridge ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_262_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetNaniteStatistics); \
	DECLARE_FUNCTION(execCleanupUnusedGeometry); \
	DECLARE_FUNCTION(execSetNaniteQuality); \
	DECLARE_FUNCTION(execOptimizeGeometryByDistance); \
	DECLARE_FUNCTION(execConvertProceduralToNanite); \
	DECLARE_FUNCTION(execUpdateProceduralGeometry); \
	DECLARE_FUNCTION(execGenerateProceduralGeometry); \
	DECLARE_FUNCTION(execSpawnPortalGeometry); \
	DECLARE_FUNCTION(execTransitionRealmGeometry); \
	DECLARE_FUNCTION(execUpdateRealmGeometry); \
	DECLARE_FUNCTION(execGenerateRealmGeometry); \
	DECLARE_FUNCTION(execUpdateGeometryInstance); \
	DECLARE_FUNCTION(execRemoveGeometryInstance); \
	DECLARE_FUNCTION(execAddGeometryInstance); \
	DECLARE_FUNCTION(execCreateGeometryInstances); \
	DECLARE_FUNCTION(execGenerateNaniteLODs); \
	DECLARE_FUNCTION(execOptimizeMeshForNanite); \
	DECLARE_FUNCTION(execConvertMeshToNanite); \
	DECLARE_FUNCTION(execSpawnNaniteGeometry);


AURACRONNANITEBRIDGE_API UClass* Z_Construct_UClass_UAuracronNaniteBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_262_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronNaniteBridge(); \
	friend struct Z_Construct_UClass_UAuracronNaniteBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONNANITEBRIDGE_API UClass* Z_Construct_UClass_UAuracronNaniteBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronNaniteBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronNaniteBridge"), Z_Construct_UClass_UAuracronNaniteBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronNaniteBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		NaniteConfiguration=NETFIELD_REP_START, \
		NETFIELD_REP_END=NaniteConfiguration	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_262_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronNaniteBridge(UAuracronNaniteBridge&&) = delete; \
	UAuracronNaniteBridge(const UAuracronNaniteBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronNaniteBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronNaniteBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronNaniteBridge) \
	NO_API virtual ~UAuracronNaniteBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_259_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_262_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_262_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_262_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h_262_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronNaniteBridge;

// ********** End Class UAuracronNaniteBridge ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h

// ********** Begin Enum EAuracronNaniteGeometryType ***********************************************
#define FOREACH_ENUM_EAURACRONNANITEGEOMETRYTYPE(op) \
	op(EAuracronNaniteGeometryType::None) \
	op(EAuracronNaniteGeometryType::StaticMesh) \
	op(EAuracronNaniteGeometryType::InstancedMesh) \
	op(EAuracronNaniteGeometryType::ProceduralMesh) \
	op(EAuracronNaniteGeometryType::LandscapeSpline) \
	op(EAuracronNaniteGeometryType::Foliage) \
	op(EAuracronNaniteGeometryType::Architecture) \
	op(EAuracronNaniteGeometryType::Decoration) \
	op(EAuracronNaniteGeometryType::Terrain) \
	op(EAuracronNaniteGeometryType::Destructible) 

enum class EAuracronNaniteGeometryType : uint8;
template<> struct TIsUEnumClass<EAuracronNaniteGeometryType> { enum { Value = true }; };
template<> AURACRONNANITEBRIDGE_API UEnum* StaticEnum<EAuracronNaniteGeometryType>();
// ********** End Enum EAuracronNaniteGeometryType *************************************************

// ********** Begin Enum EAuracronNaniteQuality ****************************************************
#define FOREACH_ENUM_EAURACRONNANITEQUALITY(op) \
	op(EAuracronNaniteQuality::Low) \
	op(EAuracronNaniteQuality::Medium) \
	op(EAuracronNaniteQuality::High) \
	op(EAuracronNaniteQuality::Ultra) \
	op(EAuracronNaniteQuality::Cinematic) 

enum class EAuracronNaniteQuality : uint8;
template<> struct TIsUEnumClass<EAuracronNaniteQuality> { enum { Value = true }; };
template<> AURACRONNANITEBRIDGE_API UEnum* StaticEnum<EAuracronNaniteQuality>();
// ********** End Enum EAuracronNaniteQuality ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
