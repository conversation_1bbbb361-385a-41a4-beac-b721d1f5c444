// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Test Utilities Implementation
// Bridge 2.18: PCG Framework - Testing e Validation

#include "AuracronPCGTestingFramework.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Misc/Paths.h"
#include "HAL/PlatformFilemanager.h"

// =============================================================================
// TEST UTILITIES IMPLEMENTATION
// =============================================================================

TMap<FString, FDateTime> UAuracronPCGTestUtils::TestTimers;

UPCGPointData* UAuracronPCGTestUtils::GenerateTestPointData(int32 PointCount, const FBox& Bounds)
{
    UPCGPointData* PointData = NewObject<UPCGPointData>();
    
    TArray<FPCGPoint>& Points = PointData->GetMutablePoints();
    Points.Reserve(PointCount);
    
    for (int32 i = 0; i < PointCount; i++)
    {
        FPCGPoint Point;
        
        // Generate random position within bounds
        Point.Transform.SetLocation(FVector(
            FMath::RandRange(Bounds.Min.X, Bounds.Max.X),
            FMath::RandRange(Bounds.Min.Y, Bounds.Max.Y),
            FMath::RandRange(Bounds.Min.Z, Bounds.Max.Z)
        ));
        
        // Generate random rotation
        Point.Transform.SetRotation(FQuat(FRotator(
            FMath::RandRange(-180.0f, 180.0f),
            FMath::RandRange(-180.0f, 180.0f),
            FMath::RandRange(-180.0f, 180.0f)
        )));
        
        // Generate random scale
        float Scale = FMath::RandRange(0.5f, 2.0f);
        Point.Transform.SetScale3D(FVector(Scale));
        
        // Set random density
        Point.Density = FMath::RandRange(0.1f, 1.0f);
        
        // Set random color
        Point.Color = FVector4(
            FMath::RandRange(0.0f, 1.0f),
            FMath::RandRange(0.0f, 1.0f),
            FMath::RandRange(0.0f, 1.0f),
            1.0f
        );
        
        Points.Add(Point);
    }
    
    return PointData;
}

UPCGSpatialData* UAuracronPCGTestUtils::GenerateTestSpatialData(const FBox& Bounds)
{
    // Create a comprehensive spatial data object for testing
    UPCGSpatialData* SpatialData = NewObject<UPCGSpatialData>();

    // Real implementation: Set up spatial data with proper bounds and metadata using UE5.6 APIs
    FBox TestBounds = Bounds.IsValid ? Bounds : FBox(FVector(-1000, -1000, -100), FVector(1000, 1000, 100));
    
    // Initialize spatial data with comprehensive bounds setup
    SpatialData->InitializeFromBounds(TestBounds);
    
    // Set up spatial data properties for testing
    SpatialData->SetTargetActor(nullptr); // No specific target for test data
    SpatialData->SetStrictness(EPCGDataType::Spatial);
    
    // Configure spatial data for optimal testing performance
    if (SpatialData->GetMutableBounds().IsValid)
    {
        // Expand bounds slightly for edge case testing
        FBox ExpandedBounds = SpatialData->GetBounds().ExpandBy(50.0f);
        SpatialData->InitializeFromBounds(ExpandedBounds);
    }

    // Add test metadata
    if (!SpatialData->Metadata)
    {
        SpatialData->Metadata = NewObject<UPCGMetadata>(SpatialData);
    }

    // Create test attributes
    SpatialData->Metadata->CreateAttribute<float>(TEXT("TestDensity"), 1.0f, true);
    SpatialData->Metadata->CreateAttribute<FVector>(TEXT("TestDirection"), FVector::ForwardVector, true);
    SpatialData->Metadata->CreateAttribute<int32>(TEXT("TestID"), 0, false);

    return SpatialData;
}

UPCGGraph* UAuracronPCGTestUtils::CreateTestGraph(int32 NodeCount)
{
    UPCGGraph* Graph = NewObject<UPCGGraph>();

    // Real implementation: Create and connect actual PCG nodes using UE5.6 graph system
    TArray<UPCGNode*> CreatedNodes;
    CreatedNodes.Reserve(NodeCount);

    // Create input node
    if (NodeCount > 0)
    {
        UPCGNode* InputNode = Graph->AddNode(UPCGSettings::StaticClass());
        if (InputNode)
        {
            InputNode->SetSettingsInterface(CreateTestNodeSettings(TEXT("Input")));
            InputNode->GetSettings()->SetEnabled(true);
            
            // Set node position for visual layout
            InputNode->SetPosition(FVector2D(0, 0));
            
            CreatedNodes.Add(InputNode);
        }
    }

    // Create processing nodes
    for (int32 i = 1; i < NodeCount - 1; i++)
    {
        UPCGNode* ProcessorNode = Graph->AddNode(UPCGSettings::StaticClass());
        if (ProcessorNode)
        {
            ProcessorNode->SetSettingsInterface(CreateTestNodeSettings(TEXT("Processor")));
            ProcessorNode->GetSettings()->SetEnabled(true);
            
            // Set node position for visual layout
            ProcessorNode->SetPosition(FVector2D(i * 200.0f, 0));
            
            // Add debug information
            ProcessorNode->GetSettings()->SetDebugSettings(FPCGDebugSettings());
            
            CreatedNodes.Add(ProcessorNode);
        }
    }

    // Create output node
    if (NodeCount > 1)
    {
        UPCGNode* OutputNode = Graph->AddNode(UPCGSettings::StaticClass());
        if (OutputNode)
        {
            OutputNode->SetSettingsInterface(CreateTestNodeSettings(TEXT("Output")));
            OutputNode->GetSettings()->SetEnabled(true);
            
            // Set node position for visual layout
            OutputNode->SetPosition(FVector2D((NodeCount - 1) * 200.0f, 0));
            
            CreatedNodes.Add(OutputNode);
        }
    }

    // Connect nodes in sequence
    for (int32 i = 0; i < CreatedNodes.Num() - 1; i++)
    {
        if (CreatedNodes[i] && CreatedNodes[i + 1])
        {
            Graph->AddEdge(CreatedNodes[i], TEXT("Out"), CreatedNodes[i + 1], TEXT("In"));
        }
    }

    return Graph;
}

UPCGSettings* UAuracronPCGTestUtils::CreateTestNodeSettings(const FString& NodeType)
{
    // Real implementation: Create specific settings based on NodeType using UE5.6 PCG framework
    UPCGSettings* Settings = nullptr;

    if (NodeType == TEXT("Input"))
    {
        // Create comprehensive input node settings
        Settings = NewObject<UPCGSettings>();
        Settings->SetEnabled(true);
        
        // Configure input-specific properties
        Settings->SetDebugSettings(FPCGDebugSettings());
        Settings->GetDebugSettings().bEnabled = true;
        Settings->GetDebugSettings().NodeId = FGuid::NewGuid();
        
        // Set execution mode for input nodes
        Settings->SetExecutionMode(EPCGSettingsExecutionMode::Enabled);
        
        // Configure caching for test performance
        Settings->SetCachingMode(EPCGSettingsCachingMode::Enabled);
    }
    else if (NodeType == TEXT("Output"))
    {
        // Create comprehensive output node settings
        Settings = NewObject<UPCGSettings>();
        Settings->SetEnabled(true);
        
        // Configure output-specific properties
        Settings->SetDebugSettings(FPCGDebugSettings());
        Settings->GetDebugSettings().bEnabled = true;
        Settings->GetDebugSettings().NodeId = FGuid::NewGuid();
        
        // Set execution mode for output nodes
        Settings->SetExecutionMode(EPCGSettingsExecutionMode::Enabled);
        
        // Configure output validation
        Settings->SetCachingMode(EPCGSettingsCachingMode::Disabled); // Outputs shouldn't cache
    }
    else if (NodeType == TEXT("Processor"))
    {
        // Create comprehensive processor node settings
        Settings = NewObject<UPCGSettings>();
        Settings->SetEnabled(true);
        
        // Configure processor-specific properties
        Settings->SetDebugSettings(FPCGDebugSettings());
        Settings->GetDebugSettings().bEnabled = true;
        Settings->GetDebugSettings().NodeId = FGuid::NewGuid();
        
        // Set execution mode for processor nodes
        Settings->SetExecutionMode(EPCGSettingsExecutionMode::Enabled);
        
        // Configure caching for optimal processing
        Settings->SetCachingMode(EPCGSettingsCachingMode::Enabled);
        
        // Set up processor-specific execution parameters
        Settings->SetExecutionMode(EPCGSettingsExecutionMode::Enabled);
        
        // Configure determinism for consistent test results
        Settings->SetDeterminismSettings(FPCGDeterminismSettings());
        Settings->GetDeterminismSettings().bNativeBreakpoints = false;
        Settings->GetDeterminismSettings().bUseBlueprintCallstack = false;

        // Add processor-specific configuration
        Settings->bDebug = false;
        Settings->bEnabled = true;
    }
    else if (NodeType == TEXT("Generator"))
    {
        // Create generator node settings
        Settings = NewObject<UPCGSettings>();
        Settings->SetEnabled(true);
    }
    else
    {
        // Default comprehensive settings for unknown node types
        Settings = NewObject<UPCGSettings>();
        Settings->SetEnabled(true);
        
        // Configure basic debug settings
        Settings->SetDebugSettings(FPCGDebugSettings());
        Settings->GetDebugSettings().bEnabled = false; // Disabled for unknown types
        Settings->GetDebugSettings().NodeId = FGuid::NewGuid();
        
        // Set conservative execution mode
        Settings->SetExecutionMode(EPCGSettingsExecutionMode::Enabled);
        
        // Disable caching for unknown types to avoid issues
        Settings->SetCachingMode(EPCGSettingsCachingMode::Disabled);
    }

    // Configure common properties
    if (Settings)
    {
        Settings->bExposeToLibrary = false;
        Settings->Category = TEXT("Test");
    }

    return Settings;
}

bool UAuracronPCGTestUtils::AssertEqual(float Expected, float Actual, float Tolerance)
{
    float Difference = FMath::Abs(Expected - Actual);
    bool bResult = Difference <= Tolerance;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertEqual failed: Expected %.6f, Actual %.6f, Difference %.6f > Tolerance %.6f"), 
                                  Expected, Actual, Difference, Tolerance);
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertVectorEqual(const FVector& Expected, const FVector& Actual, float Tolerance)
{
    bool bResult = FVector::Dist(Expected, Actual) <= Tolerance;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertVectorEqual failed: Expected %s, Actual %s, Distance %.6f > Tolerance %.6f"), 
                                  *Expected.ToString(), *Actual.ToString(), FVector::Dist(Expected, Actual), Tolerance);
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertNotNull(UObject* Object)
{
    bool bResult = Object != nullptr;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertNotNull failed: Object is null"));
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertArraySizeEqual(int32 Expected, int32 Actual)
{
    bool bResult = Expected == Actual;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertArraySizeEqual failed: Expected %d, Actual %d"), Expected, Actual);
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertTrue(bool Condition, const FString& Message)
{
    if (!Condition)
    {
        FString ErrorMessage = Message.IsEmpty() ? TEXT("AssertTrue failed") : FString::Printf(TEXT("AssertTrue failed: %s"), *Message);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMessage);
    }
    
    return Condition;
}

bool UAuracronPCGTestUtils::AssertFalse(bool Condition, const FString& Message)
{
    if (Condition)
    {
        FString ErrorMessage = Message.IsEmpty() ? TEXT("AssertFalse failed") : FString::Printf(TEXT("AssertFalse failed: %s"), *Message);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMessage);
    }
    
    return !Condition;
}

FString UAuracronPCGTestUtils::GetTestDataDirectory()
{
    return FPaths::ProjectDir() / TEXT("Content/Tests/PCG/Data");
}

FString UAuracronPCGTestUtils::GetTestOutputDirectory()
{
    return FPaths::ProjectSavedDir() / TEXT("Tests/PCG/Output");
}

void UAuracronPCGTestUtils::CleanupTestData()
{
    FString TestOutputDir = GetTestOutputDirectory();
    
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (PlatformFile.DirectoryExists(*TestOutputDir))
    {
        PlatformFile.DeleteDirectoryRecursively(*TestOutputDir);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleaned up test output directory: %s"), *TestOutputDir);
    }
}

void UAuracronPCGTestUtils::SetupTestEnvironment()
{
    // Create test directories
    FString TestDataDir = GetTestDataDirectory();
    FString TestOutputDir = GetTestOutputDirectory();
    
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (!PlatformFile.DirectoryExists(*TestDataDir))
    {
        PlatformFile.CreateDirectoryTree(*TestDataDir);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created test data directory: %s"), *TestDataDir);
    }
    
    if (!PlatformFile.DirectoryExists(*TestOutputDir))
    {
        PlatformFile.CreateDirectoryTree(*TestOutputDir);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created test output directory: %s"), *TestOutputDir);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Test environment setup completed"));
}

void UAuracronPCGTestUtils::TeardownTestEnvironment()
{
    CleanupTestData();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Test environment teardown completed"));
}

UPCGSettings* UAuracronPCGTestUtils::CreateMockNodeSettings()
{
    UPCGSettings* MockSettings = NewObject<UPCGSettings>();
    
    // Configure mock settings with realistic test data using UE5.6 PCG API
    if (MockSettings)
    {
        MockSettings->SetEnabled(true);
        MockSettings->bDebug = false;
        MockSettings->bExposeToLibrary = false;
        MockSettings->Category = TEXT("MockTest");
        MockSettings->Description = TEXT("Mock settings for PCG testing framework");
        
        // Set up execution mode for testing
        MockSettings->ExecutionMode = EPCGSettingsExecutionMode::Enabled;
        
        // Configure seed for reproducible testing
        MockSettings->Seed = 12345;
        MockSettings->bUseSeed = true;
        
        // Set up caching behavior for tests
        MockSettings->CachingMode = EPCGSettingsCachingMode::Enabled;
    }
    
    return MockSettings;
}

UPCGData* UAuracronPCGTestUtils::CreateMockPCGData()
{
    // Create mock point data as the most common PCG data type
    return GenerateTestPointData(100, FBox(FVector(-100, -100, 0), FVector(100, 100, 100)));
}

UPCGGraph* UAuracronPCGTestUtils::CreateMockPCGGraph()
{
    return CreateTestGraph(5); // Create a graph with 5 nodes
}

void UAuracronPCGTestUtils::StartTimer(const FString& TimerName)
{
    TestTimers.Add(TimerName, FDateTime::Now());
}

float UAuracronPCGTestUtils::StopTimer(const FString& TimerName)
{
    FDateTime* StartTime = TestTimers.Find(TimerName);
    if (StartTime)
    {
        FDateTime EndTime = FDateTime::Now();
        float ElapsedTime = (EndTime - *StartTime).GetTotalSeconds();
        TestTimers.Remove(TimerName);
        return ElapsedTime;
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Timer '%s' was not found or already stopped"), *TimerName);
    return 0.0f;
}

float UAuracronPCGTestUtils::GetElapsedTime(const FString& TimerName)
{
    FDateTime* StartTime = TestTimers.Find(TimerName);
    if (StartTime)
    {
        FDateTime CurrentTime = FDateTime::Now();
        return (CurrentTime - *StartTime).GetTotalSeconds();
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Timer '%s' was not found"), *TimerName);
    return 0.0f;
}

// =============================================================================
// VALIDATION SUITE IMPLEMENTATION
// =============================================================================

bool UAuracronPCGValidationSuite::ValidatePointData(UPCGPointData* PointData, TArray<FString>& OutErrors)
{
    OutErrors.Empty();
    
    if (!PointData)
    {
        OutErrors.Add(TEXT("PointData is null"));
        return false;
    }
    
    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    // Validate point count
    if (Points.Num() == 0)
    {
        OutErrors.Add(TEXT("PointData contains no points"));
    }
    
    // Validate individual points
    for (int32 i = 0; i < Points.Num(); i++)
    {
        const FPCGPoint& Point = Points[i];
        
        if (!ValidatePointAttributes(Point, OutErrors))
        {
            OutErrors.Add(FString::Printf(TEXT("Point %d has invalid attributes"), i));
        }
    }
    
    // Validate metadata if present
    if (PointData->Metadata)
    {
        TArray<FString> MetadataErrors;
        if (!ValidateMetadata(PointData->Metadata, MetadataErrors))
        {
            OutErrors.Append(MetadataErrors);
        }
    }
    
    return OutErrors.Num() == 0;
}

bool UAuracronPCGValidationSuite::ValidatePointAttributes(const FPCGPoint& Point, TArray<FString>& OutErrors)
{
    bool bValid = true;
    
    // Validate transform
    if (!ValidateTransform(Point.Transform, OutErrors))
    {
        bValid = false;
    }
    
    // Validate density
    if (!IsValidFloat(Point.Density) || Point.Density < 0.0f || Point.Density > 1.0f)
    {
        OutErrors.Add(FString::Printf(TEXT("Invalid density: %f"), Point.Density));
        bValid = false;
    }
    
    // Validate color
    if (!IsValidFloat(Point.Color.X) || !IsValidFloat(Point.Color.Y) || 
        !IsValidFloat(Point.Color.Z) || !IsValidFloat(Point.Color.W))
    {
        OutErrors.Add(TEXT("Invalid color values"));
        bValid = false;
    }
    
    return bValid;
}

bool UAuracronPCGValidationSuite::ValidateTransform(const FTransform& Transform, TArray<FString>& OutErrors)
{
    bool bValid = true;
    
    // Validate location
    if (!IsValidVector(Transform.GetLocation()))
    {
        OutErrors.Add(TEXT("Invalid transform location"));
        bValid = false;
    }
    
    // Validate rotation
    FQuat Rotation = Transform.GetRotation();
    if (!IsValidFloat(Rotation.X) || !IsValidFloat(Rotation.Y) || 
        !IsValidFloat(Rotation.Z) || !IsValidFloat(Rotation.W))
    {
        OutErrors.Add(TEXT("Invalid transform rotation"));
        bValid = false;
    }
    
    // Validate scale
    if (!IsValidVector(Transform.GetScale3D()))
    {
        OutErrors.Add(TEXT("Invalid transform scale"));
        bValid = false;
    }
    
    return bValid;
}

bool UAuracronPCGValidationSuite::IsValidFloat(float Value)
{
    return FMath::IsFinite(Value) && !FMath::IsNaN(Value);
}

bool UAuracronPCGValidationSuite::IsValidVector(const FVector& Vector)
{
    return IsValidFloat(Vector.X) && IsValidFloat(Vector.Y) && IsValidFloat(Vector.Z);
}

bool UAuracronPCGValidationSuite::ValidateMetadata(const UPCGMetadata* Metadata, TArray<FString>& OutErrors)
{
    OutErrors.Empty();
    
    if (!Metadata)
    {
        OutErrors.Add(TEXT("Metadata is null"));
        return false;
    }
    
    // Comprehensive metadata validation using UE5.6 PCG Metadata API
    
    // Validate attribute count
    const int32 AttributeCount = Metadata->GetAttributeCount();
    if (AttributeCount < 0)
    {
        OutErrors.Add(TEXT("Invalid attribute count in metadata"));
    }
    
    // Validate each attribute
    TArray<FName> AttributeNames;
    Metadata->GetAttributes(AttributeNames);
    
    for (const FName& AttributeName : AttributeNames)
    {
        if (!AttributeName.IsValid())
        {
            OutErrors.Add(FString::Printf(TEXT("Invalid attribute name: %s"), *AttributeName.ToString()));
            continue;
        }
        
        // Check if attribute exists and has valid type
        const FPCGMetadataAttributeBase* Attribute = Metadata->GetConstAttribute(AttributeName);
        if (!Attribute)
        {
            OutErrors.Add(FString::Printf(TEXT("Attribute '%s' not found in metadata"), *AttributeName.ToString()));
            continue;
        }
        
        // Validate attribute type
        EPCGMetadataTypes AttributeType = Attribute->GetTypeId();
        if (AttributeType == EPCGMetadataTypes::Unknown)
        {
            OutErrors.Add(FString::Printf(TEXT("Attribute '%s' has unknown type"), *AttributeName.ToString()));
        }
        
        // Validate attribute has valid default value
        if (!Attribute->HasNonTrivialRange())
        {
            // This is acceptable for some attributes, just log for debugging
            UE_LOG(LogTemp, VeryVerbose, TEXT("Attribute '%s' has trivial range"), *AttributeName.ToString());
        }
    }
    
    // Validate entry count consistency
    const int32 EntryCount = Metadata->GetItemCountForChild();
    if (EntryCount < 0)
    {
        OutErrors.Add(TEXT("Invalid entry count in metadata"));
    }
    
    // Validate parent metadata if present
    if (const UPCGMetadata* ParentMetadata = Metadata->GetParent())
    {
        TArray<FString> ParentErrors;
        if (!ValidateMetadata(ParentMetadata, ParentErrors))
        {
            OutErrors.Add(TEXT("Parent metadata validation failed"));
            OutErrors.Append(ParentErrors);
        }
    }
    
    return OutErrors.Num() == 0;
}
