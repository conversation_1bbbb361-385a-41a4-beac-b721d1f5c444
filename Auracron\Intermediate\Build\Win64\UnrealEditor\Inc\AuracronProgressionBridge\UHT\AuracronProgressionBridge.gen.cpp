// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronProgressionBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronProgressionBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPROGRESSIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronProgressionBridge();
AURACRONPROGRESSIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronProgressionBridge_NoRegister();
AURACRONPROGRESSIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType();
AURACRONPROGRESSIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity();
AURACRONPROGRESSIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType();
AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature();
AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature();
AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature();
AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature();
AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature();
AURACRONPROGRESSIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAccountProgression();
AURACRONPROGRESSIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionMastery();
AURACRONPROGRESSIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry();
AURACRONPROGRESSIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronProgressionMilestone();
AURACRONPROGRESSIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmMastery();
AURACRONPROGRESSIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry();
AURACRONPROGRESSIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronReward();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UPackage* Z_Construct_UPackage__Script_AuracronProgressionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronProgressionType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronProgressionType;
static UEnum* EAuracronProgressionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronProgressionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronProgressionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("EAuracronProgressionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronProgressionType.OuterSingleton;
}
template<> AURACRONPROGRESSIONBRIDGE_API UEnum* StaticEnum<EAuracronProgressionType>()
{
	return EAuracronProgressionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AccountLevel.DisplayName", "Account Level" },
		{ "AccountLevel.Name", "EAuracronProgressionType::AccountLevel" },
		{ "Achievement.DisplayName", "Achievement" },
		{ "Achievement.Name", "EAuracronProgressionType::Achievement" },
		{ "BattlePass.DisplayName", "Battle Pass" },
		{ "BattlePass.Name", "EAuracronProgressionType::BattlePass" },
		{ "BlueprintType", "true" },
		{ "ChampionMastery.DisplayName", "Champion Mastery" },
		{ "ChampionMastery.Name", "EAuracronProgressionType::ChampionMastery" },
		{ "Collection.DisplayName", "Collection" },
		{ "Collection.Name", "EAuracronProgressionType::Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de progress\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "Milestone.DisplayName", "Milestone" },
		{ "Milestone.Name", "EAuracronProgressionType::Milestone" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronProgressionType::None" },
		{ "RealmMastery.DisplayName", "Realm Mastery" },
		{ "RealmMastery.Name", "EAuracronProgressionType::RealmMastery" },
		{ "SeasonalRank.DisplayName", "Seasonal Rank" },
		{ "SeasonalRank.Name", "EAuracronProgressionType::SeasonalRank" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de progress\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronProgressionType::None", (int64)EAuracronProgressionType::None },
		{ "EAuracronProgressionType::AccountLevel", (int64)EAuracronProgressionType::AccountLevel },
		{ "EAuracronProgressionType::ChampionMastery", (int64)EAuracronProgressionType::ChampionMastery },
		{ "EAuracronProgressionType::RealmMastery", (int64)EAuracronProgressionType::RealmMastery },
		{ "EAuracronProgressionType::SeasonalRank", (int64)EAuracronProgressionType::SeasonalRank },
		{ "EAuracronProgressionType::BattlePass", (int64)EAuracronProgressionType::BattlePass },
		{ "EAuracronProgressionType::Achievement", (int64)EAuracronProgressionType::Achievement },
		{ "EAuracronProgressionType::Collection", (int64)EAuracronProgressionType::Collection },
		{ "EAuracronProgressionType::Milestone", (int64)EAuracronProgressionType::Milestone },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	"EAuracronProgressionType",
	"EAuracronProgressionType",
	Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronProgressionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronProgressionType.InnerSingleton, Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronProgressionType.InnerSingleton;
}
// ********** End Enum EAuracronProgressionType ****************************************************

// ********** Begin Enum EAuracronRewardType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronRewardType;
static UEnum* EAuracronRewardType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronRewardType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronRewardType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("EAuracronRewardType"));
	}
	return Z_Registration_Info_UEnum_EAuracronRewardType.OuterSingleton;
}
template<> AURACRONPROGRESSIONBRIDGE_API UEnum* StaticEnum<EAuracronRewardType>()
{
	return EAuracronRewardType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Boost.DisplayName", "Boost" },
		{ "Boost.Name", "EAuracronRewardType::Boost" },
		{ "Border.DisplayName", "Border" },
		{ "Border.Name", "EAuracronRewardType::Border" },
		{ "Champion.DisplayName", "Champion" },
		{ "Champion.Name", "EAuracronRewardType::Champion" },
		{ "Chest.DisplayName", "Chest" },
		{ "Chest.Name", "EAuracronRewardType::Chest" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de recompensa\n */" },
#endif
		{ "Emote.DisplayName", "Emote" },
		{ "Emote.Name", "EAuracronRewardType::Emote" },
		{ "Essence.DisplayName", "Essence" },
		{ "Essence.Name", "EAuracronRewardType::Essence" },
		{ "Experience.DisplayName", "Experience" },
		{ "Experience.Name", "EAuracronRewardType::Experience" },
		{ "Gold.DisplayName", "Gold" },
		{ "Gold.Name", "EAuracronRewardType::Gold" },
		{ "Icon.DisplayName", "Icon" },
		{ "Icon.Name", "EAuracronRewardType::Icon" },
		{ "Key.DisplayName", "Key" },
		{ "Key.Name", "EAuracronRewardType::Key" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronRewardType::None" },
		{ "PremiumCurrency.DisplayName", "Premium Currency" },
		{ "PremiumCurrency.Name", "EAuracronRewardType::PremiumCurrency" },
		{ "Recall.DisplayName", "Recall" },
		{ "Recall.Name", "EAuracronRewardType::Recall" },
		{ "Sigilo.DisplayName", "Sigilo" },
		{ "Sigilo.Name", "EAuracronRewardType::Sigilo" },
		{ "Skin.DisplayName", "Skin" },
		{ "Skin.Name", "EAuracronRewardType::Skin" },
		{ "Title.DisplayName", "Title" },
		{ "Title.Name", "EAuracronRewardType::Title" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de recompensa" },
#endif
		{ "Ward.DisplayName", "Ward" },
		{ "Ward.Name", "EAuracronRewardType::Ward" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronRewardType::None", (int64)EAuracronRewardType::None },
		{ "EAuracronRewardType::Experience", (int64)EAuracronRewardType::Experience },
		{ "EAuracronRewardType::Gold", (int64)EAuracronRewardType::Gold },
		{ "EAuracronRewardType::PremiumCurrency", (int64)EAuracronRewardType::PremiumCurrency },
		{ "EAuracronRewardType::Champion", (int64)EAuracronRewardType::Champion },
		{ "EAuracronRewardType::Skin", (int64)EAuracronRewardType::Skin },
		{ "EAuracronRewardType::Emote", (int64)EAuracronRewardType::Emote },
		{ "EAuracronRewardType::Icon", (int64)EAuracronRewardType::Icon },
		{ "EAuracronRewardType::Border", (int64)EAuracronRewardType::Border },
		{ "EAuracronRewardType::Title", (int64)EAuracronRewardType::Title },
		{ "EAuracronRewardType::Ward", (int64)EAuracronRewardType::Ward },
		{ "EAuracronRewardType::Recall", (int64)EAuracronRewardType::Recall },
		{ "EAuracronRewardType::Sigilo", (int64)EAuracronRewardType::Sigilo },
		{ "EAuracronRewardType::Boost", (int64)EAuracronRewardType::Boost },
		{ "EAuracronRewardType::Chest", (int64)EAuracronRewardType::Chest },
		{ "EAuracronRewardType::Key", (int64)EAuracronRewardType::Key },
		{ "EAuracronRewardType::Essence", (int64)EAuracronRewardType::Essence },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	"EAuracronRewardType",
	"EAuracronRewardType",
	Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType()
{
	if (!Z_Registration_Info_UEnum_EAuracronRewardType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronRewardType.InnerSingleton, Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronRewardType.InnerSingleton;
}
// ********** End Enum EAuracronRewardType *********************************************************

// ********** Begin Enum EAuracronRewardRarity *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronRewardRarity;
static UEnum* EAuracronRewardRarity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronRewardRarity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronRewardRarity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("EAuracronRewardRarity"));
	}
	return Z_Registration_Info_UEnum_EAuracronRewardRarity.OuterSingleton;
}
template<> AURACRONPROGRESSIONBRIDGE_API UEnum* StaticEnum<EAuracronRewardRarity>()
{
	return EAuracronRewardRarity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de recompensas\n */" },
#endif
		{ "Common.DisplayName", "Common" },
		{ "Common.Name", "EAuracronRewardRarity::Common" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronRewardRarity::Epic" },
		{ "Legendary.DisplayName", "Legendary" },
		{ "Legendary.Name", "EAuracronRewardRarity::Legendary" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
		{ "Mythic.DisplayName", "Mythic" },
		{ "Mythic.Name", "EAuracronRewardRarity::Mythic" },
		{ "Rare.DisplayName", "Rare" },
		{ "Rare.Name", "EAuracronRewardRarity::Rare" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de recompensas" },
#endif
		{ "Ultimate.DisplayName", "Ultimate" },
		{ "Ultimate.Name", "EAuracronRewardRarity::Ultimate" },
		{ "Uncommon.DisplayName", "Uncommon" },
		{ "Uncommon.Name", "EAuracronRewardRarity::Uncommon" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronRewardRarity::Common", (int64)EAuracronRewardRarity::Common },
		{ "EAuracronRewardRarity::Uncommon", (int64)EAuracronRewardRarity::Uncommon },
		{ "EAuracronRewardRarity::Rare", (int64)EAuracronRewardRarity::Rare },
		{ "EAuracronRewardRarity::Epic", (int64)EAuracronRewardRarity::Epic },
		{ "EAuracronRewardRarity::Legendary", (int64)EAuracronRewardRarity::Legendary },
		{ "EAuracronRewardRarity::Mythic", (int64)EAuracronRewardRarity::Mythic },
		{ "EAuracronRewardRarity::Ultimate", (int64)EAuracronRewardRarity::Ultimate },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	"EAuracronRewardRarity",
	"EAuracronRewardRarity",
	Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity()
{
	if (!Z_Registration_Info_UEnum_EAuracronRewardRarity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronRewardRarity.InnerSingleton, Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronRewardRarity.InnerSingleton;
}
// ********** End Enum EAuracronRewardRarity *******************************************************

// ********** Begin ScriptStruct FAuracronReward ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronReward;
class UScriptStruct* FAuracronReward::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronReward.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronReward.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronReward, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("AuracronReward"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronReward.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronReward_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para recompensa\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico da recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardName_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardDescription_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardType_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo da recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardRarity_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade da recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quantity_MetaData[] = {
		{ "Category", "Reward" },
		{ "ClampMax", "1000000" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quantidade da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quantidade da recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardIcon_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8d""cone da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8d""cone da recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PremiumValue_MetaData[] = {
		{ "Category", "Reward" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor em moeda premium */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor em moeda premium" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldValue_MetaData[] = {
		{ "Category", "Reward" },
		{ "ClampMax", "100000" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor em gold */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor em gold" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPremiumReward_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\xb0 recompensa premium */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\xb0 recompensa premium" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresBattlePass_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer Battle Pass */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer Battle Pass" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpirationDate_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de expira\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de expira\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasExpirationDate_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tem data de expira\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tem data de expira\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardTags_MetaData[] = {
		{ "Category", "Reward" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags da recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags da recompensa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_RewardName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_RewardDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RewardType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RewardType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RewardRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RewardRarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RewardIcon;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PremiumValue;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldValue;
	static void NewProp_bIsPremiumReward_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPremiumReward;
	static void NewProp_bRequiresBattlePass_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresBattlePass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExpirationDate;
	static void NewProp_bHasExpirationDate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasExpirationDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RewardTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronReward>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardName = { "RewardName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, RewardName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardName_MetaData), NewProp_RewardName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardDescription = { "RewardDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, RewardDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardDescription_MetaData), NewProp_RewardDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardType = { "RewardType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, RewardType), Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardType_MetaData), NewProp_RewardType_MetaData) }; // 2016650149
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardRarity = { "RewardRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, RewardRarity), Z_Construct_UEnum_AuracronProgressionBridge_EAuracronRewardRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardRarity_MetaData), NewProp_RewardRarity_MetaData) }; // 3886300181
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, Quantity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quantity_MetaData), NewProp_Quantity_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardIcon = { "RewardIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, RewardIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardIcon_MetaData), NewProp_RewardIcon_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_PremiumValue = { "PremiumValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, PremiumValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PremiumValue_MetaData), NewProp_PremiumValue_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_GoldValue = { "GoldValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, GoldValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldValue_MetaData), NewProp_GoldValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bIsPremiumReward_SetBit(void* Obj)
{
	((FAuracronReward*)Obj)->bIsPremiumReward = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bIsPremiumReward = { "bIsPremiumReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReward), &Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bIsPremiumReward_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPremiumReward_MetaData), NewProp_bIsPremiumReward_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bRequiresBattlePass_SetBit(void* Obj)
{
	((FAuracronReward*)Obj)->bRequiresBattlePass = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bRequiresBattlePass = { "bRequiresBattlePass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReward), &Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bRequiresBattlePass_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresBattlePass_MetaData), NewProp_bRequiresBattlePass_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_ExpirationDate = { "ExpirationDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, ExpirationDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpirationDate_MetaData), NewProp_ExpirationDate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bHasExpirationDate_SetBit(void* Obj)
{
	((FAuracronReward*)Obj)->bHasExpirationDate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bHasExpirationDate = { "bHasExpirationDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReward), &Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bHasExpirationDate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasExpirationDate_MetaData), NewProp_bHasExpirationDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardTags = { "RewardTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReward, RewardTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardTags_MetaData), NewProp_RewardTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_PremiumValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_GoldValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bIsPremiumReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bRequiresBattlePass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_ExpirationDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_bHasExpirationDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReward_Statics::NewProp_RewardTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronReward_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	&NewStructOps,
	"AuracronReward",
	Z_Construct_UScriptStruct_FAuracronReward_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronReward_Statics::PropPointers),
	sizeof(FAuracronReward),
	alignof(FAuracronReward),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronReward_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronReward_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronReward()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronReward.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronReward.InnerSingleton, Z_Construct_UScriptStruct_FAuracronReward_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronReward.InnerSingleton;
}
// ********** End ScriptStruct FAuracronReward *****************************************************

// ********** Begin ScriptStruct FAuracronAccountProgression ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAccountProgression;
class UScriptStruct* FAuracronAccountProgression::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAccountProgression.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAccountProgression.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAccountProgression, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("AuracronAccountProgression"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAccountProgression.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para progress\xc3\x83\xc2\xa3o de conta\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para progress\xc3\x83\xc2\xa3o de conta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountLevel_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMax", "500" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel da conta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel da conta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentExperience_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalExperience_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia total acumulada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia total acumulada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceToNextLevel_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentGold_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gold atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gold atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PremiumCurrency_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Moeda premium atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Moeda premium atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlueEssence_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ess\xc3\x83\xc2\xaancia azul atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ess\xc3\x83\xc2\xaancia azul atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OrangeEssence_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ess\xc3\x83\xc2\xaancia laranja atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ess\xc3\x83\xc2\xaancia laranja atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchesPlayed_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Partidas jogadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Partidas jogadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchesWon_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Partidas vencidas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Partidas vencidas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WinRate_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Taxa de vit\xc3\x83\xc2\xb3ria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Taxa de vit\xc3\x83\xc2\xb3ria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalPlayTimeMinutes_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo total jogado (em minutos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo total jogado (em minutos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountCreationDate_MetaData[] = {
		{ "Category", "Account Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de cria\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da conta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de cria\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da conta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastOnlineDate_MetaData[] = {
		{ "Category", "Account Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltima vez online */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltima vez online" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWinStreak_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Streak de vit\xc3\x83\xc2\xb3rias atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streak de vit\xc3\x83\xc2\xb3rias atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BestWinStreak_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maior streak de vit\xc3\x83\xc2\xb3rias */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maior streak de vit\xc3\x83\xc2\xb3rias" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HonorLevel_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMax", "5" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Honra atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Honra atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HonorPoints_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de honra */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de honra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasActiveBattlePass_MetaData[] = {
		{ "Category", "Account Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tem Battle Pass ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tem Battle Pass ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BattlePassLevel_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMax", "100" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel do Battle Pass */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel do Battle Pass" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BattlePassXP_MetaData[] = {
		{ "Category", "Account Progression" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** XP do Battle Pass */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "XP do Battle Pass" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_AccountLevel;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_CurrentExperience;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_TotalExperience;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_ExperienceToNextLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentGold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PremiumCurrency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BlueEssence;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OrangeEssence;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MatchesPlayed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MatchesWon;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WinRate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalPlayTimeMinutes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AccountCreationDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastOnlineDate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentWinStreak;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BestWinStreak;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HonorLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HonorPoints;
	static void NewProp_bHasActiveBattlePass_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasActiveBattlePass;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BattlePassLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BattlePassXP;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAccountProgression>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_AccountLevel = { "AccountLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, AccountLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountLevel_MetaData), NewProp_AccountLevel_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_CurrentExperience = { "CurrentExperience", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, CurrentExperience), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentExperience_MetaData), NewProp_CurrentExperience_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_TotalExperience = { "TotalExperience", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, TotalExperience), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalExperience_MetaData), NewProp_TotalExperience_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_ExperienceToNextLevel = { "ExperienceToNextLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, ExperienceToNextLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceToNextLevel_MetaData), NewProp_ExperienceToNextLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_CurrentGold = { "CurrentGold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, CurrentGold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentGold_MetaData), NewProp_CurrentGold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_PremiumCurrency = { "PremiumCurrency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, PremiumCurrency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PremiumCurrency_MetaData), NewProp_PremiumCurrency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BlueEssence = { "BlueEssence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, BlueEssence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlueEssence_MetaData), NewProp_BlueEssence_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_OrangeEssence = { "OrangeEssence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, OrangeEssence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OrangeEssence_MetaData), NewProp_OrangeEssence_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_MatchesPlayed = { "MatchesPlayed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, MatchesPlayed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchesPlayed_MetaData), NewProp_MatchesPlayed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_MatchesWon = { "MatchesWon", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, MatchesWon), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchesWon_MetaData), NewProp_MatchesWon_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_WinRate = { "WinRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, WinRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WinRate_MetaData), NewProp_WinRate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_TotalPlayTimeMinutes = { "TotalPlayTimeMinutes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, TotalPlayTimeMinutes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalPlayTimeMinutes_MetaData), NewProp_TotalPlayTimeMinutes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_AccountCreationDate = { "AccountCreationDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, AccountCreationDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountCreationDate_MetaData), NewProp_AccountCreationDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_LastOnlineDate = { "LastOnlineDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, LastOnlineDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastOnlineDate_MetaData), NewProp_LastOnlineDate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_CurrentWinStreak = { "CurrentWinStreak", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, CurrentWinStreak), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWinStreak_MetaData), NewProp_CurrentWinStreak_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BestWinStreak = { "BestWinStreak", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, BestWinStreak), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BestWinStreak_MetaData), NewProp_BestWinStreak_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_HonorLevel = { "HonorLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, HonorLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HonorLevel_MetaData), NewProp_HonorLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_HonorPoints = { "HonorPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, HonorPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HonorPoints_MetaData), NewProp_HonorPoints_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_bHasActiveBattlePass_SetBit(void* Obj)
{
	((FAuracronAccountProgression*)Obj)->bHasActiveBattlePass = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_bHasActiveBattlePass = { "bHasActiveBattlePass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAccountProgression), &Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_bHasActiveBattlePass_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasActiveBattlePass_MetaData), NewProp_bHasActiveBattlePass_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BattlePassLevel = { "BattlePassLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, BattlePassLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BattlePassLevel_MetaData), NewProp_BattlePassLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BattlePassXP = { "BattlePassXP", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAccountProgression, BattlePassXP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BattlePassXP_MetaData), NewProp_BattlePassXP_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_AccountLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_CurrentExperience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_TotalExperience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_ExperienceToNextLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_CurrentGold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_PremiumCurrency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BlueEssence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_OrangeEssence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_MatchesPlayed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_MatchesWon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_WinRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_TotalPlayTimeMinutes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_AccountCreationDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_LastOnlineDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_CurrentWinStreak,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BestWinStreak,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_HonorLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_HonorPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_bHasActiveBattlePass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BattlePassLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewProp_BattlePassXP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	&NewStructOps,
	"AuracronAccountProgression",
	Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::PropPointers),
	sizeof(FAuracronAccountProgression),
	alignof(FAuracronAccountProgression),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAccountProgression()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAccountProgression.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAccountProgression.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAccountProgression.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAccountProgression *****************************************

// ********** Begin ScriptStruct FAuracronChampionMastery ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionMastery;
class UScriptStruct* FAuracronChampionMastery::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionMastery.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionMastery.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionMastery, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("AuracronChampionMastery"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionMastery.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para maestria de campe\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para maestria de campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "Category", "Champion Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasteryLevel_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMax", "10" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de maestria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de maestria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasteryPoints_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de maestria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de maestria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsToNextLevel_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchesPlayed_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Partidas jogadas com o campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Partidas jogadas com o campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchesWon_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Partidas vencidas com o campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Partidas vencidas com o campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionWinRate_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Taxa de vit\xc3\x83\xc2\xb3ria com o campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Taxa de vit\xc3\x83\xc2\xb3ria com o campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageKDA_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** KDA m\xc3\x83\xc2\xa9""dio com o campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "KDA m\xc3\x83\xc2\xa9""dio com o campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BestKDA_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Melhor KDA com o campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Melhor KDA com o campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalPlayTime_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo total jogado com o campe\xc3\x83\xc2\xa3o (em minutos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo total jogado com o campe\xc3\x83\xc2\xa3o (em minutos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastPlayed_MetaData[] = {
		{ "Category", "Champion Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltima vez jogado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltima vez jogado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedMilestones_MetaData[] = {
		{ "Category", "Champion Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Marcos desbloqueados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Marcos desbloqueados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableRewards_MetaData[] = {
		{ "Category", "Champion Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas dispon\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas dispon\xc3\x83\xc2\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasMasteryToken_MetaData[] = {
		{ "Category", "Champion Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tem token de maestria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tem token de maestria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasteryTokens_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero de tokens de maestria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero de tokens de maestria" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MasteryLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MasteryPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsToNextLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MatchesPlayed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MatchesWon;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChampionWinRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageKDA;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BestKDA;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalPlayTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastPlayed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UnlockedMilestones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedMilestones;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AvailableRewards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AvailableRewards;
	static void NewProp_bHasMasteryToken_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasMasteryToken;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MasteryTokens;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionMastery>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MasteryLevel = { "MasteryLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, MasteryLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasteryLevel_MetaData), NewProp_MasteryLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MasteryPoints = { "MasteryPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, MasteryPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasteryPoints_MetaData), NewProp_MasteryPoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_PointsToNextLevel = { "PointsToNextLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, PointsToNextLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsToNextLevel_MetaData), NewProp_PointsToNextLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MatchesPlayed = { "MatchesPlayed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, MatchesPlayed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchesPlayed_MetaData), NewProp_MatchesPlayed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MatchesWon = { "MatchesWon", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, MatchesWon), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchesWon_MetaData), NewProp_MatchesWon_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_ChampionWinRate = { "ChampionWinRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, ChampionWinRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionWinRate_MetaData), NewProp_ChampionWinRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_AverageKDA = { "AverageKDA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, AverageKDA), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageKDA_MetaData), NewProp_AverageKDA_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_BestKDA = { "BestKDA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, BestKDA), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BestKDA_MetaData), NewProp_BestKDA_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_TotalPlayTime = { "TotalPlayTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, TotalPlayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalPlayTime_MetaData), NewProp_TotalPlayTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_LastPlayed = { "LastPlayed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, LastPlayed), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastPlayed_MetaData), NewProp_LastPlayed_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_UnlockedMilestones_Inner = { "UnlockedMilestones", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_UnlockedMilestones = { "UnlockedMilestones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, UnlockedMilestones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedMilestones_MetaData), NewProp_UnlockedMilestones_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_AvailableRewards_Inner = { "AvailableRewards", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_AvailableRewards = { "AvailableRewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, AvailableRewards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableRewards_MetaData), NewProp_AvailableRewards_MetaData) }; // 2703334020
void Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_bHasMasteryToken_SetBit(void* Obj)
{
	((FAuracronChampionMastery*)Obj)->bHasMasteryToken = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_bHasMasteryToken = { "bHasMasteryToken", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionMastery), &Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_bHasMasteryToken_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasMasteryToken_MetaData), NewProp_bHasMasteryToken_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MasteryTokens = { "MasteryTokens", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMastery, MasteryTokens), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasteryTokens_MetaData), NewProp_MasteryTokens_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MasteryLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MasteryPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_PointsToNextLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MatchesPlayed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MatchesWon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_ChampionWinRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_AverageKDA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_BestKDA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_TotalPlayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_LastPlayed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_UnlockedMilestones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_UnlockedMilestones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_AvailableRewards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_AvailableRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_bHasMasteryToken,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewProp_MasteryTokens,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionMastery",
	Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::PropPointers),
	sizeof(FAuracronChampionMastery),
	alignof(FAuracronChampionMastery),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionMastery()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionMastery.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionMastery.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionMastery.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionMastery ********************************************

// ********** Begin ScriptStruct FAuracronRealmMastery *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmMastery;
class UScriptStruct* FAuracronRealmMastery::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmMastery.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmMastery.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmMastery, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("AuracronRealmMastery"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmMastery.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para maestria de realm\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para maestria de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmType_MetaData[] = {
		{ "Category", "Realm Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasteryLevel_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMax", "10" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de maestria do realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de maestria do realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasteryPoints_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de maestria do realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de maestria do realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSpentInRealm_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo passado no realm (em minutos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo passado no realm (em minutos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectivesCompleted_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objetivos completados no realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objetivos completados no realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KillsInRealm_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Kills no realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Kills no realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeathsInRealm_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mortes no realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mortes no realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssistsInRealm_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Assist\xc3\x83\xc2\xaancias no realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Assist\xc3\x83\xc2\xaancias no realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscoveredSecrets_MetaData[] = {
		{ "Category", "Realm Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Segredos descobertos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segredos descobertos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExplorationPercentage_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x81reas exploradas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x81reas exploradas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedRealmMilestones_MetaData[] = {
		{ "Category", "Realm Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Marcos de realm desbloqueados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Marcos de realm desbloqueados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmRewards_MetaData[] = {
		{ "Category", "Realm Mastery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas de realm dispon\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas de realm dispon\xc3\x83\xc2\xadveis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MasteryLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MasteryPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TimeSpentInRealm;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectivesCompleted;
	static const UECodeGen_Private::FIntPropertyParams NewProp_KillsInRealm;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DeathsInRealm;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AssistsInRealm;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiscoveredSecrets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DiscoveredSecrets;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExplorationPercentage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UnlockedRealmMilestones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedRealmMilestones;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmRewards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmRewards;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmMastery>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, RealmType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmType_MetaData), NewProp_RealmType_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_MasteryLevel = { "MasteryLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, MasteryLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasteryLevel_MetaData), NewProp_MasteryLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_MasteryPoints = { "MasteryPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, MasteryPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasteryPoints_MetaData), NewProp_MasteryPoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_TimeSpentInRealm = { "TimeSpentInRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, TimeSpentInRealm), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSpentInRealm_MetaData), NewProp_TimeSpentInRealm_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_ObjectivesCompleted = { "ObjectivesCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, ObjectivesCompleted), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectivesCompleted_MetaData), NewProp_ObjectivesCompleted_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_KillsInRealm = { "KillsInRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, KillsInRealm), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KillsInRealm_MetaData), NewProp_KillsInRealm_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_DeathsInRealm = { "DeathsInRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, DeathsInRealm), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeathsInRealm_MetaData), NewProp_DeathsInRealm_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_AssistsInRealm = { "AssistsInRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, AssistsInRealm), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssistsInRealm_MetaData), NewProp_AssistsInRealm_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_DiscoveredSecrets_Inner = { "DiscoveredSecrets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_DiscoveredSecrets = { "DiscoveredSecrets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, DiscoveredSecrets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscoveredSecrets_MetaData), NewProp_DiscoveredSecrets_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_ExplorationPercentage = { "ExplorationPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, ExplorationPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExplorationPercentage_MetaData), NewProp_ExplorationPercentage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_UnlockedRealmMilestones_Inner = { "UnlockedRealmMilestones", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_UnlockedRealmMilestones = { "UnlockedRealmMilestones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, UnlockedRealmMilestones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedRealmMilestones_MetaData), NewProp_UnlockedRealmMilestones_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_RealmRewards_Inner = { "RealmRewards", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_RealmRewards = { "RealmRewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMastery, RealmRewards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmRewards_MetaData), NewProp_RealmRewards_MetaData) }; // 2703334020
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_MasteryLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_MasteryPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_TimeSpentInRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_ObjectivesCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_KillsInRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_DeathsInRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_AssistsInRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_DiscoveredSecrets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_DiscoveredSecrets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_ExplorationPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_UnlockedRealmMilestones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_UnlockedRealmMilestones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_RealmRewards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewProp_RealmRewards,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmMastery",
	Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::PropPointers),
	sizeof(FAuracronRealmMastery),
	alignof(FAuracronRealmMastery),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmMastery()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmMastery.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmMastery.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmMastery.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmMastery ***********************************************

// ********** Begin ScriptStruct FAuracronProgressionMilestone *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone;
class UScriptStruct* FAuracronProgressionMilestone::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronProgressionMilestone, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("AuracronProgressionMilestone"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para marco de progress\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para marco de progress\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneID_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico do marco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico do marco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneName_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do marco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do marco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneDescription_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do marco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do marco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressionType_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de progress\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de progress\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredValue_MetaData[] = {
		{ "Category", "Progression Milestone" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor necess\xc3\x83\xc2\xa1rio para completar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor necess\xc3\x83\xc2\xa1rio para completar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentValue_MetaData[] = {
		{ "Category", "Progression Milestone" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCompleted_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Marco foi completado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Marco foi completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionDate_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de conclus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de conclus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneRewards_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas do marco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas do marco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneIcon_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8d""cone do marco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8d""cone do marco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSecretMilestone_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\xb0 marco secreto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\xb0 marco secreto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSeasonalMilestone_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\xb0 marco sazonal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\xb0 marco sazonal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Season_MetaData[] = {
		{ "Category", "Progression Milestone" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Temporada do marco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Temporada do marco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayPriority_MetaData[] = {
		{ "Category", "Progression Milestone" },
		{ "ClampMax", "10" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneTags_MetaData[] = {
		{ "Category", "Progression Milestone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags do marco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags do marco" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MilestoneID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_MilestoneName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_MilestoneDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProgressionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProgressionType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredValue;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentValue;
	static void NewProp_bCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCompleted;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletionDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MilestoneRewards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MilestoneRewards;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MilestoneIcon;
	static void NewProp_bIsSecretMilestone_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSecretMilestone;
	static void NewProp_bIsSeasonalMilestone_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSeasonalMilestone;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Season;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DisplayPriority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MilestoneTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronProgressionMilestone>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneID = { "MilestoneID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, MilestoneID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneID_MetaData), NewProp_MilestoneID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneName = { "MilestoneName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, MilestoneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneName_MetaData), NewProp_MilestoneName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneDescription = { "MilestoneDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, MilestoneDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneDescription_MetaData), NewProp_MilestoneDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_ProgressionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_ProgressionType = { "ProgressionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, ProgressionType), Z_Construct_UEnum_AuracronProgressionBridge_EAuracronProgressionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressionType_MetaData), NewProp_ProgressionType_MetaData) }; // 4169099298
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_RequiredValue = { "RequiredValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, RequiredValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredValue_MetaData), NewProp_RequiredValue_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_CurrentValue = { "CurrentValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, CurrentValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentValue_MetaData), NewProp_CurrentValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bCompleted_SetBit(void* Obj)
{
	((FAuracronProgressionMilestone*)Obj)->bCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bCompleted = { "bCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProgressionMilestone), &Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCompleted_MetaData), NewProp_bCompleted_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_CompletionDate = { "CompletionDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, CompletionDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionDate_MetaData), NewProp_CompletionDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneRewards_Inner = { "MilestoneRewards", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneRewards = { "MilestoneRewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, MilestoneRewards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneRewards_MetaData), NewProp_MilestoneRewards_MetaData) }; // 2703334020
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneIcon = { "MilestoneIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, MilestoneIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneIcon_MetaData), NewProp_MilestoneIcon_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSecretMilestone_SetBit(void* Obj)
{
	((FAuracronProgressionMilestone*)Obj)->bIsSecretMilestone = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSecretMilestone = { "bIsSecretMilestone", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProgressionMilestone), &Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSecretMilestone_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSecretMilestone_MetaData), NewProp_bIsSecretMilestone_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSeasonalMilestone_SetBit(void* Obj)
{
	((FAuracronProgressionMilestone*)Obj)->bIsSeasonalMilestone = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSeasonalMilestone = { "bIsSeasonalMilestone", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProgressionMilestone), &Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSeasonalMilestone_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSeasonalMilestone_MetaData), NewProp_bIsSeasonalMilestone_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_Season = { "Season", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, Season), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Season_MetaData), NewProp_Season_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_DisplayPriority = { "DisplayPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, DisplayPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayPriority_MetaData), NewProp_DisplayPriority_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneTags = { "MilestoneTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProgressionMilestone, MilestoneTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneTags_MetaData), NewProp_MilestoneTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_ProgressionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_ProgressionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_RequiredValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_CurrentValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_CompletionDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneRewards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSecretMilestone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_bIsSeasonalMilestone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_Season,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_DisplayPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewProp_MilestoneTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	&NewStructOps,
	"AuracronProgressionMilestone",
	Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::PropPointers),
	sizeof(FAuracronProgressionMilestone),
	alignof(FAuracronProgressionMilestone),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronProgressionMilestone()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone.InnerSingleton, Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone.InnerSingleton;
}
// ********** End ScriptStruct FAuracronProgressionMilestone ***************************************

// ********** Begin ScriptStruct FAuracronChampionMasteryEntry *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry;
class UScriptStruct* FAuracronChampionMasteryEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("AuracronChampionMasteryEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de maestria de campe\xc3\xa3o (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de maestria de campe\xc3\xa3o (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mastery_MetaData[] = {
		{ "Category", "Champion Mastery" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Mastery;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionMasteryEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMasteryEntry, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::NewProp_Mastery = { "Mastery", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMasteryEntry, Mastery), Z_Construct_UScriptStruct_FAuracronChampionMastery, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mastery_MetaData), NewProp_Mastery_MetaData) }; // 1686980573
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::NewProp_Mastery,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionMasteryEntry",
	Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::PropPointers),
	sizeof(FAuracronChampionMasteryEntry),
	alignof(FAuracronChampionMasteryEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionMasteryEntry ***************************************

// ********** Begin ScriptStruct FAuracronRealmMasteryEntry ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry;
class UScriptStruct* FAuracronRealmMasteryEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry, (UObject*)Z_Construct_UPackage__Script_AuracronProgressionBridge(), TEXT("AuracronRealmMasteryEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de maestria de realm (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de maestria de realm (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmID_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mastery_MetaData[] = {
		{ "Category", "Realm Mastery" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Mastery;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmMasteryEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::NewProp_RealmID = { "RealmID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMasteryEntry, RealmID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmID_MetaData), NewProp_RealmID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::NewProp_Mastery = { "Mastery", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmMasteryEntry, Mastery), Z_Construct_UScriptStruct_FAuracronRealmMastery, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mastery_MetaData), NewProp_Mastery_MetaData) }; // 1602401411
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::NewProp_RealmID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::NewProp_Mastery,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmMasteryEntry",
	Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::PropPointers),
	sizeof(FAuracronRealmMasteryEntry),
	alignof(FAuracronRealmMasteryEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmMasteryEntry ******************************************

// ********** Begin Delegate FOnAccountLevelUp *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics
{
	struct AuracronProgressionBridge_eventOnAccountLevelUp_Parms
	{
		int32 OldLevel;
		int32 NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando conta sobe de n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando conta sobe de n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_OldLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::NewProp_OldLevel = { "OldLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnAccountLevelUp_Parms, OldLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnAccountLevelUp_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::NewProp_OldLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "OnAccountLevelUp__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::AuracronProgressionBridge_eventOnAccountLevelUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::AuracronProgressionBridge_eventOnAccountLevelUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronProgressionBridge::FOnAccountLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnAccountLevelUp, int32 OldLevel, int32 NewLevel)
{
	struct AuracronProgressionBridge_eventOnAccountLevelUp_Parms
	{
		int32 OldLevel;
		int32 NewLevel;
	};
	AuracronProgressionBridge_eventOnAccountLevelUp_Parms Parms;
	Parms.OldLevel=OldLevel;
	Parms.NewLevel=NewLevel;
	OnAccountLevelUp.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAccountLevelUp *******************************************************

// ********** Begin Delegate FOnChampionMasteryLevelUp *********************************************
struct Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics
{
	struct AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms
	{
		FString ChampionID;
		int32 OldLevel;
		int32 NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando maestria de campe\xc3\x83\xc2\xa3o sobe de n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando maestria de campe\xc3\x83\xc2\xa3o sobe de n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OldLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms, ChampionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::NewProp_OldLevel = { "OldLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms, OldLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::NewProp_OldLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "OnChampionMasteryLevelUp__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronProgressionBridge::FOnChampionMasteryLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnChampionMasteryLevelUp, const FString& ChampionID, int32 OldLevel, int32 NewLevel)
{
	struct AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms
	{
		FString ChampionID;
		int32 OldLevel;
		int32 NewLevel;
	};
	AuracronProgressionBridge_eventOnChampionMasteryLevelUp_Parms Parms;
	Parms.ChampionID=ChampionID;
	Parms.OldLevel=OldLevel;
	Parms.NewLevel=NewLevel;
	OnChampionMasteryLevelUp.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChampionMasteryLevelUp ***********************************************

// ********** Begin Delegate FOnMilestoneCompleted *************************************************
struct Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics
{
	struct AuracronProgressionBridge_eventOnMilestoneCompleted_Parms
	{
		FString MilestoneID;
		TArray<FAuracronReward> Rewards;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando marco \xc3\x83\xc2\xa9 completado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando marco \xc3\x83\xc2\xa9 completado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MilestoneID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rewards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Rewards;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::NewProp_MilestoneID = { "MilestoneID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnMilestoneCompleted_Parms, MilestoneID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::NewProp_Rewards_Inner = { "Rewards", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::NewProp_Rewards = { "Rewards", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnMilestoneCompleted_Parms, Rewards), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::NewProp_MilestoneID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::NewProp_Rewards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::NewProp_Rewards,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "OnMilestoneCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::AuracronProgressionBridge_eventOnMilestoneCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::AuracronProgressionBridge_eventOnMilestoneCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronProgressionBridge::FOnMilestoneCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnMilestoneCompleted, const FString& MilestoneID, const TArray<FAuracronReward>& Rewards)
{
	struct AuracronProgressionBridge_eventOnMilestoneCompleted_Parms
	{
		FString MilestoneID;
		TArray<FAuracronReward> Rewards;
	};
	AuracronProgressionBridge_eventOnMilestoneCompleted_Parms Parms;
	Parms.MilestoneID=MilestoneID;
	Parms.Rewards=Rewards;
	OnMilestoneCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMilestoneCompleted ***************************************************

// ********** Begin Delegate FOnRewardGranted ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics
{
	struct AuracronProgressionBridge_eventOnRewardGranted_Parms
	{
		FAuracronReward Reward;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando recompensa \xc3\x83\xc2\xa9 concedida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando recompensa \xc3\x83\xc2\xa9 concedida" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventOnRewardGranted_Parms, Reward), Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::NewProp_Reward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "OnRewardGranted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::AuracronProgressionBridge_eventOnRewardGranted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::AuracronProgressionBridge_eventOnRewardGranted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronProgressionBridge::FOnRewardGranted_DelegateWrapper(const FMulticastScriptDelegate& OnRewardGranted, FAuracronReward Reward)
{
	struct AuracronProgressionBridge_eventOnRewardGranted_Parms
	{
		FAuracronReward Reward;
	};
	AuracronProgressionBridge_eventOnRewardGranted_Parms Parms;
	Parms.Reward=Reward;
	OnRewardGranted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRewardGranted ********************************************************

// ********** Begin Delegate FOnProgressionSynced **************************************************
struct Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics
{
	struct AuracronProgressionBridge_eventOnProgressionSynced_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando progress\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 sincronizada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando progress\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 sincronizada" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventOnProgressionSynced_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventOnProgressionSynced_Parms), &Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "OnProgressionSynced__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::AuracronProgressionBridge_eventOnProgressionSynced_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::AuracronProgressionBridge_eventOnProgressionSynced_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronProgressionBridge::FOnProgressionSynced_DelegateWrapper(const FMulticastScriptDelegate& OnProgressionSynced, bool bSuccess)
{
	struct AuracronProgressionBridge_eventOnProgressionSynced_Parms
	{
		bool bSuccess;
	};
	AuracronProgressionBridge_eventOnProgressionSynced_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	OnProgressionSynced.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnProgressionSynced ****************************************************

// ********** Begin Class UAuracronProgressionBridge Function AddGold ******************************
struct Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics
{
	struct AuracronProgressionBridge_eventAddGold_Parms
	{
		int32 GoldAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Account" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar gold\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar gold" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::NewProp_GoldAmount = { "GoldAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventAddGold_Parms, GoldAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventAddGold_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventAddGold_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::NewProp_GoldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "AddGold", Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::AuracronProgressionBridge_eventAddGold_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::AuracronProgressionBridge_eventAddGold_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_AddGold()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_AddGold_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execAddGold)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GoldAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddGold(Z_Param_GoldAmount);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function AddGold ********************************

// ********** Begin Class UAuracronProgressionBridge Function AddPremiumCurrency *******************
struct Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics
{
	struct AuracronProgressionBridge_eventAddPremiumCurrency_Parms
	{
		int32 Amount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Account" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar moeda premium\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar moeda premium" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventAddPremiumCurrency_Parms, Amount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventAddPremiumCurrency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventAddPremiumCurrency_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "AddPremiumCurrency", Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::AuracronProgressionBridge_eventAddPremiumCurrency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::AuracronProgressionBridge_eventAddPremiumCurrency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execAddPremiumCurrency)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddPremiumCurrency(Z_Param_Amount);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function AddPremiumCurrency *********************

// ********** Begin Class UAuracronProgressionBridge Function BackupProgression ********************
struct Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics
{
	struct AuracronProgressionBridge_eventBackupProgression_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Cloud" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Fazer backup da progress\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fazer backup da progress\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventBackupProgression_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventBackupProgression_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "BackupProgression", Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::AuracronProgressionBridge_eventBackupProgression_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::AuracronProgressionBridge_eventBackupProgression_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execBackupProgression)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BackupProgression();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function BackupProgression **********************

// ********** Begin Class UAuracronProgressionBridge Function CalculateExperienceForLevel **********
struct Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics
{
	struct AuracronProgressionBridge_eventCalculateExperienceForLevel_Parms
	{
		int32 Level;
		int64 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Account" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Calcular experi\xc3\x83\xc2\xaancia necess\xc3\x83\xc2\xa1ria para n\xc3\x83\xc2\xadvel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular experi\xc3\x83\xc2\xaancia necess\xc3\x83\xc2\xa1ria para n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventCalculateExperienceForLevel_Parms, Level), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventCalculateExperienceForLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "CalculateExperienceForLevel", Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::AuracronProgressionBridge_eventCalculateExperienceForLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::AuracronProgressionBridge_eventCalculateExperienceForLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execCalculateExperienceForLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Level);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int64*)Z_Param__Result=P_THIS->CalculateExperienceForLevel(Z_Param_Level);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function CalculateExperienceForLevel ************

// ********** Begin Class UAuracronProgressionBridge Function CheckMilestoneProgress ***************
struct Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics
{
	struct AuracronProgressionBridge_eventCheckMilestoneProgress_Parms
	{
		FString MilestoneID;
		int32 NewValue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Milestones" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar progresso de marco\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar progresso de marco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MilestoneID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewValue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_MilestoneID = { "MilestoneID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventCheckMilestoneProgress_Parms, MilestoneID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneID_MetaData), NewProp_MilestoneID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventCheckMilestoneProgress_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventCheckMilestoneProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventCheckMilestoneProgress_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_MilestoneID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_NewValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "CheckMilestoneProgress", Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::AuracronProgressionBridge_eventCheckMilestoneProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::AuracronProgressionBridge_eventCheckMilestoneProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execCheckMilestoneProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MilestoneID);
	P_GET_PROPERTY(FIntProperty,Z_Param_NewValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckMilestoneProgress(Z_Param_MilestoneID,Z_Param_NewValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function CheckMilestoneProgress *****************

// ********** Begin Class UAuracronProgressionBridge Function ClaimAllRewards **********************
struct Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics
{
	struct AuracronProgressionBridge_eventClaimAllRewards_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Coletar todas as recompensas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coletar todas as recompensas" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventClaimAllRewards_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventClaimAllRewards_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "ClaimAllRewards", Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::AuracronProgressionBridge_eventClaimAllRewards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::AuracronProgressionBridge_eventClaimAllRewards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execClaimAllRewards)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ClaimAllRewards();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function ClaimAllRewards ************************

// ********** Begin Class UAuracronProgressionBridge Function ClaimReward **************************
struct Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics
{
	struct AuracronProgressionBridge_eventClaimReward_Parms
	{
		FString RewardID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Coletar recompensa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coletar recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventClaimReward_Parms, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
void Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventClaimReward_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventClaimReward_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::NewProp_RewardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "ClaimReward", Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::AuracronProgressionBridge_eventClaimReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::AuracronProgressionBridge_eventClaimReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execClaimReward)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RewardID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ClaimReward(Z_Param_RewardID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function ClaimReward ****************************

// ********** Begin Class UAuracronProgressionBridge Function CompleteMilestone ********************
struct Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics
{
	struct AuracronProgressionBridge_eventCompleteMilestone_Parms
	{
		FString MilestoneID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Milestones" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Completar marco de progress\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completar marco de progress\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MilestoneID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MilestoneID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::NewProp_MilestoneID = { "MilestoneID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventCompleteMilestone_Parms, MilestoneID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MilestoneID_MetaData), NewProp_MilestoneID_MetaData) };
void Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventCompleteMilestone_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventCompleteMilestone_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::NewProp_MilestoneID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "CompleteMilestone", Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::AuracronProgressionBridge_eventCompleteMilestone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::AuracronProgressionBridge_eventCompleteMilestone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execCompleteMilestone)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MilestoneID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CompleteMilestone(Z_Param_MilestoneID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function CompleteMilestone **********************

// ********** Begin Class UAuracronProgressionBridge Function DiscoverRealmSecret ******************
struct Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics
{
	struct AuracronProgressionBridge_eventDiscoverRealmSecret_Parms
	{
		int32 RealmType;
		FString SecretID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Descobrir segredo do realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descobrir segredo do realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecretID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SecretID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventDiscoverRealmSecret_Parms, RealmType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_SecretID = { "SecretID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventDiscoverRealmSecret_Parms, SecretID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecretID_MetaData), NewProp_SecretID_MetaData) };
void Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventDiscoverRealmSecret_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventDiscoverRealmSecret_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_SecretID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "DiscoverRealmSecret", Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::AuracronProgressionBridge_eventDiscoverRealmSecret_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::AuracronProgressionBridge_eventDiscoverRealmSecret_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execDiscoverRealmSecret)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmType);
	P_GET_PROPERTY(FStrProperty,Z_Param_SecretID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DiscoverRealmSecret(Z_Param_RealmType,Z_Param_SecretID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function DiscoverRealmSecret ********************

// ********** Begin Class UAuracronProgressionBridge Function GainAccountExperience ****************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics
{
	struct AuracronProgressionBridge_eventGainAccountExperience_Parms
	{
		int32 ExperienceAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Account" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ganhar experi\xc3\x83\xc2\xaancia de conta\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ganhar experi\xc3\x83\xc2\xaancia de conta" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::NewProp_ExperienceAmount = { "ExperienceAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGainAccountExperience_Parms, ExperienceAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventGainAccountExperience_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventGainAccountExperience_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::NewProp_ExperienceAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GainAccountExperience", Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::AuracronProgressionBridge_eventGainAccountExperience_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::AuracronProgressionBridge_eventGainAccountExperience_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGainAccountExperience)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ExperienceAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GainAccountExperience(Z_Param_ExperienceAmount);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GainAccountExperience ******************

// ********** Begin Class UAuracronProgressionBridge Function GainChampionMasteryPoints ************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics
{
	struct AuracronProgressionBridge_eventGainChampionMasteryPoints_Parms
	{
		FString ChampionID;
		int32 Points;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ganhar pontos de maestria de campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ganhar pontos de maestria de campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Points;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGainChampionMasteryPoints_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGainChampionMasteryPoints_Parms, Points), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventGainChampionMasteryPoints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventGainChampionMasteryPoints_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GainChampionMasteryPoints", Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::AuracronProgressionBridge_eventGainChampionMasteryPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::AuracronProgressionBridge_eventGainChampionMasteryPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGainChampionMasteryPoints)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Points);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GainChampionMasteryPoints(Z_Param_ChampionID,Z_Param_Points);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GainChampionMasteryPoints **************

// ********** Begin Class UAuracronProgressionBridge Function GainRealmMasteryPoints ***************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics
{
	struct AuracronProgressionBridge_eventGainRealmMasteryPoints_Parms
	{
		int32 RealmType;
		int32 Points;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ganhar pontos de maestria de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ganhar pontos de maestria de realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Points;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGainRealmMasteryPoints_Parms, RealmType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGainRealmMasteryPoints_Parms, Points), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventGainRealmMasteryPoints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventGainRealmMasteryPoints_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GainRealmMasteryPoints", Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::AuracronProgressionBridge_eventGainRealmMasteryPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::AuracronProgressionBridge_eventGainRealmMasteryPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGainRealmMasteryPoints)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmType);
	P_GET_PROPERTY(FIntProperty,Z_Param_Points);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GainRealmMasteryPoints(Z_Param_RealmType,Z_Param_Points);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GainRealmMasteryPoints *****************

// ********** Begin Class UAuracronProgressionBridge Function GetAccountProgression ****************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics
{
	struct AuracronProgressionBridge_eventGetAccountProgression_Parms
	{
		FAuracronAccountProgression ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Account" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter progress\xc3\x83\xc2\xa3o da conta\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progress\xc3\x83\xc2\xa3o da conta" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetAccountProgression_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAccountProgression, METADATA_PARAMS(0, nullptr) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GetAccountProgression", Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::AuracronProgressionBridge_eventGetAccountProgression_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::AuracronProgressionBridge_eventGetAccountProgression_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGetAccountProgression)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAccountProgression*)Z_Param__Result=P_THIS->GetAccountProgression();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GetAccountProgression ******************

// ********** Begin Class UAuracronProgressionBridge Function GetAllChampionMasteries **************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics
{
	struct AuracronProgressionBridge_eventGetAllChampionMasteries_Parms
	{
		TArray<FAuracronChampionMastery> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter todas as maestrias de campe\xc3\x83\xc2\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todas as maestrias de campe\xc3\x83\xc2\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronChampionMastery, METADATA_PARAMS(0, nullptr) }; // 1686980573
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetAllChampionMasteries_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1686980573
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GetAllChampionMasteries", Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::AuracronProgressionBridge_eventGetAllChampionMasteries_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::AuracronProgressionBridge_eventGetAllChampionMasteries_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGetAllChampionMasteries)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronChampionMastery>*)Z_Param__Result=P_THIS->GetAllChampionMasteries();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GetAllChampionMasteries ****************

// ********** Begin Class UAuracronProgressionBridge Function GetAvailableMilestones ***************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics
{
	struct AuracronProgressionBridge_eventGetAvailableMilestones_Parms
	{
		TArray<FAuracronProgressionMilestone> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Milestones" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter marcos dispon\xc3\x83\xc2\xadveis\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter marcos dispon\xc3\x83\xc2\xadveis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProgressionMilestone, METADATA_PARAMS(0, nullptr) }; // 2858283715
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetAvailableMilestones_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2858283715
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GetAvailableMilestones", Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::AuracronProgressionBridge_eventGetAvailableMilestones_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::AuracronProgressionBridge_eventGetAvailableMilestones_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGetAvailableMilestones)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronProgressionMilestone>*)Z_Param__Result=P_THIS->GetAvailableMilestones();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GetAvailableMilestones *****************

// ********** Begin Class UAuracronProgressionBridge Function GetChampionMastery *******************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics
{
	struct AuracronProgressionBridge_eventGetChampionMastery_Parms
	{
		FString ChampionID;
		FAuracronChampionMastery ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter maestria de campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter maestria de campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetChampionMastery_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetChampionMastery_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronChampionMastery, METADATA_PARAMS(0, nullptr) }; // 1686980573
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GetChampionMastery", Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::AuracronProgressionBridge_eventGetChampionMastery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::AuracronProgressionBridge_eventGetChampionMastery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGetChampionMastery)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronChampionMastery*)Z_Param__Result=P_THIS->GetChampionMastery(Z_Param_ChampionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GetChampionMastery *********************

// ********** Begin Class UAuracronProgressionBridge Function GetCompletedMilestones ***************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics
{
	struct AuracronProgressionBridge_eventGetCompletedMilestones_Parms
	{
		TArray<FAuracronProgressionMilestone> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Milestones" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter marcos completados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter marcos completados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProgressionMilestone, METADATA_PARAMS(0, nullptr) }; // 2858283715
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetCompletedMilestones_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2858283715
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GetCompletedMilestones", Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::AuracronProgressionBridge_eventGetCompletedMilestones_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::AuracronProgressionBridge_eventGetCompletedMilestones_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGetCompletedMilestones)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronProgressionMilestone>*)Z_Param__Result=P_THIS->GetCompletedMilestones();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GetCompletedMilestones *****************

// ********** Begin Class UAuracronProgressionBridge Function GetPendingRewards ********************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics
{
	struct AuracronProgressionBridge_eventGetPendingRewards_Parms
	{
		TArray<FAuracronReward> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter recompensas pendentes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter recompensas pendentes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetPendingRewards_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GetPendingRewards", Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::AuracronProgressionBridge_eventGetPendingRewards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::AuracronProgressionBridge_eventGetPendingRewards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGetPendingRewards)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronReward>*)Z_Param__Result=P_THIS->GetPendingRewards();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GetPendingRewards **********************

// ********** Begin Class UAuracronProgressionBridge Function GetRealmMastery **********************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics
{
	struct AuracronProgressionBridge_eventGetRealmMastery_Parms
	{
		int32 RealmType;
		FAuracronRealmMastery ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter maestria de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter maestria de realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetRealmMastery_Parms, RealmType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGetRealmMastery_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronRealmMastery, METADATA_PARAMS(0, nullptr) }; // 1602401411
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GetRealmMastery", Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::AuracronProgressionBridge_eventGetRealmMastery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::AuracronProgressionBridge_eventGetRealmMastery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGetRealmMastery)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronRealmMastery*)Z_Param__Result=P_THIS->GetRealmMastery(Z_Param_RealmType);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GetRealmMastery ************************

// ********** Begin Class UAuracronProgressionBridge Function GrantReward **************************
struct Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics
{
	struct AuracronProgressionBridge_eventGrantReward_Parms
	{
		FAuracronReward Reward;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Conceder recompensa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conceder recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventGrantReward_Parms, Reward), Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reward_MetaData), NewProp_Reward_MetaData) }; // 2703334020
void Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventGrantReward_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventGrantReward_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::NewProp_Reward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "GrantReward", Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::AuracronProgressionBridge_eventGrantReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::AuracronProgressionBridge_eventGrantReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execGrantReward)
{
	P_GET_STRUCT_REF(FAuracronReward,Z_Param_Out_Reward);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GrantReward(Z_Param_Out_Reward);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function GrantReward ****************************

// ********** Begin Class UAuracronProgressionBridge Function LevelUpAccount ***********************
struct Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics
{
	struct AuracronProgressionBridge_eventLevelUpAccount_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Account" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Subir n\xc3\x83\xc2\xadvel de conta\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subir n\xc3\x83\xc2\xadvel de conta" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventLevelUpAccount_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventLevelUpAccount_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "LevelUpAccount", Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::AuracronProgressionBridge_eventLevelUpAccount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::AuracronProgressionBridge_eventLevelUpAccount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execLevelUpAccount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LevelUpAccount();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function LevelUpAccount *************************

// ********** Begin Class UAuracronProgressionBridge Function LevelUpChampionMastery ***************
struct Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics
{
	struct AuracronProgressionBridge_eventLevelUpChampionMastery_Parms
	{
		FString ChampionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Subir n\xc3\x83\xc2\xadvel de maestria de campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subir n\xc3\x83\xc2\xadvel de maestria de campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventLevelUpChampionMastery_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
void Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventLevelUpChampionMastery_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventLevelUpChampionMastery_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "LevelUpChampionMastery", Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::AuracronProgressionBridge_eventLevelUpChampionMastery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::AuracronProgressionBridge_eventLevelUpChampionMastery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execLevelUpChampionMastery)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LevelUpChampionMastery(Z_Param_ChampionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function LevelUpChampionMastery *****************

// ********** Begin Class UAuracronProgressionBridge Function LoadProgressionFromCloud *************
struct Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics
{
	struct AuracronProgressionBridge_eventLoadProgressionFromCloud_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Cloud" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar progress\xc3\x83\xc2\xa3o da nuvem\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar progress\xc3\x83\xc2\xa3o da nuvem" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventLoadProgressionFromCloud_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventLoadProgressionFromCloud_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "LoadProgressionFromCloud", Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::AuracronProgressionBridge_eventLoadProgressionFromCloud_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::AuracronProgressionBridge_eventLoadProgressionFromCloud_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execLoadProgressionFromCloud)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadProgressionFromCloud();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function LoadProgressionFromCloud ***************

// ********** Begin Class UAuracronProgressionBridge Function OnRep_PendingRewards *****************
struct Z_Construct_UFunction_UAuracronProgressionBridge_OnRep_PendingRewards_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_OnRep_PendingRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "OnRep_PendingRewards", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_OnRep_PendingRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_OnRep_PendingRewards_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_OnRep_PendingRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_OnRep_PendingRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execOnRep_PendingRewards)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_PendingRewards();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function OnRep_PendingRewards *******************

// ********** Begin Class UAuracronProgressionBridge Function RegisterTimeInRealm ******************
struct Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics
{
	struct AuracronProgressionBridge_eventRegisterTimeInRealm_Parms
	{
		int32 RealmType;
		int32 TimeMinutes;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar tempo no realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar tempo no realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TimeMinutes;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventRegisterTimeInRealm_Parms, RealmType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_TimeMinutes = { "TimeMinutes", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventRegisterTimeInRealm_Parms, TimeMinutes), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventRegisterTimeInRealm_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventRegisterTimeInRealm_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_TimeMinutes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "RegisterTimeInRealm", Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::AuracronProgressionBridge_eventRegisterTimeInRealm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::AuracronProgressionBridge_eventRegisterTimeInRealm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execRegisterTimeInRealm)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmType);
	P_GET_PROPERTY(FIntProperty,Z_Param_TimeMinutes);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterTimeInRealm(Z_Param_RealmType,Z_Param_TimeMinutes);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function RegisterTimeInRealm ********************

// ********** Begin Class UAuracronProgressionBridge Function RemoveGold ***************************
struct Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics
{
	struct AuracronProgressionBridge_eventRemoveGold_Parms
	{
		int32 GoldAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Account" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover gold\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover gold" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::NewProp_GoldAmount = { "GoldAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProgressionBridge_eventRemoveGold_Parms, GoldAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventRemoveGold_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventRemoveGold_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::NewProp_GoldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "RemoveGold", Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::AuracronProgressionBridge_eventRemoveGold_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::AuracronProgressionBridge_eventRemoveGold_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execRemoveGold)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GoldAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveGold(Z_Param_GoldAmount);
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function RemoveGold *****************************

// ********** Begin Class UAuracronProgressionBridge Function SaveProgressionToCloud ***************
struct Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics
{
	struct AuracronProgressionBridge_eventSaveProgressionToCloud_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Cloud" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Salvar progress\xc3\x83\xc2\xa3o na nuvem\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Salvar progress\xc3\x83\xc2\xa3o na nuvem" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventSaveProgressionToCloud_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventSaveProgressionToCloud_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "SaveProgressionToCloud", Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::AuracronProgressionBridge_eventSaveProgressionToCloud_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::AuracronProgressionBridge_eventSaveProgressionToCloud_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execSaveProgressionToCloud)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveProgressionToCloud();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function SaveProgressionToCloud *****************

// ********** Begin Class UAuracronProgressionBridge Function SyncWithFirebase *********************
struct Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics
{
	struct AuracronProgressionBridge_eventSyncWithFirebase_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Progression|Cloud" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Sincronizar com Firebase\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sincronizar com Firebase" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronProgressionBridge_eventSyncWithFirebase_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProgressionBridge_eventSyncWithFirebase_Parms), &Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProgressionBridge, nullptr, "SyncWithFirebase", Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::AuracronProgressionBridge_eventSyncWithFirebase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::AuracronProgressionBridge_eventSyncWithFirebase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProgressionBridge::execSyncWithFirebase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SyncWithFirebase();
	P_NATIVE_END;
}
// ********** End Class UAuracronProgressionBridge Function SyncWithFirebase ***********************

// ********** Begin Class UAuracronProgressionBridge ***********************************************
void UAuracronProgressionBridge::StaticRegisterNativesUAuracronProgressionBridge()
{
	UClass* Class = UAuracronProgressionBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddGold", &UAuracronProgressionBridge::execAddGold },
		{ "AddPremiumCurrency", &UAuracronProgressionBridge::execAddPremiumCurrency },
		{ "BackupProgression", &UAuracronProgressionBridge::execBackupProgression },
		{ "CalculateExperienceForLevel", &UAuracronProgressionBridge::execCalculateExperienceForLevel },
		{ "CheckMilestoneProgress", &UAuracronProgressionBridge::execCheckMilestoneProgress },
		{ "ClaimAllRewards", &UAuracronProgressionBridge::execClaimAllRewards },
		{ "ClaimReward", &UAuracronProgressionBridge::execClaimReward },
		{ "CompleteMilestone", &UAuracronProgressionBridge::execCompleteMilestone },
		{ "DiscoverRealmSecret", &UAuracronProgressionBridge::execDiscoverRealmSecret },
		{ "GainAccountExperience", &UAuracronProgressionBridge::execGainAccountExperience },
		{ "GainChampionMasteryPoints", &UAuracronProgressionBridge::execGainChampionMasteryPoints },
		{ "GainRealmMasteryPoints", &UAuracronProgressionBridge::execGainRealmMasteryPoints },
		{ "GetAccountProgression", &UAuracronProgressionBridge::execGetAccountProgression },
		{ "GetAllChampionMasteries", &UAuracronProgressionBridge::execGetAllChampionMasteries },
		{ "GetAvailableMilestones", &UAuracronProgressionBridge::execGetAvailableMilestones },
		{ "GetChampionMastery", &UAuracronProgressionBridge::execGetChampionMastery },
		{ "GetCompletedMilestones", &UAuracronProgressionBridge::execGetCompletedMilestones },
		{ "GetPendingRewards", &UAuracronProgressionBridge::execGetPendingRewards },
		{ "GetRealmMastery", &UAuracronProgressionBridge::execGetRealmMastery },
		{ "GrantReward", &UAuracronProgressionBridge::execGrantReward },
		{ "LevelUpAccount", &UAuracronProgressionBridge::execLevelUpAccount },
		{ "LevelUpChampionMastery", &UAuracronProgressionBridge::execLevelUpChampionMastery },
		{ "LoadProgressionFromCloud", &UAuracronProgressionBridge::execLoadProgressionFromCloud },
		{ "OnRep_PendingRewards", &UAuracronProgressionBridge::execOnRep_PendingRewards },
		{ "RegisterTimeInRealm", &UAuracronProgressionBridge::execRegisterTimeInRealm },
		{ "RemoveGold", &UAuracronProgressionBridge::execRemoveGold },
		{ "SaveProgressionToCloud", &UAuracronProgressionBridge::execSaveProgressionToCloud },
		{ "SyncWithFirebase", &UAuracronProgressionBridge::execSyncWithFirebase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronProgressionBridge;
UClass* UAuracronProgressionBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronProgressionBridge;
	if (!Z_Registration_Info_UClass_UAuracronProgressionBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronProgressionBridge"),
			Z_Registration_Info_UClass_UAuracronProgressionBridge.InnerSingleton,
			StaticRegisterNativesUAuracronProgressionBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronProgressionBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronProgressionBridge_NoRegister()
{
	return UAuracronProgressionBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronProgressionBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Progress\xc3\x83\xc2\xa3o\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de progress\xc3\x83\xc2\xa3o com Firebase e EOS\n */" },
#endif
		{ "DisplayName", "AURACRON Progression Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronProgressionBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Progress\xc3\x83\xc2\xa3o\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de progress\xc3\x83\xc2\xa3o com Firebase e EOS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAccountProgression_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progress\xc3\x83\xc2\xa3o da conta atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress\xc3\x83\xc2\xa3o da conta atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionMasteries_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maestrias de campe\xc3\x83\xc2\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maestrias de campe\xc3\x83\xc2\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmMasteries_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maestrias de realms */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maestrias de realms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressionMilestones_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Marcos de progress\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Marcos de progress\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PendingRewards_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas pendentes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas pendentes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastCloudSync_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltima sincroniza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o com a nuvem */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltima sincroniza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o com a nuvem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAccountLevelUp_MetaData[] = {
		{ "Category", "AURACRON Progression|Events" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChampionMasteryLevelUp_MetaData[] = {
		{ "Category", "AURACRON Progression|Events" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMilestoneCompleted_MetaData[] = {
		{ "Category", "AURACRON Progression|Events" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRewardGranted_MetaData[] = {
		{ "Category", "AURACRON Progression|Events" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnProgressionSynced_MetaData[] = {
		{ "Category", "AURACRON Progression|Events" },
		{ "ModuleRelativePath", "Public/AuracronProgressionBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentAccountProgression;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChampionMasteries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChampionMasteries;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmMasteries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmMasteries;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProgressionMilestones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ProgressionMilestones;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PendingRewards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PendingRewards;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastCloudSync;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAccountLevelUp;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChampionMasteryLevelUp;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMilestoneCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRewardGranted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnProgressionSynced;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_AddGold, "AddGold" }, // *********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_AddPremiumCurrency, "AddPremiumCurrency" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_BackupProgression, "BackupProgression" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_CalculateExperienceForLevel, "CalculateExperienceForLevel" }, // 2439350333
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_CheckMilestoneProgress, "CheckMilestoneProgress" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_ClaimAllRewards, "ClaimAllRewards" }, // *********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_ClaimReward, "ClaimReward" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_CompleteMilestone, "CompleteMilestone" }, // *********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_DiscoverRealmSecret, "DiscoverRealmSecret" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GainAccountExperience, "GainAccountExperience" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GainChampionMasteryPoints, "GainChampionMasteryPoints" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GainRealmMasteryPoints, "GainRealmMasteryPoints" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GetAccountProgression, "GetAccountProgression" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GetAllChampionMasteries, "GetAllChampionMasteries" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GetAvailableMilestones, "GetAvailableMilestones" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GetChampionMastery, "GetChampionMastery" }, // *********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GetCompletedMilestones, "GetCompletedMilestones" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GetPendingRewards, "GetPendingRewards" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GetRealmMastery, "GetRealmMastery" }, // ********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_GrantReward, "GrantReward" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpAccount, "LevelUpAccount" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_LevelUpChampionMastery, "LevelUpChampionMastery" }, // *********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_LoadProgressionFromCloud, "LoadProgressionFromCloud" }, // **********
		{ &Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature, "OnAccountLevelUp__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature, "OnChampionMasteryLevelUp__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature, "OnMilestoneCompleted__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature, "OnProgressionSynced__DelegateSignature" }, // *********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_OnRep_PendingRewards, "OnRep_PendingRewards" }, // **********
		{ &Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature, "OnRewardGranted__DelegateSignature" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_RegisterTimeInRealm, "RegisterTimeInRealm" }, // **********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_RemoveGold, "RemoveGold" }, // 738631472
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_SaveProgressionToCloud, "SaveProgressionToCloud" }, // *********
		{ &Z_Construct_UFunction_UAuracronProgressionBridge_SyncWithFirebase, "SyncWithFirebase" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronProgressionBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_CurrentAccountProgression = { "CurrentAccountProgression", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, CurrentAccountProgression), Z_Construct_UScriptStruct_FAuracronAccountProgression, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAccountProgression_MetaData), NewProp_CurrentAccountProgression_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ChampionMasteries_Inner = { "ChampionMasteries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry, METADATA_PARAMS(0, nullptr) }; // **********
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ChampionMasteries = { "ChampionMasteries", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, ChampionMasteries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionMasteries_MetaData), NewProp_ChampionMasteries_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_RealmMasteries_Inner = { "RealmMasteries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry, METADATA_PARAMS(0, nullptr) }; // 4240946472
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_RealmMasteries = { "RealmMasteries", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, RealmMasteries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmMasteries_MetaData), NewProp_RealmMasteries_MetaData) }; // 4240946472
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ProgressionMilestones_Inner = { "ProgressionMilestones", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProgressionMilestone, METADATA_PARAMS(0, nullptr) }; // 2858283715
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ProgressionMilestones = { "ProgressionMilestones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, ProgressionMilestones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressionMilestones_MetaData), NewProp_ProgressionMilestones_MetaData) }; // 2858283715
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_PendingRewards_Inner = { "PendingRewards", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronReward, METADATA_PARAMS(0, nullptr) }; // 2703334020
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_PendingRewards = { "PendingRewards", "OnRep_PendingRewards", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, PendingRewards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PendingRewards_MetaData), NewProp_PendingRewards_MetaData) }; // 2703334020
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_LastCloudSync = { "LastCloudSync", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, LastCloudSync), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastCloudSync_MetaData), NewProp_LastCloudSync_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnAccountLevelUp = { "OnAccountLevelUp", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, OnAccountLevelUp), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAccountLevelUp_MetaData), NewProp_OnAccountLevelUp_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnChampionMasteryLevelUp = { "OnChampionMasteryLevelUp", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, OnChampionMasteryLevelUp), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChampionMasteryLevelUp_MetaData), NewProp_OnChampionMasteryLevelUp_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnMilestoneCompleted = { "OnMilestoneCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, OnMilestoneCompleted), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMilestoneCompleted_MetaData), NewProp_OnMilestoneCompleted_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnRewardGranted = { "OnRewardGranted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, OnRewardGranted), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRewardGranted_MetaData), NewProp_OnRewardGranted_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnProgressionSynced = { "OnProgressionSynced", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProgressionBridge, OnProgressionSynced), Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnProgressionSynced_MetaData), NewProp_OnProgressionSynced_MetaData) }; // *********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronProgressionBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_CurrentAccountProgression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ChampionMasteries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ChampionMasteries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_RealmMasteries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_RealmMasteries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ProgressionMilestones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_ProgressionMilestones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_PendingRewards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_PendingRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_LastCloudSync,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnAccountLevelUp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnChampionMasteryLevelUp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnMilestoneCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnRewardGranted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProgressionBridge_Statics::NewProp_OnProgressionSynced,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProgressionBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronProgressionBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronProgressionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProgressionBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronProgressionBridge_Statics::ClassParams = {
	&UAuracronProgressionBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronProgressionBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProgressionBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProgressionBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronProgressionBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronProgressionBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronProgressionBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronProgressionBridge.OuterSingleton, Z_Construct_UClass_UAuracronProgressionBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronProgressionBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronProgressionBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentAccountProgression(TEXT("CurrentAccountProgression"));
	static FName Name_ChampionMasteries(TEXT("ChampionMasteries"));
	static FName Name_RealmMasteries(TEXT("RealmMasteries"));
	static FName Name_PendingRewards(TEXT("PendingRewards"));
	const bool bIsValid = true
		&& Name_CurrentAccountProgression == ClassReps[(int32)ENetFields_Private::CurrentAccountProgression].Property->GetFName()
		&& Name_ChampionMasteries == ClassReps[(int32)ENetFields_Private::ChampionMasteries].Property->GetFName()
		&& Name_RealmMasteries == ClassReps[(int32)ENetFields_Private::RealmMasteries].Property->GetFName()
		&& Name_PendingRewards == ClassReps[(int32)ENetFields_Private::PendingRewards].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronProgressionBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronProgressionBridge);
UAuracronProgressionBridge::~UAuracronProgressionBridge() {}
// ********** End Class UAuracronProgressionBridge *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronProgressionType_StaticEnum, TEXT("EAuracronProgressionType"), &Z_Registration_Info_UEnum_EAuracronProgressionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4169099298U) },
		{ EAuracronRewardType_StaticEnum, TEXT("EAuracronRewardType"), &Z_Registration_Info_UEnum_EAuracronRewardType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2016650149U) },
		{ EAuracronRewardRarity_StaticEnum, TEXT("EAuracronRewardRarity"), &Z_Registration_Info_UEnum_EAuracronRewardRarity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3886300181U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronReward::StaticStruct, Z_Construct_UScriptStruct_FAuracronReward_Statics::NewStructOps, TEXT("AuracronReward"), &Z_Registration_Info_UScriptStruct_FAuracronReward, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronReward), 2703334020U) },
		{ FAuracronAccountProgression::StaticStruct, Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics::NewStructOps, TEXT("AuracronAccountProgression"), &Z_Registration_Info_UScriptStruct_FAuracronAccountProgression, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAccountProgression), **********U) },
		{ FAuracronChampionMastery::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics::NewStructOps, TEXT("AuracronChampionMastery"), &Z_Registration_Info_UScriptStruct_FAuracronChampionMastery, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionMastery), 1686980573U) },
		{ FAuracronRealmMastery::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics::NewStructOps, TEXT("AuracronRealmMastery"), &Z_Registration_Info_UScriptStruct_FAuracronRealmMastery, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmMastery), 1602401411U) },
		{ FAuracronProgressionMilestone::StaticStruct, Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics::NewStructOps, TEXT("AuracronProgressionMilestone"), &Z_Registration_Info_UScriptStruct_FAuracronProgressionMilestone, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronProgressionMilestone), 2858283715U) },
		{ FAuracronChampionMasteryEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics::NewStructOps, TEXT("AuracronChampionMasteryEntry"), &Z_Registration_Info_UScriptStruct_FAuracronChampionMasteryEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionMasteryEntry), **********U) },
		{ FAuracronRealmMasteryEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics::NewStructOps, TEXT("AuracronRealmMasteryEntry"), &Z_Registration_Info_UScriptStruct_FAuracronRealmMasteryEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmMasteryEntry), 4240946472U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronProgressionBridge, UAuracronProgressionBridge::StaticClass, TEXT("UAuracronProgressionBridge"), &Z_Registration_Info_UClass_UAuracronProgressionBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronProgressionBridge), 1447920258U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_3983262075(TEXT("/Script/AuracronProgressionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h__Script_AuracronProgressionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
