# 🌟 AURACRON - GAME DESIGN DOCUMENT _(formerly Nexus Realms)_
**Versão**: 1.0  
**Data**: 27 de Junho de 2025  
**Plataforma**: Mobile (Android/iOS) + PC  
**Engine**: Unity 6.2  

---

## 📋 **ÍNDICE**
1. [Visão Geral](#visão-geral)
2. [Análise Competitiva](#análise-competitiva)
3. [Mecânicas Inovadoras](#mecânicas-inovadoras)
4. [Sistemas Técnicos](#sistemas-técnicos)
5. [Progressão e Monetização](#progressão-e-monetização)
6. [Roadmap de Desenvolvimento](#roadmap-de-desenvolvimento)
7. [Análise de Riscos](#análise-de-riscos)

---

## 🎯 **VISÃO GERAL**

### **Conceito Central**
**AURACRON** é um MOBA 5v5 revolucionário que combina elementos tradicionais com **mapas dinâmicos multidimensionais** e **IA adaptativa**. O diferencial está na capacidade do mapa evoluir durante a partida, criando layers verticais de combate e objetivos procedurais únicos.

### **Público-Alvo**
- **Primário**: Players de MOBA mobile (18-35 anos)
- **Secundário**: mobile players buscando inovação no gênero
- **Terciário**: Streamers/criadores de conteúdo

### **Pillars de Design**
1. **📐 EVOLUÇÃO CONSTANTE**: Mapas que mudam, estratégias que adaptam
2. **🎮 ACESSIBILIDADE INTELIGENTE**: Complexo para mestres, simples para iniciantes
3. **🤝 COOPERAÇÃO AMPLIADA**: Mecânicas que recompensam teamwork criativo
4. **⚡ INOVAÇÃO TECNOLÓGICA**: IA, procedural generation, physics avançada

---

## ⚔️ **ANÁLISE COMPETITIVA**

### **Wild Rift vs AURACRON**

| **ASPECTO** | **WILD RIFT** | **AURACRON** | **VANTAGEM** |
|-------------|---------------|---------------|--------------|
| **Mapa** | Estático, 3 lanes fixas | Dinâmico, 3 realms evolutivos | 🟢 **NOSSA** |
| **Combate** | 2D horizontal | 3D vertical (3 layers) | 🟢 **NOSSA** |
| **Objectives** | Fixos e previsíveis | Procedurais + IA adaptativa | 🟢 **NOSSA** |
| **Champions** | 164 fixos | Sistema de fusão temporária | 🟢 **NOSSA** |
| **Jungle** | Spawn patterns fixos | IA que aprende e adapta | 🟢 **NOSSA** |
| **Reconhecimento** | ✅ Brand estabelecido | ❌ Brand novo | 🔴 **DELES** |
| **Player Base** | ✅ 50M+ players | ❌ Zero players | 🔴 **DELES** |
| **Recursos** | ✅ Budget Riot Games | ❌ Indie/Startup | 🔴 **DELES** |

### **Outros Competidores**
- **Mobile Legends**: Nosso diferencial é superior tecnicamente
- **Arena of Valor**: Inovação vs established gameplay
- **Heroes Evolved**: Superamos em todos os aspectos técnicos

---

## 🚀 **MECÂNICAS INOVADORAS**

### **1. DYNAMIC REALM SYSTEM** 🌍

#### **Como Funciona**
```
TIMELINE DA PARTIDA:
00:00-10:00 → REALM TERRESTRE
├── Mapa tradicional 3-lane
├── Jungle padrão com adaptação IA
└── Foco em laning phase clássico

10:00-15:00 → PORTAL PHASE
├── Portais dimensionais aparecem
├── Escolhas estratégicas de realm
└── Split decisions entre equipes

15:00-20:00 → REALM CELESTIAL
├── Plataformas flutuantes ativam
├── Air champions ganham vantagem
├── Nova layer de combate vertical
└── Objetivos aéreos únicos

20:00-25:00 → REALM ABYSSAL
├── Túneis subterrâneos abrem
├── Stealth champions dominam
├── Underground objectives
└── Rotas de flank secretas

25:00+ → NEXUS FUSION
├── Todos os realms simultâneos
├── Combate 3D completo
├── Máxima complexidade estratégica
└── Endgame épico e único
```

#### **Impacto Estratégico**
- **Early Game**: Foco em farming e positioning clássico
- **Mid Game**: Decisões de realm criam vantagens posicionais
- **Late Game**: Maestria 3D separa players casuais de pros

### **2. VERTICAL COMBAT LAYERS** ⬆️

#### **Surface Layer (Camada Terrestre)**
```cpp
// Exemplo de combate tradicional
void SurfaceAbility() {
    // Abilities funcionam como MOBA tradicional
    castRange = 800f;
    aoERadius = 300f;
    verticalRange = 0f; // Não afeta outras layers
}
```

#### **Sky Layer (Camada Celestial)**
```cpp
// Exemplo de ability aérea
void SkyAbility() {
    castRange = 1200f; // Maior alcance
    aoERadius = 400f;
    verticalRange = 500f; // Afeta layer abaixo
    canHitGroundTargets = true;
    groundDamageReduction = 0.7f; // 30% redução vs ground
}
```

#### **Underground Layer (Camada Subterrânea)**
```cpp
// Exemplo de ability subterrânea
void UndergroundAbility() {
    castRange = 600f; // Menor alcance
    aoERadius = 250f;
    stealthDuration = 3f; // Stealth natural
    canAmbushSurface = true;
    ambushDamageBonus = 1.5f; // 50% bonus damage
}
```

### **3. CHAMPION FUSION SYSTEM** 👥

#### **Mecânica Central**
- **jogadores podem fundir com outras classes(tank,mage,support,etc...)** podem temporariamente fundir seus champions
- **Duração**: 45 segundos
- **Cooldown**: 5 minutos (compartilhado)
- **Limitação**: Máximo 1 fusão ativa por equipe

#### **Tipos de Fusão**
```
TANK + MAGE = BATTLE-MAGE
├── HP Pool combinado
├── AP scaling em defensive abilities
├── Área de controle massiva
└── Exemplo: Malphite + Orianna = Graviton Colossus

ASSASSIN + SUPPORT = PHANTOM GUARDIAN
├── Stealth compartilhado para ally
├── Execution abilities com heal/shield
├── Alta mobilidade + utility
└── Exemplo: Zed + Lulu = Shadow Sprite

ADC + JUNGLE = APEX PREDATOR
├── DPS sustentado + burst windows
├── Objective control supremo
├── Map mobility + team fight presence
└── Exemplo: Jinx + Graves = Arsenal Beast
```

### **4. ADAPTIVE AI JUNGLE** 🤖

#### **Machine Learning Integration**
```python
# Pseudocódigo do sistema de IA
class AdaptiveJungle:
    def __init__(self):
        self.player_patterns = {}
        self.team_strategies = {}
        self.match_history = []
    
    def analyze_behavior(self, player_id, action_data):
        # Aprende padrões individuais
        self.player_patterns[player_id].update(action_data)
        
    def adapt_spawns(self, game_state):
        if self.detect_pattern("heavy_jungle_focus"):
            self.increase_camp_difficulty()
            self.spawn_counter_objectives()
        
        if self.detect_pattern("ignore_objectives"):
            self.create_forced_objectives()
            self.increase_objective_rewards()
    
    def predict_strategy(self, team_composition):
        # Prevê estratégia baseada em comp + historical data
        return self.strategy_neural_network.predict(team_composition)
```

#### **Adaptive Elements**
- **Camp Spawns**: Baseado em clear patterns
- **Objective Timing**: Adaptado ao ritmo da partida
- **Creature Behavior**: "Lembram" de encontros anteriores
- **Reward Scaling**: Balanceamento dinâmico baseado em performance

### **5. PROCEDURAL OBJECTIVES** 🎲

#### **Sistema de Geração**
```cpp
class ProceduralObjective {
    void GenerateObjective(GameState state) {
        float gameTime = state.gameTime;
        float killDifference = state.GetKillDifference();
        float goldDifference = state.GetGoldDifference();
        
        if (gameTime > 20 && killDifference > 10) {
            SpawnCatchUpObjective(); // Rubber band mechanic
        }
        
        if (gameTime < 15 && state.firstBloodTime < 120) {
            SpawnAggressionReward(); // Reward early fighting
        }
        
        if (state.teamFightsCount < 3 && gameTime > 18) {
            SpawnForceEngageObjective(); // Force team fights
        }
    }
}
```

#### **Tipos de Objetivos Procedurais**
1. **Nexus Fragments**: Scattered mini-objectives que buildam para major buff
2. **Temporal Rifts**: Permite "rewind" de 10 segundos em área específica
3. **Realm Anchors**: Controlam qual realm está ativo
4. **Fusion Catalysts**: Reduzem cooldown de champion fusion
5. **Vertical Bridges**: Conectam temporariamente as layers

---

## 🔧 **SISTEMAS TÉCNICOS**

### **Engine & Frameworks**
```
🛠️ TECH STACK:
├── Unity 6.2 (Core Engine)
│   ├── HDRP (Visual fidelity)
│   ├── Addressables (Content streaming)
│   ├── Unity NetCode (Multiplayer)
│   └── ML-Agents (AI Jungle)
├── Backend Services
│   ├── Mirror Networking (Authoritative server)
│   ├── Firebase (User data, analytics)
│   ├── Unity Cloud Build (CI/CD)
│   └── Vivox (Voice communication)
├── Platform Integration
│   ├── Google Play Games (Android achievements)
│   ├── Game Center (iOS achievements)
│   └── Steam (PC distribution)
└── Analytics & Balancing
    ├── Unity Analytics (Player behavior)
    ├── Custom telemetry (Balance data)
    └── A/B Testing framework
```

### **Performance Targets**
| **Platform** | **FPS** | **Resolution** | **Memory** | **Storage** |
|--------------|---------|----------------|------------|-------------|
| **Flagship Mobile** | 60 FPS | 1080p+ | <4GB RAM | <8GB |
| **Mid-range Mobile** | 30 FPS | 720p | <3GB RAM | <6GB |
| **PC** | 120 FPS | 1440p+ | <8GB RAM | <15GB |

### **Networking Architecture**
```cpp
// Authoritative server para evitar cheating
class NetworkedGameState {
    [ServerOnly]
    void ValidatePlayerAction(PlayerAction action) {
        if (IsValidAction(action)) {
            ApplyAction(action);
            BroadcastToClients(action);
        } else {
            LogSuspiciousActivity(action.playerId);
        }
    }
    
    [ClientRpc]
    void UpdateGameState(GameStateSnapshot snapshot) {
        localGameState.ApplySnapshot(snapshot);
        PredictFutureStates(); // Client-side prediction
    }
}
```

---

## 💰 **PROGRESSÃO E MONETIZAÇÃO**

### **Modelo de Monetização Ética**

#### **Battle Pass Evoluído**
```
🎁 ADAPTIVE BATTLE PASS:
├── Traditional Track (Linear progression)
├── Role-Specific Tracks (Tank, DPS, Support, Jungle, Mid)
├── Playstyle Tracks (Aggressive, Defensive, Strategic)
└── Community Tracks (Unlocked via community goals)

EXEMPLO:
Player joga principalmente Support → Unlock Support Track
├── Exclusive support champion skins
├── Ward customizations
├── Healing/Shield VFX variants
└── Support-specific emotes & voice lines
```

#### **Champion Acquisition**
- **Free Rotation**: 15 champions/week (vs 10 do Wild Rift)
- **Earn Rate**: 1 novo champion/semana jogando casual
- **Currency**: Blue Essence (earned) + Realm Crystals (premium)
- **No P2W**: Champions purchasable apenas com earned currency

#### **Cosmetics Premium**
```
✨ FUNCTIONAL COSMETICS:
├── Champion Skins
│   ├── Model changes
│   ├── VFX customization
│   ├── Voice pack variations
│   └── Fusion appearance alterations
├── Map Themes (Community votes)
│   ├── Seasonal realm appearances
│   ├── Weather effects
│   └── Ambient sound packs
└── Customizable Elements
    ├── Ability particle colors
    ├── Recall animations
    └── Victory/Defeat celebrations
```

### **Progression Systems**

#### **Account Level (1-500)**
```
MILESTONES:
Level 10 → Ranked mode unlock
Level 25 → Champion fusion unlock
Level 50 → Realm mastery tracking
Level 100 → Custom lobby creation
Level 200 → Beta tester privileges
Level 500 → Legendary status + unique rewards
```

#### **Champion Mastery (1-10)**
```
PER CHAMPION PROGRESSION:
Mastery 1-3 → Basic cosmetic rewards
Mastery 4-6 → Advanced skin chromas
Mastery 7-8 → Exclusive emotes + animations
Mastery 9-10 → Champion title + border
Mastery 10 → Custom ability names + rare rewards
```

#### **Realm Mastery (New System)**
```
REALM-SPECIFIC PROGRESSION:
Terrestrial Realm → Ground combat expertise
├── Laning phase bonus XP
├── Jungle clear efficiency
└── Ground-based objective priority

Celestial Realm → Aerial combat mastery
├── Sky layer damage bonus
├── Vertical positioning awareness
└── Flying unit synergy

Abyssal Realm → Underground expertise
├── Stealth detection radius
├── Tunnel navigation speed
└── Ambush damage multiplier
```

---

## 📈 **ROADMAP DE DESENVOLVIMENTO**

### **FASE 1: FOUNDATION (Meses 1-6)**
```
🏗️ CORE SYSTEMS:
├── ✅ Unity 6.2 setup
├── ✅ Basic MOBA mechanics
├── ✅ Networking foundation
├── ⏳ Champion system architecture
├── ⏳ Basic UI/UX
└── ⏳ Single realm prototype

DELIVERABLES:
- Playable vertical slice (1 realm, 5v5)
- Core champion abilities working
- Basic progression system
- Alpha build for internal testing
```

### **FASE 2: INNOVATION CORE (Meses 7-14)**
```
🚀 UNIQUE FEATURES:
├── ⏳ Dynamic realm transitions
├── ⏳ Vertical combat layers
├── ⏳ Champion fusion system
├── ⏳ AI adaptive jungle
├── ⏳ Procedural objectives
└── ⏳ Advanced networking

DELIVERABLES:
- All core innovations implemented
- 20 champions with fusion compatibility
- Closed beta with 1000 players
- Balancing data collection system
```

### **FASE 3: POLISH & LAUNCH (Meses 15-18)**
```
⚡ FINALIZATION:
├── ⏳ Performance optimization
├── ⏳ UI/UX refinement
├── ⏳ Mobile platform optimization
├── ⏳ Monetization integration
├── ⏳ Anti-cheat systems
└── ⏳ Launch marketing campaign

DELIVERABLES:
- Production-ready build
- 50+ champions at launch
- Comprehensive tutorial system
- Global release (soft launch → worldwide)
```

### **FASE 4: POST-LAUNCH (Meses 19+)**
```
🔄 LIVE SERVICE:
├── ⏳ Seasonal content updates
├── ⏳ New realm introductions
├── ⏳ Esports infrastructure
├── ⏳ Community features
└── ⏳ Platform expansion (PC, Console)

DELIVERABLES:
- Quarterly major updates
- Monthly champion releases
- Annual realm expansions
- Competitive scene establishment
```

---

## ⚠️ **ANÁLISE DE RISCOS**

### **RISCOS TÉCNICOS**

#### **🔴 ALTO RISCO**
1. **Complexidade 3D em Mobile**
   - **Problema**: Performance em devices low-end
   - **Solução**: Scalable quality settings, 2D fallback mode
   - **Mitigation**: Extensive device testing, cloud gaming option

2. **Networking Complexity**
   - **Problema**: 3D positioning sync, realm transitions
   - **Solução**: Predictive networking, authoritative server
   - **Mitigation**: Dedicated server infrastructure, regional servers

#### **🟡 MÉDIO RISCO**
1. **AI Jungle Balance**
   - **Problema**: IA muito forte/fraca, exploits
   - **Solução**: Extensive playtesting, gradual learning
   - **Mitigation**: Manual override system, community feedback

2. **Champion Fusion Balance**
   - **Problema**: Combinations OP, meta stagnation
   - **Solução**: Data-driven nerfs/buffs, rotation restrictions
   - **Mitigation**: Regular balance patches, pro player input

### **RISCOS DE MERCADO**

#### **🔴 ALTO RISCO**
1. **Competição com Wild Rift**
   - **Problema**: Brand recognition, established playerbase
   - **Solução**: Focus em inovação, influencer partnerships
   - **Mitigation**: Unique value proposition, superior tech

2. **Monetização Sustentável**
   - **Problema**: F2P market saturation, whale dependency
   - **Solução**: Ethical monetization, broad appeal cosmetics
   - **Mitigation**: Multiple revenue streams, community support

#### **🟡 MÉDIO RISCO**
1. **Player Adoption**
   - **Problema**: Learning curve das inovações
   - **Solução**: Gradual feature introduction, excellent tutorials
   - **Mitigation**: Progressive complexity, casual modes

---

## 🎯 **MÉTRICAS DE SUCESSO**

### **KPIs Pré-Launch**
- **Alpha Retention**: >40% D7, >20% D30
- **Beta Feedback Score**: >4.2/5.0
- **Performance**: 60fps em 80% dos devices testados
- **Bug Reports**: <5 critical bugs per build

### **KPIs Post-Launch**
- **DAU**: 100K+ em 6 meses
- **Player Retention**: >30% D30, >10% D90
- **Revenue**: $1M+ revenue em ano 1
- **Community**: 50K+ Discord members, 100K+ Reddit

### **KPIs Long-term**
- **Esports**: Tournament com $100K+ prize pool
- **Global Reach**: Lançamento em 10+ países
- **Platform Expansion**: PC/Console versions
- **Brand Recognition**: Top 10 mobile MOBA rankings

### 🔄 **Atualização 29/06/2025 – Refinamentos de Design**

> Resultado de pesquisa interna + benchmarking (Inworld AI, Riot UX, BBC GEL) e discussões de equipe.

#### 1. Essência do Produto
- **Mapa Dinâmico ✔️** continua sendo o diferencial, mas agora as transições de realm começam como "sub-zonas" experimentais antes da mudança global (reduz sobrecarga cognitiva).
- **Fusão de Campeões 🔧** passa a ter apenas 3 arquétipos por temporada (ex.: Tank + Mage, Assassin + Support, ADC + Jungle) para simplificar balanceamento.
- **Objetivos Procedurais 🎲** divididos em "Core" (sempre presentes) e "Catch-up" (ativados quando uma equipe está >10 % atrás em ouro/kills).

#### 2. Onboarding & Acessibilidade
- **Progressive Onboarding**: tutorial de 5 min na Surface Layer → PVE "Academy Realm" → primeira partida real com IA fraca.
- **AI Mentor (Navigator Drone)**: NPC de voz/ícones que responde dúvidas e faz *reboarding* de jogadores ausentes.
- **DDA Light**: ajuste dinâmico de dano recebido nos 10 primeiros níveis de conta.

#### 3. Comunidade Saudável
- Chat/voice **opt-in desativado** até nível 10.
- **Bônus de honra coletiva**: todo o time recebe +5 % XP se ninguém for mutado/reportado.
- Detecção em tempo real de linguagem tóxica ⇒ silenciamento automático 15 min.

#### 4. Roadmap 2.0 (iterativo)
| Fase | Janela | Principais Entregas |
|------|--------|---------------------|
| **0 – POC** | M1-M3 | Grey-box 1 lane vertical, 6 campeões, perfil de CPU/GPU |
| **1 – Vertical Slice** | M4-M8 | Transição Terra→Céu, fusão Tank+Mage, tutorial completo |
| **2 – Closed Beta** | M9-M15 | Realm Abyssal, honor system, 20 campeões, soft-launch regional |
| **3 – Launch** | M16-M18 | Monetização ética, spectator, 50+ campeões, campanha de marketing |

#### 5. Próximos Passos Imediatos
1. Prototipar sub-zona de transição "Portal Preview".
2. Implementar protótipo de AI Mentor usando Unity Sentis 2.1.
3. Ajustar GDD de habilidades para arquétipos de fusão reduzidos.

### 🔄 **Atualização 30/06/2025 – Identidade de Mapas & Narrativa**

> Esta revisão elimina nomenclaturas populares de outros MOBAs e aprofunda as mecânicas de lore in-game.

#### A. Estrutura de Camadas (nomenclatura própria)
| Camada | Nome Interno | Função | Objetivos Exclusivos |
|--------|--------------|--------|----------------------|
| I | **Planície Radiante** | Campo base, acessível; três Rotas (Trilho Solar, Trilho Axis, Trilho Lunar) com vegetação "*Canopy*" para emboscadas. | Guardião Prismal (empurra rota) • Torre Prisma (aura de dano em área) |
| II | **Firmamento Zephyr** | Plataformas flutuantes a 70 m; domínio de visão vertical. | Núcleo de Tempestade (buff ofensivo) • Santuários dos Ventos (reduz recarga de mobilidade) |
| III | **Abismo Umbrio** | Rede de túneis bioluminescentes; foco em furtividade. | Leviatã Umbrático (lifesteal + penetração) • Altares da Sombra (porta unidirecional) |

*Nota:* Rotas usam "Trilho" em vez de *lane*; vegetação é *Canopy*; sentinelas de visão são **Balizas** em vez de *wards*.

#### B. Conectores Verticais
1. **Portais de Ânima** – permanentes nas bases.
2. **Fendas Fluxo** – rasgam o solo e permitem descida/ascensão temporária.
3. **Cipós Astria** – cordas vegetais escaláveis.
4. **Elevadores de Vórtice** – colunas de vento que transportam tropas e heróis.

#### C. Sistema de Lore Dinâmico (versão condensada)
1. **Fragmentos de Crônica** – dropam após marcos (1ª torre, 1ª fusão, etc.); coletados ➜ revelam trechos de história no **Codex Nexus**.
2. **Vínculos Ocultos** – duplas de heróis concedem fala + buff de 2 % velocidade por 8 s (ex.: Irmãos Astria). Válido apenas se estiverem a ≤900u.
3. **Missões de Temporada** – desafios semanais (Ex.: Caçar 5 Relíquias Etéreas) que liberam variante de cor de skin.
4. **Eco de Fusão** – primeira fusão de par específico grava memória no Codex e concede ícone exclusivo.

#### D. Terminologia Padronizada (anti-confusão / anti-trademark)
| Antigo termo | Novo termo Nexus |
|--------------|------------------|
| Lane | Trilho |
| Brush | Canopy |
| Ward | Baliza |
| River | Fluxo |
| Baron/Herald | Guardião Prismal / Núcleo de Tempestade |
| Dragon | Leviatã Umbrático |

> Todos os asset labels, VO e UI devem utilizar APENAS os termos Nexus listados acima para preservar identidade única.

---

## 📞 **PRÓXIMOS PASSOS**

### **IMEDIATOS (1-2 semanas)**
1. **📋 Refinar este documento** baseado em feedback
2. **👥 Formar core team** (Lead Designer, Tech Lead, Artist Lead)
3. **💰 Preparar pitch deck** para investidores/publishers
4. **🔬 Research aprofundado** de mercado e competição

### **CURTO PRAZO (1-3 meses)**
1. **🎮 Protótipo vertical** de realm transition
2. **🤖 PoC do sistema de IA** adaptativa
3. **📊 Validar interesse** via surveys/focus groups
4. **🏢 Buscar funding** inicial para desenvolvimento

### **MÉDIO PRAZO (3-6 meses)**
1. **⚡ Alpha build** com todas as inovações core
2. **🧪 Playtesting** extensivo com target audience
3. **📈 Iteração** baseada em feedback e dados
4. **🎨 Finalizar art direction** e brand identity

---

## 📝 **CONCLUSÃO**

**AURACRON** representa uma evolução natural do gênero MOBA, mantendo a acessibilidade que tornou Wild Rift popular enquanto introduz inovações significativas que podem redefinir o competitive gaming mobile.

**Diferenciais Únicos:**
- ✅ Mapas dinâmicos que evoluem durante a partida
- ✅ Combate 3D vertical em plataforma mobile
- ✅ IA adaptativa que aprende e desafia players
- ✅ Sistema de fusão que recompensa teamwork
- ✅ Objetivos procedurais únicos a cada match

**Desafios Principais:**
- ⚠️ Complexidade técnica vs performance mobile
- ⚠️ Balanceamento de mecânicas inovadoras
- ⚠️ Competição com IPs estabelecidos
- ⚠️ Sustainable monetization em mercado saturado

**Potencial de Sucesso:**
Com execução adequada e recursos suficientes, **AURACRON** tem potencial para se tornar o "next-gen MOBA" que a comunidade busca, oferecendo profundidade estratégica sem sacrificar acessibilidade.

---

*Este documento é um living document e será atualizado conforme o desenvolvimento do conceito e feedback da equipe.*

### 🔄 **Atualização 05/07/2025 – Rebrand & Fusion 2.0 (Sigil System)**

#### 1. Rebrand Global
- Codename/brand oficial passa a ser **AURACRON**; referências a "Nexus Realms" ficam como nome de engine/lore interno.
- Tagline de marketing: _"Domine as três camadas. Desperte o Auracron."_
- Domínios reservados: auracron.com / auracron.gg / auracron.game.

#### 2. Fusion 2.0 → Sistema de **SÍGILOS**
- A fusão deixa de exigir dois jogadores.
- Durante a **tela de seleção de campeões**, cada player escolhe **1 de 3 "Sigilos Auracron"** (Tank, Damage, Utility).
- O Sigilo funde-se ao campeão aos 6 min, desbloqueando árvore de habilidades alternativa.
- Pode ser re-forjado no Nexus uma vez por partida (cooldown global de 2 min).
- Cria combinatória de 50 campeões × 3 Sigilos = 150 arquétipos sem depender de cooperação específica.

| Sigilo | Bônus Passivo | Habilidade Exclusiva | Arquétipo-chave |
|--------|--------------|----------------------|-----------------|
| **Aegis** (Tank) | +15% HP, Armadura adaptativa | "Murallion" – cria barreira circular 3s | Frontliner / Iniciado |
| **Ruin** (Damage) | +12% ATK / AP adaptativo | "Fracasso Prismal" – reset parcial de CD | Burst / Skirmisher |
| **Vesper** (Utility) | +10% Vel. Move + 8% Cooldown | "Sopro de Fluxo" – dash aliado + shield | Roamer / Suporte |

#### 3. Impacto em Balanceamento
- Remove gargalo de _matchmaking_ de fusão 2-jogadores.
- Incentiva expressão individual (paralelo às Runas de LoL).
- Mantém identidade "fusão" como power-spike temático.

#### 4. Roadmap Ajustado
- **Fase 2**: Implementar Sigilos em todos os 20 champions alvo.
- **Fase 3**: Ferramenta de telemetria para taxa de escolha / win-rate de Sigilos.

> *Nota: Documentação futura deve substituir referências a "Champion Fusion" por "Sigilo Auracron".* 