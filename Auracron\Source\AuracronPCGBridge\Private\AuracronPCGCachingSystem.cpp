// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Caching System Implementation
// Bridge 2.17: PCG Framework - Caching System

#include "AuracronPCGCachingSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/PlatformMemory.h"
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/SecureHash.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Serialization/ObjectAndNameAsStringProxyArchive.h"

// =============================================================================
// CACHE KEY IMPLEMENTATION
// =============================================================================

FString FAuracronPCGCacheKey::GenerateHash() const
{
    FString CombinedString = FString::Printf(TEXT("%s_%s_%s_%s_%d_%d"), 
        *NodeId, *GraphId, *ParametersHash, *InputDataHash, (int32)EntryType, Version);
    
    return FMD5::HashAnsiString(*CombinedString);
}

bool FAuracronPCGCacheKey::operator==(const FAuracronPCGCacheKey& Other) const
{
    return NodeId == Other.NodeId &&
           GraphId == Other.GraphId &&
           ParametersHash == Other.ParametersHash &&
           InputDataHash == Other.InputDataHash &&
           EntryType == Other.EntryType &&
           Version == Other.Version;
}

bool FAuracronPCGCacheKey::operator!=(const FAuracronPCGCacheKey& Other) const
{
    return !(*this == Other);
}

bool FAuracronPCGCacheKey::operator<(const FAuracronPCGCacheKey& Other) const
{
    if (NodeId != Other.NodeId) return NodeId < Other.NodeId;
    if (GraphId != Other.GraphId) return GraphId < Other.GraphId;
    if (ParametersHash != Other.ParametersHash) return ParametersHash < Other.ParametersHash;
    if (InputDataHash != Other.InputDataHash) return InputDataHash < Other.InputDataHash;
    if (EntryType != Other.EntryType) return EntryType < Other.EntryType;
    return Version < Other.Version;
}

uint32 GetTypeHash(const FAuracronPCGCacheKey& Key)
{
    return GetTypeHash(Key.GenerateHash());
}

// =============================================================================
// CACHE ENTRY IMPLEMENTATION
// =============================================================================

bool FAuracronPCGCacheEntry::IsExpired() const
{
    return FDateTime::Now() > ExpirationTime;
}

void FAuracronPCGCacheEntry::UpdateAccess()
{
    LastAccessTime = FDateTime::Now();
    AccessCount++;
}

void FAuracronPCGCacheEntry::Serialize(FArchive& Ar)
{
    Ar << Key.NodeId;
    Ar << Key.GraphId;
    Ar << Key.ParametersHash;
    Ar << Key.InputDataHash;
    Ar << Key.EntryType;
    Ar << Key.Version;
    
    Ar << SerializedData;
    Ar << CreationTime;
    Ar << LastAccessTime;
    Ar << ExpirationTime;
    Ar << AccessCount;
    Ar << DataSizeBytes;
    Ar << bIsCompressed;
    Ar << bIsEncrypted;
    Ar << bIsPersistent;
    Ar << ComputationTimeSeconds;
    Ar << Dependencies;
    Ar << Metadata;
}

// =============================================================================
// CACHE STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronPCGCacheStatistics::UpdateCalculatedFields()
{
    int32 TotalAccesses = TotalHits + TotalMisses;
    if (TotalAccesses > 0)
    {
        HitRatio = static_cast<float>(TotalHits) / static_cast<float>(TotalAccesses);
        MissRatio = static_cast<float>(TotalMisses) / static_cast<float>(TotalAccesses);
    }
    else
    {
        HitRatio = 0.0f;
        MissRatio = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// DEPENDENCY TRACKER IMPLEMENTATION
// =============================================================================

void UAuracronPCGDependencyTracker::AddDependency(const FString& DependentId, const FString& DependencyId)
{
    FScopeLock Lock(&DependencyLock);
    
    Dependencies.FindOrAdd(DependentId).Add(DependencyId);
    Dependents.FindOrAdd(DependencyId).Add(DependentId);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Added dependency: %s -> %s"), *DependentId, *DependencyId);
}

void UAuracronPCGDependencyTracker::RemoveDependency(const FString& DependentId, const FString& DependencyId)
{
    FScopeLock Lock(&DependencyLock);
    
    if (TSet<FString>* DependencySet = Dependencies.Find(DependentId))
    {
        DependencySet->Remove(DependencyId);
        if (DependencySet->Num() == 0)
        {
            Dependencies.Remove(DependentId);
        }
    }
    
    if (TSet<FString>* DependentSet = Dependents.Find(DependencyId))
    {
        DependentSet->Remove(DependentId);
        if (DependentSet->Num() == 0)
        {
            Dependents.Remove(DependencyId);
        }
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Removed dependency: %s -> %s"), *DependentId, *DependencyId);
}

void UAuracronPCGDependencyTracker::RemoveAllDependencies(const FString& NodeId)
{
    FScopeLock Lock(&DependencyLock);
    
    // Remove all dependencies of this node
    if (TSet<FString>* DependencySet = Dependencies.Find(NodeId))
    {
        for (const FString& DependencyId : *DependencySet)
        {
            if (TSet<FString>* DependentSet = Dependents.Find(DependencyId))
            {
                DependentSet->Remove(NodeId);
                if (DependentSet->Num() == 0)
                {
                    Dependents.Remove(DependencyId);
                }
            }
        }
        Dependencies.Remove(NodeId);
    }
    
    // Remove all dependents of this node
    if (TSet<FString>* DependentSet = Dependents.Find(NodeId))
    {
        for (const FString& DependentId : *DependentSet)
        {
            if (TSet<FString>* DependencySet = Dependencies.Find(DependentId))
            {
                DependencySet->Remove(NodeId);
                if (DependencySet->Num() == 0)
                {
                    Dependencies.Remove(DependentId);
                }
            }
        }
        Dependents.Remove(NodeId);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Removed all dependencies for node: %s"), *NodeId);
}

TArray<FString> UAuracronPCGDependencyTracker::GetDependencies(const FString& NodeId) const
{
    FScopeLock Lock(&DependencyLock);
    
    const TSet<FString>* DependencySet = Dependencies.Find(NodeId);
    if (DependencySet)
    {
        return DependencySet->Array();
    }
    
    return TArray<FString>();
}

TArray<FString> UAuracronPCGDependencyTracker::GetDependents(const FString& NodeId) const
{
    FScopeLock Lock(&DependencyLock);
    
    const TSet<FString>* DependentSet = Dependents.Find(NodeId);
    if (DependentSet)
    {
        return DependentSet->Array();
    }
    
    return TArray<FString>();
}

TArray<FString> UAuracronPCGDependencyTracker::GetAllDependencies(const FString& NodeId, bool bRecursive) const
{
    FScopeLock Lock(&DependencyLock);
    
    TArray<FString> Result;
    if (bRecursive)
    {
        TSet<FString> VisitedNodes;
        GetAllDependenciesRecursive(NodeId, VisitedNodes, Result);
    }
    else
    {
        Result = GetDependencies(NodeId);
    }
    
    return Result;
}

TArray<FString> UAuracronPCGDependencyTracker::GetAllDependents(const FString& NodeId, bool bRecursive) const
{
    FScopeLock Lock(&DependencyLock);
    
    TArray<FString> Result;
    if (bRecursive)
    {
        TSet<FString> VisitedNodes;
        GetAllDependentsRecursive(NodeId, VisitedNodes, Result);
    }
    else
    {
        Result = GetDependents(NodeId);
    }
    
    return Result;
}

bool UAuracronPCGDependencyTracker::HasDependency(const FString& DependentId, const FString& DependencyId) const
{
    FScopeLock Lock(&DependencyLock);
    
    const TSet<FString>* DependencySet = Dependencies.Find(DependentId);
    return DependencySet && DependencySet->Contains(DependencyId);
}

bool UAuracronPCGDependencyTracker::HasCircularDependency(const FString& NodeId) const
{
    FScopeLock Lock(&DependencyLock);
    
    TSet<FString> VisitedNodes;
    TSet<FString> RecursionStack;
    return HasCircularDependencyRecursive(NodeId, VisitedNodes, RecursionStack);
}

TArray<FString> UAuracronPCGDependencyTracker::DetectCircularDependencies() const
{
    FScopeLock Lock(&DependencyLock);
    
    TArray<FString> CircularNodes;
    TSet<FString> GlobalVisited;
    
    for (const auto& NodePair : Dependencies)
    {
        const FString& NodeId = NodePair.Key;
        if (!GlobalVisited.Contains(NodeId))
        {
            TSet<FString> VisitedNodes;
            TSet<FString> RecursionStack;
            if (HasCircularDependencyRecursive(NodeId, VisitedNodes, RecursionStack))
            {
                CircularNodes.Add(NodeId);
            }
            GlobalVisited.Append(VisitedNodes);
        }
    }
    
    return CircularNodes;
}

TArray<FString> UAuracronPCGDependencyTracker::GetTopologicalOrder() const
{
    FScopeLock Lock(&DependencyLock);
    
    TArray<FString> Result;
    TSet<FString> VisitedNodes;
    TSet<FString> TempMarked;
    
    // Kahn's algorithm for topological sorting
    TMap<FString, int32> InDegree;
    
    // Calculate in-degrees
    for (const auto& NodePair : Dependencies)
    {
        const FString& NodeId = NodePair.Key;
        InDegree.FindOrAdd(NodeId, 0);
        
        for (const FString& DependencyId : NodePair.Value)
        {
            InDegree.FindOrAdd(DependencyId, 0);
            InDegree[NodeId]++;
        }
    }
    
    // Find nodes with no incoming edges
    TArray<FString> Queue;
    for (const auto& DegreePair : InDegree)
    {
        if (DegreePair.Value == 0)
        {
            Queue.Add(DegreePair.Key);
        }
    }
    
    // Process queue
    while (Queue.Num() > 0)
    {
        FString CurrentNode = Queue[0];
        Queue.RemoveAt(0);
        Result.Add(CurrentNode);
        
        // Reduce in-degree of dependent nodes
        const TSet<FString>* DependentSet = Dependents.Find(CurrentNode);
        if (DependentSet)
        {
            for (const FString& DependentId : *DependentSet)
            {
                InDegree[DependentId]--;
                if (InDegree[DependentId] == 0)
                {
                    Queue.Add(DependentId);
                }
            }
        }
    }
    
    return Result;
}

int32 UAuracronPCGDependencyTracker::GetDependencyDepth(const FString& NodeId) const
{
    FScopeLock Lock(&DependencyLock);
    
    TSet<FString> VisitedNodes;
    return GetDependencyDepthRecursive(NodeId, VisitedNodes);
}

void UAuracronPCGDependencyTracker::GetAllDependenciesRecursive(const FString& NodeId, TSet<FString>& VisitedNodes, TArray<FString>& Result) const
{
    if (VisitedNodes.Contains(NodeId))
    {
        return;
    }
    
    VisitedNodes.Add(NodeId);
    
    const TSet<FString>* DependencySet = Dependencies.Find(NodeId);
    if (DependencySet)
    {
        for (const FString& DependencyId : *DependencySet)
        {
            Result.AddUnique(DependencyId);
            GetAllDependenciesRecursive(DependencyId, VisitedNodes, Result);
        }
    }
}

void UAuracronPCGDependencyTracker::GetAllDependentsRecursive(const FString& NodeId, TSet<FString>& VisitedNodes, TArray<FString>& Result) const
{
    if (VisitedNodes.Contains(NodeId))
    {
        return;
    }
    
    VisitedNodes.Add(NodeId);
    
    const TSet<FString>* DependentSet = Dependents.Find(NodeId);
    if (DependentSet)
    {
        for (const FString& DependentId : *DependentSet)
        {
            Result.AddUnique(DependentId);
            GetAllDependentsRecursive(DependentId, VisitedNodes, Result);
        }
    }
}

bool UAuracronPCGDependencyTracker::HasCircularDependencyRecursive(const FString& NodeId, TSet<FString>& VisitedNodes, TSet<FString>& RecursionStack) const
{
    if (RecursionStack.Contains(NodeId))
    {
        return true; // Circular dependency detected
    }
    
    if (VisitedNodes.Contains(NodeId))
    {
        return false; // Already processed
    }
    
    VisitedNodes.Add(NodeId);
    RecursionStack.Add(NodeId);
    
    const TSet<FString>* DependencySet = Dependencies.Find(NodeId);
    if (DependencySet)
    {
        for (const FString& DependencyId : *DependencySet)
        {
            if (HasCircularDependencyRecursive(DependencyId, VisitedNodes, RecursionStack))
            {
                return true;
            }
        }
    }
    
    RecursionStack.Remove(NodeId);
    return false;
}

int32 UAuracronPCGDependencyTracker::GetDependencyDepthRecursive(const FString& NodeId, TSet<FString>& VisitedNodes) const
{
    if (VisitedNodes.Contains(NodeId))
    {
        return 0; // Avoid infinite recursion
    }
    
    VisitedNodes.Add(NodeId);
    
    int32 MaxDepth = 0;
    const TSet<FString>* DependencySet = Dependencies.Find(NodeId);
    if (DependencySet)
    {
        for (const FString& DependencyId : *DependencySet)
        {
            int32 Depth = GetDependencyDepthRecursive(DependencyId, VisitedNodes) + 1;
            MaxDepth = FMath::Max(MaxDepth, Depth);
        }
    }
    
    VisitedNodes.Remove(NodeId);
    return MaxDepth;
}

// =============================================================================
// CACHE MANAGER IMPLEMENTATION
// =============================================================================

UAuracronPCGCacheManager* UAuracronPCGCacheManager::Instance = nullptr;

UAuracronPCGCacheManager* UAuracronPCGCacheManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGCacheManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronPCGCacheManager::Initialize(const FAuracronPCGCacheConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Cache Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;

    // Initialize subsystems
    DependencyTracker = NewObject<UAuracronPCGDependencyTracker>();
    CacheInvalidator = NewObject<UAuracronPCGCacheInvalidator>();
    PersistentStorage = NewObject<UAuracronPCGPersistentStorage>();

    // Configure persistent storage
    PersistentStorage->SetStorageDirectory(Configuration.DiskCacheDirectory);
    PersistentStorage->SetCompressionEnabled(Configuration.bCompressDiskCache);
    PersistentStorage->SetEncryptionEnabled(Configuration.bEncryptDiskCache);

    // Initialize statistics
    Statistics = FAuracronPCGCacheStatistics();

    bIsInitialized = true;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cache Manager initialized with %d MB memory limit"), Configuration.MaxMemoryCacheSizeMB);
}

void UAuracronPCGCacheManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Save persistent cache entries
    if (Configuration.StorageType == EAuracronPCGCacheStorageType::Disk ||
        Configuration.StorageType == EAuracronPCGCacheStorageType::Hybrid)
    {
        TArray<FAuracronPCGCacheEntry> PersistentEntries;
        for (const auto& CachePair : MemoryCache)
        {
            if (CachePair.Value.bIsPersistent)
            {
                PersistentEntries.Add(CachePair.Value);
            }
        }

        if (PersistentEntries.Num() > 0)
        {
            PersistentStorage->SaveEntries(PersistentEntries);
            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Saved %d persistent cache entries"), PersistentEntries.Num());
        }
    }

    // Clear memory cache
    MemoryCache.Empty();
    AccessTimes.Empty();

    bIsInitialized = false;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cache Manager shutdown"));
}

bool UAuracronPCGCacheManager::IsInitialized() const
{
    return bIsInitialized;
}

bool UAuracronPCGCacheManager::GetCachedResult(const FAuracronPCGCacheKey& Key, TArray<uint8>& OutData)
{
    if (!bIsInitialized || !Configuration.bEnableCaching)
    {
        return false;
    }

    FDateTime StartTime = FDateTime::Now();

    FAuracronPCGCacheEntry Entry;
    bool bFound = false;

    // Try memory cache first
    if (GetFromMemoryCache(Key, Entry))
    {
        bFound = true;
        AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Cache hit (memory): %s"), *Key.GenerateHash());
    }
    // Try disk cache if not in memory
    else if ((Configuration.StorageType == EAuracronPCGCacheStorageType::Disk ||
              Configuration.StorageType == EAuracronPCGCacheStorageType::Hybrid) &&
             GetFromDiskCache(Key, Entry))
    {
        bFound = true;
        // Move to memory cache for faster access
        SetToMemoryCache(Key, Entry);
        AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Cache hit (disk): %s"), *Key.GenerateHash());
    }

    if (bFound && !Entry.IsExpired())
    {
        OutData = Entry.SerializedData;
        Entry.UpdateAccess();

        // Update access time
        {
            FScopeLock Lock(&CacheLock);
            AccessTimes.Add(Key, FDateTime::Now());
        }

        float AccessTime = (FDateTime::Now() - StartTime).GetTotalSeconds();
        UpdateStatistics(true, AccessTime);

        OnCacheHit.Broadcast(Key, AccessTime);
        return true;
    }
    else
    {
        if (bFound && Entry.IsExpired())
        {
            // Remove expired entry
            RemoveCachedResult(Key);
            AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Cache entry expired: %s"), *Key.GenerateHash());
        }

        float AccessTime = (FDateTime::Now() - StartTime).GetTotalSeconds();
        UpdateStatistics(false, AccessTime);

        OnCacheMiss.Broadcast(Key, 0.0f);
        return false;
    }
}

bool UAuracronPCGCacheManager::SetCachedResult(const FAuracronPCGCacheKey& Key, const TArray<uint8>& Data, float ComputationTime)
{
    if (!bIsInitialized || !Configuration.bEnableCaching)
    {
        return false;
    }

    // Check if we should cache this result
    if (!UAuracronPCGCacheUtils::ShouldCacheResult(ComputationTime, Data.Num()))
    {
        return false;
    }

    FAuracronPCGCacheEntry Entry;
    Entry.Key = Key;
    Entry.SerializedData = Data;
    Entry.CreationTime = FDateTime::Now();
    Entry.LastAccessTime = Entry.CreationTime;
    Entry.ExpirationTime = Entry.CreationTime.AddSeconds(Configuration.DefaultExpirationTimeSeconds);
    Entry.DataSizeBytes = Data.Num();
    Entry.ComputationTimeSeconds = ComputationTime;
    Entry.bIsPersistent = (Configuration.StorageType == EAuracronPCGCacheStorageType::Disk ||
                          Configuration.StorageType == EAuracronPCGCacheStorageType::Hybrid);

    // Store in memory cache
    SetToMemoryCache(Key, Entry);

    // Store in disk cache if configured
    if (Entry.bIsPersistent)
    {
        SetToDiskCache(Key, Entry);
    }

    AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Cached result: %s (%.3fs computation, %d bytes)"),
                              *Key.GenerateHash(), ComputationTime, Data.Num());

    return true;
}

bool UAuracronPCGCacheManager::HasCachedResult(const FAuracronPCGCacheKey& Key)
{
    if (!bIsInitialized || !Configuration.bEnableCaching)
    {
        return false;
    }

    FScopeLock Lock(&CacheLock);

    // Check memory cache
    if (MemoryCache.Contains(Key))
    {
        const FAuracronPCGCacheEntry& Entry = MemoryCache[Key];
        return !Entry.IsExpired();
    }

    // Check disk cache
    if (Configuration.StorageType == EAuracronPCGCacheStorageType::Disk ||
        Configuration.StorageType == EAuracronPCGCacheStorageType::Hybrid)
    {
        return PersistentStorage->EntryExists(Key);
    }

    return false;
}

bool UAuracronPCGCacheManager::RemoveCachedResult(const FAuracronPCGCacheKey& Key)
{
    if (!bIsInitialized)
    {
        return false;
    }

    bool bRemoved = false;

    // Remove from memory cache
    {
        FScopeLock Lock(&CacheLock);
        if (MemoryCache.Remove(Key) > 0)
        {
            AccessTimes.Remove(Key);
            bRemoved = true;
        }
    }

    // Remove from disk cache
    if (Configuration.StorageType == EAuracronPCGCacheStorageType::Disk ||
        Configuration.StorageType == EAuracronPCGCacheStorageType::Hybrid)
    {
        if (PersistentStorage->DeleteEntry(Key))
        {
            bRemoved = true;
        }
    }

    if (bRemoved)
    {
        AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Removed cached result: %s"), *Key.GenerateHash());
    }

    return bRemoved;
}

bool UAuracronPCGCacheManager::GetFromMemoryCache(const FAuracronPCGCacheKey& Key, FAuracronPCGCacheEntry& OutEntry)
{
    FScopeLock Lock(&CacheLock);

    const FAuracronPCGCacheEntry* Entry = MemoryCache.Find(Key);
    if (Entry)
    {
        OutEntry = *Entry;
        return true;
    }

    return false;
}

bool UAuracronPCGCacheManager::GetFromDiskCache(const FAuracronPCGCacheKey& Key, FAuracronPCGCacheEntry& OutEntry)
{
    return PersistentStorage->LoadEntry(Key, OutEntry);
}

void UAuracronPCGCacheManager::SetToMemoryCache(const FAuracronPCGCacheKey& Key, const FAuracronPCGCacheEntry& Entry)
{
    FScopeLock Lock(&CacheLock);

    // Check memory limits before adding
    int32 CurrentMemoryUsage = GetMemoryUsage();
    int32 MaxMemoryBytes = Configuration.MaxMemoryCacheSizeMB * 1024 * 1024;

    if (CurrentMemoryUsage + Entry.DataSizeBytes > MaxMemoryBytes)
    {
        EvictLRUEntries();
    }

    MemoryCache.Add(Key, Entry);
    AccessTimes.Add(Key, FDateTime::Now());

    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.MemoryEntries++;
        Statistics.TotalEntries++;
        Statistics.MemoryUsageBytes += Entry.DataSizeBytes;
    }
}

void UAuracronPCGCacheManager::SetToDiskCache(const FAuracronPCGCacheKey& Key, const FAuracronPCGCacheEntry& Entry)
{
    if (PersistentStorage->SaveEntry(Entry))
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.DiskEntries++;
        Statistics.DiskUsageBytes += Entry.DataSizeBytes;
    }
}

void UAuracronPCGCacheManager::EvictLRUEntries()
{
    // Sort entries by last access time
    TArray<TPair<FAuracronPCGCacheKey, FDateTime>> SortedEntries;
    for (const auto& AccessPair : AccessTimes)
    {
        SortedEntries.Add(TPair<FAuracronPCGCacheKey, FDateTime>(AccessPair.Key, AccessPair.Value));
    }

    SortedEntries.Sort([](const TPair<FAuracronPCGCacheKey, FDateTime>& A, const TPair<FAuracronPCGCacheKey, FDateTime>& B)
    {
        return A.Value < B.Value; // Oldest first
    });

    // Remove oldest entries until we're under the memory limit
    int32 MaxMemoryBytes = Configuration.MaxMemoryCacheSizeMB * 1024 * 1024;
    int32 CurrentMemoryUsage = GetMemoryUsage();
    int32 TargetMemoryUsage = static_cast<int32>(MaxMemoryBytes * Configuration.MemoryPressureThreshold);

    for (const auto& EntryPair : SortedEntries)
    {
        if (CurrentMemoryUsage <= TargetMemoryUsage)
        {
            break;
        }

        const FAuracronPCGCacheKey& Key = EntryPair.Key;
        const FAuracronPCGCacheEntry* Entry = MemoryCache.Find(Key);
        if (Entry)
        {
            CurrentMemoryUsage -= Entry->DataSizeBytes;
            MemoryCache.Remove(Key);
            AccessTimes.Remove(Key);

            OnCacheEviction.Broadcast(Key, TEXT("LRU"));

            FScopeLock StatsLock(&StatisticsLock);
            Statistics.TotalEvictions++;
            Statistics.MemoryEntries--;
            Statistics.MemoryUsageBytes -= Entry->DataSizeBytes;
        }
    }
}

void UAuracronPCGCacheManager::UpdateStatistics(bool bHit, float AccessTime, float ComputationTime)
{
    FScopeLock Lock(&StatisticsLock);

    if (bHit)
    {
        Statistics.TotalHits++;
        Statistics.TimeSavedSeconds += ComputationTime;
    }
    else
    {
        Statistics.TotalMisses++;
    }

    Statistics.AverageAccessTime = (Statistics.AverageAccessTime + AccessTime) / 2.0f;
    if (ComputationTime > 0.0f)
    {
        Statistics.AverageComputationTime = (Statistics.AverageComputationTime + ComputationTime) / 2.0f;
    }

    Statistics.UpdateCalculatedFields();
}
