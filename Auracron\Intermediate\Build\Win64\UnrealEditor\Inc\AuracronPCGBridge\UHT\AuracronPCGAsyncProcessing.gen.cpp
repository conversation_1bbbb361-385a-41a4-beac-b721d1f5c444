// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGAsyncProcessing.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGAsyncProcessing() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncTaskManager();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncTaskManager_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCancellationToken();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCancellationToken_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMemoryPoolManager();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMemoryPoolManager_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProgressTracker();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProgressTracker_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType();
AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature();
AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature();
AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGProgressInfo();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGPointData_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGAsyncProcessingMode *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode;
static UEnum* EAuracronPCGAsyncProcessingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAsyncProcessingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAsyncProcessingMode>()
{
	return EAuracronPCGAsyncProcessingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AsyncTask.DisplayName", "Async Task" },
		{ "AsyncTask.Name", "EAuracronPCGAsyncProcessingMode::AsyncTask" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Async processing modes\n" },
#endif
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EAuracronPCGAsyncProcessingMode::Hybrid" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
		{ "Parallel.DisplayName", "Parallel" },
		{ "Parallel.Name", "EAuracronPCGAsyncProcessingMode::Parallel" },
		{ "ParallelFor.DisplayName", "Parallel For" },
		{ "ParallelFor.Name", "EAuracronPCGAsyncProcessingMode::ParallelFor" },
		{ "Sequential.DisplayName", "Sequential" },
		{ "Sequential.Name", "EAuracronPCGAsyncProcessingMode::Sequential" },
		{ "TaskGraph.DisplayName", "Task Graph" },
		{ "TaskGraph.Name", "EAuracronPCGAsyncProcessingMode::TaskGraph" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async processing modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAsyncProcessingMode::Sequential", (int64)EAuracronPCGAsyncProcessingMode::Sequential },
		{ "EAuracronPCGAsyncProcessingMode::Parallel", (int64)EAuracronPCGAsyncProcessingMode::Parallel },
		{ "EAuracronPCGAsyncProcessingMode::TaskGraph", (int64)EAuracronPCGAsyncProcessingMode::TaskGraph },
		{ "EAuracronPCGAsyncProcessingMode::AsyncTask", (int64)EAuracronPCGAsyncProcessingMode::AsyncTask },
		{ "EAuracronPCGAsyncProcessingMode::ParallelFor", (int64)EAuracronPCGAsyncProcessingMode::ParallelFor },
		{ "EAuracronPCGAsyncProcessingMode::Hybrid", (int64)EAuracronPCGAsyncProcessingMode::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAsyncProcessingMode",
	"EAuracronPCGAsyncProcessingMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGAsyncProcessingMode *********************************************

// ********** Begin Enum EAuracronPCGTaskPriority **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGTaskPriority;
static UEnum* EAuracronPCGTaskPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGTaskPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGTaskPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGTaskPriority"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGTaskPriority.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTaskPriority>()
{
	return EAuracronPCGTaskPriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Task priorities\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronPCGTaskPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronPCGTaskPriority::High" },
		{ "Immediate.DisplayName", "Immediate" },
		{ "Immediate.Name", "EAuracronPCGTaskPriority::Immediate" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronPCGTaskPriority::Low" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronPCGTaskPriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task priorities" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGTaskPriority::Low", (int64)EAuracronPCGTaskPriority::Low },
		{ "EAuracronPCGTaskPriority::Normal", (int64)EAuracronPCGTaskPriority::Normal },
		{ "EAuracronPCGTaskPriority::High", (int64)EAuracronPCGTaskPriority::High },
		{ "EAuracronPCGTaskPriority::Critical", (int64)EAuracronPCGTaskPriority::Critical },
		{ "EAuracronPCGTaskPriority::Immediate", (int64)EAuracronPCGTaskPriority::Immediate },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGTaskPriority",
	"EAuracronPCGTaskPriority",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGTaskPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGTaskPriority.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGTaskPriority.InnerSingleton;
}
// ********** End Enum EAuracronPCGTaskPriority ****************************************************

// ********** Begin Enum EAuracronPCGTaskState *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGTaskState;
static UEnum* EAuracronPCGTaskState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGTaskState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGTaskState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGTaskState"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGTaskState.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTaskState>()
{
	return EAuracronPCGTaskState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cancelled.DisplayName", "Cancelled" },
		{ "Cancelled.Name", "EAuracronPCGTaskState::Cancelled" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Task states\n" },
#endif
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "EAuracronPCGTaskState::Completed" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronPCGTaskState::Failed" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
		{ "Paused.DisplayName", "Paused" },
		{ "Paused.Name", "EAuracronPCGTaskState::Paused" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "EAuracronPCGTaskState::Pending" },
		{ "Running.DisplayName", "Running" },
		{ "Running.Name", "EAuracronPCGTaskState::Running" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGTaskState::Pending", (int64)EAuracronPCGTaskState::Pending },
		{ "EAuracronPCGTaskState::Running", (int64)EAuracronPCGTaskState::Running },
		{ "EAuracronPCGTaskState::Completed", (int64)EAuracronPCGTaskState::Completed },
		{ "EAuracronPCGTaskState::Failed", (int64)EAuracronPCGTaskState::Failed },
		{ "EAuracronPCGTaskState::Cancelled", (int64)EAuracronPCGTaskState::Cancelled },
		{ "EAuracronPCGTaskState::Paused", (int64)EAuracronPCGTaskState::Paused },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGTaskState",
	"EAuracronPCGTaskState",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGTaskState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGTaskState.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGTaskState.InnerSingleton;
}
// ********** End Enum EAuracronPCGTaskState *******************************************************

// ********** Begin Enum EAuracronPCGThreadType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGThreadType;
static UEnum* EAuracronPCGThreadType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGThreadType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGThreadType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGThreadType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGThreadType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGThreadType>()
{
	return EAuracronPCGThreadType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AnyThread.DisplayName", "Any Thread" },
		{ "AnyThread.Name", "EAuracronPCGThreadType::AnyThread" },
		{ "BackgroundThread.DisplayName", "Background Thread" },
		{ "BackgroundThread.Name", "EAuracronPCGThreadType::BackgroundThread" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Thread types\n" },
#endif
		{ "GameThread.DisplayName", "Game Thread" },
		{ "GameThread.Name", "EAuracronPCGThreadType::GameThread" },
		{ "HighPriorityThread.DisplayName", "High Priority Thread" },
		{ "HighPriorityThread.Name", "EAuracronPCGThreadType::HighPriorityThread" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
		{ "RenderThread.DisplayName", "Render Thread" },
		{ "RenderThread.Name", "EAuracronPCGThreadType::RenderThread" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Thread types" },
#endif
		{ "WorkerThread.DisplayName", "Worker Thread" },
		{ "WorkerThread.Name", "EAuracronPCGThreadType::WorkerThread" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGThreadType::GameThread", (int64)EAuracronPCGThreadType::GameThread },
		{ "EAuracronPCGThreadType::RenderThread", (int64)EAuracronPCGThreadType::RenderThread },
		{ "EAuracronPCGThreadType::AnyThread", (int64)EAuracronPCGThreadType::AnyThread },
		{ "EAuracronPCGThreadType::BackgroundThread", (int64)EAuracronPCGThreadType::BackgroundThread },
		{ "EAuracronPCGThreadType::WorkerThread", (int64)EAuracronPCGThreadType::WorkerThread },
		{ "EAuracronPCGThreadType::HighPriorityThread", (int64)EAuracronPCGThreadType::HighPriorityThread },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGThreadType",
	"EAuracronPCGThreadType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGThreadType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGThreadType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGThreadType.InnerSingleton;
}
// ********** End Enum EAuracronPCGThreadType ******************************************************

// ********** Begin ScriptStruct FAuracronPCGAsyncTaskDescriptor ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor;
class UScriptStruct* FAuracronPCGAsyncTaskDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGAsyncTaskDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Async Task Descriptor\n * Describes configuration for async task execution\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async Task Descriptor\nDescribes configuration for async task execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskName_MetaData[] = {
		{ "Category", "Task" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskDescription_MetaData[] = {
		{ "Category", "Task" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingMode_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadType_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentTasks_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutSeconds_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowCancellation_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTrackProgress_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReportProgress_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressUpdateInterval_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMemoryPooling_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPoolSize_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLimitMemoryUsage_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// MB\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBatching_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchSize_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinBatchSize_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBatchSize_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRetryOnFailure_MetaData[] = {
		{ "Category", "Error Handling" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRetryAttempts_MetaData[] = {
		{ "Category", "Error Handling" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RetryDelaySeconds_MetaData[] = {
		{ "Category", "Error Handling" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "Category", "Debugging" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceProfiling_MetaData[] = {
		{ "Category", "Debugging" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMemoryTracking_MetaData[] = {
		{ "Category", "Debugging" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProcessingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProcessingMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ThreadType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ThreadType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentTasks;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static void NewProp_bAllowCancellation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowCancellation;
	static void NewProp_bTrackProgress_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTrackProgress;
	static void NewProp_bReportProgress_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReportProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProgressUpdateInterval;
	static void NewProp_bUseMemoryPooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMemoryPooling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryPoolSize;
	static void NewProp_bLimitMemoryUsage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLimitMemoryUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMemoryUsageMB;
	static void NewProp_bUseBatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBatching;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinBatchSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxBatchSize;
	static void NewProp_bRetryOnFailure_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRetryOnFailure;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRetryAttempts;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RetryDelaySeconds;
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static void NewProp_bEnablePerformanceProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceProfiling;
	static void NewProp_bEnableMemoryTracking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMemoryTracking;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGAsyncTaskDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_TaskName = { "TaskName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, TaskName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskName_MetaData), NewProp_TaskName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_TaskDescription = { "TaskDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, TaskDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskDescription_MetaData), NewProp_TaskDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ProcessingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ProcessingMode = { "ProcessingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, ProcessingMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingMode_MetaData), NewProp_ProcessingMode_MetaData) }; // 1257345079
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, Priority), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 2732136417
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ThreadType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ThreadType = { "ThreadType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, ThreadType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGThreadType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadType_MetaData), NewProp_ThreadType_MetaData) }; // 1299885978
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxConcurrentTasks = { "MaxConcurrentTasks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, MaxConcurrentTasks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentTasks_MetaData), NewProp_MaxConcurrentTasks_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, TimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutSeconds_MetaData), NewProp_TimeoutSeconds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bAllowCancellation_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bAllowCancellation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bAllowCancellation = { "bAllowCancellation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bAllowCancellation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowCancellation_MetaData), NewProp_bAllowCancellation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bTrackProgress_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bTrackProgress = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bTrackProgress = { "bTrackProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bTrackProgress_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTrackProgress_MetaData), NewProp_bTrackProgress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bReportProgress_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bReportProgress = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bReportProgress = { "bReportProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bReportProgress_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReportProgress_MetaData), NewProp_bReportProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ProgressUpdateInterval = { "ProgressUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, ProgressUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressUpdateInterval_MetaData), NewProp_ProgressUpdateInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseMemoryPooling_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bUseMemoryPooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseMemoryPooling = { "bUseMemoryPooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseMemoryPooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMemoryPooling_MetaData), NewProp_bUseMemoryPooling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MemoryPoolSize = { "MemoryPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, MemoryPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPoolSize_MetaData), NewProp_MemoryPoolSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bLimitMemoryUsage_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bLimitMemoryUsage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bLimitMemoryUsage = { "bLimitMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bLimitMemoryUsage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLimitMemoryUsage_MetaData), NewProp_bLimitMemoryUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxMemoryUsageMB = { "MaxMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, MaxMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryUsageMB_MetaData), NewProp_MaxMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseBatching_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bUseBatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseBatching = { "bUseBatching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseBatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBatching_MetaData), NewProp_bUseBatching_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_BatchSize = { "BatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, BatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchSize_MetaData), NewProp_BatchSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MinBatchSize = { "MinBatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, MinBatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinBatchSize_MetaData), NewProp_MinBatchSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxBatchSize = { "MaxBatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, MaxBatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBatchSize_MetaData), NewProp_MaxBatchSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bRetryOnFailure_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bRetryOnFailure = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bRetryOnFailure = { "bRetryOnFailure", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bRetryOnFailure_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRetryOnFailure_MetaData), NewProp_bRetryOnFailure_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxRetryAttempts = { "MaxRetryAttempts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, MaxRetryAttempts), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRetryAttempts_MetaData), NewProp_MaxRetryAttempts_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_RetryDelaySeconds = { "RetryDelaySeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskDescriptor, RetryDelaySeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RetryDelaySeconds_MetaData), NewProp_RetryDelaySeconds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnablePerformanceProfiling_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bEnablePerformanceProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnablePerformanceProfiling = { "bEnablePerformanceProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnablePerformanceProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceProfiling_MetaData), NewProp_bEnablePerformanceProfiling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableMemoryTracking_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskDescriptor*)Obj)->bEnableMemoryTracking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableMemoryTracking = { "bEnableMemoryTracking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableMemoryTracking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMemoryTracking_MetaData), NewProp_bEnableMemoryTracking_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_TaskName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_TaskDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ProcessingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ProcessingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ThreadType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ThreadType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxConcurrentTasks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_TimeoutSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bAllowCancellation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bTrackProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bReportProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_ProgressUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseMemoryPooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MemoryPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bLimitMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bUseBatching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_BatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MinBatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxBatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bRetryOnFailure,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_MaxRetryAttempts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_RetryDelaySeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableDetailedLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnablePerformanceProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewProp_bEnableMemoryTracking,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGAsyncTaskDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGAsyncTaskDescriptor),
	alignof(FAuracronPCGAsyncTaskDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGAsyncTaskDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGProgressInfo ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo;
class UScriptStruct* FAuracronPCGProgressInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGProgressInfo, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGProgressInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Progress Info\n * Information about task progress\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress Info\nInformation about task progress" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskName_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskState_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressPercentage_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemsProcessed_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalItems_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElapsedTime_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedTimeRemaining_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentOperation_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatusMessage_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemsPerSecond_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveThreads_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasErrors_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessages_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningMessages_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TaskState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TaskState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProgressPercentage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ItemsProcessed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalItems;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElapsedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedTimeRemaining;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentOperation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StatusMessage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ItemsPerSecond;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveThreads;
	static void NewProp_bHasErrors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasErrors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrorMessages;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WarningMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WarningMessages;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGProgressInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskName = { "TaskName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, TaskName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskName_MetaData), NewProp_TaskName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskState = { "TaskState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, TaskState), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskState_MetaData), NewProp_TaskState_MetaData) }; // 546880179
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ProgressPercentage = { "ProgressPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, ProgressPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressPercentage_MetaData), NewProp_ProgressPercentage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ItemsProcessed = { "ItemsProcessed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, ItemsProcessed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemsProcessed_MetaData), NewProp_ItemsProcessed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TotalItems = { "TotalItems", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, TotalItems), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalItems_MetaData), NewProp_TotalItems_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ElapsedTime = { "ElapsedTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, ElapsedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElapsedTime_MetaData), NewProp_ElapsedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_EstimatedTimeRemaining = { "EstimatedTimeRemaining", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, EstimatedTimeRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedTimeRemaining_MetaData), NewProp_EstimatedTimeRemaining_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_CurrentOperation = { "CurrentOperation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, CurrentOperation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentOperation_MetaData), NewProp_CurrentOperation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_StatusMessage = { "StatusMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, StatusMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatusMessage_MetaData), NewProp_StatusMessage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ItemsPerSecond = { "ItemsPerSecond", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, ItemsPerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemsPerSecond_MetaData), NewProp_ItemsPerSecond_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ActiveThreads = { "ActiveThreads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, ActiveThreads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveThreads_MetaData), NewProp_ActiveThreads_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_bHasErrors_SetBit(void* Obj)
{
	((FAuracronPCGProgressInfo*)Obj)->bHasErrors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_bHasErrors = { "bHasErrors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGProgressInfo), &Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_bHasErrors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasErrors_MetaData), NewProp_bHasErrors_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ErrorMessages_Inner = { "ErrorMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ErrorMessages = { "ErrorMessages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, ErrorMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessages_MetaData), NewProp_ErrorMessages_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_WarningMessages_Inner = { "WarningMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_WarningMessages = { "WarningMessages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGProgressInfo, WarningMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningMessages_MetaData), NewProp_WarningMessages_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TaskState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ProgressPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ItemsProcessed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_TotalItems,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ElapsedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_EstimatedTimeRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_CurrentOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_StatusMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ItemsPerSecond,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ActiveThreads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_bHasErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ErrorMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_ErrorMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_WarningMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewProp_WarningMessages,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGProgressInfo",
	Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::PropPointers),
	sizeof(FAuracronPCGProgressInfo),
	alignof(FAuracronPCGProgressInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGProgressInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGProgressInfo ********************************************

// ********** Begin ScriptStruct FAuracronPCGAsyncTaskResult ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult;
class UScriptStruct* FAuracronPCGAsyncTaskResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGAsyncTaskResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Async Task Result\n * Result of async task execution\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async Task Result\nResult of async task execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FinalState_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningMessages_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalExecutionTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemsProcessed_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemsFailed_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageProcessingTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakMemoryUsageMB_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RetryAttempts_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomData_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FinalState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FinalState;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WarningMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WarningMessages;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalExecutionTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ItemsProcessed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ItemsFailed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageProcessingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakMemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RetryAttempts;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGAsyncTaskResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronPCGAsyncTaskResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAsyncTaskResult), &Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_FinalState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_FinalState = { "FinalState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, FinalState), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FinalState_MetaData), NewProp_FinalState_MetaData) }; // 546880179
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_WarningMessages_Inner = { "WarningMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_WarningMessages = { "WarningMessages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, WarningMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningMessages_MetaData), NewProp_WarningMessages_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_TotalExecutionTime = { "TotalExecutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, TotalExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalExecutionTime_MetaData), NewProp_TotalExecutionTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_ItemsProcessed = { "ItemsProcessed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, ItemsProcessed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemsProcessed_MetaData), NewProp_ItemsProcessed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_ItemsFailed = { "ItemsFailed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, ItemsFailed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemsFailed_MetaData), NewProp_ItemsFailed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_AverageProcessingTime = { "AverageProcessingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, AverageProcessingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageProcessingTime_MetaData), NewProp_AverageProcessingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PeakMemoryUsageMB = { "PeakMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, PeakMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakMemoryUsageMB_MetaData), NewProp_PeakMemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_RetryAttempts = { "RetryAttempts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, RetryAttempts), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RetryAttempts_MetaData), NewProp_RetryAttempts_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_CustomData_ValueProp = { "CustomData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_CustomData_Key_KeyProp = { "CustomData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_CustomData = { "CustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, CustomData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomData_MetaData), NewProp_CustomData_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PerformanceMetrics_ValueProp = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PerformanceMetrics_Key_KeyProp = { "PerformanceMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAsyncTaskResult, PerformanceMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_FinalState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_FinalState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_WarningMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_WarningMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_TotalExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_ItemsProcessed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_ItemsFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_AverageProcessingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PeakMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_RetryAttempts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_CustomData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_CustomData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_CustomData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PerformanceMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PerformanceMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewProp_PerformanceMetrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGAsyncTaskResult",
	Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::PropPointers),
	sizeof(FAuracronPCGAsyncTaskResult),
	alignof(FAuracronPCGAsyncTaskResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGAsyncTaskResult *****************************************

// ********** Begin Class UAuracronPCGProgressTracker Function AddError ****************************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics
{
	struct AuracronPCGProgressTracker_eventAddError_Parms
	{
		FString TaskId;
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventAddError_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventAddError_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "AddError", Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::AuracronPCGProgressTracker_eventAddError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::AuracronPCGProgressTracker_eventAddError_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execAddError)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_GET_PROPERTY(FStrProperty,Z_Param_ErrorMessage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddError(Z_Param_TaskId,Z_Param_ErrorMessage);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function AddError ******************************

// ********** Begin Class UAuracronPCGProgressTracker Function AddWarning **************************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics
{
	struct AuracronPCGProgressTracker_eventAddWarning_Parms
	{
		FString TaskId;
		FString WarningMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WarningMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventAddWarning_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::NewProp_WarningMessage = { "WarningMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventAddWarning_Parms, WarningMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningMessage_MetaData), NewProp_WarningMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::NewProp_WarningMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "AddWarning", Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::AuracronPCGProgressTracker_eventAddWarning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::AuracronPCGProgressTracker_eventAddWarning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execAddWarning)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_GET_PROPERTY(FStrProperty,Z_Param_WarningMessage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddWarning(Z_Param_TaskId,Z_Param_WarningMessage);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function AddWarning ****************************

// ********** Begin Class UAuracronPCGProgressTracker Function ClearAllTasks ***********************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearAllTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearAllTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "ClearAllTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearAllTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearAllTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearAllTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearAllTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execClearAllTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function ClearAllTasks *************************

// ********** Begin Class UAuracronPCGProgressTracker Function ClearCompletedTasks *****************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearCompletedTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cleanup\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cleanup" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearCompletedTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "ClearCompletedTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearCompletedTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearCompletedTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearCompletedTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearCompletedTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execClearCompletedTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCompletedTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function ClearCompletedTasks *******************

// ********** Begin Class UAuracronPCGProgressTracker Function FinishTracking **********************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics
{
	struct AuracronPCGProgressTracker_eventFinishTracking_Parms
	{
		FString TaskId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventFinishTracking_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
void Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronPCGProgressTracker_eventFinishTracking_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGProgressTracker_eventFinishTracking_Parms), &Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "FinishTracking", Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::AuracronPCGProgressTracker_eventFinishTracking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::AuracronPCGProgressTracker_eventFinishTracking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execFinishTracking)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_GET_UBOOL(Z_Param_bSuccess);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FinishTracking(Z_Param_TaskId,Z_Param_bSuccess);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function FinishTracking ************************

// ********** Begin Class UAuracronPCGProgressTracker Function GetActiveTaskCount ******************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics
{
	struct AuracronPCGProgressTracker_eventGetActiveTaskCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventGetActiveTaskCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "GetActiveTaskCount", Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::AuracronPCGProgressTracker_eventGetActiveTaskCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::AuracronPCGProgressTracker_eventGetActiveTaskCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execGetActiveTaskCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveTaskCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function GetActiveTaskCount ********************

// ********** Begin Class UAuracronPCGProgressTracker Function GetAllProgressInfo ******************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics
{
	struct AuracronPCGProgressTracker_eventGetAllProgressInfo_Parms
	{
		TArray<FAuracronPCGProgressInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGProgressInfo, METADATA_PARAMS(0, nullptr) }; // 4186792507
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventGetAllProgressInfo_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4186792507
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "GetAllProgressInfo", Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::AuracronPCGProgressTracker_eventGetAllProgressInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::AuracronPCGProgressTracker_eventGetAllProgressInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execGetAllProgressInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGProgressInfo>*)Z_Param__Result=P_THIS->GetAllProgressInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function GetAllProgressInfo ********************

// ********** Begin Class UAuracronPCGProgressTracker Function GetOverallProgress ******************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics
{
	struct AuracronPCGProgressTracker_eventGetOverallProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventGetOverallProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "GetOverallProgress", Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::AuracronPCGProgressTracker_eventGetOverallProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::AuracronPCGProgressTracker_eventGetOverallProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execGetOverallProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetOverallProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function GetOverallProgress ********************

// ********** Begin Class UAuracronPCGProgressTracker Function GetProgressInfo *********************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics
{
	struct AuracronPCGProgressTracker_eventGetProgressInfo_Parms
	{
		FString TaskId;
		FAuracronPCGProgressInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Progress retrieval\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress retrieval" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventGetProgressInfo_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventGetProgressInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGProgressInfo, METADATA_PARAMS(0, nullptr) }; // 4186792507
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "GetProgressInfo", Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::AuracronPCGProgressTracker_eventGetProgressInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::AuracronPCGProgressTracker_eventGetProgressInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execGetProgressInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGProgressInfo*)Z_Param__Result=P_THIS->GetProgressInfo(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function GetProgressInfo ***********************

// ********** Begin Class UAuracronPCGProgressTracker Function IsTaskActive ************************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics
{
	struct AuracronPCGProgressTracker_eventIsTaskActive_Parms
	{
		FString TaskId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventIsTaskActive_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
void Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGProgressTracker_eventIsTaskActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGProgressTracker_eventIsTaskActive_Parms), &Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "IsTaskActive", Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::AuracronPCGProgressTracker_eventIsTaskActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::AuracronPCGProgressTracker_eventIsTaskActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execIsTaskActive)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTaskActive(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function IsTaskActive **************************

// ********** Begin Class UAuracronPCGProgressTracker Function RemoveTask **************************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics
{
	struct AuracronPCGProgressTracker_eventRemoveTask_Parms
	{
		FString TaskId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventRemoveTask_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::NewProp_TaskId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "RemoveTask", Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::AuracronPCGProgressTracker_eventRemoveTask_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::AuracronPCGProgressTracker_eventRemoveTask_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execRemoveTask)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveTask(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function RemoveTask ****************************

// ********** Begin Class UAuracronPCGProgressTracker Function SetTaskState ************************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics
{
	struct AuracronPCGProgressTracker_eventSetTaskState_Parms
	{
		FString TaskId;
		EAuracronPCGTaskState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventSetTaskState_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventSetTaskState_Parms, NewState), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState, METADATA_PARAMS(0, nullptr) }; // 546880179
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "SetTaskState", Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::AuracronPCGProgressTracker_eventSetTaskState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::AuracronPCGProgressTracker_eventSetTaskState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execSetTaskState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_GET_ENUM(EAuracronPCGTaskState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTaskState(Z_Param_TaskId,EAuracronPCGTaskState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function SetTaskState **************************

// ********** Begin Class UAuracronPCGProgressTracker Function StartTracking ***********************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics
{
	struct AuracronPCGProgressTracker_eventStartTracking_Parms
	{
		FString TaskId;
		FString TaskName;
		int32 TotalItems;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Progress tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalItems;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventStartTracking_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::NewProp_TaskName = { "TaskName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventStartTracking_Parms, TaskName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskName_MetaData), NewProp_TaskName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::NewProp_TotalItems = { "TotalItems", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventStartTracking_Parms, TotalItems), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::NewProp_TaskName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::NewProp_TotalItems,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "StartTracking", Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::AuracronPCGProgressTracker_eventStartTracking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::AuracronPCGProgressTracker_eventStartTracking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execStartTracking)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskName);
	P_GET_PROPERTY(FIntProperty,Z_Param_TotalItems);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartTracking(Z_Param_TaskId,Z_Param_TaskName,Z_Param_TotalItems);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function StartTracking *************************

// ********** Begin Class UAuracronPCGProgressTracker Function UpdateProgress **********************
struct Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics
{
	struct AuracronPCGProgressTracker_eventUpdateProgress_Parms
	{
		FString TaskId;
		int32 ItemsProcessed;
		FString CurrentOperation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress Tracker" },
		{ "CPP_Default_CurrentOperation", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentOperation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ItemsProcessed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentOperation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventUpdateProgress_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::NewProp_ItemsProcessed = { "ItemsProcessed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventUpdateProgress_Parms, ItemsProcessed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::NewProp_CurrentOperation = { "CurrentOperation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGProgressTracker_eventUpdateProgress_Parms, CurrentOperation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentOperation_MetaData), NewProp_CurrentOperation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::NewProp_ItemsProcessed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::NewProp_CurrentOperation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGProgressTracker, nullptr, "UpdateProgress", Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::AuracronPCGProgressTracker_eventUpdateProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::AuracronPCGProgressTracker_eventUpdateProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGProgressTracker::execUpdateProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_GET_PROPERTY(FIntProperty,Z_Param_ItemsProcessed);
	P_GET_PROPERTY(FStrProperty,Z_Param_CurrentOperation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateProgress(Z_Param_TaskId,Z_Param_ItemsProcessed,Z_Param_CurrentOperation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGProgressTracker Function UpdateProgress ************************

// ********** Begin Class UAuracronPCGProgressTracker **********************************************
void UAuracronPCGProgressTracker::StaticRegisterNativesUAuracronPCGProgressTracker()
{
	UClass* Class = UAuracronPCGProgressTracker::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddError", &UAuracronPCGProgressTracker::execAddError },
		{ "AddWarning", &UAuracronPCGProgressTracker::execAddWarning },
		{ "ClearAllTasks", &UAuracronPCGProgressTracker::execClearAllTasks },
		{ "ClearCompletedTasks", &UAuracronPCGProgressTracker::execClearCompletedTasks },
		{ "FinishTracking", &UAuracronPCGProgressTracker::execFinishTracking },
		{ "GetActiveTaskCount", &UAuracronPCGProgressTracker::execGetActiveTaskCount },
		{ "GetAllProgressInfo", &UAuracronPCGProgressTracker::execGetAllProgressInfo },
		{ "GetOverallProgress", &UAuracronPCGProgressTracker::execGetOverallProgress },
		{ "GetProgressInfo", &UAuracronPCGProgressTracker::execGetProgressInfo },
		{ "IsTaskActive", &UAuracronPCGProgressTracker::execIsTaskActive },
		{ "RemoveTask", &UAuracronPCGProgressTracker::execRemoveTask },
		{ "SetTaskState", &UAuracronPCGProgressTracker::execSetTaskState },
		{ "StartTracking", &UAuracronPCGProgressTracker::execStartTracking },
		{ "UpdateProgress", &UAuracronPCGProgressTracker::execUpdateProgress },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGProgressTracker;
UClass* UAuracronPCGProgressTracker::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGProgressTracker;
	if (!Z_Registration_Info_UClass_UAuracronPCGProgressTracker.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGProgressTracker"),
			Z_Registration_Info_UClass_UAuracronPCGProgressTracker.InnerSingleton,
			StaticRegisterNativesUAuracronPCGProgressTracker,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGProgressTracker.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGProgressTracker_NoRegister()
{
	return UAuracronPCGProgressTracker::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGProgressTracker_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Progress Tracker\n * Tracks progress of async tasks\n */" },
#endif
		{ "IncludePath", "AuracronPCGAsyncProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress Tracker\nTracks progress of async tasks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressMap_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProgressMap_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProgressMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ProgressMap;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_AddError, "AddError" }, // 2015493043
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_AddWarning, "AddWarning" }, // 2489462067
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearAllTasks, "ClearAllTasks" }, // 2686838582
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_ClearCompletedTasks, "ClearCompletedTasks" }, // 1769963084
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_FinishTracking, "FinishTracking" }, // 2137186866
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_GetActiveTaskCount, "GetActiveTaskCount" }, // 3911370935
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_GetAllProgressInfo, "GetAllProgressInfo" }, // 748691147
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_GetOverallProgress, "GetOverallProgress" }, // 1681437614
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_GetProgressInfo, "GetProgressInfo" }, // 2319839159
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_IsTaskActive, "IsTaskActive" }, // 479751920
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_RemoveTask, "RemoveTask" }, // 1401004281
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_SetTaskState, "SetTaskState" }, // 2854446250
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_StartTracking, "StartTracking" }, // 519273758
		{ &Z_Construct_UFunction_UAuracronPCGProgressTracker_UpdateProgress, "UpdateProgress" }, // 660177634
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGProgressTracker>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::NewProp_ProgressMap_ValueProp = { "ProgressMap", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronPCGProgressInfo, METADATA_PARAMS(0, nullptr) }; // 4186792507
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::NewProp_ProgressMap_Key_KeyProp = { "ProgressMap_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::NewProp_ProgressMap = { "ProgressMap", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGProgressTracker, ProgressMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressMap_MetaData), NewProp_ProgressMap_MetaData) }; // 4186792507
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::NewProp_ProgressMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::NewProp_ProgressMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::NewProp_ProgressMap,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::ClassParams = {
	&UAuracronPCGProgressTracker::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGProgressTracker()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGProgressTracker.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGProgressTracker.OuterSingleton, Z_Construct_UClass_UAuracronPCGProgressTracker_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGProgressTracker.OuterSingleton;
}
UAuracronPCGProgressTracker::UAuracronPCGProgressTracker(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGProgressTracker);
UAuracronPCGProgressTracker::~UAuracronPCGProgressTracker() {}
// ********** End Class UAuracronPCGProgressTracker ************************************************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function ClearMemoryTrackingInfo ***********
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ClearMemoryTrackingInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ClearMemoryTrackingInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "ClearMemoryTrackingInfo", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ClearMemoryTrackingInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ClearMemoryTrackingInfo_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ClearMemoryTrackingInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ClearMemoryTrackingInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execClearMemoryTrackingInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearMemoryTrackingInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function ClearMemoryTrackingInfo *************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function EnableMemoryTracking **************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics
{
	struct AuracronPCGMemoryPoolManager_eventEnableMemoryTracking_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory tracking" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronPCGMemoryPoolManager_eventEnableMemoryTracking_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMemoryPoolManager_eventEnableMemoryTracking_Parms), &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "EnableMemoryTracking", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::AuracronPCGMemoryPoolManager_eventEnableMemoryTracking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::AuracronPCGMemoryPoolManager_eventEnableMemoryTracking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execEnableMemoryTracking)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableMemoryTracking(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function EnableMemoryTracking ****************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function GetAllocationCount ****************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics
{
	struct AuracronPCGMemoryPoolManager_eventGetAllocationCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventGetAllocationCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "GetAllocationCount", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::AuracronPCGMemoryPoolManager_eventGetAllocationCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::AuracronPCGMemoryPoolManager_eventGetAllocationCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execGetAllocationCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetAllocationCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function GetAllocationCount ******************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function GetFreeMemory *********************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics
{
	struct AuracronPCGMemoryPoolManager_eventGetFreeMemory_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventGetFreeMemory_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "GetFreeMemory", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::AuracronPCGMemoryPoolManager_eventGetFreeMemory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::AuracronPCGMemoryPoolManager_eventGetFreeMemory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execGetFreeMemory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetFreeMemory();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function GetFreeMemory ***********************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function GetInstance ***********************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics
{
	struct AuracronPCGMemoryPoolManager_eventGetInstance_Parms
	{
		UAuracronPCGMemoryPoolManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGMemoryPoolManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::AuracronPCGMemoryPoolManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::AuracronPCGMemoryPoolManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGMemoryPoolManager**)Z_Param__Result=UAuracronPCGMemoryPoolManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function GetInstance *************************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function GetMemoryTrackingInfo *************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics
{
	struct AuracronPCGMemoryPoolManager_eventGetMemoryTrackingInfo_Parms
	{
		TMap<FString,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventGetMemoryTrackingInfo_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "GetMemoryTrackingInfo", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::AuracronPCGMemoryPoolManager_eventGetMemoryTrackingInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::AuracronPCGMemoryPoolManager_eventGetMemoryTrackingInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execGetMemoryTrackingInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,int32>*)Z_Param__Result=P_THIS->GetMemoryTrackingInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function GetMemoryTrackingInfo ***************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function GetMemoryUsagePercentage **********
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics
{
	struct AuracronPCGMemoryPoolManager_eventGetMemoryUsagePercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventGetMemoryUsagePercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "GetMemoryUsagePercentage", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::AuracronPCGMemoryPoolManager_eventGetMemoryUsagePercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::AuracronPCGMemoryPoolManager_eventGetMemoryUsagePercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execGetMemoryUsagePercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMemoryUsagePercentage();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function GetMemoryUsagePercentage ************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function GetTotalPoolSize ******************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics
{
	struct AuracronPCGMemoryPoolManager_eventGetTotalPoolSize_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pool statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventGetTotalPoolSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "GetTotalPoolSize", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::AuracronPCGMemoryPoolManager_eventGetTotalPoolSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::AuracronPCGMemoryPoolManager_eventGetTotalPoolSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execGetTotalPoolSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalPoolSize();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function GetTotalPoolSize ********************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function GetUsedMemory *********************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics
{
	struct AuracronPCGMemoryPoolManager_eventGetUsedMemory_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventGetUsedMemory_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "GetUsedMemory", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::AuracronPCGMemoryPoolManager_eventGetUsedMemory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::AuracronPCGMemoryPoolManager_eventGetUsedMemory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execGetUsedMemory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetUsedMemory();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function GetUsedMemory ***********************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function InitializePool ********************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics
{
	struct AuracronPCGMemoryPoolManager_eventInitializePool_Parms
	{
		int32 PoolSizeMB;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pool management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolSizeMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::NewProp_PoolSizeMB = { "PoolSizeMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMemoryPoolManager_eventInitializePool_Parms, PoolSizeMB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::NewProp_PoolSizeMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "InitializePool", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::AuracronPCGMemoryPoolManager_eventInitializePool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::AuracronPCGMemoryPoolManager_eventInitializePool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execInitializePool)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PoolSizeMB);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePool(Z_Param_PoolSizeMB);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function InitializePool **********************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function IsPoolInitialized *****************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics
{
	struct AuracronPCGMemoryPoolManager_eventIsPoolInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMemoryPoolManager_eventIsPoolInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMemoryPoolManager_eventIsPoolInitialized_Parms), &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "IsPoolInitialized", Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::AuracronPCGMemoryPoolManager_eventIsPoolInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::AuracronPCGMemoryPoolManager_eventIsPoolInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execIsPoolInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPoolInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function IsPoolInitialized *******************

// ********** Begin Class UAuracronPCGMemoryPoolManager Function ShutdownPool **********************
struct Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ShutdownPool_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory Pool Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ShutdownPool_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMemoryPoolManager, nullptr, "ShutdownPool", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ShutdownPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ShutdownPool_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ShutdownPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ShutdownPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMemoryPoolManager::execShutdownPool)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownPool();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMemoryPoolManager Function ShutdownPool ************************

// ********** Begin Class UAuracronPCGMemoryPoolManager ********************************************
void UAuracronPCGMemoryPoolManager::StaticRegisterNativesUAuracronPCGMemoryPoolManager()
{
	UClass* Class = UAuracronPCGMemoryPoolManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearMemoryTrackingInfo", &UAuracronPCGMemoryPoolManager::execClearMemoryTrackingInfo },
		{ "EnableMemoryTracking", &UAuracronPCGMemoryPoolManager::execEnableMemoryTracking },
		{ "GetAllocationCount", &UAuracronPCGMemoryPoolManager::execGetAllocationCount },
		{ "GetFreeMemory", &UAuracronPCGMemoryPoolManager::execGetFreeMemory },
		{ "GetInstance", &UAuracronPCGMemoryPoolManager::execGetInstance },
		{ "GetMemoryTrackingInfo", &UAuracronPCGMemoryPoolManager::execGetMemoryTrackingInfo },
		{ "GetMemoryUsagePercentage", &UAuracronPCGMemoryPoolManager::execGetMemoryUsagePercentage },
		{ "GetTotalPoolSize", &UAuracronPCGMemoryPoolManager::execGetTotalPoolSize },
		{ "GetUsedMemory", &UAuracronPCGMemoryPoolManager::execGetUsedMemory },
		{ "InitializePool", &UAuracronPCGMemoryPoolManager::execInitializePool },
		{ "IsPoolInitialized", &UAuracronPCGMemoryPoolManager::execIsPoolInitialized },
		{ "ShutdownPool", &UAuracronPCGMemoryPoolManager::execShutdownPool },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager;
UClass* UAuracronPCGMemoryPoolManager::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGMemoryPoolManager;
	if (!Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGMemoryPoolManager"),
			Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager.InnerSingleton,
			StaticRegisterNativesUAuracronPCGMemoryPoolManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGMemoryPoolManager_NoRegister()
{
	return UAuracronPCGMemoryPoolManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Memory Pool Manager\n * Manages memory pools for async processing\n */" },
#endif
		{ "IncludePath", "AuracronPCGAsyncProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory Pool Manager\nManages memory pools for async processing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolSizeMB_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UsedMemoryBytes_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllocationCount_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMemoryTrackingEnabled_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolSizeMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UsedMemoryBytes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AllocationCount;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bMemoryTrackingEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMemoryTrackingEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ClearMemoryTrackingInfo, "ClearMemoryTrackingInfo" }, // 1502614369
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_EnableMemoryTracking, "EnableMemoryTracking" }, // 2769260508
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetAllocationCount, "GetAllocationCount" }, // 3596485374
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetFreeMemory, "GetFreeMemory" }, // 4087056759
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetInstance, "GetInstance" }, // 809645971
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryTrackingInfo, "GetMemoryTrackingInfo" }, // 3779804994
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetMemoryUsagePercentage, "GetMemoryUsagePercentage" }, // 1716387864
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetTotalPoolSize, "GetTotalPoolSize" }, // 1348663006
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_GetUsedMemory, "GetUsedMemory" }, // 2089506022
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_InitializePool, "InitializePool" }, // 1495198954
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_IsPoolInitialized, "IsPoolInitialized" }, // 3563862700
		{ &Z_Construct_UFunction_UAuracronPCGMemoryPoolManager_ShutdownPool, "ShutdownPool" }, // 3524456713
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGMemoryPoolManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_PoolSizeMB = { "PoolSizeMB", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMemoryPoolManager, PoolSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolSizeMB_MetaData), NewProp_PoolSizeMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_UsedMemoryBytes = { "UsedMemoryBytes", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMemoryPoolManager, UsedMemoryBytes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UsedMemoryBytes_MetaData), NewProp_UsedMemoryBytes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_AllocationCount = { "AllocationCount", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMemoryPoolManager, AllocationCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllocationCount_MetaData), NewProp_AllocationCount_MetaData) };
void Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronPCGMemoryPoolManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMemoryPoolManager), &Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bMemoryTrackingEnabled_SetBit(void* Obj)
{
	((UAuracronPCGMemoryPoolManager*)Obj)->bMemoryTrackingEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bMemoryTrackingEnabled = { "bMemoryTrackingEnabled", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMemoryPoolManager), &Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bMemoryTrackingEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMemoryTrackingEnabled_MetaData), NewProp_bMemoryTrackingEnabled_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_PoolSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_UsedMemoryBytes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_AllocationCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::NewProp_bMemoryTrackingEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::ClassParams = {
	&UAuracronPCGMemoryPoolManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGMemoryPoolManager()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager.OuterSingleton, Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager.OuterSingleton;
}
UAuracronPCGMemoryPoolManager::UAuracronPCGMemoryPoolManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGMemoryPoolManager);
UAuracronPCGMemoryPoolManager::~UAuracronPCGMemoryPoolManager() {}
// ********** End Class UAuracronPCGMemoryPoolManager **********************************************

// ********** Begin Class UAuracronPCGCancellationToken Function Cancel ****************************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_Cancel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_Cancel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "Cancel", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_Cancel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_Cancel_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_Cancel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_Cancel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execCancel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Cancel();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function Cancel ******************************

// ********** Begin Class UAuracronPCGCancellationToken Function ClearTimeout **********************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_ClearTimeout_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_ClearTimeout_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "ClearTimeout", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_ClearTimeout_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_ClearTimeout_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_ClearTimeout()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_ClearTimeout_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execClearTimeout)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearTimeout();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function ClearTimeout ************************

// ********** Begin Class UAuracronPCGCancellationToken Function CreateToken ***********************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics
{
	struct AuracronPCGCancellationToken_eventCreateToken_Parms
	{
		UAuracronPCGCancellationToken* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Token management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Token management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCancellationToken_eventCreateToken_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCancellationToken_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "CreateToken", Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::AuracronPCGCancellationToken_eventCreateToken_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::AuracronPCGCancellationToken_eventCreateToken_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execCreateToken)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCancellationToken**)Z_Param__Result=UAuracronPCGCancellationToken::CreateToken();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function CreateToken *************************

// ********** Begin Class UAuracronPCGCancellationToken Function GetRemainingTime ******************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics
{
	struct AuracronPCGCancellationToken_eventGetRemainingTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCancellationToken_eventGetRemainingTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "GetRemainingTime", Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::AuracronPCGCancellationToken_eventGetRemainingTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::AuracronPCGCancellationToken_eventGetRemainingTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execGetRemainingTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetRemainingTime();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function GetRemainingTime ********************

// ********** Begin Class UAuracronPCGCancellationToken Function HasTimeout ************************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics
{
	struct AuracronPCGCancellationToken_eventHasTimeout_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCancellationToken_eventHasTimeout_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCancellationToken_eventHasTimeout_Parms), &Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "HasTimeout", Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::AuracronPCGCancellationToken_eventHasTimeout_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::AuracronPCGCancellationToken_eventHasTimeout_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execHasTimeout)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasTimeout();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function HasTimeout **************************

// ********** Begin Class UAuracronPCGCancellationToken Function IsCancelled ***********************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics
{
	struct AuracronPCGCancellationToken_eventIsCancelled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Token state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Token state" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCancellationToken_eventIsCancelled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCancellationToken_eventIsCancelled_Parms), &Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "IsCancelled", Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::AuracronPCGCancellationToken_eventIsCancelled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::AuracronPCGCancellationToken_eventIsCancelled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execIsCancelled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCancelled();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function IsCancelled *************************

// ********** Begin Class UAuracronPCGCancellationToken Function RegisterCancellationCallback ******
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics
{
	struct AuracronPCGCancellationToken_eventRegisterCancellationCallback_Parms
	{
		FString CallbackId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Callbacks\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callbacks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CallbackId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::NewProp_CallbackId = { "CallbackId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCancellationToken_eventRegisterCancellationCallback_Parms, CallbackId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackId_MetaData), NewProp_CallbackId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::NewProp_CallbackId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "RegisterCancellationCallback", Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::AuracronPCGCancellationToken_eventRegisterCancellationCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::AuracronPCGCancellationToken_eventRegisterCancellationCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execRegisterCancellationCallback)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CallbackId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterCancellationCallback(Z_Param_CallbackId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function RegisterCancellationCallback ********

// ********** Begin Class UAuracronPCGCancellationToken Function Reset *****************************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_Reset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_Reset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "Reset", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_Reset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_Reset_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_Reset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_Reset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execReset)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Reset();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function Reset *******************************

// ********** Begin Class UAuracronPCGCancellationToken Function SetTimeout ************************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics
{
	struct AuracronPCGCancellationToken_eventSetTimeout_Parms
	{
		float TimeoutSeconds;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Timeout support\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timeout support" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCancellationToken_eventSetTimeout_Parms, TimeoutSeconds), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::NewProp_TimeoutSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "SetTimeout", Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::AuracronPCGCancellationToken_eventSetTimeout_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::AuracronPCGCancellationToken_eventSetTimeout_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execSetTimeout)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeoutSeconds);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTimeout(Z_Param_TimeoutSeconds);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function SetTimeout **************************

// ********** Begin Class UAuracronPCGCancellationToken Function ThrowIfCancelled ******************
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_ThrowIfCancelled_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_ThrowIfCancelled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "ThrowIfCancelled", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_ThrowIfCancelled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_ThrowIfCancelled_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_ThrowIfCancelled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_ThrowIfCancelled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execThrowIfCancelled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ThrowIfCancelled();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function ThrowIfCancelled ********************

// ********** Begin Class UAuracronPCGCancellationToken Function UnregisterCancellationCallback ****
struct Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics
{
	struct AuracronPCGCancellationToken_eventUnregisterCancellationCallback_Parms
	{
		FString CallbackId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cancellation Token" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CallbackId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::NewProp_CallbackId = { "CallbackId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCancellationToken_eventUnregisterCancellationCallback_Parms, CallbackId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackId_MetaData), NewProp_CallbackId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::NewProp_CallbackId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCancellationToken, nullptr, "UnregisterCancellationCallback", Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::AuracronPCGCancellationToken_eventUnregisterCancellationCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::AuracronPCGCancellationToken_eventUnregisterCancellationCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCancellationToken::execUnregisterCancellationCallback)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CallbackId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterCancellationCallback(Z_Param_CallbackId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCancellationToken Function UnregisterCancellationCallback ******

// ********** Begin Class UAuracronPCGCancellationToken ********************************************
void UAuracronPCGCancellationToken::StaticRegisterNativesUAuracronPCGCancellationToken()
{
	UClass* Class = UAuracronPCGCancellationToken::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "Cancel", &UAuracronPCGCancellationToken::execCancel },
		{ "ClearTimeout", &UAuracronPCGCancellationToken::execClearTimeout },
		{ "CreateToken", &UAuracronPCGCancellationToken::execCreateToken },
		{ "GetRemainingTime", &UAuracronPCGCancellationToken::execGetRemainingTime },
		{ "HasTimeout", &UAuracronPCGCancellationToken::execHasTimeout },
		{ "IsCancelled", &UAuracronPCGCancellationToken::execIsCancelled },
		{ "RegisterCancellationCallback", &UAuracronPCGCancellationToken::execRegisterCancellationCallback },
		{ "Reset", &UAuracronPCGCancellationToken::execReset },
		{ "SetTimeout", &UAuracronPCGCancellationToken::execSetTimeout },
		{ "ThrowIfCancelled", &UAuracronPCGCancellationToken::execThrowIfCancelled },
		{ "UnregisterCancellationCallback", &UAuracronPCGCancellationToken::execUnregisterCancellationCallback },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCancellationToken;
UClass* UAuracronPCGCancellationToken::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCancellationToken;
	if (!Z_Registration_Info_UClass_UAuracronPCGCancellationToken.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCancellationToken"),
			Z_Registration_Info_UClass_UAuracronPCGCancellationToken.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCancellationToken,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCancellationToken.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCancellationToken_NoRegister()
{
	return UAuracronPCGCancellationToken::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCancellationToken_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Cancellation Token\n * Provides cancellation support for async tasks\n */" },
#endif
		{ "IncludePath", "AuracronPCGAsyncProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancellation Token\nProvides cancellation support for async tasks" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_Cancel, "Cancel" }, // 3979361893
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_ClearTimeout, "ClearTimeout" }, // 2664908892
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_CreateToken, "CreateToken" }, // 1228041012
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_GetRemainingTime, "GetRemainingTime" }, // 2139237151
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_HasTimeout, "HasTimeout" }, // 541229426
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_IsCancelled, "IsCancelled" }, // 1518441585
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_RegisterCancellationCallback, "RegisterCancellationCallback" }, // 1534964155
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_Reset, "Reset" }, // 597683981
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_SetTimeout, "SetTimeout" }, // 4065900198
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_ThrowIfCancelled, "ThrowIfCancelled" }, // 3781697953
		{ &Z_Construct_UFunction_UAuracronPCGCancellationToken_UnregisterCancellationCallback, "UnregisterCancellationCallback" }, // 3911436808
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCancellationToken>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGCancellationToken_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCancellationToken_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCancellationToken_Statics::ClassParams = {
	&UAuracronPCGCancellationToken::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCancellationToken_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCancellationToken_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCancellationToken()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCancellationToken.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCancellationToken.OuterSingleton, Z_Construct_UClass_UAuracronPCGCancellationToken_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCancellationToken.OuterSingleton;
}
UAuracronPCGCancellationToken::UAuracronPCGCancellationToken(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCancellationToken);
UAuracronPCGCancellationToken::~UAuracronPCGCancellationToken() {}
// ********** End Class UAuracronPCGCancellationToken **********************************************

// ********** Begin Delegate FOnTaskCompleted ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics
{
	struct AuracronPCGAsyncTaskManager_eventOnTaskCompleted_Parms
	{
		FString TaskId;
		FAuracronPCGAsyncTaskResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events and callbacks\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events and callbacks" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventOnTaskCompleted_Parms, TaskId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventOnTaskCompleted_Parms, Result), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult, METADATA_PARAMS(0, nullptr) }; // 1566977905
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "OnTaskCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::AuracronPCGAsyncTaskManager_eventOnTaskCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::AuracronPCGAsyncTaskManager_eventOnTaskCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronPCGAsyncTaskManager::FOnTaskCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTaskCompleted, const FString& TaskId, FAuracronPCGAsyncTaskResult Result)
{
	struct AuracronPCGAsyncTaskManager_eventOnTaskCompleted_Parms
	{
		FString TaskId;
		FAuracronPCGAsyncTaskResult Result;
	};
	AuracronPCGAsyncTaskManager_eventOnTaskCompleted_Parms Parms;
	Parms.TaskId=TaskId;
	Parms.Result=Result;
	OnTaskCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTaskCompleted ********************************************************

// ********** Begin Delegate FOnTaskFailed *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics
{
	struct AuracronPCGAsyncTaskManager_eventOnTaskFailed_Parms
	{
		FString TaskId;
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventOnTaskFailed_Parms, TaskId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventOnTaskFailed_Parms, ErrorMessage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "OnTaskFailed__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::AuracronPCGAsyncTaskManager_eventOnTaskFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::AuracronPCGAsyncTaskManager_eventOnTaskFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronPCGAsyncTaskManager::FOnTaskFailed_DelegateWrapper(const FMulticastScriptDelegate& OnTaskFailed, const FString& TaskId, const FString& ErrorMessage)
{
	struct AuracronPCGAsyncTaskManager_eventOnTaskFailed_Parms
	{
		FString TaskId;
		FString ErrorMessage;
	};
	AuracronPCGAsyncTaskManager_eventOnTaskFailed_Parms Parms;
	Parms.TaskId=TaskId;
	Parms.ErrorMessage=ErrorMessage;
	OnTaskFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTaskFailed ***********************************************************

// ********** Begin Delegate FOnTaskProgress *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics
{
	struct AuracronPCGAsyncTaskManager_eventOnTaskProgress_Parms
	{
		FString TaskId;
		FAuracronPCGProgressInfo ProgressInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProgressInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventOnTaskProgress_Parms, TaskId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::NewProp_ProgressInfo = { "ProgressInfo", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventOnTaskProgress_Parms, ProgressInfo), Z_Construct_UScriptStruct_FAuracronPCGProgressInfo, METADATA_PARAMS(0, nullptr) }; // 4186792507
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::NewProp_ProgressInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "OnTaskProgress__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::AuracronPCGAsyncTaskManager_eventOnTaskProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::AuracronPCGAsyncTaskManager_eventOnTaskProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronPCGAsyncTaskManager::FOnTaskProgress_DelegateWrapper(const FMulticastScriptDelegate& OnTaskProgress, const FString& TaskId, FAuracronPCGProgressInfo ProgressInfo)
{
	struct AuracronPCGAsyncTaskManager_eventOnTaskProgress_Parms
	{
		FString TaskId;
		FAuracronPCGProgressInfo ProgressInfo;
	};
	AuracronPCGAsyncTaskManager_eventOnTaskProgress_Parms Parms;
	Parms.TaskId=TaskId;
	Parms.ProgressInfo=ProgressInfo;
	OnTaskProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTaskProgress *********************************************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function AreBatchTasksCompleted *************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics
{
	struct AuracronPCGAsyncTaskManager_eventAreBatchTasksCompleted_Parms
	{
		TArray<FString> TaskIds;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskIds_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TaskIds;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_TaskIds_Inner = { "TaskIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_TaskIds = { "TaskIds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventAreBatchTasksCompleted_Parms, TaskIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskIds_MetaData), NewProp_TaskIds_MetaData) };
void Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncTaskManager_eventAreBatchTasksCompleted_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncTaskManager_eventAreBatchTasksCompleted_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_TaskIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_TaskIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "AreBatchTasksCompleted", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::AuracronPCGAsyncTaskManager_eventAreBatchTasksCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::AuracronPCGAsyncTaskManager_eventAreBatchTasksCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execAreBatchTasksCompleted)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_TaskIds);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreBatchTasksCompleted(Z_Param_Out_TaskIds);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function AreBatchTasksCompleted ***************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function CancelAllTasks *********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelAllTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelAllTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "CancelAllTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelAllTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelAllTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelAllTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelAllTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execCancelAllTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelAllTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function CancelAllTasks ***********************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function CancelTask *************************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics
{
	struct AuracronPCGAsyncTaskManager_eventCancelTask_Parms
	{
		FString TaskId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Task management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventCancelTask_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::NewProp_TaskId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "CancelTask", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::AuracronPCGAsyncTaskManager_eventCancelTask_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::AuracronPCGAsyncTaskManager_eventCancelTask_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execCancelTask)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelTask(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function CancelTask ***************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function CleanupAllTasks ********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupAllTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupAllTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "CleanupAllTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupAllTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupAllTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupAllTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupAllTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execCleanupAllTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupAllTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function CleanupAllTasks **********************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function CleanupCompletedTasks **************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupCompletedTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cleanup\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cleanup" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupCompletedTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "CleanupCompletedTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupCompletedTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupCompletedTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupCompletedTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupCompletedTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execCleanupCompletedTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupCompletedTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function CleanupCompletedTasks ****************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function CleanupFailedTasks *****************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupFailedTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupFailedTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "CleanupFailedTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupFailedTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupFailedTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupFailedTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupFailedTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execCleanupFailedTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupFailedTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function CleanupFailedTasks *******************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function ExecuteBatchPointProcessing ********
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics
{
	struct AuracronPCGAsyncTaskManager_eventExecuteBatchPointProcessing_Parms
	{
		TArray<UPCGPointData*> PointDataArray;
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batch operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batch operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointDataArray_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PointDataArray_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PointDataArray;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_PointDataArray_Inner = { "PointDataArray", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGPointData_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_PointDataArray = { "PointDataArray", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteBatchPointProcessing_Parms, PointDataArray), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointDataArray_MetaData), NewProp_PointDataArray_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteBatchPointProcessing_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteBatchPointProcessing_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_PointDataArray_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_PointDataArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "ExecuteBatchPointProcessing", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::AuracronPCGAsyncTaskManager_eventExecuteBatchPointProcessing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::AuracronPCGAsyncTaskManager_eventExecuteBatchPointProcessing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execExecuteBatchPointProcessing)
{
	P_GET_TARRAY_REF(UPCGPointData*,Z_Param_Out_PointDataArray);
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->ExecuteBatchPointProcessing(Z_Param_Out_PointDataArray,Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function ExecuteBatchPointProcessing **********

// ********** Begin Class UAuracronPCGAsyncTaskManager Function ExecuteCustomTaskAsync *************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics
{
	struct AuracronPCGAsyncTaskManager_eventExecuteCustomTaskAsync_Parms
	{
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		FString TaskType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteCustomTaskAsync_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::NewProp_TaskType = { "TaskType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteCustomTaskAsync_Parms, TaskType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskType_MetaData), NewProp_TaskType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteCustomTaskAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::NewProp_TaskType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "ExecuteCustomTaskAsync", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::AuracronPCGAsyncTaskManager_eventExecuteCustomTaskAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::AuracronPCGAsyncTaskManager_eventExecuteCustomTaskAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execExecuteCustomTaskAsync)
{
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ExecuteCustomTaskAsync(Z_Param_Out_Descriptor,Z_Param_TaskType);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function ExecuteCustomTaskAsync ***************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function ExecuteGraphAsync ******************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics
{
	struct AuracronPCGAsyncTaskManager_eventExecuteGraphAsync_Parms
	{
		UPCGGraph* Graph;
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Graph;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::NewProp_Graph = { "Graph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteGraphAsync_Parms, Graph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteGraphAsync_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecuteGraphAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::NewProp_Graph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "ExecuteGraphAsync", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::AuracronPCGAsyncTaskManager_eventExecuteGraphAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::AuracronPCGAsyncTaskManager_eventExecuteGraphAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execExecuteGraphAsync)
{
	P_GET_OBJECT(UPCGGraph,Z_Param_Graph);
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ExecuteGraphAsync(Z_Param_Graph,Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function ExecuteGraphAsync ********************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function ExecutePointProcessingAsync ********
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics
{
	struct AuracronPCGAsyncTaskManager_eventExecutePointProcessingAsync_Parms
	{
		UPCGPointData* PointData;
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Task execution\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PointData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::NewProp_PointData = { "PointData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecutePointProcessingAsync_Parms, PointData), Z_Construct_UClass_UPCGPointData_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecutePointProcessingAsync_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventExecutePointProcessingAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::NewProp_PointData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "ExecutePointProcessingAsync", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::AuracronPCGAsyncTaskManager_eventExecutePointProcessingAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::AuracronPCGAsyncTaskManager_eventExecutePointProcessingAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execExecutePointProcessingAsync)
{
	P_GET_OBJECT(UPCGPointData,Z_Param_PointData);
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ExecutePointProcessingAsync(Z_Param_PointData,Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function ExecutePointProcessingAsync **********

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetActiveTaskCount *****************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetActiveTaskCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetActiveTaskCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetActiveTaskCount", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::AuracronPCGAsyncTaskManager_eventGetActiveTaskCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::AuracronPCGAsyncTaskManager_eventGetActiveTaskCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetActiveTaskCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveTaskCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetActiveTaskCount *******************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetActiveTasks *********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetActiveTasks_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetActiveTasks_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetActiveTasks", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::AuracronPCGAsyncTaskManager_eventGetActiveTasks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::AuracronPCGAsyncTaskManager_eventGetActiveTasks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetActiveTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActiveTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetActiveTasks ***********************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetAverageTaskExecutionTime ********
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetAverageTaskExecutionTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetAverageTaskExecutionTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetAverageTaskExecutionTime", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::AuracronPCGAsyncTaskManager_eventGetAverageTaskExecutionTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::AuracronPCGAsyncTaskManager_eventGetAverageTaskExecutionTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetAverageTaskExecutionTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageTaskExecutionTime();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetAverageTaskExecutionTime **********

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetCompletedTasks ******************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetCompletedTasks_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetCompletedTasks_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetCompletedTasks", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::AuracronPCGAsyncTaskManager_eventGetCompletedTasks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::AuracronPCGAsyncTaskManager_eventGetCompletedTasks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetCompletedTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetCompletedTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetCompletedTasks ********************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetDefaultTaskDescriptor ***********
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetDefaultTaskDescriptor_Parms
	{
		FAuracronPCGAsyncTaskDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetDefaultTaskDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(0, nullptr) }; // 620662372
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetDefaultTaskDescriptor", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::AuracronPCGAsyncTaskManager_eventGetDefaultTaskDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::AuracronPCGAsyncTaskManager_eventGetDefaultTaskDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetDefaultTaskDescriptor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGAsyncTaskDescriptor*)Z_Param__Result=P_THIS->GetDefaultTaskDescriptor();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetDefaultTaskDescriptor *************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetInstance ************************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetInstance_Parms
	{
		UAuracronPCGAsyncTaskManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGAsyncTaskManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::AuracronPCGAsyncTaskManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::AuracronPCGAsyncTaskManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGAsyncTaskManager**)Z_Param__Result=UAuracronPCGAsyncTaskManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetInstance **************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetMaxConcurrentTasks **************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetMaxConcurrentTasks_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetMaxConcurrentTasks_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetMaxConcurrentTasks", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::AuracronPCGAsyncTaskManager_eventGetMaxConcurrentTasks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::AuracronPCGAsyncTaskManager_eventGetMaxConcurrentTasks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetMaxConcurrentTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetMaxConcurrentTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetMaxConcurrentTasks ****************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetPerformanceMetrics **************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetPerformanceMetrics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetPerformanceMetrics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetPerformanceMetrics", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::AuracronPCGAsyncTaskManager_eventGetPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::AuracronPCGAsyncTaskManager_eventGetPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetPerformanceMetrics ****************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetSystemLoadPercentage ************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetSystemLoadPercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetSystemLoadPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetSystemLoadPercentage", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::AuracronPCGAsyncTaskManager_eventGetSystemLoadPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::AuracronPCGAsyncTaskManager_eventGetSystemLoadPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetSystemLoadPercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetSystemLoadPercentage();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetSystemLoadPercentage **************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetTaskProgress ********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetTaskProgress_Parms
	{
		FString TaskId;
		FAuracronPCGProgressInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetTaskProgress_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetTaskProgress_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGProgressInfo, METADATA_PARAMS(0, nullptr) }; // 4186792507
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetTaskProgress", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::AuracronPCGAsyncTaskManager_eventGetTaskProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::AuracronPCGAsyncTaskManager_eventGetTaskProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetTaskProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGProgressInfo*)Z_Param__Result=P_THIS->GetTaskProgress(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetTaskProgress **********************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetTaskResult **********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetTaskResult_Parms
	{
		FString TaskId;
		FAuracronPCGAsyncTaskResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetTaskResult_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetTaskResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult, METADATA_PARAMS(0, nullptr) }; // 1566977905
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetTaskResult", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::AuracronPCGAsyncTaskManager_eventGetTaskResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::AuracronPCGAsyncTaskManager_eventGetTaskResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetTaskResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGAsyncTaskResult*)Z_Param__Result=P_THIS->GetTaskResult(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetTaskResult ************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetTaskState ***********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetTaskState_Parms
	{
		FString TaskId;
		EAuracronPCGTaskState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetTaskState_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetTaskState_Parms, ReturnValue), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskState, METADATA_PARAMS(0, nullptr) }; // 546880179
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetTaskState", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::AuracronPCGAsyncTaskManager_eventGetTaskState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::AuracronPCGAsyncTaskManager_eventGetTaskState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetTaskState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPCGTaskState*)Z_Param__Result=P_THIS->GetTaskState(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetTaskState *************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function GetTotalTasksExecuted **************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics
{
	struct AuracronPCGAsyncTaskManager_eventGetTotalTasksExecuted_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventGetTotalTasksExecuted_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "GetTotalTasksExecuted", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::AuracronPCGAsyncTaskManager_eventGetTotalTasksExecuted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::AuracronPCGAsyncTaskManager_eventGetTotalTasksExecuted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execGetTotalTasksExecuted)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalTasksExecuted();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function GetTotalTasksExecuted ****************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function Initialize *************************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Initialize_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "Initialize", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Initialize_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execInitialize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function Initialize ***************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function IsInitialized **********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics
{
	struct AuracronPCGAsyncTaskManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncTaskManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncTaskManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::AuracronPCGAsyncTaskManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::AuracronPCGAsyncTaskManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function IsInitialized ************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function IsTaskActive ***********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics
{
	struct AuracronPCGAsyncTaskManager_eventIsTaskActive_Parms
	{
		FString TaskId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Task state queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task state queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventIsTaskActive_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
void Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncTaskManager_eventIsTaskActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncTaskManager_eventIsTaskActive_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::NewProp_TaskId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "IsTaskActive", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::AuracronPCGAsyncTaskManager_eventIsTaskActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::AuracronPCGAsyncTaskManager_eventIsTaskActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execIsTaskActive)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTaskActive(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function IsTaskActive *************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function PauseAllTasks **********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseAllTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseAllTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "PauseAllTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseAllTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseAllTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseAllTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseAllTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execPauseAllTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseAllTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function PauseAllTasks ************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function PauseTask **************************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics
{
	struct AuracronPCGAsyncTaskManager_eventPauseTask_Parms
	{
		FString TaskId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventPauseTask_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::NewProp_TaskId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "PauseTask", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::AuracronPCGAsyncTaskManager_eventPauseTask_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::AuracronPCGAsyncTaskManager_eventPauseTask_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execPauseTask)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseTask(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function PauseTask ****************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function ResumeAllTasks *********************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeAllTasks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeAllTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "ResumeAllTasks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeAllTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeAllTasks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeAllTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeAllTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execResumeAllTasks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeAllTasks();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function ResumeAllTasks ***********************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function ResumeTask *************************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics
{
	struct AuracronPCGAsyncTaskManager_eventResumeTask_Parms
	{
		FString TaskId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::NewProp_TaskId = { "TaskId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventResumeTask_Parms, TaskId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskId_MetaData), NewProp_TaskId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::NewProp_TaskId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "ResumeTask", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::AuracronPCGAsyncTaskManager_eventResumeTask_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::AuracronPCGAsyncTaskManager_eventResumeTask_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execResumeTask)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TaskId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeTask(Z_Param_TaskId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function ResumeTask ***************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function SetDefaultTaskDescriptor ***********
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics
{
	struct AuracronPCGAsyncTaskManager_eventSetDefaultTaskDescriptor_Parms
	{
		FAuracronPCGAsyncTaskDescriptor Descriptor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventSetDefaultTaskDescriptor_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::NewProp_Descriptor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "SetDefaultTaskDescriptor", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::AuracronPCGAsyncTaskManager_eventSetDefaultTaskDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::AuracronPCGAsyncTaskManager_eventSetDefaultTaskDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execSetDefaultTaskDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDefaultTaskDescriptor(Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function SetDefaultTaskDescriptor *************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function SetMaxConcurrentTasks **************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics
{
	struct AuracronPCGAsyncTaskManager_eventSetMaxConcurrentTasks_Parms
	{
		int32 MaxTasks;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxTasks;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::NewProp_MaxTasks = { "MaxTasks", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventSetMaxConcurrentTasks_Parms, MaxTasks), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::NewProp_MaxTasks,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "SetMaxConcurrentTasks", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::AuracronPCGAsyncTaskManager_eventSetMaxConcurrentTasks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::AuracronPCGAsyncTaskManager_eventSetMaxConcurrentTasks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execSetMaxConcurrentTasks)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxTasks);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaxConcurrentTasks(Z_Param_MaxTasks);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function SetMaxConcurrentTasks ****************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function Shutdown ***************************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function Shutdown *****************************

// ********** Begin Class UAuracronPCGAsyncTaskManager Function WaitForBatchCompletion *************
struct Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics
{
	struct AuracronPCGAsyncTaskManager_eventWaitForBatchCompletion_Parms
	{
		TArray<FString> TaskIds;
		float TimeoutSeconds;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Task Manager" },
		{ "CPP_Default_TimeoutSeconds", "0.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskIds_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TaskIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TaskIds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::NewProp_TaskIds_Inner = { "TaskIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::NewProp_TaskIds = { "TaskIds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventWaitForBatchCompletion_Parms, TaskIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskIds_MetaData), NewProp_TaskIds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncTaskManager_eventWaitForBatchCompletion_Parms, TimeoutSeconds), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::NewProp_TaskIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::NewProp_TaskIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::NewProp_TimeoutSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncTaskManager, nullptr, "WaitForBatchCompletion", Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::AuracronPCGAsyncTaskManager_eventWaitForBatchCompletion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::AuracronPCGAsyncTaskManager_eventWaitForBatchCompletion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncTaskManager::execWaitForBatchCompletion)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_TaskIds);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeoutSeconds);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->WaitForBatchCompletion(Z_Param_Out_TaskIds,Z_Param_TimeoutSeconds);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncTaskManager Function WaitForBatchCompletion ***************

// ********** Begin Class UAuracronPCGAsyncTaskManager *********************************************
void UAuracronPCGAsyncTaskManager::StaticRegisterNativesUAuracronPCGAsyncTaskManager()
{
	UClass* Class = UAuracronPCGAsyncTaskManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AreBatchTasksCompleted", &UAuracronPCGAsyncTaskManager::execAreBatchTasksCompleted },
		{ "CancelAllTasks", &UAuracronPCGAsyncTaskManager::execCancelAllTasks },
		{ "CancelTask", &UAuracronPCGAsyncTaskManager::execCancelTask },
		{ "CleanupAllTasks", &UAuracronPCGAsyncTaskManager::execCleanupAllTasks },
		{ "CleanupCompletedTasks", &UAuracronPCGAsyncTaskManager::execCleanupCompletedTasks },
		{ "CleanupFailedTasks", &UAuracronPCGAsyncTaskManager::execCleanupFailedTasks },
		{ "ExecuteBatchPointProcessing", &UAuracronPCGAsyncTaskManager::execExecuteBatchPointProcessing },
		{ "ExecuteCustomTaskAsync", &UAuracronPCGAsyncTaskManager::execExecuteCustomTaskAsync },
		{ "ExecuteGraphAsync", &UAuracronPCGAsyncTaskManager::execExecuteGraphAsync },
		{ "ExecutePointProcessingAsync", &UAuracronPCGAsyncTaskManager::execExecutePointProcessingAsync },
		{ "GetActiveTaskCount", &UAuracronPCGAsyncTaskManager::execGetActiveTaskCount },
		{ "GetActiveTasks", &UAuracronPCGAsyncTaskManager::execGetActiveTasks },
		{ "GetAverageTaskExecutionTime", &UAuracronPCGAsyncTaskManager::execGetAverageTaskExecutionTime },
		{ "GetCompletedTasks", &UAuracronPCGAsyncTaskManager::execGetCompletedTasks },
		{ "GetDefaultTaskDescriptor", &UAuracronPCGAsyncTaskManager::execGetDefaultTaskDescriptor },
		{ "GetInstance", &UAuracronPCGAsyncTaskManager::execGetInstance },
		{ "GetMaxConcurrentTasks", &UAuracronPCGAsyncTaskManager::execGetMaxConcurrentTasks },
		{ "GetPerformanceMetrics", &UAuracronPCGAsyncTaskManager::execGetPerformanceMetrics },
		{ "GetSystemLoadPercentage", &UAuracronPCGAsyncTaskManager::execGetSystemLoadPercentage },
		{ "GetTaskProgress", &UAuracronPCGAsyncTaskManager::execGetTaskProgress },
		{ "GetTaskResult", &UAuracronPCGAsyncTaskManager::execGetTaskResult },
		{ "GetTaskState", &UAuracronPCGAsyncTaskManager::execGetTaskState },
		{ "GetTotalTasksExecuted", &UAuracronPCGAsyncTaskManager::execGetTotalTasksExecuted },
		{ "Initialize", &UAuracronPCGAsyncTaskManager::execInitialize },
		{ "IsInitialized", &UAuracronPCGAsyncTaskManager::execIsInitialized },
		{ "IsTaskActive", &UAuracronPCGAsyncTaskManager::execIsTaskActive },
		{ "PauseAllTasks", &UAuracronPCGAsyncTaskManager::execPauseAllTasks },
		{ "PauseTask", &UAuracronPCGAsyncTaskManager::execPauseTask },
		{ "ResumeAllTasks", &UAuracronPCGAsyncTaskManager::execResumeAllTasks },
		{ "ResumeTask", &UAuracronPCGAsyncTaskManager::execResumeTask },
		{ "SetDefaultTaskDescriptor", &UAuracronPCGAsyncTaskManager::execSetDefaultTaskDescriptor },
		{ "SetMaxConcurrentTasks", &UAuracronPCGAsyncTaskManager::execSetMaxConcurrentTasks },
		{ "Shutdown", &UAuracronPCGAsyncTaskManager::execShutdown },
		{ "WaitForBatchCompletion", &UAuracronPCGAsyncTaskManager::execWaitForBatchCompletion },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager;
UClass* UAuracronPCGAsyncTaskManager::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAsyncTaskManager;
	if (!Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAsyncTaskManager"),
			Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAsyncTaskManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAsyncTaskManager_NoRegister()
{
	return UAuracronPCGAsyncTaskManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Async Task Manager\n * Central manager for all async PCG processing\n */" },
#endif
		{ "IncludePath", "AuracronPCGAsyncProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async Task Manager\nCentral manager for all async PCG processing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTaskCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTaskFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTaskProgress_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentTasks_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTaskDescriptor_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressTracker_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPoolManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTaskCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTaskFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTaskProgress;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentTasks;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultTaskDescriptor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ProgressTracker;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MemoryPoolManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_AreBatchTasksCompleted, "AreBatchTasksCompleted" }, // 3515931964
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelAllTasks, "CancelAllTasks" }, // 1464804599
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CancelTask, "CancelTask" }, // 1566998638
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupAllTasks, "CleanupAllTasks" }, // 3821097644
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupCompletedTasks, "CleanupCompletedTasks" }, // 3435653774
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_CleanupFailedTasks, "CleanupFailedTasks" }, // 579210091
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteBatchPointProcessing, "ExecuteBatchPointProcessing" }, // 4071712239
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteCustomTaskAsync, "ExecuteCustomTaskAsync" }, // 163214251
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecuteGraphAsync, "ExecuteGraphAsync" }, // 2197660986
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ExecutePointProcessingAsync, "ExecutePointProcessingAsync" }, // 870529581
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTaskCount, "GetActiveTaskCount" }, // 3410132548
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetActiveTasks, "GetActiveTasks" }, // 3260547207
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetAverageTaskExecutionTime, "GetAverageTaskExecutionTime" }, // 1078492521
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetCompletedTasks, "GetCompletedTasks" }, // 3527897325
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetDefaultTaskDescriptor, "GetDefaultTaskDescriptor" }, // 1010192310
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetInstance, "GetInstance" }, // 2521411454
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetMaxConcurrentTasks, "GetMaxConcurrentTasks" }, // 3501250186
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetPerformanceMetrics, "GetPerformanceMetrics" }, // 3175498057
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetSystemLoadPercentage, "GetSystemLoadPercentage" }, // 3062364189
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskProgress, "GetTaskProgress" }, // 3125495012
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskResult, "GetTaskResult" }, // 1063785427
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTaskState, "GetTaskState" }, // 2111105858
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_GetTotalTasksExecuted, "GetTotalTasksExecuted" }, // 344513319
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Initialize, "Initialize" }, // 3775964327
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsInitialized, "IsInitialized" }, // 607980919
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_IsTaskActive, "IsTaskActive" }, // 157762776
		{ &Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature, "OnTaskCompleted__DelegateSignature" }, // 866307764
		{ &Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature, "OnTaskFailed__DelegateSignature" }, // 2283740099
		{ &Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature, "OnTaskProgress__DelegateSignature" }, // 1505491515
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseAllTasks, "PauseAllTasks" }, // 1602691446
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_PauseTask, "PauseTask" }, // 2358206395
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeAllTasks, "ResumeAllTasks" }, // 468764353
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_ResumeTask, "ResumeTask" }, // 3395372506
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetDefaultTaskDescriptor, "SetDefaultTaskDescriptor" }, // 3667511271
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_SetMaxConcurrentTasks, "SetMaxConcurrentTasks" }, // 573221883
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_Shutdown, "Shutdown" }, // 889359656
		{ &Z_Construct_UFunction_UAuracronPCGAsyncTaskManager_WaitForBatchCompletion, "WaitForBatchCompletion" }, // 3248542565
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAsyncTaskManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_OnTaskCompleted = { "OnTaskCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAsyncTaskManager, OnTaskCompleted), Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTaskCompleted_MetaData), NewProp_OnTaskCompleted_MetaData) }; // 866307764
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_OnTaskFailed = { "OnTaskFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAsyncTaskManager, OnTaskFailed), Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTaskFailed_MetaData), NewProp_OnTaskFailed_MetaData) }; // 2283740099
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_OnTaskProgress = { "OnTaskProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAsyncTaskManager, OnTaskProgress), Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTaskProgress_MetaData), NewProp_OnTaskProgress_MetaData) }; // 1505491515
void Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronPCGAsyncTaskManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAsyncTaskManager), &Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_MaxConcurrentTasks = { "MaxConcurrentTasks", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAsyncTaskManager, MaxConcurrentTasks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentTasks_MetaData), NewProp_MaxConcurrentTasks_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_DefaultTaskDescriptor = { "DefaultTaskDescriptor", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAsyncTaskManager, DefaultTaskDescriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTaskDescriptor_MetaData), NewProp_DefaultTaskDescriptor_MetaData) }; // 620662372
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_ProgressTracker = { "ProgressTracker", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAsyncTaskManager, ProgressTracker), Z_Construct_UClass_UAuracronPCGProgressTracker_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressTracker_MetaData), NewProp_ProgressTracker_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_MemoryPoolManager = { "MemoryPoolManager", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAsyncTaskManager, MemoryPoolManager), Z_Construct_UClass_UAuracronPCGMemoryPoolManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPoolManager_MetaData), NewProp_MemoryPoolManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_OnTaskCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_OnTaskFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_OnTaskProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_MaxConcurrentTasks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_DefaultTaskDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_ProgressTracker,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::NewProp_MemoryPoolManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::ClassParams = {
	&UAuracronPCGAsyncTaskManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAsyncTaskManager()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager.OuterSingleton, Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager.OuterSingleton;
}
UAuracronPCGAsyncTaskManager::UAuracronPCGAsyncTaskManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAsyncTaskManager);
UAuracronPCGAsyncTaskManager::~UAuracronPCGAsyncTaskManager() {}
// ********** End Class UAuracronPCGAsyncTaskManager ***********************************************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function CalculateOptimalBatchSize ******
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventCalculateOptimalBatchSize_Parms
	{
		int32 DataSize;
		int32 ThreadCount;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Task optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventCalculateOptimalBatchSize_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventCalculateOptimalBatchSize_Parms, ThreadCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventCalculateOptimalBatchSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::NewProp_ThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "CalculateOptimalBatchSize", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::AuracronPCGAsyncProcessingUtils_eventCalculateOptimalBatchSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::AuracronPCGAsyncProcessingUtils_eventCalculateOptimalBatchSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execCalculateOptimalBatchSize)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_GET_PROPERTY(FIntProperty,Z_Param_ThreadCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::CalculateOptimalBatchSize(Z_Param_DataSize,Z_Param_ThreadCount);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function CalculateOptimalBatchSize ********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function CreateOptimizedDescriptor ******
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventCreateOptimizedDescriptor_Parms
	{
		int32 DataSize;
		EAuracronPCGTaskPriority Priority;
		FAuracronPCGAsyncTaskDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventCreateOptimizedDescriptor_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventCreateOptimizedDescriptor_Parms, Priority), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTaskPriority, METADATA_PARAMS(0, nullptr) }; // 2732136417
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventCreateOptimizedDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(0, nullptr) }; // 620662372
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "CreateOptimizedDescriptor", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::AuracronPCGAsyncProcessingUtils_eventCreateOptimizedDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::AuracronPCGAsyncProcessingUtils_eventCreateOptimizedDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execCreateOptimizedDescriptor)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_GET_ENUM(EAuracronPCGTaskPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGAsyncTaskDescriptor*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::CreateOptimizedDescriptor(Z_Param_DataSize,EAuracronPCGTaskPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function CreateOptimizedDescriptor ********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function EnableAsyncDebugging ***********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventEnableAsyncDebugging_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debugging utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debugging utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronPCGAsyncProcessingUtils_eventEnableAsyncDebugging_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncProcessingUtils_eventEnableAsyncDebugging_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "EnableAsyncDebugging", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::AuracronPCGAsyncProcessingUtils_eventEnableAsyncDebugging_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::AuracronPCGAsyncProcessingUtils_eventEnableAsyncDebugging_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execEnableAsyncDebugging)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGAsyncProcessingUtils::EnableAsyncDebugging(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function EnableAsyncDebugging *************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function EstimateExecutionTime **********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventEstimateExecutionTime_Parms
	{
		int32 DataSize;
		EAuracronPCGAsyncProcessingMode Mode;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance analysis\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance analysis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventEstimateExecutionTime_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventEstimateExecutionTime_Parms, Mode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode, METADATA_PARAMS(0, nullptr) }; // 1257345079
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventEstimateExecutionTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_Mode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "EstimateExecutionTime", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::AuracronPCGAsyncProcessingUtils_eventEstimateExecutionTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::AuracronPCGAsyncProcessingUtils_eventEstimateExecutionTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execEstimateExecutionTime)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_GET_ENUM(EAuracronPCGAsyncProcessingMode,Z_Param_Mode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::EstimateExecutionTime(Z_Param_DataSize,EAuracronPCGAsyncProcessingMode(Z_Param_Mode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function EstimateExecutionTime ************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function EstimateMemoryUsage ************
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventEstimateMemoryUsage_Parms
	{
		int32 DataSize;
		EAuracronPCGAsyncProcessingMode Mode;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventEstimateMemoryUsage_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventEstimateMemoryUsage_Parms, Mode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode, METADATA_PARAMS(0, nullptr) }; // 1257345079
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventEstimateMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_Mode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "EstimateMemoryUsage", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::AuracronPCGAsyncProcessingUtils_eventEstimateMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::AuracronPCGAsyncProcessingUtils_eventEstimateMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execEstimateMemoryUsage)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_GET_ENUM(EAuracronPCGAsyncProcessingMode,Z_Param_Mode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::EstimateMemoryUsage(Z_Param_DataSize,EAuracronPCGAsyncProcessingMode(Z_Param_Mode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function EstimateMemoryUsage **************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function GeneratePerformanceReport ******
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventGeneratePerformanceReport_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventGeneratePerformanceReport_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "GeneratePerformanceReport", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::AuracronPCGAsyncProcessingUtils_eventGeneratePerformanceReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::AuracronPCGAsyncProcessingUtils_eventGeneratePerformanceReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execGeneratePerformanceReport)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::GeneratePerformanceReport();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function GeneratePerformanceReport ********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function GetAvailableMemoryMB ***********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventGetAvailableMemoryMB_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventGetAvailableMemoryMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "GetAvailableMemoryMB", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::AuracronPCGAsyncProcessingUtils_eventGetAvailableMemoryMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::AuracronPCGAsyncProcessingUtils_eventGetAvailableMemoryMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execGetAvailableMemoryMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::GetAvailableMemoryMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function GetAvailableMemoryMB *************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function GetCPUUsagePercentage **********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventGetCPUUsagePercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventGetCPUUsagePercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "GetCPUUsagePercentage", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::AuracronPCGAsyncProcessingUtils_eventGetCPUUsagePercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::AuracronPCGAsyncProcessingUtils_eventGetCPUUsagePercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execGetCPUUsagePercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::GetCPUUsagePercentage();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function GetCPUUsagePercentage ************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function GetOptimalThreadCount **********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventGetOptimalThreadCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// System information\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "System information" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventGetOptimalThreadCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "GetOptimalThreadCount", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::AuracronPCGAsyncProcessingUtils_eventGetOptimalThreadCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::AuracronPCGAsyncProcessingUtils_eventGetOptimalThreadCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execGetOptimalThreadCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::GetOptimalThreadCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function GetOptimalThreadCount ************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function IsAsyncDebuggingEnabled ********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventIsAsyncDebuggingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncProcessingUtils_eventIsAsyncDebuggingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncProcessingUtils_eventIsAsyncDebuggingEnabled_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "IsAsyncDebuggingEnabled", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::AuracronPCGAsyncProcessingUtils_eventIsAsyncDebuggingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::AuracronPCGAsyncProcessingUtils_eventIsAsyncDebuggingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execIsAsyncDebuggingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::IsAsyncDebuggingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function IsAsyncDebuggingEnabled **********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function IsMultithreadingSupported ******
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventIsMultithreadingSupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncProcessingUtils_eventIsMultithreadingSupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncProcessingUtils_eventIsMultithreadingSupported_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "IsMultithreadingSupported", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::AuracronPCGAsyncProcessingUtils_eventIsMultithreadingSupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::AuracronPCGAsyncProcessingUtils_eventIsMultithreadingSupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execIsMultithreadingSupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::IsMultithreadingSupported();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function IsMultithreadingSupported ********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function IsTaskDescriptorOptimal ********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventIsTaskDescriptorOptimal_Parms
	{
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		int32 DataSize;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventIsTaskDescriptorOptimal_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventIsTaskDescriptorOptimal_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncProcessingUtils_eventIsTaskDescriptorOptimal_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncProcessingUtils_eventIsTaskDescriptorOptimal_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "IsTaskDescriptorOptimal", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::AuracronPCGAsyncProcessingUtils_eventIsTaskDescriptorOptimal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::AuracronPCGAsyncProcessingUtils_eventIsTaskDescriptorOptimal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execIsTaskDescriptorOptimal)
{
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::IsTaskDescriptorOptimal(Z_Param_Out_Descriptor,Z_Param_DataSize);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function IsTaskDescriptorOptimal **********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function LogSystemInfo ******************
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_LogSystemInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_LogSystemInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "LogSystemInfo", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_LogSystemInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_LogSystemInfo_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_LogSystemInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_LogSystemInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execLogSystemInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGAsyncProcessingUtils::LogSystemInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function LogSystemInfo ********************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function OptimizeTaskDescriptor *********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventOptimizeTaskDescriptor_Parms
	{
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		int32 DataSize;
		FAuracronPCGAsyncTaskDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventOptimizeTaskDescriptor_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventOptimizeTaskDescriptor_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventOptimizeTaskDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(0, nullptr) }; // 620662372
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "OptimizeTaskDescriptor", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::AuracronPCGAsyncProcessingUtils_eventOptimizeTaskDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::AuracronPCGAsyncProcessingUtils_eventOptimizeTaskDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execOptimizeTaskDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGAsyncTaskDescriptor*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::OptimizeTaskDescriptor(Z_Param_Out_Descriptor,Z_Param_DataSize);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function OptimizeTaskDescriptor ***********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function RecommendProcessingMode ********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventRecommendProcessingMode_Parms
	{
		int32 DataSize;
		int32 Complexity;
		EAuracronPCGAsyncProcessingMode ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Complexity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventRecommendProcessingMode_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_Complexity = { "Complexity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventRecommendProcessingMode_Parms, Complexity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventRecommendProcessingMode_Parms, ReturnValue), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAsyncProcessingMode, METADATA_PARAMS(0, nullptr) }; // 1257345079
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_Complexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "RecommendProcessingMode", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::AuracronPCGAsyncProcessingUtils_eventRecommendProcessingMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::AuracronPCGAsyncProcessingUtils_eventRecommendProcessingMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execRecommendProcessingMode)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_GET_PROPERTY(FIntProperty,Z_Param_Complexity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPCGAsyncProcessingMode*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::RecommendProcessingMode(Z_Param_DataSize,Z_Param_Complexity);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function RecommendProcessingMode **********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function ValidateTaskDescriptor *********
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventValidateTaskDescriptor_Parms
	{
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		TArray<FString> OutErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventValidateTaskDescriptor_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_OutErrors_Inner = { "OutErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_OutErrors = { "OutErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventValidateTaskDescriptor_Parms, OutErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncProcessingUtils_eventValidateTaskDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncProcessingUtils_eventValidateTaskDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_OutErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_OutErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "ValidateTaskDescriptor", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::AuracronPCGAsyncProcessingUtils_eventValidateTaskDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::AuracronPCGAsyncProcessingUtils_eventValidateTaskDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execValidateTaskDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_GET_TARRAY_REF(FString,Z_Param_Out_OutErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::ValidateTaskDescriptor(Z_Param_Out_Descriptor,Z_Param_Out_OutErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function ValidateTaskDescriptor ***********

// ********** Begin Class UAuracronPCGAsyncProcessingUtils Function WillTaskFitInMemory ************
struct Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics
{
	struct AuracronPCGAsyncProcessingUtils_eventWillTaskFitInMemory_Parms
	{
		FAuracronPCGAsyncTaskDescriptor Descriptor;
		int32 DataSize;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSize;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventWillTaskFitInMemory_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 620662372
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_DataSize = { "DataSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAsyncProcessingUtils_eventWillTaskFitInMemory_Parms, DataSize), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAsyncProcessingUtils_eventWillTaskFitInMemory_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAsyncProcessingUtils_eventWillTaskFitInMemory_Parms), &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_DataSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, nullptr, "WillTaskFitInMemory", Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::AuracronPCGAsyncProcessingUtils_eventWillTaskFitInMemory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::AuracronPCGAsyncProcessingUtils_eventWillTaskFitInMemory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAsyncProcessingUtils::execWillTaskFitInMemory)
{
	P_GET_STRUCT_REF(FAuracronPCGAsyncTaskDescriptor,Z_Param_Out_Descriptor);
	P_GET_PROPERTY(FIntProperty,Z_Param_DataSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAsyncProcessingUtils::WillTaskFitInMemory(Z_Param_Out_Descriptor,Z_Param_DataSize);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAsyncProcessingUtils Function WillTaskFitInMemory **************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils *****************************************
void UAuracronPCGAsyncProcessingUtils::StaticRegisterNativesUAuracronPCGAsyncProcessingUtils()
{
	UClass* Class = UAuracronPCGAsyncProcessingUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateOptimalBatchSize", &UAuracronPCGAsyncProcessingUtils::execCalculateOptimalBatchSize },
		{ "CreateOptimizedDescriptor", &UAuracronPCGAsyncProcessingUtils::execCreateOptimizedDescriptor },
		{ "EnableAsyncDebugging", &UAuracronPCGAsyncProcessingUtils::execEnableAsyncDebugging },
		{ "EstimateExecutionTime", &UAuracronPCGAsyncProcessingUtils::execEstimateExecutionTime },
		{ "EstimateMemoryUsage", &UAuracronPCGAsyncProcessingUtils::execEstimateMemoryUsage },
		{ "GeneratePerformanceReport", &UAuracronPCGAsyncProcessingUtils::execGeneratePerformanceReport },
		{ "GetAvailableMemoryMB", &UAuracronPCGAsyncProcessingUtils::execGetAvailableMemoryMB },
		{ "GetCPUUsagePercentage", &UAuracronPCGAsyncProcessingUtils::execGetCPUUsagePercentage },
		{ "GetOptimalThreadCount", &UAuracronPCGAsyncProcessingUtils::execGetOptimalThreadCount },
		{ "IsAsyncDebuggingEnabled", &UAuracronPCGAsyncProcessingUtils::execIsAsyncDebuggingEnabled },
		{ "IsMultithreadingSupported", &UAuracronPCGAsyncProcessingUtils::execIsMultithreadingSupported },
		{ "IsTaskDescriptorOptimal", &UAuracronPCGAsyncProcessingUtils::execIsTaskDescriptorOptimal },
		{ "LogSystemInfo", &UAuracronPCGAsyncProcessingUtils::execLogSystemInfo },
		{ "OptimizeTaskDescriptor", &UAuracronPCGAsyncProcessingUtils::execOptimizeTaskDescriptor },
		{ "RecommendProcessingMode", &UAuracronPCGAsyncProcessingUtils::execRecommendProcessingMode },
		{ "ValidateTaskDescriptor", &UAuracronPCGAsyncProcessingUtils::execValidateTaskDescriptor },
		{ "WillTaskFitInMemory", &UAuracronPCGAsyncProcessingUtils::execWillTaskFitInMemory },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils;
UClass* UAuracronPCGAsyncProcessingUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAsyncProcessingUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAsyncProcessingUtils"),
			Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAsyncProcessingUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_NoRegister()
{
	return UAuracronPCGAsyncProcessingUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Async Processing Utilities\n * Utility functions for async processing\n */" },
#endif
		{ "IncludePath", "AuracronPCGAsyncProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAsyncProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async Processing Utilities\nUtility functions for async processing" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CalculateOptimalBatchSize, "CalculateOptimalBatchSize" }, // 2551260515
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_CreateOptimizedDescriptor, "CreateOptimizedDescriptor" }, // 203714354
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EnableAsyncDebugging, "EnableAsyncDebugging" }, // 2478107252
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateExecutionTime, "EstimateExecutionTime" }, // 3373803367
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_EstimateMemoryUsage, "EstimateMemoryUsage" }, // 1981930498
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GeneratePerformanceReport, "GeneratePerformanceReport" }, // 1399780878
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetAvailableMemoryMB, "GetAvailableMemoryMB" }, // 3834604373
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetCPUUsagePercentage, "GetCPUUsagePercentage" }, // 208666271
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_GetOptimalThreadCount, "GetOptimalThreadCount" }, // 2926679520
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsAsyncDebuggingEnabled, "IsAsyncDebuggingEnabled" }, // 1978571177
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsMultithreadingSupported, "IsMultithreadingSupported" }, // 1068376482
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_IsTaskDescriptorOptimal, "IsTaskDescriptorOptimal" }, // 767155794
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_LogSystemInfo, "LogSystemInfo" }, // 215624464
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_OptimizeTaskDescriptor, "OptimizeTaskDescriptor" }, // 3982200061
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_RecommendProcessingMode, "RecommendProcessingMode" }, // 3973305940
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_ValidateTaskDescriptor, "ValidateTaskDescriptor" }, // 2603783777
		{ &Z_Construct_UFunction_UAuracronPCGAsyncProcessingUtils_WillTaskFitInMemory, "WillTaskFitInMemory" }, // 3884686683
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAsyncProcessingUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics::ClassParams = {
	&UAuracronPCGAsyncProcessingUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils.OuterSingleton;
}
UAuracronPCGAsyncProcessingUtils::UAuracronPCGAsyncProcessingUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAsyncProcessingUtils);
UAuracronPCGAsyncProcessingUtils::~UAuracronPCGAsyncProcessingUtils() {}
// ********** End Class UAuracronPCGAsyncProcessingUtils *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGAsyncProcessingMode_StaticEnum, TEXT("EAuracronPCGAsyncProcessingMode"), &Z_Registration_Info_UEnum_EAuracronPCGAsyncProcessingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1257345079U) },
		{ EAuracronPCGTaskPriority_StaticEnum, TEXT("EAuracronPCGTaskPriority"), &Z_Registration_Info_UEnum_EAuracronPCGTaskPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2732136417U) },
		{ EAuracronPCGTaskState_StaticEnum, TEXT("EAuracronPCGTaskState"), &Z_Registration_Info_UEnum_EAuracronPCGTaskState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 546880179U) },
		{ EAuracronPCGThreadType_StaticEnum, TEXT("EAuracronPCGThreadType"), &Z_Registration_Info_UEnum_EAuracronPCGThreadType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1299885978U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGAsyncTaskDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics::NewStructOps, TEXT("AuracronPCGAsyncTaskDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGAsyncTaskDescriptor), 620662372U) },
		{ FAuracronPCGProgressInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics::NewStructOps, TEXT("AuracronPCGProgressInfo"), &Z_Registration_Info_UScriptStruct_FAuracronPCGProgressInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGProgressInfo), 4186792507U) },
		{ FAuracronPCGAsyncTaskResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics::NewStructOps, TEXT("AuracronPCGAsyncTaskResult"), &Z_Registration_Info_UScriptStruct_FAuracronPCGAsyncTaskResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGAsyncTaskResult), 1566977905U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGProgressTracker, UAuracronPCGProgressTracker::StaticClass, TEXT("UAuracronPCGProgressTracker"), &Z_Registration_Info_UClass_UAuracronPCGProgressTracker, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGProgressTracker), 833536773U) },
		{ Z_Construct_UClass_UAuracronPCGMemoryPoolManager, UAuracronPCGMemoryPoolManager::StaticClass, TEXT("UAuracronPCGMemoryPoolManager"), &Z_Registration_Info_UClass_UAuracronPCGMemoryPoolManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGMemoryPoolManager), 2890978974U) },
		{ Z_Construct_UClass_UAuracronPCGCancellationToken, UAuracronPCGCancellationToken::StaticClass, TEXT("UAuracronPCGCancellationToken"), &Z_Registration_Info_UClass_UAuracronPCGCancellationToken, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCancellationToken), 4104508448U) },
		{ Z_Construct_UClass_UAuracronPCGAsyncTaskManager, UAuracronPCGAsyncTaskManager::StaticClass, TEXT("UAuracronPCGAsyncTaskManager"), &Z_Registration_Info_UClass_UAuracronPCGAsyncTaskManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAsyncTaskManager), 4197651343U) },
		{ Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils, UAuracronPCGAsyncProcessingUtils::StaticClass, TEXT("UAuracronPCGAsyncProcessingUtils"), &Z_Registration_Info_UClass_UAuracronPCGAsyncProcessingUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAsyncProcessingUtils), 3915752201U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_2573059690(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
