// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronHairGeneration.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronHairGeneration_generated_h
#error "AuracronHairGeneration.generated.h already included, missing '#pragma once' in AuracronHairGeneration.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronHairGeneration_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FHairStrandData ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_78_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairStrandData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairStrandData;
// ********** End ScriptStruct FHairStrandData *****************************************************

// ********** Begin ScriptStruct FHairStylingData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_111_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairStylingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairStylingData;
// ********** End ScriptStruct FHairStylingData ****************************************************

// ********** Begin ScriptStruct FHairColorData ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_147_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairColorData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairColorData;
// ********** End ScriptStruct FHairColorData ******************************************************

// ********** Begin ScriptStruct FHairPhysicsData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_177_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairPhysicsData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairPhysicsData;
// ********** End ScriptStruct FHairPhysicsData ****************************************************

// ********** Begin ScriptStruct FHairCardData *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_210_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairCardData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairCardData;
// ********** End ScriptStruct FHairCardData *******************************************************

// ********** Begin ScriptStruct FHairLODData ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_234_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairLODData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairLODData;
// ********** End ScriptStruct FHairLODData ********************************************************

// ********** Begin ScriptStruct FHairNoiseParameters **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_258_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairNoiseParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairNoiseParameters;
// ********** End ScriptStruct FHairNoiseParameters ************************************************

// ********** Begin ScriptStruct FHairGenerationParameters *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h_282_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHairGenerationParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHairGenerationParameters;
// ********** End ScriptStruct FHairGenerationParameters *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h

// ********** Begin Enum EHairType *****************************************************************
#define FOREACH_ENUM_EHAIRTYPE(op) \
	op(EHairType::Straight) \
	op(EHairType::Wavy) \
	op(EHairType::Curly) \
	op(EHairType::Coily) \
	op(EHairType::Kinky) 

enum class EHairType : uint8;
template<> struct TIsUEnumClass<EHairType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairType>();
// ********** End Enum EHairType *******************************************************************

// ********** Begin Enum EHairLength ***************************************************************
#define FOREACH_ENUM_EHAIRLENGTH(op) \
	op(EHairLength::VeryShort) \
	op(EHairLength::Short) \
	op(EHairLength::Medium) \
	op(EHairLength::Long) \
	op(EHairLength::VeryLong) 

enum class EHairLength : uint8;
template<> struct TIsUEnumClass<EHairLength> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairLength>();
// ********** End Enum EHairLength *****************************************************************

// ********** Begin Enum EHairDensity **************************************************************
#define FOREACH_ENUM_EHAIRDENSITY(op) \
	op(EHairDensity::Sparse) \
	op(EHairDensity::Low) \
	op(EHairDensity::Medium) \
	op(EHairDensity::High) \
	op(EHairDensity::VeryHigh) 

enum class EHairDensity : uint8;
template<> struct TIsUEnumClass<EHairDensity> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairDensity>();
// ********** End Enum EHairDensity ****************************************************************

// ********** Begin Enum EHairRenderingMode ********************************************************
#define FOREACH_ENUM_EHAIRRENDERINGMODE(op) \
	op(EHairRenderingMode::Strands) \
	op(EHairRenderingMode::Cards) \
	op(EHairRenderingMode::Meshes) \
	op(EHairRenderingMode::Hybrid) 

enum class EHairRenderingMode : uint8;
template<> struct TIsUEnumClass<EHairRenderingMode> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairRenderingMode>();
// ********** End Enum EHairRenderingMode **********************************************************

// ********** Begin Enum EHairPhysicsType **********************************************************
#define FOREACH_ENUM_EHAIRPHYSICSTYPE(op) \
	op(EHairPhysicsType::None) \
	op(EHairPhysicsType::Simple) \
	op(EHairPhysicsType::Advanced) \
	op(EHairPhysicsType::Niagara) 

enum class EHairPhysicsType : uint8;
template<> struct TIsUEnumClass<EHairPhysicsType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairPhysicsType>();
// ********** End Enum EHairPhysicsType ************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
