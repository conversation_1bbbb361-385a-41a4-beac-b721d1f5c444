// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPCGAsyncGenerationParams();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPerformanceData();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPCGPerformanceData ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGPerformanceData;
class UScriptStruct* FPCGPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("PCGPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance monitoring data for PCG operations\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring data for PCG operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsGenerated_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsage_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AsyncTasksExecuted_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageProcessingTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsGenerated;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AsyncTasksExecuted;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageProcessingTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceData, GenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_PointsGenerated = { "PointsGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceData, PointsGenerated), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsGenerated_MetaData), NewProp_PointsGenerated_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_MemoryUsage = { "MemoryUsage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceData, MemoryUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsage_MetaData), NewProp_MemoryUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_AsyncTasksExecuted = { "AsyncTasksExecuted", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceData, AsyncTasksExecuted), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AsyncTasksExecuted_MetaData), NewProp_AsyncTasksExecuted_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_AverageProcessingTime = { "AverageProcessingTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceData, AverageProcessingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageProcessingTime_MetaData), NewProp_AverageProcessingTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_GenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_PointsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_MemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_AsyncTasksExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewProp_AverageProcessingTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"PCGPerformanceData",
	Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::PropPointers),
	sizeof(FPCGPerformanceData),
	alignof(FPCGPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FPCGPerformanceData *************************************************

// ********** Begin ScriptStruct FPCGAsyncGenerationParams *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams;
class UScriptStruct* FPCGAsyncGenerationParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGAsyncGenerationParams, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("PCGAsyncGenerationParams"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Async PCG generation parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async PCG generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncGeneration_MetaData[] = {
		{ "Category", "Async" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentTasks_MetaData[] = {
		{ "Category", "Async" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSlicePerFrame_MetaData[] = {
		{ "Category", "Async" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProgressCallback_MetaData[] = {
		{ "Category", "Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 16ms\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "16ms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMemoryOptimization_MetaData[] = {
		{ "Category", "Async" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bUseAsyncGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncGeneration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentTasks;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeSlicePerFrame;
	static void NewProp_bEnableProgressCallback_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProgressCallback;
	static void NewProp_bEnableMemoryOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMemoryOptimization;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGAsyncGenerationParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bUseAsyncGeneration_SetBit(void* Obj)
{
	((FPCGAsyncGenerationParams*)Obj)->bUseAsyncGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bUseAsyncGeneration = { "bUseAsyncGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGAsyncGenerationParams), &Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bUseAsyncGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncGeneration_MetaData), NewProp_bUseAsyncGeneration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_MaxConcurrentTasks = { "MaxConcurrentTasks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAsyncGenerationParams, MaxConcurrentTasks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentTasks_MetaData), NewProp_MaxConcurrentTasks_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_TimeSlicePerFrame = { "TimeSlicePerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAsyncGenerationParams, TimeSlicePerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSlicePerFrame_MetaData), NewProp_TimeSlicePerFrame_MetaData) };
void Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableProgressCallback_SetBit(void* Obj)
{
	((FPCGAsyncGenerationParams*)Obj)->bEnableProgressCallback = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableProgressCallback = { "bEnableProgressCallback", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGAsyncGenerationParams), &Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableProgressCallback_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProgressCallback_MetaData), NewProp_bEnableProgressCallback_MetaData) };
void Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableMemoryOptimization_SetBit(void* Obj)
{
	((FPCGAsyncGenerationParams*)Obj)->bEnableMemoryOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableMemoryOptimization = { "bEnableMemoryOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGAsyncGenerationParams), &Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableMemoryOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMemoryOptimization_MetaData), NewProp_bEnableMemoryOptimization_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bUseAsyncGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_MaxConcurrentTasks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_TimeSlicePerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableProgressCallback,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewProp_bEnableMemoryOptimization,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"PCGAsyncGenerationParams",
	Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::PropPointers),
	sizeof(FPCGAsyncGenerationParams),
	alignof(FPCGAsyncGenerationParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGAsyncGenerationParams()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams.InnerSingleton, Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams.InnerSingleton;
}
// ********** End ScriptStruct FPCGAsyncGenerationParams *******************************************

// ********** Begin ScriptStruct FPCGMemoryOptimizationSettings ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings;
class UScriptStruct* FPCGMemoryOptimizationSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("PCGMemoryOptimizationSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * PCG Memory optimization settings\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG Memory optimization settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMemoryPooling_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCacheSize_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDataStreaming_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 100MB\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "100MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GCThreshold_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCompression_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 80% memory usage\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "80% memory usage" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableMemoryPooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMemoryPooling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCacheSize;
	static void NewProp_bEnableDataStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDataStreaming;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GCThreshold;
	static void NewProp_bEnableCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCompression;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGMemoryOptimizationSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableMemoryPooling_SetBit(void* Obj)
{
	((FPCGMemoryOptimizationSettings*)Obj)->bEnableMemoryPooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableMemoryPooling = { "bEnableMemoryPooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGMemoryOptimizationSettings), &Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableMemoryPooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMemoryPooling_MetaData), NewProp_bEnableMemoryPooling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_MaxCacheSize = { "MaxCacheSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGMemoryOptimizationSettings, MaxCacheSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCacheSize_MetaData), NewProp_MaxCacheSize_MetaData) };
void Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableDataStreaming_SetBit(void* Obj)
{
	((FPCGMemoryOptimizationSettings*)Obj)->bEnableDataStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableDataStreaming = { "bEnableDataStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGMemoryOptimizationSettings), &Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableDataStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDataStreaming_MetaData), NewProp_bEnableDataStreaming_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_GCThreshold = { "GCThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGMemoryOptimizationSettings, GCThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GCThreshold_MetaData), NewProp_GCThreshold_MetaData) };
void Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableCompression_SetBit(void* Obj)
{
	((FPCGMemoryOptimizationSettings*)Obj)->bEnableCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableCompression = { "bEnableCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGMemoryOptimizationSettings), &Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCompression_MetaData), NewProp_bEnableCompression_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableMemoryPooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_MaxCacheSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableDataStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_GCThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewProp_bEnableCompression,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"PCGMemoryOptimizationSettings",
	Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::PropPointers),
	sizeof(FPCGMemoryOptimizationSettings),
	alignof(FPCGMemoryOptimizationSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings.InnerSingleton, Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings.InnerSingleton;
}
// ********** End ScriptStruct FPCGMemoryOptimizationSettings **************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FPCGPerformanceData_Statics::NewStructOps, TEXT("PCGPerformanceData"), &Z_Registration_Info_UScriptStruct_FPCGPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGPerformanceData), 953834958U) },
		{ FPCGAsyncGenerationParams::StaticStruct, Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics::NewStructOps, TEXT("PCGAsyncGenerationParams"), &Z_Registration_Info_UScriptStruct_FPCGAsyncGenerationParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGAsyncGenerationParams), 4037517306U) },
		{ FPCGMemoryOptimizationSettings::StaticStruct, Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics::NewStructOps, TEXT("PCGMemoryOptimizationSettings"), &Z_Registration_Info_UScriptStruct_FPCGMemoryOptimizationSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGMemoryOptimizationSettings), 1323163792U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h__Script_AuracronPCGBridge_1516652935(TEXT("/Script/AuracronPCGBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
