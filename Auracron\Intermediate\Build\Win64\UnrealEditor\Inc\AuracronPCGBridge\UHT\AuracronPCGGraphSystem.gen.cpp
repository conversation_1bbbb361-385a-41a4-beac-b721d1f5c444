// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGGraphSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGGraphSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGGraphWrapper();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPinConnection();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGNode_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSettings_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGGraphState ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGGraphState;
static UEnum* EAuracronPCGGraphState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGGraphState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGGraphState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGGraphState"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGGraphState.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGGraphState>()
{
	return EAuracronPCGGraphState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cancelled.DisplayName", "Cancelled" },
		{ "Cancelled.Name", "EAuracronPCGGraphState::Cancelled" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph execution state\n" },
#endif
		{ "Compiling.DisplayName", "Compiling" },
		{ "Compiling.Name", "EAuracronPCGGraphState::Compiling" },
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "EAuracronPCGGraphState::Completed" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronPCGGraphState::Error" },
		{ "Executing.DisplayName", "Executing" },
		{ "Executing.Name", "EAuracronPCGGraphState::Executing" },
		{ "Idle.DisplayName", "Idle" },
		{ "Idle.Name", "EAuracronPCGGraphState::Idle" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph execution state" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGGraphState::Idle", (int64)EAuracronPCGGraphState::Idle },
		{ "EAuracronPCGGraphState::Compiling", (int64)EAuracronPCGGraphState::Compiling },
		{ "EAuracronPCGGraphState::Executing", (int64)EAuracronPCGGraphState::Executing },
		{ "EAuracronPCGGraphState::Completed", (int64)EAuracronPCGGraphState::Completed },
		{ "EAuracronPCGGraphState::Error", (int64)EAuracronPCGGraphState::Error },
		{ "EAuracronPCGGraphState::Cancelled", (int64)EAuracronPCGGraphState::Cancelled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGGraphState",
	"EAuracronPCGGraphState",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGGraphState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGGraphState.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGGraphState.InnerSingleton;
}
// ********** End Enum EAuracronPCGGraphState ******************************************************

// ********** Begin Enum EAuracronPCGPinType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPinType;
static UEnum* EAuracronPCGPinType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPinType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPinType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPinType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPinType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPinType>()
{
	return EAuracronPCGPinType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pin connection types\n" },
#endif
		{ "Execution.DisplayName", "Execution" },
		{ "Execution.Name", "EAuracronPCGPinType::Execution" },
		{ "Input.DisplayName", "Input" },
		{ "Input.Name", "EAuracronPCGPinType::Input" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
		{ "Output.DisplayName", "Output" },
		{ "Output.Name", "EAuracronPCGPinType::Output" },
		{ "Parameter.DisplayName", "Parameter" },
		{ "Parameter.Name", "EAuracronPCGPinType::Parameter" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pin connection types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPinType::Input", (int64)EAuracronPCGPinType::Input },
		{ "EAuracronPCGPinType::Output", (int64)EAuracronPCGPinType::Output },
		{ "EAuracronPCGPinType::Parameter", (int64)EAuracronPCGPinType::Parameter },
		{ "EAuracronPCGPinType::Execution", (int64)EAuracronPCGPinType::Execution },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPinType",
	"EAuracronPCGPinType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPinType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPinType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPinType.InnerSingleton;
}
// ********** End Enum EAuracronPCGPinType *********************************************************

// ********** Begin ScriptStruct FAuracronPCGGraphValidationResult *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult;
class UScriptStruct* FAuracronPCGGraphValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGGraphValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph validation result\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph validation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Errors_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Warnings_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeCount_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EdgeCount_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCycles_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisconnectedNodes_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Errors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Errors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Warnings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Warnings;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NodeCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EdgeCount;
	static void NewProp_bHasCycles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCycles;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisconnectedNodes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DisconnectedNodes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGGraphValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FAuracronPCGGraphValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGGraphValidationResult), &Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Errors_Inner = { "Errors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Errors = { "Errors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphValidationResult, Errors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Errors_MetaData), NewProp_Errors_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Warnings_Inner = { "Warnings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Warnings = { "Warnings", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphValidationResult, Warnings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Warnings_MetaData), NewProp_Warnings_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_NodeCount = { "NodeCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphValidationResult, NodeCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeCount_MetaData), NewProp_NodeCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_EdgeCount = { "EdgeCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphValidationResult, EdgeCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EdgeCount_MetaData), NewProp_EdgeCount_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bHasCycles_SetBit(void* Obj)
{
	((FAuracronPCGGraphValidationResult*)Obj)->bHasCycles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bHasCycles = { "bHasCycles", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGGraphValidationResult), &Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bHasCycles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCycles_MetaData), NewProp_bHasCycles_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_DisconnectedNodes_Inner = { "DisconnectedNodes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_DisconnectedNodes = { "DisconnectedNodes", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphValidationResult, DisconnectedNodes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisconnectedNodes_MetaData), NewProp_DisconnectedNodes_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Errors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Errors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Warnings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_Warnings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_NodeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_EdgeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_bHasCycles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_DisconnectedNodes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewProp_DisconnectedNodes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGGraphValidationResult",
	Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::PropPointers),
	sizeof(FAuracronPCGGraphValidationResult),
	alignof(FAuracronPCGGraphValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGGraphValidationResult ***********************************

// ********** Begin ScriptStruct FAuracronPCGGraphExecutionStats ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats;
class UScriptStruct* FAuracronPCGGraphExecutionStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGGraphExecutionStats"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph execution statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph execution statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalExecutionTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompilationTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodesExecuted_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsGenerated_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsedMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalExecutionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompilationTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NodesExecuted;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsGenerated;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsedMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGGraphExecutionStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_TotalExecutionTime = { "TotalExecutionTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphExecutionStats, TotalExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalExecutionTime_MetaData), NewProp_TotalExecutionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_CompilationTime = { "CompilationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphExecutionStats, CompilationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompilationTime_MetaData), NewProp_CompilationTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_NodesExecuted = { "NodesExecuted", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphExecutionStats, NodesExecuted), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodesExecuted_MetaData), NewProp_NodesExecuted_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_PointsGenerated = { "PointsGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphExecutionStats, PointsGenerated), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsGenerated_MetaData), NewProp_PointsGenerated_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_MemoryUsedMB = { "MemoryUsedMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphExecutionStats, MemoryUsedMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsedMB_MetaData), NewProp_MemoryUsedMB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphExecutionStats, StartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_EndTime = { "EndTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphExecutionStats, EndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndTime_MetaData), NewProp_EndTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_TotalExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_CompilationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_NodesExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_PointsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_MemoryUsedMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewProp_EndTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGGraphExecutionStats",
	Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::PropPointers),
	sizeof(FAuracronPCGGraphExecutionStats),
	alignof(FAuracronPCGGraphExecutionStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGGraphExecutionStats *************************************

// ********** Begin ScriptStruct FAuracronPCGPinConnection *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection;
class UScriptStruct* FAuracronPCGPinConnection::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGPinConnection, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGPinConnection"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pin connection information\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pin connection information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceNodeId_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourcePinName_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetNodeId_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPinName_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinType_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourcePinName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetPinName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PinType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PinType;
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGPinConnection>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_SourceNodeId = { "SourceNodeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPinConnection, SourceNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceNodeId_MetaData), NewProp_SourceNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_SourcePinName = { "SourcePinName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPinConnection, SourcePinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourcePinName_MetaData), NewProp_SourcePinName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_TargetNodeId = { "TargetNodeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPinConnection, TargetNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetNodeId_MetaData), NewProp_TargetNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_TargetPinName = { "TargetPinName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPinConnection, TargetPinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPinName_MetaData), NewProp_TargetPinName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_PinType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_PinType = { "PinType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPinConnection, PinType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPinType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinType_MetaData), NewProp_PinType_MetaData) }; // 877656239
void Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FAuracronPCGPinConnection*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPinConnection), &Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_SourceNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_SourcePinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_TargetNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_TargetPinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_PinType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_PinType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewProp_bIsValid,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGPinConnection",
	Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::PropPointers),
	sizeof(FAuracronPCGPinConnection),
	alignof(FAuracronPCGPinConnection),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPinConnection()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGPinConnection *******************************************

// ********** Begin ScriptStruct FAuracronPCGGraphSerializationData ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData;
class UScriptStruct* FAuracronPCGGraphSerializationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGGraphSerializationData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph serialization data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph serialization data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphName_MetaData[] = {
		{ "Category", "Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphVersion_MetaData[] = {
		{ "Category", "Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeData_MetaData[] = {
		{ "Category", "Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Connections_MetaData[] = {
		{ "Category", "Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphParameters_MetaData[] = {
		{ "Category", "Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastModified_MetaData[] = {
		{ "Category", "Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphVersion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NodeData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Connections_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Connections;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_GraphParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastModified;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGGraphSerializationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphName = { "GraphName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphSerializationData, GraphName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphName_MetaData), NewProp_GraphName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphVersion = { "GraphVersion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphSerializationData, GraphVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphVersion_MetaData), NewProp_GraphVersion_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_NodeData_Inner = { "NodeData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_NodeData = { "NodeData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphSerializationData, NodeData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeData_MetaData), NewProp_NodeData_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_Connections_Inner = { "Connections", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGPinConnection, METADATA_PARAMS(0, nullptr) }; // 3705306541
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_Connections = { "Connections", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphSerializationData, Connections), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Connections_MetaData), NewProp_Connections_MetaData) }; // 3705306541
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphParameters_ValueProp = { "GraphParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphParameters_Key_KeyProp = { "GraphParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphParameters = { "GraphParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphSerializationData, GraphParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphParameters_MetaData), NewProp_GraphParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphSerializationData, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_LastModified = { "LastModified", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGGraphSerializationData, LastModified), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastModified_MetaData), NewProp_LastModified_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_NodeData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_NodeData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_Connections_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_Connections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_GraphParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewProp_LastModified,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGGraphSerializationData",
	Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::PropPointers),
	sizeof(FAuracronPCGGraphSerializationData),
	alignof(FAuracronPCGGraphSerializationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGGraphSerializationData **********************************

// ********** Begin Class UAuracronPCGGraphWrapper Function AddNode ********************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics
{
	struct AuracronPCGGraphWrapper_eventAddNode_Parms
	{
		TSubclassOf<UPCGSettings> NodeClass;
		FVector2D Position;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nodes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node management\n" },
#endif
		{ "CPP_Default_Position", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventAddNode_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventAddNode_Parms, Position), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventAddNode_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "AddNode", Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::AuracronPCGGraphWrapper_eventAddNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::AuracronPCGGraphWrapper_eventAddNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execAddNode)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->AddNode(Z_Param_NodeClass,Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function AddNode **********************************

// ********** Begin Class UAuracronPCGGraphWrapper Function CancelExecution ************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics
{
	struct AuracronPCGGraphWrapper_eventCancelExecution_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventCancelExecution_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventCancelExecution_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "CancelExecution", Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::AuracronPCGGraphWrapper_eventCancelExecution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::AuracronPCGGraphWrapper_eventCancelExecution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execCancelExecution)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelExecution();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function CancelExecution **************************

// ********** Begin Class UAuracronPCGGraphWrapper Function ClearGraph *****************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_ClearGraph_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Utilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ClearGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "ClearGraph", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ClearGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_ClearGraph_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_ClearGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_ClearGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execClearGraph)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearGraph();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function ClearGraph *******************************

// ********** Begin Class UAuracronPCGGraphWrapper Function CloneGraph *****************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventCloneGraph_Parms
	{
		UAuracronPCGGraphWrapper* SourceGraph;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Graph" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceGraph;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::NewProp_SourceGraph = { "SourceGraph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventCloneGraph_Parms, SourceGraph), Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventCloneGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventCloneGraph_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::NewProp_SourceGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "CloneGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::AuracronPCGGraphWrapper_eventCloneGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::AuracronPCGGraphWrapper_eventCloneGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execCloneGraph)
{
	P_GET_OBJECT(UAuracronPCGGraphWrapper,Z_Param_SourceGraph);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CloneGraph(Z_Param_SourceGraph);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function CloneGraph *******************************

// ********** Begin Class UAuracronPCGGraphWrapper Function ConnectPins ****************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics
{
	struct AuracronPCGGraphWrapper_eventConnectPins_Parms
	{
		FString SourceNodeId;
		FString SourcePinName;
		FString TargetNodeId;
		FString TargetPinName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Connections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Connection management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Connection management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceNodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourcePinName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetNodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPinName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourcePinName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetPinName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_SourceNodeId = { "SourceNodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventConnectPins_Parms, SourceNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceNodeId_MetaData), NewProp_SourceNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_SourcePinName = { "SourcePinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventConnectPins_Parms, SourcePinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourcePinName_MetaData), NewProp_SourcePinName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_TargetNodeId = { "TargetNodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventConnectPins_Parms, TargetNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetNodeId_MetaData), NewProp_TargetNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_TargetPinName = { "TargetPinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventConnectPins_Parms, TargetPinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPinName_MetaData), NewProp_TargetPinName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventConnectPins_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventConnectPins_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_SourceNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_SourcePinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_TargetNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_TargetPinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "ConnectPins", Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::AuracronPCGGraphWrapper_eventConnectPins_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::AuracronPCGGraphWrapper_eventConnectPins_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execConnectPins)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceNodeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_SourcePinName);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetNodeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetPinName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConnectPins(Z_Param_SourceNodeId,Z_Param_SourcePinName,Z_Param_TargetNodeId,Z_Param_TargetPinName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function ConnectPins ******************************

// ********** Begin Class UAuracronPCGGraphWrapper Function CreateNewGraph *************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventCreateNewGraph_Parms
	{
		FString GraphName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Graph" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::NewProp_GraphName = { "GraphName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventCreateNewGraph_Parms, GraphName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphName_MetaData), NewProp_GraphName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventCreateNewGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventCreateNewGraph_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::NewProp_GraphName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "CreateNewGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::AuracronPCGGraphWrapper_eventCreateNewGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::AuracronPCGGraphWrapper_eventCreateNewGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execCreateNewGraph)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GraphName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateNewGraph(Z_Param_GraphName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function CreateNewGraph ***************************

// ********** Begin Class UAuracronPCGGraphWrapper Function DeserializeGraphFromString *************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics
{
	struct AuracronPCGGraphWrapper_eventDeserializeGraphFromString_Parms
	{
		FString SerializedData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SerializedData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SerializedData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::NewProp_SerializedData = { "SerializedData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventDeserializeGraphFromString_Parms, SerializedData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SerializedData_MetaData), NewProp_SerializedData_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventDeserializeGraphFromString_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventDeserializeGraphFromString_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::NewProp_SerializedData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "DeserializeGraphFromString", Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::AuracronPCGGraphWrapper_eventDeserializeGraphFromString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::AuracronPCGGraphWrapper_eventDeserializeGraphFromString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execDeserializeGraphFromString)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SerializedData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeserializeGraphFromString(Z_Param_SerializedData);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function DeserializeGraphFromString ***************

// ********** Begin Class UAuracronPCGGraphWrapper Function DisconnectPins *************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics
{
	struct AuracronPCGGraphWrapper_eventDisconnectPins_Parms
	{
		FString SourceNodeId;
		FString SourcePinName;
		FString TargetNodeId;
		FString TargetPinName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Connections" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceNodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourcePinName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetNodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPinName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourcePinName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetPinName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_SourceNodeId = { "SourceNodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventDisconnectPins_Parms, SourceNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceNodeId_MetaData), NewProp_SourceNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_SourcePinName = { "SourcePinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventDisconnectPins_Parms, SourcePinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourcePinName_MetaData), NewProp_SourcePinName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_TargetNodeId = { "TargetNodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventDisconnectPins_Parms, TargetNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetNodeId_MetaData), NewProp_TargetNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_TargetPinName = { "TargetPinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventDisconnectPins_Parms, TargetPinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPinName_MetaData), NewProp_TargetPinName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventDisconnectPins_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventDisconnectPins_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_SourceNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_SourcePinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_TargetNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_TargetPinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "DisconnectPins", Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::AuracronPCGGraphWrapper_eventDisconnectPins_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::AuracronPCGGraphWrapper_eventDisconnectPins_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execDisconnectPins)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceNodeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_SourcePinName);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetNodeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetPinName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DisconnectPins(Z_Param_SourceNodeId,Z_Param_SourcePinName,Z_Param_TargetNodeId,Z_Param_TargetPinName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function DisconnectPins ***************************

// ********** Begin Class UAuracronPCGGraphWrapper Function DuplicateGraph *************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventDuplicateGraph_Parms
	{
		UAuracronPCGGraphWrapper* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Utilities" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventDuplicateGraph_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "DuplicateGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::AuracronPCGGraphWrapper_eventDuplicateGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::AuracronPCGGraphWrapper_eventDuplicateGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execDuplicateGraph)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGGraphWrapper**)Z_Param__Result=P_THIS->DuplicateGraph();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function DuplicateGraph ***************************

// ********** Begin Class UAuracronPCGGraphWrapper Function ExecuteGraph ***************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventExecuteGraph_Parms
	{
		AActor* TargetActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph execution\n" },
#endif
		{ "CPP_Default_TargetActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph execution" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventExecuteGraph_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventExecuteGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventExecuteGraph_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "ExecuteGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::AuracronPCGGraphWrapper_eventExecuteGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::AuracronPCGGraphWrapper_eventExecuteGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execExecuteGraph)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteGraph(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function ExecuteGraph *****************************

// ********** Begin Class UAuracronPCGGraphWrapper Function ExecuteGraphAsync **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics
{
	struct AuracronPCGGraphWrapper_eventExecuteGraphAsync_Parms
	{
		AActor* TargetActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Execution" },
		{ "CPP_Default_TargetActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventExecuteGraphAsync_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventExecuteGraphAsync_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventExecuteGraphAsync_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "ExecuteGraphAsync", Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::AuracronPCGGraphWrapper_eventExecuteGraphAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::AuracronPCGGraphWrapper_eventExecuteGraphAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execExecuteGraphAsync)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteGraphAsync(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function ExecuteGraphAsync ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function FindDisconnectedNodes ******************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics
{
	struct AuracronPCGGraphWrapper_eventFindDisconnectedNodes_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventFindDisconnectedNodes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "FindDisconnectedNodes", Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::AuracronPCGGraphWrapper_eventFindDisconnectedNodes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::AuracronPCGGraphWrapper_eventFindDisconnectedNodes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execFindDisconnectedNodes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->FindDisconnectedNodes();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function FindDisconnectedNodes ********************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetAllConnections **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics
{
	struct AuracronPCGGraphWrapper_eventGetAllConnections_Parms
	{
		TArray<FAuracronPCGPinConnection> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Connections" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGPinConnection, METADATA_PARAMS(0, nullptr) }; // 3705306541
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetAllConnections_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3705306541
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetAllConnections", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::AuracronPCGGraphWrapper_eventGetAllConnections_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::AuracronPCGGraphWrapper_eventGetAllConnections_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetAllConnections)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGPinConnection>*)Z_Param__Result=P_THIS->GetAllConnections();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetAllConnections ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetAllGraphParameters ******************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics
{
	struct AuracronPCGGraphWrapper_eventGetAllGraphParameters_Parms
	{
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetAllGraphParameters_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetAllGraphParameters", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::AuracronPCGGraphWrapper_eventGetAllGraphParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::AuracronPCGGraphWrapper_eventGetAllGraphParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetAllGraphParameters)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=P_THIS->GetAllGraphParameters();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetAllGraphParameters ********************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetAllNodeIds **************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics
{
	struct AuracronPCGGraphWrapper_eventGetAllNodeIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nodes" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetAllNodeIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetAllNodeIds", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::AuracronPCGGraphWrapper_eventGetAllNodeIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::AuracronPCGGraphWrapper_eventGetAllNodeIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetAllNodeIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAllNodeIds();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetAllNodeIds ****************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetConnectionCount *********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics
{
	struct AuracronPCGGraphWrapper_eventGetConnectionCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetConnectionCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetConnectionCount", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::AuracronPCGGraphWrapper_eventGetConnectionCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::AuracronPCGGraphWrapper_eventGetConnectionCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetConnectionCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetConnectionCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetConnectionCount ***********************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetExecutionProgress *******************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics
{
	struct AuracronPCGGraphWrapper_eventGetExecutionProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetExecutionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetExecutionProgress", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::AuracronPCGGraphWrapper_eventGetExecutionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::AuracronPCGGraphWrapper_eventGetExecutionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetExecutionProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetExecutionProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetExecutionProgress *********************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetExecutionState **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics
{
	struct AuracronPCGGraphWrapper_eventGetExecutionState_Parms
	{
		EAuracronPCGGraphState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetExecutionState_Parms, ReturnValue), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState, METADATA_PARAMS(0, nullptr) }; // 1423251129
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetExecutionState", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::AuracronPCGGraphWrapper_eventGetExecutionState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::AuracronPCGGraphWrapper_eventGetExecutionState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetExecutionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPCGGraphState*)Z_Param__Result=P_THIS->GetExecutionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetExecutionState ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetExecutionStats **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics
{
	struct AuracronPCGGraphWrapper_eventGetExecutionStats_Parms
	{
		FAuracronPCGGraphExecutionStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetExecutionStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats, METADATA_PARAMS(0, nullptr) }; // 2072718043
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetExecutionStats", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::AuracronPCGGraphWrapper_eventGetExecutionStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::AuracronPCGGraphWrapper_eventGetExecutionStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetExecutionStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGGraphExecutionStats*)Z_Param__Result=P_THIS->GetExecutionStats();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetExecutionStats ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetGraphName ***************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics
{
	struct AuracronPCGGraphWrapper_eventGetGraphName_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph properties\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph properties" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetGraphName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetGraphName", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::AuracronPCGGraphWrapper_eventGetGraphName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::AuracronPCGGraphWrapper_eventGetGraphName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetGraphName)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetGraphName();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetGraphName *****************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetGraphParameter **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics
{
	struct AuracronPCGGraphWrapper_eventGetGraphParameter_Parms
	{
		FString ParameterName;
		FString DefaultValue;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Parameters" },
		{ "CPP_Default_DefaultValue", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetGraphParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetGraphParameter_Parms, DefaultValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetGraphParameter_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetGraphParameter", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::AuracronPCGGraphWrapper_eventGetGraphParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::AuracronPCGGraphWrapper_eventGetGraphParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetGraphParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_PROPERTY(FStrProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetGraphParameter(Z_Param_ParameterName,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetGraphParameter ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetNodeById ****************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics
{
	struct AuracronPCGGraphWrapper_eventGetNodeById_Parms
	{
		FString NodeId;
		UPCGNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nodes" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::NewProp_NodeId = { "NodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetNodeById_Parms, NodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeId_MetaData), NewProp_NodeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetNodeById_Parms, ReturnValue), Z_Construct_UClass_UPCGNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::NewProp_NodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetNodeById", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::AuracronPCGGraphWrapper_eventGetNodeById_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::AuracronPCGGraphWrapper_eventGetNodeById_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetNodeById)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGNode**)Z_Param__Result=P_THIS->GetNodeById(Z_Param_NodeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetNodeById ******************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetNodeCount ***************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics
{
	struct AuracronPCGGraphWrapper_eventGetNodeCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetNodeCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetNodeCount", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::AuracronPCGGraphWrapper_eventGetNodeCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::AuracronPCGGraphWrapper_eventGetNodeCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetNodeCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetNodeCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetNodeCount *****************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetNodesByType *************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics
{
	struct AuracronPCGGraphWrapper_eventGetNodesByType_Parms
	{
		TSubclassOf<UPCGSettings> NodeType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Utilities" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::NewProp_NodeType = { "NodeType", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetNodesByType_Parms, NodeType), Z_Construct_UClass_UClass, Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetNodesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::NewProp_NodeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetNodesByType", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::AuracronPCGGraphWrapper_eventGetNodesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::AuracronPCGGraphWrapper_eventGetNodesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetNodesByType)
{
	P_GET_OBJECT(UClass,Z_Param_NodeType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetNodesByType(Z_Param_NodeType);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetNodesByType ***************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetPCGGraph ****************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventGetPCGGraph_Parms
	{
		UPCGGraph* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Graph" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetPCGGraph_Parms, ReturnValue), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetPCGGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::AuracronPCGGraphWrapper_eventGetPCGGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::AuracronPCGGraphWrapper_eventGetPCGGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetPCGGraph)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGGraph**)Z_Param__Result=P_THIS->GetPCGGraph();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetPCGGraph ******************************

// ********** Begin Class UAuracronPCGGraphWrapper Function GetSerializationData *******************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics
{
	struct AuracronPCGGraphWrapper_eventGetSerializationData_Parms
	{
		FAuracronPCGGraphSerializationData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventGetSerializationData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData, METADATA_PARAMS(0, nullptr) }; // 555029254
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "GetSerializationData", Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::AuracronPCGGraphWrapper_eventGetSerializationData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::AuracronPCGGraphWrapper_eventGetSerializationData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execGetSerializationData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGGraphSerializationData*)Z_Param__Result=P_THIS->GetSerializationData();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function GetSerializationData *********************

// ********** Begin Class UAuracronPCGGraphWrapper Function HasCycles ******************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics
{
	struct AuracronPCGGraphWrapper_eventHasCycles_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventHasCycles_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventHasCycles_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "HasCycles", Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::AuracronPCGGraphWrapper_eventHasCycles_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::AuracronPCGGraphWrapper_eventHasCycles_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execHasCycles)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasCycles();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function HasCycles ********************************

// ********** Begin Class UAuracronPCGGraphWrapper Function InitializeFromPCGGraph *****************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventInitializeFromPCGGraph_Parms
	{
		UPCGGraph* SourceGraph;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Graph" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceGraph;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::NewProp_SourceGraph = { "SourceGraph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventInitializeFromPCGGraph_Parms, SourceGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventInitializeFromPCGGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventInitializeFromPCGGraph_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::NewProp_SourceGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "InitializeFromPCGGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::AuracronPCGGraphWrapper_eventInitializeFromPCGGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::AuracronPCGGraphWrapper_eventInitializeFromPCGGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execInitializeFromPCGGraph)
{
	P_GET_OBJECT(UPCGGraph,Z_Param_SourceGraph);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeFromPCGGraph(Z_Param_SourceGraph);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function InitializeFromPCGGraph *******************

// ********** Begin Class UAuracronPCGGraphWrapper Function IsGraphModified ************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics
{
	struct AuracronPCGGraphWrapper_eventIsGraphModified_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventIsGraphModified_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventIsGraphModified_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "IsGraphModified", Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::AuracronPCGGraphWrapper_eventIsGraphModified_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::AuracronPCGGraphWrapper_eventIsGraphModified_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execIsGraphModified)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGraphModified();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function IsGraphModified **************************

// ********** Begin Class UAuracronPCGGraphWrapper Function IsValidConnection **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics
{
	struct AuracronPCGGraphWrapper_eventIsValidConnection_Parms
	{
		FString SourceNodeId;
		FString SourcePinName;
		FString TargetNodeId;
		FString TargetPinName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Connections" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceNodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourcePinName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetNodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPinName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourcePinName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetNodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetPinName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_SourceNodeId = { "SourceNodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventIsValidConnection_Parms, SourceNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceNodeId_MetaData), NewProp_SourceNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_SourcePinName = { "SourcePinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventIsValidConnection_Parms, SourcePinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourcePinName_MetaData), NewProp_SourcePinName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_TargetNodeId = { "TargetNodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventIsValidConnection_Parms, TargetNodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetNodeId_MetaData), NewProp_TargetNodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_TargetPinName = { "TargetPinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventIsValidConnection_Parms, TargetPinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPinName_MetaData), NewProp_TargetPinName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventIsValidConnection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventIsValidConnection_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_SourceNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_SourcePinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_TargetNodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_TargetPinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "IsValidConnection", Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::AuracronPCGGraphWrapper_eventIsValidConnection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::AuracronPCGGraphWrapper_eventIsValidConnection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execIsValidConnection)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceNodeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_SourcePinName);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetNodeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetPinName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidConnection(Z_Param_SourceNodeId,Z_Param_SourcePinName,Z_Param_TargetNodeId,Z_Param_TargetPinName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function IsValidConnection ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function LoadGraphFromFile **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics
{
	struct AuracronPCGGraphWrapper_eventLoadGraphFromFile_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventLoadGraphFromFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventLoadGraphFromFile_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventLoadGraphFromFile_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "LoadGraphFromFile", Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::AuracronPCGGraphWrapper_eventLoadGraphFromFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::AuracronPCGGraphWrapper_eventLoadGraphFromFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execLoadGraphFromFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadGraphFromFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function LoadGraphFromFile ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function MarkAsModified *************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_MarkAsModified_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MarkAsModified_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "MarkAsModified", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MarkAsModified_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_MarkAsModified_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_MarkAsModified()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_MarkAsModified_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execMarkAsModified)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MarkAsModified();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function MarkAsModified ***************************

// ********** Begin Class UAuracronPCGGraphWrapper Function MergeGraph *****************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventMergeGraph_Parms
	{
		UAuracronPCGGraphWrapper* OtherGraph;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Utilities" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherGraph;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::NewProp_OtherGraph = { "OtherGraph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventMergeGraph_Parms, OtherGraph), Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventMergeGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventMergeGraph_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::NewProp_OtherGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "MergeGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::AuracronPCGGraphWrapper_eventMergeGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::AuracronPCGGraphWrapper_eventMergeGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execMergeGraph)
{
	P_GET_OBJECT(UAuracronPCGGraphWrapper,Z_Param_OtherGraph);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MergeGraph(Z_Param_OtherGraph);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function MergeGraph *******************************

// ********** Begin Class UAuracronPCGGraphWrapper Function MoveNode *******************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics
{
	struct AuracronPCGGraphWrapper_eventMoveNode_Parms
	{
		FString NodeId;
		FVector2D NewPosition;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nodes" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewPosition;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_NodeId = { "NodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventMoveNode_Parms, NodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeId_MetaData), NewProp_NodeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_NewPosition = { "NewPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventMoveNode_Parms, NewPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewPosition_MetaData), NewProp_NewPosition_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventMoveNode_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventMoveNode_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_NodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_NewPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "MoveNode", Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::AuracronPCGGraphWrapper_eventMoveNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::AuracronPCGGraphWrapper_eventMoveNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execMoveNode)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeId);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_NewPosition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveNode(Z_Param_NodeId,Z_Param_Out_NewPosition);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function MoveNode *********************************

// ********** Begin Class UAuracronPCGGraphWrapper Function RemoveGraphParameter *******************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics
{
	struct AuracronPCGGraphWrapper_eventRemoveGraphParameter_Parms
	{
		FString ParameterName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventRemoveGraphParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventRemoveGraphParameter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventRemoveGraphParameter_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "RemoveGraphParameter", Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::AuracronPCGGraphWrapper_eventRemoveGraphParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::AuracronPCGGraphWrapper_eventRemoveGraphParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execRemoveGraphParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveGraphParameter(Z_Param_ParameterName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function RemoveGraphParameter *********************

// ********** Begin Class UAuracronPCGGraphWrapper Function RemoveNode *****************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics
{
	struct AuracronPCGGraphWrapper_eventRemoveNode_Parms
	{
		FString NodeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nodes" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::NewProp_NodeId = { "NodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventRemoveNode_Parms, NodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeId_MetaData), NewProp_NodeId_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventRemoveNode_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventRemoveNode_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::NewProp_NodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "RemoveNode", Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::AuracronPCGGraphWrapper_eventRemoveNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::AuracronPCGGraphWrapper_eventRemoveNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execRemoveNode)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveNode(Z_Param_NodeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function RemoveNode *******************************

// ********** Begin Class UAuracronPCGGraphWrapper Function SaveGraphToFile ************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics
{
	struct AuracronPCGGraphWrapper_eventSaveGraphToFile_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Serialization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Serialization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Serialization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventSaveGraphToFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventSaveGraphToFile_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventSaveGraphToFile_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "SaveGraphToFile", Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::AuracronPCGGraphWrapper_eventSaveGraphToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::AuracronPCGGraphWrapper_eventSaveGraphToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execSaveGraphToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveGraphToFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function SaveGraphToFile **************************

// ********** Begin Class UAuracronPCGGraphWrapper Function SerializeGraphToString *****************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics
{
	struct AuracronPCGGraphWrapper_eventSerializeGraphToString_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Serialization" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventSerializeGraphToString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "SerializeGraphToString", Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::AuracronPCGGraphWrapper_eventSerializeGraphToString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::AuracronPCGGraphWrapper_eventSerializeGraphToString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execSerializeGraphToString)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->SerializeGraphToString();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function SerializeGraphToString *******************

// ********** Begin Class UAuracronPCGGraphWrapper Function SetGraphName ***************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics
{
	struct AuracronPCGGraphWrapper_eventSetGraphName_Parms
	{
		FString NewName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::NewProp_NewName = { "NewName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventSetGraphName_Parms, NewName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewName_MetaData), NewProp_NewName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::NewProp_NewName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "SetGraphName", Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::AuracronPCGGraphWrapper_eventSetGraphName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::AuracronPCGGraphWrapper_eventSetGraphName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execSetGraphName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NewName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGraphName(Z_Param_NewName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function SetGraphName *****************************

// ********** Begin Class UAuracronPCGGraphWrapper Function SetGraphParameter **********************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics
{
	struct AuracronPCGGraphWrapper_eventSetGraphParameter_Parms
	{
		FString ParameterName;
		FString ParameterValue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterValue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventSetGraphParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ParameterValue = { "ParameterValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventSetGraphParameter_Parms, ParameterValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterValue_MetaData), NewProp_ParameterValue_MetaData) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventSetGraphParameter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventSetGraphParameter_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ParameterValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "SetGraphParameter", Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::AuracronPCGGraphWrapper_eventSetGraphParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::AuracronPCGGraphWrapper_eventSetGraphParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execSetGraphParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetGraphParameter(Z_Param_ParameterName,Z_Param_ParameterValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function SetGraphParameter ************************

// ********** Begin Class UAuracronPCGGraphWrapper Function ValidateGraph **************************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics
{
	struct AuracronPCGGraphWrapper_eventValidateGraph_Parms
	{
		FAuracronPCGGraphValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Graph validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Graph validation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventValidateGraph_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult, METADATA_PARAMS(0, nullptr) }; // 3096217166
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "ValidateGraph", Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::AuracronPCGGraphWrapper_eventValidateGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::AuracronPCGGraphWrapper_eventValidateGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execValidateGraph)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGGraphValidationResult*)Z_Param__Result=P_THIS->ValidateGraph();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function ValidateGraph ****************************

// ********** Begin Class UAuracronPCGGraphWrapper Function ValidateNodeConnections ****************
struct Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics
{
	struct AuracronPCGGraphWrapper_eventValidateNodeConnections_Parms
	{
		FString NodeId;
		TArray<FString> ValidationErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_NodeId = { "NodeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventValidateNodeConnections_Parms, NodeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeId_MetaData), NewProp_NodeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ValidationErrors_Inner = { "ValidationErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ValidationErrors = { "ValidationErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGGraphWrapper_eventValidateNodeConnections_Parms, ValidationErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGGraphWrapper_eventValidateNodeConnections_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGGraphWrapper_eventValidateNodeConnections_Parms), &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_NodeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ValidationErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGGraphWrapper, nullptr, "ValidateNodeConnections", Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::AuracronPCGGraphWrapper_eventValidateNodeConnections_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::AuracronPCGGraphWrapper_eventValidateNodeConnections_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGGraphWrapper::execValidateNodeConnections)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeId);
	P_GET_TARRAY_REF(FString,Z_Param_Out_ValidationErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateNodeConnections(Z_Param_NodeId,Z_Param_Out_ValidationErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGGraphWrapper Function ValidateNodeConnections ******************

// ********** Begin Class UAuracronPCGGraphWrapper *************************************************
void UAuracronPCGGraphWrapper::StaticRegisterNativesUAuracronPCGGraphWrapper()
{
	UClass* Class = UAuracronPCGGraphWrapper::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddNode", &UAuracronPCGGraphWrapper::execAddNode },
		{ "CancelExecution", &UAuracronPCGGraphWrapper::execCancelExecution },
		{ "ClearGraph", &UAuracronPCGGraphWrapper::execClearGraph },
		{ "CloneGraph", &UAuracronPCGGraphWrapper::execCloneGraph },
		{ "ConnectPins", &UAuracronPCGGraphWrapper::execConnectPins },
		{ "CreateNewGraph", &UAuracronPCGGraphWrapper::execCreateNewGraph },
		{ "DeserializeGraphFromString", &UAuracronPCGGraphWrapper::execDeserializeGraphFromString },
		{ "DisconnectPins", &UAuracronPCGGraphWrapper::execDisconnectPins },
		{ "DuplicateGraph", &UAuracronPCGGraphWrapper::execDuplicateGraph },
		{ "ExecuteGraph", &UAuracronPCGGraphWrapper::execExecuteGraph },
		{ "ExecuteGraphAsync", &UAuracronPCGGraphWrapper::execExecuteGraphAsync },
		{ "FindDisconnectedNodes", &UAuracronPCGGraphWrapper::execFindDisconnectedNodes },
		{ "GetAllConnections", &UAuracronPCGGraphWrapper::execGetAllConnections },
		{ "GetAllGraphParameters", &UAuracronPCGGraphWrapper::execGetAllGraphParameters },
		{ "GetAllNodeIds", &UAuracronPCGGraphWrapper::execGetAllNodeIds },
		{ "GetConnectionCount", &UAuracronPCGGraphWrapper::execGetConnectionCount },
		{ "GetExecutionProgress", &UAuracronPCGGraphWrapper::execGetExecutionProgress },
		{ "GetExecutionState", &UAuracronPCGGraphWrapper::execGetExecutionState },
		{ "GetExecutionStats", &UAuracronPCGGraphWrapper::execGetExecutionStats },
		{ "GetGraphName", &UAuracronPCGGraphWrapper::execGetGraphName },
		{ "GetGraphParameter", &UAuracronPCGGraphWrapper::execGetGraphParameter },
		{ "GetNodeById", &UAuracronPCGGraphWrapper::execGetNodeById },
		{ "GetNodeCount", &UAuracronPCGGraphWrapper::execGetNodeCount },
		{ "GetNodesByType", &UAuracronPCGGraphWrapper::execGetNodesByType },
		{ "GetPCGGraph", &UAuracronPCGGraphWrapper::execGetPCGGraph },
		{ "GetSerializationData", &UAuracronPCGGraphWrapper::execGetSerializationData },
		{ "HasCycles", &UAuracronPCGGraphWrapper::execHasCycles },
		{ "InitializeFromPCGGraph", &UAuracronPCGGraphWrapper::execInitializeFromPCGGraph },
		{ "IsGraphModified", &UAuracronPCGGraphWrapper::execIsGraphModified },
		{ "IsValidConnection", &UAuracronPCGGraphWrapper::execIsValidConnection },
		{ "LoadGraphFromFile", &UAuracronPCGGraphWrapper::execLoadGraphFromFile },
		{ "MarkAsModified", &UAuracronPCGGraphWrapper::execMarkAsModified },
		{ "MergeGraph", &UAuracronPCGGraphWrapper::execMergeGraph },
		{ "MoveNode", &UAuracronPCGGraphWrapper::execMoveNode },
		{ "RemoveGraphParameter", &UAuracronPCGGraphWrapper::execRemoveGraphParameter },
		{ "RemoveNode", &UAuracronPCGGraphWrapper::execRemoveNode },
		{ "SaveGraphToFile", &UAuracronPCGGraphWrapper::execSaveGraphToFile },
		{ "SerializeGraphToString", &UAuracronPCGGraphWrapper::execSerializeGraphToString },
		{ "SetGraphName", &UAuracronPCGGraphWrapper::execSetGraphName },
		{ "SetGraphParameter", &UAuracronPCGGraphWrapper::execSetGraphParameter },
		{ "ValidateGraph", &UAuracronPCGGraphWrapper::execValidateGraph },
		{ "ValidateNodeConnections", &UAuracronPCGGraphWrapper::execValidateNodeConnections },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGGraphWrapper;
UClass* UAuracronPCGGraphWrapper::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGGraphWrapper;
	if (!Z_Registration_Info_UClass_UAuracronPCGGraphWrapper.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGGraphWrapper"),
			Z_Registration_Info_UClass_UAuracronPCGGraphWrapper.InnerSingleton,
			StaticRegisterNativesUAuracronPCGGraphWrapper,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGGraphWrapper.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister()
{
	return UAuracronPCGGraphWrapper::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enhanced PCG Graph wrapper for AURACRON framework\n * Provides advanced graph management, validation, and execution capabilities\n */" },
#endif
		{ "IncludePath", "AuracronPCGGraphSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced PCG Graph wrapper for AURACRON framework\nProvides advanced graph management, validation, and execution capabilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraph_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core graph data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core graph data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphName_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphParameters_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeIdMap_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionState_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Execution state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execution state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionStats_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsModified_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Modification tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modification tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastModified_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGGraphSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGGraph;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GraphParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_GraphParameters;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NodeIdMap_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeIdMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NodeIdMap;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExecutionState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExecutionState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExecutionStats;
	static void NewProp_bIsModified_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsModified;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastModified;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_AddNode, "AddNode" }, // 3642078937
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_CancelExecution, "CancelExecution" }, // 1960851615
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ClearGraph, "ClearGraph" }, // 2996660607
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_CloneGraph, "CloneGraph" }, // 3696170427
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ConnectPins, "ConnectPins" }, // 3486371413
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_CreateNewGraph, "CreateNewGraph" }, // 37550670
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_DeserializeGraphFromString, "DeserializeGraphFromString" }, // 1967760556
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_DisconnectPins, "DisconnectPins" }, // 3945051330
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_DuplicateGraph, "DuplicateGraph" }, // 4170600497
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraph, "ExecuteGraph" }, // 200876466
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ExecuteGraphAsync, "ExecuteGraphAsync" }, // 94963261
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_FindDisconnectedNodes, "FindDisconnectedNodes" }, // 4061658250
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllConnections, "GetAllConnections" }, // 3924853469
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllGraphParameters, "GetAllGraphParameters" }, // 4060368618
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetAllNodeIds, "GetAllNodeIds" }, // 2456148310
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetConnectionCount, "GetConnectionCount" }, // 2531755087
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionProgress, "GetExecutionProgress" }, // 1352157459
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionState, "GetExecutionState" }, // 1719588653
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetExecutionStats, "GetExecutionStats" }, // 3140603037
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphName, "GetGraphName" }, // 1517225928
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetGraphParameter, "GetGraphParameter" }, // 1362737794
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeById, "GetNodeById" }, // 1413247169
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodeCount, "GetNodeCount" }, // 1264409416
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetNodesByType, "GetNodesByType" }, // 2428539877
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetPCGGraph, "GetPCGGraph" }, // 4107740069
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_GetSerializationData, "GetSerializationData" }, // 298644342
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_HasCycles, "HasCycles" }, // 725959847
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_InitializeFromPCGGraph, "InitializeFromPCGGraph" }, // 3408290849
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsGraphModified, "IsGraphModified" }, // 668824550
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_IsValidConnection, "IsValidConnection" }, // 1833864349
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_LoadGraphFromFile, "LoadGraphFromFile" }, // 1934869318
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_MarkAsModified, "MarkAsModified" }, // 1983851762
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_MergeGraph, "MergeGraph" }, // 4045707445
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_MoveNode, "MoveNode" }, // 3979054444
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveGraphParameter, "RemoveGraphParameter" }, // 2203210997
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_RemoveNode, "RemoveNode" }, // 1204310303
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_SaveGraphToFile, "SaveGraphToFile" }, // 2644401911
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_SerializeGraphToString, "SerializeGraphToString" }, // 774218316
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphName, "SetGraphName" }, // 217774493
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_SetGraphParameter, "SetGraphParameter" }, // 1027566650
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateGraph, "ValidateGraph" }, // 2516615096
		{ &Z_Construct_UFunction_UAuracronPCGGraphWrapper_ValidateNodeConnections, "ValidateNodeConnections" }, // 1115148577
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGGraphWrapper>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_PCGGraph = { "PCGGraph", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, PCGGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraph_MetaData), NewProp_PCGGraph_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphName = { "GraphName", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, GraphName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphName_MetaData), NewProp_GraphName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphParameters_ValueProp = { "GraphParameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphParameters_Key_KeyProp = { "GraphParameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphParameters = { "GraphParameters", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, GraphParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphParameters_MetaData), NewProp_GraphParameters_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_NodeIdMap_ValueProp = { "NodeIdMap", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UPCGNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_NodeIdMap_Key_KeyProp = { "NodeIdMap_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_NodeIdMap = { "NodeIdMap", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, NodeIdMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeIdMap_MetaData), NewProp_NodeIdMap_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionState = { "ExecutionState", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, ExecutionState), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGraphState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionState_MetaData), NewProp_ExecutionState_MetaData) }; // 1423251129
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionProgress = { "ExecutionProgress", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, ExecutionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionProgress_MetaData), NewProp_ExecutionProgress_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionStats = { "ExecutionStats", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, ExecutionStats), Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionStats_MetaData), NewProp_ExecutionStats_MetaData) }; // 2072718043
void Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_bIsModified_SetBit(void* Obj)
{
	((UAuracronPCGGraphWrapper*)Obj)->bIsModified = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_bIsModified = { "bIsModified", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGGraphWrapper), &Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_bIsModified_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsModified_MetaData), NewProp_bIsModified_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_LastModified = { "LastModified", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGGraphWrapper, LastModified), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastModified_MetaData), NewProp_LastModified_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_PCGGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_GraphParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_NodeIdMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_NodeIdMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_NodeIdMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_ExecutionStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_bIsModified,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::NewProp_LastModified,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::ClassParams = {
	&UAuracronPCGGraphWrapper::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGGraphWrapper()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGGraphWrapper.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGGraphWrapper.OuterSingleton, Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGGraphWrapper.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGGraphWrapper);
UAuracronPCGGraphWrapper::~UAuracronPCGGraphWrapper() {}
// ********** End Class UAuracronPCGGraphWrapper ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGGraphState_StaticEnum, TEXT("EAuracronPCGGraphState"), &Z_Registration_Info_UEnum_EAuracronPCGGraphState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1423251129U) },
		{ EAuracronPCGPinType_StaticEnum, TEXT("EAuracronPCGPinType"), &Z_Registration_Info_UEnum_EAuracronPCGPinType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 877656239U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGGraphValidationResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics::NewStructOps, TEXT("AuracronPCGGraphValidationResult"), &Z_Registration_Info_UScriptStruct_FAuracronPCGGraphValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGGraphValidationResult), 3096217166U) },
		{ FAuracronPCGGraphExecutionStats::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics::NewStructOps, TEXT("AuracronPCGGraphExecutionStats"), &Z_Registration_Info_UScriptStruct_FAuracronPCGGraphExecutionStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGGraphExecutionStats), 2072718043U) },
		{ FAuracronPCGPinConnection::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics::NewStructOps, TEXT("AuracronPCGPinConnection"), &Z_Registration_Info_UScriptStruct_FAuracronPCGPinConnection, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGPinConnection), 3705306541U) },
		{ FAuracronPCGGraphSerializationData::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics::NewStructOps, TEXT("AuracronPCGGraphSerializationData"), &Z_Registration_Info_UScriptStruct_FAuracronPCGGraphSerializationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGGraphSerializationData), 555029254U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGGraphWrapper, UAuracronPCGGraphWrapper::StaticClass, TEXT("UAuracronPCGGraphWrapper"), &Z_Registration_Info_UClass_UAuracronPCGGraphWrapper, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGGraphWrapper), 1126721875U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_2000251705(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
