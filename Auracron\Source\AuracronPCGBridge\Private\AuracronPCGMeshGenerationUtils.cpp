// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Mesh Generation Utilities Implementation
// Bridge 2.6: PCG Framework - Mesh Generation Nodes

#include "AuracronPCGMeshGeneration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// =============================================================================
// MESH GENERATION UTILITIES IMPLEMENTATION
// =============================================================================

int32 UAuracronPCGMeshGenerationUtils::SelectMeshIndex(const TArray<FAuracronPCGMeshEntry>& MeshEntries, 
                                                       const FPCGPoint& Point, 
                                                       const UPCGMetadata* Metadata, 
                                                       EAuracronPCGMeshSelectionMode SelectionMode, 
                                                       const FString& SelectionAttribute)
{
    if (MeshEntries.Num() == 0)
    {
        return -1;
    }

    switch (SelectionMode)
    {
        case EAuracronPCGMeshSelectionMode::Random:
        {
            // Use point seed for consistent randomness
            FRandomStream RandomStream(Point.Seed);
            return RandomStream.RandRange(0, MeshEntries.Num() - 1);
        }
        case EAuracronPCGMeshSelectionMode::ByAttribute:
        {
            if (!SelectionAttribute.IsEmpty() && Metadata && Point.MetadataEntry != PCGInvalidEntryKey)
            {
                // Use UE5.6 PCG metadata system for proper attribute access
                float AttributeValue = 0.5f; // Default value
                
                // Try to get the attribute value from metadata
                const FPCGMetadataAttributeBase* Attribute = Metadata->GetConstAttribute(SelectionAttribute);
                if (Attribute)
                {
                    // Handle different attribute types
                    if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
                    {
                        const FPCGMetadataAttribute<float>* FloatAttribute = static_cast<const FPCGMetadataAttribute<float>*>(Attribute);
                        AttributeValue = FloatAttribute->GetValueFromItemKey(Point.MetadataEntry);
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<double>::Id)
                    {
                        const FPCGMetadataAttribute<double>* DoubleAttribute = static_cast<const FPCGMetadataAttribute<double>*>(Attribute);
                        AttributeValue = static_cast<float>(DoubleAttribute->GetValueFromItemKey(Point.MetadataEntry));
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<int32>::Id)
                    {
                        const FPCGMetadataAttribute<int32>* IntAttribute = static_cast<const FPCGMetadataAttribute<int32>*>(Attribute);
                        AttributeValue = static_cast<float>(IntAttribute->GetValueFromItemKey(Point.MetadataEntry));
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<int64>::Id)
                    {
                        const FPCGMetadataAttribute<int64>* Int64Attribute = static_cast<const FPCGMetadataAttribute<int64>*>(Attribute);
                        AttributeValue = static_cast<float>(Int64Attribute->GetValueFromItemKey(Point.MetadataEntry));
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id)
                    {
                        const FPCGMetadataAttribute<FVector>* VectorAttribute = static_cast<const FPCGMetadataAttribute<FVector>*>(Attribute);
                        FVector VectorValue = VectorAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        AttributeValue = VectorValue.Size(); // Use magnitude for selection
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<FString>::Id)
                    {
                        const FPCGMetadataAttribute<FString>* StringAttribute = static_cast<const FPCGMetadataAttribute<FString>*>(Attribute);
                        FString StringValue = StringAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        // Convert string to hash for selection
                        AttributeValue = static_cast<float>(GetTypeHash(StringValue)) / static_cast<float>(MAX_uint32);
                    }
                    
                    AURACRON_PCG_LOG_VERBOSE(TEXT("Retrieved attribute '%s' value: %.3f for mesh selection"), 
                                           *SelectionAttribute, AttributeValue);
                }
                else
                {
                    AURACRON_PCG_LOG_WARNING(TEXT("Attribute '%s' not found in metadata, using default value"), *SelectionAttribute);
                }
                
                // Normalize attribute value to [0,1] range if needed
                AttributeValue = FMath::Clamp(AttributeValue, 0.0f, 1.0f);
                
                // Map to mesh index
                int32 Index = FMath::FloorToInt(AttributeValue * MeshEntries.Num());
                return FMath::Clamp(Index, 0, MeshEntries.Num() - 1);
            }
            
            AURACRON_PCG_LOG_WARNING(TEXT("Invalid attribute selection parameters, falling back to index 0"));
            return 0;
        }
        case EAuracronPCGMeshSelectionMode::ByDensity:
        {
            float Density = Point.Density;
            int32 Index = FMath::FloorToInt(Density * MeshEntries.Num());
            return FMath::Clamp(Index, 0, MeshEntries.Num() - 1);
        }
        case EAuracronPCGMeshSelectionMode::Sequential:
        {
            // Use point index for sequential selection
            return Point.Seed % MeshEntries.Num();
        }
        case EAuracronPCGMeshSelectionMode::Weighted:
        {
            // Calculate total weight
            float TotalWeight = 0.0f;
            for (const FAuracronPCGMeshEntry& Entry : MeshEntries)
            {
                TotalWeight += Entry.Weight;
            }

            if (TotalWeight <= 0.0f)
            {
                return 0;
            }

            // Generate random value based on point seed
            FRandomStream RandomStream(Point.Seed);
            float RandomValue = RandomStream.FRand() * TotalWeight;

            // Find weighted selection
            float CurrentWeight = 0.0f;
            for (int32 i = 0; i < MeshEntries.Num(); i++)
            {
                CurrentWeight += MeshEntries[i].Weight;
                if (RandomValue <= CurrentWeight)
                {
                    return i;
                }
            }

            return MeshEntries.Num() - 1;
        }
        default:
            return 0;
    }
}

bool UAuracronPCGMeshGenerationUtils::ValidateMeshEntry(const FAuracronPCGMeshEntry& MeshEntry, FString& ValidationError)
{
    ValidationError.Empty();

    // Check if mesh is valid
    if (!MeshEntry.Mesh.IsValid())
    {
        ValidationError = TEXT("Mesh reference is invalid");
        return false;
    }

    // Check weight
    if (MeshEntry.Weight < 0.0f)
    {
        ValidationError = TEXT("Weight cannot be negative");
        return false;
    }

    // Check scale
    if (MeshEntry.LocalScale.X <= 0.0f || MeshEntry.LocalScale.Y <= 0.0f || MeshEntry.LocalScale.Z <= 0.0f)
    {
        ValidationError = TEXT("Scale components must be positive");
        return false;
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateBoxMesh(const FVector& Size, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor)
{
    OutMeshDescriptor.Vertices.Empty();
    OutMeshDescriptor.Triangles.Empty();
    OutMeshDescriptor.Normals.Empty();
    OutMeshDescriptor.UVs.Empty();

    FVector HalfSize = Size * 0.5f;

    // Generate vertices (8 corners of a box)
    TArray<FVector> BoxVertices = {
        FVector(-HalfSize.X, -HalfSize.Y, -HalfSize.Z), // 0
        FVector( HalfSize.X, -HalfSize.Y, -HalfSize.Z), // 1
        FVector( HalfSize.X,  HalfSize.Y, -HalfSize.Z), // 2
        FVector(-HalfSize.X,  HalfSize.Y, -HalfSize.Z), // 3
        FVector(-HalfSize.X, -HalfSize.Y,  HalfSize.Z), // 4
        FVector( HalfSize.X, -HalfSize.Y,  HalfSize.Z), // 5
        FVector( HalfSize.X,  HalfSize.Y,  HalfSize.Z), // 6
        FVector(-HalfSize.X,  HalfSize.Y,  HalfSize.Z)  // 7
    };

    // Generate faces (6 faces, 4 vertices each, 2 triangles per face)
    TArray<int32> FaceIndices = {
        // Bottom face
        0, 1, 2, 0, 2, 3,
        // Top face
        4, 7, 6, 4, 6, 5,
        // Front face
        0, 4, 5, 0, 5, 1,
        // Back face
        2, 6, 7, 2, 7, 3,
        // Left face
        0, 3, 7, 0, 7, 4,
        // Right face
        1, 5, 6, 1, 6, 2
    };

    // Generate normals for each face
    TArray<FVector> FaceNormals = {
        FVector(0, 0, -1), FVector(0, 0, -1), // Bottom
        FVector(0, 0,  1), FVector(0, 0,  1), // Top
        FVector(0, -1, 0), FVector(0, -1, 0), // Front
        FVector(0,  1, 0), FVector(0,  1, 0), // Back
        FVector(-1, 0, 0), FVector(-1, 0, 0), // Left
        FVector( 1, 0, 0), FVector( 1, 0, 0)  // Right
    };

    // Build final vertex and triangle arrays
    for (int32 FaceIndex = 0; FaceIndex < 6; FaceIndex++)
    {
        for (int32 VertexIndex = 0; VertexIndex < 6; VertexIndex++) // 6 vertices per face (2 triangles)
        {
            int32 BoxVertexIndex = FaceIndices[FaceIndex * 6 + VertexIndex];
            OutMeshDescriptor.Vertices.Add(BoxVertices[BoxVertexIndex]);
            OutMeshDescriptor.Normals.Add(FaceNormals[FaceIndex * 2 + (VertexIndex / 3)]);
            OutMeshDescriptor.Triangles.Add(OutMeshDescriptor.Vertices.Num() - 1);
            
            // Generate UVs
            FVector2D UV = FVector2D(0.0f, 0.0f);
            if (VertexIndex % 3 == 1) UV.X = 1.0f;
            if (VertexIndex % 3 == 2) UV.Y = 1.0f;
            if (VertexIndex == 3) { UV.X = 0.0f; UV.Y = 1.0f; }
            if (VertexIndex == 4) { UV.X = 1.0f; UV.Y = 1.0f; }
            if (VertexIndex == 5) { UV.X = 1.0f; UV.Y = 0.0f; }
            OutMeshDescriptor.UVs.Add(UV);
        }
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateSphereMesh(float Radius, int32 Segments, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor)
{
    OutMeshDescriptor.Vertices.Empty();
    OutMeshDescriptor.Triangles.Empty();
    OutMeshDescriptor.Normals.Empty();
    OutMeshDescriptor.UVs.Empty();

    if (Segments < 3)
    {
        Segments = 3;
    }

    // Generate sphere vertices using spherical coordinates
    for (int32 LatIndex = 0; LatIndex <= Segments; LatIndex++)
    {
        float Lat = PI * LatIndex / Segments;
        float SinLat = FMath::Sin(Lat);
        float CosLat = FMath::Cos(Lat);

        for (int32 LonIndex = 0; LonIndex <= Segments; LonIndex++)
        {
            float Lon = 2.0f * PI * LonIndex / Segments;
            float SinLon = FMath::Sin(Lon);
            float CosLon = FMath::Cos(Lon);

            FVector Vertex = FVector(
                Radius * SinLat * CosLon,
                Radius * SinLat * SinLon,
                Radius * CosLat
            );

            OutMeshDescriptor.Vertices.Add(Vertex);
            OutMeshDescriptor.Normals.Add(Vertex.GetSafeNormal());
            OutMeshDescriptor.UVs.Add(FVector2D(
                static_cast<float>(LonIndex) / Segments,
                static_cast<float>(LatIndex) / Segments
            ));
        }
    }

    // Generate triangles
    for (int32 LatIndex = 0; LatIndex < Segments; LatIndex++)
    {
        for (int32 LonIndex = 0; LonIndex < Segments; LonIndex++)
        {
            int32 Current = LatIndex * (Segments + 1) + LonIndex;
            int32 Next = Current + Segments + 1;

            // First triangle
            OutMeshDescriptor.Triangles.Add(Current);
            OutMeshDescriptor.Triangles.Add(Next);
            OutMeshDescriptor.Triangles.Add(Current + 1);

            // Second triangle
            OutMeshDescriptor.Triangles.Add(Current + 1);
            OutMeshDescriptor.Triangles.Add(Next);
            OutMeshDescriptor.Triangles.Add(Next + 1);
        }
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::OptimizeMesh(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, 
                                                   bool bWeldVertices, 
                                                   float WeldThreshold, 
                                                   bool bRemoveDegenerateTriangles)
{
    if (bWeldVertices)
    {
        // Use sophisticated vertex welding with spatial hashing for better performance
        TRACE_CPUPROFILER_EVENT_SCOPE(OptimizeMesh_VertexWelding);
        
        const int32 VertexCount = MeshDescriptor.Vertices.Num();
        if (VertexCount == 0)
        {
            return true;
        }
        
        // Calculate bounding box for spatial hashing
        FBox BoundingBox(ForceInit);
        for (const FVector& Vertex : MeshDescriptor.Vertices)
        {
            BoundingBox += Vertex;
        }
        
        // Create spatial hash grid for efficient vertex lookup
        const float CellSize = FMath::Max(WeldThreshold * 2.0f, 1.0f);
        const FVector GridSize = BoundingBox.GetSize() / CellSize;
        const int32 GridX = FMath::Max(1, FMath::CeilToInt(GridSize.X));
        const int32 GridY = FMath::Max(1, FMath::CeilToInt(GridSize.Y));
        const int32 GridZ = FMath::Max(1, FMath::CeilToInt(GridSize.Z));
        
        TMap<int32, TArray<int32>> SpatialGrid;
        
        // Hash function for 3D grid
        auto GetGridHash = [&](const FVector& Position) -> int32
        {
            FVector RelativePos = (Position - BoundingBox.Min) / CellSize;
            int32 X = FMath::Clamp(FMath::FloorToInt(RelativePos.X), 0, GridX - 1);
            int32 Y = FMath::Clamp(FMath::FloorToInt(RelativePos.Y), 0, GridY - 1);
            int32 Z = FMath::Clamp(FMath::FloorToInt(RelativePos.Z), 0, GridZ - 1);
            return X + Y * GridX + Z * GridX * GridY;
        };
        
        TMap<int32, int32> VertexRemap;
        TArray<FVector> WeldedVertices;
        TArray<FVector> WeldedNormals;
        TArray<FVector2D> WeldedUVs;
        TArray<FLinearColor> WeldedColors;
        
        // Reserve space for better performance
        WeldedVertices.Reserve(VertexCount);
        WeldedNormals.Reserve(MeshDescriptor.Normals.Num());
        WeldedUVs.Reserve(MeshDescriptor.UVs.Num());
        WeldedColors.Reserve(MeshDescriptor.Colors.Num());
        
        const float WeldThresholdSq = WeldThreshold * WeldThreshold;
        
        for (int32 i = 0; i < VertexCount; i++)
        {
            const FVector& CurrentVertex = MeshDescriptor.Vertices[i];
            int32 GridHash = GetGridHash(CurrentVertex);
            
            bool bFoundMatch = false;
            int32 MatchIndex = -1;
            
            // Check current cell and neighboring cells
            for (int32 dx = -1; dx <= 1; dx++)
            {
                for (int32 dy = -1; dy <= 1; dy++)
                {
                    for (int32 dz = -1; dz <= 1; dz++)
                    {
                        FVector NeighborPos = CurrentVertex + FVector(dx, dy, dz) * CellSize;
                        int32 NeighborHash = GetGridHash(NeighborPos);
                        
                        if (TArray<int32>* CellVertices = SpatialGrid.Find(NeighborHash))
                        {
                            for (int32 ExistingIndex : *CellVertices)
                            {
                                if (FVector::DistSquared(CurrentVertex, WeldedVertices[ExistingIndex]) <= WeldThresholdSq)
                                {
                                    MatchIndex = ExistingIndex;
                                    bFoundMatch = true;
                                    break;
                                }
                            }
                            if (bFoundMatch) break;
                        }
                    }
                    if (bFoundMatch) break;
                }
                if (bFoundMatch) break;
            }
            
            if (bFoundMatch)
            {
                VertexRemap.Add(i, MatchIndex);
                
                // Average normals for better quality
                if (i < MeshDescriptor.Normals.Num() && MatchIndex < WeldedNormals.Num())
                {
                    WeldedNormals[MatchIndex] = (WeldedNormals[MatchIndex] + MeshDescriptor.Normals[i]).GetSafeNormal();
                }
            }
            else
            {
                int32 NewIndex = WeldedVertices.Num();
                VertexRemap.Add(i, NewIndex);
                
                WeldedVertices.Add(CurrentVertex);
                
                if (i < MeshDescriptor.Normals.Num())
                {
                    WeldedNormals.Add(MeshDescriptor.Normals[i]);
                }
                if (i < MeshDescriptor.UVs.Num())
                {
                    WeldedUVs.Add(MeshDescriptor.UVs[i]);
                }
                if (i < MeshDescriptor.Colors.Num())
                {
                    WeldedColors.Add(MeshDescriptor.Colors[i]);
                }
                
                // Add to spatial grid
                SpatialGrid.FindOrAdd(GridHash).Add(NewIndex);
            }
        }
        
        // Update triangle indices
        for (int32& TriangleIndex : MeshDescriptor.Triangles)
        {
            if (int32* RemappedIndex = VertexRemap.Find(TriangleIndex))
            {
                TriangleIndex = *RemappedIndex;
            }
        }
        
        // Update mesh descriptor with welded data
        MeshDescriptor.Vertices = MoveTemp(WeldedVertices);
        MeshDescriptor.Normals = MoveTemp(WeldedNormals);
        MeshDescriptor.UVs = MoveTemp(WeldedUVs);
        MeshDescriptor.Colors = MoveTemp(WeldedColors);
        
        AURACRON_PCG_LOG_VERBOSE(TEXT("Vertex welding completed: %d -> %d vertices (%.1f%% reduction)"),
                               VertexCount, MeshDescriptor.Vertices.Num(),
                               (1.0f - static_cast<float>(MeshDescriptor.Vertices.Num()) / VertexCount) * 100.0f);
    }

    if (bRemoveDegenerateTriangles)
    {
        // Remove degenerate triangles
        TArray<int32> ValidTriangles;
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            if (i + 2 < MeshDescriptor.Triangles.Num())
            {
                int32 V0 = MeshDescriptor.Triangles[i];
                int32 V1 = MeshDescriptor.Triangles[i + 1];
                int32 V2 = MeshDescriptor.Triangles[i + 2];

                // Check if triangle is degenerate
                if (V0 != V1 && V1 != V2 && V0 != V2)
                {
                    if (V0 < MeshDescriptor.Vertices.Num() && 
                        V1 < MeshDescriptor.Vertices.Num() && 
                        V2 < MeshDescriptor.Vertices.Num())
                    {
                        FVector Edge1 = MeshDescriptor.Vertices[V1] - MeshDescriptor.Vertices[V0];
                        FVector Edge2 = MeshDescriptor.Vertices[V2] - MeshDescriptor.Vertices[V0];
                        FVector Normal = FVector::CrossProduct(Edge1, Edge2);

                        if (Normal.SizeSquared() > SMALL_NUMBER)
                        {
                            ValidTriangles.Add(V0);
                            ValidTriangles.Add(V1);
                            ValidTriangles.Add(V2);
                        }
                    }
                }
            }
        }
        MeshDescriptor.Triangles = ValidTriangles;
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateNormals(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, bool bSmoothNormals)
{
    MeshDescriptor.Normals.Empty();
    MeshDescriptor.Normals.SetNum(MeshDescriptor.Vertices.Num());

    // Initialize normals to zero
    for (FVector& Normal : MeshDescriptor.Normals)
    {
        Normal = FVector::ZeroVector;
    }

    // Calculate face normals and accumulate to vertex normals
    for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
    {
        if (i + 2 < MeshDescriptor.Triangles.Num())
        {
            int32 V0 = MeshDescriptor.Triangles[i];
            int32 V1 = MeshDescriptor.Triangles[i + 1];
            int32 V2 = MeshDescriptor.Triangles[i + 2];

            if (V0 < MeshDescriptor.Vertices.Num() && 
                V1 < MeshDescriptor.Vertices.Num() && 
                V2 < MeshDescriptor.Vertices.Num())
            {
                FVector Edge1 = MeshDescriptor.Vertices[V1] - MeshDescriptor.Vertices[V0];
                FVector Edge2 = MeshDescriptor.Vertices[V2] - MeshDescriptor.Vertices[V0];
                FVector FaceNormal = FVector::CrossProduct(Edge1, Edge2).GetSafeNormal();

                MeshDescriptor.Normals[V0] += FaceNormal;
                MeshDescriptor.Normals[V1] += FaceNormal;
                MeshDescriptor.Normals[V2] += FaceNormal;
            }
        }
    }

    // Normalize vertex normals
    for (FVector& Normal : MeshDescriptor.Normals)
    {
        Normal = Normal.GetSafeNormal();
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateUVs(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, float UVScale, bool bWorldSpace)
{
    MeshDescriptor.UVs.Empty();
    MeshDescriptor.UVs.SetNum(MeshDescriptor.Vertices.Num());

    if (bWorldSpace)
    {
        // Generate UVs based on world space coordinates
        for (int32 i = 0; i < MeshDescriptor.Vertices.Num(); i++)
        {
            const FVector& Vertex = MeshDescriptor.Vertices[i];
            MeshDescriptor.UVs[i] = FVector2D(Vertex.X * UVScale, Vertex.Y * UVScale);
        }
    }
    else
    {
        // Generate planar UVs
        FBox BoundingBox(MeshDescriptor.Vertices);
        FVector Size = BoundingBox.GetSize();
        FVector Min = BoundingBox.Min;

        for (int32 i = 0; i < MeshDescriptor.Vertices.Num(); i++)
        {
            const FVector& Vertex = MeshDescriptor.Vertices[i];
            FVector RelativePos = (Vertex - Min) / Size;
            MeshDescriptor.UVs[i] = FVector2D(RelativePos.X * UVScale, RelativePos.Y * UVScale);
        }
    }

    return true;
}

int32 UAuracronPCGMeshGenerationUtils::GetOptimalInstanceCount(int32 TotalInstances, int32 MaxInstancesPerComponent)
{
    return FMath::Min(TotalInstances, MaxInstancesPerComponent);
}

bool UAuracronPCGMeshGenerationUtils::ShouldUseHierarchicalInstancing(int32 InstanceCount, int32 Threshold)
{
    return InstanceCount >= Threshold;
}
