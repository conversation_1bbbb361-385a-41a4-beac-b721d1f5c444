// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronPerformanceOptimization.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPerformanceOptimization() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAsyncProcessingConfiguration();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FBatchProcessingConfiguration();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FGPUAccelerationConfiguration();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FMemoryPoolConfiguration();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPerformanceMetrics();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPerformanceOptimizationLevel *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPerformanceOptimizationLevel;
static UEnum* EPerformanceOptimizationLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPerformanceOptimizationLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPerformanceOptimizationLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EPerformanceOptimizationLevel"));
	}
	return Z_Registration_Info_UEnum_EPerformanceOptimizationLevel.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPerformanceOptimizationLevel>()
{
	return EPerformanceOptimizationLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EPerformanceOptimizationLevel::Aggressive" },
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EPerformanceOptimizationLevel::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for performance optimization\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EPerformanceOptimizationLevel::Custom" },
		{ "Moderate.DisplayName", "Moderate" },
		{ "Moderate.Name", "EPerformanceOptimizationLevel::Moderate" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EPerformanceOptimizationLevel::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for performance optimization" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPerformanceOptimizationLevel::None", (int64)EPerformanceOptimizationLevel::None },
		{ "EPerformanceOptimizationLevel::Basic", (int64)EPerformanceOptimizationLevel::Basic },
		{ "EPerformanceOptimizationLevel::Moderate", (int64)EPerformanceOptimizationLevel::Moderate },
		{ "EPerformanceOptimizationLevel::Aggressive", (int64)EPerformanceOptimizationLevel::Aggressive },
		{ "EPerformanceOptimizationLevel::Custom", (int64)EPerformanceOptimizationLevel::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EPerformanceOptimizationLevel",
	"EPerformanceOptimizationLevel",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel()
{
	if (!Z_Registration_Info_UEnum_EPerformanceOptimizationLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPerformanceOptimizationLevel.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPerformanceOptimizationLevel.InnerSingleton;
}
// ********** End Enum EPerformanceOptimizationLevel ***********************************************

// ********** Begin Enum EMemoryPoolType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMemoryPoolType;
static UEnum* EMemoryPoolType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMemoryPoolType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMemoryPoolType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMemoryPoolType"));
	}
	return Z_Registration_Info_UEnum_EMemoryPoolType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMemoryPoolType>()
{
	return EMemoryPoolType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Animation.DisplayName", "Animation" },
		{ "Animation.Name", "EMemoryPoolType::Animation" },
		{ "Audio.DisplayName", "Audio" },
		{ "Audio.Name", "EMemoryPoolType::Audio" },
		{ "BlueprintType", "true" },
		{ "DNA.DisplayName", "DNA" },
		{ "DNA.Name", "EMemoryPoolType::DNA" },
		{ "General.DisplayName", "General" },
		{ "General.Name", "EMemoryPoolType::General" },
		{ "Mesh.DisplayName", "Mesh" },
		{ "Mesh.Name", "EMemoryPoolType::Mesh" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
		{ "Texture.DisplayName", "Texture" },
		{ "Texture.Name", "EMemoryPoolType::Texture" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMemoryPoolType::DNA", (int64)EMemoryPoolType::DNA },
		{ "EMemoryPoolType::Texture", (int64)EMemoryPoolType::Texture },
		{ "EMemoryPoolType::Mesh", (int64)EMemoryPoolType::Mesh },
		{ "EMemoryPoolType::Animation", (int64)EMemoryPoolType::Animation },
		{ "EMemoryPoolType::Audio", (int64)EMemoryPoolType::Audio },
		{ "EMemoryPoolType::General", (int64)EMemoryPoolType::General },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMemoryPoolType",
	"EMemoryPoolType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType()
{
	if (!Z_Registration_Info_UEnum_EMemoryPoolType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMemoryPoolType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMemoryPoolType.InnerSingleton;
}
// ********** End Enum EMemoryPoolType *************************************************************

// ********** Begin Enum EAsyncProcessingPriority **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAsyncProcessingPriority;
static UEnum* EAsyncProcessingPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAsyncProcessingPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAsyncProcessingPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EAsyncProcessingPriority"));
	}
	return Z_Registration_Info_UEnum_EAsyncProcessingPriority.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAsyncProcessingPriority>()
{
	return EAsyncProcessingPriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAsyncProcessingPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAsyncProcessingPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAsyncProcessingPriority::Low" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAsyncProcessingPriority::Normal" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAsyncProcessingPriority::Low", (int64)EAsyncProcessingPriority::Low },
		{ "EAsyncProcessingPriority::Normal", (int64)EAsyncProcessingPriority::Normal },
		{ "EAsyncProcessingPriority::High", (int64)EAsyncProcessingPriority::High },
		{ "EAsyncProcessingPriority::Critical", (int64)EAsyncProcessingPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EAsyncProcessingPriority",
	"EAsyncProcessingPriority",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority()
{
	if (!Z_Registration_Info_UEnum_EAsyncProcessingPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAsyncProcessingPriority.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EAsyncProcessingPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAsyncProcessingPriority.InnerSingleton;
}
// ********** End Enum EAsyncProcessingPriority ****************************************************

// ********** Begin Enum EBatchOperationType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EBatchOperationType;
static UEnum* EBatchOperationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EBatchOperationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EBatchOperationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EBatchOperationType"));
	}
	return Z_Registration_Info_UEnum_EBatchOperationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBatchOperationType>()
{
	return EBatchOperationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AnimationBaking.DisplayName", "Animation Baking" },
		{ "AnimationBaking.Name", "EBatchOperationType::AnimationBaking" },
		{ "BlueprintType", "true" },
		{ "DNAProcessing.DisplayName", "DNA Processing" },
		{ "DNAProcessing.Name", "EBatchOperationType::DNAProcessing" },
		{ "MeshDeformation.DisplayName", "Mesh Deformation" },
		{ "MeshDeformation.Name", "EBatchOperationType::MeshDeformation" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
		{ "TextureGeneration.DisplayName", "Texture Generation" },
		{ "TextureGeneration.Name", "EBatchOperationType::TextureGeneration" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EBatchOperationType::DNAProcessing", (int64)EBatchOperationType::DNAProcessing },
		{ "EBatchOperationType::TextureGeneration", (int64)EBatchOperationType::TextureGeneration },
		{ "EBatchOperationType::MeshDeformation", (int64)EBatchOperationType::MeshDeformation },
		{ "EBatchOperationType::AnimationBaking", (int64)EBatchOperationType::AnimationBaking },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EBatchOperationType",
	"EBatchOperationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType()
{
	if (!Z_Registration_Info_UEnum_EBatchOperationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EBatchOperationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EBatchOperationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EBatchOperationType.InnerSingleton;
}
// ********** End Enum EBatchOperationType *********************************************************

// ********** Begin ScriptStruct FMemoryPoolConfiguration ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration;
class UScriptStruct* FMemoryPoolConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMemoryPoolConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("MemoryPoolConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for performance optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for performance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolType_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialSizeMB_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSizeMB_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthSizeMB_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGrow_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoShrink_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShrinkThreshold_MetaData[] = {
		{ "Category", "Memory Pool" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PoolType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PoolType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InitialSizeMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSizeMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GrowthSizeMB;
	static void NewProp_bAutoGrow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGrow;
	static void NewProp_bAutoShrink_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoShrink;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShrinkThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMemoryPoolConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_PoolType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_PoolType = { "PoolType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMemoryPoolConfiguration, PoolType), Z_Construct_UEnum_AuracronMetaHumanBridge_EMemoryPoolType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolType_MetaData), NewProp_PoolType_MetaData) }; // 53303534
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_InitialSizeMB = { "InitialSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMemoryPoolConfiguration, InitialSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialSizeMB_MetaData), NewProp_InitialSizeMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_MaxSizeMB = { "MaxSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMemoryPoolConfiguration, MaxSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSizeMB_MetaData), NewProp_MaxSizeMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_GrowthSizeMB = { "GrowthSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMemoryPoolConfiguration, GrowthSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthSizeMB_MetaData), NewProp_GrowthSizeMB_MetaData) };
void Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoGrow_SetBit(void* Obj)
{
	((FMemoryPoolConfiguration*)Obj)->bAutoGrow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoGrow = { "bAutoGrow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMemoryPoolConfiguration), &Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoGrow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGrow_MetaData), NewProp_bAutoGrow_MetaData) };
void Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoShrink_SetBit(void* Obj)
{
	((FMemoryPoolConfiguration*)Obj)->bAutoShrink = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoShrink = { "bAutoShrink", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMemoryPoolConfiguration), &Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoShrink_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoShrink_MetaData), NewProp_bAutoShrink_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_ShrinkThreshold = { "ShrinkThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMemoryPoolConfiguration, ShrinkThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShrinkThreshold_MetaData), NewProp_ShrinkThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_PoolType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_PoolType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_InitialSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_MaxSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_GrowthSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoGrow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_bAutoShrink,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewProp_ShrinkThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"MemoryPoolConfiguration",
	Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::PropPointers),
	sizeof(FMemoryPoolConfiguration),
	alignof(FMemoryPoolConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMemoryPoolConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FMemoryPoolConfiguration ********************************************

// ********** Begin ScriptStruct FAsyncProcessingConfiguration *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration;
class UScriptStruct* FAsyncProcessingConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAsyncProcessingConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("AsyncProcessingConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentTasks_MetaData[] = {
		{ "Category", "Async Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskQueueSize_MetaData[] = {
		{ "Category", "Async Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTaskPrioritization_MetaData[] = {
		{ "Category", "Async Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLoadBalancing_MetaData[] = {
		{ "Category", "Async Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TaskTimeoutSeconds_MetaData[] = {
		{ "Category", "Async Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTaskRetry_MetaData[] = {
		{ "Category", "Async Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRetryAttempts_MetaData[] = {
		{ "Category", "Async Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentTasks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TaskQueueSize;
	static void NewProp_bEnableTaskPrioritization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTaskPrioritization;
	static void NewProp_bEnableLoadBalancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLoadBalancing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TaskTimeoutSeconds;
	static void NewProp_bEnableTaskRetry_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTaskRetry;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRetryAttempts;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAsyncProcessingConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_MaxConcurrentTasks = { "MaxConcurrentTasks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAsyncProcessingConfiguration, MaxConcurrentTasks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentTasks_MetaData), NewProp_MaxConcurrentTasks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_TaskQueueSize = { "TaskQueueSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAsyncProcessingConfiguration, TaskQueueSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskQueueSize_MetaData), NewProp_TaskQueueSize_MetaData) };
void Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskPrioritization_SetBit(void* Obj)
{
	((FAsyncProcessingConfiguration*)Obj)->bEnableTaskPrioritization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskPrioritization = { "bEnableTaskPrioritization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAsyncProcessingConfiguration), &Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskPrioritization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTaskPrioritization_MetaData), NewProp_bEnableTaskPrioritization_MetaData) };
void Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableLoadBalancing_SetBit(void* Obj)
{
	((FAsyncProcessingConfiguration*)Obj)->bEnableLoadBalancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableLoadBalancing = { "bEnableLoadBalancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAsyncProcessingConfiguration), &Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableLoadBalancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLoadBalancing_MetaData), NewProp_bEnableLoadBalancing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_TaskTimeoutSeconds = { "TaskTimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAsyncProcessingConfiguration, TaskTimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TaskTimeoutSeconds_MetaData), NewProp_TaskTimeoutSeconds_MetaData) };
void Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskRetry_SetBit(void* Obj)
{
	((FAsyncProcessingConfiguration*)Obj)->bEnableTaskRetry = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskRetry = { "bEnableTaskRetry", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAsyncProcessingConfiguration), &Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskRetry_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTaskRetry_MetaData), NewProp_bEnableTaskRetry_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_MaxRetryAttempts = { "MaxRetryAttempts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAsyncProcessingConfiguration, MaxRetryAttempts), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRetryAttempts_MetaData), NewProp_MaxRetryAttempts_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_MaxConcurrentTasks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_TaskQueueSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskPrioritization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableLoadBalancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_TaskTimeoutSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_bEnableTaskRetry,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewProp_MaxRetryAttempts,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"AsyncProcessingConfiguration",
	Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::PropPointers),
	sizeof(FAsyncProcessingConfiguration),
	alignof(FAsyncProcessingConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAsyncProcessingConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAsyncProcessingConfiguration ***************************************

// ********** Begin ScriptStruct FGPUAccelerationConfiguration *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration;
class UScriptStruct* FGPUAccelerationConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FGPUAccelerationConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("GPUAccelerationConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUAcceleration_MetaData[] = {
		{ "Category", "GPU Acceleration" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableComputeShaders_MetaData[] = {
		{ "Category", "GPU Acceleration" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGPUMemoryMB_MetaData[] = {
		{ "Category", "GPU Acceleration" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComputeShaderThreadGroups_MetaData[] = {
		{ "Category", "GPU Acceleration" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUProfiling_MetaData[] = {
		{ "Category", "GPU Acceleration" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreferDedicatedGPU_MetaData[] = {
		{ "Category", "GPU Acceleration" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableGPUAcceleration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUAcceleration;
	static void NewProp_bEnableComputeShaders_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableComputeShaders;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxGPUMemoryMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComputeShaderThreadGroups;
	static void NewProp_bEnableGPUProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUProfiling;
	static void NewProp_bPreferDedicatedGPU_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreferDedicatedGPU;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FGPUAccelerationConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUAcceleration_SetBit(void* Obj)
{
	((FGPUAccelerationConfiguration*)Obj)->bEnableGPUAcceleration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUAcceleration = { "bEnableGPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGPUAccelerationConfiguration), &Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUAcceleration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUAcceleration_MetaData), NewProp_bEnableGPUAcceleration_MetaData) };
void Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableComputeShaders_SetBit(void* Obj)
{
	((FGPUAccelerationConfiguration*)Obj)->bEnableComputeShaders = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableComputeShaders = { "bEnableComputeShaders", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGPUAccelerationConfiguration), &Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableComputeShaders_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableComputeShaders_MetaData), NewProp_bEnableComputeShaders_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_MaxGPUMemoryMB = { "MaxGPUMemoryMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGPUAccelerationConfiguration, MaxGPUMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGPUMemoryMB_MetaData), NewProp_MaxGPUMemoryMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_ComputeShaderThreadGroups = { "ComputeShaderThreadGroups", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGPUAccelerationConfiguration, ComputeShaderThreadGroups), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComputeShaderThreadGroups_MetaData), NewProp_ComputeShaderThreadGroups_MetaData) };
void Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUProfiling_SetBit(void* Obj)
{
	((FGPUAccelerationConfiguration*)Obj)->bEnableGPUProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUProfiling = { "bEnableGPUProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGPUAccelerationConfiguration), &Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUProfiling_MetaData), NewProp_bEnableGPUProfiling_MetaData) };
void Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bPreferDedicatedGPU_SetBit(void* Obj)
{
	((FGPUAccelerationConfiguration*)Obj)->bPreferDedicatedGPU = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bPreferDedicatedGPU = { "bPreferDedicatedGPU", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGPUAccelerationConfiguration), &Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bPreferDedicatedGPU_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreferDedicatedGPU_MetaData), NewProp_bPreferDedicatedGPU_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableComputeShaders,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_MaxGPUMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_ComputeShaderThreadGroups,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bEnableGPUProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewProp_bPreferDedicatedGPU,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"GPUAccelerationConfiguration",
	Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::PropPointers),
	sizeof(FGPUAccelerationConfiguration),
	alignof(FGPUAccelerationConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FGPUAccelerationConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FGPUAccelerationConfiguration ***************************************

// ********** Begin ScriptStruct FBatchProcessingConfiguration *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration;
class UScriptStruct* FBatchProcessingConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBatchProcessingConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("BatchProcessingConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchSize_MetaData[] = {
		{ "Category", "Batch Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBatchQueueSize_MetaData[] = {
		{ "Category", "Batch Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBatchOptimization_MetaData[] = {
		{ "Category", "Batch Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProgressReporting_MetaData[] = {
		{ "Category", "Batch Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchTimeoutSeconds_MetaData[] = {
		{ "Category", "Batch Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBatchCaching_MetaData[] = {
		{ "Category", "Batch Processing" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxBatchQueueSize;
	static void NewProp_bEnableBatchOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBatchOptimization;
	static void NewProp_bEnableProgressReporting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProgressReporting;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BatchTimeoutSeconds;
	static void NewProp_bEnableBatchCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBatchCaching;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBatchProcessingConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_BatchSize = { "BatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBatchProcessingConfiguration, BatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchSize_MetaData), NewProp_BatchSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_MaxBatchQueueSize = { "MaxBatchQueueSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBatchProcessingConfiguration, MaxBatchQueueSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBatchQueueSize_MetaData), NewProp_MaxBatchQueueSize_MetaData) };
void Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchOptimization_SetBit(void* Obj)
{
	((FBatchProcessingConfiguration*)Obj)->bEnableBatchOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchOptimization = { "bEnableBatchOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FBatchProcessingConfiguration), &Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBatchOptimization_MetaData), NewProp_bEnableBatchOptimization_MetaData) };
void Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableProgressReporting_SetBit(void* Obj)
{
	((FBatchProcessingConfiguration*)Obj)->bEnableProgressReporting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableProgressReporting = { "bEnableProgressReporting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FBatchProcessingConfiguration), &Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableProgressReporting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProgressReporting_MetaData), NewProp_bEnableProgressReporting_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_BatchTimeoutSeconds = { "BatchTimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBatchProcessingConfiguration, BatchTimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchTimeoutSeconds_MetaData), NewProp_BatchTimeoutSeconds_MetaData) };
void Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchCaching_SetBit(void* Obj)
{
	((FBatchProcessingConfiguration*)Obj)->bEnableBatchCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchCaching = { "bEnableBatchCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FBatchProcessingConfiguration), &Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBatchCaching_MetaData), NewProp_bEnableBatchCaching_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_BatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_MaxBatchQueueSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableProgressReporting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_BatchTimeoutSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewProp_bEnableBatchCaching,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"BatchProcessingConfiguration",
	Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::PropPointers),
	sizeof(FBatchProcessingConfiguration),
	alignof(FBatchProcessingConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBatchProcessingConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FBatchProcessingConfiguration ***************************************

// ********** Begin ScriptStruct FPerformanceOptimizationConfiguration *****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration;
class UScriptStruct* FPerformanceOptimizationConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("PerformanceOptimizationConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationLevel_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPools_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AsyncProcessing_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUAcceleration_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchProcessing_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceMonitoring_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticOptimizations_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoringIntervalSeconds_MetaData[] = {
		{ "Category", "Performance Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MemoryPools_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MemoryPools;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AsyncProcessing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GPUAcceleration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BatchProcessing;
	static void NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceMonitoring;
	static void NewProp_bEnableAutomaticOptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticOptimizations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MonitoringIntervalSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPerformanceOptimizationConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_OptimizationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_OptimizationLevel = { "OptimizationLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceOptimizationConfiguration, OptimizationLevel), Z_Construct_UEnum_AuracronMetaHumanBridge_EPerformanceOptimizationLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationLevel_MetaData), NewProp_OptimizationLevel_MetaData) }; // 2900834250
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_MemoryPools_Inner = { "MemoryPools", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FMemoryPoolConfiguration, METADATA_PARAMS(0, nullptr) }; // 381040809
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_MemoryPools = { "MemoryPools", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceOptimizationConfiguration, MemoryPools), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPools_MetaData), NewProp_MemoryPools_MetaData) }; // 381040809
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_AsyncProcessing = { "AsyncProcessing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceOptimizationConfiguration, AsyncProcessing), Z_Construct_UScriptStruct_FAsyncProcessingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AsyncProcessing_MetaData), NewProp_AsyncProcessing_MetaData) }; // 1003233080
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_GPUAcceleration = { "GPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceOptimizationConfiguration, GPUAcceleration), Z_Construct_UScriptStruct_FGPUAccelerationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUAcceleration_MetaData), NewProp_GPUAcceleration_MetaData) }; // 3650116326
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_BatchProcessing = { "BatchProcessing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceOptimizationConfiguration, BatchProcessing), Z_Construct_UScriptStruct_FBatchProcessingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchProcessing_MetaData), NewProp_BatchProcessing_MetaData) }; // 3253548189
void Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj)
{
	((FPerformanceOptimizationConfiguration*)Obj)->bEnablePerformanceMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceMonitoring = { "bEnablePerformanceMonitoring", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceMonitoring_MetaData), NewProp_bEnablePerformanceMonitoring_MetaData) };
void Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnableAutomaticOptimizations_SetBit(void* Obj)
{
	((FPerformanceOptimizationConfiguration*)Obj)->bEnableAutomaticOptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnableAutomaticOptimizations = { "bEnableAutomaticOptimizations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnableAutomaticOptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticOptimizations_MetaData), NewProp_bEnableAutomaticOptimizations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_MonitoringIntervalSeconds = { "MonitoringIntervalSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceOptimizationConfiguration, MonitoringIntervalSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoringIntervalSeconds_MetaData), NewProp_MonitoringIntervalSeconds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_OptimizationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_OptimizationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_MemoryPools_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_MemoryPools,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_AsyncProcessing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_GPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_BatchProcessing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_bEnableAutomaticOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewProp_MonitoringIntervalSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"PerformanceOptimizationConfiguration",
	Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::PropPointers),
	sizeof(FPerformanceOptimizationConfiguration),
	alignof(FPerformanceOptimizationConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FPerformanceOptimizationConfiguration *******************************

// ********** Begin ScriptStruct FPerformanceMetrics ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPerformanceMetrics;
class UScriptStruct* FPerformanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPerformanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPerformanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPerformanceMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("PerformanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FPerformanceMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUUsagePercent_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsagePercent_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUUsagePercent_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheHitRatio_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveThreadCount_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AsyncOperationsCount_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchOperationsCount_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameTime_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronPerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUUsagePercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsagePercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUUsagePercent;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CacheHitRatio;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveThreadCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AsyncOperationsCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchOperationsCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPerformanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_CPUUsagePercent = { "CPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, CPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUUsagePercent_MetaData), NewProp_CPUUsagePercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MemoryUsagePercent = { "MemoryUsagePercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, MemoryUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsagePercent_MetaData), NewProp_MemoryUsagePercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_GPUUsagePercent = { "GPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, GPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUUsagePercent_MetaData), NewProp_GPUUsagePercent_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_CacheHitRatio = { "CacheHitRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, CacheHitRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheHitRatio_MetaData), NewProp_CacheHitRatio_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_ActiveThreadCount = { "ActiveThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, ActiveThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveThreadCount_MetaData), NewProp_ActiveThreadCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AsyncOperationsCount = { "AsyncOperationsCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, AsyncOperationsCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AsyncOperationsCount_MetaData), NewProp_AsyncOperationsCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_BatchOperationsCount = { "BatchOperationsCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, BatchOperationsCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchOperationsCount_MetaData), NewProp_BatchOperationsCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AverageFrameTime = { "AverageFrameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, AverageFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameTime_MetaData), NewProp_AverageFrameTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_CPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MemoryUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_GPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_CacheHitRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_ActiveThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AsyncOperationsCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_BatchOperationsCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AverageFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"PerformanceMetrics",
	Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers),
	sizeof(FPerformanceMetrics),
	alignof(FPerformanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPerformanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FPerformanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPerformanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPerformanceMetrics.InnerSingleton;
}
// ********** End ScriptStruct FPerformanceMetrics *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPerformanceOptimizationLevel_StaticEnum, TEXT("EPerformanceOptimizationLevel"), &Z_Registration_Info_UEnum_EPerformanceOptimizationLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2900834250U) },
		{ EMemoryPoolType_StaticEnum, TEXT("EMemoryPoolType"), &Z_Registration_Info_UEnum_EMemoryPoolType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 53303534U) },
		{ EAsyncProcessingPriority_StaticEnum, TEXT("EAsyncProcessingPriority"), &Z_Registration_Info_UEnum_EAsyncProcessingPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 754045621U) },
		{ EBatchOperationType_StaticEnum, TEXT("EBatchOperationType"), &Z_Registration_Info_UEnum_EBatchOperationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1021700116U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FMemoryPoolConfiguration::StaticStruct, Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics::NewStructOps, TEXT("MemoryPoolConfiguration"), &Z_Registration_Info_UScriptStruct_FMemoryPoolConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMemoryPoolConfiguration), 381040809U) },
		{ FAsyncProcessingConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics::NewStructOps, TEXT("AsyncProcessingConfiguration"), &Z_Registration_Info_UScriptStruct_FAsyncProcessingConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAsyncProcessingConfiguration), 1003233080U) },
		{ FGPUAccelerationConfiguration::StaticStruct, Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics::NewStructOps, TEXT("GPUAccelerationConfiguration"), &Z_Registration_Info_UScriptStruct_FGPUAccelerationConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FGPUAccelerationConfiguration), 3650116326U) },
		{ FBatchProcessingConfiguration::StaticStruct, Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics::NewStructOps, TEXT("BatchProcessingConfiguration"), &Z_Registration_Info_UScriptStruct_FBatchProcessingConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBatchProcessingConfiguration), 3253548189U) },
		{ FPerformanceOptimizationConfiguration::StaticStruct, Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics::NewStructOps, TEXT("PerformanceOptimizationConfiguration"), &Z_Registration_Info_UScriptStruct_FPerformanceOptimizationConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPerformanceOptimizationConfiguration), 1697265905U) },
		{ FPerformanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewStructOps, TEXT("PerformanceMetrics"), &Z_Registration_Info_UScriptStruct_FPerformanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPerformanceMetrics), 1848815492U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h__Script_AuracronMetaHumanBridge_1655033038(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
