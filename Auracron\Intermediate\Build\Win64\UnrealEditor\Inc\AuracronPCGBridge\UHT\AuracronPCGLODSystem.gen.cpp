// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGLODSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGLODSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODSystemUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODSystemUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UHLODLayer_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGLODGenerationMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode;
static UEnum* EAuracronPCGLODGenerationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGLODGenerationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLODGenerationMode>()
{
	return EAuracronPCGLODGenerationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Automatic.DisplayName", "Automatic" },
		{ "Automatic.Name", "EAuracronPCGLODGenerationMode::Automatic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD generation modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGLODGenerationMode::Custom" },
		{ "DistanceBased.DisplayName", "Distance Based" },
		{ "DistanceBased.Name", "EAuracronPCGLODGenerationMode::DistanceBased" },
		{ "HLOD.DisplayName", "HLOD" },
		{ "HLOD.Name", "EAuracronPCGLODGenerationMode::HLOD" },
		{ "Impostor.DisplayName", "Impostor" },
		{ "Impostor.Name", "EAuracronPCGLODGenerationMode::Impostor" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
		{ "Nanite.DisplayName", "Nanite" },
		{ "Nanite.Name", "EAuracronPCGLODGenerationMode::Nanite" },
		{ "ScreenSize.DisplayName", "Screen Size" },
		{ "ScreenSize.Name", "EAuracronPCGLODGenerationMode::ScreenSize" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD generation modes" },
#endif
		{ "TriangleCount.DisplayName", "Triangle Count" },
		{ "TriangleCount.Name", "EAuracronPCGLODGenerationMode::TriangleCount" },
		{ "VertexCount.DisplayName", "Vertex Count" },
		{ "VertexCount.Name", "EAuracronPCGLODGenerationMode::VertexCount" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGLODGenerationMode::Automatic", (int64)EAuracronPCGLODGenerationMode::Automatic },
		{ "EAuracronPCGLODGenerationMode::DistanceBased", (int64)EAuracronPCGLODGenerationMode::DistanceBased },
		{ "EAuracronPCGLODGenerationMode::ScreenSize", (int64)EAuracronPCGLODGenerationMode::ScreenSize },
		{ "EAuracronPCGLODGenerationMode::VertexCount", (int64)EAuracronPCGLODGenerationMode::VertexCount },
		{ "EAuracronPCGLODGenerationMode::TriangleCount", (int64)EAuracronPCGLODGenerationMode::TriangleCount },
		{ "EAuracronPCGLODGenerationMode::Custom", (int64)EAuracronPCGLODGenerationMode::Custom },
		{ "EAuracronPCGLODGenerationMode::Nanite", (int64)EAuracronPCGLODGenerationMode::Nanite },
		{ "EAuracronPCGLODGenerationMode::HLOD", (int64)EAuracronPCGLODGenerationMode::HLOD },
		{ "EAuracronPCGLODGenerationMode::Impostor", (int64)EAuracronPCGLODGenerationMode::Impostor },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGLODGenerationMode",
	"EAuracronPCGLODGenerationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGLODGenerationMode ***********************************************

// ********** Begin Enum EAuracronPCGCullingMode ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCullingMode;
static UEnum* EAuracronPCGCullingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCullingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCullingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCullingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCullingMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCullingMode>()
{
	return EAuracronPCGCullingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Combined.DisplayName", "Combined" },
		{ "Combined.Name", "EAuracronPCGCullingMode::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGCullingMode::Custom" },
		{ "Distance.DisplayName", "Distance" },
		{ "Distance.Name", "EAuracronPCGCullingMode::Distance" },
		{ "Frustum.DisplayName", "Frustum" },
		{ "Frustum.Name", "EAuracronPCGCullingMode::Frustum" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGCullingMode::None" },
		{ "Occlusion.DisplayName", "Occlusion" },
		{ "Occlusion.Name", "EAuracronPCGCullingMode::Occlusion" },
		{ "ScreenSize.DisplayName", "Screen Size" },
		{ "ScreenSize.Name", "EAuracronPCGCullingMode::ScreenSize" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCullingMode::None", (int64)EAuracronPCGCullingMode::None },
		{ "EAuracronPCGCullingMode::Distance", (int64)EAuracronPCGCullingMode::Distance },
		{ "EAuracronPCGCullingMode::Frustum", (int64)EAuracronPCGCullingMode::Frustum },
		{ "EAuracronPCGCullingMode::Occlusion", (int64)EAuracronPCGCullingMode::Occlusion },
		{ "EAuracronPCGCullingMode::ScreenSize", (int64)EAuracronPCGCullingMode::ScreenSize },
		{ "EAuracronPCGCullingMode::Combined", (int64)EAuracronPCGCullingMode::Combined },
		{ "EAuracronPCGCullingMode::Custom", (int64)EAuracronPCGCullingMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCullingMode",
	"EAuracronPCGCullingMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCullingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCullingMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCullingMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGCullingMode *****************************************************

// ********** Begin Enum EAuracronPCGInstancingMode ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGInstancingMode;
static UEnum* EAuracronPCGInstancingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGInstancingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGInstancingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGInstancingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGInstancingMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGInstancingMode>()
{
	return EAuracronPCGInstancingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Clustered.DisplayName", "Clustered" },
		{ "Clustered.Name", "EAuracronPCGInstancingMode::Clustered" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instancing optimization modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGInstancingMode::Custom" },
		{ "GPU.DisplayName", "GPU" },
		{ "GPU.Name", "EAuracronPCGInstancingMode::GPU" },
		{ "Hierarchical.DisplayName", "Hierarchical" },
		{ "Hierarchical.Name", "EAuracronPCGInstancingMode::Hierarchical" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
		{ "Nanite.DisplayName", "Nanite" },
		{ "Nanite.Name", "EAuracronPCGInstancingMode::Nanite" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGInstancingMode::None" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronPCGInstancingMode::Static" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instancing optimization modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGInstancingMode::None", (int64)EAuracronPCGInstancingMode::None },
		{ "EAuracronPCGInstancingMode::Static", (int64)EAuracronPCGInstancingMode::Static },
		{ "EAuracronPCGInstancingMode::Hierarchical", (int64)EAuracronPCGInstancingMode::Hierarchical },
		{ "EAuracronPCGInstancingMode::Clustered", (int64)EAuracronPCGInstancingMode::Clustered },
		{ "EAuracronPCGInstancingMode::GPU", (int64)EAuracronPCGInstancingMode::GPU },
		{ "EAuracronPCGInstancingMode::Nanite", (int64)EAuracronPCGInstancingMode::Nanite },
		{ "EAuracronPCGInstancingMode::Custom", (int64)EAuracronPCGInstancingMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGInstancingMode",
	"EAuracronPCGInstancingMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGInstancingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGInstancingMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGInstancingMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGInstancingMode **************************************************

// ********** Begin Enum EAuracronPCGPerformanceMetric *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric;
static UEnum* EAuracronPCGPerformanceMetric_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPerformanceMetric"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPerformanceMetric>()
{
	return EAuracronPCGPerformanceMetric_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance profiling types\n" },
#endif
		{ "CulledInstances.DisplayName", "Culled Instances" },
		{ "CulledInstances.Name", "EAuracronPCGPerformanceMetric::CulledInstances" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGPerformanceMetric::Custom" },
		{ "DrawCalls.DisplayName", "Draw Calls" },
		{ "DrawCalls.Name", "EAuracronPCGPerformanceMetric::DrawCalls" },
		{ "InstanceCount.DisplayName", "Instance Count" },
		{ "InstanceCount.Name", "EAuracronPCGPerformanceMetric::InstanceCount" },
		{ "LODLevel.DisplayName", "LOD Level" },
		{ "LODLevel.Name", "EAuracronPCGPerformanceMetric::LODLevel" },
		{ "MemoryUsage.DisplayName", "Memory Usage" },
		{ "MemoryUsage.Name", "EAuracronPCGPerformanceMetric::MemoryUsage" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
		{ "RenderTime.DisplayName", "Render Time" },
		{ "RenderTime.Name", "EAuracronPCGPerformanceMetric::RenderTime" },
		{ "ScreenSize.DisplayName", "Screen Size" },
		{ "ScreenSize.Name", "EAuracronPCGPerformanceMetric::ScreenSize" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance profiling types" },
#endif
		{ "TriangleCount.DisplayName", "Triangle Count" },
		{ "TriangleCount.Name", "EAuracronPCGPerformanceMetric::TriangleCount" },
		{ "VertexCount.DisplayName", "Vertex Count" },
		{ "VertexCount.Name", "EAuracronPCGPerformanceMetric::VertexCount" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPerformanceMetric::RenderTime", (int64)EAuracronPCGPerformanceMetric::RenderTime },
		{ "EAuracronPCGPerformanceMetric::DrawCalls", (int64)EAuracronPCGPerformanceMetric::DrawCalls },
		{ "EAuracronPCGPerformanceMetric::TriangleCount", (int64)EAuracronPCGPerformanceMetric::TriangleCount },
		{ "EAuracronPCGPerformanceMetric::VertexCount", (int64)EAuracronPCGPerformanceMetric::VertexCount },
		{ "EAuracronPCGPerformanceMetric::MemoryUsage", (int64)EAuracronPCGPerformanceMetric::MemoryUsage },
		{ "EAuracronPCGPerformanceMetric::InstanceCount", (int64)EAuracronPCGPerformanceMetric::InstanceCount },
		{ "EAuracronPCGPerformanceMetric::CulledInstances", (int64)EAuracronPCGPerformanceMetric::CulledInstances },
		{ "EAuracronPCGPerformanceMetric::LODLevel", (int64)EAuracronPCGPerformanceMetric::LODLevel },
		{ "EAuracronPCGPerformanceMetric::ScreenSize", (int64)EAuracronPCGPerformanceMetric::ScreenSize },
		{ "EAuracronPCGPerformanceMetric::Custom", (int64)EAuracronPCGPerformanceMetric::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPerformanceMetric",
	"EAuracronPCGPerformanceMetric",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric.InnerSingleton;
}
// ********** End Enum EAuracronPCGPerformanceMetric ***********************************************

// ********** Begin Enum EAuracronPCGMeshSimplificationAlgorithm ***********************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm;
static UEnum* EAuracronPCGMeshSimplificationAlgorithm_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMeshSimplificationAlgorithm"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshSimplificationAlgorithm>()
{
	return EAuracronPCGMeshSimplificationAlgorithm_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronPCGMeshSimplificationAlgorithm::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh simplification algorithms\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGMeshSimplificationAlgorithm::Custom" },
		{ "EdgeCollapse.DisplayName", "Edge Collapse" },
		{ "EdgeCollapse.Name", "EAuracronPCGMeshSimplificationAlgorithm::EdgeCollapse" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
		{ "Progressive.DisplayName", "Progressive" },
		{ "Progressive.Name", "EAuracronPCGMeshSimplificationAlgorithm::Progressive" },
		{ "QuadricErrorMetrics.DisplayName", "Quadric Error Metrics" },
		{ "QuadricErrorMetrics.Name", "EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh simplification algorithms" },
#endif
		{ "VertexClustering.DisplayName", "Vertex Clustering" },
		{ "VertexClustering.Name", "EAuracronPCGMeshSimplificationAlgorithm::VertexClustering" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics", (int64)EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics },
		{ "EAuracronPCGMeshSimplificationAlgorithm::EdgeCollapse", (int64)EAuracronPCGMeshSimplificationAlgorithm::EdgeCollapse },
		{ "EAuracronPCGMeshSimplificationAlgorithm::VertexClustering", (int64)EAuracronPCGMeshSimplificationAlgorithm::VertexClustering },
		{ "EAuracronPCGMeshSimplificationAlgorithm::Progressive", (int64)EAuracronPCGMeshSimplificationAlgorithm::Progressive },
		{ "EAuracronPCGMeshSimplificationAlgorithm::Adaptive", (int64)EAuracronPCGMeshSimplificationAlgorithm::Adaptive },
		{ "EAuracronPCGMeshSimplificationAlgorithm::Custom", (int64)EAuracronPCGMeshSimplificationAlgorithm::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMeshSimplificationAlgorithm",
	"EAuracronPCGMeshSimplificationAlgorithm",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm.InnerSingleton;
}
// ********** End Enum EAuracronPCGMeshSimplificationAlgorithm *************************************

// ********** Begin ScriptStruct FAuracronPCGLODGenerationDescriptor *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor;
class UScriptStruct* FAuracronPCGLODGenerationDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGLODGenerationDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD Generation Descriptor\n * Describes parameters for LOD generation and optimization\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD Generation Descriptor\nDescribes parameters for LOD generation and optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationMode_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevels_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "Distance Based" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::DistanceBased" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenSizeThresholds_MetaData[] = {
		{ "Category", "Screen Size" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::ScreenSize" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexCountTargets_MetaData[] = {
		{ "Category", "Vertex Count" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::VertexCount" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleCountTargets_MetaData[] = {
		{ "Category", "Triangle Count" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::TriangleCount" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationAlgorithm_MetaData[] = {
		{ "Category", "Simplification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationQuality_MetaData[] = {
		{ "Category", "Simplification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveBoundaries_MetaData[] = {
		{ "Category", "Simplification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveUVs_MetaData[] = {
		{ "Category", "Simplification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveNormals_MetaData[] = {
		{ "Category", "Simplification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HLODLayer_MetaData[] = {
		{ "Category", "HLOD" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::HLOD" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HLODCellSize_MetaData[] = {
		{ "Category", "HLOD" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::HLOD" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNanite_MetaData[] = {
		{ "Category", "Nanite" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::Nanite" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteTriangleThreshold_MetaData[] = {
		{ "Category", "Nanite" },
		{ "EditCondition", "GenerationMode == EAuracronPCGLODGenerationMode::Nanite" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateImpostors_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDitheredLODTransition_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODTransitionTime_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_GenerationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GenerationMode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevels;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScreenSizeThresholds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ScreenSizeThresholds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VertexCountTargets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VertexCountTargets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TriangleCountTargets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TriangleCountTargets;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SimplificationAlgorithm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SimplificationAlgorithm;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimplificationQuality;
	static void NewProp_bPreserveBoundaries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveBoundaries;
	static void NewProp_bPreserveUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveUVs;
	static void NewProp_bPreserveNormals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveNormals;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_HLODLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HLODCellSize;
	static void NewProp_bEnableNanite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNanite;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NaniteTriangleThreshold;
	static void NewProp_bGenerateImpostors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateImpostors;
	static void NewProp_bUseDitheredLODTransition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDitheredLODTransition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODTransitionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGLODGenerationDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_GenerationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_GenerationMode = { "GenerationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, GenerationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationMode_MetaData), NewProp_GenerationMode_MetaData) }; // 1582331820
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_MaxLODLevels = { "MaxLODLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, MaxLODLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevels_MetaData), NewProp_MaxLODLevels_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_ScreenSizeThresholds_Inner = { "ScreenSizeThresholds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_ScreenSizeThresholds = { "ScreenSizeThresholds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, ScreenSizeThresholds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenSizeThresholds_MetaData), NewProp_ScreenSizeThresholds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_VertexCountTargets_Inner = { "VertexCountTargets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_VertexCountTargets = { "VertexCountTargets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, VertexCountTargets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexCountTargets_MetaData), NewProp_VertexCountTargets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_TriangleCountTargets_Inner = { "TriangleCountTargets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_TriangleCountTargets = { "TriangleCountTargets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, TriangleCountTargets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleCountTargets_MetaData), NewProp_TriangleCountTargets_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_SimplificationAlgorithm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_SimplificationAlgorithm = { "SimplificationAlgorithm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, SimplificationAlgorithm), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationAlgorithm_MetaData), NewProp_SimplificationAlgorithm_MetaData) }; // 1959481846
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_SimplificationQuality = { "SimplificationQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, SimplificationQuality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationQuality_MetaData), NewProp_SimplificationQuality_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveBoundaries_SetBit(void* Obj)
{
	((FAuracronPCGLODGenerationDescriptor*)Obj)->bPreserveBoundaries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveBoundaries = { "bPreserveBoundaries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLODGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveBoundaries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveBoundaries_MetaData), NewProp_bPreserveBoundaries_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveUVs_SetBit(void* Obj)
{
	((FAuracronPCGLODGenerationDescriptor*)Obj)->bPreserveUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveUVs = { "bPreserveUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLODGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveUVs_MetaData), NewProp_bPreserveUVs_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveNormals_SetBit(void* Obj)
{
	((FAuracronPCGLODGenerationDescriptor*)Obj)->bPreserveNormals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveNormals = { "bPreserveNormals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLODGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveNormals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveNormals_MetaData), NewProp_bPreserveNormals_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_HLODLayer = { "HLODLayer", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, HLODLayer), Z_Construct_UClass_UHLODLayer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HLODLayer_MetaData), NewProp_HLODLayer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_HLODCellSize = { "HLODCellSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, HLODCellSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HLODCellSize_MetaData), NewProp_HLODCellSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bEnableNanite_SetBit(void* Obj)
{
	((FAuracronPCGLODGenerationDescriptor*)Obj)->bEnableNanite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bEnableNanite = { "bEnableNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLODGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bEnableNanite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNanite_MetaData), NewProp_bEnableNanite_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_NaniteTriangleThreshold = { "NaniteTriangleThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, NaniteTriangleThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteTriangleThreshold_MetaData), NewProp_NaniteTriangleThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bGenerateImpostors_SetBit(void* Obj)
{
	((FAuracronPCGLODGenerationDescriptor*)Obj)->bGenerateImpostors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bGenerateImpostors = { "bGenerateImpostors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLODGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bGenerateImpostors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateImpostors_MetaData), NewProp_bGenerateImpostors_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bUseDitheredLODTransition_SetBit(void* Obj)
{
	((FAuracronPCGLODGenerationDescriptor*)Obj)->bUseDitheredLODTransition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bUseDitheredLODTransition = { "bUseDitheredLODTransition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLODGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bUseDitheredLODTransition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDitheredLODTransition_MetaData), NewProp_bUseDitheredLODTransition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_LODTransitionTime = { "LODTransitionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLODGenerationDescriptor, LODTransitionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODTransitionTime_MetaData), NewProp_LODTransitionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_GenerationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_GenerationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_MaxLODLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_ScreenSizeThresholds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_ScreenSizeThresholds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_VertexCountTargets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_VertexCountTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_TriangleCountTargets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_TriangleCountTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_SimplificationAlgorithm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_SimplificationAlgorithm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_SimplificationQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveBoundaries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bPreserveNormals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_HLODLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_HLODCellSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bEnableNanite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_NaniteTriangleThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bGenerateImpostors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_bUseDitheredLODTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewProp_LODTransitionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGLODGenerationDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGLODGenerationDescriptor),
	alignof(FAuracronPCGLODGenerationDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGLODGenerationDescriptor *********************************

// ********** Begin ScriptStruct FAuracronPCGCullingDescriptor *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor;
class UScriptStruct* FAuracronPCGCullingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGCullingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Culling Descriptor\n * Describes parameters for culling optimization\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling Descriptor\nDescribes parameters for culling optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingMode_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDrawDistance_MetaData[] = {
		{ "Category", "Distance Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::Distance || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDrawDistance_MetaData[] = {
		{ "Category", "Distance Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::Distance || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFrustumCulling_MetaData[] = {
		{ "Category", "Frustum Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::Frustum || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrustumCullingMargin_MetaData[] = {
		{ "Category", "Frustum Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::Frustum || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOcclusionCulling_MetaData[] = {
		{ "Category", "Occlusion Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::Occlusion || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OcclusionCullingAccuracy_MetaData[] = {
		{ "Category", "Occlusion Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::Occlusion || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinScreenSize_MetaData[] = {
		{ "Category", "Screen Size Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::ScreenSize || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAdaptiveScreenSize_MetaData[] = {
		{ "Category", "Screen Size Culling" },
		{ "EditCondition", "CullingMode == EAuracronPCGCullingMode::ScreenSize || CullingMode == EAuracronPCGCullingMode::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerInstanceCulling_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBatchedCulling_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingBatchSize_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingUpdateFrequency_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CullingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CullingMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDrawDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDrawDistance;
	static void NewProp_bEnableFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFrustumCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrustumCullingMargin;
	static void NewProp_bEnableOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusionCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OcclusionCullingAccuracy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinScreenSize;
	static void NewProp_bUseAdaptiveScreenSize_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAdaptiveScreenSize;
	static void NewProp_bEnablePerInstanceCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerInstanceCulling;
	static void NewProp_bEnableBatchedCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBatchedCulling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CullingBatchSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingUpdateFrequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGCullingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingMode = { "CullingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, CullingMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCullingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingMode_MetaData), NewProp_CullingMode_MetaData) }; // 2831073751
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_MaxDrawDistance = { "MaxDrawDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, MaxDrawDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDrawDistance_MetaData), NewProp_MaxDrawDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_MinDrawDistance = { "MinDrawDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, MinDrawDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDrawDistance_MetaData), NewProp_MinDrawDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableFrustumCulling_SetBit(void* Obj)
{
	((FAuracronPCGCullingDescriptor*)Obj)->bEnableFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableFrustumCulling = { "bEnableFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCullingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFrustumCulling_MetaData), NewProp_bEnableFrustumCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_FrustumCullingMargin = { "FrustumCullingMargin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, FrustumCullingMargin), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrustumCullingMargin_MetaData), NewProp_FrustumCullingMargin_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableOcclusionCulling_SetBit(void* Obj)
{
	((FAuracronPCGCullingDescriptor*)Obj)->bEnableOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableOcclusionCulling = { "bEnableOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCullingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOcclusionCulling_MetaData), NewProp_bEnableOcclusionCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_OcclusionCullingAccuracy = { "OcclusionCullingAccuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, OcclusionCullingAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OcclusionCullingAccuracy_MetaData), NewProp_OcclusionCullingAccuracy_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_MinScreenSize = { "MinScreenSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, MinScreenSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinScreenSize_MetaData), NewProp_MinScreenSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bUseAdaptiveScreenSize_SetBit(void* Obj)
{
	((FAuracronPCGCullingDescriptor*)Obj)->bUseAdaptiveScreenSize = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bUseAdaptiveScreenSize = { "bUseAdaptiveScreenSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCullingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bUseAdaptiveScreenSize_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAdaptiveScreenSize_MetaData), NewProp_bUseAdaptiveScreenSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnablePerInstanceCulling_SetBit(void* Obj)
{
	((FAuracronPCGCullingDescriptor*)Obj)->bEnablePerInstanceCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnablePerInstanceCulling = { "bEnablePerInstanceCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCullingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnablePerInstanceCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerInstanceCulling_MetaData), NewProp_bEnablePerInstanceCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableBatchedCulling_SetBit(void* Obj)
{
	((FAuracronPCGCullingDescriptor*)Obj)->bEnableBatchedCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableBatchedCulling = { "bEnableBatchedCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCullingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableBatchedCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBatchedCulling_MetaData), NewProp_bEnableBatchedCulling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingBatchSize = { "CullingBatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, CullingBatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingBatchSize_MetaData), NewProp_CullingBatchSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingUpdateFrequency = { "CullingUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCullingDescriptor, CullingUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingUpdateFrequency_MetaData), NewProp_CullingUpdateFrequency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_MaxDrawDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_MinDrawDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_FrustumCullingMargin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_OcclusionCullingAccuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_MinScreenSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bUseAdaptiveScreenSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnablePerInstanceCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_bEnableBatchedCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingBatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewProp_CullingUpdateFrequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGCullingDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGCullingDescriptor),
	alignof(FAuracronPCGCullingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGCullingDescriptor ***************************************

// ********** Begin ScriptStruct FAuracronPCGInstancingDescriptor **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor;
class UScriptStruct* FAuracronPCGInstancingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGInstancingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Instancing Descriptor\n * Describes parameters for instancing optimization\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instancing Descriptor\nDescribes parameters for instancing optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancingMode_MetaData[] = {
		{ "Category", "Instancing" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerComponent_MetaData[] = {
		{ "Category", "Instancing" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinInstancesForBatching_MetaData[] = {
		{ "Category", "Instancing" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterTreeDepth_MetaData[] = {
		{ "Category", "Hierarchical" },
		{ "EditCondition", "InstancingMode == EAuracronPCGInstancingMode::Hierarchical" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerCluster_MetaData[] = {
		{ "Category", "Hierarchical" },
		{ "EditCondition", "InstancingMode == EAuracronPCGInstancingMode::Hierarchical" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterRadius_MetaData[] = {
		{ "Category", "Clustered" },
		{ "EditCondition", "InstancingMode == EAuracronPCGInstancingMode::Clustered" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxClustersPerComponent_MetaData[] = {
		{ "Category", "Clustered" },
		{ "EditCondition", "InstancingMode == EAuracronPCGInstancingMode::Clustered" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUCulling_MetaData[] = {
		{ "Category", "GPU" },
		{ "EditCondition", "InstancingMode == EAuracronPCGInstancingMode::GPU" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUCullingBatchSize_MetaData[] = {
		{ "Category", "GPU" },
		{ "EditCondition", "InstancingMode == EAuracronPCGInstancingMode::GPU" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceMerging_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceSorting_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceCaching_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceMergingThreshold_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncInstanceUpdates_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceStreaming_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceStreamingDistance_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InstancingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InstancingMode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinInstancesForBatching;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ClusterTreeDepth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerCluster;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxClustersPerComponent;
	static void NewProp_bEnableGPUCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUCulling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GPUCullingBatchSize;
	static void NewProp_bEnableInstanceMerging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceMerging;
	static void NewProp_bEnableInstanceSorting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceSorting;
	static void NewProp_bEnableInstanceCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceCaching;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InstanceMergingThreshold;
	static void NewProp_bUseAsyncInstanceUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncInstanceUpdates;
	static void NewProp_bEnableInstanceStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceStreaming;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceStreamingDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGInstancingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstancingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstancingMode = { "InstancingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, InstancingMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGInstancingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancingMode_MetaData), NewProp_InstancingMode_MetaData) }; // 2656978254
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MaxInstancesPerComponent = { "MaxInstancesPerComponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, MaxInstancesPerComponent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerComponent_MetaData), NewProp_MaxInstancesPerComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MinInstancesForBatching = { "MinInstancesForBatching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, MinInstancesForBatching), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinInstancesForBatching_MetaData), NewProp_MinInstancesForBatching_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_ClusterTreeDepth = { "ClusterTreeDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, ClusterTreeDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterTreeDepth_MetaData), NewProp_ClusterTreeDepth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MaxInstancesPerCluster = { "MaxInstancesPerCluster", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, MaxInstancesPerCluster), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerCluster_MetaData), NewProp_MaxInstancesPerCluster_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_ClusterRadius = { "ClusterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, ClusterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterRadius_MetaData), NewProp_ClusterRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MaxClustersPerComponent = { "MaxClustersPerComponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, MaxClustersPerComponent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxClustersPerComponent_MetaData), NewProp_MaxClustersPerComponent_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableGPUCulling_SetBit(void* Obj)
{
	((FAuracronPCGInstancingDescriptor*)Obj)->bEnableGPUCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableGPUCulling = { "bEnableGPUCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGInstancingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableGPUCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUCulling_MetaData), NewProp_bEnableGPUCulling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_GPUCullingBatchSize = { "GPUCullingBatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, GPUCullingBatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUCullingBatchSize_MetaData), NewProp_GPUCullingBatchSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceMerging_SetBit(void* Obj)
{
	((FAuracronPCGInstancingDescriptor*)Obj)->bEnableInstanceMerging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceMerging = { "bEnableInstanceMerging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGInstancingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceMerging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceMerging_MetaData), NewProp_bEnableInstanceMerging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceSorting_SetBit(void* Obj)
{
	((FAuracronPCGInstancingDescriptor*)Obj)->bEnableInstanceSorting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceSorting = { "bEnableInstanceSorting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGInstancingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceSorting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceSorting_MetaData), NewProp_bEnableInstanceSorting_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceCaching_SetBit(void* Obj)
{
	((FAuracronPCGInstancingDescriptor*)Obj)->bEnableInstanceCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceCaching = { "bEnableInstanceCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGInstancingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceCaching_MetaData), NewProp_bEnableInstanceCaching_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstanceMergingThreshold = { "InstanceMergingThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, InstanceMergingThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceMergingThreshold_MetaData), NewProp_InstanceMergingThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bUseAsyncInstanceUpdates_SetBit(void* Obj)
{
	((FAuracronPCGInstancingDescriptor*)Obj)->bUseAsyncInstanceUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bUseAsyncInstanceUpdates = { "bUseAsyncInstanceUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGInstancingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bUseAsyncInstanceUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncInstanceUpdates_MetaData), NewProp_bUseAsyncInstanceUpdates_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceStreaming_SetBit(void* Obj)
{
	((FAuracronPCGInstancingDescriptor*)Obj)->bEnableInstanceStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceStreaming = { "bEnableInstanceStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGInstancingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceStreaming_MetaData), NewProp_bEnableInstanceStreaming_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstanceStreamingDistance = { "InstanceStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGInstancingDescriptor, InstanceStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceStreamingDistance_MetaData), NewProp_InstanceStreamingDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstancingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstancingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MaxInstancesPerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MinInstancesForBatching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_ClusterTreeDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MaxInstancesPerCluster,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_ClusterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_MaxClustersPerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableGPUCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_GPUCullingBatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceMerging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceSorting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstanceMergingThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bUseAsyncInstanceUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_bEnableInstanceStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewProp_InstanceStreamingDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGInstancingDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGInstancingDescriptor),
	alignof(FAuracronPCGInstancingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGInstancingDescriptor ************************************

// ********** Begin ScriptStruct FAuracronPCGPerformanceProfilingDescriptor ************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor;
class UScriptStruct* FAuracronPCGPerformanceProfilingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGPerformanceProfilingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Profiling Descriptor\n * Describes parameters for performance monitoring and profiling\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Profiling Descriptor\nDescribes parameters for performance monitoring and profiling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricsToTrack_MetaData[] = {
		{ "Category", "Profiling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRealTimeMonitoring_MetaData[] = {
		{ "Category", "Profiling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfilingUpdateFrequency_MetaData[] = {
		{ "Category", "Profiling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogPerformanceData_MetaData[] = {
		{ "Category", "Profiling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bExportPerformanceData_MetaData[] = {
		{ "Category", "Profiling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAcceptableRenderTime_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAcceptableDrawCalls_MetaData[] = {
		{ "Category", "Thresholds" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 60 FPS\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "60 FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAcceptableTriangles_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAcceptableMemoryUsage_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceAlerts_MetaData[] = {
		{ "Category", "Alerts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// MB\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoOptimizeOnThresholdExceeded_MetaData[] = {
		{ "Category", "Alerts" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTrackGPUPerformance_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTrackMemoryUsage_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceHistorySize_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MetricsToTrack_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MetricsToTrack_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MetricsToTrack;
	static void NewProp_bEnableRealTimeMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRealTimeMonitoring;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProfilingUpdateFrequency;
	static void NewProp_bLogPerformanceData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogPerformanceData;
	static void NewProp_bExportPerformanceData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bExportPerformanceData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAcceptableRenderTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAcceptableDrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAcceptableTriangles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAcceptableMemoryUsage;
	static void NewProp_bEnablePerformanceAlerts_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceAlerts;
	static void NewProp_bAutoOptimizeOnThresholdExceeded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoOptimizeOnThresholdExceeded;
	static void NewProp_bTrackGPUPerformance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTrackGPUPerformance;
	static void NewProp_bTrackMemoryUsage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTrackMemoryUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PerformanceHistorySize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGPerformanceProfilingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MetricsToTrack_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MetricsToTrack_Inner = { "MetricsToTrack", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 3188180533
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MetricsToTrack = { "MetricsToTrack", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceProfilingDescriptor, MetricsToTrack), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricsToTrack_MetaData), NewProp_MetricsToTrack_MetaData) }; // 3188180533
void Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnableRealTimeMonitoring_SetBit(void* Obj)
{
	((FAuracronPCGPerformanceProfilingDescriptor*)Obj)->bEnableRealTimeMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnableRealTimeMonitoring = { "bEnableRealTimeMonitoring", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPerformanceProfilingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnableRealTimeMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRealTimeMonitoring_MetaData), NewProp_bEnableRealTimeMonitoring_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_ProfilingUpdateFrequency = { "ProfilingUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceProfilingDescriptor, ProfilingUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfilingUpdateFrequency_MetaData), NewProp_ProfilingUpdateFrequency_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bLogPerformanceData_SetBit(void* Obj)
{
	((FAuracronPCGPerformanceProfilingDescriptor*)Obj)->bLogPerformanceData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bLogPerformanceData = { "bLogPerformanceData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPerformanceProfilingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bLogPerformanceData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogPerformanceData_MetaData), NewProp_bLogPerformanceData_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bExportPerformanceData_SetBit(void* Obj)
{
	((FAuracronPCGPerformanceProfilingDescriptor*)Obj)->bExportPerformanceData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bExportPerformanceData = { "bExportPerformanceData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPerformanceProfilingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bExportPerformanceData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bExportPerformanceData_MetaData), NewProp_bExportPerformanceData_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableRenderTime = { "MaxAcceptableRenderTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceProfilingDescriptor, MaxAcceptableRenderTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAcceptableRenderTime_MetaData), NewProp_MaxAcceptableRenderTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableDrawCalls = { "MaxAcceptableDrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceProfilingDescriptor, MaxAcceptableDrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAcceptableDrawCalls_MetaData), NewProp_MaxAcceptableDrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableTriangles = { "MaxAcceptableTriangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceProfilingDescriptor, MaxAcceptableTriangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAcceptableTriangles_MetaData), NewProp_MaxAcceptableTriangles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableMemoryUsage = { "MaxAcceptableMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceProfilingDescriptor, MaxAcceptableMemoryUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAcceptableMemoryUsage_MetaData), NewProp_MaxAcceptableMemoryUsage_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnablePerformanceAlerts_SetBit(void* Obj)
{
	((FAuracronPCGPerformanceProfilingDescriptor*)Obj)->bEnablePerformanceAlerts = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnablePerformanceAlerts = { "bEnablePerformanceAlerts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPerformanceProfilingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnablePerformanceAlerts_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceAlerts_MetaData), NewProp_bEnablePerformanceAlerts_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bAutoOptimizeOnThresholdExceeded_SetBit(void* Obj)
{
	((FAuracronPCGPerformanceProfilingDescriptor*)Obj)->bAutoOptimizeOnThresholdExceeded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bAutoOptimizeOnThresholdExceeded = { "bAutoOptimizeOnThresholdExceeded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPerformanceProfilingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bAutoOptimizeOnThresholdExceeded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoOptimizeOnThresholdExceeded_MetaData), NewProp_bAutoOptimizeOnThresholdExceeded_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackGPUPerformance_SetBit(void* Obj)
{
	((FAuracronPCGPerformanceProfilingDescriptor*)Obj)->bTrackGPUPerformance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackGPUPerformance = { "bTrackGPUPerformance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPerformanceProfilingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackGPUPerformance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTrackGPUPerformance_MetaData), NewProp_bTrackGPUPerformance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackMemoryUsage_SetBit(void* Obj)
{
	((FAuracronPCGPerformanceProfilingDescriptor*)Obj)->bTrackMemoryUsage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackMemoryUsage = { "bTrackMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPerformanceProfilingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackMemoryUsage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTrackMemoryUsage_MetaData), NewProp_bTrackMemoryUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_PerformanceHistorySize = { "PerformanceHistorySize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceProfilingDescriptor, PerformanceHistorySize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceHistorySize_MetaData), NewProp_PerformanceHistorySize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MetricsToTrack_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MetricsToTrack_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MetricsToTrack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnableRealTimeMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_ProfilingUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bLogPerformanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bExportPerformanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableRenderTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableDrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_MaxAcceptableMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bEnablePerformanceAlerts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bAutoOptimizeOnThresholdExceeded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackGPUPerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_bTrackMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewProp_PerformanceHistorySize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGPerformanceProfilingDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGPerformanceProfilingDescriptor),
	alignof(FAuracronPCGPerformanceProfilingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGPerformanceProfilingDescriptor **************************

// ********** Begin Class UAuracronPCGLODGeneratorSettings *****************************************
void UAuracronPCGLODGeneratorSettings::StaticRegisterNativesUAuracronPCGLODGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings;
UClass* UAuracronPCGLODGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGLODGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGLODGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGLODGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_NoRegister()
{
	return UAuracronPCGLODGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLODSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDescriptor_MetaData[] = {
		{ "Category", "LOD Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD generation configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD generation configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSourceMeshFromInput_MetaData[] = {
		{ "Category", "Source" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Source mesh configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Source mesh configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceMesh_MetaData[] = {
		{ "Category", "Source" },
		{ "EditCondition", "!bUseSourceMeshFromInput" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateFullLODChain_MetaData[] = {
		{ "Category", "LOD Chain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD chain configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD chain configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeLODTransitions_MetaData[] = {
		{ "Category", "LOD Chain" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateLODChain_MetaData[] = {
		{ "Category", "LOD Chain" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputLODMeshes_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputLODStatistics_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevelAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODDescriptor;
	static void NewProp_bUseSourceMeshFromInput_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSourceMeshFromInput;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SourceMesh;
	static void NewProp_bGenerateFullLODChain_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateFullLODChain;
	static void NewProp_bOptimizeLODTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeLODTransitions;
	static void NewProp_bValidateLODChain_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateLODChain;
	static void NewProp_bOutputLODMeshes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputLODMeshes;
	static void NewProp_bOutputLODStatistics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputLODStatistics;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODLevelAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGLODGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_LODDescriptor = { "LODDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLODGeneratorSettings, LODDescriptor), Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDescriptor_MetaData), NewProp_LODDescriptor_MetaData) }; // 339217262
void Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bUseSourceMeshFromInput_SetBit(void* Obj)
{
	((UAuracronPCGLODGeneratorSettings*)Obj)->bUseSourceMeshFromInput = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bUseSourceMeshFromInput = { "bUseSourceMeshFromInput", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLODGeneratorSettings), &Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bUseSourceMeshFromInput_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSourceMeshFromInput_MetaData), NewProp_bUseSourceMeshFromInput_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLODGeneratorSettings, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceMesh_MetaData), NewProp_SourceMesh_MetaData) };
void Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bGenerateFullLODChain_SetBit(void* Obj)
{
	((UAuracronPCGLODGeneratorSettings*)Obj)->bGenerateFullLODChain = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bGenerateFullLODChain = { "bGenerateFullLODChain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLODGeneratorSettings), &Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bGenerateFullLODChain_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateFullLODChain_MetaData), NewProp_bGenerateFullLODChain_MetaData) };
void Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOptimizeLODTransitions_SetBit(void* Obj)
{
	((UAuracronPCGLODGeneratorSettings*)Obj)->bOptimizeLODTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOptimizeLODTransitions = { "bOptimizeLODTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLODGeneratorSettings), &Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOptimizeLODTransitions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeLODTransitions_MetaData), NewProp_bOptimizeLODTransitions_MetaData) };
void Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bValidateLODChain_SetBit(void* Obj)
{
	((UAuracronPCGLODGeneratorSettings*)Obj)->bValidateLODChain = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bValidateLODChain = { "bValidateLODChain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLODGeneratorSettings), &Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bValidateLODChain_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateLODChain_MetaData), NewProp_bValidateLODChain_MetaData) };
void Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODMeshes_SetBit(void* Obj)
{
	((UAuracronPCGLODGeneratorSettings*)Obj)->bOutputLODMeshes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODMeshes = { "bOutputLODMeshes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLODGeneratorSettings), &Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODMeshes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputLODMeshes_MetaData), NewProp_bOutputLODMeshes_MetaData) };
void Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODStatistics_SetBit(void* Obj)
{
	((UAuracronPCGLODGeneratorSettings*)Obj)->bOutputLODStatistics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODStatistics = { "bOutputLODStatistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLODGeneratorSettings), &Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODStatistics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputLODStatistics_MetaData), NewProp_bOutputLODStatistics_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_LODLevelAttribute = { "LODLevelAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLODGeneratorSettings, LODLevelAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevelAttribute_MetaData), NewProp_LODLevelAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_LODDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bUseSourceMeshFromInput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bGenerateFullLODChain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOptimizeLODTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bValidateLODChain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_bOutputLODStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::NewProp_LODLevelAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGLODGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGLODGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGLODGeneratorSettings);
UAuracronPCGLODGeneratorSettings::~UAuracronPCGLODGeneratorSettings() {}
// ********** End Class UAuracronPCGLODGeneratorSettings *******************************************

// ********** Begin Class UAuracronPCGDistanceBasedCullerSettings **********************************
void UAuracronPCGDistanceBasedCullerSettings::StaticRegisterNativesUAuracronPCGDistanceBasedCullerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings;
UClass* UAuracronPCGDistanceBasedCullerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGDistanceBasedCullerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGDistanceBasedCullerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGDistanceBasedCullerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_NoRegister()
{
	return UAuracronPCGDistanceBasedCullerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLODSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDescriptor_MetaData[] = {
		{ "Category", "Culling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseViewerLocation_MetaData[] = {
		{ "Category", "Reference" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reference point for distance calculations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reference point for distance calculations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferenceLocation_MetaData[] = {
		{ "Category", "Reference" },
		{ "EditCondition", "!bUseViewerLocation" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUpdateReferenceLocationDynamically_MetaData[] = {
		{ "Category", "Reference" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCullingCurve_MetaData[] = {
		{ "Category", "Curves" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling curves\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling curves" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingCurve_MetaData[] = {
		{ "Category", "Curves" },
		{ "EditCondition", "bUseCullingCurve" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncCulling_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCacheDistanceCalculations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheUpdateFrequency_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputCullingInfo_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingStateAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CullingDescriptor;
	static void NewProp_bUseViewerLocation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReferenceLocation;
	static void NewProp_bUpdateReferenceLocationDynamically_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUpdateReferenceLocationDynamically;
	static void NewProp_bUseCullingCurve_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCullingCurve;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CullingCurve;
	static void NewProp_bEnableAsyncCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncCulling;
	static void NewProp_bCacheDistanceCalculations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCacheDistanceCalculations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CacheUpdateFrequency;
	static void NewProp_bOutputCullingInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputCullingInfo;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CullingStateAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DistanceAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGDistanceBasedCullerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CullingDescriptor = { "CullingDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDistanceBasedCullerSettings, CullingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDescriptor_MetaData), NewProp_CullingDescriptor_MetaData) }; // 4262772543
void Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseViewerLocation_SetBit(void* Obj)
{
	((UAuracronPCGDistanceBasedCullerSettings*)Obj)->bUseViewerLocation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseViewerLocation = { "bUseViewerLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDistanceBasedCullerSettings), &Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseViewerLocation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseViewerLocation_MetaData), NewProp_bUseViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_ReferenceLocation = { "ReferenceLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDistanceBasedCullerSettings, ReferenceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferenceLocation_MetaData), NewProp_ReferenceLocation_MetaData) };
void Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUpdateReferenceLocationDynamically_SetBit(void* Obj)
{
	((UAuracronPCGDistanceBasedCullerSettings*)Obj)->bUpdateReferenceLocationDynamically = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUpdateReferenceLocationDynamically = { "bUpdateReferenceLocationDynamically", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDistanceBasedCullerSettings), &Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUpdateReferenceLocationDynamically_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUpdateReferenceLocationDynamically_MetaData), NewProp_bUpdateReferenceLocationDynamically_MetaData) };
void Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseCullingCurve_SetBit(void* Obj)
{
	((UAuracronPCGDistanceBasedCullerSettings*)Obj)->bUseCullingCurve = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseCullingCurve = { "bUseCullingCurve", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDistanceBasedCullerSettings), &Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseCullingCurve_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCullingCurve_MetaData), NewProp_bUseCullingCurve_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CullingCurve = { "CullingCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDistanceBasedCullerSettings, CullingCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingCurve_MetaData), NewProp_CullingCurve_MetaData) };
void Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bEnableAsyncCulling_SetBit(void* Obj)
{
	((UAuracronPCGDistanceBasedCullerSettings*)Obj)->bEnableAsyncCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bEnableAsyncCulling = { "bEnableAsyncCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDistanceBasedCullerSettings), &Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bEnableAsyncCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncCulling_MetaData), NewProp_bEnableAsyncCulling_MetaData) };
void Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bCacheDistanceCalculations_SetBit(void* Obj)
{
	((UAuracronPCGDistanceBasedCullerSettings*)Obj)->bCacheDistanceCalculations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bCacheDistanceCalculations = { "bCacheDistanceCalculations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDistanceBasedCullerSettings), &Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bCacheDistanceCalculations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCacheDistanceCalculations_MetaData), NewProp_bCacheDistanceCalculations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CacheUpdateFrequency = { "CacheUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDistanceBasedCullerSettings, CacheUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheUpdateFrequency_MetaData), NewProp_CacheUpdateFrequency_MetaData) };
void Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bOutputCullingInfo_SetBit(void* Obj)
{
	((UAuracronPCGDistanceBasedCullerSettings*)Obj)->bOutputCullingInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bOutputCullingInfo = { "bOutputCullingInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGDistanceBasedCullerSettings), &Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bOutputCullingInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputCullingInfo_MetaData), NewProp_bOutputCullingInfo_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CullingStateAttribute = { "CullingStateAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDistanceBasedCullerSettings, CullingStateAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingStateAttribute_MetaData), NewProp_CullingStateAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_DistanceAttribute = { "DistanceAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGDistanceBasedCullerSettings, DistanceAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceAttribute_MetaData), NewProp_DistanceAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CullingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_ReferenceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUpdateReferenceLocationDynamically,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bUseCullingCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CullingCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bEnableAsyncCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bCacheDistanceCalculations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CacheUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_bOutputCullingInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_CullingStateAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::NewProp_DistanceAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::ClassParams = {
	&UAuracronPCGDistanceBasedCullerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGDistanceBasedCullerSettings);
UAuracronPCGDistanceBasedCullerSettings::~UAuracronPCGDistanceBasedCullerSettings() {}
// ********** End Class UAuracronPCGDistanceBasedCullerSettings ************************************

// ********** Begin Class UAuracronPCGInstancingOptimizerSettings **********************************
void UAuracronPCGInstancingOptimizerSettings::StaticRegisterNativesUAuracronPCGInstancingOptimizerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings;
UClass* UAuracronPCGInstancingOptimizerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGInstancingOptimizerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGInstancingOptimizerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGInstancingOptimizerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_NoRegister()
{
	return UAuracronPCGInstancingOptimizerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLODSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancingDescriptor_MetaData[] = {
		{ "Category", "Instancing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instancing configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instancing configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGroupByMesh_MetaData[] = {
		{ "Category", "Grouping" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh grouping\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh grouping" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGroupByMaterial_MetaData[] = {
		{ "Category", "Grouping" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGroupByLODLevel_MetaData[] = {
		{ "Category", "Grouping" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupingAttribute_MetaData[] = {
		{ "Category", "Grouping" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeInstanceTransforms_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRemoveDuplicateInstances_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DuplicateThreshold_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSortInstancesByDistance_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateInstancedComponents_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Component creation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component creation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHierarchicalInstancing_MetaData[] = {
		{ "Category", "Components" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUCulling_MetaData[] = {
		{ "Category", "Components" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputInstanceComponents_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputOptimizationStats_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceCountAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstancingDescriptor;
	static void NewProp_bGroupByMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGroupByMesh;
	static void NewProp_bGroupByMaterial_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGroupByMaterial;
	static void NewProp_bGroupByLODLevel_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGroupByLODLevel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupingAttribute;
	static void NewProp_bOptimizeInstanceTransforms_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeInstanceTransforms;
	static void NewProp_bRemoveDuplicateInstances_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRemoveDuplicateInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DuplicateThreshold;
	static void NewProp_bSortInstancesByDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSortInstancesByDistance;
	static void NewProp_bCreateInstancedComponents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateInstancedComponents;
	static void NewProp_bUseHierarchicalInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHierarchicalInstancing;
	static void NewProp_bEnableGPUCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUCulling;
	static void NewProp_bOutputInstanceComponents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputInstanceComponents;
	static void NewProp_bOutputOptimizationStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputOptimizationStats;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceCountAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGInstancingOptimizerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_InstancingDescriptor = { "InstancingDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancingOptimizerSettings, InstancingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancingDescriptor_MetaData), NewProp_InstancingDescriptor_MetaData) }; // 3409027822
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMesh_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bGroupByMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMesh = { "bGroupByMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMesh_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGroupByMesh_MetaData), NewProp_bGroupByMesh_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMaterial_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bGroupByMaterial = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMaterial = { "bGroupByMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMaterial_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGroupByMaterial_MetaData), NewProp_bGroupByMaterial_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByLODLevel_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bGroupByLODLevel = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByLODLevel = { "bGroupByLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByLODLevel_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGroupByLODLevel_MetaData), NewProp_bGroupByLODLevel_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_GroupingAttribute = { "GroupingAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancingOptimizerSettings, GroupingAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupingAttribute_MetaData), NewProp_GroupingAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOptimizeInstanceTransforms_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bOptimizeInstanceTransforms = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOptimizeInstanceTransforms = { "bOptimizeInstanceTransforms", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOptimizeInstanceTransforms_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeInstanceTransforms_MetaData), NewProp_bOptimizeInstanceTransforms_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bRemoveDuplicateInstances_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bRemoveDuplicateInstances = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bRemoveDuplicateInstances = { "bRemoveDuplicateInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bRemoveDuplicateInstances_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRemoveDuplicateInstances_MetaData), NewProp_bRemoveDuplicateInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_DuplicateThreshold = { "DuplicateThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancingOptimizerSettings, DuplicateThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DuplicateThreshold_MetaData), NewProp_DuplicateThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bSortInstancesByDistance_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bSortInstancesByDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bSortInstancesByDistance = { "bSortInstancesByDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bSortInstancesByDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSortInstancesByDistance_MetaData), NewProp_bSortInstancesByDistance_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bCreateInstancedComponents_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bCreateInstancedComponents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bCreateInstancedComponents = { "bCreateInstancedComponents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bCreateInstancedComponents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateInstancedComponents_MetaData), NewProp_bCreateInstancedComponents_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bUseHierarchicalInstancing_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bUseHierarchicalInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bUseHierarchicalInstancing = { "bUseHierarchicalInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bUseHierarchicalInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHierarchicalInstancing_MetaData), NewProp_bUseHierarchicalInstancing_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bEnableGPUCulling_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bEnableGPUCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bEnableGPUCulling = { "bEnableGPUCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bEnableGPUCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUCulling_MetaData), NewProp_bEnableGPUCulling_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputInstanceComponents_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bOutputInstanceComponents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputInstanceComponents = { "bOutputInstanceComponents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputInstanceComponents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputInstanceComponents_MetaData), NewProp_bOutputInstanceComponents_MetaData) };
void Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputOptimizationStats_SetBit(void* Obj)
{
	((UAuracronPCGInstancingOptimizerSettings*)Obj)->bOutputOptimizationStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputOptimizationStats = { "bOutputOptimizationStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGInstancingOptimizerSettings), &Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputOptimizationStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputOptimizationStats_MetaData), NewProp_bOutputOptimizationStats_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_InstanceCountAttribute = { "InstanceCountAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGInstancingOptimizerSettings, InstanceCountAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceCountAttribute_MetaData), NewProp_InstanceCountAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_InstancingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bGroupByLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_GroupingAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOptimizeInstanceTransforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bRemoveDuplicateInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_DuplicateThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bSortInstancesByDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bCreateInstancedComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bUseHierarchicalInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bEnableGPUCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputInstanceComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_bOutputOptimizationStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::NewProp_InstanceCountAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::ClassParams = {
	&UAuracronPCGInstancingOptimizerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGInstancingOptimizerSettings);
UAuracronPCGInstancingOptimizerSettings::~UAuracronPCGInstancingOptimizerSettings() {}
// ********** End Class UAuracronPCGInstancingOptimizerSettings ************************************

// ********** Begin Class UAuracronPCGPerformanceProfilerSettings **********************************
void UAuracronPCGPerformanceProfilerSettings::StaticRegisterNativesUAuracronPCGPerformanceProfilerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings;
UClass* UAuracronPCGPerformanceProfilerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPerformanceProfilerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPerformanceProfilerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPerformanceProfilerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_NoRegister()
{
	return UAuracronPCGPerformanceProfilerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLODSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfilingDescriptor_MetaData[] = {
		{ "Category", "Profiling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Profiling configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profiling configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProfileEntireGraph_MetaData[] = {
		{ "Category", "Scope" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Profiling scope\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profiling scope" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProfileCurrentNode_MetaData[] = {
		{ "Category", "Scope" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProfileRenderingPipeline_MetaData[] = {
		{ "Category", "Scope" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCollectFrameData_MetaData[] = {
		{ "Category", "Data Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data collection\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data collection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCollectMemoryData_MetaData[] = {
		{ "Category", "Data Collection" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCollectGPUData_MetaData[] = {
		{ "Category", "Data Collection" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataCollectionFrames_MetaData[] = {
		{ "Category", "Data Collection" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPerformStatisticalAnalysis_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Analysis\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analysis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDetectPerformanceBottlenecks_MetaData[] = {
		{ "Category", "Analysis" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateOptimizationSuggestions_MetaData[] = {
		{ "Category", "Analysis" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputPerformanceReport_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputRawMetrics_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceScoreAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProfilingDescriptor;
	static void NewProp_bProfileEntireGraph_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProfileEntireGraph;
	static void NewProp_bProfileCurrentNode_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProfileCurrentNode;
	static void NewProp_bProfileRenderingPipeline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProfileRenderingPipeline;
	static void NewProp_bCollectFrameData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCollectFrameData;
	static void NewProp_bCollectMemoryData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCollectMemoryData;
	static void NewProp_bCollectGPUData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCollectGPUData;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataCollectionFrames;
	static void NewProp_bPerformStatisticalAnalysis_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPerformStatisticalAnalysis;
	static void NewProp_bDetectPerformanceBottlenecks_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDetectPerformanceBottlenecks;
	static void NewProp_bGenerateOptimizationSuggestions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateOptimizationSuggestions;
	static void NewProp_bOutputPerformanceReport_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputPerformanceReport;
	static void NewProp_bOutputRawMetrics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputRawMetrics;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceScoreAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPerformanceProfilerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_ProfilingDescriptor = { "ProfilingDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPerformanceProfilerSettings, ProfilingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfilingDescriptor_MetaData), NewProp_ProfilingDescriptor_MetaData) }; // 3885280298
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileEntireGraph_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bProfileEntireGraph = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileEntireGraph = { "bProfileEntireGraph", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileEntireGraph_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProfileEntireGraph_MetaData), NewProp_bProfileEntireGraph_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileCurrentNode_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bProfileCurrentNode = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileCurrentNode = { "bProfileCurrentNode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileCurrentNode_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProfileCurrentNode_MetaData), NewProp_bProfileCurrentNode_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileRenderingPipeline_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bProfileRenderingPipeline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileRenderingPipeline = { "bProfileRenderingPipeline", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileRenderingPipeline_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProfileRenderingPipeline_MetaData), NewProp_bProfileRenderingPipeline_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectFrameData_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bCollectFrameData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectFrameData = { "bCollectFrameData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectFrameData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCollectFrameData_MetaData), NewProp_bCollectFrameData_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectMemoryData_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bCollectMemoryData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectMemoryData = { "bCollectMemoryData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectMemoryData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCollectMemoryData_MetaData), NewProp_bCollectMemoryData_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectGPUData_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bCollectGPUData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectGPUData = { "bCollectGPUData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectGPUData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCollectGPUData_MetaData), NewProp_bCollectGPUData_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_DataCollectionFrames = { "DataCollectionFrames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPerformanceProfilerSettings, DataCollectionFrames), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataCollectionFrames_MetaData), NewProp_DataCollectionFrames_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bPerformStatisticalAnalysis_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bPerformStatisticalAnalysis = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bPerformStatisticalAnalysis = { "bPerformStatisticalAnalysis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bPerformStatisticalAnalysis_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPerformStatisticalAnalysis_MetaData), NewProp_bPerformStatisticalAnalysis_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bDetectPerformanceBottlenecks_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bDetectPerformanceBottlenecks = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bDetectPerformanceBottlenecks = { "bDetectPerformanceBottlenecks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bDetectPerformanceBottlenecks_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDetectPerformanceBottlenecks_MetaData), NewProp_bDetectPerformanceBottlenecks_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bGenerateOptimizationSuggestions_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bGenerateOptimizationSuggestions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bGenerateOptimizationSuggestions = { "bGenerateOptimizationSuggestions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bGenerateOptimizationSuggestions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateOptimizationSuggestions_MetaData), NewProp_bGenerateOptimizationSuggestions_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputPerformanceReport_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bOutputPerformanceReport = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputPerformanceReport = { "bOutputPerformanceReport", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputPerformanceReport_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputPerformanceReport_MetaData), NewProp_bOutputPerformanceReport_MetaData) };
void Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputRawMetrics_SetBit(void* Obj)
{
	((UAuracronPCGPerformanceProfilerSettings*)Obj)->bOutputRawMetrics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputRawMetrics = { "bOutputRawMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPerformanceProfilerSettings), &Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputRawMetrics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputRawMetrics_MetaData), NewProp_bOutputRawMetrics_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_PerformanceScoreAttribute = { "PerformanceScoreAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPerformanceProfilerSettings, PerformanceScoreAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceScoreAttribute_MetaData), NewProp_PerformanceScoreAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_ProfilingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileEntireGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileCurrentNode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bProfileRenderingPipeline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectFrameData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectMemoryData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bCollectGPUData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_DataCollectionFrames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bPerformStatisticalAnalysis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bDetectPerformanceBottlenecks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bGenerateOptimizationSuggestions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputPerformanceReport,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_bOutputRawMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::NewProp_PerformanceScoreAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::ClassParams = {
	&UAuracronPCGPerformanceProfilerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPerformanceProfilerSettings);
UAuracronPCGPerformanceProfilerSettings::~UAuracronPCGPerformanceProfilerSettings() {}
// ********** End Class UAuracronPCGPerformanceProfilerSettings ************************************

// ********** Begin Class UAuracronPCGMeshSimplifierSettings ***************************************
void UAuracronPCGMeshSimplifierSettings::StaticRegisterNativesUAuracronPCGMeshSimplifierSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings;
UClass* UAuracronPCGMeshSimplifierSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGMeshSimplifierSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGMeshSimplifierSettings"),
			Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGMeshSimplifierSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_NoRegister()
{
	return UAuracronPCGMeshSimplifierSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLODSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationAlgorithm_MetaData[] = {
		{ "Category", "Simplification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simplification algorithm\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simplification algorithm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePercentageReduction_MetaData[] = {
		{ "Category", "Target" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target reduction\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target reduction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReductionPercentage_MetaData[] = {
		{ "Category", "Target" },
		{ "EditCondition", "bUsePercentageReduction" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetTriangleCount_MetaData[] = {
		{ "Category", "Target" },
		{ "EditCondition", "!bUsePercentageReduction" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetVertexCount_MetaData[] = {
		{ "Category", "Target" },
		{ "EditCondition", "!bUsePercentageReduction" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationQuality_MetaData[] = {
		{ "Category", "Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Quality settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveBoundaries_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveUVSeams_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveNormals_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveMaterialBoundaries_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EdgeCollapseThreshold_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAdaptiveSimplification_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeForGPU_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputSimplificationStats_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReductionRatioAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SimplificationAlgorithm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SimplificationAlgorithm;
	static void NewProp_bUsePercentageReduction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePercentageReduction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReductionPercentage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetTriangleCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetVertexCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimplificationQuality;
	static void NewProp_bPreserveBoundaries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveBoundaries;
	static void NewProp_bPreserveUVSeams_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveUVSeams;
	static void NewProp_bPreserveNormals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveNormals;
	static void NewProp_bPreserveMaterialBoundaries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveMaterialBoundaries;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EdgeCollapseThreshold;
	static void NewProp_bUseAdaptiveSimplification_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAdaptiveSimplification;
	static void NewProp_bOptimizeForGPU_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeForGPU;
	static void NewProp_bOutputSimplificationStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputSimplificationStats;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReductionRatioAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGMeshSimplifierSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_SimplificationAlgorithm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_SimplificationAlgorithm = { "SimplificationAlgorithm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshSimplifierSettings, SimplificationAlgorithm), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationAlgorithm_MetaData), NewProp_SimplificationAlgorithm_MetaData) }; // 1959481846
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUsePercentageReduction_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bUsePercentageReduction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUsePercentageReduction = { "bUsePercentageReduction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUsePercentageReduction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePercentageReduction_MetaData), NewProp_bUsePercentageReduction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_ReductionPercentage = { "ReductionPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshSimplifierSettings, ReductionPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReductionPercentage_MetaData), NewProp_ReductionPercentage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_TargetTriangleCount = { "TargetTriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshSimplifierSettings, TargetTriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetTriangleCount_MetaData), NewProp_TargetTriangleCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_TargetVertexCount = { "TargetVertexCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshSimplifierSettings, TargetVertexCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetVertexCount_MetaData), NewProp_TargetVertexCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_SimplificationQuality = { "SimplificationQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshSimplifierSettings, SimplificationQuality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationQuality_MetaData), NewProp_SimplificationQuality_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveBoundaries_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bPreserveBoundaries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveBoundaries = { "bPreserveBoundaries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveBoundaries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveBoundaries_MetaData), NewProp_bPreserveBoundaries_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveUVSeams_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bPreserveUVSeams = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveUVSeams = { "bPreserveUVSeams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveUVSeams_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveUVSeams_MetaData), NewProp_bPreserveUVSeams_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveNormals_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bPreserveNormals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveNormals = { "bPreserveNormals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveNormals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveNormals_MetaData), NewProp_bPreserveNormals_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveMaterialBoundaries_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bPreserveMaterialBoundaries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveMaterialBoundaries = { "bPreserveMaterialBoundaries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveMaterialBoundaries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveMaterialBoundaries_MetaData), NewProp_bPreserveMaterialBoundaries_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_EdgeCollapseThreshold = { "EdgeCollapseThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshSimplifierSettings, EdgeCollapseThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EdgeCollapseThreshold_MetaData), NewProp_EdgeCollapseThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUseAdaptiveSimplification_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bUseAdaptiveSimplification = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUseAdaptiveSimplification = { "bUseAdaptiveSimplification", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUseAdaptiveSimplification_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAdaptiveSimplification_MetaData), NewProp_bUseAdaptiveSimplification_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOptimizeForGPU_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bOptimizeForGPU = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOptimizeForGPU = { "bOptimizeForGPU", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOptimizeForGPU_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeForGPU_MetaData), NewProp_bOptimizeForGPU_MetaData) };
void Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOutputSimplificationStats_SetBit(void* Obj)
{
	((UAuracronPCGMeshSimplifierSettings*)Obj)->bOutputSimplificationStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOutputSimplificationStats = { "bOutputSimplificationStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMeshSimplifierSettings), &Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOutputSimplificationStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputSimplificationStats_MetaData), NewProp_bOutputSimplificationStats_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_ReductionRatioAttribute = { "ReductionRatioAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMeshSimplifierSettings, ReductionRatioAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReductionRatioAttribute_MetaData), NewProp_ReductionRatioAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_SimplificationAlgorithm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_SimplificationAlgorithm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUsePercentageReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_ReductionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_TargetTriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_TargetVertexCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_SimplificationQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveBoundaries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveUVSeams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveNormals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bPreserveMaterialBoundaries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_EdgeCollapseThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bUseAdaptiveSimplification,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOptimizeForGPU,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_bOutputSimplificationStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::NewProp_ReductionRatioAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::ClassParams = {
	&UAuracronPCGMeshSimplifierSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGMeshSimplifierSettings);
UAuracronPCGMeshSimplifierSettings::~UAuracronPCGMeshSimplifierSettings() {}
// ********** End Class UAuracronPCGMeshSimplifierSettings *****************************************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CalculateMemoryUsage *****************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics
{
	struct AuracronPCGLODSystemUtils_eventCalculateMemoryUsage_Parms
	{
		TArray<UPrimitiveComponent*> Components;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Components_MetaData[] = {
		{ "EditInline", "true" },
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Components_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Components;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::NewProp_Components_Inner = { "Components", nullptr, (EPropertyFlags)0x0000000000080000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::NewProp_Components = { "Components", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateMemoryUsage_Parms, Components), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Components_MetaData), NewProp_Components_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::NewProp_Components_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::NewProp_Components,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CalculateMemoryUsage", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::AuracronPCGLODSystemUtils_eventCalculateMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::AuracronPCGLODSystemUtils_eventCalculateMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCalculateMemoryUsage)
{
	P_GET_TARRAY_REF(UPrimitiveComponent*,Z_Param_Out_Components);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLODSystemUtils::CalculateMemoryUsage(Z_Param_Out_Components);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CalculateMemoryUsage *******************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CalculateOcclusionFactor *************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics
{
	struct AuracronPCGLODSystemUtils_eventCalculateOcclusionFactor_Parms
	{
		FVector Location;
		FVector ViewerLocation;
		UWorld* World;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOcclusionFactor_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOcclusionFactor_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOcclusionFactor_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOcclusionFactor_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CalculateOcclusionFactor", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::AuracronPCGLODSystemUtils_eventCalculateOcclusionFactor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::AuracronPCGLODSystemUtils_eventCalculateOcclusionFactor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCalculateOcclusionFactor)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLODSystemUtils::CalculateOcclusionFactor(Z_Param_Out_Location,Z_Param_Out_ViewerLocation,Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CalculateOcclusionFactor ***************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CalculateOptimalLODLevel *************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics
{
	struct AuracronPCGLODSystemUtils_eventCalculateOptimalLODLevel_Parms
	{
		FVector ViewerLocation;
		FVector ObjectLocation;
		TArray<float> LODDistances;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOptimalLODLevel_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_ObjectLocation = { "ObjectLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOptimalLODLevel_Parms, ObjectLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectLocation_MetaData), NewProp_ObjectLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOptimalLODLevel_Parms, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateOptimalLODLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_ObjectLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CalculateOptimalLODLevel", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::AuracronPCGLODSystemUtils_eventCalculateOptimalLODLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::AuracronPCGLODSystemUtils_eventCalculateOptimalLODLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCalculateOptimalLODLevel)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ObjectLocation);
	P_GET_TARRAY_REF(float,Z_Param_Out_LODDistances);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGLODSystemUtils::CalculateOptimalLODLevel(Z_Param_Out_ViewerLocation,Z_Param_Out_ObjectLocation,Z_Param_Out_LODDistances);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CalculateOptimalLODLevel ***************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CalculatePerformanceScore ************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics
{
	struct AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms
	{
		float RenderTime;
		int32 DrawCalls;
		int32 TriangleCount;
		float MemoryUsage;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RenderTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TriangleCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_RenderTime = { "RenderTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms, RenderTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms, DrawCalls), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_TriangleCount = { "TriangleCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms, TriangleCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_MemoryUsage = { "MemoryUsage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms, MemoryUsage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_RenderTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_TriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_MemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CalculatePerformanceScore", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::AuracronPCGLODSystemUtils_eventCalculatePerformanceScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCalculatePerformanceScore)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_RenderTime);
	P_GET_PROPERTY(FIntProperty,Z_Param_DrawCalls);
	P_GET_PROPERTY(FIntProperty,Z_Param_TriangleCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MemoryUsage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLODSystemUtils::CalculatePerformanceScore(Z_Param_RenderTime,Z_Param_DrawCalls,Z_Param_TriangleCount,Z_Param_MemoryUsage);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CalculatePerformanceScore **************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CalculateScreenSize ******************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics
{
	struct AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms
	{
		FVector ViewerLocation;
		FVector ObjectLocation;
		FVector ObjectBounds;
		float FOV;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectBounds_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectBounds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FOV;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ObjectLocation = { "ObjectLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms, ObjectLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectLocation_MetaData), NewProp_ObjectLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ObjectBounds = { "ObjectBounds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms, ObjectBounds), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectBounds_MetaData), NewProp_ObjectBounds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_FOV = { "FOV", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms, FOV), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ObjectLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ObjectBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_FOV,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CalculateScreenSize", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::AuracronPCGLODSystemUtils_eventCalculateScreenSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCalculateScreenSize)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ObjectLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ObjectBounds);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FOV);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLODSystemUtils::CalculateScreenSize(Z_Param_Out_ViewerLocation,Z_Param_Out_ObjectLocation,Z_Param_Out_ObjectBounds,Z_Param_FOV);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CalculateScreenSize ********************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CountDrawCalls ***********************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics
{
	struct AuracronPCGLODSystemUtils_eventCountDrawCalls_Parms
	{
		TArray<UPrimitiveComponent*> Components;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Components_MetaData[] = {
		{ "EditInline", "true" },
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Components_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Components;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::NewProp_Components_Inner = { "Components", nullptr, (EPropertyFlags)0x0000000000080000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::NewProp_Components = { "Components", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCountDrawCalls_Parms, Components), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Components_MetaData), NewProp_Components_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCountDrawCalls_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::NewProp_Components_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::NewProp_Components,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CountDrawCalls", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::AuracronPCGLODSystemUtils_eventCountDrawCalls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::AuracronPCGLODSystemUtils_eventCountDrawCalls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCountDrawCalls)
{
	P_GET_TARRAY_REF(UPrimitiveComponent*,Z_Param_Out_Components);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGLODSystemUtils::CountDrawCalls(Z_Param_Out_Components);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CountDrawCalls *************************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CountTriangles ***********************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics
{
	struct AuracronPCGLODSystemUtils_eventCountTriangles_Parms
	{
		UStaticMesh* Mesh;
		int32 LODLevel;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "CPP_Default_LODLevel", "0" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCountTriangles_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCountTriangles_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCountTriangles_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CountTriangles", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::AuracronPCGLODSystemUtils_eventCountTriangles_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::AuracronPCGLODSystemUtils_eventCountTriangles_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCountTriangles)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGLODSystemUtils::CountTriangles(Z_Param_Mesh,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CountTriangles *************************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CreateDefaultLODDescriptor ***********
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics
{
	struct AuracronPCGLODSystemUtils_eventCreateDefaultLODDescriptor_Parms
	{
		EAuracronPCGLODGenerationMode GenerationMode;
		FAuracronPCGLODGenerationDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_GenerationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GenerationMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::NewProp_GenerationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::NewProp_GenerationMode = { "GenerationMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateDefaultLODDescriptor_Parms, GenerationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLODGenerationMode, METADATA_PARAMS(0, nullptr) }; // 1582331820
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateDefaultLODDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor, METADATA_PARAMS(0, nullptr) }; // 339217262
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::NewProp_GenerationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::NewProp_GenerationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CreateDefaultLODDescriptor", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::AuracronPCGLODSystemUtils_eventCreateDefaultLODDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::AuracronPCGLODSystemUtils_eventCreateDefaultLODDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCreateDefaultLODDescriptor)
{
	P_GET_ENUM(EAuracronPCGLODGenerationMode,Z_Param_GenerationMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGLODGenerationDescriptor*)Z_Param__Result=UAuracronPCGLODSystemUtils::CreateDefaultLODDescriptor(EAuracronPCGLODGenerationMode(Z_Param_GenerationMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CreateDefaultLODDescriptor *************

// ********** Begin Class UAuracronPCGLODSystemUtils Function CreateHierarchicalInstancedComponent *
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics
{
	struct AuracronPCGLODSystemUtils_eventCreateHierarchicalInstancedComponent_Parms
	{
		AActor* Owner;
		UStaticMesh* Mesh;
		FAuracronPCGInstancingDescriptor InstancingDescriptor;
		UHierarchicalInstancedStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstancingDescriptor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateHierarchicalInstancedComponent_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateHierarchicalInstancedComponent_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_InstancingDescriptor = { "InstancingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateHierarchicalInstancedComponent_Parms, InstancingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancingDescriptor_MetaData), NewProp_InstancingDescriptor_MetaData) }; // 3409027822
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateHierarchicalInstancedComponent_Parms, ReturnValue), Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_Owner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_InstancingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CreateHierarchicalInstancedComponent", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::AuracronPCGLODSystemUtils_eventCreateHierarchicalInstancedComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::AuracronPCGLODSystemUtils_eventCreateHierarchicalInstancedComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCreateHierarchicalInstancedComponent)
{
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_STRUCT_REF(FAuracronPCGInstancingDescriptor,Z_Param_Out_InstancingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UHierarchicalInstancedStaticMeshComponent**)Z_Param__Result=UAuracronPCGLODSystemUtils::CreateHierarchicalInstancedComponent(Z_Param_Owner,Z_Param_Mesh,Z_Param_Out_InstancingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CreateHierarchicalInstancedComponent ***

// ********** Begin Class UAuracronPCGLODSystemUtils Function CreateOptimizedInstancedComponent ****
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics
{
	struct AuracronPCGLODSystemUtils_eventCreateOptimizedInstancedComponent_Parms
	{
		AActor* Owner;
		UStaticMesh* Mesh;
		FAuracronPCGInstancingDescriptor InstancingDescriptor;
		UInstancedStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instancing utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instancing utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstancingDescriptor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateOptimizedInstancedComponent_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateOptimizedInstancedComponent_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_InstancingDescriptor = { "InstancingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateOptimizedInstancedComponent_Parms, InstancingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancingDescriptor_MetaData), NewProp_InstancingDescriptor_MetaData) }; // 3409027822
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventCreateOptimizedInstancedComponent_Parms, ReturnValue), Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_Owner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_InstancingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "CreateOptimizedInstancedComponent", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::AuracronPCGLODSystemUtils_eventCreateOptimizedInstancedComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::AuracronPCGLODSystemUtils_eventCreateOptimizedInstancedComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execCreateOptimizedInstancedComponent)
{
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_STRUCT_REF(FAuracronPCGInstancingDescriptor,Z_Param_Out_InstancingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UInstancedStaticMeshComponent**)Z_Param__Result=UAuracronPCGLODSystemUtils::CreateOptimizedInstancedComponent(Z_Param_Owner,Z_Param_Mesh,Z_Param_Out_InstancingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function CreateOptimizedInstancedComponent ******

// ********** Begin Class UAuracronPCGLODSystemUtils Function GenerateLODChain *********************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics
{
	struct AuracronPCGLODSystemUtils_eventGenerateLODChain_Parms
	{
		UStaticMesh* SourceMesh;
		FAuracronPCGLODGenerationDescriptor LODDescriptor;
		TArray<UStaticMesh*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD generation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD generation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODDescriptor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventGenerateLODChain_Parms, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_LODDescriptor = { "LODDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventGenerateLODChain_Parms, LODDescriptor), Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDescriptor_MetaData), NewProp_LODDescriptor_MetaData) }; // 339217262
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventGenerateLODChain_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_LODDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "GenerateLODChain", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::AuracronPCGLODSystemUtils_eventGenerateLODChain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::AuracronPCGLODSystemUtils_eventGenerateLODChain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execGenerateLODChain)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_SourceMesh);
	P_GET_STRUCT_REF(FAuracronPCGLODGenerationDescriptor,Z_Param_Out_LODDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<UStaticMesh*>*)Z_Param__Result=UAuracronPCGLODSystemUtils::GenerateLODChain(Z_Param_SourceMesh,Z_Param_Out_LODDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function GenerateLODChain ***********************

// ********** Begin Class UAuracronPCGLODSystemUtils Function IsInFrustum **************************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics
{
	struct AuracronPCGLODSystemUtils_eventIsInFrustum_Parms
	{
		FVector Location;
		FVector ViewerLocation;
		FVector ViewDirection;
		float FOV;
		float AspectRatio;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FOV;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AspectRatio;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventIsInFrustum_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventIsInFrustum_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ViewDirection = { "ViewDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventIsInFrustum_Parms, ViewDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewDirection_MetaData), NewProp_ViewDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_FOV = { "FOV", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventIsInFrustum_Parms, FOV), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_AspectRatio = { "AspectRatio", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventIsInFrustum_Parms, AspectRatio), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLODSystemUtils_eventIsInFrustum_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLODSystemUtils_eventIsInFrustum_Parms), &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ViewDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_FOV,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_AspectRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "IsInFrustum", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::AuracronPCGLODSystemUtils_eventIsInFrustum_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::AuracronPCGLODSystemUtils_eventIsInFrustum_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execIsInFrustum)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewDirection);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FOV);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AspectRatio);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLODSystemUtils::IsInFrustum(Z_Param_Out_Location,Z_Param_Out_ViewerLocation,Z_Param_Out_ViewDirection,Z_Param_FOV,Z_Param_AspectRatio);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function IsInFrustum ****************************

// ********** Begin Class UAuracronPCGLODSystemUtils Function MeasureRenderTime ********************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics
{
	struct AuracronPCGLODSystemUtils_eventMeasureRenderTime_Parms
	{
		UWorld* World;
		TArray<UPrimitiveComponent*> Components;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Components_MetaData[] = {
		{ "EditInline", "true" },
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Components_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Components;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventMeasureRenderTime_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_Components_Inner = { "Components", nullptr, (EPropertyFlags)0x0000000000080000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_Components = { "Components", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventMeasureRenderTime_Parms, Components), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Components_MetaData), NewProp_Components_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventMeasureRenderTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_Components_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_Components,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "MeasureRenderTime", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::AuracronPCGLODSystemUtils_eventMeasureRenderTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::AuracronPCGLODSystemUtils_eventMeasureRenderTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execMeasureRenderTime)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_TARRAY_REF(UPrimitiveComponent*,Z_Param_Out_Components);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLODSystemUtils::MeasureRenderTime(Z_Param_World,Z_Param_Out_Components);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function MeasureRenderTime **********************

// ********** Begin Class UAuracronPCGLODSystemUtils Function OptimizeInstanceTransforms ***********
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics
{
	struct AuracronPCGLODSystemUtils_eventOptimizeInstanceTransforms_Parms
	{
		TArray<FTransform> Transforms;
		float MergingThreshold;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MergingThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventOptimizeInstanceTransforms_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::NewProp_MergingThreshold = { "MergingThreshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventOptimizeInstanceTransforms_Parms, MergingThreshold), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::NewProp_MergingThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "OptimizeInstanceTransforms", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::AuracronPCGLODSystemUtils_eventOptimizeInstanceTransforms_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::AuracronPCGLODSystemUtils_eventOptimizeInstanceTransforms_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execOptimizeInstanceTransforms)
{
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MergingThreshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGLODSystemUtils::OptimizeInstanceTransforms(Z_Param_Out_Transforms,Z_Param_MergingThreshold);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function OptimizeInstanceTransforms *************

// ********** Begin Class UAuracronPCGLODSystemUtils Function PerformBatchCulling ******************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics
{
	struct AuracronPCGLODSystemUtils_eventPerformBatchCulling_Parms
	{
		TArray<FVector> InstanceLocations;
		FVector ViewerLocation;
		FAuracronPCGCullingDescriptor CullingDescriptor;
		TArray<int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceLocations_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceLocations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CullingDescriptor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_InstanceLocations_Inner = { "InstanceLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_InstanceLocations = { "InstanceLocations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventPerformBatchCulling_Parms, InstanceLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceLocations_MetaData), NewProp_InstanceLocations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventPerformBatchCulling_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_CullingDescriptor = { "CullingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventPerformBatchCulling_Parms, CullingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDescriptor_MetaData), NewProp_CullingDescriptor_MetaData) }; // 4262772543
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventPerformBatchCulling_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_InstanceLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_InstanceLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_CullingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "PerformBatchCulling", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::AuracronPCGLODSystemUtils_eventPerformBatchCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::AuracronPCGLODSystemUtils_eventPerformBatchCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execPerformBatchCulling)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_InstanceLocations);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FAuracronPCGCullingDescriptor,Z_Param_Out_CullingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<int32>*)Z_Param__Result=UAuracronPCGLODSystemUtils::PerformBatchCulling(Z_Param_Out_InstanceLocations,Z_Param_Out_ViewerLocation,Z_Param_Out_CullingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function PerformBatchCulling ********************

// ********** Begin Class UAuracronPCGLODSystemUtils Function RemoveDuplicateInstances *************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics
{
	struct AuracronPCGLODSystemUtils_eventRemoveDuplicateInstances_Parms
	{
		TArray<FTransform> Transforms;
		float Threshold;
		TArray<FTransform> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transforms_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Threshold;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventRemoveDuplicateInstances_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transforms_MetaData), NewProp_Transforms_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventRemoveDuplicateInstances_Parms, Threshold), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventRemoveDuplicateInstances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_Threshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "RemoveDuplicateInstances", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::AuracronPCGLODSystemUtils_eventRemoveDuplicateInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::AuracronPCGLODSystemUtils_eventRemoveDuplicateInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execRemoveDuplicateInstances)
{
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Threshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FTransform>*)Z_Param__Result=UAuracronPCGLODSystemUtils::RemoveDuplicateInstances(Z_Param_Out_Transforms,Z_Param_Threshold);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function RemoveDuplicateInstances ***************

// ********** Begin Class UAuracronPCGLODSystemUtils Function ShouldCullInstance *******************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics
{
	struct AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms
	{
		FVector InstanceLocation;
		FVector ViewerLocation;
		FAuracronPCGCullingDescriptor CullingDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CullingDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_InstanceLocation = { "InstanceLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms, InstanceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceLocation_MetaData), NewProp_InstanceLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_CullingDescriptor = { "CullingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms, CullingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDescriptor_MetaData), NewProp_CullingDescriptor_MetaData) }; // 4262772543
void Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms), &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_InstanceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_CullingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "ShouldCullInstance", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::AuracronPCGLODSystemUtils_eventShouldCullInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execShouldCullInstance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_InstanceLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FAuracronPCGCullingDescriptor,Z_Param_Out_CullingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLODSystemUtils::ShouldCullInstance(Z_Param_Out_InstanceLocation,Z_Param_Out_ViewerLocation,Z_Param_Out_CullingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function ShouldCullInstance *********************

// ********** Begin Class UAuracronPCGLODSystemUtils Function SimplifyMesh *************************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics
{
	struct AuracronPCGLODSystemUtils_eventSimplifyMesh_Parms
	{
		UStaticMesh* SourceMesh;
		float ReductionPercentage;
		EAuracronPCGMeshSimplificationAlgorithm Algorithm;
		UStaticMesh* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReductionPercentage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Algorithm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Algorithm;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventSimplifyMesh_Parms, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_ReductionPercentage = { "ReductionPercentage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventSimplifyMesh_Parms, ReductionPercentage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_Algorithm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_Algorithm = { "Algorithm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventSimplifyMesh_Parms, Algorithm), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMeshSimplificationAlgorithm, METADATA_PARAMS(0, nullptr) }; // 1959481846
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventSimplifyMesh_Parms, ReturnValue), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_ReductionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_Algorithm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_Algorithm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "SimplifyMesh", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::AuracronPCGLODSystemUtils_eventSimplifyMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::AuracronPCGLODSystemUtils_eventSimplifyMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execSimplifyMesh)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_SourceMesh);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ReductionPercentage);
	P_GET_ENUM(EAuracronPCGMeshSimplificationAlgorithm,Z_Param_Algorithm);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UStaticMesh**)Z_Param__Result=UAuracronPCGLODSystemUtils::SimplifyMesh(Z_Param_SourceMesh,Z_Param_ReductionPercentage,EAuracronPCGMeshSimplificationAlgorithm(Z_Param_Algorithm));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function SimplifyMesh ***************************

// ********** Begin Class UAuracronPCGLODSystemUtils Function ValidateCullingDescriptor ************
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics
{
	struct AuracronPCGLODSystemUtils_eventValidateCullingDescriptor_Parms
	{
		FAuracronPCGCullingDescriptor CullingDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CullingDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::NewProp_CullingDescriptor = { "CullingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventValidateCullingDescriptor_Parms, CullingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDescriptor_MetaData), NewProp_CullingDescriptor_MetaData) }; // 4262772543
void Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLODSystemUtils_eventValidateCullingDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLODSystemUtils_eventValidateCullingDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::NewProp_CullingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "ValidateCullingDescriptor", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::AuracronPCGLODSystemUtils_eventValidateCullingDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::AuracronPCGLODSystemUtils_eventValidateCullingDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execValidateCullingDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGCullingDescriptor,Z_Param_Out_CullingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLODSystemUtils::ValidateCullingDescriptor(Z_Param_Out_CullingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function ValidateCullingDescriptor **************

// ********** Begin Class UAuracronPCGLODSystemUtils Function ValidateInstancingDescriptor *********
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics
{
	struct AuracronPCGLODSystemUtils_eventValidateInstancingDescriptor_Parms
	{
		FAuracronPCGInstancingDescriptor InstancingDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstancingDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::NewProp_InstancingDescriptor = { "InstancingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventValidateInstancingDescriptor_Parms, InstancingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancingDescriptor_MetaData), NewProp_InstancingDescriptor_MetaData) }; // 3409027822
void Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLODSystemUtils_eventValidateInstancingDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLODSystemUtils_eventValidateInstancingDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::NewProp_InstancingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "ValidateInstancingDescriptor", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::AuracronPCGLODSystemUtils_eventValidateInstancingDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::AuracronPCGLODSystemUtils_eventValidateInstancingDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execValidateInstancingDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGInstancingDescriptor,Z_Param_Out_InstancingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLODSystemUtils::ValidateInstancingDescriptor(Z_Param_Out_InstancingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function ValidateInstancingDescriptor ***********

// ********** Begin Class UAuracronPCGLODSystemUtils Function ValidateLODGenerationDescriptor ******
struct Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics
{
	struct AuracronPCGLODSystemUtils_eventValidateLODGenerationDescriptor_Parms
	{
		FAuracronPCGLODGenerationDescriptor LODDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::NewProp_LODDescriptor = { "LODDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLODSystemUtils_eventValidateLODGenerationDescriptor_Parms, LODDescriptor), Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDescriptor_MetaData), NewProp_LODDescriptor_MetaData) }; // 339217262
void Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLODSystemUtils_eventValidateLODGenerationDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLODSystemUtils_eventValidateLODGenerationDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::NewProp_LODDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLODSystemUtils, nullptr, "ValidateLODGenerationDescriptor", Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::AuracronPCGLODSystemUtils_eventValidateLODGenerationDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::AuracronPCGLODSystemUtils_eventValidateLODGenerationDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLODSystemUtils::execValidateLODGenerationDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGLODGenerationDescriptor,Z_Param_Out_LODDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLODSystemUtils::ValidateLODGenerationDescriptor(Z_Param_Out_LODDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLODSystemUtils Function ValidateLODGenerationDescriptor ********

// ********** Begin Class UAuracronPCGLODSystemUtils ***********************************************
void UAuracronPCGLODSystemUtils::StaticRegisterNativesUAuracronPCGLODSystemUtils()
{
	UClass* Class = UAuracronPCGLODSystemUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateMemoryUsage", &UAuracronPCGLODSystemUtils::execCalculateMemoryUsage },
		{ "CalculateOcclusionFactor", &UAuracronPCGLODSystemUtils::execCalculateOcclusionFactor },
		{ "CalculateOptimalLODLevel", &UAuracronPCGLODSystemUtils::execCalculateOptimalLODLevel },
		{ "CalculatePerformanceScore", &UAuracronPCGLODSystemUtils::execCalculatePerformanceScore },
		{ "CalculateScreenSize", &UAuracronPCGLODSystemUtils::execCalculateScreenSize },
		{ "CountDrawCalls", &UAuracronPCGLODSystemUtils::execCountDrawCalls },
		{ "CountTriangles", &UAuracronPCGLODSystemUtils::execCountTriangles },
		{ "CreateDefaultLODDescriptor", &UAuracronPCGLODSystemUtils::execCreateDefaultLODDescriptor },
		{ "CreateHierarchicalInstancedComponent", &UAuracronPCGLODSystemUtils::execCreateHierarchicalInstancedComponent },
		{ "CreateOptimizedInstancedComponent", &UAuracronPCGLODSystemUtils::execCreateOptimizedInstancedComponent },
		{ "GenerateLODChain", &UAuracronPCGLODSystemUtils::execGenerateLODChain },
		{ "IsInFrustum", &UAuracronPCGLODSystemUtils::execIsInFrustum },
		{ "MeasureRenderTime", &UAuracronPCGLODSystemUtils::execMeasureRenderTime },
		{ "OptimizeInstanceTransforms", &UAuracronPCGLODSystemUtils::execOptimizeInstanceTransforms },
		{ "PerformBatchCulling", &UAuracronPCGLODSystemUtils::execPerformBatchCulling },
		{ "RemoveDuplicateInstances", &UAuracronPCGLODSystemUtils::execRemoveDuplicateInstances },
		{ "ShouldCullInstance", &UAuracronPCGLODSystemUtils::execShouldCullInstance },
		{ "SimplifyMesh", &UAuracronPCGLODSystemUtils::execSimplifyMesh },
		{ "ValidateCullingDescriptor", &UAuracronPCGLODSystemUtils::execValidateCullingDescriptor },
		{ "ValidateInstancingDescriptor", &UAuracronPCGLODSystemUtils::execValidateInstancingDescriptor },
		{ "ValidateLODGenerationDescriptor", &UAuracronPCGLODSystemUtils::execValidateLODGenerationDescriptor },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils;
UClass* UAuracronPCGLODSystemUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGLODSystemUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGLODSystemUtils"),
			Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGLODSystemUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGLODSystemUtils_NoRegister()
{
	return UAuracronPCGLODSystemUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD System Utilities\n * Utility functions for LOD and optimization operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGLODSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLODSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD System Utilities\nUtility functions for LOD and optimization operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateMemoryUsage, "CalculateMemoryUsage" }, // 2315982998
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOcclusionFactor, "CalculateOcclusionFactor" }, // 4177743898
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateOptimalLODLevel, "CalculateOptimalLODLevel" }, // 1330181848
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculatePerformanceScore, "CalculatePerformanceScore" }, // 3201103944
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CalculateScreenSize, "CalculateScreenSize" }, // 375808285
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountDrawCalls, "CountDrawCalls" }, // 1186496259
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CountTriangles, "CountTriangles" }, // 3328923740
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateDefaultLODDescriptor, "CreateDefaultLODDescriptor" }, // 3414309236
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateHierarchicalInstancedComponent, "CreateHierarchicalInstancedComponent" }, // 2952121164
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_CreateOptimizedInstancedComponent, "CreateOptimizedInstancedComponent" }, // 3328587799
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_GenerateLODChain, "GenerateLODChain" }, // 186798314
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_IsInFrustum, "IsInFrustum" }, // 2996530160
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_MeasureRenderTime, "MeasureRenderTime" }, // 514720095
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_OptimizeInstanceTransforms, "OptimizeInstanceTransforms" }, // 549356091
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_PerformBatchCulling, "PerformBatchCulling" }, // 2008824735
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_RemoveDuplicateInstances, "RemoveDuplicateInstances" }, // 1246461831
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ShouldCullInstance, "ShouldCullInstance" }, // 3337162036
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_SimplifyMesh, "SimplifyMesh" }, // 2106923269
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateCullingDescriptor, "ValidateCullingDescriptor" }, // 1268425529
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateInstancingDescriptor, "ValidateInstancingDescriptor" }, // 1528344576
		{ &Z_Construct_UFunction_UAuracronPCGLODSystemUtils_ValidateLODGenerationDescriptor, "ValidateLODGenerationDescriptor" }, // 811389826
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGLODSystemUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics::ClassParams = {
	&UAuracronPCGLODSystemUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGLODSystemUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils.OuterSingleton;
}
UAuracronPCGLODSystemUtils::UAuracronPCGLODSystemUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGLODSystemUtils);
UAuracronPCGLODSystemUtils::~UAuracronPCGLODSystemUtils() {}
// ********** End Class UAuracronPCGLODSystemUtils *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGLODGenerationMode_StaticEnum, TEXT("EAuracronPCGLODGenerationMode"), &Z_Registration_Info_UEnum_EAuracronPCGLODGenerationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1582331820U) },
		{ EAuracronPCGCullingMode_StaticEnum, TEXT("EAuracronPCGCullingMode"), &Z_Registration_Info_UEnum_EAuracronPCGCullingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2831073751U) },
		{ EAuracronPCGInstancingMode_StaticEnum, TEXT("EAuracronPCGInstancingMode"), &Z_Registration_Info_UEnum_EAuracronPCGInstancingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2656978254U) },
		{ EAuracronPCGPerformanceMetric_StaticEnum, TEXT("EAuracronPCGPerformanceMetric"), &Z_Registration_Info_UEnum_EAuracronPCGPerformanceMetric, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3188180533U) },
		{ EAuracronPCGMeshSimplificationAlgorithm_StaticEnum, TEXT("EAuracronPCGMeshSimplificationAlgorithm"), &Z_Registration_Info_UEnum_EAuracronPCGMeshSimplificationAlgorithm, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1959481846U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGLODGenerationDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics::NewStructOps, TEXT("AuracronPCGLODGenerationDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGLODGenerationDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGLODGenerationDescriptor), 339217262U) },
		{ FAuracronPCGCullingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics::NewStructOps, TEXT("AuracronPCGCullingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGCullingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGCullingDescriptor), 4262772543U) },
		{ FAuracronPCGInstancingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics::NewStructOps, TEXT("AuracronPCGInstancingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGInstancingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGInstancingDescriptor), 3409027822U) },
		{ FAuracronPCGPerformanceProfilingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics::NewStructOps, TEXT("AuracronPCGPerformanceProfilingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGPerformanceProfilingDescriptor), 3885280298U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGLODGeneratorSettings, UAuracronPCGLODGeneratorSettings::StaticClass, TEXT("UAuracronPCGLODGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGLODGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGLODGeneratorSettings), 1376559435U) },
		{ Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings, UAuracronPCGDistanceBasedCullerSettings::StaticClass, TEXT("UAuracronPCGDistanceBasedCullerSettings"), &Z_Registration_Info_UClass_UAuracronPCGDistanceBasedCullerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGDistanceBasedCullerSettings), 1924411981U) },
		{ Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings, UAuracronPCGInstancingOptimizerSettings::StaticClass, TEXT("UAuracronPCGInstancingOptimizerSettings"), &Z_Registration_Info_UClass_UAuracronPCGInstancingOptimizerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGInstancingOptimizerSettings), 2761847852U) },
		{ Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings, UAuracronPCGPerformanceProfilerSettings::StaticClass, TEXT("UAuracronPCGPerformanceProfilerSettings"), &Z_Registration_Info_UClass_UAuracronPCGPerformanceProfilerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPerformanceProfilerSettings), 2806371925U) },
		{ Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings, UAuracronPCGMeshSimplifierSettings::StaticClass, TEXT("UAuracronPCGMeshSimplifierSettings"), &Z_Registration_Info_UClass_UAuracronPCGMeshSimplifierSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGMeshSimplifierSettings), 2243257049U) },
		{ Z_Construct_UClass_UAuracronPCGLODSystemUtils, UAuracronPCGLODSystemUtils::StaticClass, TEXT("UAuracronPCGLODSystemUtils"), &Z_Registration_Info_UClass_UAuracronPCGLODSystemUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGLODSystemUtils), 4050410168U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_3391914001(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
