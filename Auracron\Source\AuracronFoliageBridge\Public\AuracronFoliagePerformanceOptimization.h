// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Performance Optimization Header
// Bridge 4.12: Foliage - Performance Optimization

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
#include "AuracronFoliageLOD.h"
#include "AuracronFoliageStreaming.h"

// UE5.6 Performance includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/World.h"
#include "RenderingThread.h"
#include "RHI.h"
#include "RHICommandList.h"

// Culling includes
#include "Engine/Engine.h"
#include "SceneView.h"
#include "ConvexVolume.h"
#include "SceneManagement.h"

// GPU includes
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "RenderGraphBuilder.h"
#include "RenderGraphUtils.h"

// Performance monitoring
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

#include "AuracronFoliagePerformanceOptimization.generated.h"

// Forward declarations
class UAuracronFoliagePerformanceOptimizationManager;

// =============================================================================
// PERFORMANCE OPTIMIZATION TYPES AND ENUMS
// =============================================================================

// Culling strategy
UENUM(BlueprintType)
enum class EAuracronCullingStrategy : uint8
{
    None                    UMETA(DisplayName = "None"),
    FrustumOnly             UMETA(DisplayName = "Frustum Only"),
    OcclusionOnly           UMETA(DisplayName = "Occlusion Only"),
    DistanceOnly            UMETA(DisplayName = "Distance Only"),
    Combined                UMETA(DisplayName = "Combined Culling"),
    Adaptive                UMETA(DisplayName = "Adaptive Culling"),
    GPU                     UMETA(DisplayName = "GPU Culling"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Batching strategy
UENUM(BlueprintType)
enum class EAuracronBatchingStrategy : uint8
{
    None                    UMETA(DisplayName = "None"),
    Spatial                 UMETA(DisplayName = "Spatial Batching"),
    Material                UMETA(DisplayName = "Material Batching"),
    LOD                     UMETA(DisplayName = "LOD Batching"),
    Hybrid                  UMETA(DisplayName = "Hybrid Batching"),
    Dynamic                 UMETA(DisplayName = "Dynamic Batching"),
    GPU                     UMETA(DisplayName = "GPU Batching"),
    Custom                  UMETA(DisplayName = "Custom")
};

// GPU instancing mode
UENUM(BlueprintType)
enum class EAuracronGPUInstancingMode : uint8
{
    Disabled                UMETA(DisplayName = "Disabled"),
    Standard                UMETA(DisplayName = "Standard Instancing"),
    Hierarchical            UMETA(DisplayName = "Hierarchical Instancing"),
    GPUDriven               UMETA(DisplayName = "GPU Driven Rendering"),
    Nanite                  UMETA(DisplayName = "Nanite Instancing"),
    Adaptive                UMETA(DisplayName = "Adaptive Instancing"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Performance tier
UENUM(BlueprintType)
enum class EAuracronPerformanceTier : uint8
{
    Low                     UMETA(DisplayName = "Low Performance"),
    Medium                  UMETA(DisplayName = "Medium Performance"),
    High                    UMETA(DisplayName = "High Performance"),
    Ultra                   UMETA(DisplayName = "Ultra Performance"),
    Adaptive                UMETA(DisplayName = "Adaptive Performance"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// PERFORMANCE OPTIMIZATION CONFIGURATION
// =============================================================================

/**
 * Foliage Performance Optimization Configuration
 * Configuration for maximum performance optimization
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronFoliagePerformanceOptimizationConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance System")
    bool bEnablePerformanceOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance System")
    EAuracronPerformanceTier PerformanceTier = EAuracronPerformanceTier::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    bool bEnableAdvancedCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    EAuracronCullingStrategy CullingStrategy = EAuracronCullingStrategy::Combined;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Frustum Culling")
    bool bEnableFrustumCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Frustum Culling")
    float FrustumCullingMargin = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Frustum Culling")
    bool bUseConservativeFrustum = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Occlusion Culling")
    bool bEnableOcclusionCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Occlusion Culling")
    float OcclusionCullingAccuracy = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Occlusion Culling")
    int32 OcclusionQueryBudget = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Occlusion Culling")
    bool bUseHierarchicalOcclusion = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    bool bEnableBatchingOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    EAuracronBatchingStrategy BatchingStrategy = EAuracronBatchingStrategy::Hybrid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    int32 MaxInstancesPerBatch = 2000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    float BatchingRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    bool bEnableDynamicBatching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Instancing")
    bool bEnableGPUInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Instancing")
    EAuracronGPUInstancingMode GPUInstancingMode = EAuracronGPUInstancingMode::GPUDriven;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Instancing")
    int32 MaxGPUInstances = 100000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Instancing")
    bool bUseGPUCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Instancing")
    bool bEnableNaniteSupport = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Optimization")
    bool bEnableMemoryOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Optimization")
    float MemoryBudgetMB = 2048.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Optimization")
    bool bEnableInstanceDataCompression = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Optimization")
    bool bEnableGeometryCompression = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threading")
    bool bEnableMultithreading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threading")
    int32 WorkerThreadCount = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threading")
    bool bEnableAsyncCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threading")
    bool bEnableAsyncBatching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptive Performance")
    bool bEnableAdaptivePerformance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptive Performance")
    float TargetFrameRate = 60.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptive Performance")
    float PerformanceThreshold = 0.85f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptive Performance")
    bool bAutoAdjustQuality = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Update Intervals")
    float CullingUpdateInterval = 0.033f; // 30 FPS

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Update Intervals")
    float BatchingUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Update Intervals")
    float PerformanceMonitoringInterval = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnablePerformanceDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowCullingStats = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowBatchingStats = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowGPUStats = false;

    FAuracronFoliagePerformanceOptimizationConfiguration()
    {
        bEnablePerformanceOptimization = true;
        PerformanceTier = EAuracronPerformanceTier::High;
        bEnableAdvancedCulling = true;
        CullingStrategy = EAuracronCullingStrategy::Combined;
        bEnableFrustumCulling = true;
        FrustumCullingMargin = 100.0f;
        bUseConservativeFrustum = true;
        bEnableOcclusionCulling = true;
        OcclusionCullingAccuracy = 0.8f;
        OcclusionQueryBudget = 1000;
        bUseHierarchicalOcclusion = true;
        bEnableBatchingOptimization = true;
        BatchingStrategy = EAuracronBatchingStrategy::Hybrid;
        MaxInstancesPerBatch = 2000;
        BatchingRadius = 500.0f;
        bEnableDynamicBatching = true;
        bEnableGPUInstancing = true;
        GPUInstancingMode = EAuracronGPUInstancingMode::GPUDriven;
        MaxGPUInstances = 100000;
        bUseGPUCulling = true;
        bEnableNaniteSupport = true;
        bEnableMemoryOptimization = true;
        MemoryBudgetMB = 2048.0f;
        bEnableInstanceDataCompression = true;
        bEnableGeometryCompression = true;
        bEnableMultithreading = true;
        WorkerThreadCount = 4;
        bEnableAsyncCulling = true;
        bEnableAsyncBatching = true;
        bEnableAdaptivePerformance = true;
        TargetFrameRate = 60.0f;
        PerformanceThreshold = 0.85f;
        bAutoAdjustQuality = true;
        CullingUpdateInterval = 0.033f;
        BatchingUpdateInterval = 0.1f;
        PerformanceMonitoringInterval = 1.0f;
        bEnablePerformanceDebug = false;
        bShowCullingStats = false;
        bShowBatchingStats = false;
        bShowGPUStats = false;
    }
};

// =============================================================================
// PERFORMANCE DATA STRUCTURES
// =============================================================================

/**
 * Culling Performance Data
 * Performance metrics for culling operations
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronCullingPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    int32 TotalInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    int32 VisibleInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    int32 FrustumCulledInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    int32 OcclusionCulledInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    int32 DistanceCulledInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    float CullingTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    float FrustumCullingTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    float OcclusionCullingTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Performance")
    int32 OcclusionQueriesUsed = 0;

    FAuracronCullingPerformanceData()
    {
        TotalInstances = 0;
        VisibleInstances = 0;
        FrustumCulledInstances = 0;
        OcclusionCulledInstances = 0;
        DistanceCulledInstances = 0;
        CullingTimeMs = 0.0f;
        FrustumCullingTimeMs = 0.0f;
        OcclusionCullingTimeMs = 0.0f;
        OcclusionQueriesUsed = 0;
    }
};

/**
 * Batching Performance Data
 * Performance metrics for batching operations
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronBatchingPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    int32 TotalBatches = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    int32 ActiveBatches = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    int32 OptimizedBatches = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    float AverageInstancesPerBatch = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    float BatchingTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    float BatchOptimizationTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    int32 DrawCalls = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    int32 InstancedDrawCalls = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching Performance")
    float MemoryUsageMB = 0.0f;

    FAuracronBatchingPerformanceData()
    {
        TotalBatches = 0;
        ActiveBatches = 0;
        OptimizedBatches = 0;
        AverageInstancesPerBatch = 0.0f;
        BatchingTimeMs = 0.0f;
        BatchOptimizationTimeMs = 0.0f;
        DrawCalls = 0;
        InstancedDrawCalls = 0;
        MemoryUsageMB = 0.0f;
    }
};

/**
 * GPU Performance Data
 * Performance metrics for GPU operations
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronGPUPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    int32 GPUInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    int32 GPUCulledInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    float GPUTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    float GPUCullingTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    float GPUMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    int32 VerticesRendered = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    int32 TrianglesRendered = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    bool bNaniteEnabled = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Performance")
    int32 NaniteTriangles = 0;

    FAuracronGPUPerformanceData()
    {
        GPUInstances = 0;
        GPUCulledInstances = 0;
        GPUTimeMs = 0.0f;
        GPUCullingTimeMs = 0.0f;
        GPUMemoryUsageMB = 0.0f;
        VerticesRendered = 0;
        TrianglesRendered = 0;
        bNaniteEnabled = false;
        NaniteTriangles = 0;
    }
};

/**
 * Overall Performance Data
 * Combined performance metrics
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronOverallPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    FAuracronCullingPerformanceData CullingData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    FAuracronBatchingPerformanceData BatchingData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    FAuracronGPUPerformanceData GPUData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    float TotalFrameTimeMs = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    float CurrentFPS = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    float AverageFPS = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    EAuracronPerformanceTier CurrentPerformanceTier = EAuracronPerformanceTier::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Overall Performance")
    bool bIsPerformanceOptimal = true;

    FAuracronOverallPerformanceData()
    {
        TotalFrameTimeMs = 0.0f;
        CurrentFPS = 0.0f;
        AverageFPS = 0.0f;
        TotalMemoryUsageMB = 0.0f;
        CurrentPerformanceTier = EAuracronPerformanceTier::High;
        bIsPerformanceOptimal = true;
    }
};

// =============================================================================
// FOLIAGE PERFORMANCE OPTIMIZATION MANAGER
// =============================================================================

/**
 * Foliage Performance Optimization Manager
 * Manager for maximum foliage performance optimization
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronFoliagePerformanceOptimizationManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    static UAuracronFoliagePerformanceOptimizationManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void Initialize(const FAuracronFoliagePerformanceOptimizationConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void SetConfiguration(const FAuracronFoliagePerformanceOptimizationConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    FAuracronFoliagePerformanceOptimizationConfiguration GetConfiguration() const;

    // Frustum culling
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void EnableFrustumCulling(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void UpdateFrustumCulling(const FVector& CameraLocation, const FVector& CameraDirection, float FOV);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    bool IsFrustumCullingEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    int32 GetFrustumCulledInstanceCount() const;

    // Occlusion culling
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void EnableOcclusionCulling(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void UpdateOcclusionCulling(const FVector& CameraLocation);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    bool IsOcclusionCullingEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    int32 GetOcclusionCulledInstanceCount() const;

    // Batching optimization
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void EnableBatchingOptimization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void OptimizeBatches();

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void RebuildBatches();

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    int32 GetOptimizedBatchCount() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    float GetAverageInstancesPerBatch() const;

    // GPU instancing
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void EnableGPUInstancing(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void SetGPUInstancingMode(EAuracronGPUInstancingMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    EAuracronGPUInstancingMode GetGPUInstancingMode() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    int32 GetGPUInstanceCount() const;

    // Memory optimization
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void OptimizeMemoryUsage();

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void CompressInstanceData();

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    float GetMemoryUsageMB() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    bool IsMemoryOptimal() const;

    // Adaptive performance
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void EnableAdaptivePerformance(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void UpdateAdaptivePerformance(float CurrentFPS);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void SetPerformanceTier(EAuracronPerformanceTier Tier);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    EAuracronPerformanceTier GetCurrentPerformanceTier() const;

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    FAuracronOverallPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    FAuracronCullingPerformanceData GetCullingPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    FAuracronBatchingPerformanceData GetBatchingPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    FAuracronGPUPerformanceData GetGPUPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void UpdatePerformanceMetrics();

    // Integration with other systems
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void IntegrateWithLODSystem(UAuracronFoliageLODManager* LODManager);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void IntegrateWithStreamingSystem(UAuracronFoliageStreamingManager* StreamingManager);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void SynchronizeWithInstancedSystem(UAuracronFoliageInstancedManager* InstancedManager);

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void DrawDebugPerformanceInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Optimization Manager")
    void LogPerformanceStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPerformanceTierChanged, EAuracronPerformanceTier, OldTier, EAuracronPerformanceTier, NewTier);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceOptimized, FAuracronOverallPerformanceData, PerformanceData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBatchOptimized, int32, BatchCount, float, OptimizationTimeMs);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMemoryOptimized, float, MemorySavedMB);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPerformanceTierChanged OnPerformanceTierChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPerformanceOptimized OnPerformanceOptimized;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBatchOptimized OnBatchOptimized;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnMemoryOptimized OnMemoryOptimized;

private:
    static UAuracronFoliagePerformanceOptimizationManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronFoliagePerformanceOptimizationConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Performance data
    FAuracronOverallPerformanceData PerformanceData;
    FAuracronCullingPerformanceData CullingData;
    FAuracronBatchingPerformanceData BatchingData;
    FAuracronGPUPerformanceData GPUData;

    // Integration with other systems
    UPROPERTY()
    TWeakObjectPtr<UAuracronFoliageLODManager> LODManager;

    UPROPERTY()
    TWeakObjectPtr<UAuracronFoliageStreamingManager> StreamingManager;

    UPROPERTY()
    TWeakObjectPtr<UAuracronFoliageInstancedManager> InstancedManager;

    // Update timers
    float LastCullingUpdate = 0.0f;
    float LastBatchingUpdate = 0.0f;
    float LastPerformanceUpdate = 0.0f;
    float LastAdaptiveUpdate = 0.0f;

    // Performance tracking
    TArray<float> FrameTimeHistory;
    float AverageFPS = 60.0f;
    int32 FrameCounter = 0;

    // Debug settings
    bool bDebugVisualizationEnabled = false;

    // Thread safety
    mutable FCriticalSection PerformanceLock;

    // Internal functions
    void ValidateConfiguration();
    void UpdateCullingInternal(float DeltaTime);
    void UpdateBatchingInternal(float DeltaTime);
    void UpdateGPUPerformanceInternal(float DeltaTime);
    void UpdateAdaptivePerformanceInternal(float CurrentFPS);
    void UpdatePerformanceDataInternal();
    bool PerformFrustumCulling(const FVector& CameraLocation, const FVector& CameraDirection, float FOV);
    bool PerformOcclusionCulling(const FVector& CameraLocation);
    void OptimizeBatchesInternal();
    void CompressInstanceDataInternal();
    void AdjustQualitySettings(EAuracronPerformanceTier TargetTier);
    float CalculateFrameTime() const;
    bool IsPerformanceWithinTarget() const;
    void CleanupPerformanceData();
};
