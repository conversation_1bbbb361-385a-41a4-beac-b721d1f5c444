// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAudioBridge.h"

#ifdef AURACRONAUDIOBRIDGE_AuracronAudioBridge_generated_h
#error "AuracronAudioBridge.generated.h already included, missing '#pragma once' in AuracronAudioBridge.h"
#endif
#define AURACRONAUDIOBRIDGE_AuracronAudioBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAudioComponent;
class UMetaSoundSource;
class USoundBase;
enum class EAuracronAudioLayer : uint8;
struct FAuracronAudioConfiguration;

// ********** Begin ScriptStruct FAuracronAudioConfiguration ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_78_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAudioConfiguration;
// ********** End ScriptStruct FAuracronAudioConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronDynamicMusicConfiguration ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_155_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDynamicMusicConfiguration;
// ********** End ScriptStruct FAuracronDynamicMusicConfiguration **********************************

// ********** Begin ScriptStruct FAuracronSFXConfiguration *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_212_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSFXConfiguration;
// ********** End ScriptStruct FAuracronSFXConfiguration *******************************************

// ********** Begin Delegate FOnMusicChanged *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_543_DELEGATE \
static void FOnMusicChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMusicChanged, const FString& OldMusic, const FString& NewMusic);


// ********** End Delegate FOnMusicChanged *********************************************************

// ********** Begin Delegate FOnAudioLayerChanged **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_548_DELEGATE \
static void FOnAudioLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnAudioLayerChanged, EAuracronAudioLayer OldLayer, EAuracronAudioLayer NewLayer);


// ********** End Delegate FOnAudioLayerChanged ****************************************************

// ********** Begin Class UAuracronAudioBridge *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_262_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnAudioComponentFinished); \
	DECLARE_FUNCTION(execOnRep_MusicIntensity); \
	DECLARE_FUNCTION(execOnRep_CurrentMusic); \
	DECLARE_FUNCTION(execLoadAudioSettings); \
	DECLARE_FUNCTION(execSaveAudioSettings); \
	DECLARE_FUNCTION(execApplyAudioConfiguration); \
	DECLARE_FUNCTION(execApplyFrequencyFilter); \
	DECLARE_FUNCTION(execApplyDelayEffect); \
	DECLARE_FUNCTION(execApplyReverbEffect); \
	DECLARE_FUNCTION(execPlayMovementSound); \
	DECLARE_FUNCTION(execPlayAbilitySound); \
	DECLARE_FUNCTION(execPlayChampionVoice); \
	DECLARE_FUNCTION(execPlayRealmAmbientSound); \
	DECLARE_FUNCTION(execApplyRealmAudioEffects); \
	DECLARE_FUNCTION(execChangeToRealmAudio); \
	DECLARE_FUNCTION(execSetMusicIntensity); \
	DECLARE_FUNCTION(execTransitionToMusic); \
	DECLARE_FUNCTION(execStopCurrentMusic); \
	DECLARE_FUNCTION(execPlayDynamicMusic); \
	DECLARE_FUNCTION(execResumeAllSounds); \
	DECLARE_FUNCTION(execPauseAllSounds); \
	DECLARE_FUNCTION(execStopSound); \
	DECLARE_FUNCTION(execPlayMetaSound); \
	DECLARE_FUNCTION(execPlaySound3D);


AURACRONAUDIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAudioBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_262_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronAudioBridge(); \
	friend struct Z_Construct_UClass_UAuracronAudioBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONAUDIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAudioBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronAudioBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronAudioBridge"), Z_Construct_UClass_UAuracronAudioBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronAudioBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		AudioConfiguration=NETFIELD_REP_START, \
		CurrentMusicType, \
		CurrentMusicIntensity, \
		NETFIELD_REP_END=CurrentMusicIntensity	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_262_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronAudioBridge(UAuracronAudioBridge&&) = delete; \
	UAuracronAudioBridge(const UAuracronAudioBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronAudioBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronAudioBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronAudioBridge) \
	NO_API virtual ~UAuracronAudioBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_259_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_262_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_262_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_262_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h_262_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronAudioBridge;

// ********** End Class UAuracronAudioBridge *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h

// ********** Begin Enum EAuracronAudioType ********************************************************
#define FOREACH_ENUM_EAURACRONAUDIOTYPE(op) \
	op(EAuracronAudioType::None) \
	op(EAuracronAudioType::Music) \
	op(EAuracronAudioType::SFX) \
	op(EAuracronAudioType::Voice) \
	op(EAuracronAudioType::Ambient) \
	op(EAuracronAudioType::UI) \
	op(EAuracronAudioType::Ability) \
	op(EAuracronAudioType::Champion) \
	op(EAuracronAudioType::Realm) \
	op(EAuracronAudioType::Combat) \
	op(EAuracronAudioType::Notification) 

enum class EAuracronAudioType : uint8;
template<> struct TIsUEnumClass<EAuracronAudioType> { enum { Value = true }; };
template<> AURACRONAUDIOBRIDGE_API UEnum* StaticEnum<EAuracronAudioType>();
// ********** End Enum EAuracronAudioType **********************************************************

// ********** Begin Enum EAuracronAudioLayer *******************************************************
#define FOREACH_ENUM_EAURACRONAUDIOLAYER(op) \
	op(EAuracronAudioLayer::Surface) \
	op(EAuracronAudioLayer::Sky) \
	op(EAuracronAudioLayer::Underground) \
	op(EAuracronAudioLayer::All) 

enum class EAuracronAudioLayer : uint8;
template<> struct TIsUEnumClass<EAuracronAudioLayer> { enum { Value = true }; };
template<> AURACRONAUDIOBRIDGE_API UEnum* StaticEnum<EAuracronAudioLayer>();
// ********** End Enum EAuracronAudioLayer *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
