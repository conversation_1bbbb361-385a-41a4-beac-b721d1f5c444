// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGLandscapeIntegration.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGLandscapeIntegration_generated_h
#error "AuracronPCGLandscapeIntegration.generated.h already included, missing '#pragma once' in AuracronPCGLandscapeIntegration.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGLandscapeIntegration_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class ALandscape;
class UWorld;
enum class EAuracronPCGHeightModificationMode : uint8;
struct FAuracronPCGErosionDescriptor;

// ********** Begin ScriptStruct FAuracronPCGLandscapeSamplingDescriptor ***************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_133_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGLandscapeSamplingDescriptor;
// ********** End ScriptStruct FAuracronPCGLandscapeSamplingDescriptor *****************************

// ********** Begin ScriptStruct FAuracronPCGErosionDescriptor *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_210_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGErosionDescriptor;
// ********** End ScriptStruct FAuracronPCGErosionDescriptor ***************************************

// ********** Begin Class UAuracronPCGAdvancedLandscapeSamplerSettings *****************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_286_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedLandscapeSamplerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedLandscapeSamplerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedLandscapeSamplerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_286_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedLandscapeSamplerSettings(UAuracronPCGAdvancedLandscapeSamplerSettings&&) = delete; \
	UAuracronPCGAdvancedLandscapeSamplerSettings(const UAuracronPCGAdvancedLandscapeSamplerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedLandscapeSamplerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedLandscapeSamplerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedLandscapeSamplerSettings) \
	NO_API virtual ~UAuracronPCGAdvancedLandscapeSamplerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_283_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_286_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_286_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_286_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedLandscapeSamplerSettings;

// ********** End Class UAuracronPCGAdvancedLandscapeSamplerSettings *******************************

// ********** Begin Class UAuracronPCGLandscapeHeightModifierSettings ******************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_358_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGLandscapeHeightModifierSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGLandscapeHeightModifierSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGLandscapeHeightModifierSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_358_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGLandscapeHeightModifierSettings(UAuracronPCGLandscapeHeightModifierSettings&&) = delete; \
	UAuracronPCGLandscapeHeightModifierSettings(const UAuracronPCGLandscapeHeightModifierSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGLandscapeHeightModifierSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGLandscapeHeightModifierSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGLandscapeHeightModifierSettings) \
	NO_API virtual ~UAuracronPCGLandscapeHeightModifierSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_355_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_358_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_358_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_358_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGLandscapeHeightModifierSettings;

// ********** End Class UAuracronPCGLandscapeHeightModifierSettings ********************************

// ********** Begin Class UAuracronPCGLandscapeLayerPainterSettings ********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_446_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGLandscapeLayerPainterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGLandscapeLayerPainterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGLandscapeLayerPainterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_446_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGLandscapeLayerPainterSettings(UAuracronPCGLandscapeLayerPainterSettings&&) = delete; \
	UAuracronPCGLandscapeLayerPainterSettings(const UAuracronPCGLandscapeLayerPainterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGLandscapeLayerPainterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGLandscapeLayerPainterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGLandscapeLayerPainterSettings) \
	NO_API virtual ~UAuracronPCGLandscapeLayerPainterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_443_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_446_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_446_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_446_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGLandscapeLayerPainterSettings;

// ********** End Class UAuracronPCGLandscapeLayerPainterSettings **********************************

// ********** Begin Class UAuracronPCGLandscapeErosionSimulatorSettings ****************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_535_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGLandscapeErosionSimulatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGLandscapeErosionSimulatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGLandscapeErosionSimulatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_535_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGLandscapeErosionSimulatorSettings(UAuracronPCGLandscapeErosionSimulatorSettings&&) = delete; \
	UAuracronPCGLandscapeErosionSimulatorSettings(const UAuracronPCGLandscapeErosionSimulatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGLandscapeErosionSimulatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGLandscapeErosionSimulatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGLandscapeErosionSimulatorSettings) \
	NO_API virtual ~UAuracronPCGLandscapeErosionSimulatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_532_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_535_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_535_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_535_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGLandscapeErosionSimulatorSettings;

// ********** End Class UAuracronPCGLandscapeErosionSimulatorSettings ******************************

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_599_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetLandscapeLayerNames); \
	DECLARE_FUNCTION(execGetLandscapeBounds); \
	DECLARE_FUNCTION(execLandscapeCoordinateToWorldLocation); \
	DECLARE_FUNCTION(execWorldLocationToLandscapeCoordinate); \
	DECLARE_FUNCTION(execSimulateErosion); \
	DECLARE_FUNCTION(execPaintLandscapeLayerBatch); \
	DECLARE_FUNCTION(execPaintLandscapeLayer); \
	DECLARE_FUNCTION(execModifyLandscapeHeightBatch); \
	DECLARE_FUNCTION(execModifyLandscapeHeight); \
	DECLARE_FUNCTION(execSampleAllLandscapeLayers); \
	DECLARE_FUNCTION(execSampleLandscapeLayer); \
	DECLARE_FUNCTION(execSampleLandscapeSlope); \
	DECLARE_FUNCTION(execSampleLandscapeNormal); \
	DECLARE_FUNCTION(execSampleLandscapeHeight); \
	DECLARE_FUNCTION(execIsPointOnLandscape); \
	DECLARE_FUNCTION(execGetAllLandscapesInWorld); \
	DECLARE_FUNCTION(execFindLandscapeInWorld);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_599_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGLandscapeIntegrationUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGLandscapeIntegrationUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGLandscapeIntegrationUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_599_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGLandscapeIntegrationUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGLandscapeIntegrationUtils(UAuracronPCGLandscapeIntegrationUtils&&) = delete; \
	UAuracronPCGLandscapeIntegrationUtils(const UAuracronPCGLandscapeIntegrationUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGLandscapeIntegrationUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGLandscapeIntegrationUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGLandscapeIntegrationUtils) \
	NO_API virtual ~UAuracronPCGLandscapeIntegrationUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_596_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_599_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_599_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_599_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h_599_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGLandscapeIntegrationUtils;

// ********** End Class UAuracronPCGLandscapeIntegrationUtils **************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h

// ********** Begin Enum EAuracronPCGLandscapeSamplingMode *****************************************
#define FOREACH_ENUM_EAURACRONPCGLANDSCAPESAMPLINGMODE(op) \
	op(EAuracronPCGLandscapeSamplingMode::Height) \
	op(EAuracronPCGLandscapeSamplingMode::HeightAndNormal) \
	op(EAuracronPCGLandscapeSamplingMode::HeightAndLayers) \
	op(EAuracronPCGLandscapeSamplingMode::Complete) \
	op(EAuracronPCGLandscapeSamplingMode::LayersOnly) \
	op(EAuracronPCGLandscapeSamplingMode::NormalOnly) \
	op(EAuracronPCGLandscapeSamplingMode::SlopeOnly) \
	op(EAuracronPCGLandscapeSamplingMode::CurvatureOnly) 

enum class EAuracronPCGLandscapeSamplingMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGLandscapeSamplingMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLandscapeSamplingMode>();
// ********** End Enum EAuracronPCGLandscapeSamplingMode *******************************************

// ********** Begin Enum EAuracronPCGLandscapeBlendMode ********************************************
#define FOREACH_ENUM_EAURACRONPCGLANDSCAPEBLENDMODE(op) \
	op(EAuracronPCGLandscapeBlendMode::Replace) \
	op(EAuracronPCGLandscapeBlendMode::Add) \
	op(EAuracronPCGLandscapeBlendMode::Subtract) \
	op(EAuracronPCGLandscapeBlendMode::Multiply) \
	op(EAuracronPCGLandscapeBlendMode::Screen) \
	op(EAuracronPCGLandscapeBlendMode::Overlay) \
	op(EAuracronPCGLandscapeBlendMode::SoftLight) \
	op(EAuracronPCGLandscapeBlendMode::HardLight) \
	op(EAuracronPCGLandscapeBlendMode::ColorDodge) \
	op(EAuracronPCGLandscapeBlendMode::ColorBurn) \
	op(EAuracronPCGLandscapeBlendMode::Darken) \
	op(EAuracronPCGLandscapeBlendMode::Lighten) \
	op(EAuracronPCGLandscapeBlendMode::Difference) \
	op(EAuracronPCGLandscapeBlendMode::Exclusion) 

enum class EAuracronPCGLandscapeBlendMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGLandscapeBlendMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLandscapeBlendMode>();
// ********** End Enum EAuracronPCGLandscapeBlendMode **********************************************

// ********** Begin Enum EAuracronPCGErosionType ***************************************************
#define FOREACH_ENUM_EAURACRONPCGEROSIONTYPE(op) \
	op(EAuracronPCGErosionType::Thermal) \
	op(EAuracronPCGErosionType::Hydraulic) \
	op(EAuracronPCGErosionType::Wind) \
	op(EAuracronPCGErosionType::Chemical) \
	op(EAuracronPCGErosionType::Glacial) \
	op(EAuracronPCGErosionType::Combined) \
	op(EAuracronPCGErosionType::Custom) 

enum class EAuracronPCGErosionType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGErosionType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGErosionType>();
// ********** End Enum EAuracronPCGErosionType *****************************************************

// ********** Begin Enum EAuracronPCGLayerPaintMode ************************************************
#define FOREACH_ENUM_EAURACRONPCGLAYERPAINTMODE(op) \
	op(EAuracronPCGLayerPaintMode::Paint) \
	op(EAuracronPCGLayerPaintMode::Erase) \
	op(EAuracronPCGLayerPaintMode::Smooth) \
	op(EAuracronPCGLayerPaintMode::Flatten) \
	op(EAuracronPCGLayerPaintMode::Noise) \
	op(EAuracronPCGLayerPaintMode::Gradient) \
	op(EAuracronPCGLayerPaintMode::Pattern) \
	op(EAuracronPCGLayerPaintMode::Procedural) 

enum class EAuracronPCGLayerPaintMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGLayerPaintMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLayerPaintMode>();
// ********** End Enum EAuracronPCGLayerPaintMode **************************************************

// ********** Begin Enum EAuracronPCGHeightModificationMode ****************************************
#define FOREACH_ENUM_EAURACRONPCGHEIGHTMODIFICATIONMODE(op) \
	op(EAuracronPCGHeightModificationMode::Absolute) \
	op(EAuracronPCGHeightModificationMode::Relative) \
	op(EAuracronPCGHeightModificationMode::Additive) \
	op(EAuracronPCGHeightModificationMode::Subtractive) \
	op(EAuracronPCGHeightModificationMode::Smooth) \
	op(EAuracronPCGHeightModificationMode::Flatten) \
	op(EAuracronPCGHeightModificationMode::Noise) \
	op(EAuracronPCGHeightModificationMode::Terrace) 

enum class EAuracronPCGHeightModificationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGHeightModificationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGHeightModificationMode>();
// ********** End Enum EAuracronPCGHeightModificationMode ******************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
