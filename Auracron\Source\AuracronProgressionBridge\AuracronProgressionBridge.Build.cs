// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Progressão Bridge Build Configuration
using UnrealBuildTool;
public class AuracronProgressionBridge : ModuleRules
{
    public AuracronProgressionBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicIncludePaths.AddRange(
            new string[] {
                // ... add public include paths required here ...
            }
        );
        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph","OnlineSubsystemUtils","EOSShared",
                "Sockets",
                "Networking",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "UnrealEd",
                "PropertyEditor",
                "KismetCompiler","BlueprintGraph",
                "Kismet",
                "ToolMenus",
                "ApplicationCore",
                "RenderCore",
                "RHI","Json","AudioMixer",
                "SignalProcessing","SessionServices","DesktopPlatform",
                "LauncherServices",
                "Localization","ICU","PlatformCrypto",
                "RSA"
            }
        );
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.InShippingBuildsOnly;
            bUseUnity = true;
        }
        // Enable additional features for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_PROGRESSION_DEBUG=1");
            PublicDefinitions.Add("AURACRON_PROGRESSION_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_PROGRESSION_DEBUG=0");
            PublicDefinitions.Add("AURACRON_PROGRESSION_PROFILING=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=1");
            // Mobile-specific optimizations
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=0");
        }
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_EOS=1");
        PublicDefinitions.Add("WITH_ONLINE_SUBSYSTEM_EOS=1");
        PublicDefinitions.Add("WITH_FIREBASE=1");
        PublicDefinitions.Add("WITH_ANALYTICS=1");
        PublicDefinitions.Add("WITH_TELEMETRY=1");
        PublicDefinitions.Add("WITH_GAMEPLAY_ABILITY_SYSTEM=1");
        PublicDefinitions.Add("WITH_ENHANCED_INPUT=1");
        PublicDefinitions.Add("WITH_COMMON_UI=1");
        // Progression features
        PublicDefinitions.Add("AURACRON_ACCOUNT_PROGRESSION=1");
        PublicDefinitions.Add("AURACRON_CHAMPION_MASTERY=1");
        PublicDefinitions.Add("AURACRON_REALM_MASTERY=1");
        PublicDefinitions.Add("AURACRON_SEASONAL_PROGRESSION=1");
        PublicDefinitions.Add("AURACRON_BATTLE_PASS=1");
        PublicDefinitions.Add("AURACRON_ACHIEVEMENTS=1");
        PublicDefinitions.Add("AURACRON_LEADERBOARDS=1");
        // Security and data protection
        PublicDefinitions.Add("AURACRON_SECURE_PROGRESSION=1");
        PublicDefinitions.Add("AURACRON_ENCRYPTED_SAVES=1");
        PublicDefinitions.Add("AURACRON_CLOUD_SYNC=1");
        PublicDefinitions.Add("AURACRON_BACKUP_SYSTEM=1");
        // Cross-platform support
        PublicDefinitions.Add("AURACRON_CROSS_PLATFORM=1");
        PublicDefinitions.Add("AURACRON_CROSS_PROGRESSION=1");
        PublicDefinitions.Add("AURACRON_CLOUD_SAVE=1");
        // Performance optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_PROGRESSION=1");
            PublicDefinitions.Add("AURACRON_CACHE_PROGRESSION=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_PROGRESSION=0");
            PublicDefinitions.Add("AURACRON_CACHE_PROGRESSION=0");
        }
        // Monetization integration
        PublicDefinitions.Add("AURACRON_MONETIZATION_INTEGRATION=1");
        PublicDefinitions.Add("AURACRON_PREMIUM_REWARDS=1");
        PublicDefinitions.Add("AURACRON_SUBSCRIPTION_SYSTEM=1");
    }
}
