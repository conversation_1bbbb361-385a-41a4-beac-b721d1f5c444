// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Attribute System Advanced Nodes Implementation
// Bridge 2.5: PCG Framework - Attribute System

#include "AuracronPCGAttributeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"

// Engine includes
#include "Async/ParallelFor.h"

// =============================================================================
// ATTRIBUTE VALIDATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeValidatorSettings::UAuracronPCGAttributeValidatorSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Validator");
    NodeMetadata.NodeDescription = TEXT("Validates attributes against specified rules and constraints");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Validate"));
    NodeMetadata.Tags.Add(TEXT("Quality"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.4f, 0.2f);
}

void UAuracronPCGAttributeValidatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeValidatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& ValidPin = OutputPins.Emplace_GetRef();
    ValidPin.Label = TEXT("Valid");
    ValidPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;

    if (bOutputValidationResults)
    {
        FPCGPinProperties& InvalidPin = OutputPins.Emplace_GetRef();
        InvalidPin.Label = TEXT("Invalid");
        InvalidPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
        InvalidPin.bAdvancedPin = true;

        FPCGPinProperties& ResultsPin = OutputPins.Emplace_GetRef();
        ResultsPin.Label = TEXT("Results");
        ResultsPin.AllowedTypes = EPCGDataType::AttributeSet;
        ResultsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAttributeValidatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                             FPCGDataCollection& OutputData, 
                                                                             const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGAttributeValidatorSettings* Settings = GetTypedSettings<UAuracronPCGAttributeValidatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Validator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 ValidEntries = 0;
        int32 InvalidEntries = 0;
        TArray<FString> AllValidationErrors;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData || !InputPointData->Metadata)
            {
                continue;
            }

            UPCGPointData* ValidData = NewObject<UPCGPointData>();
            UPCGPointData* InvalidData = Settings->bOutputValidationResults ? NewObject<UPCGPointData>() : nullptr;

            TArray<FString> ValidationErrors;
            int32 ValidCount = 0;
            int32 InvalidCount = 0;

            if (ProcessValidation(InputPointData, ValidData, InvalidData, ValidationErrors, ValidCount, InvalidCount, Settings))
            {
                TotalProcessed += InputPointData->GetPoints().Num();
                ValidEntries += ValidCount;
                InvalidEntries += InvalidCount;
                AllValidationErrors.Append(ValidationErrors);

                // Add valid data to output
                FPCGTaggedData& ValidTaggedData = OutputData.TaggedData.Emplace_GetRef();
                ValidTaggedData.Data = ValidData;
                ValidTaggedData.Pin = TEXT("Valid");
                ValidTaggedData.Tags = TaggedData.Tags;

                // Add invalid data if requested
                if (InvalidData && Settings->bOutputValidationResults)
                {
                    FPCGTaggedData& InvalidTaggedData = OutputData.TaggedData.Emplace_GetRef();
                    InvalidTaggedData.Data = InvalidData;
                    InvalidTaggedData.Pin = TEXT("Invalid");
                    InvalidTaggedData.Tags = TaggedData.Tags;
                }
            }
        }

        // Create validation results attribute set
        if (Settings->bOutputValidationResults)
        {
            UPCGAttributeSet* ResultsAttributeSet = CreateValidationResultsAttributeSet(AllValidationErrors, ValidEntries, InvalidEntries);
            if (ResultsAttributeSet)
            {
                FPCGTaggedData& ResultsTaggedData = OutputData.TaggedData.Emplace_GetRef();
                ResultsTaggedData.Data = ResultsAttributeSet;
                ResultsTaggedData.Pin = TEXT("Results");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        if (Settings->bLogValidationErrors && AllValidationErrors.Num() > 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Validation errors found: %s"), 
                                      *FString::Join(AllValidationErrors, TEXT("; ")));
        }

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Validator processed %d points (%d valid, %d invalid)"), 
                                  TotalProcessed, ValidEntries, InvalidEntries);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Validator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAttributeValidatorElement::ProcessValidation(const UPCGPointData* InputData, 
                                                              UPCGPointData* ValidData, 
                                                              UPCGPointData* InvalidData,
                                                              TArray<FString>& ValidationErrors,
                                                              int32& ValidCount,
                                                              int32& InvalidCount,
                                                              const UAuracronPCGAttributeValidatorSettings* Settings) const
{
    if (!InputData || !ValidData)
    {
        return false;
    }

    // Initialize output data
    ValidData->InitializeFromData(InputData);
    if (InvalidData)
    {
        InvalidData->InitializeFromData(InputData);
    }

    const TArray<FPCGPoint>& InputPoints = InputData->GetPoints();
    TArray<FPCGPoint>& ValidPoints = ValidData->GetMutablePoints();
    TArray<FPCGPoint>* InvalidPoints = InvalidData ? &InvalidData->GetMutablePoints() : nullptr;

    ValidPoints.Empty();
    if (InvalidPoints)
    {
        InvalidPoints->Empty();
    }

    ValidCount = 0;
    InvalidCount = 0;

    // Validate each point
    for (int32 i = 0; i < InputPoints.Num(); i++)
    {
        const FPCGPoint& Point = InputPoints[i];
        TArray<FString> PointErrors;
        bool bIsValid = ValidatePoint(Point, InputData->Metadata, PointErrors, Settings);

        if (bIsValid)
        {
            ValidPoints.Add(Point);
            ValidCount++;
        }
        else
        {
            if (InvalidPoints)
            {
                InvalidPoints->Add(Point);
            }
            InvalidCount++;
            ValidationErrors.Append(PointErrors);

            if (Settings->bStopOnFirstError)
            {
                break;
            }
        }
    }

    // Remove invalid entries if requested
    if (Settings->bRemoveInvalidEntries && InvalidCount > 0)
    {
        // Already handled by separating valid and invalid points
    }

    return true;
}

bool FAuracronPCGAttributeValidatorElement::ValidatePoint(const FPCGPoint& Point, 
                                                          const UPCGMetadata* Metadata,
                                                          TArray<FString>& PointErrors,
                                                          const UAuracronPCGAttributeValidatorSettings* Settings) const
{
    bool bIsValid = true;
    PointErrors.Empty();

    if (!Metadata)
    {
        PointErrors.Add(TEXT("No metadata available"));
        return false;
    }

    // Validate against each rule
    for (const FAuracronPCGAttributeDescriptor& Rule : Settings->ValidationRules)
    {
        TArray<FString> RuleErrors;
        if (!UAuracronPCGAttributeSystemUtils::ValidateAttribute(Metadata, Rule, RuleErrors))
        {
            bIsValid = false;
            PointErrors.Append(RuleErrors);
        }
    }

    // Custom validation
    if (Settings->bUseCustomValidation && !Settings->CustomValidationExpression.IsEmpty())
    {
        // Robust custom validation with expression evaluation using UE5.6 capabilities
        if (!EvaluateCustomValidation(Point, Metadata, Settings->CustomValidationExpression))
        {
            bIsValid = false;
            PointErrors.Add(FString::Printf(TEXT("Custom validation failed: %s"), *Settings->CustomValidationExpression));
        }
    }

    return bIsValid;
}

bool FAuracronPCGAttributeValidatorElement::EvaluateCustomValidation(const FPCGPoint& Point, 
                                                                     const UPCGMetadata* Metadata,
                                                                     const FString& Expression) const
{
    // Robust custom validation with expression evaluation using UE5.6 capabilities
    if (Expression.IsEmpty())
    {
        return true; // Empty expression is always valid
    }
    
    // Parse and evaluate the expression
    FString ProcessedExpression = Expression;
    
    // Replace common variable placeholders with actual values from point and metadata
    TMap<FString, FString> VariableReplacements;
    
    // Point transform properties
    const FTransform& Transform = Point.Transform;
    const FVector Location = Transform.GetLocation();
    const FVector Scale = Transform.GetScale3D();
    const FRotator Rotation = Transform.Rotator();
    
    // Basic transform variables
    VariableReplacements.Add(TEXT("Position.X"), FString::Printf(TEXT("%.6f"), Location.X));
    VariableReplacements.Add(TEXT("Position.Y"), FString::Printf(TEXT("%.6f"), Location.Y));
    VariableReplacements.Add(TEXT("Position.Z"), FString::Printf(TEXT("%.6f"), Location.Z));
    VariableReplacements.Add(TEXT("Scale.X"), FString::Printf(TEXT("%.6f"), Scale.X));
    VariableReplacements.Add(TEXT("Scale.Y"), FString::Printf(TEXT("%.6f"), Scale.Y));
    VariableReplacements.Add(TEXT("Scale.Z"), FString::Printf(TEXT("%.6f"), Scale.Z));
    VariableReplacements.Add(TEXT("Rotation.Pitch"), FString::Printf(TEXT("%.6f"), Rotation.Pitch));
    VariableReplacements.Add(TEXT("Rotation.Yaw"), FString::Printf(TEXT("%.6f"), Rotation.Yaw));
    VariableReplacements.Add(TEXT("Rotation.Roll"), FString::Printf(TEXT("%.6f"), Rotation.Roll));
    
    // Point properties
    VariableReplacements.Add(TEXT("Density"), FString::Printf(TEXT("%.6f"), Point.Density));
    VariableReplacements.Add(TEXT("Steepness"), FString::Printf(TEXT("%.6f"), Point.Steepness));
    VariableReplacements.Add(TEXT("Seed"), FString::Printf(TEXT("%d"), Point.Seed));
    VariableReplacements.Add(TEXT("Color.R"), FString::Printf(TEXT("%.6f"), Point.Color.R));
    VariableReplacements.Add(TEXT("Color.G"), FString::Printf(TEXT("%.6f"), Point.Color.G));
    VariableReplacements.Add(TEXT("Color.B"), FString::Printf(TEXT("%.6f"), Point.Color.B));
    VariableReplacements.Add(TEXT("Color.A"), FString::Printf(TEXT("%.6f"), Point.Color.A));
    
    // Metadata attributes if available
    if (Metadata)
    {
        const TArray<FName> AttributeNames = Metadata->GetAttributeNames();
        for (const FName& AttributeName : AttributeNames)
        {
            const FPCGMetadataAttributeBase* AttributeBase = Metadata->GetConstAttribute(AttributeName);
            if (AttributeBase)
            {
                FString AttributeValue;
                // Get attribute value based on type
                if (const FPCGMetadataAttribute<float>* FloatAttr = static_cast<const FPCGMetadataAttribute<float>*>(AttributeBase))
                {
                    float Value = FloatAttr->GetValueFromItemKey(Point.MetadataEntry);
                    AttributeValue = FString::Printf(TEXT("%.6f"), Value);
                }
                else if (const FPCGMetadataAttribute<int32>* IntAttr = static_cast<const FPCGMetadataAttribute<int32>*>(AttributeBase))
                {
                    int32 Value = IntAttr->GetValueFromItemKey(Point.MetadataEntry);
                    AttributeValue = FString::Printf(TEXT("%d"), Value);
                }
                else if (const FPCGMetadataAttribute<FString>* StringAttr = static_cast<const FPCGMetadataAttribute<FString>*>(AttributeBase))
                {
                    AttributeValue = StringAttr->GetValueFromItemKey(Point.MetadataEntry);
                }
                else if (const FPCGMetadataAttribute<FVector>* VectorAttr = static_cast<const FPCGMetadataAttribute<FVector>*>(AttributeBase))
                {
                    FVector Value = VectorAttr->GetValueFromItemKey(Point.MetadataEntry);
                    VariableReplacements.Add(FString::Printf(TEXT("%s.X"), *AttributeName.ToString()), FString::Printf(TEXT("%.6f"), Value.X));
                    VariableReplacements.Add(FString::Printf(TEXT("%s.Y"), *AttributeName.ToString()), FString::Printf(TEXT("%.6f"), Value.Y));
                    VariableReplacements.Add(FString::Printf(TEXT("%s.Z"), *AttributeName.ToString()), FString::Printf(TEXT("%.6f"), Value.Z));
                    AttributeValue = FString::Printf(TEXT("(%.6f,%.6f,%.6f)"), Value.X, Value.Y, Value.Z);
                }
                
                if (!AttributeValue.IsEmpty())
                {
                    VariableReplacements.Add(AttributeName.ToString(), AttributeValue);
                }
            }
        }
    }
    VariableReplacements.Add(TEXT("Rotation.Pitch"), FString::Printf(TEXT("%.6f"), Rotation.Pitch));
    VariableReplacements.Add(TEXT("Rotation.Yaw"), FString::Printf(TEXT("%.6f"), Rotation.Yaw));
    VariableReplacements.Add(TEXT("Rotation.Roll"), FString::Printf(TEXT("%.6f"), Rotation.Roll));
    
    // Point properties
    VariableReplacements.Add(TEXT("Density"), FString::Printf(TEXT("%.6f"), Point.Density));
    VariableReplacements.Add(TEXT("Steepness"), FString::Printf(TEXT("%.6f"), Point.Steepness));
    VariableReplacements.Add(TEXT("Seed"), FString::Printf(TEXT("%d"), Point.Seed));
    VariableReplacements.Add(TEXT("Color.R"), FString::Printf(TEXT("%.6f"), Point.Color.R));
    VariableReplacements.Add(TEXT("Color.G"), FString::Printf(TEXT("%.6f"), Point.Color.G));
    VariableReplacements.Add(TEXT("Color.B"), FString::Printf(TEXT("%.6f"), Point.Color.B));
    VariableReplacements.Add(TEXT("Color.A"), FString::Printf(TEXT("%.6f"), Point.Color.A));
    
    // Metadata attributes if available
    if (Metadata && Point.MetadataEntry != PCGInvalidEntryKey)
    {
        const TMap<FName, FPCGMetadataAttributeBase*>& Attributes = Metadata->GetAttributes();
        for (const auto& AttributePair : Attributes)
        {
            const FName& AttributeName = AttributePair.Key;
            const FPCGMetadataAttributeBase* Attribute = AttributePair.Value;
            
            if (Attribute)
            {
                FString AttributeValue;
                
                // Handle different attribute types for string conversion
                if (const FPCGMetadataAttribute<float>* FloatAttr = static_cast<const FPCGMetadataAttribute<float>*>(Attribute))
                {
                    AttributeValue = FString::Printf(TEXT("%.6f"), FloatAttr->GetValueFromItemKey(Point.MetadataEntry));
                }
                else if (const FPCGMetadataAttribute<double>* DoubleAttr = static_cast<const FPCGMetadataAttribute<double>*>(Attribute))
                {
                    AttributeValue = FString::Printf(TEXT("%.6f"), DoubleAttr->GetValueFromItemKey(Point.MetadataEntry));
                }
                else if (const FPCGMetadataAttribute<int32>* IntAttr = static_cast<const FPCGMetadataAttribute<int32>*>(Attribute))
                {
                    AttributeValue = FString::Printf(TEXT("%d"), IntAttr->GetValueFromItemKey(Point.MetadataEntry));
                }
                else if (const FPCGMetadataAttribute<bool>* BoolAttr = static_cast<const FPCGMetadataAttribute<bool>*>(Attribute))
                {
                    AttributeValue = BoolAttr->GetValueFromItemKey(Point.MetadataEntry) ? TEXT("1.0") : TEXT("0.0");
                }
                else if (const FPCGMetadataAttribute<FString>* StringAttr = static_cast<const FPCGMetadataAttribute<FString>*>(Attribute))
                {
                    AttributeValue = StringAttr->GetValueFromItemKey(Point.MetadataEntry);
                }
                else if (const FPCGMetadataAttribute<FVector>* VectorAttr = static_cast<const FPCGMetadataAttribute<FVector>*>(Attribute))
                {
                    const FVector& Vec = VectorAttr->GetValueFromItemKey(Point.MetadataEntry);
                    AttributeValue = FString::Printf(TEXT("%.6f"), Vec.Size()); // Use magnitude for expressions
                    
                    // Also add individual components
                    VariableReplacements.Add(AttributeName.ToString() + TEXT(".X"), FString::Printf(TEXT("%.6f"), Vec.X));
                    VariableReplacements.Add(AttributeName.ToString() + TEXT(".Y"), FString::Printf(TEXT("%.6f"), Vec.Y));
                    VariableReplacements.Add(AttributeName.ToString() + TEXT(".Z"), FString::Printf(TEXT("%.6f"), Vec.Z));
                }
                
                if (!AttributeValue.IsEmpty())
                {
                    VariableReplacements.Add(AttributeName.ToString(), AttributeValue);
                }
            }
        }
    }
    
    // Additional point bounds properties
    VariableReplacements.Add(TEXT("BoundsMin.X"), FString::Printf(TEXT("%.6f"), Point.BoundsMin.X));
    VariableReplacements.Add(TEXT("BoundsMin.Y"), FString::Printf(TEXT("%.6f"), Point.BoundsMin.Y));
    VariableReplacements.Add(TEXT("BoundsMin.Z"), FString::Printf(TEXT("%.6f"), Point.BoundsMin.Z));
    VariableReplacements.Add(TEXT("BoundsMax.X"), FString::Printf(TEXT("%.6f"), Point.BoundsMax.X));
    VariableReplacements.Add(TEXT("BoundsMax.Y"), FString::Printf(TEXT("%.6f"), Point.BoundsMax.Y));
    VariableReplacements.Add(TEXT("BoundsMax.Z"), FString::Printf(TEXT("%.6f"), Point.BoundsMax.Z));
    VariableReplacements.Add(TEXT("Color.R"), FString::Printf(TEXT("%.6f"), Point.Color.R));
    VariableReplacements.Add(TEXT("Color.G"), FString::Printf(TEXT("%.6f"), Point.Color.G));
    VariableReplacements.Add(TEXT("Color.B"), FString::Printf(TEXT("%.6f"), Point.Color.B));
    VariableReplacements.Add(TEXT("Color.A"), FString::Printf(TEXT("%.6f"), Point.Color.A));
    VariableReplacements.Add(TEXT("Steepness"), FString::Printf(TEXT("%.6f"), Point.Steepness));
    VariableReplacements.Add(TEXT("Seed"), FString::Printf(TEXT("%d"), Point.Seed));
    
    // Metadata attributes (if available)
    if (Metadata)
    {
        // Get all attribute names and try to replace them
        TArray<FName> AttributeNames;
        TArray<EPCGMetadataTypes> AttributeTypes;
        Metadata->GetAttributes(AttributeNames, AttributeTypes);
        
        for (int32 i = 0; i < AttributeNames.Num(); ++i)
        {
            FName AttributeName = AttributeNames[i];
            EPCGMetadataTypes AttributeType = AttributeTypes[i];
            
            FString AttributeValue;
            switch (AttributeType)
            {
                case EPCGMetadataTypes::Float:
                case EPCGMetadataTypes::Double:
                {
                    const FPCGMetadataAttribute<float>* FloatAttribute = Metadata->GetConstTypedAttribute<float>(AttributeName);
                    if (FloatAttribute)
                    {
                        float Value = FloatAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        AttributeValue = FString::Printf(TEXT("%.6f"), Value);
                    }
                    break;
                }
                case EPCGMetadataTypes::Integer32:
                case EPCGMetadataTypes::Integer64:
                {
                    const FPCGMetadataAttribute<int32>* IntAttribute = Metadata->GetConstTypedAttribute<int32>(AttributeName);
                    if (IntAttribute)
                    {
                        int32 Value = IntAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        AttributeValue = FString::Printf(TEXT("%d"), Value);
                    }
                    break;
                }
                case EPCGMetadataTypes::Vector2:
                {
                    const FPCGMetadataAttribute<FVector2D>* Vec2Attribute = Metadata->GetConstTypedAttribute<FVector2D>(AttributeName);
                    if (Vec2Attribute)
                    {
                        FVector2D Value = Vec2Attribute->GetValueFromItemKey(Point.MetadataEntry);
                        VariableReplacements.Add(AttributeName.ToString() + TEXT(".X"), FString::Printf(TEXT("%.6f"), Value.X));
                        VariableReplacements.Add(AttributeName.ToString() + TEXT(".Y"), FString::Printf(TEXT("%.6f"), Value.Y));
                    }
                    break;
                }
                case EPCGMetadataTypes::Vector:
                {
                    const FPCGMetadataAttribute<FVector>* VecAttribute = Metadata->GetConstTypedAttribute<FVector>(AttributeName);
                    if (VecAttribute)
                    {
                        FVector Value = VecAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        VariableReplacements.Add(AttributeName.ToString() + TEXT(".X"), FString::Printf(TEXT("%.6f"), Value.X));
                        VariableReplacements.Add(AttributeName.ToString() + TEXT(".Y"), FString::Printf(TEXT("%.6f"), Value.Y));
                        VariableReplacements.Add(AttributeName.ToString() + TEXT(".Z"), FString::Printf(TEXT("%.6f"), Value.Z));
                    }
                    break;
                }
                case EPCGMetadataTypes::Boolean:
                {
                    const FPCGMetadataAttribute<bool>* BoolAttribute = Metadata->GetConstTypedAttribute<bool>(AttributeName);
                    if (BoolAttribute)
                    {
                        bool Value = BoolAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        AttributeValue = Value ? TEXT("1") : TEXT("0");
                    }
                    break;
                }
                case EPCGMetadataTypes::String:
                case EPCGMetadataTypes::Name:
                {
                    const FPCGMetadataAttribute<FString>* StringAttribute = Metadata->GetConstTypedAttribute<FString>(AttributeName);
                    if (StringAttribute)
                    {
                        FString Value = StringAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        AttributeValue = FString::Printf(TEXT("\"%s\""), *Value);
                    }
                    break;
                }
                default:
                    break;
            }
            
            if (!AttributeValue.IsEmpty())
            {
                VariableReplacements.Add(AttributeName.ToString(), AttributeValue);
            }
        }
    }
    
    // Replace variables in expression
    for (const auto& Replacement : VariableReplacements)
    {
        ProcessedExpression = ProcessedExpression.Replace(*Replacement.Key, *Replacement.Value);
    }
    
    // Evaluate common expression patterns
    bool bResult = true;
    
    // Handle comparison operators
    if (ProcessedExpression.Contains(TEXT(">=")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT(">="));
        if (Parts.Num() == 2)
        {
            float LeftValue = FCString::Atof(*Parts[0].TrimStartAndEnd());
            float RightValue = FCString::Atof(*Parts[1].TrimStartAndEnd());
            bResult = LeftValue >= RightValue;
        }
    }
    else if (ProcessedExpression.Contains(TEXT("<=")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT("<="));
        if (Parts.Num() == 2)
        {
            float LeftValue = FCString::Atof(*Parts[0].TrimStartAndEnd());
            float RightValue = FCString::Atof(*Parts[1].TrimStartAndEnd());
            bResult = LeftValue <= RightValue;
        }
    }
    else if (ProcessedExpression.Contains(TEXT("==")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT("=="));
        if (Parts.Num() == 2)
        {
            FString LeftStr = Parts[0].TrimStartAndEnd();
            FString RightStr = Parts[1].TrimStartAndEnd();
            
            // Try numeric comparison first
            if (LeftStr.IsNumeric() && RightStr.IsNumeric())
            {
                float LeftValue = FCString::Atof(*LeftStr);
                float RightValue = FCString::Atof(*RightStr);
                bResult = FMath::IsNearlyEqual(LeftValue, RightValue, 0.001f);
            }
            else
            {
                // String comparison
                bResult = LeftStr.Equals(RightStr);
            }
        }
    }
    else if (ProcessedExpression.Contains(TEXT("!=")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT("!="));
        if (Parts.Num() == 2)
        {
            FString LeftStr = Parts[0].TrimStartAndEnd();
            FString RightStr = Parts[1].TrimStartAndEnd();
            
            // Try numeric comparison first
            if (LeftStr.IsNumeric() && RightStr.IsNumeric())
            {
                float LeftValue = FCString::Atof(*LeftStr);
                float RightValue = FCString::Atof(*RightStr);
                bResult = !FMath::IsNearlyEqual(LeftValue, RightValue, 0.001f);
            }
            else
            {
                // String comparison
                bResult = !LeftStr.Equals(RightStr);
            }
        }
    }
    else if (ProcessedExpression.Contains(TEXT(">")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT(">"));
        if (Parts.Num() == 2)
        {
            float LeftValue = FCString::Atof(*Parts[0].TrimStartAndEnd());
            float RightValue = FCString::Atof(*Parts[1].TrimStartAndEnd());
            bResult = LeftValue > RightValue;
        }
    }
    else if (ProcessedExpression.Contains(TEXT("<")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT("<"));
        if (Parts.Num() == 2)
        {
            float LeftValue = FCString::Atof(*Parts[0].TrimStartAndEnd());
            float RightValue = FCString::Atof(*Parts[1].TrimStartAndEnd());
            bResult = LeftValue < RightValue;
        }
    }
    // Handle logical operators
    else if (ProcessedExpression.Contains(TEXT("&&")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT("&&"));
        bResult = true;
        for (const FString& Part : Parts)
        {
            FString TrimmedPart = Part.TrimStartAndEnd();
            if (TrimmedPart.Equals(TEXT("0")) || TrimmedPart.Equals(TEXT("false"), ESearchCase::IgnoreCase))
            {
                bResult = false;
                break;
            }
        }
    }
    else if (ProcessedExpression.Contains(TEXT("||")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT("||"));
        bResult = false;
        for (const FString& Part : Parts)
        {
            FString TrimmedPart = Part.TrimStartAndEnd();
            if (!TrimmedPart.Equals(TEXT("0")) && !TrimmedPart.Equals(TEXT("false"), ESearchCase::IgnoreCase))
            {
                bResult = true;
                break;
            }
        }
    }
    // Handle range checks
    else if (ProcessedExpression.Contains(TEXT("between")))
    {
        // Format: "value between min and max"
        FString LowerExpression = ProcessedExpression;
        LowerExpression = LowerExpression.Replace(TEXT("between"), TEXT(""));
        LowerExpression = LowerExpression.Replace(TEXT("and"), TEXT(","));
        
        TArray<FString> Parts;
        LowerExpression.ParseIntoArray(Parts, TEXT(","));
        if (Parts.Num() == 3)
        {
            float Value = FCString::Atof(*Parts[0].TrimStartAndEnd());
            float MinValue = FCString::Atof(*Parts[1].TrimStartAndEnd());
            float MaxValue = FCString::Atof(*Parts[2].TrimStartAndEnd());
            bResult = (Value >= MinValue && Value <= MaxValue);
        }
    }
    // Handle simple boolean values
    else if (ProcessedExpression.Equals(TEXT("true"), ESearchCase::IgnoreCase) || ProcessedExpression.Equals(TEXT("1")))
    {
        bResult = true;
    }
    else if (ProcessedExpression.Equals(TEXT("false"), ESearchCase::IgnoreCase) || ProcessedExpression.Equals(TEXT("0")))
    {
        bResult = false;
    }
    // Handle numeric values (non-zero is true)
    else if (ProcessedExpression.IsNumeric())
    {
        float NumericValue = FCString::Atof(*ProcessedExpression);
        bResult = !FMath::IsNearlyZero(NumericValue, 0.001f);
    }
    
    // Handle contains checks for strings
    else if (ProcessedExpression.Contains(TEXT("contains")))
    {
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, TEXT("contains"));
        if (Parts.Num() == 2)
        {
            FString MainStr = Parts[0].TrimStartAndEnd();
            FString SubStr = Parts[1].TrimStartAndEnd();
            
            // Remove quotes if present
            MainStr = MainStr.Replace(TEXT("\""), TEXT(""));
            SubStr = SubStr.Replace(TEXT("\""), TEXT(""));
            
            bResult = MainStr.Contains(SubStr);
        }
    }
    // Handle logical AND/OR operations
    else if (ProcessedExpression.Contains(TEXT("&&")) || ProcessedExpression.Contains(TEXT("and")))
    {
        FString Delimiter = ProcessedExpression.Contains(TEXT("&&")) ? TEXT("&&") : TEXT("and");
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, *Delimiter);
        
        bResult = true;
        for (const FString& Part : Parts)
        {
            FString TrimmedPart = Part.TrimStartAndEnd();
            if (TrimmedPart.IsNumeric())
            {
                float Value = FCString::Atof(*TrimmedPart);
                bResult = bResult && (Value != 0.0f);
            }
            else if (TrimmedPart.ToLower() == TEXT("true"))
            {
                bResult = bResult && true;
            }
            else if (TrimmedPart.ToLower() == TEXT("false"))
            {
                bResult = bResult && false;
            }
            else
            {
                // Recursively evaluate sub-expression
                bResult = bResult && EvaluateCustomValidation(Point, Metadata, TrimmedPart);
            }
            
            if (!bResult) break; // Short-circuit evaluation
        }
    }
    else if (ProcessedExpression.Contains(TEXT("||")) || ProcessedExpression.Contains(TEXT("or")))
    {
        FString Delimiter = ProcessedExpression.Contains(TEXT("||")) ? TEXT("||") : TEXT("or");
        TArray<FString> Parts;
        ProcessedExpression.ParseIntoArray(Parts, *Delimiter);
        
        bResult = false;
        for (const FString& Part : Parts)
        {
            FString TrimmedPart = Part.TrimStartAndEnd();
            if (TrimmedPart.IsNumeric())
            {
                float Value = FCString::Atof(*TrimmedPart);
                bResult = bResult || (Value != 0.0f);
            }
            else if (TrimmedPart.ToLower() == TEXT("true"))
            {
                bResult = bResult || true;
            }
            else if (TrimmedPart.ToLower() == TEXT("false"))
            {
                bResult = bResult || false;
            }
            else
            {
                // Recursively evaluate sub-expression
                bResult = bResult || EvaluateCustomValidation(Point, Metadata, TrimmedPart);
            }
            
            if (bResult) break; // Short-circuit evaluation
        }
    }
    // Handle simple boolean values
    else if (ProcessedExpression.ToLower() == TEXT("true"))
    {
        bResult = true;
    }
    else if (ProcessedExpression.ToLower() == TEXT("false"))
    {
        bResult = false;
    }
    // Handle numeric values (non-zero is true)
    else if (ProcessedExpression.IsNumeric())
    {
        float Value = FCString::Atof(*ProcessedExpression);
        bResult = Value != 0.0f;
    }
    // Default case: if expression couldn't be parsed, log warning and return true
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("EvaluateCustomValidation: Could not parse expression '%s', defaulting to true"), *Expression);
        bResult = true;
    }
    
    return bResult;
}

UPCGAttributeSet* FAuracronPCGAttributeValidatorElement::CreateValidationResultsAttributeSet(const TArray<FString>& ValidationErrors,
                                                                                              int32 ValidCount,
                                                                                              int32 InvalidCount) const
{
    UPCGAttributeSet* AttributeSet = NewObject<UPCGAttributeSet>();
    
    // Create metadata for results
    UPCGMetadata* Metadata = NewObject<UPCGMetadata>();
    Metadata->Initialize(1); // Single entry with summary

    // Create attributes for validation results
    Metadata->CreateIntegerAttribute(FName("ValidCount"), ValidCount, false);
    Metadata->CreateIntegerAttribute(FName("InvalidCount"), InvalidCount, false);
    Metadata->CreateIntegerAttribute(FName("TotalErrors"), ValidationErrors.Num(), false);
    Metadata->CreateStringAttribute(FName("ErrorSummary"), FString::Join(ValidationErrors, TEXT("; ")), false);

    AttributeSet->Metadata = Metadata;
    return AttributeSet;
}

// =============================================================================
// ATTRIBUTE AGGREGATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeAggregatorSettings::UAuracronPCGAttributeAggregatorSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Aggregator");
    NodeMetadata.NodeDescription = TEXT("Aggregates attribute values using various statistical operations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Aggregate"));
    NodeMetadata.Tags.Add(TEXT("Statistics"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.8f);
}

void UAuracronPCGAttributeAggregatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeAggregatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;

    if (bOutputGroupStatistics)
    {
        FPCGPinProperties& StatsPin = OutputPins.Emplace_GetRef();
        StatsPin.Label = TEXT("Statistics");
        StatsPin.AllowedTypes = EPCGDataType::AttributeSet;
        StatsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAttributeAggregatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                              FPCGDataCollection& OutputData, 
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGAttributeAggregatorSettings* Settings = GetTypedSettings<UAuracronPCGAttributeAggregatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Aggregator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 OperationsPerformed = 0;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData || !InputPointData->Metadata)
            {
                continue;
            }

            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            UPCGAttributeSet* StatisticsAttributeSet = nullptr;
            if (Settings->bOutputGroupStatistics)
            {
                StatisticsAttributeSet = NewObject<UPCGAttributeSet>();
            }

            if (ProcessAggregation(OutputPointData, StatisticsAttributeSet, Settings))
            {
                TotalProcessed += InputPointData->GetPoints().Num();
                OperationsPerformed += Settings->AggregationOperations.Num();

                // Add output data
                FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
                OutputTaggedData.Data = OutputPointData;
                OutputTaggedData.Pin = TEXT("Output");
                OutputTaggedData.Tags = TaggedData.Tags;

                // Add statistics if requested
                if (StatisticsAttributeSet)
                {
                    FPCGTaggedData& StatsTaggedData = OutputData.TaggedData.Emplace_GetRef();
                    StatsTaggedData.Data = StatisticsAttributeSet;
                    StatsTaggedData.Pin = TEXT("Statistics");
                    StatsTaggedData.Tags = TaggedData.Tags;
                }
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Aggregator processed %d points with %d operations"), 
                                  TotalProcessed, OperationsPerformed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Aggregator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAttributeAggregatorElement::ProcessAggregation(UPCGPointData* PointData, 
                                                                UPCGAttributeSet* StatisticsAttributeSet,
                                                                const UAuracronPCGAttributeAggregatorSettings* Settings) const
{
    if (!PointData || !PointData->Metadata)
    {
        return false;
    }

    // Process each aggregation operation
    for (const FAuracronPCGAttributeOperation& Operation : Settings->AggregationOperations)
    {
        ProcessSingleAggregation(PointData, Operation, Settings);
    }

    // Generate statistics if requested
    if (StatisticsAttributeSet && Settings->bOutputGroupStatistics)
    {
        GenerateStatistics(PointData, StatisticsAttributeSet, Settings);
    }

    return true;
}

void FAuracronPCGAttributeAggregatorElement::ProcessSingleAggregation(UPCGPointData* PointData, 
                                                                       const FAuracronPCGAttributeOperation& Operation,
                                                                       const UAuracronPCGAttributeAggregatorSettings* Settings) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    if (!Metadata || !Metadata->HasAttribute(FName(*Operation.SourceAttribute)))
    {
        return;
    }

    // Create target attribute if needed
    if (!Metadata->HasAttribute(FName(*Operation.TargetAttribute)) && Operation.bCreateIfNotExists)
    {
        const UPCGMetadataAttributeBase* SourceAttr = Metadata->GetConstAttribute(FName(*Operation.SourceAttribute));
        if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
        {
            Metadata->CreateFloatAttribute(FName(*Operation.TargetAttribute), 0.0f, true);
        }
        // Add more type handling as needed
    }

    // Perform aggregation based on operation type
    switch (Operation.Operation)
    {
        case EAuracronPCGAttributeAggregation::Sum:
        case EAuracronPCGAttributeAggregation::Average:
        case EAuracronPCGAttributeAggregation::Min:
        case EAuracronPCGAttributeAggregation::Max:
        case EAuracronPCGAttributeAggregation::Count:
            PerformNumericAggregation(PointData, Operation);
            break;
        default:
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Unsupported aggregation operation"));
            break;
    }
}

void FAuracronPCGAttributeAggregatorElement::PerformNumericAggregation(UPCGPointData* PointData, 
                                                                        const FAuracronPCGAttributeOperation& Operation) const
{
    if (!PointData || !PointData->Metadata)
    {
        UAuracronPCGLogger::LogWarning(TEXT("PerformNumericAggregation - Invalid point data or metadata"));
        return;
    }

    UPCGMetadata* Metadata = PointData->Metadata;
    const UPCGMetadataAttributeBase* SourceAttr = Metadata->GetConstAttribute(FName(*Operation.SourceAttribute));
    UPCGMetadataAttributeBase* TargetAttr = Metadata->GetMutableAttribute(FName(*Operation.TargetAttribute));

    if (!SourceAttr)
    {
        UAuracronPCGLogger::LogWarning(TEXT("PerformNumericAggregation - Source attribute '%s' not found"), *Operation.SourceAttribute);
        return;
    }

    if (!TargetAttr)
    {
        UAuracronPCGLogger::LogWarning(TEXT("PerformNumericAggregation - Target attribute '%s' not found"), *Operation.TargetAttribute);
        return;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    if (Points.Num() == 0)
    {
        UAuracronPCGLogger::LogWarning(TEXT("PerformNumericAggregation - No points to process"));
        return;
    }

    // Handle float attributes
    if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id && 
        TargetAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
    {
        PerformFloatAggregation(PointData, Operation);
    }
    // Handle int32 attributes
    else if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<int32>::Id && 
             TargetAttr->GetTypeId() == PCG::Private::MetadataTypes<int32>::Id)
    {
        PerformInt32Aggregation(PointData, Operation);
    }
    // Handle double attributes
    else if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<double>::Id && 
             TargetAttr->GetTypeId() == PCG::Private::MetadataTypes<double>::Id)
    {
        PerformDoubleAggregation(PointData, Operation);
    }
    // Handle vector attributes
    else if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id && 
             TargetAttr->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id)
    {
        PerformVectorAggregation(PointData, Operation);
    }
    else
    {
        UAuracronPCGLogger::LogWarning(TEXT("PerformNumericAggregation - Unsupported attribute type combination for source '%s' and target '%s'"), 
            *Operation.SourceAttribute, *Operation.TargetAttribute);
    }

    UAuracronPCGLogger::LogInfo(TEXT("PerformNumericAggregation - Completed aggregation operation '%s' from '%s' to '%s' on %d points"), 
        *UEnum::GetValueAsString(Operation.Operation), *Operation.SourceAttribute, *Operation.TargetAttribute, Points.Num());
}

void FAuracronPCGAttributeAggregatorElement::PerformFloatAggregation(UPCGPointData* PointData, const FAuracronPCGAttributeOperation& Operation) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    const UPCGMetadataAttribute<float>* FloatSourceAttr = static_cast<const UPCGMetadataAttribute<float>*>(Metadata->GetConstAttribute(FName(*Operation.SourceAttribute)));
    UPCGMetadataAttribute<float>* FloatTargetAttr = static_cast<UPCGMetadataAttribute<float>*>(Metadata->GetMutableAttribute(FName(*Operation.TargetAttribute)));

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    float AggregatedValue = 0.0f;
    float MinValue = FLT_MAX;
    float MaxValue = -FLT_MAX;
    float Sum = 0.0f;
    int32 Count = 0;

    for (const FPCGPoint& Point : Points)
    {
        float Value = FloatSourceAttr->GetValueFromItemKey(Point.MetadataEntry);
        Sum += Value;
        MinValue = FMath::Min(MinValue, Value);
        MaxValue = FMath::Max(MaxValue, Value);
        Count++;
    }

    switch (Operation.Operation)
    {
        case EAuracronPCGAttributeAggregation::Sum:
            AggregatedValue = Sum;
            break;
        case EAuracronPCGAttributeAggregation::Average:
            AggregatedValue = Count > 0 ? Sum / Count : 0.0f;
            break;
        case EAuracronPCGAttributeAggregation::Min:
            AggregatedValue = MinValue != FLT_MAX ? MinValue : 0.0f;
            break;
        case EAuracronPCGAttributeAggregation::Max:
            AggregatedValue = MaxValue != -FLT_MAX ? MaxValue : 0.0f;
            break;
        case EAuracronPCGAttributeAggregation::Count:
            AggregatedValue = static_cast<float>(Count);
            break;
        default:
            AggregatedValue = 0.0f;
            break;
    }

    for (const FPCGPoint& Point : Points)
    {
        FloatTargetAttr->SetValueFromItemKey(Point.MetadataEntry, AggregatedValue);
    }
}

void FAuracronPCGAttributeAggregatorElement::PerformInt32Aggregation(UPCGPointData* PointData, const FAuracronPCGAttributeOperation& Operation) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    const UPCGMetadataAttribute<int32>* Int32SourceAttr = static_cast<const UPCGMetadataAttribute<int32>*>(Metadata->GetConstAttribute(FName(*Operation.SourceAttribute)));
    UPCGMetadataAttribute<int32>* Int32TargetAttr = static_cast<UPCGMetadataAttribute<int32>*>(Metadata->GetMutableAttribute(FName(*Operation.TargetAttribute)));

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    int32 AggregatedValue = 0;
    int32 MinValue = INT32_MAX;
    int32 MaxValue = INT32_MIN;
    int64 Sum = 0;
    int32 Count = 0;

    for (const FPCGPoint& Point : Points)
    {
        int32 Value = Int32SourceAttr->GetValueFromItemKey(Point.MetadataEntry);
        Sum += Value;
        MinValue = FMath::Min(MinValue, Value);
        MaxValue = FMath::Max(MaxValue, Value);
        Count++;
    }

    switch (Operation.Operation)
    {
        case EAuracronPCGAttributeAggregation::Sum:
            AggregatedValue = static_cast<int32>(FMath::Clamp(Sum, static_cast<int64>(INT32_MIN), static_cast<int64>(INT32_MAX)));
            break;
        case EAuracronPCGAttributeAggregation::Average:
            AggregatedValue = Count > 0 ? static_cast<int32>(Sum / Count) : 0;
            break;
        case EAuracronPCGAttributeAggregation::Min:
            AggregatedValue = MinValue != INT32_MAX ? MinValue : 0;
            break;
        case EAuracronPCGAttributeAggregation::Max:
            AggregatedValue = MaxValue != INT32_MIN ? MaxValue : 0;
            break;
        case EAuracronPCGAttributeAggregation::Count:
            AggregatedValue = Count;
            break;
        default:
            AggregatedValue = 0;
            break;
    }

    for (const FPCGPoint& Point : Points)
    {
        Int32TargetAttr->SetValueFromItemKey(Point.MetadataEntry, AggregatedValue);
    }
}

void FAuracronPCGAttributeAggregatorElement::PerformDoubleAggregation(UPCGPointData* PointData, const FAuracronPCGAttributeOperation& Operation) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    const UPCGMetadataAttribute<double>* DoubleSourceAttr = static_cast<const UPCGMetadataAttribute<double>*>(Metadata->GetConstAttribute(FName(*Operation.SourceAttribute)));
    UPCGMetadataAttribute<double>* DoubleTargetAttr = static_cast<UPCGMetadataAttribute<double>*>(Metadata->GetMutableAttribute(FName(*Operation.TargetAttribute)));

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    double AggregatedValue = 0.0;
    double MinValue = DBL_MAX;
    double MaxValue = -DBL_MAX;
    double Sum = 0.0;
    int32 Count = 0;

    for (const FPCGPoint& Point : Points)
    {
        double Value = DoubleSourceAttr->GetValueFromItemKey(Point.MetadataEntry);
        Sum += Value;
        MinValue = FMath::Min(MinValue, Value);
        MaxValue = FMath::Max(MaxValue, Value);
        Count++;
    }

    switch (Operation.Operation)
    {
        case EAuracronPCGAttributeAggregation::Sum:
            AggregatedValue = Sum;
            break;
        case EAuracronPCGAttributeAggregation::Average:
            AggregatedValue = Count > 0 ? Sum / Count : 0.0;
            break;
        case EAuracronPCGAttributeAggregation::Min:
            AggregatedValue = MinValue != DBL_MAX ? MinValue : 0.0;
            break;
        case EAuracronPCGAttributeAggregation::Max:
            AggregatedValue = MaxValue != -DBL_MAX ? MaxValue : 0.0;
            break;
        case EAuracronPCGAttributeAggregation::Count:
            AggregatedValue = static_cast<double>(Count);
            break;
        default:
            AggregatedValue = 0.0;
            break;
    }

    for (const FPCGPoint& Point : Points)
    {
        DoubleTargetAttr->SetValueFromItemKey(Point.MetadataEntry, AggregatedValue);
    }
}

void FAuracronPCGAttributeAggregatorElement::PerformVectorAggregation(UPCGPointData* PointData, const FAuracronPCGAttributeOperation& Operation) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    const UPCGMetadataAttribute<FVector>* VectorSourceAttr = static_cast<const UPCGMetadataAttribute<FVector>*>(Metadata->GetConstAttribute(FName(*Operation.SourceAttribute)));
    UPCGMetadataAttribute<FVector>* VectorTargetAttr = static_cast<UPCGMetadataAttribute<FVector>*>(Metadata->GetMutableAttribute(FName(*Operation.TargetAttribute)));

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    FVector AggregatedValue = FVector::ZeroVector;
    FVector MinValue = FVector(FLT_MAX);
    FVector MaxValue = FVector(-FLT_MAX);
    FVector Sum = FVector::ZeroVector;
    int32 Count = 0;

    for (const FPCGPoint& Point : Points)
    {
        FVector Value = VectorSourceAttr->GetValueFromItemKey(Point.MetadataEntry);
        Sum += Value;
        MinValue.X = FMath::Min(MinValue.X, Value.X);
        MinValue.Y = FMath::Min(MinValue.Y, Value.Y);
        MinValue.Z = FMath::Min(MinValue.Z, Value.Z);
        MaxValue.X = FMath::Max(MaxValue.X, Value.X);
        MaxValue.Y = FMath::Max(MaxValue.Y, Value.Y);
        MaxValue.Z = FMath::Max(MaxValue.Z, Value.Z);
        Count++;
    }

    switch (Operation.Operation)
    {
        case EAuracronPCGAttributeAggregation::Sum:
            AggregatedValue = Sum;
            break;
        case EAuracronPCGAttributeAggregation::Average:
            AggregatedValue = Count > 0 ? Sum / Count : FVector::ZeroVector;
            break;
        case EAuracronPCGAttributeAggregation::Min:
            AggregatedValue = MinValue.X != FLT_MAX ? MinValue : FVector::ZeroVector;
            break;
        case EAuracronPCGAttributeAggregation::Max:
            AggregatedValue = MaxValue.X != -FLT_MAX ? MaxValue : FVector::ZeroVector;
            break;
        case EAuracronPCGAttributeAggregation::Count:
            AggregatedValue = FVector(static_cast<float>(Count));
            break;
        default:
            AggregatedValue = FVector::ZeroVector;
            break;
    }

    for (const FPCGPoint& Point : Points)
    {
        VectorTargetAttr->SetValueFromItemKey(Point.MetadataEntry, AggregatedValue);
    }
}

void FAuracronPCGAttributeAggregatorElement::GenerateStatistics(UPCGPointData* PointData, 
                                                                UPCGAttributeSet* StatisticsAttributeSet,
                                                                const UAuracronPCGAttributeAggregatorSettings* Settings) const
{
    if (!StatisticsAttributeSet)
    {
        return;
    }

    // Create metadata for statistics
    UPCGMetadata* StatsMetadata = NewObject<UPCGMetadata>();
    StatsMetadata->Initialize(1); // Single entry with summary

    // Add basic statistics
    StatsMetadata->CreateIntegerAttribute(FName("TotalPoints"), PointData->GetPoints().Num(), false);
    StatsMetadata->CreateIntegerAttribute(FName("OperationsPerformed"), Settings->AggregationOperations.Num(), false);
    StatsMetadata->CreateStringAttribute(FName("ProcessingMode"), Settings->bUseGrouping ? TEXT("Grouped") : TEXT("Global"), false);

    StatisticsAttributeSet->Metadata = StatsMetadata;
}
