// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronLumenBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronLumenBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONLUMENBRIDGE_API UClass* Z_Construct_UClass_UAuracronLumenBridgeAPI();
AURACRONLUMENBRIDGE_API UClass* Z_Construct_UClass_UAuracronLumenBridgeAPI_NoRegister();
AURACRONLUMENBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState();
AURACRONLUMENBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality();
AURACRONLUMENBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode();
AURACRONLUMENBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality();
AURACRONLUMENBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode();
AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature();
AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature();
AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature();
AURACRONLUMENBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature();
AURACRONLUMENBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLumenLightingScenario();
AURACRONLUMENBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLumenMetrics();
AURACRONLUMENBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLumenQualitySettings();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronLumenBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronLumenQuality *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLumenQuality;
static UEnum* EAuracronLumenQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLumenQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("EAuracronLumenQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronLumenQuality.OuterSingleton;
}
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenQuality>()
{
	return EAuracronLumenQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic" },
		{ "Cinematic.Name", "EAuracronLumenQuality::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Quality Levels\n */" },
#endif
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronLumenQuality::Epic" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronLumenQuality::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronLumenQuality::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronLumenQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Quality Levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLumenQuality::Low", (int64)EAuracronLumenQuality::Low },
		{ "EAuracronLumenQuality::Medium", (int64)EAuracronLumenQuality::Medium },
		{ "EAuracronLumenQuality::High", (int64)EAuracronLumenQuality::High },
		{ "EAuracronLumenQuality::Epic", (int64)EAuracronLumenQuality::Epic },
		{ "EAuracronLumenQuality::Cinematic", (int64)EAuracronLumenQuality::Cinematic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	"EAuracronLumenQuality",
	"EAuracronLumenQuality",
	Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLumenQuality.InnerSingleton, Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLumenQuality.InnerSingleton;
}
// ********** End Enum EAuracronLumenQuality *******************************************************

// ********** Begin Enum EAuracronLumenRayTracingMode **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode;
static UEnum* EAuracronLumenRayTracingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("EAuracronLumenRayTracingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode.OuterSingleton;
}
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenRayTracingMode>()
{
	return EAuracronLumenRayTracingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Ray Tracing Mode\n */" },
#endif
		{ "Hardware.DisplayName", "Hardware Ray Tracing" },
		{ "Hardware.Name", "EAuracronLumenRayTracingMode::Hardware" },
		{ "Hybrid.DisplayName", "Hybrid Mode" },
		{ "Hybrid.Name", "EAuracronLumenRayTracingMode::Hybrid" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
		{ "Software.DisplayName", "Software Ray Tracing" },
		{ "Software.Name", "EAuracronLumenRayTracingMode::Software" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Ray Tracing Mode" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLumenRayTracingMode::Software", (int64)EAuracronLumenRayTracingMode::Software },
		{ "EAuracronLumenRayTracingMode::Hardware", (int64)EAuracronLumenRayTracingMode::Hardware },
		{ "EAuracronLumenRayTracingMode::Hybrid", (int64)EAuracronLumenRayTracingMode::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	"EAuracronLumenRayTracingMode",
	"EAuracronLumenRayTracingMode",
	Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode.InnerSingleton, Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode.InnerSingleton;
}
// ********** End Enum EAuracronLumenRayTracingMode ************************************************

// ********** Begin Enum EAuracronLumenSceneMode ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLumenSceneMode;
static UEnum* EAuracronLumenSceneMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenSceneMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLumenSceneMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("EAuracronLumenSceneMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronLumenSceneMode.OuterSingleton;
}
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenSceneMode>()
{
	return EAuracronLumenSceneMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Scene Representation Mode\n */" },
#endif
		{ "GlobalSDF.DisplayName", "Global SDF" },
		{ "GlobalSDF.Name", "EAuracronLumenSceneMode::GlobalSDF" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EAuracronLumenSceneMode::Hybrid" },
		{ "MeshCards.DisplayName", "Mesh Cards" },
		{ "MeshCards.Name", "EAuracronLumenSceneMode::MeshCards" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
		{ "SurfaceCache.DisplayName", "Surface Cache" },
		{ "SurfaceCache.Name", "EAuracronLumenSceneMode::SurfaceCache" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Scene Representation Mode" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLumenSceneMode::SurfaceCache", (int64)EAuracronLumenSceneMode::SurfaceCache },
		{ "EAuracronLumenSceneMode::GlobalSDF", (int64)EAuracronLumenSceneMode::GlobalSDF },
		{ "EAuracronLumenSceneMode::MeshCards", (int64)EAuracronLumenSceneMode::MeshCards },
		{ "EAuracronLumenSceneMode::Hybrid", (int64)EAuracronLumenSceneMode::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	"EAuracronLumenSceneMode",
	"EAuracronLumenSceneMode",
	Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenSceneMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLumenSceneMode.InnerSingleton, Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLumenSceneMode.InnerSingleton;
}
// ********** End Enum EAuracronLumenSceneMode *****************************************************

// ********** Begin Enum EAuracronLumenReflectionQuality *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality;
static UEnum* EAuracronLumenReflectionQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("EAuracronLumenReflectionQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality.OuterSingleton;
}
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenReflectionQuality>()
{
	return EAuracronLumenReflectionQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Reflection Quality\n */" },
#endif
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EAuracronLumenReflectionQuality::Disabled" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronLumenReflectionQuality::Epic" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronLumenReflectionQuality::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronLumenReflectionQuality::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronLumenReflectionQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Reflection Quality" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLumenReflectionQuality::Disabled", (int64)EAuracronLumenReflectionQuality::Disabled },
		{ "EAuracronLumenReflectionQuality::Low", (int64)EAuracronLumenReflectionQuality::Low },
		{ "EAuracronLumenReflectionQuality::Medium", (int64)EAuracronLumenReflectionQuality::Medium },
		{ "EAuracronLumenReflectionQuality::High", (int64)EAuracronLumenReflectionQuality::High },
		{ "EAuracronLumenReflectionQuality::Epic", (int64)EAuracronLumenReflectionQuality::Epic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	"EAuracronLumenReflectionQuality",
	"EAuracronLumenReflectionQuality",
	Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality.InnerSingleton, Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality.InnerSingleton;
}
// ********** End Enum EAuracronLumenReflectionQuality *********************************************

// ********** Begin Enum EAuracronLumenGIState *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLumenGIState;
static UEnum* EAuracronLumenGIState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenGIState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLumenGIState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("EAuracronLumenGIState"));
	}
	return Z_Registration_Info_UEnum_EAuracronLumenGIState.OuterSingleton;
}
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenGIState>()
{
	return EAuracronLumenGIState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EAuracronLumenGIState::Active" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Global Illumination State\n */" },
#endif
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EAuracronLumenGIState::Disabled" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronLumenGIState::Error" },
		{ "Initializing.DisplayName", "Initializing" },
		{ "Initializing.Name", "EAuracronLumenGIState::Initializing" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Global Illumination State" },
#endif
		{ "Updating.DisplayName", "Updating" },
		{ "Updating.Name", "EAuracronLumenGIState::Updating" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLumenGIState::Disabled", (int64)EAuracronLumenGIState::Disabled },
		{ "EAuracronLumenGIState::Initializing", (int64)EAuracronLumenGIState::Initializing },
		{ "EAuracronLumenGIState::Active", (int64)EAuracronLumenGIState::Active },
		{ "EAuracronLumenGIState::Updating", (int64)EAuracronLumenGIState::Updating },
		{ "EAuracronLumenGIState::Error", (int64)EAuracronLumenGIState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	"EAuracronLumenGIState",
	"EAuracronLumenGIState",
	Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState()
{
	if (!Z_Registration_Info_UEnum_EAuracronLumenGIState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLumenGIState.InnerSingleton, Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLumenGIState.InnerSingleton;
}
// ********** End Enum EAuracronLumenGIState *******************************************************

// ********** Begin ScriptStruct FAuracronLumenMetrics *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics;
class UScriptStruct* FAuracronLumenMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLumenMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("AuracronLumenMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Performance Metrics Structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Performance Metrics Structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GITime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReflectionTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceCacheTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceCacheMemoryMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RadianceCacheMemoryMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveProbeCount_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemporalStability_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GITime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReflectionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SurfaceCacheTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SurfaceCacheMemoryMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RadianceCacheMemoryMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveProbeCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TemporalStability;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLumenMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, FrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTime_MetaData), NewProp_FrameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_GITime = { "GITime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, GITime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GITime_MetaData), NewProp_GITime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_ReflectionTime = { "ReflectionTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, ReflectionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReflectionTime_MetaData), NewProp_ReflectionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_SurfaceCacheTime = { "SurfaceCacheTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, SurfaceCacheTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceCacheTime_MetaData), NewProp_SurfaceCacheTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_SurfaceCacheMemoryMB = { "SurfaceCacheMemoryMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, SurfaceCacheMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceCacheMemoryMB_MetaData), NewProp_SurfaceCacheMemoryMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_RadianceCacheMemoryMB = { "RadianceCacheMemoryMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, RadianceCacheMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RadianceCacheMemoryMB_MetaData), NewProp_RadianceCacheMemoryMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_ActiveProbeCount = { "ActiveProbeCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, ActiveProbeCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveProbeCount_MetaData), NewProp_ActiveProbeCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_TemporalStability = { "TemporalStability", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenMetrics, TemporalStability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemporalStability_MetaData), NewProp_TemporalStability_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_FrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_GITime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_ReflectionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_SurfaceCacheTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_SurfaceCacheMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_RadianceCacheMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_ActiveProbeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewProp_TemporalStability,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	&NewStructOps,
	"AuracronLumenMetrics",
	Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::PropPointers),
	sizeof(FAuracronLumenMetrics),
	alignof(FAuracronLumenMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLumenMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLumenMetrics ***********************************************

// ********** Begin ScriptStruct FAuracronLumenQualitySettings *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings;
class UScriptStruct* FAuracronLumenQualitySettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLumenQualitySettings, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("AuracronLumenQualitySettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Quality Settings Structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Quality Settings Structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalIlluminationQuality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReflectionQuality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RayTracingMode_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SceneRepresentation_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResolutionScale_MetaData[] = {
		{ "Category", "Quality" },
		{ "ClampMax", "4.0" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TracingStepCount_MetaData[] = {
		{ "Category", "Quality" },
		{ "ClampMax", "8" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTemporalUpsampling_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDenoising_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_GlobalIlluminationQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GlobalIlluminationQuality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReflectionQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReflectionQuality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RayTracingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RayTracingMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SceneRepresentation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SceneRepresentation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResolutionScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TracingStepCount;
	static void NewProp_bEnableTemporalUpsampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTemporalUpsampling;
	static void NewProp_bEnableDenoising_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDenoising;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLumenQualitySettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_GlobalIlluminationQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_GlobalIlluminationQuality = { "GlobalIlluminationQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenQualitySettings, GlobalIlluminationQuality), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalIlluminationQuality_MetaData), NewProp_GlobalIlluminationQuality_MetaData) }; // 393568792
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_ReflectionQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_ReflectionQuality = { "ReflectionQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenQualitySettings, ReflectionQuality), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenReflectionQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReflectionQuality_MetaData), NewProp_ReflectionQuality_MetaData) }; // 3345947925
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_RayTracingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_RayTracingMode = { "RayTracingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenQualitySettings, RayTracingMode), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenRayTracingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RayTracingMode_MetaData), NewProp_RayTracingMode_MetaData) }; // 1926814625
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_SceneRepresentation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_SceneRepresentation = { "SceneRepresentation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenQualitySettings, SceneRepresentation), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenSceneMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SceneRepresentation_MetaData), NewProp_SceneRepresentation_MetaData) }; // 1806346539
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_ResolutionScale = { "ResolutionScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenQualitySettings, ResolutionScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResolutionScale_MetaData), NewProp_ResolutionScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_TracingStepCount = { "TracingStepCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenQualitySettings, TracingStepCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TracingStepCount_MetaData), NewProp_TracingStepCount_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableTemporalUpsampling_SetBit(void* Obj)
{
	((FAuracronLumenQualitySettings*)Obj)->bEnableTemporalUpsampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableTemporalUpsampling = { "bEnableTemporalUpsampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLumenQualitySettings), &Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableTemporalUpsampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTemporalUpsampling_MetaData), NewProp_bEnableTemporalUpsampling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableDenoising_SetBit(void* Obj)
{
	((FAuracronLumenQualitySettings*)Obj)->bEnableDenoising = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableDenoising = { "bEnableDenoising", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLumenQualitySettings), &Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableDenoising_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDenoising_MetaData), NewProp_bEnableDenoising_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_GlobalIlluminationQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_GlobalIlluminationQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_ReflectionQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_ReflectionQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_RayTracingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_RayTracingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_SceneRepresentation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_SceneRepresentation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_ResolutionScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_TracingStepCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableTemporalUpsampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewProp_bEnableDenoising,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	&NewStructOps,
	"AuracronLumenQualitySettings",
	Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::PropPointers),
	sizeof(FAuracronLumenQualitySettings),
	alignof(FAuracronLumenQualitySettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLumenQualitySettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLumenQualitySettings ***************************************

// ********** Begin ScriptStruct FAuracronLumenLightingScenario ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario;
class UScriptStruct* FAuracronLumenLightingScenario::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLumenLightingScenario, (UObject*)Z_Construct_UPackage__Script_AuracronLumenBridge(), TEXT("AuracronLumenLightingScenario"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lumen Lighting Scenario Structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Lighting Scenario Structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScenarioName_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkyLightColor_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkyLightIntensity_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DirectionalLightColor_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DirectionalLightIntensity_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DirectionalLightRotation_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalIlluminationIntensity_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReflectionIntensity_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVolumetricFog_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumetricFogColor_MetaData[] = {
		{ "Category", "Scenario" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScenarioName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SkyLightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SkyLightIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DirectionalLightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DirectionalLightIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DirectionalLightRotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalIlluminationIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReflectionIntensity;
	static void NewProp_bEnableVolumetricFog_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVolumetricFog;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VolumetricFogColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLumenLightingScenario>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_ScenarioName = { "ScenarioName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, ScenarioName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScenarioName_MetaData), NewProp_ScenarioName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_SkyLightColor = { "SkyLightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, SkyLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkyLightColor_MetaData), NewProp_SkyLightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_SkyLightIntensity = { "SkyLightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, SkyLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkyLightIntensity_MetaData), NewProp_SkyLightIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_DirectionalLightColor = { "DirectionalLightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, DirectionalLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DirectionalLightColor_MetaData), NewProp_DirectionalLightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_DirectionalLightIntensity = { "DirectionalLightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, DirectionalLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DirectionalLightIntensity_MetaData), NewProp_DirectionalLightIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_DirectionalLightRotation = { "DirectionalLightRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, DirectionalLightRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DirectionalLightRotation_MetaData), NewProp_DirectionalLightRotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_GlobalIlluminationIntensity = { "GlobalIlluminationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, GlobalIlluminationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalIlluminationIntensity_MetaData), NewProp_GlobalIlluminationIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_ReflectionIntensity = { "ReflectionIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, ReflectionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReflectionIntensity_MetaData), NewProp_ReflectionIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_bEnableVolumetricFog_SetBit(void* Obj)
{
	((FAuracronLumenLightingScenario*)Obj)->bEnableVolumetricFog = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_bEnableVolumetricFog = { "bEnableVolumetricFog", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLumenLightingScenario), &Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_bEnableVolumetricFog_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVolumetricFog_MetaData), NewProp_bEnableVolumetricFog_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_VolumetricFogColor = { "VolumetricFogColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLumenLightingScenario, VolumetricFogColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumetricFogColor_MetaData), NewProp_VolumetricFogColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_ScenarioName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_SkyLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_SkyLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_DirectionalLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_DirectionalLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_DirectionalLightRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_GlobalIlluminationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_ReflectionIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_bEnableVolumetricFog,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewProp_VolumetricFogColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
	nullptr,
	&NewStructOps,
	"AuracronLumenLightingScenario",
	Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::PropPointers),
	sizeof(FAuracronLumenLightingScenario),
	alignof(FAuracronLumenLightingScenario),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLumenLightingScenario()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLumenLightingScenario **************************************

// ********** Begin Delegate FOnLumenStateChanged **************************************************
struct Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics
{
	struct _Script_AuracronLumenBridge_eventOnLumenStateChanged_Parms
	{
		EAuracronLumenGIState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate Declarations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate Declarations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronLumenBridge_eventOnLumenStateChanged_Parms, NewState), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState, METADATA_PARAMS(0, nullptr) }; // 4120063982
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge, nullptr, "OnLumenStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLumenStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLumenStateChanged, EAuracronLumenGIState NewState)
{
	struct _Script_AuracronLumenBridge_eventOnLumenStateChanged_Parms
	{
		EAuracronLumenGIState NewState;
	};
	_Script_AuracronLumenBridge_eventOnLumenStateChanged_Parms Parms;
	Parms.NewState=NewState;
	OnLumenStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLumenStateChanged ****************************************************

// ********** Begin Delegate FOnLumenQualityChanged ************************************************
struct Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics
{
	struct _Script_AuracronLumenBridge_eventOnLumenQualityChanged_Parms
	{
		FAuracronLumenQualitySettings NewSettings;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewSettings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::NewProp_NewSettings = { "NewSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronLumenBridge_eventOnLumenQualityChanged_Parms, NewSettings), Z_Construct_UScriptStruct_FAuracronLumenQualitySettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSettings_MetaData), NewProp_NewSettings_MetaData) }; // 2549479459
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::NewProp_NewSettings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge, nullptr, "OnLumenQualityChanged__DelegateSignature", Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenQualityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenQualityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLumenQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLumenQualityChanged, FAuracronLumenQualitySettings const& NewSettings)
{
	struct _Script_AuracronLumenBridge_eventOnLumenQualityChanged_Parms
	{
		FAuracronLumenQualitySettings NewSettings;
	};
	_Script_AuracronLumenBridge_eventOnLumenQualityChanged_Parms Parms;
	Parms.NewSettings=NewSettings;
	OnLumenQualityChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLumenQualityChanged **************************************************

// ********** Begin Delegate FOnLumenMetricsUpdated ************************************************
struct Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics
{
	struct _Script_AuracronLumenBridge_eventOnLumenMetricsUpdated_Parms
	{
		FAuracronLumenMetrics Metrics;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Metrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::NewProp_Metrics = { "Metrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronLumenBridge_eventOnLumenMetricsUpdated_Parms, Metrics), Z_Construct_UScriptStruct_FAuracronLumenMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metrics_MetaData), NewProp_Metrics_MetaData) }; // 3340008787
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::NewProp_Metrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge, nullptr, "OnLumenMetricsUpdated__DelegateSignature", Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenMetricsUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenMetricsUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLumenMetricsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnLumenMetricsUpdated, FAuracronLumenMetrics const& Metrics)
{
	struct _Script_AuracronLumenBridge_eventOnLumenMetricsUpdated_Parms
	{
		FAuracronLumenMetrics Metrics;
	};
	_Script_AuracronLumenBridge_eventOnLumenMetricsUpdated_Parms Parms;
	Parms.Metrics=Metrics;
	OnLumenMetricsUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLumenMetricsUpdated **************************************************

// ********** Begin Delegate FOnLumenScenarioChanged ***********************************************
struct Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics
{
	struct _Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms
	{
		FString ScenarioName;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScenarioName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScenarioName;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::NewProp_ScenarioName = { "ScenarioName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms, ScenarioName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScenarioName_MetaData), NewProp_ScenarioName_MetaData) };
void Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms), &Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::NewProp_ScenarioName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronLumenBridge, nullptr, "OnLumenScenarioChanged__DelegateSignature", Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::_Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLumenScenarioChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLumenScenarioChanged, const FString& ScenarioName, bool bSuccess)
{
	struct _Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms
	{
		FString ScenarioName;
		bool bSuccess;
	};
	_Script_AuracronLumenBridge_eventOnLumenScenarioChanged_Parms Parms;
	Parms.ScenarioName=ScenarioName;
	Parms.bSuccess=bSuccess ? true : false;
	OnLumenScenarioChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLumenScenarioChanged *************************************************

// ********** Begin Class UAuracronLumenBridgeAPI Function ApplyLightingScenario *******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics
{
	struct AuracronLumenBridgeAPI_eventApplyLightingScenario_Parms
	{
		FString ScenarioName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Scenarios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply lighting scenario\n     * @param ScenarioName Name of scenario to apply\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply lighting scenario\n@param ScenarioName Name of scenario to apply\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScenarioName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScenarioName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::NewProp_ScenarioName = { "ScenarioName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventApplyLightingScenario_Parms, ScenarioName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScenarioName_MetaData), NewProp_ScenarioName_MetaData) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventApplyLightingScenario_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventApplyLightingScenario_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::NewProp_ScenarioName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ApplyLightingScenario", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::AuracronLumenBridgeAPI_eventApplyLightingScenario_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::AuracronLumenBridgeAPI_eventApplyLightingScenario_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execApplyLightingScenario)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScenarioName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyLightingScenario(Z_Param_ScenarioName);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ApplyLightingScenario *********************

// ********** Begin Class UAuracronLumenBridgeAPI Function ApplyQualityPreset **********************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics
{
	struct AuracronLumenBridgeAPI_eventApplyQualityPreset_Parms
	{
		EAuracronLumenQuality Quality;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply quality preset\n     * @param Quality Quality level to apply\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply quality preset\n@param Quality Quality level to apply\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventApplyQualityPreset_Parms, Quality), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenQuality, METADATA_PARAMS(0, nullptr) }; // 393568792
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventApplyQualityPreset_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventApplyQualityPreset_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ApplyQualityPreset", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::AuracronLumenBridgeAPI_eventApplyQualityPreset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::AuracronLumenBridgeAPI_eventApplyQualityPreset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execApplyQualityPreset)
{
	P_GET_ENUM(EAuracronLumenQuality,Z_Param_Quality);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyQualityPreset(EAuracronLumenQuality(Z_Param_Quality));
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ApplyQualityPreset ************************

// ********** Begin Class UAuracronLumenBridgeAPI Function ConfigureFinalGather ********************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics
{
	struct AuracronLumenBridgeAPI_eventConfigureFinalGather_Parms
	{
		int32 ImportanceSampleCount;
		float DownsampleFactor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configure Lumen Final Gather settings\n     * @param ImportanceSampleCount Number of importance samples\n     * @param DownsampleFactor Downsample factor for final gather\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_DownsampleFactor", "2.000000" },
		{ "CPP_Default_ImportanceSampleCount", "16" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure Lumen Final Gather settings\n@param ImportanceSampleCount Number of importance samples\n@param DownsampleFactor Downsample factor for final gather\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ImportanceSampleCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DownsampleFactor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_ImportanceSampleCount = { "ImportanceSampleCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureFinalGather_Parms, ImportanceSampleCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_DownsampleFactor = { "DownsampleFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureFinalGather_Parms, DownsampleFactor), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventConfigureFinalGather_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventConfigureFinalGather_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_ImportanceSampleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_DownsampleFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ConfigureFinalGather", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::AuracronLumenBridgeAPI_eventConfigureFinalGather_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::AuracronLumenBridgeAPI_eventConfigureFinalGather_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execConfigureFinalGather)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ImportanceSampleCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DownsampleFactor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureFinalGather(Z_Param_ImportanceSampleCount,Z_Param_DownsampleFactor);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ConfigureFinalGather **********************

// ********** Begin Class UAuracronLumenBridgeAPI Function ConfigureMeshCards **********************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics
{
	struct AuracronLumenBridgeAPI_eventConfigureMeshCards_Parms
	{
		int32 MeshCardsMaxLOD;
		int32 MeshCardsResolution;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configure Lumen Mesh Cards settings\n     * @param MeshCardsMaxLOD Maximum LOD for mesh cards\n     * @param MeshCardsResolution Resolution for mesh cards\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_MeshCardsMaxLOD", "3" },
		{ "CPP_Default_MeshCardsResolution", "512" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure Lumen Mesh Cards settings\n@param MeshCardsMaxLOD Maximum LOD for mesh cards\n@param MeshCardsResolution Resolution for mesh cards\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshCardsMaxLOD;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshCardsResolution;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_MeshCardsMaxLOD = { "MeshCardsMaxLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureMeshCards_Parms, MeshCardsMaxLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_MeshCardsResolution = { "MeshCardsResolution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureMeshCards_Parms, MeshCardsResolution), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventConfigureMeshCards_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventConfigureMeshCards_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_MeshCardsMaxLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_MeshCardsResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ConfigureMeshCards", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::AuracronLumenBridgeAPI_eventConfigureMeshCards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::AuracronLumenBridgeAPI_eventConfigureMeshCards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execConfigureMeshCards)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MeshCardsMaxLOD);
	P_GET_PROPERTY(FIntProperty,Z_Param_MeshCardsResolution);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureMeshCards(Z_Param_MeshCardsMaxLOD,Z_Param_MeshCardsResolution);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ConfigureMeshCards ************************

// ********** Begin Class UAuracronLumenBridgeAPI Function ConfigureRadianceCache ******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics
{
	struct AuracronLumenBridgeAPI_eventConfigureRadianceCache_Parms
	{
		float ProbeSpacing;
		int32 ProbeResolution;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configure Lumen Radiance Cache settings\n     * @param ProbeSpacing Spacing between radiance probes\n     * @param ProbeResolution Resolution of each probe\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_ProbeResolution", "32" },
		{ "CPP_Default_ProbeSpacing", "100.000000" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure Lumen Radiance Cache settings\n@param ProbeSpacing Spacing between radiance probes\n@param ProbeResolution Resolution of each probe\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProbeSpacing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ProbeResolution;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ProbeSpacing = { "ProbeSpacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureRadianceCache_Parms, ProbeSpacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ProbeResolution = { "ProbeResolution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureRadianceCache_Parms, ProbeResolution), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventConfigureRadianceCache_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventConfigureRadianceCache_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ProbeSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ProbeResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ConfigureRadianceCache", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::AuracronLumenBridgeAPI_eventConfigureRadianceCache_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::AuracronLumenBridgeAPI_eventConfigureRadianceCache_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execConfigureRadianceCache)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ProbeSpacing);
	P_GET_PROPERTY(FIntProperty,Z_Param_ProbeResolution);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureRadianceCache(Z_Param_ProbeSpacing,Z_Param_ProbeResolution);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ConfigureRadianceCache ********************

// ********** Begin Class UAuracronLumenBridgeAPI Function ConfigureSurfaceCache *******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics
{
	struct AuracronLumenBridgeAPI_eventConfigureSurfaceCache_Parms
	{
		int32 Resolution;
		int32 UpdateFrequency;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configure Lumen Surface Cache settings\n     * @param Resolution Surface cache resolution\n     * @param UpdateFrequency Update frequency in frames\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_Resolution", "1024" },
		{ "CPP_Default_UpdateFrequency", "4" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure Lumen Surface Cache settings\n@param Resolution Surface cache resolution\n@param UpdateFrequency Update frequency in frames\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Resolution;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpdateFrequency;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_Resolution = { "Resolution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureSurfaceCache_Parms, Resolution), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_UpdateFrequency = { "UpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureSurfaceCache_Parms, UpdateFrequency), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventConfigureSurfaceCache_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventConfigureSurfaceCache_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_Resolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_UpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ConfigureSurfaceCache", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::AuracronLumenBridgeAPI_eventConfigureSurfaceCache_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::AuracronLumenBridgeAPI_eventConfigureSurfaceCache_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execConfigureSurfaceCache)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Resolution);
	P_GET_PROPERTY(FIntProperty,Z_Param_UpdateFrequency);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureSurfaceCache(Z_Param_Resolution,Z_Param_UpdateFrequency);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ConfigureSurfaceCache *********************

// ********** Begin Class UAuracronLumenBridgeAPI Function ConfigureTranslucency *******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics
{
	struct AuracronLumenBridgeAPI_eventConfigureTranslucency_Parms
	{
		int32 VolumeResolution;
		int32 ReflectionSamples;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configure Lumen Translucency settings\n     * @param VolumeResolution Volume resolution for translucency\n     * @param ReflectionSamples Number of reflection samples\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_ReflectionSamples", "4" },
		{ "CPP_Default_VolumeResolution", "64" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure Lumen Translucency settings\n@param VolumeResolution Volume resolution for translucency\n@param ReflectionSamples Number of reflection samples\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_VolumeResolution;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReflectionSamples;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_VolumeResolution = { "VolumeResolution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureTranslucency_Parms, VolumeResolution), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_ReflectionSamples = { "ReflectionSamples", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventConfigureTranslucency_Parms, ReflectionSamples), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventConfigureTranslucency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventConfigureTranslucency_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_VolumeResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_ReflectionSamples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ConfigureTranslucency", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::AuracronLumenBridgeAPI_eventConfigureTranslucency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::AuracronLumenBridgeAPI_eventConfigureTranslucency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execConfigureTranslucency)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_VolumeResolution);
	P_GET_PROPERTY(FIntProperty,Z_Param_ReflectionSamples);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureTranslucency(Z_Param_VolumeResolution,Z_Param_ReflectionSamples);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ConfigureTranslucency *********************

// ********** Begin Class UAuracronLumenBridgeAPI Function CreateLightingScenario ******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics
{
	struct AuracronLumenBridgeAPI_eventCreateLightingScenario_Parms
	{
		FAuracronLumenLightingScenario Scenario;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Scenarios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create lighting scenario\n     * @param Scenario Scenario configuration\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create lighting scenario\n@param Scenario Scenario configuration\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scenario_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scenario;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::NewProp_Scenario = { "Scenario", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventCreateLightingScenario_Parms, Scenario), Z_Construct_UScriptStruct_FAuracronLumenLightingScenario, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scenario_MetaData), NewProp_Scenario_MetaData) }; // 411867718
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventCreateLightingScenario_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventCreateLightingScenario_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::NewProp_Scenario,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "CreateLightingScenario", Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::AuracronLumenBridgeAPI_eventCreateLightingScenario_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::AuracronLumenBridgeAPI_eventCreateLightingScenario_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execCreateLightingScenario)
{
	P_GET_STRUCT_REF(FAuracronLumenLightingScenario,Z_Param_Out_Scenario);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateLightingScenario(Z_Param_Out_Scenario);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function CreateLightingScenario ********************

// ********** Begin Class UAuracronLumenBridgeAPI Function EnablePerformanceMonitoring *************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics
{
	struct AuracronLumenBridgeAPI_eventEnablePerformanceMonitoring_Parms
	{
		bool bEnable;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable performance monitoring\n     * @param bEnable True to enable monitoring\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance monitoring\n@param bEnable True to enable monitoring\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventEnablePerformanceMonitoring_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventEnablePerformanceMonitoring_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventEnablePerformanceMonitoring_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventEnablePerformanceMonitoring_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_bEnable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "EnablePerformanceMonitoring", Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::AuracronLumenBridgeAPI_eventEnablePerformanceMonitoring_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::AuracronLumenBridgeAPI_eventEnablePerformanceMonitoring_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execEnablePerformanceMonitoring)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EnablePerformanceMonitoring(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function EnablePerformanceMonitoring ***************

// ********** Begin Class UAuracronLumenBridgeAPI Function GetAvailableLightingScenarios ***********
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics
{
	struct AuracronLumenBridgeAPI_eventGetAvailableLightingScenarios_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Scenarios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get all available lighting scenarios\n     * @return Array of scenario names\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get all available lighting scenarios\n@return Array of scenario names" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventGetAvailableLightingScenarios_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "GetAvailableLightingScenarios", Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::AuracronLumenBridgeAPI_eventGetAvailableLightingScenarios_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::AuracronLumenBridgeAPI_eventGetAvailableLightingScenarios_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execGetAvailableLightingScenarios)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAvailableLightingScenarios();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function GetAvailableLightingScenarios *************

// ********** Begin Class UAuracronLumenBridgeAPI Function GetDetailedLumenStatistics **************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics
{
	struct AuracronLumenBridgeAPI_eventGetDetailedLumenStatistics_Parms
	{
		FAuracronLumenMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get detailed Lumen performance statistics\n     * @return Detailed performance statistics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get detailed Lumen performance statistics\n@return Detailed performance statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventGetDetailedLumenStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLumenMetrics, METADATA_PARAMS(0, nullptr) }; // 3340008787
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "GetDetailedLumenStatistics", Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::AuracronLumenBridgeAPI_eventGetDetailedLumenStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::AuracronLumenBridgeAPI_eventGetDetailedLumenStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execGetDetailedLumenStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLumenMetrics*)Z_Param__Result=P_THIS->GetDetailedLumenStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function GetDetailedLumenStatistics ****************

// ********** Begin Class UAuracronLumenBridgeAPI Function GetLumenMetrics *************************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics
{
	struct AuracronLumenBridgeAPI_eventGetLumenMetrics_Parms
	{
		FAuracronLumenMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get current Lumen performance metrics\n     * @return Performance metrics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current Lumen performance metrics\n@return Performance metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventGetLumenMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLumenMetrics, METADATA_PARAMS(0, nullptr) }; // 3340008787
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "GetLumenMetrics", Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::AuracronLumenBridgeAPI_eventGetLumenMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::AuracronLumenBridgeAPI_eventGetLumenMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execGetLumenMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLumenMetrics*)Z_Param__Result=P_THIS->GetLumenMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function GetLumenMetrics ***************************

// ********** Begin Class UAuracronLumenBridgeAPI Function GetLumenQualitySettings *****************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics
{
	struct AuracronLumenBridgeAPI_eventGetLumenQualitySettings_Parms
	{
		FAuracronLumenQualitySettings ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get current Lumen quality settings\n     * @return Current quality settings\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current Lumen quality settings\n@return Current quality settings" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventGetLumenQualitySettings_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLumenQualitySettings, METADATA_PARAMS(0, nullptr) }; // 2549479459
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "GetLumenQualitySettings", Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::AuracronLumenBridgeAPI_eventGetLumenQualitySettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::AuracronLumenBridgeAPI_eventGetLumenQualitySettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execGetLumenQualitySettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLumenQualitySettings*)Z_Param__Result=P_THIS->GetLumenQualitySettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function GetLumenQualitySettings *******************

// ********** Begin Class UAuracronLumenBridgeAPI Function GetLumenState ***************************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics
{
	struct AuracronLumenBridgeAPI_eventGetLumenState_Parms
	{
		EAuracronLumenGIState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get current Lumen state\n     * @return Current state\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current Lumen state\n@return Current state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventGetLumenState_Parms, ReturnValue), Z_Construct_UEnum_AuracronLumenBridge_EAuracronLumenGIState, METADATA_PARAMS(0, nullptr) }; // 4120063982
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "GetLumenState", Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::AuracronLumenBridgeAPI_eventGetLumenState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::AuracronLumenBridgeAPI_eventGetLumenState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execGetLumenState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLumenGIState*)Z_Param__Result=P_THIS->GetLumenState();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function GetLumenState *****************************

// ********** Begin Class UAuracronLumenBridgeAPI Function GetRadianceCacheMemoryUsage *************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics
{
	struct AuracronLumenBridgeAPI_eventGetRadianceCacheMemoryUsage_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Radiance Cache memory usage in MB\n     * @return Memory usage in megabytes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Radiance Cache memory usage in MB\n@return Memory usage in megabytes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventGetRadianceCacheMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "GetRadianceCacheMemoryUsage", Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::AuracronLumenBridgeAPI_eventGetRadianceCacheMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::AuracronLumenBridgeAPI_eventGetRadianceCacheMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execGetRadianceCacheMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetRadianceCacheMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function GetRadianceCacheMemoryUsage ***************

// ********** Begin Class UAuracronLumenBridgeAPI Function GetSurfaceCacheMemoryUsage **************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics
{
	struct AuracronLumenBridgeAPI_eventGetSurfaceCacheMemoryUsage_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Surface Cache memory usage in MB\n     * @return Memory usage in megabytes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Surface Cache memory usage in MB\n@return Memory usage in megabytes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventGetSurfaceCacheMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "GetSurfaceCacheMemoryUsage", Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::AuracronLumenBridgeAPI_eventGetSurfaceCacheMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::AuracronLumenBridgeAPI_eventGetSurfaceCacheMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execGetSurfaceCacheMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetSurfaceCacheMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function GetSurfaceCacheMemoryUsage ****************

// ********** Begin Class UAuracronLumenBridgeAPI Function InitializeLumenSystem *******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics
{
	struct AuracronLumenBridgeAPI_eventInitializeLumenSystem_Parms
	{
		UWorld* World;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize Lumen system for the current world\n     * @param World Target world\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Lumen system for the current world\n@param World Target world\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventInitializeLumenSystem_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventInitializeLumenSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventInitializeLumenSystem_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "InitializeLumenSystem", Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::AuracronLumenBridgeAPI_eventInitializeLumenSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::AuracronLumenBridgeAPI_eventInitializeLumenSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execInitializeLumenSystem)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeLumenSystem(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function InitializeLumenSystem *********************

// ********** Begin Class UAuracronLumenBridgeAPI Function IsHardwareRayTracingAvailable ***********
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics
{
	struct AuracronLumenBridgeAPI_eventIsHardwareRayTracingAvailable_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if Hardware Ray Tracing is available\n     * @return True if available\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if Hardware Ray Tracing is available\n@return True if available" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventIsHardwareRayTracingAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventIsHardwareRayTracingAvailable_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "IsHardwareRayTracingAvailable", Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::AuracronLumenBridgeAPI_eventIsHardwareRayTracingAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::AuracronLumenBridgeAPI_eventIsHardwareRayTracingAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execIsHardwareRayTracingAvailable)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsHardwareRayTracingAvailable();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function IsHardwareRayTracingAvailable *************

// ********** Begin Class UAuracronLumenBridgeAPI Function IsLumenSupported ************************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics
{
	struct AuracronLumenBridgeAPI_eventIsLumenSupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if Lumen is supported on current platform\n     * @return True if supported\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if Lumen is supported on current platform\n@return True if supported" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventIsLumenSupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventIsLumenSupported_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "IsLumenSupported", Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::AuracronLumenBridgeAPI_eventIsLumenSupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::AuracronLumenBridgeAPI_eventIsLumenSupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execIsLumenSupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLumenSupported();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function IsLumenSupported **************************

// ********** Begin Class UAuracronLumenBridgeAPI Function RemoveLightingScenario ******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics
{
	struct AuracronLumenBridgeAPI_eventRemoveLightingScenario_Parms
	{
		FString ScenarioName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Scenarios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remove lighting scenario\n     * @param ScenarioName Name of scenario to remove\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove lighting scenario\n@param ScenarioName Name of scenario to remove\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScenarioName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScenarioName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::NewProp_ScenarioName = { "ScenarioName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventRemoveLightingScenario_Parms, ScenarioName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScenarioName_MetaData), NewProp_ScenarioName_MetaData) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventRemoveLightingScenario_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventRemoveLightingScenario_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::NewProp_ScenarioName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "RemoveLightingScenario", Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::AuracronLumenBridgeAPI_eventRemoveLightingScenario_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::AuracronLumenBridgeAPI_eventRemoveLightingScenario_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execRemoveLightingScenario)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScenarioName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveLightingScenario(Z_Param_ScenarioName);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function RemoveLightingScenario ********************

// ********** Begin Class UAuracronLumenBridgeAPI Function SetGlobalIlluminationIntensity **********
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics
{
	struct AuracronLumenBridgeAPI_eventSetGlobalIlluminationIntensity_Parms
	{
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set Global Illumination intensity\n     * @param Intensity GI intensity multiplier\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set Global Illumination intensity\n@param Intensity GI intensity multiplier\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventSetGlobalIlluminationIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetGlobalIlluminationIntensity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetGlobalIlluminationIntensity_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "SetGlobalIlluminationIntensity", Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::AuracronLumenBridgeAPI_eventSetGlobalIlluminationIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::AuracronLumenBridgeAPI_eventSetGlobalIlluminationIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execSetGlobalIlluminationIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetGlobalIlluminationIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function SetGlobalIlluminationIntensity ************

// ********** Begin Class UAuracronLumenBridgeAPI Function SetHardwareRayTracingEnabled ************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics
{
	struct AuracronLumenBridgeAPI_eventSetHardwareRayTracingEnabled_Parms
	{
		bool bEnable;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable/Disable Lumen Hardware Ray Tracing\n     * @param bEnable True to enable hardware ray tracing\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable/Disable Lumen Hardware Ray Tracing\n@param bEnable True to enable hardware ray tracing\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetHardwareRayTracingEnabled_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetHardwareRayTracingEnabled_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetHardwareRayTracingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetHardwareRayTracingEnabled_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_bEnable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "SetHardwareRayTracingEnabled", Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::AuracronLumenBridgeAPI_eventSetHardwareRayTracingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::AuracronLumenBridgeAPI_eventSetHardwareRayTracingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execSetHardwareRayTracingEnabled)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetHardwareRayTracingEnabled(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function SetHardwareRayTracingEnabled **************

// ********** Begin Class UAuracronLumenBridgeAPI Function SetLumenQualitySettings *****************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics
{
	struct AuracronLumenBridgeAPI_eventSetLumenQualitySettings_Parms
	{
		FAuracronLumenQualitySettings QualitySettings;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set Lumen quality settings\n     * @param QualitySettings New quality settings\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set Lumen quality settings\n@param QualitySettings New quality settings\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualitySettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_QualitySettings;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::NewProp_QualitySettings = { "QualitySettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventSetLumenQualitySettings_Parms, QualitySettings), Z_Construct_UScriptStruct_FAuracronLumenQualitySettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualitySettings_MetaData), NewProp_QualitySettings_MetaData) }; // 2549479459
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetLumenQualitySettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetLumenQualitySettings_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::NewProp_QualitySettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "SetLumenQualitySettings", Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::AuracronLumenBridgeAPI_eventSetLumenQualitySettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::AuracronLumenBridgeAPI_eventSetLumenQualitySettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execSetLumenQualitySettings)
{
	P_GET_STRUCT_REF(FAuracronLumenQualitySettings,Z_Param_Out_QualitySettings);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLumenQualitySettings(Z_Param_Out_QualitySettings);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function SetLumenQualitySettings *******************

// ********** Begin Class UAuracronLumenBridgeAPI Function SetLumenSceneViewDistance ***************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics
{
	struct AuracronLumenBridgeAPI_eventSetLumenSceneViewDistance_Parms
	{
		float ViewDistance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set Lumen Scene View Distance\n     * @param ViewDistance Maximum view distance for Lumen\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set Lumen Scene View Distance\n@param ViewDistance Maximum view distance for Lumen\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ViewDistance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::NewProp_ViewDistance = { "ViewDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventSetLumenSceneViewDistance_Parms, ViewDistance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetLumenSceneViewDistance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetLumenSceneViewDistance_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::NewProp_ViewDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "SetLumenSceneViewDistance", Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::AuracronLumenBridgeAPI_eventSetLumenSceneViewDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::AuracronLumenBridgeAPI_eventSetLumenSceneViewDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execSetLumenSceneViewDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ViewDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLumenSceneViewDistance(Z_Param_ViewDistance);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function SetLumenSceneViewDistance *****************

// ********** Begin Class UAuracronLumenBridgeAPI Function SetReflectionIntensity ******************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics
{
	struct AuracronLumenBridgeAPI_eventSetReflectionIntensity_Parms
	{
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set Reflection intensity\n     * @param Intensity Reflection intensity multiplier\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set Reflection intensity\n@param Intensity Reflection intensity multiplier\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLumenBridgeAPI_eventSetReflectionIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetReflectionIntensity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetReflectionIntensity_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "SetReflectionIntensity", Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::AuracronLumenBridgeAPI_eventSetReflectionIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::AuracronLumenBridgeAPI_eventSetReflectionIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execSetReflectionIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetReflectionIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function SetReflectionIntensity ********************

// ********** Begin Class UAuracronLumenBridgeAPI Function SetScreenProbeGatherEnabled *************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics
{
	struct AuracronLumenBridgeAPI_eventSetScreenProbeGatherEnabled_Parms
	{
		bool bEnable;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable/Disable Lumen Screen Probe Gather\n     * @param bEnable True to enable screen probe gather\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable/Disable Lumen Screen Probe Gather\n@param bEnable True to enable screen probe gather\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetScreenProbeGatherEnabled_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetScreenProbeGatherEnabled_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetScreenProbeGatherEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetScreenProbeGatherEnabled_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_bEnable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "SetScreenProbeGatherEnabled", Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::AuracronLumenBridgeAPI_eventSetScreenProbeGatherEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::AuracronLumenBridgeAPI_eventSetScreenProbeGatherEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execSetScreenProbeGatherEnabled)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetScreenProbeGatherEnabled(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function SetScreenProbeGatherEnabled ***************

// ********** Begin Class UAuracronLumenBridgeAPI Function SetTwoSidedFoliageEnabled ***************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics
{
	struct AuracronLumenBridgeAPI_eventSetTwoSidedFoliageEnabled_Parms
	{
		bool bEnable;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable/Disable Lumen Two Sided Foliage\n     * @param bEnable True to enable two sided foliage\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable/Disable Lumen Two Sided Foliage\n@param bEnable True to enable two sided foliage\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetTwoSidedFoliageEnabled_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetTwoSidedFoliageEnabled_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventSetTwoSidedFoliageEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventSetTwoSidedFoliageEnabled_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_bEnable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "SetTwoSidedFoliageEnabled", Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::AuracronLumenBridgeAPI_eventSetTwoSidedFoliageEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::AuracronLumenBridgeAPI_eventSetTwoSidedFoliageEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execSetTwoSidedFoliageEnabled)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetTwoSidedFoliageEnabled(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function SetTwoSidedFoliageEnabled *****************

// ********** Begin Class UAuracronLumenBridgeAPI Function ShutdownLumenSystem *********************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics
{
	struct AuracronLumenBridgeAPI_eventShutdownLumenSystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown Lumen system\n     * @return True if successful\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown Lumen system\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventShutdownLumenSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventShutdownLumenSystem_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "ShutdownLumenSystem", Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::AuracronLumenBridgeAPI_eventShutdownLumenSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::AuracronLumenBridgeAPI_eventShutdownLumenSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execShutdownLumenSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShutdownLumenSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function ShutdownLumenSystem ***********************

// ********** Begin Class UAuracronLumenBridgeAPI Function UpdateLumenScene ************************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics
{
	struct AuracronLumenBridgeAPI_eventUpdateLumenScene_Parms
	{
		bool bFullUpdate;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Force Lumen scene update\n     * @param bFullUpdate True for full scene rebuild\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bFullUpdate", "false" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Force Lumen scene update\n@param bFullUpdate True for full scene rebuild\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bFullUpdate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFullUpdate;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_bFullUpdate_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventUpdateLumenScene_Parms*)Obj)->bFullUpdate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_bFullUpdate = { "bFullUpdate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventUpdateLumenScene_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_bFullUpdate_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventUpdateLumenScene_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventUpdateLumenScene_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_bFullUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "UpdateLumenScene", Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::AuracronLumenBridgeAPI_eventUpdateLumenScene_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::AuracronLumenBridgeAPI_eventUpdateLumenScene_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execUpdateLumenScene)
{
	P_GET_UBOOL(Z_Param_bFullUpdate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateLumenScene(Z_Param_bFullUpdate);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function UpdateLumenScene **************************

// ********** Begin Class UAuracronLumenBridgeAPI Function UpdateLumenSceneCapture *****************
struct Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics
{
	struct AuracronLumenBridgeAPI_eventUpdateLumenSceneCapture_Parms
	{
		bool bFullCapture;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Lumen Bridge|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Force Lumen Scene Capture update\n     * @param bFullCapture True for full scene capture\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bFullCapture", "false" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Force Lumen Scene Capture update\n@param bFullCapture True for full scene capture\n@return True if successful" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bFullCapture_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFullCapture;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_bFullCapture_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventUpdateLumenSceneCapture_Parms*)Obj)->bFullCapture = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_bFullCapture = { "bFullCapture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventUpdateLumenSceneCapture_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_bFullCapture_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLumenBridgeAPI_eventUpdateLumenSceneCapture_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLumenBridgeAPI_eventUpdateLumenSceneCapture_Parms), &Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_bFullCapture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLumenBridgeAPI, nullptr, "UpdateLumenSceneCapture", Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::AuracronLumenBridgeAPI_eventUpdateLumenSceneCapture_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::AuracronLumenBridgeAPI_eventUpdateLumenSceneCapture_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLumenBridgeAPI::execUpdateLumenSceneCapture)
{
	P_GET_UBOOL(Z_Param_bFullCapture);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateLumenSceneCapture(Z_Param_bFullCapture);
	P_NATIVE_END;
}
// ********** End Class UAuracronLumenBridgeAPI Function UpdateLumenSceneCapture *******************

// ********** Begin Class UAuracronLumenBridgeAPI **************************************************
void UAuracronLumenBridgeAPI::StaticRegisterNativesUAuracronLumenBridgeAPI()
{
	UClass* Class = UAuracronLumenBridgeAPI::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyLightingScenario", &UAuracronLumenBridgeAPI::execApplyLightingScenario },
		{ "ApplyQualityPreset", &UAuracronLumenBridgeAPI::execApplyQualityPreset },
		{ "ConfigureFinalGather", &UAuracronLumenBridgeAPI::execConfigureFinalGather },
		{ "ConfigureMeshCards", &UAuracronLumenBridgeAPI::execConfigureMeshCards },
		{ "ConfigureRadianceCache", &UAuracronLumenBridgeAPI::execConfigureRadianceCache },
		{ "ConfigureSurfaceCache", &UAuracronLumenBridgeAPI::execConfigureSurfaceCache },
		{ "ConfigureTranslucency", &UAuracronLumenBridgeAPI::execConfigureTranslucency },
		{ "CreateLightingScenario", &UAuracronLumenBridgeAPI::execCreateLightingScenario },
		{ "EnablePerformanceMonitoring", &UAuracronLumenBridgeAPI::execEnablePerformanceMonitoring },
		{ "GetAvailableLightingScenarios", &UAuracronLumenBridgeAPI::execGetAvailableLightingScenarios },
		{ "GetDetailedLumenStatistics", &UAuracronLumenBridgeAPI::execGetDetailedLumenStatistics },
		{ "GetLumenMetrics", &UAuracronLumenBridgeAPI::execGetLumenMetrics },
		{ "GetLumenQualitySettings", &UAuracronLumenBridgeAPI::execGetLumenQualitySettings },
		{ "GetLumenState", &UAuracronLumenBridgeAPI::execGetLumenState },
		{ "GetRadianceCacheMemoryUsage", &UAuracronLumenBridgeAPI::execGetRadianceCacheMemoryUsage },
		{ "GetSurfaceCacheMemoryUsage", &UAuracronLumenBridgeAPI::execGetSurfaceCacheMemoryUsage },
		{ "InitializeLumenSystem", &UAuracronLumenBridgeAPI::execInitializeLumenSystem },
		{ "IsHardwareRayTracingAvailable", &UAuracronLumenBridgeAPI::execIsHardwareRayTracingAvailable },
		{ "IsLumenSupported", &UAuracronLumenBridgeAPI::execIsLumenSupported },
		{ "RemoveLightingScenario", &UAuracronLumenBridgeAPI::execRemoveLightingScenario },
		{ "SetGlobalIlluminationIntensity", &UAuracronLumenBridgeAPI::execSetGlobalIlluminationIntensity },
		{ "SetHardwareRayTracingEnabled", &UAuracronLumenBridgeAPI::execSetHardwareRayTracingEnabled },
		{ "SetLumenQualitySettings", &UAuracronLumenBridgeAPI::execSetLumenQualitySettings },
		{ "SetLumenSceneViewDistance", &UAuracronLumenBridgeAPI::execSetLumenSceneViewDistance },
		{ "SetReflectionIntensity", &UAuracronLumenBridgeAPI::execSetReflectionIntensity },
		{ "SetScreenProbeGatherEnabled", &UAuracronLumenBridgeAPI::execSetScreenProbeGatherEnabled },
		{ "SetTwoSidedFoliageEnabled", &UAuracronLumenBridgeAPI::execSetTwoSidedFoliageEnabled },
		{ "ShutdownLumenSystem", &UAuracronLumenBridgeAPI::execShutdownLumenSystem },
		{ "UpdateLumenScene", &UAuracronLumenBridgeAPI::execUpdateLumenScene },
		{ "UpdateLumenSceneCapture", &UAuracronLumenBridgeAPI::execUpdateLumenSceneCapture },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronLumenBridgeAPI;
UClass* UAuracronLumenBridgeAPI::GetPrivateStaticClass()
{
	using TClass = UAuracronLumenBridgeAPI;
	if (!Z_Registration_Info_UClass_UAuracronLumenBridgeAPI.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronLumenBridgeAPI"),
			Z_Registration_Info_UClass_UAuracronLumenBridgeAPI.InnerSingleton,
			StaticRegisterNativesUAuracronLumenBridgeAPI,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronLumenBridgeAPI.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronLumenBridgeAPI_NoRegister()
{
	return UAuracronLumenBridgeAPI::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Main API class for Lumen Bridge\n * Exposes UE5.6 Lumen Global Illumination functionality to Python\n */" },
#endif
		{ "IncludePath", "AuracronLumenBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main API class for Lumen Bridge\nExposes UE5.6 Lumen Global Illumination functionality to Python" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLumenStateChanged_MetaData[] = {
		{ "Category", "Lumen Bridge|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === DELEGATES ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== DELEGATES ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLumenQualityChanged_MetaData[] = {
		{ "Category", "Lumen Bridge|Events" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLumenMetricsUpdated_MetaData[] = {
		{ "Category", "Lumen Bridge|Events" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLumenScenarioChanged_MetaData[] = {
		{ "Category", "Lumen Bridge|Events" },
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWorld_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current world reference */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLumenBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current world reference" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLumenStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLumenQualityChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLumenMetricsUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLumenScenarioChanged;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyLightingScenario, "ApplyLightingScenario" }, // 3660179441
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ApplyQualityPreset, "ApplyQualityPreset" }, // 3345202095
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureFinalGather, "ConfigureFinalGather" }, // 2067421197
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureMeshCards, "ConfigureMeshCards" }, // 1094421805
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureRadianceCache, "ConfigureRadianceCache" }, // 2361679582
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureSurfaceCache, "ConfigureSurfaceCache" }, // 3955373222
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ConfigureTranslucency, "ConfigureTranslucency" }, // 3254809270
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_CreateLightingScenario, "CreateLightingScenario" }, // 3026762237
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_EnablePerformanceMonitoring, "EnablePerformanceMonitoring" }, // 2733564473
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetAvailableLightingScenarios, "GetAvailableLightingScenarios" }, // 1142827930
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetDetailedLumenStatistics, "GetDetailedLumenStatistics" }, // 4224618260
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenMetrics, "GetLumenMetrics" }, // 2439038788
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenQualitySettings, "GetLumenQualitySettings" }, // 2535105545
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetLumenState, "GetLumenState" }, // 170951372
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetRadianceCacheMemoryUsage, "GetRadianceCacheMemoryUsage" }, // 247764159
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_GetSurfaceCacheMemoryUsage, "GetSurfaceCacheMemoryUsage" }, // 2681418349
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_InitializeLumenSystem, "InitializeLumenSystem" }, // 3316008895
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsHardwareRayTracingAvailable, "IsHardwareRayTracingAvailable" }, // 2815265000
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_IsLumenSupported, "IsLumenSupported" }, // 2004554605
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_RemoveLightingScenario, "RemoveLightingScenario" }, // 1877334838
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetGlobalIlluminationIntensity, "SetGlobalIlluminationIntensity" }, // 2193214288
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetHardwareRayTracingEnabled, "SetHardwareRayTracingEnabled" }, // 968506522
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenQualitySettings, "SetLumenQualitySettings" }, // 918891509
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetLumenSceneViewDistance, "SetLumenSceneViewDistance" }, // 1221602723
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetReflectionIntensity, "SetReflectionIntensity" }, // 4207867172
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetScreenProbeGatherEnabled, "SetScreenProbeGatherEnabled" }, // 3868081702
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_SetTwoSidedFoliageEnabled, "SetTwoSidedFoliageEnabled" }, // 1247751132
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_ShutdownLumenSystem, "ShutdownLumenSystem" }, // 645753480
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenScene, "UpdateLumenScene" }, // 1615469363
		{ &Z_Construct_UFunction_UAuracronLumenBridgeAPI_UpdateLumenSceneCapture, "UpdateLumenSceneCapture" }, // 4075801073
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronLumenBridgeAPI>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenStateChanged = { "OnLumenStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLumenBridgeAPI, OnLumenStateChanged), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLumenStateChanged_MetaData), NewProp_OnLumenStateChanged_MetaData) }; // 3763547044
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenQualityChanged = { "OnLumenQualityChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLumenBridgeAPI, OnLumenQualityChanged), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenQualityChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLumenQualityChanged_MetaData), NewProp_OnLumenQualityChanged_MetaData) }; // 3593159117
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenMetricsUpdated = { "OnLumenMetricsUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLumenBridgeAPI, OnLumenMetricsUpdated), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenMetricsUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLumenMetricsUpdated_MetaData), NewProp_OnLumenMetricsUpdated_MetaData) }; // 4238407553
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenScenarioChanged = { "OnLumenScenarioChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLumenBridgeAPI, OnLumenScenarioChanged), Z_Construct_UDelegateFunction_AuracronLumenBridge_OnLumenScenarioChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLumenScenarioChanged_MetaData), NewProp_OnLumenScenarioChanged_MetaData) }; // 1414513444
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_CurrentWorld = { "CurrentWorld", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLumenBridgeAPI, CurrentWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWorld_MetaData), NewProp_CurrentWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenQualityChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenMetricsUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_OnLumenScenarioChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::NewProp_CurrentWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronLumenBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::ClassParams = {
	&UAuracronLumenBridgeAPI::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronLumenBridgeAPI()
{
	if (!Z_Registration_Info_UClass_UAuracronLumenBridgeAPI.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronLumenBridgeAPI.OuterSingleton, Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronLumenBridgeAPI.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronLumenBridgeAPI);
UAuracronLumenBridgeAPI::~UAuracronLumenBridgeAPI() {}
// ********** End Class UAuracronLumenBridgeAPI ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronLumenQuality_StaticEnum, TEXT("EAuracronLumenQuality"), &Z_Registration_Info_UEnum_EAuracronLumenQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 393568792U) },
		{ EAuracronLumenRayTracingMode_StaticEnum, TEXT("EAuracronLumenRayTracingMode"), &Z_Registration_Info_UEnum_EAuracronLumenRayTracingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1926814625U) },
		{ EAuracronLumenSceneMode_StaticEnum, TEXT("EAuracronLumenSceneMode"), &Z_Registration_Info_UEnum_EAuracronLumenSceneMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1806346539U) },
		{ EAuracronLumenReflectionQuality_StaticEnum, TEXT("EAuracronLumenReflectionQuality"), &Z_Registration_Info_UEnum_EAuracronLumenReflectionQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3345947925U) },
		{ EAuracronLumenGIState_StaticEnum, TEXT("EAuracronLumenGIState"), &Z_Registration_Info_UEnum_EAuracronLumenGIState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4120063982U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLumenMetrics::StaticStruct, Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics::NewStructOps, TEXT("AuracronLumenMetrics"), &Z_Registration_Info_UScriptStruct_FAuracronLumenMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLumenMetrics), 3340008787U) },
		{ FAuracronLumenQualitySettings::StaticStruct, Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics::NewStructOps, TEXT("AuracronLumenQualitySettings"), &Z_Registration_Info_UScriptStruct_FAuracronLumenQualitySettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLumenQualitySettings), 2549479459U) },
		{ FAuracronLumenLightingScenario::StaticStruct, Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics::NewStructOps, TEXT("AuracronLumenLightingScenario"), &Z_Registration_Info_UScriptStruct_FAuracronLumenLightingScenario, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLumenLightingScenario), 411867718U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronLumenBridgeAPI, UAuracronLumenBridgeAPI::StaticClass, TEXT("UAuracronLumenBridgeAPI"), &Z_Registration_Info_UClass_UAuracronLumenBridgeAPI, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronLumenBridgeAPI), 1578052857U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_2946208650(TEXT("/Script/AuracronLumenBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h__Script_AuracronLumenBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
