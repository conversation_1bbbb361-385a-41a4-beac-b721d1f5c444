// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Bridge Implementation
// Bridge 3.1: World Partition - Core Setup

#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionBridge.h"

// World Partition includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionRuntimeHash.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/WorldSettings.h"
#include "HAL/PlatformMemory.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "DrawDebugHelpers.h"

// =============================================================================
// STREAMING STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronStreamingStatistics::UpdateCalculatedFields()
{
    if (TotalCells > 0)
    {
        StreamingEfficiency = static_cast<float>(LoadedCells) / static_cast<float>(TotalCells);
    }
    else
    {
        StreamingEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION LOGGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionLogger* UAuracronWorldPartitionLogger::Instance = nullptr;

UAuracronWorldPartitionLogger* UAuracronWorldPartitionLogger::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionLogger>();
        Instance->AddToRoot(); // Prevent garbage collection
        
        // Initialize with default enabled categories
        Instance->EnabledCategories.Add(TEXT("WorldPartition"));
        Instance->EnabledCategories.Add(TEXT("Streaming"));
        Instance->EnabledCategories.Add(TEXT("DataLayers"));
    }
    return Instance;
}

void UAuracronWorldPartitionLogger::LogMessage(EAuracronWorldPartitionLogLevel Level, const FString& Message, const FString& Category)
{
    if (!ShouldLog(Level) || !IsCategoryEnabled(Category))
    {
        return;
    }

    FScopeLock Lock(&LogLock);
    
    FString FormattedMessage = FormatLogMessage(Level, Message, Category);
    LogHistory.Add(FormattedMessage);
    
    // Keep only recent logs (last 1000)
    if (LogHistory.Num() > 1000)
    {
        LogHistory.RemoveAt(0);
    }
    
    // Output to UE log system
    switch (Level)
    {
        case EAuracronWorldPartitionLogLevel::Error:
            UE_LOG(LogTemp, Error, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::Warning:
            UE_LOG(LogTemp, Warning, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::Log:
            UE_LOG(LogTemp, Log, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::Verbose:
            UE_LOG(LogTemp, Verbose, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::VeryVerbose:
            UE_LOG(LogTemp, VeryVerbose, TEXT("%s"), *FormattedMessage);
            break;
        default:
            break;
    }
}

void UAuracronWorldPartitionLogger::LogError(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Error, Message, Category);
}

void UAuracronWorldPartitionLogger::LogWarning(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Warning, Message, Category);
}

void UAuracronWorldPartitionLogger::LogInfo(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Log, Message, Category);
}

void UAuracronWorldPartitionLogger::LogVerbose(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Verbose, Message, Category);
}

void UAuracronWorldPartitionLogger::SetLogLevel(EAuracronWorldPartitionLogLevel Level)
{
    CurrentLogLevel = Level;
}

EAuracronWorldPartitionLogLevel UAuracronWorldPartitionLogger::GetLogLevel() const
{
    return CurrentLogLevel;
}

void UAuracronWorldPartitionLogger::EnableCategoryLogging(const FString& Category, bool bEnabled)
{
    if (bEnabled)
    {
        EnabledCategories.Add(Category);
    }
    else
    {
        EnabledCategories.Remove(Category);
    }
}

bool UAuracronWorldPartitionLogger::IsCategoryEnabled(const FString& Category) const
{
    return EnabledCategories.Contains(Category);
}

void UAuracronWorldPartitionLogger::ClearLogs()
{
    FScopeLock Lock(&LogLock);
    LogHistory.Empty();
}

TArray<FString> UAuracronWorldPartitionLogger::GetRecentLogs(int32 MaxCount) const
{
    FScopeLock Lock(&LogLock);
    
    TArray<FString> RecentLogs;
    int32 StartIndex = FMath::Max(0, LogHistory.Num() - MaxCount);
    
    for (int32 i = StartIndex; i < LogHistory.Num(); i++)
    {
        RecentLogs.Add(LogHistory[i]);
    }
    
    return RecentLogs;
}

void UAuracronWorldPartitionLogger::SaveLogsToFile(const FString& FilePath) const
{
    FScopeLock Lock(&LogLock);
    
    FString LogContent;
    for (const FString& LogEntry : LogHistory)
    {
        LogContent += LogEntry + TEXT("\n");
    }
    
    FFileHelper::SaveStringToFile(LogContent, *FilePath);
}

bool UAuracronWorldPartitionLogger::ShouldLog(EAuracronWorldPartitionLogLevel Level) const
{
    return Level <= CurrentLogLevel;
}

FString UAuracronWorldPartitionLogger::FormatLogMessage(EAuracronWorldPartitionLogLevel Level, const FString& Message, const FString& Category) const
{
    FString LevelString = LogLevelToString(Level);
    FString TimeString = FDateTime::Now().ToString(TEXT("%H:%M:%S"));
    
    return FString::Printf(TEXT("[%s][%s][%s] %s"), *TimeString, *LevelString, *Category, *Message);
}

FString UAuracronWorldPartitionLogger::LogLevelToString(EAuracronWorldPartitionLogLevel Level) const
{
    switch (Level)
    {
        case EAuracronWorldPartitionLogLevel::Error: return TEXT("ERROR");
        case EAuracronWorldPartitionLogLevel::Warning: return TEXT("WARN");
        case EAuracronWorldPartitionLogLevel::Log: return TEXT("INFO");
        case EAuracronWorldPartitionLogLevel::Verbose: return TEXT("VERB");
        case EAuracronWorldPartitionLogLevel::VeryVerbose: return TEXT("VVERB");
        default: return TEXT("NONE");
    }
}

// =============================================================================
// WORLD PARTITION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionManager* UAuracronWorldPartitionManager::GetInstance()
{
    if (GEngine)
    {
        return GEngine->GetEngineSubsystem<UAuracronWorldPartitionManager>();
    }
    return nullptr;
}

void UAuracronWorldPartitionManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    Logger = UAuracronWorldPartitionLogger::GetInstance();
    CurrentState = EAuracronWorldPartitionState::Uninitialized;
    
    AURACRON_WP_LOG_INFO(TEXT("World Partition Manager initialized"));
}

void UAuracronWorldPartitionManager::Deinitialize()
{
    if (bIsInitialized)
    {
        ShutdownWorldPartition();
    }
    
    Super::Deinitialize();
    AURACRON_WP_LOG_INFO(TEXT("World Partition Manager deinitialized"));
}

void UAuracronWorldPartitionManager::InitializeWorldPartition(UWorld* World, const FAuracronWorldPartitionConfiguration& InConfiguration)
{
    if (!World)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot initialize World Partition: World is null"));
        return;
    }

    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("World Partition already initialized"));
        return;
    }

    CurrentState = EAuracronWorldPartitionState::Initializing;
    Configuration = InConfiguration;
    ManagedWorld = World;

    // Validate configuration
    ValidateConfiguration();

    // Setup world partition
    if (!SetupWorldPartition(World))
    {
        CurrentState = EAuracronWorldPartitionState::Error;
        AURACRON_WP_LOG_ERROR(TEXT("Failed to setup World Partition"));
        return;
    }

    // Initialize statistics
    Statistics = FAuracronStreamingStatistics();
    
    bIsInitialized = true;
    CurrentState = EAuracronWorldPartitionState::Ready;
    
    AURACRON_WP_LOG_INFO(TEXT("World Partition initialized successfully for world: %s"), *World->GetName());
}

void UAuracronWorldPartitionManager::ShutdownWorldPartition()
{
    if (!bIsInitialized)
    {
        return;
    }

    CurrentState = EAuracronWorldPartitionState::Shutdown;
    
    // Clear cell tracking
    CellInfoMap.Empty();
    LoadedCells.Empty();
    StreamingCells.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    WorldPartition.Reset();
    
    bIsInitialized = false;
    CurrentState = EAuracronWorldPartitionState::Uninitialized;
    
    AURACRON_WP_LOG_INFO(TEXT("World Partition shutdown completed"));
}

bool UAuracronWorldPartitionManager::IsInitialized() const
{
    return bIsInitialized;
}

EAuracronWorldPartitionState UAuracronWorldPartitionManager::GetState() const
{
    return CurrentState;
}

bool UAuracronWorldPartitionManager::SetupWorldPartition(UWorld* World)
{
    if (!World)
    {
        return false;
    }

    // Get or create world partition
    UWorldPartition* WP = World->GetWorldPartition();
    if (!WP)
    {
        AURACRON_WP_LOG_WARNING(TEXT("World does not have World Partition enabled"));
        return false;
    }

    WorldPartition = WP;
    
    // Verify world partition is initialized
    if (!WP->IsInitialized())
    {
        AURACRON_WP_LOG_ERROR(TEXT("World Partition is not initialized"));
        return false;
    }

    AURACRON_WP_LOG_INFO(TEXT("World Partition setup completed"));
    return true;
}

UWorldPartition* UAuracronWorldPartitionManager::GetWorldPartition() const
{
    return WorldPartition.Get();
}

UDataLayerSubsystem* UAuracronWorldPartitionManager::GetDataLayerSubsystem() const
{
    if (UWorld* World = ManagedWorld.Get())
    {
        return World->GetSubsystem<UDataLayerSubsystem>();
    }
    return nullptr;
}

UWorldPartitionSubsystem* UAuracronWorldPartitionManager::GetWorldPartitionSubsystem() const
{
    if (UWorld* World = ManagedWorld.Get())
    {
        return World->GetSubsystem<UWorldPartitionSubsystem>();
    }
    return nullptr;
}

void UAuracronWorldPartitionManager::ValidateConfiguration()
{
    // Validate streaming distances
    if (Configuration.StreamingDistance <= 0.0f)
    {
        Configuration.StreamingDistance = 10000.0f;
        AURACRON_WP_LOG_WARNING(TEXT("Invalid streaming distance, set to default: %.1f"), Configuration.StreamingDistance);
    }

    if (Configuration.UnloadingDistance <= Configuration.StreamingDistance)
    {
        Configuration.UnloadingDistance = Configuration.StreamingDistance * 1.5f;
        AURACRON_WP_LOG_WARNING(TEXT("Unloading distance adjusted to: %.1f"), Configuration.UnloadingDistance);
    }

    // Validate grid sizes
    Configuration.DefaultGridSize = FMath::Clamp(Configuration.DefaultGridSize, Configuration.MinGridSize, Configuration.MaxGridSize);
    
    // Validate concurrent requests
    if (Configuration.MaxConcurrentStreamingRequests <= 0)
    {
        Configuration.MaxConcurrentStreamingRequests = 8;
        AURACRON_WP_LOG_WARNING(TEXT("Invalid max concurrent requests, set to default: %d"), Configuration.MaxConcurrentStreamingRequests);
    }
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetAllCells() const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> AllCells;
    CellInfoMap.GenerateValueArray(AllCells);
    return AllCells;
}

FAuracronCellInfo UAuracronWorldPartitionManager::GetCellInfo(const FString& CellId) const
{
    FScopeLock Lock(&ManagerLock);

    const FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
    if (CellInfo)
    {
        return *CellInfo;
    }

    return FAuracronCellInfo();
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetCellsInRadius(const FVector& Location, float Radius) const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> CellsInRadius;

    for (const auto& CellPair : CellInfoMap)
    {
        const FAuracronCellInfo& CellInfo = CellPair.Value;
        FVector CellCenter = CellInfo.CellBounds.GetCenter();

        if (FVector::Dist(Location, CellCenter) <= Radius)
        {
            CellsInRadius.Add(CellInfo);
        }
    }

    return CellsInRadius;
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetLoadedCells() const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> LoadedCellInfos;

    for (const FString& CellId : LoadedCells)
    {
        const FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
        if (CellInfo)
        {
            LoadedCellInfos.Add(*CellInfo);
        }
    }

    return LoadedCellInfos;
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetStreamingCells() const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> StreamingCellInfos;

    for (const FString& CellId : StreamingCells)
    {
        const FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
        if (CellInfo)
        {
            StreamingCellInfos.Add(*CellInfo);
        }
    }

    return StreamingCellInfos;
}

void UAuracronWorldPartitionManager::RequestCellLoading(const FString& CellId)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot request cell loading: Manager not initialized"));
        return;
    }

    FScopeLock Lock(&ManagerLock);

    if (LoadedCells.Contains(CellId))
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Cell already loaded: %s"), *CellId);
        return;
    }

    if (StreamingCells.Contains(CellId))
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Cell already streaming: %s"), *CellId);
        return;
    }

    // Add to streaming set
    StreamingCells.Add(CellId);

    // Update cell info
    FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
    if (CellInfo)
    {
        CellInfo->StreamingState = EAuracronStreamingState::Loading;
        CellInfo->LastAccessTime = FDateTime::Now();
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Requested cell loading: %s"), *CellId);

    // In a real implementation, this would trigger actual streaming
    // For now, we simulate immediate loading
    FDateTime LoadStartTime = FDateTime::Now();

    // Simulate loading time
    float SimulatedLoadTime = FMath::RandRange(0.1f, 2.0f);

    // Move from streaming to loaded
    StreamingCells.Remove(CellId);
    LoadedCells.Add(CellId);

    if (CellInfo)
    {
        CellInfo->StreamingState = EAuracronStreamingState::Loaded;
        CellInfo->LoadingTime = SimulatedLoadTime;
    }

    OnCellLoadedInternal(CellId, SimulatedLoadTime);
}

void UAuracronWorldPartitionManager::RequestCellUnloading(const FString& CellId)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot request cell unloading: Manager not initialized"));
        return;
    }

    FScopeLock Lock(&ManagerLock);

    if (!LoadedCells.Contains(CellId))
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Cell not loaded: %s"), *CellId);
        return;
    }

    // Remove from loaded set
    LoadedCells.Remove(CellId);

    // Update cell info
    FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
    if (CellInfo)
    {
        CellInfo->StreamingState = EAuracronStreamingState::Unloaded;
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Requested cell unloading: %s"), *CellId);

    // Simulate unloading time
    float SimulatedUnloadTime = FMath::RandRange(0.05f, 0.5f);

    OnCellUnloadedInternal(CellId, SimulatedUnloadTime);
}

void UAuracronWorldPartitionManager::SetConfiguration(const FAuracronWorldPartitionConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Configuration updated"));
}

FAuracronWorldPartitionConfiguration UAuracronWorldPartitionManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronWorldPartitionManager::SetStreamingDistance(float Distance)
{
    Configuration.StreamingDistance = FMath::Max(0.0f, Distance);

    // Adjust unloading distance if necessary
    if (Configuration.UnloadingDistance <= Configuration.StreamingDistance)
    {
        Configuration.UnloadingDistance = Configuration.StreamingDistance * 1.5f;
    }

    AURACRON_WP_LOG_INFO(TEXT("Streaming distance set to: %.1f"), Configuration.StreamingDistance);
}

float UAuracronWorldPartitionManager::GetStreamingDistance() const
{
    return Configuration.StreamingDistance;
}

FAuracronStreamingStatistics UAuracronWorldPartitionManager::GetStreamingStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronStreamingStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronStreamingStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Statistics reset"));
}

float UAuracronWorldPartitionManager::GetMemoryUsage() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.TotalMemoryUsageMB;
}

int32 UAuracronWorldPartitionManager::GetLoadedCellCount() const
{
    FScopeLock Lock(&ManagerLock);
    return LoadedCells.Num();
}

int32 UAuracronWorldPartitionManager::GetTotalCellCount() const
{
    FScopeLock Lock(&ManagerLock);
    return CellInfoMap.Num();
}

void UAuracronWorldPartitionManager::OnCellLoadedInternal(const FString& CellId, float LoadingTime)
{
    // Update statistics
    {
        FScopeLock Lock(&StatisticsLock);
        Statistics.LoadedCells++;
        Statistics.StreamingRequests++;
        Statistics.AverageLoadingTime = (Statistics.AverageLoadingTime + LoadingTime) / 2.0f;
    }

    // Broadcast event
    OnCellLoaded.Broadcast(CellId, LoadingTime);

    AURACRON_WP_LOG_VERBOSE(TEXT("Cell loaded: %s (%.3fs)"), *CellId, LoadingTime);
}

void UAuracronWorldPartitionManager::OnCellUnloadedInternal(const FString& CellId, float UnloadingTime)
{
    // Update statistics
    {
        FScopeLock Lock(&StatisticsLock);
        Statistics.LoadedCells = FMath::Max(0, Statistics.LoadedCells - 1);
    }

    // Broadcast event
    OnCellUnloaded.Broadcast(CellId, UnloadingTime);

    AURACRON_WP_LOG_VERBOSE(TEXT("Cell unloaded: %s (%.3fs)"), *CellId, UnloadingTime);
}

FString UAuracronWorldPartitionManager::GenerateCellId(const FIntVector& Coordinates) const
{
    return FString::Printf(TEXT("Cell_%d_%d_%d"), Coordinates.X, Coordinates.Y, Coordinates.Z);
}

FAuracronCellInfo UAuracronWorldPartitionManager::CreateCellInfo(const FIntVector& Coordinates) const
{
    FAuracronCellInfo CellInfo;
    CellInfo.CellId = GenerateCellId(Coordinates);
    CellInfo.CellCoordinates = Coordinates;

    // Calculate cell bounds based on grid size
    float GridSize = static_cast<float>(Configuration.DefaultGridSize);
    FVector CellMin = FVector(Coordinates) * GridSize;
    FVector CellMax = CellMin + FVector(GridSize);
    CellInfo.CellBounds = FBox(CellMin, CellMax);

    CellInfo.CellType = EAuracronCellType::Static;
    CellInfo.StreamingState = EAuracronStreamingState::Unloaded;

    return CellInfo;
}
