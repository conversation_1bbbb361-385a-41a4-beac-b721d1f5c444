name: AURACRON CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  UE_VERSION: "5.6"
  PROJECT_NAME: "Auracron"
  BUILD_CONFIGURATION: "Development"

jobs:
  # Análise de código Python
  python-analysis:
    runs-on: ubuntu-latest
    name: Python Code Analysis
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11.8'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black mypy pylint
        
    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 Scripts/Python --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings
        flake8 Scripts/Python --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: Check code formatting with black
      run: |
        black --check Scripts/Python
        
    - name: Type checking with mypy
      run: |
        mypy Scripts/Python --ignore-missing-imports
        
    - name: Lint with pylint
      run: |
        pylint Scripts/Python --disable=C0114,C0115,C0116 --exit-zero

  # Build do projeto C++
  cpp-build:
    runs-on: windows-latest
    name: C++ Build and Test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1.3
      
    - name: Setup Unreal Engine
      # Nota: Este step precisaria ser configurado com acesso ao UE5.6
      # Por enquanto, apenas um placeholder
      run: |
        echo "Setting up Unreal Engine 5.6..."
        echo "This would download and setup UE5.6 in a real environment"
        
    - name: Generate Project Files
      run: |
        echo "Generating project files..."
        # UnrealBuildTool.exe -projectfiles -project="Auracron.uproject" -game -rocket -progress
        
    - name: Build Project
      run: |
        echo "Building project..."
        # msbuild Auracron.sln /p:Configuration=Development /p:Platform=Win64
        
    - name: Run C++ Tests
      run: |
        echo "Running C++ tests..."
        # Executar testes unitários C++

  # Validação de assets
  asset-validation:
    runs-on: ubuntu-latest
    name: Asset Validation
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Validate project structure
      run: |
        echo "Validating project structure..."
        
        # Verificar estrutura de diretórios obrigatória
        required_dirs=(
          "Source/Auracron"
          "Source/AuracronPCGBridge"
          "Source/AuracronMetaHumanBridge"
          "Source/AuracronWorldPartitionBridge"
          "Source/AuracronFoliageBridge"
          "Source/AuracronLumenBridge"
          "Scripts/Python/Core"
          "Content"
          "Config"
        )
        
        for dir in "${required_dirs[@]}"; do
          if [ ! -d "$dir" ]; then
            echo "ERROR: Required directory missing: $dir"
            exit 1
          else
            echo "✓ Found: $dir"
          fi
        done
        
    - name: Validate configuration files
      run: |
        echo "Validating configuration files..."
        
        # Verificar arquivos de configuração obrigatórios
        required_files=(
          "Auracron.uproject"
          "Config/DefaultEngine.ini"
          "Config/DefaultGame.ini"
          "README.md"
          ".gitignore"
        )
        
        for file in "${required_files[@]}"; do
          if [ ! -f "$file" ]; then
            echo "ERROR: Required file missing: $file"
            exit 1
          else
            echo "✓ Found: $file"
          fi
        done
        
    - name: Validate Python imports
      run: |
        echo "Validating Python imports..."
        python3 -c "
        import sys
        import os
        sys.path.insert(0, 'Scripts/Python')
        
        try:
            # Verificar se módulos principais podem ser importados
            # (sem executar, apenas verificar sintaxe)
            import ast
            
            python_files = []
            for root, dirs, files in os.walk('Scripts/Python'):
                for file in files:
                    if file.endswith('.py'):
                        python_files.append(os.path.join(root, file))
            
            for file in python_files:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        ast.parse(f.read())
                    print(f'✓ Syntax OK: {file}')
                except SyntaxError as e:
                    print(f'✗ Syntax Error in {file}: {e}')
                    sys.exit(1)
                    
            print('All Python files have valid syntax')
        except Exception as e:
            print(f'Error during validation: {e}')
            sys.exit(1)
        "

  # Documentação
  documentation:
    runs-on: ubuntu-latest
    name: Documentation Check
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Check documentation completeness
      run: |
        echo "Checking documentation..."
        
        # Verificar se README existe e tem conteúdo mínimo
        if [ ! -f "README.md" ]; then
          echo "ERROR: README.md is missing"
          exit 1
        fi
        
        readme_size=$(wc -c < README.md)
        if [ $readme_size -lt 1000 ]; then
          echo "ERROR: README.md is too short (less than 1000 characters)"
          exit 1
        fi
        
        echo "✓ README.md is present and substantial"
        
        # Verificar se GDD existe
        if [ ! -f "mapas/mapa/AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md" ]; then
          echo "ERROR: Game Design Document is missing"
          exit 1
        fi
        
        echo "✓ Game Design Document is present"

  # Segurança
  security-scan:
    runs-on: ubuntu-latest
    name: Security Scan
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run security scan
      run: |
        echo "Running security scan..."
        
        # Verificar se não há credenciais hardcoded
        if grep -r -i "password\|secret\|key\|token" --include="*.cpp" --include="*.h" --include="*.py" --include="*.ini" . | grep -v "example\|template\|comment"; then
          echo "WARNING: Potential hardcoded credentials found"
          # Não falhar o build, apenas avisar
        fi
        
        # Verificar permissões de arquivos
        find . -type f -perm /111 -name "*.py" -o -name "*.cpp" -o -name "*.h" | while read file; do
          echo "WARNING: Executable permission on source file: $file"
        done
        
        echo "Security scan completed"

  # Notificação de sucesso
  notify-success:
    runs-on: ubuntu-latest
    name: Notify Success
    needs: [python-analysis, cpp-build, asset-validation, documentation, security-scan]
    if: success()
    
    steps:
    - name: Success notification
      run: |
        echo "🎉 AURACRON CI/CD Pipeline completed successfully!"
        echo "All checks passed:"
        echo "✓ Python code analysis"
        echo "✓ C++ build"
        echo "✓ Asset validation"
        echo "✓ Documentation check"
        echo "✓ Security scan"
        echo ""
        echo "Ready for deployment! 🚀"
