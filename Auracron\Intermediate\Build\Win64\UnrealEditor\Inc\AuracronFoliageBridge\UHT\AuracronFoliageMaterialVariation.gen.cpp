// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageMaterialVariation.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageMaterialVariation() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageMaterialVariationManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronColorVariationData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMaterialInstanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTextureBlendingData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronMaterialVariationStrategy ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy;
static UEnum* EAuracronMaterialVariationStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronMaterialVariationStrategy"));
	}
	return Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronMaterialVariationStrategy>()
{
	return EAuracronMaterialVariationStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ColorOnly.DisplayName", "Color Only" },
		{ "ColorOnly.Name", "EAuracronMaterialVariationStrategy::ColorOnly" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material variation strategy\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronMaterialVariationStrategy::Custom" },
		{ "Hybrid.DisplayName", "Hybrid Strategy" },
		{ "Hybrid.Name", "EAuracronMaterialVariationStrategy::Hybrid" },
		{ "InstanceSwapping.DisplayName", "Instance Swapping" },
		{ "InstanceSwapping.Name", "EAuracronMaterialVariationStrategy::InstanceSwapping" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronMaterialVariationStrategy::None" },
		{ "ParameterVariation.DisplayName", "Parameter Variation" },
		{ "ParameterVariation.Name", "EAuracronMaterialVariationStrategy::ParameterVariation" },
		{ "ProceduralGeneration.DisplayName", "Procedural Generation" },
		{ "ProceduralGeneration.Name", "EAuracronMaterialVariationStrategy::ProceduralGeneration" },
		{ "TextureBlending.DisplayName", "Texture Blending" },
		{ "TextureBlending.Name", "EAuracronMaterialVariationStrategy::TextureBlending" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material variation strategy" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronMaterialVariationStrategy::None", (int64)EAuracronMaterialVariationStrategy::None },
		{ "EAuracronMaterialVariationStrategy::ColorOnly", (int64)EAuracronMaterialVariationStrategy::ColorOnly },
		{ "EAuracronMaterialVariationStrategy::TextureBlending", (int64)EAuracronMaterialVariationStrategy::TextureBlending },
		{ "EAuracronMaterialVariationStrategy::ParameterVariation", (int64)EAuracronMaterialVariationStrategy::ParameterVariation },
		{ "EAuracronMaterialVariationStrategy::InstanceSwapping", (int64)EAuracronMaterialVariationStrategy::InstanceSwapping },
		{ "EAuracronMaterialVariationStrategy::ProceduralGeneration", (int64)EAuracronMaterialVariationStrategy::ProceduralGeneration },
		{ "EAuracronMaterialVariationStrategy::Hybrid", (int64)EAuracronMaterialVariationStrategy::Hybrid },
		{ "EAuracronMaterialVariationStrategy::Custom", (int64)EAuracronMaterialVariationStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronMaterialVariationStrategy",
	"EAuracronMaterialVariationStrategy",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy()
{
	if (!Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy.InnerSingleton;
}
// ********** End Enum EAuracronMaterialVariationStrategy ******************************************

// ********** Begin Enum EAuracronColorVariationMode ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronColorVariationMode;
static UEnum* EAuracronColorVariationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronColorVariationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronColorVariationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronColorVariationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronColorVariationMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronColorVariationMode>()
{
	return EAuracronColorVariationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Color variation mode\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronColorVariationMode::Custom" },
		{ "Environmental.DisplayName", "Environmental" },
		{ "Environmental.Name", "EAuracronColorVariationMode::Environmental" },
		{ "Gradient.DisplayName", "Gradient Based" },
		{ "Gradient.Name", "EAuracronColorVariationMode::Gradient" },
		{ "HSV.DisplayName", "HSV Variation" },
		{ "HSV.Name", "EAuracronColorVariationMode::HSV" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronColorVariationMode::None" },
		{ "Palette.DisplayName", "Palette Based" },
		{ "Palette.Name", "EAuracronColorVariationMode::Palette" },
		{ "RGB.DisplayName", "RGB Variation" },
		{ "RGB.Name", "EAuracronColorVariationMode::RGB" },
		{ "Seasonal.DisplayName", "Seasonal Colors" },
		{ "Seasonal.Name", "EAuracronColorVariationMode::Seasonal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Color variation mode" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronColorVariationMode::None", (int64)EAuracronColorVariationMode::None },
		{ "EAuracronColorVariationMode::HSV", (int64)EAuracronColorVariationMode::HSV },
		{ "EAuracronColorVariationMode::RGB", (int64)EAuracronColorVariationMode::RGB },
		{ "EAuracronColorVariationMode::Palette", (int64)EAuracronColorVariationMode::Palette },
		{ "EAuracronColorVariationMode::Gradient", (int64)EAuracronColorVariationMode::Gradient },
		{ "EAuracronColorVariationMode::Seasonal", (int64)EAuracronColorVariationMode::Seasonal },
		{ "EAuracronColorVariationMode::Environmental", (int64)EAuracronColorVariationMode::Environmental },
		{ "EAuracronColorVariationMode::Custom", (int64)EAuracronColorVariationMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronColorVariationMode",
	"EAuracronColorVariationMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronColorVariationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronColorVariationMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronColorVariationMode.InnerSingleton;
}
// ********** End Enum EAuracronColorVariationMode *************************************************

// ********** Begin Enum EAuracronTextureBlendingMode **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTextureBlendingMode;
static UEnum* EAuracronTextureBlendingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTextureBlendingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTextureBlendingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronTextureBlendingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronTextureBlendingMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronTextureBlendingMode>()
{
	return EAuracronTextureBlendingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ColorBurn.DisplayName", "Color Burn" },
		{ "ColorBurn.Name", "EAuracronTextureBlendingMode::ColorBurn" },
		{ "ColorDodge.DisplayName", "Color Dodge" },
		{ "ColorDodge.Name", "EAuracronTextureBlendingMode::ColorDodge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Texture blending mode\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronTextureBlendingMode::Custom" },
		{ "Darken.DisplayName", "Darken" },
		{ "Darken.Name", "EAuracronTextureBlendingMode::Darken" },
		{ "Difference.DisplayName", "Difference" },
		{ "Difference.Name", "EAuracronTextureBlendingMode::Difference" },
		{ "Exclusion.DisplayName", "Exclusion" },
		{ "Exclusion.Name", "EAuracronTextureBlendingMode::Exclusion" },
		{ "HardLight.DisplayName", "Hard Light" },
		{ "HardLight.Name", "EAuracronTextureBlendingMode::HardLight" },
		{ "Lighten.DisplayName", "Lighten" },
		{ "Lighten.Name", "EAuracronTextureBlendingMode::Lighten" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "EAuracronTextureBlendingMode::Multiply" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronTextureBlendingMode::None" },
		{ "Overlay.DisplayName", "Overlay" },
		{ "Overlay.Name", "EAuracronTextureBlendingMode::Overlay" },
		{ "Screen.DisplayName", "Screen" },
		{ "Screen.Name", "EAuracronTextureBlendingMode::Screen" },
		{ "SoftLight.DisplayName", "Soft Light" },
		{ "SoftLight.Name", "EAuracronTextureBlendingMode::SoftLight" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture blending mode" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTextureBlendingMode::None", (int64)EAuracronTextureBlendingMode::None },
		{ "EAuracronTextureBlendingMode::Multiply", (int64)EAuracronTextureBlendingMode::Multiply },
		{ "EAuracronTextureBlendingMode::Screen", (int64)EAuracronTextureBlendingMode::Screen },
		{ "EAuracronTextureBlendingMode::Overlay", (int64)EAuracronTextureBlendingMode::Overlay },
		{ "EAuracronTextureBlendingMode::SoftLight", (int64)EAuracronTextureBlendingMode::SoftLight },
		{ "EAuracronTextureBlendingMode::HardLight", (int64)EAuracronTextureBlendingMode::HardLight },
		{ "EAuracronTextureBlendingMode::ColorDodge", (int64)EAuracronTextureBlendingMode::ColorDodge },
		{ "EAuracronTextureBlendingMode::ColorBurn", (int64)EAuracronTextureBlendingMode::ColorBurn },
		{ "EAuracronTextureBlendingMode::Darken", (int64)EAuracronTextureBlendingMode::Darken },
		{ "EAuracronTextureBlendingMode::Lighten", (int64)EAuracronTextureBlendingMode::Lighten },
		{ "EAuracronTextureBlendingMode::Difference", (int64)EAuracronTextureBlendingMode::Difference },
		{ "EAuracronTextureBlendingMode::Exclusion", (int64)EAuracronTextureBlendingMode::Exclusion },
		{ "EAuracronTextureBlendingMode::Custom", (int64)EAuracronTextureBlendingMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronTextureBlendingMode",
	"EAuracronTextureBlendingMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronTextureBlendingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTextureBlendingMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTextureBlendingMode.InnerSingleton;
}
// ********** End Enum EAuracronTextureBlendingMode ************************************************

// ********** Begin Enum EAuracronMaterialAssignmentMode *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode;
static UEnum* EAuracronMaterialAssignmentMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronMaterialAssignmentMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronMaterialAssignmentMode>()
{
	return EAuracronMaterialAssignmentMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AttributeBased.DisplayName", "Attribute Based" },
		{ "AttributeBased.Name", "EAuracronMaterialAssignmentMode::AttributeBased" },
		{ "BiomeBased.DisplayName", "Biome Based" },
		{ "BiomeBased.Name", "EAuracronMaterialAssignmentMode::BiomeBased" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material assignment mode\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronMaterialAssignmentMode::Custom" },
		{ "DensityBased.DisplayName", "Density Based" },
		{ "DensityBased.Name", "EAuracronMaterialAssignmentMode::DensityBased" },
		{ "DistanceBased.DisplayName", "Distance Based" },
		{ "DistanceBased.Name", "EAuracronMaterialAssignmentMode::DistanceBased" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EAuracronMaterialAssignmentMode::Random" },
		{ "RuleBased.DisplayName", "Rule Based" },
		{ "RuleBased.Name", "EAuracronMaterialAssignmentMode::RuleBased" },
		{ "SeasonalBased.DisplayName", "Seasonal Based" },
		{ "SeasonalBased.Name", "EAuracronMaterialAssignmentMode::SeasonalBased" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material assignment mode" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronMaterialAssignmentMode::Random", (int64)EAuracronMaterialAssignmentMode::Random },
		{ "EAuracronMaterialAssignmentMode::DistanceBased", (int64)EAuracronMaterialAssignmentMode::DistanceBased },
		{ "EAuracronMaterialAssignmentMode::DensityBased", (int64)EAuracronMaterialAssignmentMode::DensityBased },
		{ "EAuracronMaterialAssignmentMode::BiomeBased", (int64)EAuracronMaterialAssignmentMode::BiomeBased },
		{ "EAuracronMaterialAssignmentMode::SeasonalBased", (int64)EAuracronMaterialAssignmentMode::SeasonalBased },
		{ "EAuracronMaterialAssignmentMode::AttributeBased", (int64)EAuracronMaterialAssignmentMode::AttributeBased },
		{ "EAuracronMaterialAssignmentMode::RuleBased", (int64)EAuracronMaterialAssignmentMode::RuleBased },
		{ "EAuracronMaterialAssignmentMode::Custom", (int64)EAuracronMaterialAssignmentMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronMaterialAssignmentMode",
	"EAuracronMaterialAssignmentMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode.InnerSingleton;
}
// ********** End Enum EAuracronMaterialAssignmentMode *********************************************

// ********** Begin ScriptStruct FAuracronFoliageMaterialVariationConfiguration ********************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration;
class UScriptStruct* FAuracronFoliageMaterialVariationConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageMaterialVariationConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Material Variation Configuration\n * Configuration for material variation system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Material Variation Configuration\nConfiguration for material variation system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMaterialVariation_MetaData[] = {
		{ "Category", "Material Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationStrategy_MetaData[] = {
		{ "Category", "Material Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableColorVariation_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariationMode_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariationIntensity_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseColorTint_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HueVariationRange_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaturationVariationRange_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueVariationRange_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTextureBlending_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureBlendingMode_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingOpacity_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingTextures_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssignmentMode_MetaData[] = {
		{ "Category", "Material Assignment" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialPool_MetaData[] = {
		{ "Category", "Material Assignment" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialWeights_MetaData[] = {
		{ "Category", "Material Assignment" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProceduralMaterials_MetaData[] = {
		{ "Category", "Procedural Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralSeed_MetaData[] = {
		{ "Category", "Procedural Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralVariationStrength_MetaData[] = {
		{ "Category", "Procedural Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncMaterialGeneration_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMaterialInstancesPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMaterialCaching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCachedMaterials_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithSeasonalSystem_MetaData[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithBiomeSystem_MetaData[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithWindSystem_MetaData[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogMaterialVariations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableMaterialVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMaterialVariation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariationStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariationStrategy;
	static void NewProp_bEnableColorVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableColorVariation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ColorVariationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ColorVariationMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorVariationIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseColorTint;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HueVariationRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SaturationVariationRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValueVariationRange;
	static void NewProp_bEnableTextureBlending_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTextureBlending;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TextureBlendingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TextureBlendingMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendingOpacity;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BlendingTextures_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendingTextures;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AssignmentMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AssignmentMode;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialPool_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialPool;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaterialWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialWeights;
	static void NewProp_bEnableProceduralMaterials_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProceduralMaterials;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ProceduralSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProceduralVariationStrength;
	static void NewProp_bEnableAsyncMaterialGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncMaterialGeneration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMaterialInstancesPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaterialUpdateInterval;
	static void NewProp_bEnableMaterialCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMaterialCaching;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCachedMaterials;
	static void NewProp_bIntegrateWithSeasonalSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithSeasonalSystem;
	static void NewProp_bIntegrateWithBiomeSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithBiomeSystem;
	static void NewProp_bIntegrateWithWindSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithWindSystem;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bLogMaterialVariations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogMaterialVariations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageMaterialVariationConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialVariation_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bEnableMaterialVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialVariation = { "bEnableMaterialVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMaterialVariation_MetaData), NewProp_bEnableMaterialVariation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_VariationStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_VariationStrategy = { "VariationStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, VariationStrategy), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialVariationStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationStrategy_MetaData), NewProp_VariationStrategy_MetaData) }; // 1155178383
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableColorVariation_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bEnableColorVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableColorVariation = { "bEnableColorVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableColorVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableColorVariation_MetaData), NewProp_bEnableColorVariation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ColorVariationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ColorVariationMode = { "ColorVariationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, ColorVariationMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariationMode_MetaData), NewProp_ColorVariationMode_MetaData) }; // 1115969821
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ColorVariationIntensity = { "ColorVariationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, ColorVariationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariationIntensity_MetaData), NewProp_ColorVariationIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BaseColorTint = { "BaseColorTint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, BaseColorTint), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseColorTint_MetaData), NewProp_BaseColorTint_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_HueVariationRange = { "HueVariationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, HueVariationRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HueVariationRange_MetaData), NewProp_HueVariationRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_SaturationVariationRange = { "SaturationVariationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, SaturationVariationRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaturationVariationRange_MetaData), NewProp_SaturationVariationRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ValueVariationRange = { "ValueVariationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, ValueVariationRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueVariationRange_MetaData), NewProp_ValueVariationRange_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableTextureBlending_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bEnableTextureBlending = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableTextureBlending = { "bEnableTextureBlending", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableTextureBlending_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTextureBlending_MetaData), NewProp_bEnableTextureBlending_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_TextureBlendingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_TextureBlendingMode = { "TextureBlendingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, TextureBlendingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureBlendingMode_MetaData), NewProp_TextureBlendingMode_MetaData) }; // 867664803
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BlendingOpacity = { "BlendingOpacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, BlendingOpacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingOpacity_MetaData), NewProp_BlendingOpacity_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BlendingTextures_Inner = { "BlendingTextures", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BlendingTextures = { "BlendingTextures", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, BlendingTextures), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingTextures_MetaData), NewProp_BlendingTextures_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_AssignmentMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_AssignmentMode = { "AssignmentMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, AssignmentMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronMaterialAssignmentMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssignmentMode_MetaData), NewProp_AssignmentMode_MetaData) }; // 4285863054
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialPool_Inner = { "MaterialPool", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialPool = { "MaterialPool", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, MaterialPool), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialPool_MetaData), NewProp_MaterialPool_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialWeights_Inner = { "MaterialWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialWeights = { "MaterialWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, MaterialWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialWeights_MetaData), NewProp_MaterialWeights_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableProceduralMaterials_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bEnableProceduralMaterials = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableProceduralMaterials = { "bEnableProceduralMaterials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableProceduralMaterials_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProceduralMaterials_MetaData), NewProp_bEnableProceduralMaterials_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ProceduralSeed = { "ProceduralSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, ProceduralSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralSeed_MetaData), NewProp_ProceduralSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ProceduralVariationStrength = { "ProceduralVariationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, ProceduralVariationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralVariationStrength_MetaData), NewProp_ProceduralVariationStrength_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableAsyncMaterialGeneration_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bEnableAsyncMaterialGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableAsyncMaterialGeneration = { "bEnableAsyncMaterialGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableAsyncMaterialGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncMaterialGeneration_MetaData), NewProp_bEnableAsyncMaterialGeneration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaxMaterialInstancesPerFrame = { "MaxMaterialInstancesPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, MaxMaterialInstancesPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMaterialInstancesPerFrame_MetaData), NewProp_MaxMaterialInstancesPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialUpdateInterval = { "MaterialUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, MaterialUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialUpdateInterval_MetaData), NewProp_MaterialUpdateInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialCaching_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bEnableMaterialCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialCaching = { "bEnableMaterialCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMaterialCaching_MetaData), NewProp_bEnableMaterialCaching_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaxCachedMaterials = { "MaxCachedMaterials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageMaterialVariationConfiguration, MaxCachedMaterials), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCachedMaterials_MetaData), NewProp_MaxCachedMaterials_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithSeasonalSystem_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bIntegrateWithSeasonalSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithSeasonalSystem = { "bIntegrateWithSeasonalSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithSeasonalSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithSeasonalSystem_MetaData), NewProp_bIntegrateWithSeasonalSystem_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithBiomeSystem_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bIntegrateWithBiomeSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithBiomeSystem = { "bIntegrateWithBiomeSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithBiomeSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithBiomeSystem_MetaData), NewProp_bIntegrateWithBiomeSystem_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithWindSystem_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bIntegrateWithWindSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithWindSystem = { "bIntegrateWithWindSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithWindSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithWindSystem_MetaData), NewProp_bIntegrateWithWindSystem_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bLogMaterialVariations_SetBit(void* Obj)
{
	((FAuracronFoliageMaterialVariationConfiguration*)Obj)->bLogMaterialVariations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bLogMaterialVariations = { "bLogMaterialVariations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageMaterialVariationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bLogMaterialVariations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogMaterialVariations_MetaData), NewProp_bLogMaterialVariations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_VariationStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_VariationStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableColorVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ColorVariationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ColorVariationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ColorVariationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BaseColorTint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_HueVariationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_SaturationVariationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ValueVariationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableTextureBlending,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_TextureBlendingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_TextureBlendingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BlendingOpacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BlendingTextures_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_BlendingTextures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_AssignmentMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_AssignmentMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialPool_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialPool,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableProceduralMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ProceduralSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_ProceduralVariationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableAsyncMaterialGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaxMaterialInstancesPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaterialUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableMaterialCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_MaxCachedMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithSeasonalSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithBiomeSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bIntegrateWithWindSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewProp_bLogMaterialVariations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageMaterialVariationConfiguration",
	Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::PropPointers),
	sizeof(FAuracronFoliageMaterialVariationConfiguration),
	alignof(FAuracronFoliageMaterialVariationConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageMaterialVariationConfiguration **********************

// ********** Begin ScriptStruct FAuracronColorVariationData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronColorVariationData;
class UScriptStruct* FAuracronColorVariationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronColorVariationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronColorVariationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronColorVariationData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronColorVariationData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronColorVariationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Color Variation Data\n * Data for color variation of foliage materials\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Color Variation Data\nData for color variation of foliage materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationId_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationMode_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseColor_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationColor_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationIntensity_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HueShift_MetaData[] = {
		{ "Category", "HSV Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaturationMultiplier_MetaData[] = {
		{ "Category", "HSV Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueMultiplier_MetaData[] = {
		{ "Category", "HSV Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorPalette_MetaData[] = {
		{ "Category", "Palette" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GradientColors_MetaData[] = {
		{ "Category", "Gradient" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GradientStops_MetaData[] = {
		{ "Category", "Gradient" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariationId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariationMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VariationColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VariationIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HueShift;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SaturationMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValueMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorPalette_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ColorPalette;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GradientColors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GradientColors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GradientStops_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GradientStops;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronColorVariationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationId = { "VariationId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, VariationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationId_MetaData), NewProp_VariationId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationMode = { "VariationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, VariationMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronColorVariationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationMode_MetaData), NewProp_VariationMode_MetaData) }; // 1115969821
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_BaseColor = { "BaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, BaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseColor_MetaData), NewProp_BaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationColor = { "VariationColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, VariationColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationColor_MetaData), NewProp_VariationColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationIntensity = { "VariationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, VariationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationIntensity_MetaData), NewProp_VariationIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_HueShift = { "HueShift", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, HueShift), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HueShift_MetaData), NewProp_HueShift_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_SaturationMultiplier = { "SaturationMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, SaturationMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaturationMultiplier_MetaData), NewProp_SaturationMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_ValueMultiplier = { "ValueMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, ValueMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueMultiplier_MetaData), NewProp_ValueMultiplier_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_ColorPalette_Inner = { "ColorPalette", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_ColorPalette = { "ColorPalette", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, ColorPalette), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorPalette_MetaData), NewProp_ColorPalette_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientColors_Inner = { "GradientColors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientColors = { "GradientColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, GradientColors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GradientColors_MetaData), NewProp_GradientColors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientStops_Inner = { "GradientStops", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientStops = { "GradientStops", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, GradientStops), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GradientStops_MetaData), NewProp_GradientStops_MetaData) };
void Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronColorVariationData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronColorVariationData), &Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronColorVariationData, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_BaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_VariationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_HueShift,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_SaturationMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_ValueMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_ColorPalette_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_ColorPalette,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientColors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientColors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientStops_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_GradientStops,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewProp_CreationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronColorVariationData",
	Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::PropPointers),
	sizeof(FAuracronColorVariationData),
	alignof(FAuracronColorVariationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronColorVariationData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronColorVariationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronColorVariationData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronColorVariationData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronColorVariationData *****************************************

// ********** Begin ScriptStruct FAuracronTextureBlendingData **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData;
class UScriptStruct* FAuracronTextureBlendingData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTextureBlendingData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronTextureBlendingData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Texture Blending Data\n * Data for texture blending in foliage materials\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture Blending Data\nData for texture blending in foliage materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingId_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingMode_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseTexture_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendTexture_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaskTexture_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendOpacity_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureScale_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureOffset_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureRotation_MetaData[] = {
		{ "Category", "Texture Blending" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWorldSpaceUV_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertMask_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendingId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BlendingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BlendingMode;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BaseTexture;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BlendTexture;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaskTexture;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendOpacity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TextureScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TextureOffset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TextureRotation;
	static void NewProp_bUseWorldSpaceUV_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWorldSpaceUV;
	static void NewProp_bInvertMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertMask;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTextureBlendingData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendingId = { "BlendingId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, BlendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingId_MetaData), NewProp_BlendingId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendingMode = { "BlendingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, BlendingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTextureBlendingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingMode_MetaData), NewProp_BlendingMode_MetaData) }; // 867664803
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BaseTexture = { "BaseTexture", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, BaseTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseTexture_MetaData), NewProp_BaseTexture_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendTexture = { "BlendTexture", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, BlendTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendTexture_MetaData), NewProp_BlendTexture_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_MaskTexture = { "MaskTexture", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, MaskTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaskTexture_MetaData), NewProp_MaskTexture_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendOpacity = { "BlendOpacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, BlendOpacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendOpacity_MetaData), NewProp_BlendOpacity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_TextureScale = { "TextureScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, TextureScale), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureScale_MetaData), NewProp_TextureScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_TextureOffset = { "TextureOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, TextureOffset), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureOffset_MetaData), NewProp_TextureOffset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_TextureRotation = { "TextureRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, TextureRotation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureRotation_MetaData), NewProp_TextureRotation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bUseWorldSpaceUV_SetBit(void* Obj)
{
	((FAuracronTextureBlendingData*)Obj)->bUseWorldSpaceUV = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bUseWorldSpaceUV = { "bUseWorldSpaceUV", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureBlendingData), &Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bUseWorldSpaceUV_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWorldSpaceUV_MetaData), NewProp_bUseWorldSpaceUV_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bInvertMask_SetBit(void* Obj)
{
	((FAuracronTextureBlendingData*)Obj)->bInvertMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bInvertMask = { "bInvertMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureBlendingData), &Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bInvertMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertMask_MetaData), NewProp_bInvertMask_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronTextureBlendingData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureBlendingData), &Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureBlendingData, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BaseTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_MaskTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_BlendOpacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_TextureScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_TextureOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_TextureRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bUseWorldSpaceUV,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bInvertMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewProp_CreationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronTextureBlendingData",
	Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::PropPointers),
	sizeof(FAuracronTextureBlendingData),
	alignof(FAuracronTextureBlendingData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTextureBlendingData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTextureBlendingData ****************************************

// ********** Begin ScriptStruct FAuracronMaterialInstanceData *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData;
class UScriptStruct* FAuracronMaterialInstanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMaterialInstanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronMaterialInstanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Material Instance Data\n * Data for dynamic material instances\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Instance Data\nData for dynamic material instances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "Category", "Material Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "Category", "Material Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseMaterial_MetaData[] = {
		{ "Category", "Material Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicMaterial_MetaData[] = {
		{ "Category", "Material Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalarParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureBlending_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerated_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNeedsUpdate_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BaseMaterial;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_DynamicMaterial;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScalarParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScalarParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ScalarParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VectorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VectorParameters;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TextureParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TextureParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TextureParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorVariation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TextureBlending;
	static void NewProp_bIsGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerated;
	static void NewProp_bNeedsUpdate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNeedsUpdate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMaterialInstanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_BaseMaterial = { "BaseMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, BaseMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseMaterial_MetaData), NewProp_BaseMaterial_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_DynamicMaterial = { "DynamicMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, DynamicMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicMaterial_MetaData), NewProp_DynamicMaterial_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ScalarParameters_ValueProp = { "ScalarParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ScalarParameters_Key_KeyProp = { "ScalarParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ScalarParameters = { "ScalarParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, ScalarParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalarParameters_MetaData), NewProp_ScalarParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_VectorParameters_ValueProp = { "VectorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_VectorParameters_Key_KeyProp = { "VectorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_VectorParameters = { "VectorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, VectorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorParameters_MetaData), NewProp_VectorParameters_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureParameters_ValueProp = { "TextureParameters", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UTexture_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureParameters_Key_KeyProp = { "TextureParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureParameters = { "TextureParameters", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, TextureParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureParameters_MetaData), NewProp_TextureParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ColorVariation = { "ColorVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, ColorVariation), Z_Construct_UScriptStruct_FAuracronColorVariationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariation_MetaData), NewProp_ColorVariation_MetaData) }; // 1309543827
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureBlending = { "TextureBlending", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, TextureBlending), Z_Construct_UScriptStruct_FAuracronTextureBlendingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureBlending_MetaData), NewProp_TextureBlending_MetaData) }; // 4032748465
void Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bIsGenerated_SetBit(void* Obj)
{
	((FAuracronMaterialInstanceData*)Obj)->bIsGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bIsGenerated = { "bIsGenerated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMaterialInstanceData), &Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bIsGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerated_MetaData), NewProp_bIsGenerated_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bNeedsUpdate_SetBit(void* Obj)
{
	((FAuracronMaterialInstanceData*)Obj)->bNeedsUpdate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bNeedsUpdate = { "bNeedsUpdate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMaterialInstanceData), &Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bNeedsUpdate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNeedsUpdate_MetaData), NewProp_bNeedsUpdate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialInstanceData, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_BaseMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_DynamicMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ScalarParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ScalarParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ScalarParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_VectorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_VectorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_VectorParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_ColorVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_TextureBlending,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bIsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_bNeedsUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronMaterialInstanceData",
	Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::PropPointers),
	sizeof(FAuracronMaterialInstanceData),
	alignof(FAuracronMaterialInstanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMaterialInstanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMaterialInstanceData ***************************************

// ********** Begin ScriptStruct FAuracronMaterialVariationPerformanceData *************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData;
class UScriptStruct* FAuracronMaterialVariationPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronMaterialVariationPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Material Variation Performance Data\n * Performance metrics for material variation system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Variation Performance Data\nPerformance metrics for material variation system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMaterialInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveMaterialInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedMaterialInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureBlends_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialGenerationTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariationTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureBlendingTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalMaterialInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveMaterialInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CachedMaterialInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ColorVariations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TextureBlends;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaterialGenerationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorVariationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TextureBlendingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMaterialVariationPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_TotalMaterialInstances = { "TotalMaterialInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, TotalMaterialInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMaterialInstances_MetaData), NewProp_TotalMaterialInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_ActiveMaterialInstances = { "ActiveMaterialInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, ActiveMaterialInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveMaterialInstances_MetaData), NewProp_ActiveMaterialInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_CachedMaterialInstances = { "CachedMaterialInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, CachedMaterialInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedMaterialInstances_MetaData), NewProp_CachedMaterialInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_ColorVariations = { "ColorVariations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, ColorVariations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariations_MetaData), NewProp_ColorVariations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_TextureBlends = { "TextureBlends", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, TextureBlends), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureBlends_MetaData), NewProp_TextureBlends_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_MaterialGenerationTime = { "MaterialGenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, MaterialGenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialGenerationTime_MetaData), NewProp_MaterialGenerationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_ColorVariationTime = { "ColorVariationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, ColorVariationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariationTime_MetaData), NewProp_ColorVariationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_TextureBlendingTime = { "TextureBlendingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, TextureBlendingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureBlendingTime_MetaData), NewProp_TextureBlendingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMaterialVariationPerformanceData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_TotalMaterialInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_ActiveMaterialInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_CachedMaterialInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_ColorVariations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_TextureBlends,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_MaterialGenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_ColorVariationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_TextureBlendingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronMaterialVariationPerformanceData",
	Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::PropPointers),
	sizeof(FAuracronMaterialVariationPerformanceData),
	alignof(FAuracronMaterialVariationPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMaterialVariationPerformanceData ***************************

// ********** Begin Delegate FOnMaterialInstanceCreated ********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceCreated_Parms
	{
		FString InstanceId;
		UMaterialInstanceDynamic* MaterialInstance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MaterialInstance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceCreated_Parms, InstanceId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::NewProp_MaterialInstance = { "MaterialInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceCreated_Parms, MaterialInstance), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::NewProp_MaterialInstance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "OnMaterialInstanceCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageMaterialVariationManager::FOnMaterialInstanceCreated_DelegateWrapper(const FMulticastScriptDelegate& OnMaterialInstanceCreated, const FString& InstanceId, UMaterialInstanceDynamic* MaterialInstance)
{
	struct AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceCreated_Parms
	{
		FString InstanceId;
		UMaterialInstanceDynamic* MaterialInstance;
	};
	AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceCreated_Parms Parms;
	Parms.InstanceId=InstanceId;
	Parms.MaterialInstance=MaterialInstance;
	OnMaterialInstanceCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMaterialInstanceCreated **********************************************

// ********** Begin Delegate FOnMaterialInstanceRemoved ********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceRemoved_Parms
	{
		FString InstanceId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceRemoved_Parms, InstanceId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::NewProp_InstanceId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "OnMaterialInstanceRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageMaterialVariationManager::FOnMaterialInstanceRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnMaterialInstanceRemoved, const FString& InstanceId)
{
	struct AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceRemoved_Parms
	{
		FString InstanceId;
	};
	AuracronFoliageMaterialVariationManager_eventOnMaterialInstanceRemoved_Parms Parms;
	Parms.InstanceId=InstanceId;
	OnMaterialInstanceRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMaterialInstanceRemoved **********************************************

// ********** Begin Delegate FOnColorVariationApplied **********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventOnColorVariationApplied_Parms
	{
		FString VariationId;
		FLinearColor ResultColor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariationId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResultColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::NewProp_VariationId = { "VariationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventOnColorVariationApplied_Parms, VariationId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::NewProp_ResultColor = { "ResultColor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventOnColorVariationApplied_Parms, ResultColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::NewProp_VariationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::NewProp_ResultColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "OnColorVariationApplied__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnColorVariationApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnColorVariationApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageMaterialVariationManager::FOnColorVariationApplied_DelegateWrapper(const FMulticastScriptDelegate& OnColorVariationApplied, const FString& VariationId, FLinearColor ResultColor)
{
	struct AuracronFoliageMaterialVariationManager_eventOnColorVariationApplied_Parms
	{
		FString VariationId;
		FLinearColor ResultColor;
	};
	AuracronFoliageMaterialVariationManager_eventOnColorVariationApplied_Parms Parms;
	Parms.VariationId=VariationId;
	Parms.ResultColor=ResultColor;
	OnColorVariationApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnColorVariationApplied ************************************************

// ********** Begin Delegate FOnTextureBlendingCompleted *******************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventOnTextureBlendingCompleted_Parms
	{
		FString BlendingId;
		UTexture2D* ResultTexture;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendingId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ResultTexture;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::NewProp_BlendingId = { "BlendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventOnTextureBlendingCompleted_Parms, BlendingId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::NewProp_ResultTexture = { "ResultTexture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventOnTextureBlendingCompleted_Parms, ResultTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::NewProp_BlendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::NewProp_ResultTexture,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "OnTextureBlendingCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnTextureBlendingCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::AuracronFoliageMaterialVariationManager_eventOnTextureBlendingCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageMaterialVariationManager::FOnTextureBlendingCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTextureBlendingCompleted, const FString& BlendingId, UTexture2D* ResultTexture)
{
	struct AuracronFoliageMaterialVariationManager_eventOnTextureBlendingCompleted_Parms
	{
		FString BlendingId;
		UTexture2D* ResultTexture;
	};
	AuracronFoliageMaterialVariationManager_eventOnTextureBlendingCompleted_Parms Parms;
	Parms.BlendingId=BlendingId;
	Parms.ResultTexture=ResultTexture;
	OnTextureBlendingCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTextureBlendingCompleted *********************************************

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function ApplyColorVariation ****
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventApplyColorVariation_Parms
	{
		FLinearColor BaseColor;
		FAuracronColorVariationData VariationData;
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseColor_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VariationData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::NewProp_BaseColor = { "BaseColor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplyColorVariation_Parms, BaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseColor_MetaData), NewProp_BaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::NewProp_VariationData = { "VariationData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplyColorVariation_Parms, VariationData), Z_Construct_UScriptStruct_FAuracronColorVariationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationData_MetaData), NewProp_VariationData_MetaData) }; // 1309543827
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplyColorVariation_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::NewProp_BaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::NewProp_VariationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "ApplyColorVariation", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventApplyColorVariation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventApplyColorVariation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execApplyColorVariation)
{
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_BaseColor);
	P_GET_STRUCT_REF(FAuracronColorVariationData,Z_Param_Out_VariationData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=P_THIS->ApplyColorVariation(Z_Param_Out_BaseColor,Z_Param_Out_VariationData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function ApplyColorVariation ******

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function ApplySeasonalMaterialChanges 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventApplySeasonalMaterialChanges_Parms
	{
		EAuracronSeasonType Season;
		float SeasonProgress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Season_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Season;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonProgress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::NewProp_Season_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::NewProp_Season = { "Season", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplySeasonalMaterialChanges_Parms, Season), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, METADATA_PARAMS(0, nullptr) }; // 1660267735
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::NewProp_SeasonProgress = { "SeasonProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplySeasonalMaterialChanges_Parms, SeasonProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::NewProp_Season_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::NewProp_Season,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::NewProp_SeasonProgress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "ApplySeasonalMaterialChanges", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::AuracronFoliageMaterialVariationManager_eventApplySeasonalMaterialChanges_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::AuracronFoliageMaterialVariationManager_eventApplySeasonalMaterialChanges_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execApplySeasonalMaterialChanges)
{
	P_GET_ENUM(EAuracronSeasonType,Z_Param_Season);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SeasonProgress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplySeasonalMaterialChanges(EAuracronSeasonType(Z_Param_Season),Z_Param_SeasonProgress);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function ApplySeasonalMaterialChanges 

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function ApplyTextureBlending ***
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventApplyTextureBlending_Parms
	{
		UTexture2D* BaseTexture;
		UTexture2D* BlendTexture;
		FAuracronTextureBlendingData BlendingData;
		UTexture2D* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaseTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BlendTexture;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendingData;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_BaseTexture = { "BaseTexture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplyTextureBlending_Parms, BaseTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_BlendTexture = { "BlendTexture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplyTextureBlending_Parms, BlendTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_BlendingData = { "BlendingData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplyTextureBlending_Parms, BlendingData), Z_Construct_UScriptStruct_FAuracronTextureBlendingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingData_MetaData), NewProp_BlendingData_MetaData) }; // 4032748465
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventApplyTextureBlending_Parms, ReturnValue), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_BaseTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_BlendTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_BlendingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "ApplyTextureBlending", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventApplyTextureBlending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventApplyTextureBlending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execApplyTextureBlending)
{
	P_GET_OBJECT(UTexture2D,Z_Param_BaseTexture);
	P_GET_OBJECT(UTexture2D,Z_Param_BlendTexture);
	P_GET_STRUCT_REF(FAuracronTextureBlendingData,Z_Param_Out_BlendingData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UTexture2D**)Z_Param__Result=P_THIS->ApplyTextureBlending(Z_Param_BaseTexture,Z_Param_BlendTexture,Z_Param_Out_BlendingData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function ApplyTextureBlending *****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function AssignProceduralMaterial 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventAssignProceduralMaterial_Parms
	{
		FString FoliageInstanceId;
		FVector Location;
		FString BiomeId;
		UMaterialInterface* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Procedural material assignment\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural material assignment" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventAssignProceduralMaterial_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventAssignProceduralMaterial_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventAssignProceduralMaterial_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventAssignProceduralMaterial_Parms, ReturnValue), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "AssignProceduralMaterial", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::AuracronFoliageMaterialVariationManager_eventAssignProceduralMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::AuracronFoliageMaterialVariationManager_eventAssignProceduralMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execAssignProceduralMaterial)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInterface**)Z_Param__Result=P_THIS->AssignProceduralMaterial(Z_Param_FoliageInstanceId,Z_Param_Out_Location,Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function AssignProceduralMaterial *

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function CreateColorVariation ***
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventCreateColorVariation_Parms
	{
		FString FoliageTypeId;
		FAuracronColorVariationData ColorData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Color variation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Color variation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateColorVariation_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::NewProp_ColorData = { "ColorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateColorVariation_Parms, ColorData), Z_Construct_UScriptStruct_FAuracronColorVariationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorData_MetaData), NewProp_ColorData_MetaData) }; // 1309543827
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateColorVariation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::NewProp_ColorData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "CreateColorVariation", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventCreateColorVariation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventCreateColorVariation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execCreateColorVariation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_STRUCT_REF(FAuracronColorVariationData,Z_Param_Out_ColorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateColorVariation(Z_Param_FoliageTypeId,Z_Param_Out_ColorData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function CreateColorVariation *****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function CreateMaterialInstance *
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventCreateMaterialInstance_Parms
	{
		FString FoliageInstanceId;
		UMaterialInterface* BaseMaterial;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material instance management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material instance management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaseMaterial;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateMaterialInstance_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::NewProp_BaseMaterial = { "BaseMaterial", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateMaterialInstance_Parms, BaseMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateMaterialInstance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::NewProp_BaseMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "CreateMaterialInstance", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventCreateMaterialInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventCreateMaterialInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execCreateMaterialInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_GET_OBJECT(UMaterialInterface,Z_Param_BaseMaterial);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateMaterialInstance(Z_Param_FoliageInstanceId,Z_Param_BaseMaterial);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function CreateMaterialInstance ***

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function CreateTextureBlending **
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventCreateTextureBlending_Parms
	{
		FString FoliageTypeId;
		FAuracronTextureBlendingData BlendingData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Texture blending\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture blending" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendingData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateTextureBlending_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::NewProp_BlendingData = { "BlendingData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateTextureBlending_Parms, BlendingData), Z_Construct_UScriptStruct_FAuracronTextureBlendingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingData_MetaData), NewProp_BlendingData_MetaData) }; // 4032748465
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventCreateTextureBlending_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::NewProp_BlendingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "CreateTextureBlending", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventCreateTextureBlending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventCreateTextureBlending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execCreateTextureBlending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_STRUCT_REF(FAuracronTextureBlendingData,Z_Param_Out_BlendingData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateTextureBlending(Z_Param_FoliageTypeId,Z_Param_Out_BlendingData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function CreateTextureBlending ****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function DrawDebugMaterialInfo **
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventDrawDebugMaterialInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventDrawDebugMaterialInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "DrawDebugMaterialInfo", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::AuracronFoliageMaterialVariationManager_eventDrawDebugMaterialInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::AuracronFoliageMaterialVariationManager_eventDrawDebugMaterialInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execDrawDebugMaterialInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugMaterialInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function DrawDebugMaterialInfo ****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function EnableDebugVisualization 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::AuracronFoliageMaterialVariationManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::AuracronFoliageMaterialVariationManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function EnableDebugVisualization *

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetActiveMaterialInstanceCount 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetActiveMaterialInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetActiveMaterialInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetActiveMaterialInstanceCount", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::AuracronFoliageMaterialVariationManager_eventGetActiveMaterialInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::AuracronFoliageMaterialVariationManager_eventGetActiveMaterialInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetActiveMaterialInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveMaterialInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetActiveMaterialInstanceCount 

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetAllMaterialInstances 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetAllMaterialInstances_Parms
	{
		TArray<FAuracronMaterialInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronMaterialInstanceData, METADATA_PARAMS(0, nullptr) }; // 16143705
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetAllMaterialInstances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 16143705
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetAllMaterialInstances", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::AuracronFoliageMaterialVariationManager_eventGetAllMaterialInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::AuracronFoliageMaterialVariationManager_eventGetAllMaterialInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetAllMaterialInstances)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronMaterialInstanceData>*)Z_Param__Result=P_THIS->GetAllMaterialInstances();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetAllMaterialInstances **

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetColorVariation ******
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetColorVariation_Parms
	{
		FString VariationId;
		FAuracronColorVariationData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariationId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::NewProp_VariationId = { "VariationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetColorVariation_Parms, VariationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationId_MetaData), NewProp_VariationId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetColorVariation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronColorVariationData, METADATA_PARAMS(0, nullptr) }; // 1309543827
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::NewProp_VariationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetColorVariation", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventGetColorVariation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventGetColorVariation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetColorVariation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VariationId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronColorVariationData*)Z_Param__Result=P_THIS->GetColorVariation(Z_Param_VariationId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetColorVariation ********

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetConfiguration *******
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetConfiguration_Parms
	{
		FAuracronFoliageMaterialVariationConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration, METADATA_PARAMS(0, nullptr) }; // 1857622286
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::AuracronFoliageMaterialVariationManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::AuracronFoliageMaterialVariationManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageMaterialVariationConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetConfiguration *********

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetInstance ************
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetInstance_Parms
	{
		UAuracronFoliageMaterialVariationManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::AuracronFoliageMaterialVariationManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::AuracronFoliageMaterialVariationManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageMaterialVariationManager**)Z_Param__Result=UAuracronFoliageMaterialVariationManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetInstance **************

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetMaterialInstance ****
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetMaterialInstance_Parms
	{
		FString InstanceId;
		FAuracronMaterialInstanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetMaterialInstance_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetMaterialInstance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronMaterialInstanceData, METADATA_PARAMS(0, nullptr) }; // 16143705
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetMaterialInstance", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventGetMaterialInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventGetMaterialInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetMaterialInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronMaterialInstanceData*)Z_Param__Result=P_THIS->GetMaterialInstance(Z_Param_InstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetMaterialInstance ******

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetMaterialMemoryUsage *
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetMaterialMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetMaterialMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetMaterialMemoryUsage", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::AuracronFoliageMaterialVariationManager_eventGetMaterialMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::AuracronFoliageMaterialVariationManager_eventGetMaterialMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetMaterialMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMaterialMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetMaterialMemoryUsage ***

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetMaterialsForBiome ***
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetMaterialsForBiome_Parms
	{
		FString BiomeId;
		TArray<UMaterialInterface*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetMaterialsForBiome_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetMaterialsForBiome_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetMaterialsForBiome", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::AuracronFoliageMaterialVariationManager_eventGetMaterialsForBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::AuracronFoliageMaterialVariationManager_eventGetMaterialsForBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetMaterialsForBiome)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<UMaterialInterface*>*)Z_Param__Result=P_THIS->GetMaterialsForBiome(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetMaterialsForBiome *****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetPerformanceData *****
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetPerformanceData_Parms
	{
		FAuracronMaterialVariationPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData, METADATA_PARAMS(0, nullptr) }; // 731253120
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::AuracronFoliageMaterialVariationManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::AuracronFoliageMaterialVariationManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronMaterialVariationPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetPerformanceData *******

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function GetTextureBlending *****
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventGetTextureBlending_Parms
	{
		FString BlendingId;
		FAuracronTextureBlendingData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::NewProp_BlendingId = { "BlendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetTextureBlending_Parms, BlendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingId_MetaData), NewProp_BlendingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventGetTextureBlending_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureBlendingData, METADATA_PARAMS(0, nullptr) }; // 4032748465
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::NewProp_BlendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "GetTextureBlending", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventGetTextureBlending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventGetTextureBlending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execGetTextureBlending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BlendingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureBlendingData*)Z_Param__Result=P_THIS->GetTextureBlending(Z_Param_BlendingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function GetTextureBlending *******

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function Initialize *************
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventInitialize_Parms
	{
		FAuracronFoliageMaterialVariationConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1857622286
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::AuracronFoliageMaterialVariationManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::AuracronFoliageMaterialVariationManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronFoliageMaterialVariationConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function Initialize ***************

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function IntegrateWithSeasonalSystem 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventIntegrateWithSeasonalSystem_Parms
	{
		UAuracronFoliageSeasonalManager* SeasonalManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SeasonalManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::NewProp_SeasonalManager = { "SeasonalManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventIntegrateWithSeasonalSystem_Parms, SeasonalManager), Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::NewProp_SeasonalManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "IntegrateWithSeasonalSystem", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::AuracronFoliageMaterialVariationManager_eventIntegrateWithSeasonalSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::AuracronFoliageMaterialVariationManager_eventIntegrateWithSeasonalSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execIntegrateWithSeasonalSystem)
{
	P_GET_OBJECT(UAuracronFoliageSeasonalManager,Z_Param_SeasonalManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithSeasonalSystem(Z_Param_SeasonalManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function IntegrateWithSeasonalSystem 

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function IsDebugVisualizationEnabled 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageMaterialVariationManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageMaterialVariationManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function IsDebugVisualizationEnabled 

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function IsInitialized **********
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::AuracronFoliageMaterialVariationManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::AuracronFoliageMaterialVariationManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function IsInitialized ************

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function LogMaterialVariationStatistics 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_LogMaterialVariationStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_LogMaterialVariationStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "LogMaterialVariationStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_LogMaterialVariationStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_LogMaterialVariationStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_LogMaterialVariationStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_LogMaterialVariationStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execLogMaterialVariationStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogMaterialVariationStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function LogMaterialVariationStatistics 

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function RemoveColorVariation ***
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventRemoveColorVariation_Parms
	{
		FString VariationId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariationId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::NewProp_VariationId = { "VariationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventRemoveColorVariation_Parms, VariationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationId_MetaData), NewProp_VariationId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventRemoveColorVariation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventRemoveColorVariation_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::NewProp_VariationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "RemoveColorVariation", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventRemoveColorVariation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventRemoveColorVariation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execRemoveColorVariation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VariationId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveColorVariation(Z_Param_VariationId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function RemoveColorVariation *****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function RemoveMaterialInstance *
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventRemoveMaterialInstance_Parms
	{
		FString InstanceId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventRemoveMaterialInstance_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventRemoveMaterialInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventRemoveMaterialInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "RemoveMaterialInstance", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventRemoveMaterialInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventRemoveMaterialInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execRemoveMaterialInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveMaterialInstance(Z_Param_InstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function RemoveMaterialInstance ***

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function RemoveTextureBlending **
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventRemoveTextureBlending_Parms
	{
		FString BlendingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::NewProp_BlendingId = { "BlendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventRemoveTextureBlending_Parms, BlendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingId_MetaData), NewProp_BlendingId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventRemoveTextureBlending_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventRemoveTextureBlending_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::NewProp_BlendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "RemoveTextureBlending", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventRemoveTextureBlending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventRemoveTextureBlending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execRemoveTextureBlending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BlendingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveTextureBlending(Z_Param_BlendingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function RemoveTextureBlending ****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function SelectMaterialByDensity 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventSelectMaterialByDensity_Parms
	{
		float Density;
		UMaterialInterface* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventSelectMaterialByDensity_Parms, Density), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventSelectMaterialByDensity_Parms, ReturnValue), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "SelectMaterialByDensity", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::AuracronFoliageMaterialVariationManager_eventSelectMaterialByDensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::AuracronFoliageMaterialVariationManager_eventSelectMaterialByDensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execSelectMaterialByDensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInterface**)Z_Param__Result=P_THIS->SelectMaterialByDensity(Z_Param_Density);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function SelectMaterialByDensity **

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function SelectMaterialByDistance 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventSelectMaterialByDistance_Parms
	{
		FVector Location;
		float Distance;
		UMaterialInterface* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventSelectMaterialByDistance_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventSelectMaterialByDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventSelectMaterialByDistance_Parms, ReturnValue), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "SelectMaterialByDistance", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::AuracronFoliageMaterialVariationManager_eventSelectMaterialByDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::AuracronFoliageMaterialVariationManager_eventSelectMaterialByDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execSelectMaterialByDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInterface**)Z_Param__Result=P_THIS->SelectMaterialByDistance(Z_Param_Out_Location,Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function SelectMaterialByDistance *

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function SetConfiguration *******
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventSetConfiguration_Parms
	{
		FAuracronFoliageMaterialVariationConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1857622286
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::AuracronFoliageMaterialVariationManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::AuracronFoliageMaterialVariationManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronFoliageMaterialVariationConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function SetConfiguration *********

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function Shutdown ***************
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function Shutdown *****************

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function SynchronizeWithBiomeSystem 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventSynchronizeWithBiomeSystem_Parms
	{
		FString BiomeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventSynchronizeWithBiomeSystem_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::NewProp_BiomeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "SynchronizeWithBiomeSystem", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::AuracronFoliageMaterialVariationManager_eventSynchronizeWithBiomeSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::AuracronFoliageMaterialVariationManager_eventSynchronizeWithBiomeSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execSynchronizeWithBiomeSystem)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithBiomeSystem(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function SynchronizeWithBiomeSystem 

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function Tick *******************
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::AuracronFoliageMaterialVariationManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::AuracronFoliageMaterialVariationManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function Tick *********************

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function UpdateColorVariation ***
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventUpdateColorVariation_Parms
	{
		FString VariationId;
		FAuracronColorVariationData ColorData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariationId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_VariationId = { "VariationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventUpdateColorVariation_Parms, VariationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationId_MetaData), NewProp_VariationId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_ColorData = { "ColorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventUpdateColorVariation_Parms, ColorData), Z_Construct_UScriptStruct_FAuracronColorVariationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorData_MetaData), NewProp_ColorData_MetaData) }; // 1309543827
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventUpdateColorVariation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventUpdateColorVariation_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_VariationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_ColorData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "UpdateColorVariation", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventUpdateColorVariation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::AuracronFoliageMaterialVariationManager_eventUpdateColorVariation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execUpdateColorVariation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VariationId);
	P_GET_STRUCT_REF(FAuracronColorVariationData,Z_Param_Out_ColorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateColorVariation(Z_Param_VariationId,Z_Param_Out_ColorData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function UpdateColorVariation *****

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function UpdateMaterialInstance *
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventUpdateMaterialInstance_Parms
	{
		FString InstanceId;
		FAuracronMaterialInstanceData InstanceData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventUpdateMaterialInstance_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_InstanceData = { "InstanceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventUpdateMaterialInstance_Parms, InstanceData), Z_Construct_UScriptStruct_FAuracronMaterialInstanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceData_MetaData), NewProp_InstanceData_MetaData) }; // 16143705
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventUpdateMaterialInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventUpdateMaterialInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_InstanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "UpdateMaterialInstance", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventUpdateMaterialInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::AuracronFoliageMaterialVariationManager_eventUpdateMaterialInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execUpdateMaterialInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_GET_STRUCT_REF(FAuracronMaterialInstanceData,Z_Param_Out_InstanceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateMaterialInstance(Z_Param_InstanceId,Z_Param_Out_InstanceData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function UpdateMaterialInstance ***

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function UpdatePerformanceMetrics 
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function UpdatePerformanceMetrics *

// ********** Begin Class UAuracronFoliageMaterialVariationManager Function UpdateTextureBlending **
struct Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics
{
	struct AuracronFoliageMaterialVariationManager_eventUpdateTextureBlending_Parms
	{
		FString BlendingId;
		FAuracronTextureBlendingData BlendingData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Variation Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendingData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_BlendingId = { "BlendingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventUpdateTextureBlending_Parms, BlendingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingId_MetaData), NewProp_BlendingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_BlendingData = { "BlendingData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageMaterialVariationManager_eventUpdateTextureBlending_Parms, BlendingData), Z_Construct_UScriptStruct_FAuracronTextureBlendingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingData_MetaData), NewProp_BlendingData_MetaData) }; // 4032748465
void Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageMaterialVariationManager_eventUpdateTextureBlending_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageMaterialVariationManager_eventUpdateTextureBlending_Parms), &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_BlendingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_BlendingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, nullptr, "UpdateTextureBlending", Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventUpdateTextureBlending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::AuracronFoliageMaterialVariationManager_eventUpdateTextureBlending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageMaterialVariationManager::execUpdateTextureBlending)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BlendingId);
	P_GET_STRUCT_REF(FAuracronTextureBlendingData,Z_Param_Out_BlendingData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateTextureBlending(Z_Param_BlendingId,Z_Param_Out_BlendingData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageMaterialVariationManager Function UpdateTextureBlending ****

// ********** Begin Class UAuracronFoliageMaterialVariationManager *********************************
void UAuracronFoliageMaterialVariationManager::StaticRegisterNativesUAuracronFoliageMaterialVariationManager()
{
	UClass* Class = UAuracronFoliageMaterialVariationManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyColorVariation", &UAuracronFoliageMaterialVariationManager::execApplyColorVariation },
		{ "ApplySeasonalMaterialChanges", &UAuracronFoliageMaterialVariationManager::execApplySeasonalMaterialChanges },
		{ "ApplyTextureBlending", &UAuracronFoliageMaterialVariationManager::execApplyTextureBlending },
		{ "AssignProceduralMaterial", &UAuracronFoliageMaterialVariationManager::execAssignProceduralMaterial },
		{ "CreateColorVariation", &UAuracronFoliageMaterialVariationManager::execCreateColorVariation },
		{ "CreateMaterialInstance", &UAuracronFoliageMaterialVariationManager::execCreateMaterialInstance },
		{ "CreateTextureBlending", &UAuracronFoliageMaterialVariationManager::execCreateTextureBlending },
		{ "DrawDebugMaterialInfo", &UAuracronFoliageMaterialVariationManager::execDrawDebugMaterialInfo },
		{ "EnableDebugVisualization", &UAuracronFoliageMaterialVariationManager::execEnableDebugVisualization },
		{ "GetActiveMaterialInstanceCount", &UAuracronFoliageMaterialVariationManager::execGetActiveMaterialInstanceCount },
		{ "GetAllMaterialInstances", &UAuracronFoliageMaterialVariationManager::execGetAllMaterialInstances },
		{ "GetColorVariation", &UAuracronFoliageMaterialVariationManager::execGetColorVariation },
		{ "GetConfiguration", &UAuracronFoliageMaterialVariationManager::execGetConfiguration },
		{ "GetInstance", &UAuracronFoliageMaterialVariationManager::execGetInstance },
		{ "GetMaterialInstance", &UAuracronFoliageMaterialVariationManager::execGetMaterialInstance },
		{ "GetMaterialMemoryUsage", &UAuracronFoliageMaterialVariationManager::execGetMaterialMemoryUsage },
		{ "GetMaterialsForBiome", &UAuracronFoliageMaterialVariationManager::execGetMaterialsForBiome },
		{ "GetPerformanceData", &UAuracronFoliageMaterialVariationManager::execGetPerformanceData },
		{ "GetTextureBlending", &UAuracronFoliageMaterialVariationManager::execGetTextureBlending },
		{ "Initialize", &UAuracronFoliageMaterialVariationManager::execInitialize },
		{ "IntegrateWithSeasonalSystem", &UAuracronFoliageMaterialVariationManager::execIntegrateWithSeasonalSystem },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageMaterialVariationManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageMaterialVariationManager::execIsInitialized },
		{ "LogMaterialVariationStatistics", &UAuracronFoliageMaterialVariationManager::execLogMaterialVariationStatistics },
		{ "RemoveColorVariation", &UAuracronFoliageMaterialVariationManager::execRemoveColorVariation },
		{ "RemoveMaterialInstance", &UAuracronFoliageMaterialVariationManager::execRemoveMaterialInstance },
		{ "RemoveTextureBlending", &UAuracronFoliageMaterialVariationManager::execRemoveTextureBlending },
		{ "SelectMaterialByDensity", &UAuracronFoliageMaterialVariationManager::execSelectMaterialByDensity },
		{ "SelectMaterialByDistance", &UAuracronFoliageMaterialVariationManager::execSelectMaterialByDistance },
		{ "SetConfiguration", &UAuracronFoliageMaterialVariationManager::execSetConfiguration },
		{ "Shutdown", &UAuracronFoliageMaterialVariationManager::execShutdown },
		{ "SynchronizeWithBiomeSystem", &UAuracronFoliageMaterialVariationManager::execSynchronizeWithBiomeSystem },
		{ "Tick", &UAuracronFoliageMaterialVariationManager::execTick },
		{ "UpdateColorVariation", &UAuracronFoliageMaterialVariationManager::execUpdateColorVariation },
		{ "UpdateMaterialInstance", &UAuracronFoliageMaterialVariationManager::execUpdateMaterialInstance },
		{ "UpdatePerformanceMetrics", &UAuracronFoliageMaterialVariationManager::execUpdatePerformanceMetrics },
		{ "UpdateTextureBlending", &UAuracronFoliageMaterialVariationManager::execUpdateTextureBlending },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager;
UClass* UAuracronFoliageMaterialVariationManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageMaterialVariationManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageMaterialVariationManager"),
			Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageMaterialVariationManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_NoRegister()
{
	return UAuracronFoliageMaterialVariationManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Material Variation Manager\n * Manager for foliage material variation system\n */" },
#endif
		{ "IncludePath", "AuracronFoliageMaterialVariation.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Material Variation Manager\nManager for foliage material variation system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMaterialInstanceCreated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMaterialInstanceRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnColorVariationApplied_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTextureBlendingCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalManager_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageMaterialVariation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMaterialInstanceCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMaterialInstanceRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnColorVariationApplied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTextureBlendingCompleted;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_SeasonalManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyColorVariation, "ApplyColorVariation" }, // 2405356673
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplySeasonalMaterialChanges, "ApplySeasonalMaterialChanges" }, // 1808559171
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_ApplyTextureBlending, "ApplyTextureBlending" }, // 1945257692
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_AssignProceduralMaterial, "AssignProceduralMaterial" }, // 921047859
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateColorVariation, "CreateColorVariation" }, // 3698498820
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateMaterialInstance, "CreateMaterialInstance" }, // 3255419804
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_CreateTextureBlending, "CreateTextureBlending" }, // 3167814832
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_DrawDebugMaterialInfo, "DrawDebugMaterialInfo" }, // 3391495122
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 159101308
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetActiveMaterialInstanceCount, "GetActiveMaterialInstanceCount" }, // 1629728078
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetAllMaterialInstances, "GetAllMaterialInstances" }, // 2720315152
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetColorVariation, "GetColorVariation" }, // 2337810126
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetConfiguration, "GetConfiguration" }, // 1461041693
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetInstance, "GetInstance" }, // 3054652434
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialInstance, "GetMaterialInstance" }, // 2270715101
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialMemoryUsage, "GetMaterialMemoryUsage" }, // 2038188698
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetMaterialsForBiome, "GetMaterialsForBiome" }, // 2393669957
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetPerformanceData, "GetPerformanceData" }, // 2945984160
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_GetTextureBlending, "GetTextureBlending" }, // 826999302
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Initialize, "Initialize" }, // 3669368685
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IntegrateWithSeasonalSystem, "IntegrateWithSeasonalSystem" }, // 4203261906
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 2150492063
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_IsInitialized, "IsInitialized" }, // 656852388
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_LogMaterialVariationStatistics, "LogMaterialVariationStatistics" }, // 3922369321
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature, "OnColorVariationApplied__DelegateSignature" }, // 4240576318
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature, "OnMaterialInstanceCreated__DelegateSignature" }, // 3664403200
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature, "OnMaterialInstanceRemoved__DelegateSignature" }, // 1266997974
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature, "OnTextureBlendingCompleted__DelegateSignature" }, // 1080204083
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveColorVariation, "RemoveColorVariation" }, // 150041113
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveMaterialInstance, "RemoveMaterialInstance" }, // 691819748
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_RemoveTextureBlending, "RemoveTextureBlending" }, // 2690789899
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDensity, "SelectMaterialByDensity" }, // 3305556473
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SelectMaterialByDistance, "SelectMaterialByDistance" }, // 3557407535
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SetConfiguration, "SetConfiguration" }, // 2803907525
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Shutdown, "Shutdown" }, // 2393895062
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_SynchronizeWithBiomeSystem, "SynchronizeWithBiomeSystem" }, // 3458349646
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_Tick, "Tick" }, // 1521957410
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateColorVariation, "UpdateColorVariation" }, // 4093212505
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateMaterialInstance, "UpdateMaterialInstance" }, // 3324410722
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 1134786291
		{ &Z_Construct_UFunction_UAuracronFoliageMaterialVariationManager_UpdateTextureBlending, "UpdateTextureBlending" }, // 902203222
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageMaterialVariationManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnMaterialInstanceCreated = { "OnMaterialInstanceCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageMaterialVariationManager, OnMaterialInstanceCreated), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMaterialInstanceCreated_MetaData), NewProp_OnMaterialInstanceCreated_MetaData) }; // 3664403200
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnMaterialInstanceRemoved = { "OnMaterialInstanceRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageMaterialVariationManager, OnMaterialInstanceRemoved), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMaterialInstanceRemoved_MetaData), NewProp_OnMaterialInstanceRemoved_MetaData) }; // 1266997974
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnColorVariationApplied = { "OnColorVariationApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageMaterialVariationManager, OnColorVariationApplied), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnColorVariationApplied_MetaData), NewProp_OnColorVariationApplied_MetaData) }; // 4240576318
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnTextureBlendingCompleted = { "OnTextureBlendingCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageMaterialVariationManager, OnTextureBlendingCompleted), Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTextureBlendingCompleted_MetaData), NewProp_OnTextureBlendingCompleted_MetaData) }; // 1080204083
void Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageMaterialVariationManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageMaterialVariationManager), &Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageMaterialVariationManager, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1857622286
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageMaterialVariationManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_SeasonalManager = { "SeasonalManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageMaterialVariationManager, SeasonalManager), Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalManager_MetaData), NewProp_SeasonalManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnMaterialInstanceCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnMaterialInstanceRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnColorVariationApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_OnTextureBlendingCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::NewProp_SeasonalManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::ClassParams = {
	&UAuracronFoliageMaterialVariationManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageMaterialVariationManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager.OuterSingleton;
}
UAuracronFoliageMaterialVariationManager::UAuracronFoliageMaterialVariationManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageMaterialVariationManager);
UAuracronFoliageMaterialVariationManager::~UAuracronFoliageMaterialVariationManager() {}
// ********** End Class UAuracronFoliageMaterialVariationManager ***********************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronMaterialVariationStrategy_StaticEnum, TEXT("EAuracronMaterialVariationStrategy"), &Z_Registration_Info_UEnum_EAuracronMaterialVariationStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1155178383U) },
		{ EAuracronColorVariationMode_StaticEnum, TEXT("EAuracronColorVariationMode"), &Z_Registration_Info_UEnum_EAuracronColorVariationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1115969821U) },
		{ EAuracronTextureBlendingMode_StaticEnum, TEXT("EAuracronTextureBlendingMode"), &Z_Registration_Info_UEnum_EAuracronTextureBlendingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 867664803U) },
		{ EAuracronMaterialAssignmentMode_StaticEnum, TEXT("EAuracronMaterialAssignmentMode"), &Z_Registration_Info_UEnum_EAuracronMaterialAssignmentMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4285863054U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronFoliageMaterialVariationConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics::NewStructOps, TEXT("AuracronFoliageMaterialVariationConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageMaterialVariationConfiguration), 1857622286U) },
		{ FAuracronColorVariationData::StaticStruct, Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics::NewStructOps, TEXT("AuracronColorVariationData"), &Z_Registration_Info_UScriptStruct_FAuracronColorVariationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronColorVariationData), 1309543827U) },
		{ FAuracronTextureBlendingData::StaticStruct, Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics::NewStructOps, TEXT("AuracronTextureBlendingData"), &Z_Registration_Info_UScriptStruct_FAuracronTextureBlendingData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTextureBlendingData), 4032748465U) },
		{ FAuracronMaterialInstanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics::NewStructOps, TEXT("AuracronMaterialInstanceData"), &Z_Registration_Info_UScriptStruct_FAuracronMaterialInstanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMaterialInstanceData), 16143705U) },
		{ FAuracronMaterialVariationPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics::NewStructOps, TEXT("AuracronMaterialVariationPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronMaterialVariationPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMaterialVariationPerformanceData), 731253120U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageMaterialVariationManager, UAuracronFoliageMaterialVariationManager::StaticClass, TEXT("UAuracronFoliageMaterialVariationManager"), &Z_Registration_Info_UClass_UAuracronFoliageMaterialVariationManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageMaterialVariationManager), 3972302516U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_3326689016(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
