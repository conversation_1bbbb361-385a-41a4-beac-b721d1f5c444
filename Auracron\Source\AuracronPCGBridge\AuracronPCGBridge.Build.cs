using UnrealBuildTool;
public class AuracronPCGBridge : ModuleRules
{
    public AuracronPCGBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine","GeometryScriptingCore",
            "GeometryFramework",
            "DynamicMesh",
            "MeshDescription",
            "StaticMeshDescription",
            "MeshConversion",
            "ModelingComponents",
            "ModelingOperators",
            "GeometryProcessingInterfaces",
            "MeshUtilities",
            "MeshUtilitiesCommon",
            "PythonScriptPlugin",
            "BlueprintGraph",
            "KismetCompiler",
            "ToolMenus","RenderCore",
            "RHI"
        });
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Slate",
            "SlateCore",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "EngineSettings",
            "RenderCore",
            "RHI",
            "Projects","DesktopPlatform",
            "ApplicationCore",
            "AppFramework",
            "MainFrame",
            "SourceControl",
            "SourceControlWindows",
            "ToolWidgets",
            "WorkspaceMenuStructure",
            "AssetTools",
            "AssetRegistry",
            "ContentBrowser",
            "ContentBrowserData",
            "EditorSubsystem",
            "GameplayTags",
            "GameplayTasks",
            "GameplayAbilities","NavigationSystem",
            "Landscape",
            "Foliage",
            "Engine",
            "DeveloperSettings","TraceLog","TraceInsights","Serialization",
            "Json",
            "Json"
        });
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers"
            });
        }
        // Enable RTTI for Python integration
        bUseRTTI = true;
        // Enable exceptions for Python integration
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // UE 5.6 specific definitions
        PublicDefinitions.Add("WITH_PCG_ADVANCED_FEATURES=1");
        PublicDefinitions.Add("WITH_PCG_ASYNC_PROCESSING=1");
        PublicDefinitions.Add("WITH_PCG_PERFORMANCE_MONITORING=1");
        PublicDefinitions.Add("WITH_PCG_MEMORY_OPTIMIZATION=1");
        PublicDefinitions.Add("WITH_PCG_CUSTOM_ELEMENTS=1");
        // Platform specific settings
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("AURACRON_PCG_BRIDGE_PLATFORM_WINDOWS=1");
            // Windows-specific optimizations
            PublicDefinitions.Add("WITH_PCG_MULTITHREADING=1");
            PublicDefinitions.Add("WITH_PCG_SIMD_OPTIMIZATIONS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("AURACRON_PCG_BRIDGE_PLATFORM_ANDROID=1");
            // Mobile-specific optimizations
            PublicDefinitions.Add("WITH_PCG_MOBILE_OPTIMIZATIONS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_PCG_BRIDGE_PLATFORM_IOS=1");
        }
        // Development and debugging settings
        if (Target.Configuration == UnrealTargetConfiguration.Debug ||
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_PCG_DEBUG=1");
            PublicDefinitions.Add("AURACRON_PCG_ENABLE_DETAILED_LOGGING=1");
            PublicDefinitions.Add("AURACRON_PCG_ENABLE_PROFILING=1");
        }
        // Shipping optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_PCG_SHIPPING=1");
            PublicDefinitions.Add("AURACRON_PCG_DISABLE_DEBUG_FEATURES=1");
            PublicDefinitions.Add("AURACRON_PCG_ENABLE_AGGRESSIVE_OPTIMIZATIONS=1");
        }
        // Enable Unity builds for faster compilation
        bUseUnity = true;
        // Enable Include-What-You-Use for better compile times
        IWYUSupport = IWYUSupport.Full;
        // C++ standard
        CppStandard = CppStandardVersion.Cpp17;
        // PCG specific definitions
        PublicDefinitions.Add("WITH_PCG=1");
        PublicDefinitions.Add("AURACRON_PCG_BRIDGE_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_PCG_BRIDGE_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_PCG_BRIDGE_VERSION_PATCH=0");
    }
}
