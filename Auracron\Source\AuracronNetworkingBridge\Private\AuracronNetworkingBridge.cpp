// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema Multiplayer 5v5 Bridge Implementation

#include "AuracronNetworkingBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/NetDriver.h"
#include "Engine/NetConnection.h"
#include "GameFramework/GameMode.h"
#include "GameFramework/GameState.h"
#include "GameFramework/PlayerState.h"
#include "GameFramework/PlayerController.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "OnlineSessionSettings.h"
#include "FindSessionsCallbackProxy.h"
#include "CreateSessionCallbackProxy.h"
#include "DestroySessionCallbackProxy.h"
#include "JoinSessionCallbackProxy.h"
#include "Net/ReplicationGraph.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"

UAuracronNetworkingBridge::UAuracronNetworkingBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // 1 FPS para monitoramento
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão
    CurrentSessionConfig.SessionName = TEXT("AuracronMatch");
    CurrentSessionConfig.MaxPlayers = 10;
    CurrentSessionConfig.MinPlayersToStart = 10;
    CurrentSessionConfig.bIsPublic = true;
    CurrentSessionConfig.bUseAntiCheat = true;
    CurrentSessionConfig.PreferredRegion = TEXT("us-east-1");
    CurrentSessionConfig.GameMode = TEXT("AuracronClassic");
    CurrentSessionConfig.MapName = TEXT("AuracronRift");
    CurrentSessionConfig.MaxMatchDuration = 1800;
    CurrentSessionConfig.ConnectionTimeout = 60;
    CurrentSessionConfig.MaxPing = 150;
    CurrentSessionConfig.bUseVoiceChat = true;
    CurrentSessionConfig.bAllowReconnection = true;
    CurrentSessionConfig.ReconnectionTimeLimit = 180;
    
    AntiCheatConfig.bValidateMovement = true;
    AntiCheatConfig.bValidateAbilities = true;
    AntiCheatConfig.bValidateDamage = true;
    AntiCheatConfig.bValidateTiming = true;
    AntiCheatConfig.MovementTolerance = 1000.0f;
    AntiCheatConfig.PingTolerance = 200;
    AntiCheatConfig.MaxActionsPerSecond = 20;
    AntiCheatConfig.SpamDetectionCooldown = 1.0f;
    AntiCheatConfig.MaxViolationsBeforeKick = 5;
    AntiCheatConfig.bLogSuspiciousActivity = true;
    AntiCheatConfig.bAutoReportCheaters = true;
    
    ReplicationConfig.bUseReplicationGraph = true;
    ReplicationConfig.bUseIrisNetworking = true;
    ReplicationConfig.bUseNetworkPrediction = true;
    ReplicationConfig.ReplicationFrequency = 60;
    ReplicationConfig.MaxReplicationDistance = 15000.0f;
    ReplicationConfig.bUsePacketCompression = true;
    ReplicationConfig.bUseDeltaCompression = true;
    ReplicationConfig.ChampionReplicationPriority = 5.0f;
    ReplicationConfig.AbilityReplicationPriority = 8.0f;
    ReplicationConfig.EffectReplicationPriority = 3.0f;
    ReplicationConfig.bUseDistanceBasedRelevancy = true;
    ReplicationConfig.bUseLineOfSightRelevancy = false;
    ReplicationConfig.bUseFrustumCulling = true;
}

void UAuracronNetworkingBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema Multiplayer 5v5"));

    // Verificar se é servidor dedicado
    bIsDedicatedServer = GetWorld()->GetNetMode() == NM_DedicatedServer;

    // Inicializar sistema
    bSystemInitialized = InitializeNetworkingSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers para monitoramento
        GetWorld()->GetTimerManager().SetTimer(
            MonitoringTimer,
            [this]()
            {
                MonitorPlayerConnections(1.0f);
                ProcessServerValidations(1.0f);
            },
            1.0f,
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            StatisticsTimer,
            [this]()
            {
                UpdateNetworkStatistics(5.0f);
            },
            5.0f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema Multiplayer 5v5 inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema Multiplayer 5v5"));
    }
}

void UAuracronNetworkingBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(MonitoringTimer);
        GetWorld()->GetTimerManager().ClearTimer(StatisticsTimer);
    }
    
    // Sair da sessão se estiver conectado
    if (CurrentSessionState != EAuracronSessionState::None && 
        CurrentSessionState != EAuracronSessionState::Disconnected)
    {
        LeaveSession();
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronNetworkingBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronNetworkingBridge, CurrentSessionConfig);
    DOREPLIFETIME(UAuracronNetworkingBridge, CurrentSessionState);
    DOREPLIFETIME(UAuracronNetworkingBridge, CurrentConnectionType);
    DOREPLIFETIME(UAuracronNetworkingBridge, ConnectedPlayers);
}

void UAuracronNetworkingBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar validações server-side
    if (bIsDedicatedServer)
    {
        ProcessServerValidations(DeltaTime);
    }
    
    // Monitorar conexões
    MonitorPlayerConnections(DeltaTime);
    
    // Atualizar estatísticas
    UpdateNetworkStatistics(DeltaTime);
}

// === Session Management ===

bool UAuracronNetworkingBridge::CreateSession(const FAuracronSessionConfiguration& SessionConfig)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema de networking não inicializado"));
        return false;
    }

    if (!ValidateSessionConfiguration(SessionConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de sessão inválida"));
        return false;
    }

    if (CurrentSessionState != EAuracronSessionState::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Já existe uma sessão ativa"));
        return false;
    }

    if (!SessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Session Interface não disponível"));
        return false;
    }

    CurrentSessionState = EAuracronSessionState::Creating;
    CurrentSessionConfig = SessionConfig;

    // Configurar settings da sessão
    FOnlineSessionSettings SessionSettings;
    SessionSettings.NumPublicConnections = SessionConfig.MaxPlayers;
    SessionSettings.NumPrivateConnections = 0;
    SessionSettings.bIsLANMatch = false;
    SessionSettings.bShouldAdvertise = SessionConfig.bIsPublic;
    SessionSettings.bAllowJoinInProgress = true;
    SessionSettings.bAllowInvites = true;
    SessionSettings.bUsesPresence = true;
    SessionSettings.bUseLobbiesIfAvailable = true;
    SessionSettings.bUseLobbiesVoiceChatIfAvailable = SessionConfig.bUseVoiceChat;

    // Configurar propriedades customizadas
    SessionSettings.Set(FName("GameMode"), SessionConfig.GameMode, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);
    SessionSettings.Set(FName("MapName"), SessionConfig.MapName, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);
    SessionSettings.Set(FName("Region"), SessionConfig.PreferredRegion, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);
    SessionSettings.Set(FName("MaxPing"), SessionConfig.MaxPing, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);
    SessionSettings.Set(FName("AntiCheat"), SessionConfig.bUseAntiCheat, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);

    // Criar sessão
    bool bCreateResult = SessionInterface->CreateSession(0, FName(*SessionConfig.SessionName), SessionSettings);

    if (bCreateResult)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Criando sessão: %s"), *SessionConfig.SessionName);
        return true;
    }
    else
    {
        CurrentSessionState = EAuracronSessionState::Error;
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar sessão"));
        return false;
    }
}

bool UAuracronNetworkingBridge::FindSessions(int32 MaxResults)
{
    if (!bSystemInitialized || !SessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou Session Interface inválido"));
        return false;
    }

    if (CurrentSessionState == EAuracronSessionState::Searching)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Já está buscando sessões"));
        return false;
    }

    CurrentSessionState = EAuracronSessionState::Searching;

    // Configurar busca
    SessionSearch = MakeShareable(new FOnlineSessionSearch());
    SessionSearch->MaxSearchResults = MaxResults;
    SessionSearch->bIsLanQuery = false;
    SessionSearch->QuerySettings.Set(SEARCH_PRESENCE, true, EOnlineComparisonOp::Equals);

    // Buscar sessões
    bool bSearchResult = SessionInterface->FindSessions(0, SessionSearch.ToSharedRef());

    if (bSearchResult)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Buscando sessões (máximo: %d)"), MaxResults);
        return true;
    }
    else
    {
        CurrentSessionState = EAuracronSessionState::Error;
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao buscar sessões"));
        return false;
    }
}
