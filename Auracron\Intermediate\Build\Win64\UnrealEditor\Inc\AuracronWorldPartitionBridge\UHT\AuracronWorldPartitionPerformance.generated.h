// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionPerformance.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionPerformance_generated_h
#error "AuracronWorldPartitionPerformance.generated.h already included, missing '#pragma once' in AuracronWorldPartitionPerformance.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionPerformance_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronWorldPartitionPerformanceManager;
class UWorld;
enum class EAuracronBottleneckType : uint8;
enum class EAuracronPerformanceMetricType : uint8;
enum class EAuracronPerformanceMonitoringState : uint8;
enum class EAuracronPerformanceSeverity : uint8;
struct FAuracronPerformanceConfiguration;
struct FAuracronPerformanceMetric;
struct FAuracronPerformanceReport;

// ********** Begin ScriptStruct FAuracronPerformanceConfiguration *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_100_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPerformanceConfiguration;
// ********** End ScriptStruct FAuracronPerformanceConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronPerformanceMetric ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_206_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPerformanceMetric;
// ********** End ScriptStruct FAuracronPerformanceMetric ******************************************

// ********** Begin ScriptStruct FAuracronPerformanceReport ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_265_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPerformanceReport;
// ********** End ScriptStruct FAuracronPerformanceReport ******************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_482_DELEGATE \
static void FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, FAuracronPerformanceMetric Metric);


// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Delegate FOnBottleneckDetected *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_483_DELEGATE \
static void FOnBottleneckDetected_DelegateWrapper(const FMulticastScriptDelegate& OnBottleneckDetected, EAuracronBottleneckType BottleneckType);


// ********** End Delegate FOnBottleneckDetected ***************************************************

// ********** Begin Delegate FOnPerformanceReportGenerated *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_484_DELEGATE \
static void FOnPerformanceReportGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceReportGenerated, FAuracronPerformanceReport Report);


// ********** End Delegate FOnPerformanceReportGenerated *******************************************

// ********** Begin Delegate FOnThresholdExceeded **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_485_DELEGATE \
static void FOnThresholdExceeded_DelegateWrapper(const FMulticastScriptDelegate& OnThresholdExceeded, EAuracronPerformanceMetricType MetricType, float Value);


// ********** End Delegate FOnThresholdExceeded ****************************************************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_319_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetMetrics); \
	DECLARE_FUNCTION(execDrawDebugPerformanceInfo); \
	DECLARE_FUNCTION(execLogPerformanceState); \
	DECLARE_FUNCTION(execIsPerformanceDebugEnabled); \
	DECLARE_FUNCTION(execEnablePerformanceDebug); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetActiveAlerts); \
	DECLARE_FUNCTION(execGetThreshold); \
	DECLARE_FUNCTION(execSetThreshold); \
	DECLARE_FUNCTION(execLoadPerformanceReport); \
	DECLARE_FUNCTION(execSavePerformanceReport); \
	DECLARE_FUNCTION(execGeneratePerformanceReport); \
	DECLARE_FUNCTION(execGetPerformanceScoreBreakdown); \
	DECLARE_FUNCTION(execCalculatePerformanceScore); \
	DECLARE_FUNCTION(execApplyAutoOptimization); \
	DECLARE_FUNCTION(execGetOptimizationSuggestionsForBottleneck); \
	DECLARE_FUNCTION(execGetOptimizationSuggestions); \
	DECLARE_FUNCTION(execGetBottleneckSeverity); \
	DECLARE_FUNCTION(execGetBottleneckDetails); \
	DECLARE_FUNCTION(execDetectBottleneck); \
	DECLARE_FUNCTION(execGetStreamingBreakdown); \
	DECLARE_FUNCTION(execGetActiveStreamingOperations); \
	DECLARE_FUNCTION(execGetStreamingBandwidthMBps); \
	DECLARE_FUNCTION(execGetGPUBreakdown); \
	DECLARE_FUNCTION(execGetGPUMemoryUsageMB); \
	DECLARE_FUNCTION(execGetCurrentGPUUsagePercent); \
	DECLARE_FUNCTION(execGetCPUBreakdown); \
	DECLARE_FUNCTION(execGetAverageCPUUsagePercent); \
	DECLARE_FUNCTION(execGetCurrentCPUUsagePercent); \
	DECLARE_FUNCTION(execGetMemoryBreakdown); \
	DECLARE_FUNCTION(execGetAvailableMemoryMB); \
	DECLARE_FUNCTION(execGetPeakMemoryUsageMB); \
	DECLARE_FUNCTION(execGetCurrentMemoryUsageMB); \
	DECLARE_FUNCTION(execGetAllCurrentMetrics); \
	DECLARE_FUNCTION(execGetMetricHistory); \
	DECLARE_FUNCTION(execGetCurrentMetric); \
	DECLARE_FUNCTION(execCollectMetrics); \
	DECLARE_FUNCTION(execGetMonitoringState); \
	DECLARE_FUNCTION(execResumeMonitoring); \
	DECLARE_FUNCTION(execPauseMonitoring); \
	DECLARE_FUNCTION(execStopMonitoring); \
	DECLARE_FUNCTION(execStartMonitoring); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_319_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionPerformanceManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionPerformanceManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionPerformanceManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_319_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionPerformanceManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionPerformanceManager(UAuracronWorldPartitionPerformanceManager&&) = delete; \
	UAuracronWorldPartitionPerformanceManager(const UAuracronWorldPartitionPerformanceManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionPerformanceManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionPerformanceManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionPerformanceManager) \
	NO_API virtual ~UAuracronWorldPartitionPerformanceManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_316_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_319_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_319_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_319_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h_319_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionPerformanceManager;

// ********** End Class UAuracronWorldPartitionPerformanceManager **********************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h

// ********** Begin Enum EAuracronPerformanceMonitoringState ***************************************
#define FOREACH_ENUM_EAURACRONPERFORMANCEMONITORINGSTATE(op) \
	op(EAuracronPerformanceMonitoringState::Disabled) \
	op(EAuracronPerformanceMonitoringState::Enabled) \
	op(EAuracronPerformanceMonitoringState::Recording) \
	op(EAuracronPerformanceMonitoringState::Analyzing) \
	op(EAuracronPerformanceMonitoringState::Reporting) 

enum class EAuracronPerformanceMonitoringState : uint8;
template<> struct TIsUEnumClass<EAuracronPerformanceMonitoringState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceMonitoringState>();
// ********** End Enum EAuracronPerformanceMonitoringState *****************************************

// ********** Begin Enum EAuracronPerformanceSeverity **********************************************
#define FOREACH_ENUM_EAURACRONPERFORMANCESEVERITY(op) \
	op(EAuracronPerformanceSeverity::Info) \
	op(EAuracronPerformanceSeverity::Warning) \
	op(EAuracronPerformanceSeverity::Critical) \
	op(EAuracronPerformanceSeverity::Emergency) 

enum class EAuracronPerformanceSeverity : uint8;
template<> struct TIsUEnumClass<EAuracronPerformanceSeverity> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceSeverity>();
// ********** End Enum EAuracronPerformanceSeverity ************************************************

// ********** Begin Enum EAuracronPerformanceMetricType ********************************************
#define FOREACH_ENUM_EAURACRONPERFORMANCEMETRICTYPE(op) \
	op(EAuracronPerformanceMetricType::Memory) \
	op(EAuracronPerformanceMetricType::CPU) \
	op(EAuracronPerformanceMetricType::GPU) \
	op(EAuracronPerformanceMetricType::Streaming) \
	op(EAuracronPerformanceMetricType::Network) \
	op(EAuracronPerformanceMetricType::IO) 

enum class EAuracronPerformanceMetricType : uint8;
template<> struct TIsUEnumClass<EAuracronPerformanceMetricType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceMetricType>();
// ********** End Enum EAuracronPerformanceMetricType **********************************************

// ********** Begin Enum EAuracronBottleneckType ***************************************************
#define FOREACH_ENUM_EAURACRONBOTTLENECKTYPE(op) \
	op(EAuracronBottleneckType::None) \
	op(EAuracronBottleneckType::Memory) \
	op(EAuracronBottleneckType::CPU) \
	op(EAuracronBottleneckType::GPU) \
	op(EAuracronBottleneckType::IO) \
	op(EAuracronBottleneckType::Network) \
	op(EAuracronBottleneckType::Streaming) 

enum class EAuracronBottleneckType : uint8;
template<> struct TIsUEnumClass<EAuracronBottleneckType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronBottleneckType>();
// ********** End Enum EAuracronBottleneckType *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
