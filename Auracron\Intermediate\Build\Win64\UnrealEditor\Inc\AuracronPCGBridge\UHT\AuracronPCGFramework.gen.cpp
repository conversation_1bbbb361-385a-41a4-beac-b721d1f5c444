// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGFramework.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGFramework() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGConfiguration();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGErrorInfo();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGErrorCode *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGErrorCode;
static UEnum* EAuracronPCGErrorCode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGErrorCode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGErrorCode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGErrorCode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGErrorCode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGErrorCode>()
{
	return EAuracronPCGErrorCode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Error codes for PCG operations\n" },
#endif
		{ "ExecutionTimeout.DisplayName", "Execution Timeout" },
		{ "ExecutionTimeout.Name", "EAuracronPCGErrorCode::ExecutionTimeout" },
		{ "GenerationFailed.DisplayName", "Generation Failed" },
		{ "GenerationFailed.Name", "EAuracronPCGErrorCode::GenerationFailed" },
		{ "InvalidElement.DisplayName", "Invalid Element" },
		{ "InvalidElement.Name", "EAuracronPCGErrorCode::InvalidElement" },
		{ "InvalidGraph.DisplayName", "Invalid Graph" },
		{ "InvalidGraph.Name", "EAuracronPCGErrorCode::InvalidGraph" },
		{ "InvalidInput.DisplayName", "Invalid Input" },
		{ "InvalidInput.Name", "EAuracronPCGErrorCode::InvalidInput" },
		{ "InvalidMetadata.DisplayName", "Invalid Metadata" },
		{ "InvalidMetadata.Name", "EAuracronPCGErrorCode::InvalidMetadata" },
		{ "InvalidPointData.DisplayName", "Invalid Point Data" },
		{ "InvalidPointData.Name", "EAuracronPCGErrorCode::InvalidPointData" },
		{ "InvalidSpatialData.DisplayName", "Invalid Spatial Data" },
		{ "InvalidSpatialData.Name", "EAuracronPCGErrorCode::InvalidSpatialData" },
		{ "MemoryAllocationFailed.DisplayName", "Memory Allocation Failed" },
		{ "MemoryAllocationFailed.Name", "EAuracronPCGErrorCode::MemoryAllocationFailed" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
		{ "None.DisplayName", "No Error" },
		{ "None.Name", "EAuracronPCGErrorCode::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Error codes for PCG operations" },
#endif
		{ "UnknownError.DisplayName", "Unknown Error" },
		{ "UnknownError.Name", "EAuracronPCGErrorCode::UnknownError" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGErrorCode::None", (int64)EAuracronPCGErrorCode::None },
		{ "EAuracronPCGErrorCode::InvalidInput", (int64)EAuracronPCGErrorCode::InvalidInput },
		{ "EAuracronPCGErrorCode::InvalidGraph", (int64)EAuracronPCGErrorCode::InvalidGraph },
		{ "EAuracronPCGErrorCode::InvalidElement", (int64)EAuracronPCGErrorCode::InvalidElement },
		{ "EAuracronPCGErrorCode::ExecutionTimeout", (int64)EAuracronPCGErrorCode::ExecutionTimeout },
		{ "EAuracronPCGErrorCode::MemoryAllocationFailed", (int64)EAuracronPCGErrorCode::MemoryAllocationFailed },
		{ "EAuracronPCGErrorCode::InvalidPointData", (int64)EAuracronPCGErrorCode::InvalidPointData },
		{ "EAuracronPCGErrorCode::InvalidSpatialData", (int64)EAuracronPCGErrorCode::InvalidSpatialData },
		{ "EAuracronPCGErrorCode::InvalidMetadata", (int64)EAuracronPCGErrorCode::InvalidMetadata },
		{ "EAuracronPCGErrorCode::GenerationFailed", (int64)EAuracronPCGErrorCode::GenerationFailed },
		{ "EAuracronPCGErrorCode::UnknownError", (int64)EAuracronPCGErrorCode::UnknownError },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGErrorCode",
	"EAuracronPCGErrorCode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGErrorCode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGErrorCode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGErrorCode.InnerSingleton;
}
// ********** End Enum EAuracronPCGErrorCode *******************************************************

// ********** Begin Enum EAuracronPCGExecutionMode *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGExecutionMode;
static UEnum* EAuracronPCGExecutionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGExecutionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGExecutionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGExecutionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGExecutionMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGExecutionMode>()
{
	return EAuracronPCGExecutionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Asynchronous.DisplayName", "Asynchronous" },
		{ "Asynchronous.Name", "EAuracronPCGExecutionMode::Asynchronous" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG execution modes\n" },
#endif
		{ "GPU.DisplayName", "GPU Accelerated" },
		{ "GPU.Name", "EAuracronPCGExecutionMode::GPU" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
		{ "Synchronous.DisplayName", "Synchronous" },
		{ "Synchronous.Name", "EAuracronPCGExecutionMode::Synchronous" },
		{ "Threaded.DisplayName", "Threaded" },
		{ "Threaded.Name", "EAuracronPCGExecutionMode::Threaded" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG execution modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGExecutionMode::Synchronous", (int64)EAuracronPCGExecutionMode::Synchronous },
		{ "EAuracronPCGExecutionMode::Asynchronous", (int64)EAuracronPCGExecutionMode::Asynchronous },
		{ "EAuracronPCGExecutionMode::Threaded", (int64)EAuracronPCGExecutionMode::Threaded },
		{ "EAuracronPCGExecutionMode::GPU", (int64)EAuracronPCGExecutionMode::GPU },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGExecutionMode",
	"EAuracronPCGExecutionMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGExecutionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGExecutionMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGExecutionMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGExecutionMode ***************************************************

// ********** Begin Enum EAuracronPCGQualityLevel **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGQualityLevel;
static UEnum* EAuracronPCGQualityLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGQualityLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGQualityLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGQualityLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGQualityLevel.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGQualityLevel>()
{
	return EAuracronPCGQualityLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG quality levels\n" },
#endif
		{ "Custom.DisplayName", "Custom Quality" },
		{ "Custom.Name", "EAuracronPCGQualityLevel::Custom" },
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EAuracronPCGQualityLevel::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EAuracronPCGQualityLevel::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EAuracronPCGQualityLevel::Medium" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG quality levels" },
#endif
		{ "Ultra.DisplayName", "Ultra Quality" },
		{ "Ultra.Name", "EAuracronPCGQualityLevel::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGQualityLevel::Low", (int64)EAuracronPCGQualityLevel::Low },
		{ "EAuracronPCGQualityLevel::Medium", (int64)EAuracronPCGQualityLevel::Medium },
		{ "EAuracronPCGQualityLevel::High", (int64)EAuracronPCGQualityLevel::High },
		{ "EAuracronPCGQualityLevel::Ultra", (int64)EAuracronPCGQualityLevel::Ultra },
		{ "EAuracronPCGQualityLevel::Custom", (int64)EAuracronPCGQualityLevel::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGQualityLevel",
	"EAuracronPCGQualityLevel",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGQualityLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGQualityLevel.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGQualityLevel.InnerSingleton;
}
// ********** End Enum EAuracronPCGQualityLevel ****************************************************

// ********** Begin Enum EAuracronPCGGenerationType ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGGenerationType;
static UEnum* EAuracronPCGGenerationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGGenerationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGGenerationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGGenerationType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGGenerationType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGGenerationType>()
{
	return EAuracronPCGGenerationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Biomes.DisplayName", "Biome Generation" },
		{ "Biomes.Name", "EAuracronPCGGenerationType::Biomes" },
		{ "BlueprintType", "true" },
		{ "Buildings.DisplayName", "Building Generation" },
		{ "Buildings.Name", "EAuracronPCGGenerationType::Buildings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG generation types\n" },
#endif
		{ "Custom.DisplayName", "Custom Generation" },
		{ "Custom.Name", "EAuracronPCGGenerationType::Custom" },
		{ "Foliage.DisplayName", "Foliage Generation" },
		{ "Foliage.Name", "EAuracronPCGGenerationType::Foliage" },
		{ "Landscapes.DisplayName", "Landscape Generation" },
		{ "Landscapes.Name", "EAuracronPCGGenerationType::Landscapes" },
		{ "Meshes.DisplayName", "Mesh Generation" },
		{ "Meshes.Name", "EAuracronPCGGenerationType::Meshes" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
		{ "Points.DisplayName", "Point Generation" },
		{ "Points.Name", "EAuracronPCGGenerationType::Points" },
		{ "Roads.DisplayName", "Road Generation" },
		{ "Roads.Name", "EAuracronPCGGenerationType::Roads" },
		{ "Terrain.DisplayName", "Terrain Generation" },
		{ "Terrain.Name", "EAuracronPCGGenerationType::Terrain" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG generation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGGenerationType::Points", (int64)EAuracronPCGGenerationType::Points },
		{ "EAuracronPCGGenerationType::Meshes", (int64)EAuracronPCGGenerationType::Meshes },
		{ "EAuracronPCGGenerationType::Landscapes", (int64)EAuracronPCGGenerationType::Landscapes },
		{ "EAuracronPCGGenerationType::Foliage", (int64)EAuracronPCGGenerationType::Foliage },
		{ "EAuracronPCGGenerationType::Buildings", (int64)EAuracronPCGGenerationType::Buildings },
		{ "EAuracronPCGGenerationType::Roads", (int64)EAuracronPCGGenerationType::Roads },
		{ "EAuracronPCGGenerationType::Terrain", (int64)EAuracronPCGGenerationType::Terrain },
		{ "EAuracronPCGGenerationType::Biomes", (int64)EAuracronPCGGenerationType::Biomes },
		{ "EAuracronPCGGenerationType::Custom", (int64)EAuracronPCGGenerationType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGGenerationType",
	"EAuracronPCGGenerationType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGGenerationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGGenerationType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGGenerationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGGenerationType.InnerSingleton;
}
// ********** End Enum EAuracronPCGGenerationType **************************************************

// ********** Begin ScriptStruct FAuracronPCGErrorInfo *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo;
class UScriptStruct* FAuracronPCGErrorInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGErrorInfo, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGErrorInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Error information structure\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Error information structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorCode_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorContext_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceElement_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineNumber_MetaData[] = {
		{ "Category", "Error" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ErrorCode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ErrorCode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorContext;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceElement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LineNumber;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGErrorInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorCode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorCode = { "ErrorCode", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErrorInfo, ErrorCode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErrorCode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorCode_MetaData), NewProp_ErrorCode_MetaData) }; // 4134387259
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErrorInfo, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorContext = { "ErrorContext", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErrorInfo, ErrorContext), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorContext_MetaData), NewProp_ErrorContext_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErrorInfo, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_SourceElement = { "SourceElement", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErrorInfo, SourceElement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceElement_MetaData), NewProp_SourceElement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_LineNumber = { "LineNumber", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErrorInfo, LineNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineNumber_MetaData), NewProp_LineNumber_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorCode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_ErrorContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_SourceElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewProp_LineNumber,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGErrorInfo",
	Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::PropPointers),
	sizeof(FAuracronPCGErrorInfo),
	alignof(FAuracronPCGErrorInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGErrorInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGErrorInfo ***********************************************

// ********** Begin ScriptStruct FAuracronPCGPerformanceMetrics ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics;
class UScriptStruct* FAuracronPCGPerformanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGPerformanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance metrics structure\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance metrics structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTimeSeconds_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsGenerated_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementsExecuted_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUUsagePercent_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUUsagePercent_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadsUsed_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionTimeSeconds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsGenerated;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ElementsExecuted;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUUsagePercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUUsagePercent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadsUsed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGPerformanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_ExecutionTimeSeconds = { "ExecutionTimeSeconds", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, ExecutionTimeSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTimeSeconds_MetaData), NewProp_ExecutionTimeSeconds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_PointsGenerated = { "PointsGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, PointsGenerated), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsGenerated_MetaData), NewProp_PointsGenerated_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_ElementsExecuted = { "ElementsExecuted", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, ElementsExecuted), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementsExecuted_MetaData), NewProp_ElementsExecuted_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_CPUUsagePercent = { "CPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, CPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUUsagePercent_MetaData), NewProp_CPUUsagePercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_GPUUsagePercent = { "GPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, GPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUUsagePercent_MetaData), NewProp_GPUUsagePercent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_ThreadsUsed = { "ThreadsUsed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, ThreadsUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadsUsed_MetaData), NewProp_ThreadsUsed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, StartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_EndTime = { "EndTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPerformanceMetrics, EndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndTime_MetaData), NewProp_EndTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_ExecutionTimeSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_PointsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_ElementsExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_CPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_GPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_ThreadsUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewProp_EndTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGPerformanceMetrics",
	Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::PropPointers),
	sizeof(FAuracronPCGPerformanceMetrics),
	alignof(FAuracronPCGPerformanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGPerformanceMetrics **************************************

// ********** Begin ScriptStruct FAuracronPCGConfiguration *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration;
class UScriptStruct* FAuracronPCGConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG configuration structure\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG configuration structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionMode_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityLevel_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadCount_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPointsPerBatch_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPoolSizeMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutSeconds_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceLogging_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUAcceleration_MetaData[] = {
		{ "Category", "GPU" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreferGPUProcessing_MetaData[] = {
		{ "Category", "GPU" },
		{ "ModuleRelativePath", "Public/AuracronPCGFramework.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExecutionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExecutionMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPointsPerBatch;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryPoolSizeMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bEnablePerformanceLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceLogging;
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static void NewProp_bEnableGPUAcceleration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUAcceleration;
	static void NewProp_bPreferGPUProcessing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreferGPUProcessing;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_ExecutionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_ExecutionMode = { "ExecutionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGConfiguration, ExecutionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGExecutionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionMode_MetaData), NewProp_ExecutionMode_MetaData) }; // 1595283585
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_QualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGConfiguration, QualityLevel), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGQualityLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityLevel_MetaData), NewProp_QualityLevel_MetaData) }; // 3175764907
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGConfiguration, ThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadCount_MetaData), NewProp_ThreadCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_MaxPointsPerBatch = { "MaxPointsPerBatch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGConfiguration, MaxPointsPerBatch), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPointsPerBatch_MetaData), NewProp_MaxPointsPerBatch_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_MemoryPoolSizeMB = { "MemoryPoolSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGConfiguration, MemoryPoolSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPoolSizeMB_MetaData), NewProp_MemoryPoolSizeMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGConfiguration, TimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutSeconds_MetaData), NewProp_TimeoutSeconds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FAuracronPCGConfiguration*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGConfiguration), &Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnablePerformanceLogging_SetBit(void* Obj)
{
	((FAuracronPCGConfiguration*)Obj)->bEnablePerformanceLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnablePerformanceLogging = { "bEnablePerformanceLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGConfiguration), &Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnablePerformanceLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceLogging_MetaData), NewProp_bEnablePerformanceLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((FAuracronPCGConfiguration*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGConfiguration), &Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableGPUAcceleration_SetBit(void* Obj)
{
	((FAuracronPCGConfiguration*)Obj)->bEnableGPUAcceleration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableGPUAcceleration = { "bEnableGPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGConfiguration), &Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableGPUAcceleration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUAcceleration_MetaData), NewProp_bEnableGPUAcceleration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bPreferGPUProcessing_SetBit(void* Obj)
{
	((FAuracronPCGConfiguration*)Obj)->bPreferGPUProcessing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bPreferGPUProcessing = { "bPreferGPUProcessing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGConfiguration), &Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bPreferGPUProcessing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreferGPUProcessing_MetaData), NewProp_bPreferGPUProcessing_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_ExecutionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_ExecutionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_QualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_QualityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_ThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_MaxPointsPerBatch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_MemoryPoolSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_TimeoutSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnablePerformanceLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableDetailedLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bEnableGPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewProp_bPreferGPUProcessing,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGConfiguration",
	Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::PropPointers),
	sizeof(FAuracronPCGConfiguration),
	alignof(FAuracronPCGConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGConfiguration *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGErrorCode_StaticEnum, TEXT("EAuracronPCGErrorCode"), &Z_Registration_Info_UEnum_EAuracronPCGErrorCode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4134387259U) },
		{ EAuracronPCGExecutionMode_StaticEnum, TEXT("EAuracronPCGExecutionMode"), &Z_Registration_Info_UEnum_EAuracronPCGExecutionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1595283585U) },
		{ EAuracronPCGQualityLevel_StaticEnum, TEXT("EAuracronPCGQualityLevel"), &Z_Registration_Info_UEnum_EAuracronPCGQualityLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3175764907U) },
		{ EAuracronPCGGenerationType_StaticEnum, TEXT("EAuracronPCGGenerationType"), &Z_Registration_Info_UEnum_EAuracronPCGGenerationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1055494586U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGErrorInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics::NewStructOps, TEXT("AuracronPCGErrorInfo"), &Z_Registration_Info_UScriptStruct_FAuracronPCGErrorInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGErrorInfo), 1923069771U) },
		{ FAuracronPCGPerformanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics::NewStructOps, TEXT("AuracronPCGPerformanceMetrics"), &Z_Registration_Info_UScriptStruct_FAuracronPCGPerformanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGPerformanceMetrics), 68781111U) },
		{ FAuracronPCGConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics::NewStructOps, TEXT("AuracronPCGConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronPCGConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGConfiguration), 1384230652U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h__Script_AuracronPCGBridge_792016723(TEXT("/Script/AuracronPCGBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
