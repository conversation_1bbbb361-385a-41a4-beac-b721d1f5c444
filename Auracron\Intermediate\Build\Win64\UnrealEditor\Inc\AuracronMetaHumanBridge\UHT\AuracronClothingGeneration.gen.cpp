// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronClothingGeneration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronClothingGeneration() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FClothingGenerationParameters();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FClothLODData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FClothLODLevel();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FClothMaterialData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FClothMeshData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FClothPhysicsData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FClothSimulationData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2f();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector3f();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EClothType ****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothType;
static UEnum* EClothType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothType"));
	}
	return Z_Registration_Info_UEnum_EClothType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothType>()
{
	return EClothType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cape.DisplayName", "Cape" },
		{ "Cape.Name", "EClothType::Cape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumeration for different types of cloth\n */" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EClothType::Custom" },
		{ "Dress.DisplayName", "Dress" },
		{ "Dress.Name", "EClothType::Dress" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EClothType::None" },
		{ "Pants.DisplayName", "Pants" },
		{ "Pants.Name", "EClothType::Pants" },
		{ "Shirt.DisplayName", "Shirt" },
		{ "Shirt.Name", "EClothType::Shirt" },
		{ "Skirt.DisplayName", "Skirt" },
		{ "Skirt.Name", "EClothType::Skirt" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumeration for different types of cloth" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothType::None", (int64)EClothType::None },
		{ "EClothType::Shirt", (int64)EClothType::Shirt },
		{ "EClothType::Pants", (int64)EClothType::Pants },
		{ "EClothType::Dress", (int64)EClothType::Dress },
		{ "EClothType::Skirt", (int64)EClothType::Skirt },
		{ "EClothType::Cape", (int64)EClothType::Cape },
		{ "EClothType::Custom", (int64)EClothType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothType",
	"EClothType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType()
{
	if (!Z_Registration_Info_UEnum_EClothType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothType.InnerSingleton;
}
// ********** End Enum EClothType ******************************************************************

// ********** Begin ScriptStruct FClothMeshData ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClothMeshData;
class UScriptStruct* FClothMeshData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClothMeshData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClothMeshData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClothMeshData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ClothMeshData"));
	}
	return Z_Registration_Info_UScriptStruct_FClothMeshData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClothMeshData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for cloth mesh data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for cloth mesh data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothType_MetaData[] = {
		{ "Category", "Cloth Mesh" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Resolution_MetaData[] = {
		{ "Category", "Cloth Mesh" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Size_MetaData[] = {
		{ "Category", "Cloth Mesh" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomVertices_MetaData[] = {
		{ "Category", "Cloth Mesh" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomNormals_MetaData[] = {
		{ "Category", "Cloth Mesh" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomUVs_MetaData[] = {
		{ "Category", "Cloth Mesh" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ClothType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ClothType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Resolution;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Size;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomVertices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CustomVertices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomNormals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CustomNormals;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomUVs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CustomUVs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClothMeshData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_ClothType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_ClothType = { "ClothType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMeshData, ClothType), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothType_MetaData), NewProp_ClothType_MetaData) }; // 1168589472
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_Resolution = { "Resolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMeshData, Resolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Resolution_MetaData), NewProp_Resolution_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_Size = { "Size", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMeshData, Size), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Size_MetaData), NewProp_Size_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomVertices_Inner = { "CustomVertices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector3f, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomVertices = { "CustomVertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMeshData, CustomVertices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomVertices_MetaData), NewProp_CustomVertices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomNormals_Inner = { "CustomNormals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector3f, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomNormals = { "CustomNormals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMeshData, CustomNormals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomNormals_MetaData), NewProp_CustomNormals_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomUVs_Inner = { "CustomUVs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2f, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomUVs = { "CustomUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMeshData, CustomUVs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomUVs_MetaData), NewProp_CustomUVs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClothMeshData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_ClothType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_ClothType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_Resolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_Size,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomVertices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomVertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomNormals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomNormals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomUVs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMeshData_Statics::NewProp_CustomUVs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothMeshData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClothMeshData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ClothMeshData",
	Z_Construct_UScriptStruct_FClothMeshData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothMeshData_Statics::PropPointers),
	sizeof(FClothMeshData),
	alignof(FClothMeshData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothMeshData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClothMeshData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClothMeshData()
{
	if (!Z_Registration_Info_UScriptStruct_FClothMeshData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClothMeshData.InnerSingleton, Z_Construct_UScriptStruct_FClothMeshData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClothMeshData.InnerSingleton;
}
// ********** End ScriptStruct FClothMeshData ******************************************************

// ********** Begin ScriptStruct FClothPhysicsData *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClothPhysicsData;
class UScriptStruct* FClothPhysicsData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClothPhysicsData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClothPhysicsData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClothPhysicsData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ClothPhysicsData"));
	}
	return Z_Registration_Info_UScriptStruct_FClothPhysicsData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClothPhysicsData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for cloth physics data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for cloth physics data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mass_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinParticleMass_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EdgeStiffness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StiffnessMultiplier_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingStiffness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendingStiffnessMultiplier_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Damping_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LocalDamping_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityScale_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGravityOverride_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityOverride_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindVelocity_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDragCoefficient_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindLiftCoefficient_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionThickness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelfCollisionThickness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrictionCoefficient_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseContinuousCollision_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSelfCollision_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelfCollisionStiffness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLongRangeAttachment_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TetherStiffness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TetherScale_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaStiffness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumeStiffness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinParticleMass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EdgeStiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StiffnessMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingStiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendingStiffnessMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LocalDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GravityScale;
	static void NewProp_bUseGravityOverride_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGravityOverride;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GravityOverride;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindVelocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindDragCoefficient;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindLiftCoefficient;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionThickness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SelfCollisionThickness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrictionCoefficient;
	static void NewProp_bUseContinuousCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseContinuousCollision;
	static void NewProp_bEnableSelfCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSelfCollision;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SelfCollisionStiffness;
	static void NewProp_bUseLongRangeAttachment_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLongRangeAttachment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TetherStiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TetherScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaStiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VolumeStiffness;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClothPhysicsData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_Mass = { "Mass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, Mass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mass_MetaData), NewProp_Mass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_MinParticleMass = { "MinParticleMass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, MinParticleMass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinParticleMass_MetaData), NewProp_MinParticleMass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_EdgeStiffness = { "EdgeStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, EdgeStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EdgeStiffness_MetaData), NewProp_EdgeStiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_StiffnessMultiplier = { "StiffnessMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, StiffnessMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StiffnessMultiplier_MetaData), NewProp_StiffnessMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_BendingStiffness = { "BendingStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, BendingStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingStiffness_MetaData), NewProp_BendingStiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_BendingStiffnessMultiplier = { "BendingStiffnessMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, BendingStiffnessMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendingStiffnessMultiplier_MetaData), NewProp_BendingStiffnessMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_Damping = { "Damping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, Damping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Damping_MetaData), NewProp_Damping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_LocalDamping = { "LocalDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, LocalDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LocalDamping_MetaData), NewProp_LocalDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_GravityScale = { "GravityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, GravityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityScale_MetaData), NewProp_GravityScale_MetaData) };
void Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseGravityOverride_SetBit(void* Obj)
{
	((FClothPhysicsData*)Obj)->bUseGravityOverride = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseGravityOverride = { "bUseGravityOverride", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothPhysicsData), &Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseGravityOverride_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGravityOverride_MetaData), NewProp_bUseGravityOverride_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_GravityOverride = { "GravityOverride", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, GravityOverride), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityOverride_MetaData), NewProp_GravityOverride_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_WindVelocity = { "WindVelocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, WindVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindVelocity_MetaData), NewProp_WindVelocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_WindDragCoefficient = { "WindDragCoefficient", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, WindDragCoefficient), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDragCoefficient_MetaData), NewProp_WindDragCoefficient_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_WindLiftCoefficient = { "WindLiftCoefficient", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, WindLiftCoefficient), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindLiftCoefficient_MetaData), NewProp_WindLiftCoefficient_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_CollisionThickness = { "CollisionThickness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, CollisionThickness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionThickness_MetaData), NewProp_CollisionThickness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_SelfCollisionThickness = { "SelfCollisionThickness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, SelfCollisionThickness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelfCollisionThickness_MetaData), NewProp_SelfCollisionThickness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_FrictionCoefficient = { "FrictionCoefficient", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, FrictionCoefficient), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrictionCoefficient_MetaData), NewProp_FrictionCoefficient_MetaData) };
void Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseContinuousCollision_SetBit(void* Obj)
{
	((FClothPhysicsData*)Obj)->bUseContinuousCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseContinuousCollision = { "bUseContinuousCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothPhysicsData), &Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseContinuousCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseContinuousCollision_MetaData), NewProp_bUseContinuousCollision_MetaData) };
void Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bEnableSelfCollision_SetBit(void* Obj)
{
	((FClothPhysicsData*)Obj)->bEnableSelfCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bEnableSelfCollision = { "bEnableSelfCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothPhysicsData), &Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bEnableSelfCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSelfCollision_MetaData), NewProp_bEnableSelfCollision_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_SelfCollisionStiffness = { "SelfCollisionStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, SelfCollisionStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelfCollisionStiffness_MetaData), NewProp_SelfCollisionStiffness_MetaData) };
void Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseLongRangeAttachment_SetBit(void* Obj)
{
	((FClothPhysicsData*)Obj)->bUseLongRangeAttachment = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseLongRangeAttachment = { "bUseLongRangeAttachment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothPhysicsData), &Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseLongRangeAttachment_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLongRangeAttachment_MetaData), NewProp_bUseLongRangeAttachment_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_TetherStiffness = { "TetherStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, TetherStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TetherStiffness_MetaData), NewProp_TetherStiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_TetherScale = { "TetherScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, TetherScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TetherScale_MetaData), NewProp_TetherScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_AreaStiffness = { "AreaStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, AreaStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaStiffness_MetaData), NewProp_AreaStiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_VolumeStiffness = { "VolumeStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothPhysicsData, VolumeStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumeStiffness_MetaData), NewProp_VolumeStiffness_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClothPhysicsData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_Mass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_MinParticleMass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_EdgeStiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_StiffnessMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_BendingStiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_BendingStiffnessMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_Damping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_LocalDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_GravityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseGravityOverride,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_GravityOverride,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_WindVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_WindDragCoefficient,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_WindLiftCoefficient,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_CollisionThickness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_SelfCollisionThickness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_FrictionCoefficient,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseContinuousCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bEnableSelfCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_SelfCollisionStiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_bUseLongRangeAttachment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_TetherStiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_TetherScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_AreaStiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewProp_VolumeStiffness,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothPhysicsData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClothPhysicsData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ClothPhysicsData",
	Z_Construct_UScriptStruct_FClothPhysicsData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothPhysicsData_Statics::PropPointers),
	sizeof(FClothPhysicsData),
	alignof(FClothPhysicsData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothPhysicsData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClothPhysicsData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClothPhysicsData()
{
	if (!Z_Registration_Info_UScriptStruct_FClothPhysicsData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClothPhysicsData.InnerSingleton, Z_Construct_UScriptStruct_FClothPhysicsData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClothPhysicsData.InnerSingleton;
}
// ********** End ScriptStruct FClothPhysicsData ***************************************************

// ********** Begin ScriptStruct FClothSimulationData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClothSimulationData;
class UScriptStruct* FClothSimulationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClothSimulationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClothSimulationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClothSimulationData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ClothSimulationData"));
	}
	return Z_Registration_Info_UScriptStruct_FClothSimulationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClothSimulationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for cloth simulation data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for cloth simulation data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IterationCount_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxIterationCount_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubdivisionCount_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePointBasedWind_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFastTether_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolverFrequency_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseXPBDConstraints_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBackstop_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackstopRadius_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackstopDistance_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_IterationCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxIterationCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SubdivisionCount;
	static void NewProp_bUsePointBasedWind_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePointBasedWind;
	static void NewProp_bUseFastTether_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFastTether;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SolverFrequency;
	static void NewProp_bUseXPBDConstraints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseXPBDConstraints;
	static void NewProp_bUseBackstop_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBackstop;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BackstopRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BackstopDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClothSimulationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_IterationCount = { "IterationCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothSimulationData, IterationCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IterationCount_MetaData), NewProp_IterationCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_MaxIterationCount = { "MaxIterationCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothSimulationData, MaxIterationCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxIterationCount_MetaData), NewProp_MaxIterationCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_SubdivisionCount = { "SubdivisionCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothSimulationData, SubdivisionCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubdivisionCount_MetaData), NewProp_SubdivisionCount_MetaData) };
void Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUsePointBasedWind_SetBit(void* Obj)
{
	((FClothSimulationData*)Obj)->bUsePointBasedWind = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUsePointBasedWind = { "bUsePointBasedWind", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothSimulationData), &Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUsePointBasedWind_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePointBasedWind_MetaData), NewProp_bUsePointBasedWind_MetaData) };
void Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseFastTether_SetBit(void* Obj)
{
	((FClothSimulationData*)Obj)->bUseFastTether = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseFastTether = { "bUseFastTether", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothSimulationData), &Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseFastTether_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFastTether_MetaData), NewProp_bUseFastTether_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_SolverFrequency = { "SolverFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothSimulationData, SolverFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolverFrequency_MetaData), NewProp_SolverFrequency_MetaData) };
void Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseXPBDConstraints_SetBit(void* Obj)
{
	((FClothSimulationData*)Obj)->bUseXPBDConstraints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseXPBDConstraints = { "bUseXPBDConstraints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothSimulationData), &Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseXPBDConstraints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseXPBDConstraints_MetaData), NewProp_bUseXPBDConstraints_MetaData) };
void Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseBackstop_SetBit(void* Obj)
{
	((FClothSimulationData*)Obj)->bUseBackstop = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseBackstop = { "bUseBackstop", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothSimulationData), &Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseBackstop_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBackstop_MetaData), NewProp_bUseBackstop_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_BackstopRadius = { "BackstopRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothSimulationData, BackstopRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackstopRadius_MetaData), NewProp_BackstopRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_BackstopDistance = { "BackstopDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothSimulationData, BackstopDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackstopDistance_MetaData), NewProp_BackstopDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClothSimulationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_IterationCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_MaxIterationCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_SubdivisionCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUsePointBasedWind,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseFastTether,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_SolverFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseXPBDConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_bUseBackstop,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_BackstopRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewProp_BackstopDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothSimulationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClothSimulationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ClothSimulationData",
	Z_Construct_UScriptStruct_FClothSimulationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothSimulationData_Statics::PropPointers),
	sizeof(FClothSimulationData),
	alignof(FClothSimulationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothSimulationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClothSimulationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClothSimulationData()
{
	if (!Z_Registration_Info_UScriptStruct_FClothSimulationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClothSimulationData.InnerSingleton, Z_Construct_UScriptStruct_FClothSimulationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClothSimulationData.InnerSingleton;
}
// ********** End ScriptStruct FClothSimulationData ************************************************

// ********** Begin ScriptStruct FClothMaterialData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClothMaterialData;
class UScriptStruct* FClothMaterialData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClothMaterialData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClothMaterialData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClothMaterialData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ClothMaterialData"));
	}
	return Z_Registration_Info_UScriptStruct_FClothMaterialData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClothMaterialData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for cloth material data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for cloth material data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseMaterial_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiffuseTexture_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NormalTexture_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoughnessTexture_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseColor_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metallic_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Roughness_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Specular_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Opacity_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothStiffness_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothDamping_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindResponse_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaseMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DiffuseTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NormalTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RoughnessTexture;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metallic;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Roughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Specular;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Opacity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClothStiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClothDamping;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindResponse;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClothMaterialData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_BaseMaterial = { "BaseMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, BaseMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseMaterial_MetaData), NewProp_BaseMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_DiffuseTexture = { "DiffuseTexture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, DiffuseTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiffuseTexture_MetaData), NewProp_DiffuseTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_NormalTexture = { "NormalTexture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, NormalTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NormalTexture_MetaData), NewProp_NormalTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_RoughnessTexture = { "RoughnessTexture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, RoughnessTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoughnessTexture_MetaData), NewProp_RoughnessTexture_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_BaseColor = { "BaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, BaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseColor_MetaData), NewProp_BaseColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Metallic = { "Metallic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, Metallic), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metallic_MetaData), NewProp_Metallic_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Roughness = { "Roughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, Roughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Roughness_MetaData), NewProp_Roughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Specular = { "Specular", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, Specular), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Specular_MetaData), NewProp_Specular_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Opacity = { "Opacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, Opacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Opacity_MetaData), NewProp_Opacity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_ClothStiffness = { "ClothStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, ClothStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothStiffness_MetaData), NewProp_ClothStiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_ClothDamping = { "ClothDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, ClothDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothDamping_MetaData), NewProp_ClothDamping_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_WindResponse = { "WindResponse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothMaterialData, WindResponse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindResponse_MetaData), NewProp_WindResponse_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClothMaterialData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_BaseMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_DiffuseTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_NormalTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_RoughnessTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_BaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Metallic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Roughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Specular,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_Opacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_ClothStiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_ClothDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewProp_WindResponse,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothMaterialData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClothMaterialData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ClothMaterialData",
	Z_Construct_UScriptStruct_FClothMaterialData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothMaterialData_Statics::PropPointers),
	sizeof(FClothMaterialData),
	alignof(FClothMaterialData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothMaterialData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClothMaterialData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClothMaterialData()
{
	if (!Z_Registration_Info_UScriptStruct_FClothMaterialData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClothMaterialData.InnerSingleton, Z_Construct_UScriptStruct_FClothMaterialData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClothMaterialData.InnerSingleton;
}
// ********** End ScriptStruct FClothMaterialData **************************************************

// ********** Begin ScriptStruct FClothLODLevel ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClothLODLevel;
class UScriptStruct* FClothLODLevel::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClothLODLevel.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClothLODLevel.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClothLODLevel, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ClothLODLevel"));
	}
	return Z_Registration_Info_UScriptStruct_FClothLODLevel.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClothLODLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for cloth LOD level data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for cloth LOD level data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODIndex_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReductionPercentage_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxVertices_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenSize_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReductionPercentage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxVertices;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScreenSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClothLODLevel>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_LODIndex = { "LODIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothLODLevel, LODIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODIndex_MetaData), NewProp_LODIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_ReductionPercentage = { "ReductionPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothLODLevel, ReductionPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReductionPercentage_MetaData), NewProp_ReductionPercentage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_MaxVertices = { "MaxVertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothLODLevel, MaxVertices), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxVertices_MetaData), NewProp_MaxVertices_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_ScreenSize = { "ScreenSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothLODLevel, ScreenSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenSize_MetaData), NewProp_ScreenSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClothLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_LODIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_ReductionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_MaxVertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewProp_ScreenSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClothLODLevel_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ClothLODLevel",
	Z_Construct_UScriptStruct_FClothLODLevel_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothLODLevel_Statics::PropPointers),
	sizeof(FClothLODLevel),
	alignof(FClothLODLevel),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothLODLevel_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClothLODLevel_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClothLODLevel()
{
	if (!Z_Registration_Info_UScriptStruct_FClothLODLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClothLODLevel.InnerSingleton, Z_Construct_UScriptStruct_FClothLODLevel_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClothLODLevel.InnerSingleton;
}
// ********** End ScriptStruct FClothLODLevel ******************************************************

// ********** Begin ScriptStruct FClothLODData *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClothLODData;
class UScriptStruct* FClothLODData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClothLODData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClothLODData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClothLODData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ClothLODData"));
	}
	return Z_Registration_Info_UScriptStruct_FClothLODData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClothLODData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for cloth LOD data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for cloth LOD data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevels_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateLODs_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevels_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODLevels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODLevels;
	static void NewProp_bAutoGenerateLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevels;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClothLODData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_LODLevels_Inner = { "LODLevels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FClothLODLevel, METADATA_PARAMS(0, nullptr) }; // 1422511935
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_LODLevels = { "LODLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothLODData, LODLevels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevels_MetaData), NewProp_LODLevels_MetaData) }; // 1422511935
void Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_bAutoGenerateLODs_SetBit(void* Obj)
{
	((FClothLODData*)Obj)->bAutoGenerateLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_bAutoGenerateLODs = { "bAutoGenerateLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothLODData), &Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_bAutoGenerateLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateLODs_MetaData), NewProp_bAutoGenerateLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_MaxLODLevels = { "MaxLODLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothLODData, MaxLODLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevels_MetaData), NewProp_MaxLODLevels_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClothLODData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_LODLevels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_LODLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_bAutoGenerateLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothLODData_Statics::NewProp_MaxLODLevels,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothLODData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClothLODData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ClothLODData",
	Z_Construct_UScriptStruct_FClothLODData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothLODData_Statics::PropPointers),
	sizeof(FClothLODData),
	alignof(FClothLODData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothLODData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClothLODData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClothLODData()
{
	if (!Z_Registration_Info_UScriptStruct_FClothLODData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClothLODData.InnerSingleton, Z_Construct_UScriptStruct_FClothLODData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClothLODData.InnerSingleton;
}
// ********** End ScriptStruct FClothLODData *******************************************************

// ********** Begin ScriptStruct FClothingGenerationParameters *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClothingGenerationParameters;
class UScriptStruct* FClothingGenerationParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClothingGenerationParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClothingGenerationParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClothingGenerationParameters, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ClothingGenerationParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FClothingGenerationParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for cloth generation parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for cloth generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingName_MetaData[] = {
		{ "Category", "General" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshData_MetaData[] = {
		{ "Category", "Mesh" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePhysics_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsData_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSimulation_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationData_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialData_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateLODs_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODData_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronClothingGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClothingName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshData;
	static void NewProp_bEnablePhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePhysics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsData;
	static void NewProp_bEnableSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSimulation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SimulationData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaterialData;
	static void NewProp_bGenerateLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateLODs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClothingGenerationParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_ClothingName = { "ClothingName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothingGenerationParameters, ClothingName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingName_MetaData), NewProp_ClothingName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_MeshData = { "MeshData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothingGenerationParameters, MeshData), Z_Construct_UScriptStruct_FClothMeshData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshData_MetaData), NewProp_MeshData_MetaData) }; // 1194617811
void Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnablePhysics_SetBit(void* Obj)
{
	((FClothingGenerationParameters*)Obj)->bEnablePhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnablePhysics = { "bEnablePhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothingGenerationParameters), &Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnablePhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePhysics_MetaData), NewProp_bEnablePhysics_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_PhysicsData = { "PhysicsData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothingGenerationParameters, PhysicsData), Z_Construct_UScriptStruct_FClothPhysicsData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsData_MetaData), NewProp_PhysicsData_MetaData) }; // 3370143869
void Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnableSimulation_SetBit(void* Obj)
{
	((FClothingGenerationParameters*)Obj)->bEnableSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnableSimulation = { "bEnableSimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothingGenerationParameters), &Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnableSimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSimulation_MetaData), NewProp_bEnableSimulation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_SimulationData = { "SimulationData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothingGenerationParameters, SimulationData), Z_Construct_UScriptStruct_FClothSimulationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationData_MetaData), NewProp_SimulationData_MetaData) }; // 2294028943
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_MaterialData = { "MaterialData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothingGenerationParameters, MaterialData), Z_Construct_UScriptStruct_FClothMaterialData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialData_MetaData), NewProp_MaterialData_MetaData) }; // 3436378440
void Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bGenerateLODs_SetBit(void* Obj)
{
	((FClothingGenerationParameters*)Obj)->bGenerateLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bGenerateLODs = { "bGenerateLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClothingGenerationParameters), &Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bGenerateLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateLODs_MetaData), NewProp_bGenerateLODs_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_LODData = { "LODData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClothingGenerationParameters, LODData), Z_Construct_UScriptStruct_FClothLODData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODData_MetaData), NewProp_LODData_MetaData) }; // 3069416003
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_ClothingName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_MeshData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnablePhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_PhysicsData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bEnableSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_SimulationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_MaterialData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_bGenerateLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewProp_LODData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ClothingGenerationParameters",
	Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::PropPointers),
	sizeof(FClothingGenerationParameters),
	alignof(FClothingGenerationParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClothingGenerationParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FClothingGenerationParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClothingGenerationParameters.InnerSingleton, Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClothingGenerationParameters.InnerSingleton;
}
// ********** End ScriptStruct FClothingGenerationParameters ***************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EClothType_StaticEnum, TEXT("EClothType"), &Z_Registration_Info_UEnum_EClothType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1168589472U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FClothMeshData::StaticStruct, Z_Construct_UScriptStruct_FClothMeshData_Statics::NewStructOps, TEXT("ClothMeshData"), &Z_Registration_Info_UScriptStruct_FClothMeshData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClothMeshData), 1194617811U) },
		{ FClothPhysicsData::StaticStruct, Z_Construct_UScriptStruct_FClothPhysicsData_Statics::NewStructOps, TEXT("ClothPhysicsData"), &Z_Registration_Info_UScriptStruct_FClothPhysicsData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClothPhysicsData), 3370143869U) },
		{ FClothSimulationData::StaticStruct, Z_Construct_UScriptStruct_FClothSimulationData_Statics::NewStructOps, TEXT("ClothSimulationData"), &Z_Registration_Info_UScriptStruct_FClothSimulationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClothSimulationData), 2294028943U) },
		{ FClothMaterialData::StaticStruct, Z_Construct_UScriptStruct_FClothMaterialData_Statics::NewStructOps, TEXT("ClothMaterialData"), &Z_Registration_Info_UScriptStruct_FClothMaterialData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClothMaterialData), 3436378440U) },
		{ FClothLODLevel::StaticStruct, Z_Construct_UScriptStruct_FClothLODLevel_Statics::NewStructOps, TEXT("ClothLODLevel"), &Z_Registration_Info_UScriptStruct_FClothLODLevel, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClothLODLevel), 1422511935U) },
		{ FClothLODData::StaticStruct, Z_Construct_UScriptStruct_FClothLODData_Statics::NewStructOps, TEXT("ClothLODData"), &Z_Registration_Info_UScriptStruct_FClothLODData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClothLODData), 3069416003U) },
		{ FClothingGenerationParameters::StaticStruct, Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics::NewStructOps, TEXT("ClothingGenerationParameters"), &Z_Registration_Info_UScriptStruct_FClothingGenerationParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClothingGenerationParameters), 3682747384U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h__Script_AuracronMetaHumanBridge_1849984334(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
