// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAdaptiveCreaturesBridge.h"

#ifdef AURACRONADAPTIVECREATURESBRIDGE_AuracronAdaptiveCreaturesBridge_generated_h
#error "AuracronAdaptiveCreaturesBridge.generated.h already included, missing '#pragma once' in AuracronAdaptiveCreaturesBridge.h"
#endif
#define AURACRONADAPTIVECREATURESBRIDGE_AuracronAdaptiveCreaturesBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAuracronAdaptiveRealmType : uint8;
struct FCreatureSpawnData;

// ********** Begin ScriptStruct FCreatureProperties ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_86_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FCreatureProperties_Statics; \
	static class UScriptStruct* StaticStruct();


struct FCreatureProperties;
// ********** End ScriptStruct FCreatureProperties *************************************************

// ********** Begin ScriptStruct FAdaptationData ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_122_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAdaptationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAdaptationData;
// ********** End ScriptStruct FAdaptationData *****************************************************

// ********** Begin ScriptStruct FCreatureSpawnData ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_146_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FCreatureSpawnData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FCreatureSpawnData;
// ********** End ScriptStruct FCreatureSpawnData **************************************************

// ********** Begin ScriptStruct FMassCreatureFragment *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_185_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMassCreatureFragment_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FMassFragment Super;


struct FMassCreatureFragment;
// ********** End ScriptStruct FMassCreatureFragment ***********************************************

// ********** Begin ScriptStruct FMassCreatureTag **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_203_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMassCreatureTag_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FMassTag Super;


struct FMassCreatureTag;
// ********** End ScriptStruct FMassCreatureTag ****************************************************

// ********** Begin ScriptStruct FAdaptationDataArrayWrapper ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_212_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAdaptationDataArrayWrapper;
// ********** End ScriptStruct FAdaptationDataArrayWrapper *****************************************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_234_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetAllAdaptations); \
	DECLARE_FUNCTION(execValidateSystemIntegrity); \
	DECLARE_FUNCTION(execGetSystemStatistics); \
	DECLARE_FUNCTION(execGetCreatureDataForPython); \
	DECLARE_FUNCTION(execExecutePythonScript); \
	DECLARE_FUNCTION(execInitializePythonBindings); \
	DECLARE_FUNCTION(execApplyRealmEnvironmentalEffects); \
	DECLARE_FUNCTION(execConfigureRealmSpecificBehaviors); \
	DECLARE_FUNCTION(execUpdate3DNavigation); \
	DECLARE_FUNCTION(execProcessPackBehaviors); \
	DECLARE_FUNCTION(execUpdateAllAdaptations); \
	DECLARE_FUNCTION(execProcessPlayerBehaviorAdaptations); \
	DECLARE_FUNCTION(execRemoveAllCreatures); \
	DECLARE_FUNCTION(execSpawnCreatures);


AURACRONADAPTIVECREATURESBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_234_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronAdaptiveCreaturesBridge(); \
	friend struct Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONADAPTIVECREATURESBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronAdaptiveCreaturesBridge, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronAdaptiveCreaturesBridge"), Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronAdaptiveCreaturesBridge)


#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_234_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronAdaptiveCreaturesBridge(UAuracronAdaptiveCreaturesBridge&&) = delete; \
	UAuracronAdaptiveCreaturesBridge(const UAuracronAdaptiveCreaturesBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronAdaptiveCreaturesBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronAdaptiveCreaturesBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronAdaptiveCreaturesBridge) \
	NO_API virtual ~UAuracronAdaptiveCreaturesBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_231_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_234_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_234_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_234_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h_234_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronAdaptiveCreaturesBridge;

// ********** End Class UAuracronAdaptiveCreaturesBridge *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h

// ********** Begin Enum EAuracronAdaptiveRealmType ************************************************
#define FOREACH_ENUM_EAURACRONADAPTIVEREALMTYPE(op) \
	op(EAuracronAdaptiveRealmType::PlanicieRadiante) \
	op(EAuracronAdaptiveRealmType::FirmamentoZephyr) \
	op(EAuracronAdaptiveRealmType::AbismoUmbrio) 

enum class EAuracronAdaptiveRealmType : uint8;
template<> struct TIsUEnumClass<EAuracronAdaptiveRealmType> { enum { Value = true }; };
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<EAuracronAdaptiveRealmType>();
// ********** End Enum EAuracronAdaptiveRealmType **************************************************

// ********** Begin Enum ECreatureType *************************************************************
#define FOREACH_ENUM_ECREATURETYPE(op) \
	op(ECreatureType::GuardiaoTerrestre) \
	op(ECreatureType::CacadorAgil) \
	op(ECreatureType::ColetorMistico) \
	op(ECreatureType::SentinelaVoadora) \
	op(ECreatureType::TempestadeViva) \
	op(ECreatureType::NavegadorEtereo) \
	op(ECreatureType::SombraAdaptativa) \
	op(ECreatureType::Cristalizador) \
	op(ECreatureType::EcoSombrio) 

enum class ECreatureType : uint8;
template<> struct TIsUEnumClass<ECreatureType> { enum { Value = true }; };
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<ECreatureType>();
// ********** End Enum ECreatureType ***************************************************************

// ********** Begin Enum EBehaviorState ************************************************************
#define FOREACH_ENUM_EBEHAVIORSTATE(op) \
	op(EBehaviorState::Passive) \
	op(EBehaviorState::Alert) \
	op(EBehaviorState::Aggressive) \
	op(EBehaviorState::Defensive) \
	op(EBehaviorState::Hunting) \
	op(EBehaviorState::Fleeing) \
	op(EBehaviorState::Territorial) \
	op(EBehaviorState::Cooperative) 

enum class EBehaviorState : uint8;
template<> struct TIsUEnumClass<EBehaviorState> { enum { Value = true }; };
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<EBehaviorState>();
// ********** End Enum EBehaviorState **************************************************************

// ********** Begin Enum EAdaptationType ***********************************************************
#define FOREACH_ENUM_EADAPTATIONTYPE(op) \
	op(EAdaptationType::AggressionScaling) \
	op(EAdaptationType::TerritoryShifting) \
	op(EAdaptationType::PackCoordination) \
	op(EAdaptationType::ResourceCompetition) \
	op(EAdaptationType::StealthAdaptation) \
	op(EAdaptationType::PatrolOptimization) 

enum class EAdaptationType : uint8;
template<> struct TIsUEnumClass<EAdaptationType> { enum { Value = true }; };
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<EAdaptationType>();
// ********** End Enum EAdaptationType *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
