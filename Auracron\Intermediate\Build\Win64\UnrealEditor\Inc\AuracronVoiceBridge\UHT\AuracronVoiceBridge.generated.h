// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronVoiceBridge.h"

#ifdef AURACRONVOICEBRIDGE_AuracronVoiceBridge_generated_h
#error "AuracronVoiceBridge.generated.h already included, missing '#pragma once' in AuracronVoiceBridge.h"
#endif
#define AURACRONVOICEBRIDGE_AuracronVoiceBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FAuracronVoiceChannel;
struct FAuracronVoiceParticipant;

// ********** Begin ScriptStruct FAuracronVoiceConfiguration ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_81_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronVoiceConfiguration;
// ********** End ScriptStruct FAuracronVoiceConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronVoiceChannel *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_170_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronVoiceChannel;
// ********** End ScriptStruct FAuracronVoiceChannel ***********************************************

// ********** Begin ScriptStruct FAuracronVoiceParticipant *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_231_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronVoiceParticipant;
// ********** End ScriptStruct FAuracronVoiceParticipant *******************************************

// ********** Begin Delegate FOnParticipantJoined **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_542_DELEGATE \
static void FOnParticipantJoined_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantJoined, const FString& ChannelID, FAuracronVoiceParticipant Participant);


// ********** End Delegate FOnParticipantJoined ****************************************************

// ********** Begin Delegate FOnParticipantLeft ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_547_DELEGATE \
static void FOnParticipantLeft_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantLeft, const FString& ChannelID, const FString& PlayerID);


// ********** End Delegate FOnParticipantLeft ******************************************************

// ********** Begin Delegate FOnParticipantStartedSpeaking *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_552_DELEGATE \
static void FOnParticipantStartedSpeaking_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantStartedSpeaking, const FString& PlayerID);


// ********** End Delegate FOnParticipantStartedSpeaking *******************************************

// ********** Begin Delegate FOnParticipantStoppedSpeaking *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_557_DELEGATE \
static void FOnParticipantStoppedSpeaking_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantStoppedSpeaking, const FString& PlayerID);


// ********** End Delegate FOnParticipantStoppedSpeaking *******************************************

// ********** Begin Class UAuracronVoiceBridge *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_301_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_VoiceState); \
	DECLARE_FUNCTION(execPlayVoiceRecording); \
	DECLARE_FUNCTION(execStopVoiceRecording); \
	DECLARE_FUNCTION(execStartVoiceRecording); \
	DECLARE_FUNCTION(execApplyVoiceModulation); \
	DECLARE_FUNCTION(execRemoveVoiceFilter); \
	DECLARE_FUNCTION(execApplyVoiceFilter); \
	DECLARE_FUNCTION(execSetProximityDistance); \
	DECLARE_FUNCTION(execUpdatePlayer3DPosition); \
	DECLARE_FUNCTION(execEnable3DVoice); \
	DECLARE_FUNCTION(execIsParticipantSpeaking); \
	DECLARE_FUNCTION(execGetChannelParticipants); \
	DECLARE_FUNCTION(execSetParticipantVolume); \
	DECLARE_FUNCTION(execMuteParticipant); \
	DECLARE_FUNCTION(execSetChannelVolume); \
	DECLARE_FUNCTION(execMuteVoiceChannel); \
	DECLARE_FUNCTION(execLeaveVoiceChannel); \
	DECLARE_FUNCTION(execJoinVoiceChannel); \
	DECLARE_FUNCTION(execCreateVoiceChannel); \
	DECLARE_FUNCTION(execDeafenAudio); \
	DECLARE_FUNCTION(execMuteMicrophone); \
	DECLARE_FUNCTION(execDisconnectFromVoiceChat); \
	DECLARE_FUNCTION(execConnectToVoiceChat); \
	DECLARE_FUNCTION(execInitializeVoiceChat);


AURACRONVOICEBRIDGE_API UClass* Z_Construct_UClass_UAuracronVoiceBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_301_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronVoiceBridge(); \
	friend struct Z_Construct_UClass_UAuracronVoiceBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONVOICEBRIDGE_API UClass* Z_Construct_UClass_UAuracronVoiceBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronVoiceBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronVoiceBridge"), Z_Construct_UClass_UAuracronVoiceBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronVoiceBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		VoiceConfiguration=NETFIELD_REP_START, \
		ActiveVoiceChannels, \
		ConnectedParticipants, \
		CurrentVoiceState, \
		NETFIELD_REP_END=CurrentVoiceState	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_301_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronVoiceBridge(UAuracronVoiceBridge&&) = delete; \
	UAuracronVoiceBridge(const UAuracronVoiceBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronVoiceBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronVoiceBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronVoiceBridge) \
	NO_API virtual ~UAuracronVoiceBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_298_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_301_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_301_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_301_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h_301_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronVoiceBridge;

// ********** End Class UAuracronVoiceBridge *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h

// ********** Begin Enum EAuracronVoiceChannelType *************************************************
#define FOREACH_ENUM_EAURACRONVOICECHANNELTYPE(op) \
	op(EAuracronVoiceChannelType::None) \
	op(EAuracronVoiceChannelType::Team) \
	op(EAuracronVoiceChannelType::Proximity) \
	op(EAuracronVoiceChannelType::Global) \
	op(EAuracronVoiceChannelType::Party) \
	op(EAuracronVoiceChannelType::Whisper) \
	op(EAuracronVoiceChannelType::Broadcast) \
	op(EAuracronVoiceChannelType::Emergency) \
	op(EAuracronVoiceChannelType::Coach) 

enum class EAuracronVoiceChannelType : uint8;
template<> struct TIsUEnumClass<EAuracronVoiceChannelType> { enum { Value = true }; };
template<> AURACRONVOICEBRIDGE_API UEnum* StaticEnum<EAuracronVoiceChannelType>();
// ********** End Enum EAuracronVoiceChannelType ***************************************************

// ********** Begin Enum EAuracronVoiceQuality *****************************************************
#define FOREACH_ENUM_EAURACRONVOICEQUALITY(op) \
	op(EAuracronVoiceQuality::Low) \
	op(EAuracronVoiceQuality::Medium) \
	op(EAuracronVoiceQuality::High) \
	op(EAuracronVoiceQuality::Ultra) 

enum class EAuracronVoiceQuality : uint8;
template<> struct TIsUEnumClass<EAuracronVoiceQuality> { enum { Value = true }; };
template<> AURACRONVOICEBRIDGE_API UEnum* StaticEnum<EAuracronVoiceQuality>();
// ********** End Enum EAuracronVoiceQuality *******************************************************

// ********** Begin Enum EAuracronVoiceState *******************************************************
#define FOREACH_ENUM_EAURACRONVOICESTATE(op) \
	op(EAuracronVoiceState::Disconnected) \
	op(EAuracronVoiceState::Connecting) \
	op(EAuracronVoiceState::Connected) \
	op(EAuracronVoiceState::Speaking) \
	op(EAuracronVoiceState::Listening) \
	op(EAuracronVoiceState::Muted) \
	op(EAuracronVoiceState::Deafened) \
	op(EAuracronVoiceState::Error) 

enum class EAuracronVoiceState : uint8;
template<> struct TIsUEnumClass<EAuracronVoiceState> { enum { Value = true }; };
template<> AURACRONVOICEBRIDGE_API UEnum* StaticEnum<EAuracronVoiceState>();
// ********** End Enum EAuracronVoiceState *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
