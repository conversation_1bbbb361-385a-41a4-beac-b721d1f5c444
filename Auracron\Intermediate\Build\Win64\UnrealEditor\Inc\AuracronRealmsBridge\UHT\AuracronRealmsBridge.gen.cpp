// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronRealmsBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronRealmsBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONREALMSBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmsBridge();
AURACRONREALMSBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmsBridge_NoRegister();
AURACRONREALMSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState();
AURACRONREALMSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature();
AURACRONREALMSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature();
AURACRONREALMSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDimensionalPortal();
AURACRONREALMSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMapEvolution();
AURACRONREALMSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig();
AURACRONREALMSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry();
AURACRONREALMSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmConfiguration();
AURACRONREALMSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry();
AURACRONREALMSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronVerticalConnector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UDataLayerInstance_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDataLayerSubsystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTimelineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldPartitionSubsystem_NoRegister();
FOLIAGE_API UClass* Z_Construct_UClass_AInstancedFoliageActor_NoRegister();
LANDSCAPE_API UClass* Z_Construct_UClass_ALandscape_NoRegister();
METASOUNDENGINE_API UClass* Z_Construct_UClass_UMetaSoundSource_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSubsystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronRealmsBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronRealmType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronRealmType;
static UEnum* EAuracronRealmType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronRealmType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronRealmType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("EAuracronRealmType"));
	}
	return Z_Registration_Info_UEnum_EAuracronRealmType.OuterSingleton;
}
template<> AURACRONREALMSBRIDGE_API UEnum* StaticEnum<EAuracronRealmType>()
{
	return EAuracronRealmType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AbismoUmbrio.DisplayName", "Abismo Umbrio (Underground)" },
		{ "AbismoUmbrio.Name", "EAuracronRealmType::AbismoUmbrio" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de Realms do AURACRON\n */" },
#endif
		{ "FirmamentoZephyr.DisplayName", "Firmamento Zephyr (Sky)" },
		{ "FirmamentoZephyr.Name", "EAuracronRealmType::FirmamentoZephyr" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronRealmType::None" },
		{ "PlanicieRadiante.DisplayName", "Plan\xc3\x83\xc2\xad""cie Radiante (Surface)" },
		{ "PlanicieRadiante.Name", "EAuracronRealmType::PlanicieRadiante" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de Realms do AURACRON" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronRealmType::None", (int64)EAuracronRealmType::None },
		{ "EAuracronRealmType::PlanicieRadiante", (int64)EAuracronRealmType::PlanicieRadiante },
		{ "EAuracronRealmType::FirmamentoZephyr", (int64)EAuracronRealmType::FirmamentoZephyr },
		{ "EAuracronRealmType::AbismoUmbrio", (int64)EAuracronRealmType::AbismoUmbrio },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	"EAuracronRealmType",
	"EAuracronRealmType",
	Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType()
{
	if (!Z_Registration_Info_UEnum_EAuracronRealmType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronRealmType.InnerSingleton, Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronRealmType.InnerSingleton;
}
// ********** End Enum EAuracronRealmType **********************************************************

// ********** Begin Enum EAuracronRealmTransitionState *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronRealmTransitionState;
static UEnum* EAuracronRealmTransitionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronRealmTransitionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronRealmTransitionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("EAuracronRealmTransitionState"));
	}
	return Z_Registration_Info_UEnum_EAuracronRealmTransitionState.OuterSingleton;
}
template<> AURACRONREALMSBRIDGE_API UEnum* StaticEnum<EAuracronRealmTransitionState>()
{
	return EAuracronRealmTransitionState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre Realms\n */" },
#endif
		{ "Evolving.DisplayName", "Evolving" },
		{ "Evolving.Name", "EAuracronRealmTransitionState::Evolving" },
		{ "Merging.DisplayName", "Merging" },
		{ "Merging.Name", "EAuracronRealmTransitionState::Merging" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
		{ "Splitting.DisplayName", "Splitting" },
		{ "Splitting.Name", "EAuracronRealmTransitionState::Splitting" },
		{ "Stable.DisplayName", "Stable" },
		{ "Stable.Name", "EAuracronRealmTransitionState::Stable" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre Realms" },
#endif
		{ "Transitioning.DisplayName", "Transitioning" },
		{ "Transitioning.Name", "EAuracronRealmTransitionState::Transitioning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronRealmTransitionState::Stable", (int64)EAuracronRealmTransitionState::Stable },
		{ "EAuracronRealmTransitionState::Transitioning", (int64)EAuracronRealmTransitionState::Transitioning },
		{ "EAuracronRealmTransitionState::Evolving", (int64)EAuracronRealmTransitionState::Evolving },
		{ "EAuracronRealmTransitionState::Merging", (int64)EAuracronRealmTransitionState::Merging },
		{ "EAuracronRealmTransitionState::Splitting", (int64)EAuracronRealmTransitionState::Splitting },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	"EAuracronRealmTransitionState",
	"EAuracronRealmTransitionState",
	Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState()
{
	if (!Z_Registration_Info_UEnum_EAuracronRealmTransitionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronRealmTransitionState.InnerSingleton, Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronRealmTransitionState.InnerSingleton;
}
// ********** End Enum EAuracronRealmTransitionState ***********************************************

// ********** Begin ScriptStruct FAuracronRealmConfiguration ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration;
class UScriptStruct* FAuracronRealmConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("AuracronRealmConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Realm\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmType_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmName_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmDescription_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinHeight_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "-10000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura m\xc3\x83\xc2\xadnima do Realm (em unidades) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura m\xc3\x83\xc2\xadnima do Realm (em unidades)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHeight_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "-10000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura m\xc3\x83\xc2\xa1xima do Realm (em unidades) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura m\xc3\x83\xc2\xa1xima do Realm (em unidades)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayers_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data Layers associadas ao Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Layers associadas ao Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraphs_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gr\xc3\x83\xc2\xa1""ficos PCG para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gr\xc3\x83\xc2\xa1""ficos PCG para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryLandscape_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Landscape principal do Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape principal do Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageActors_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atores de foliage instanciada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atores de foliage instanciada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientColor_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor ambiente do Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor ambiente do Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingIntensity_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogDensity_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade de n\xc3\x83\xc2\xa9voa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade de n\xc3\x83\xc2\xa9voa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityScale_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "-2000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gravidade espec\xc3\x83\xc2\xad""fica do Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gravidade espec\xc3\x83\xc2\xad""fica do Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedModifier_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de movimento modificada no Realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de movimento modificada no Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionTime_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ClampMax", "1800.0" },
		{ "ClampMin", "60.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Realm (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Realm (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bActiveByDefault_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativo por padr\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativo por padr\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowDynamicTransitions_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es din\xc3\x83\xc2\xa2micas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es din\xc3\x83\xc2\xa2micas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSupportsProceduralGeneration_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Suporta gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Suporta gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_RealmName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_RealmDescription;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHeight;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DataLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DataLayers;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PCGGraphs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PCGGraphs;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PrimaryLandscape;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FoliageActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FoliageActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AmbientColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightingIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FogDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GravityScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedModifier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionTime;
	static void NewProp_bActiveByDefault_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActiveByDefault;
	static void NewProp_bAllowDynamicTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowDynamicTransitions;
	static void NewProp_bSupportsProceduralGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSupportsProceduralGeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmType_MetaData), NewProp_RealmType_MetaData) }; // 2486602686
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmName = { "RealmName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, RealmName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmName_MetaData), NewProp_RealmName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmDescription = { "RealmDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, RealmDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmDescription_MetaData), NewProp_RealmDescription_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_MinHeight = { "MinHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, MinHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinHeight_MetaData), NewProp_MinHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_MaxHeight = { "MaxHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, MaxHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHeight_MetaData), NewProp_MaxHeight_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_DataLayers_Inner = { "DataLayers", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UDataLayerInstance_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_DataLayers = { "DataLayers", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, DataLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayers_MetaData), NewProp_DataLayers_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_PCGGraphs_Inner = { "PCGGraphs", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_PCGGraphs = { "PCGGraphs", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, PCGGraphs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraphs_MetaData), NewProp_PCGGraphs_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_PrimaryLandscape = { "PrimaryLandscape", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, PrimaryLandscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryLandscape_MetaData), NewProp_PrimaryLandscape_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_FoliageActors_Inner = { "FoliageActors", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AInstancedFoliageActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_FoliageActors = { "FoliageActors", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, FoliageActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageActors_MetaData), NewProp_FoliageActors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_AmbientColor = { "AmbientColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, AmbientColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientColor_MetaData), NewProp_AmbientColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_LightingIntensity = { "LightingIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, LightingIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingIntensity_MetaData), NewProp_LightingIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_FogDensity = { "FogDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, FogDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogDensity_MetaData), NewProp_FogDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_GravityScale = { "GravityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, GravityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityScale_MetaData), NewProp_GravityScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_MovementSpeedModifier = { "MovementSpeedModifier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, MovementSpeedModifier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedModifier_MetaData), NewProp_MovementSpeedModifier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_EvolutionTime = { "EvolutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfiguration, EvolutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionTime_MetaData), NewProp_EvolutionTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bActiveByDefault_SetBit(void* Obj)
{
	((FAuracronRealmConfiguration*)Obj)->bActiveByDefault = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bActiveByDefault = { "bActiveByDefault", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmConfiguration), &Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bActiveByDefault_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bActiveByDefault_MetaData), NewProp_bActiveByDefault_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bAllowDynamicTransitions_SetBit(void* Obj)
{
	((FAuracronRealmConfiguration*)Obj)->bAllowDynamicTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bAllowDynamicTransitions = { "bAllowDynamicTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmConfiguration), &Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bAllowDynamicTransitions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowDynamicTransitions_MetaData), NewProp_bAllowDynamicTransitions_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bSupportsProceduralGeneration_SetBit(void* Obj)
{
	((FAuracronRealmConfiguration*)Obj)->bSupportsProceduralGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bSupportsProceduralGeneration = { "bSupportsProceduralGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmConfiguration), &Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bSupportsProceduralGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSupportsProceduralGeneration_MetaData), NewProp_bSupportsProceduralGeneration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_RealmDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_MinHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_MaxHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_DataLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_DataLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_PCGGraphs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_PCGGraphs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_PrimaryLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_FoliageActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_FoliageActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_AmbientColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_LightingIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_FogDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_GravityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_MovementSpeedModifier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_EvolutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bActiveByDefault,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bAllowDynamicTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewProp_bSupportsProceduralGeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmConfiguration",
	Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::PropPointers),
	sizeof(FAuracronRealmConfiguration),
	alignof(FAuracronRealmConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronDimensionalPortal ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal;
class UScriptStruct* FAuracronDimensionalPortal::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDimensionalPortal, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("AuracronDimensionalPortal"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para portal dimensional entre Realms\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para portal dimensional entre Realms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceRealm_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm de origem */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm de origem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestinationRealm_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm de destino */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm de destino" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalLocation_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do portal no mundo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do portal no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalRotation_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do portal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalScale_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala do portal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationRadius_MetaData[] = {
		{ "Category", "Portal Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de ativa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do portal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de ativa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTime_MetaData[] = {
		{ "Category", "Portal Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalCooldown_MetaData[] = {
		{ "Category", "Portal Configuration" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown do portal (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown do portal (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPortalActive_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Portal ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Portal ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBidirectional_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o bidirecional */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o bidirecional" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresSpecialConditions_MetaData[] = {
		{ "Category", "Portal Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es especiais para ativa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es especiais para ativa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalParticleSystem_MetaData[] = {
		{ "Category", "Portal Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\x83\xc2\xad""culas do portal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\x83\xc2\xad""culas do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalSound_MetaData[] = {
		{ "Category", "Portal Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som do portal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalMaterial_MetaData[] = {
		{ "Category", "Portal Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material do portal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DestinationRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DestinationRealm;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PortalLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PortalRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PortalScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PortalCooldown;
	static void NewProp_bPortalActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPortalActive;
	static void NewProp_bBidirectional_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBidirectional;
	static void NewProp_bRequiresSpecialConditions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresSpecialConditions;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PortalParticleSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PortalSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PortalMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDimensionalPortal>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_SourceRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_SourceRealm = { "SourceRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, SourceRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceRealm_MetaData), NewProp_SourceRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_DestinationRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_DestinationRealm = { "DestinationRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, DestinationRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestinationRealm_MetaData), NewProp_DestinationRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalLocation = { "PortalLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, PortalLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalLocation_MetaData), NewProp_PortalLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalRotation = { "PortalRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, PortalRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalRotation_MetaData), NewProp_PortalRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalScale = { "PortalScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, PortalScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalScale_MetaData), NewProp_PortalScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_ActivationRadius = { "ActivationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, ActivationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationRadius_MetaData), NewProp_ActivationRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, TransitionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTime_MetaData), NewProp_TransitionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalCooldown = { "PortalCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, PortalCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalCooldown_MetaData), NewProp_PortalCooldown_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bPortalActive_SetBit(void* Obj)
{
	((FAuracronDimensionalPortal*)Obj)->bPortalActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bPortalActive = { "bPortalActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDimensionalPortal), &Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bPortalActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPortalActive_MetaData), NewProp_bPortalActive_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bBidirectional_SetBit(void* Obj)
{
	((FAuracronDimensionalPortal*)Obj)->bBidirectional = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bBidirectional = { "bBidirectional", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDimensionalPortal), &Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bBidirectional_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBidirectional_MetaData), NewProp_bBidirectional_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bRequiresSpecialConditions_SetBit(void* Obj)
{
	((FAuracronDimensionalPortal*)Obj)->bRequiresSpecialConditions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bRequiresSpecialConditions = { "bRequiresSpecialConditions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDimensionalPortal), &Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bRequiresSpecialConditions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresSpecialConditions_MetaData), NewProp_bRequiresSpecialConditions_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalParticleSystem = { "PortalParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, PortalParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalParticleSystem_MetaData), NewProp_PortalParticleSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalSound = { "PortalSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, PortalSound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalSound_MetaData), NewProp_PortalSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalMaterial = { "PortalMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDimensionalPortal, PortalMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalMaterial_MetaData), NewProp_PortalMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_SourceRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_SourceRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_DestinationRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_DestinationRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_ActivationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_TransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bPortalActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bBidirectional,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_bRequiresSpecialConditions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewProp_PortalMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	&NewStructOps,
	"AuracronDimensionalPortal",
	Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::PropPointers),
	sizeof(FAuracronDimensionalPortal),
	alignof(FAuracronDimensionalPortal),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDimensionalPortal()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDimensionalPortal ******************************************

// ********** Begin ScriptStruct FAuracronMapEvolution *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMapEvolution;
class UScriptStruct* FAuracronMapEvolution::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMapEvolution.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMapEvolution.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMapEvolution, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("AuracronMapEvolution"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMapEvolution.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o temporal do mapa\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o temporal do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Map Evolution" },
		{ "ClampMax", "3600.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de in\xc3\x83\xc2\xad""cio da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos de jogo) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de in\xc3\x83\xc2\xad""cio da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos de jogo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Map Evolution" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedRealms_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realms afetados pela evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realms afetados pela evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionPCGGraphs_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gr\xc3\x83\xc2\xa1""ficos PCG para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gr\xc3\x83\xc2\xa1""ficos PCG para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewPortals_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Novos portais criados durante evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Novos portais criados durante evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RemovedPortalIndices_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Portais removidos durante evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Portais removidos durante evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewAmbientColor_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mudan\xc3\x83\xc2\xa7""as na ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mudan\xc3\x83\xc2\xa7""as na ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewLightingIntensity_MetaData[] = {
		{ "Category", "Map Evolution" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nova intensidade de ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nova intensidade de ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionParticleSystem_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos visuais da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos visuais da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionSound_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEvolutionActive_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o ativa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectsNavigation_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Afeta navega\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Afeta navega\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresPCGRegeneration_MetaData[] = {
		{ "Category", "Map Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de PCG */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de PCG" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AffectedRealms_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AffectedRealms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AffectedRealms;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EvolutionPCGGraphs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EvolutionPCGGraphs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewPortals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NewPortals;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RemovedPortalIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RemovedPortalIndices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewAmbientColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewLightingIntensity;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EvolutionParticleSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EvolutionSound;
	static void NewProp_bEvolutionActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEvolutionActive;
	static void NewProp_bAffectsNavigation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectsNavigation;
	static void NewProp_bRequiresPCGRegeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresPCGRegeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMapEvolution>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, StartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_AffectedRealms_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_AffectedRealms_Inner = { "AffectedRealms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_AffectedRealms = { "AffectedRealms", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, AffectedRealms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedRealms_MetaData), NewProp_AffectedRealms_MetaData) }; // 2486602686
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionPCGGraphs_Inner = { "EvolutionPCGGraphs", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionPCGGraphs = { "EvolutionPCGGraphs", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, EvolutionPCGGraphs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionPCGGraphs_MetaData), NewProp_EvolutionPCGGraphs_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewPortals_Inner = { "NewPortals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDimensionalPortal, METADATA_PARAMS(0, nullptr) }; // 4019285765
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewPortals = { "NewPortals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, NewPortals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewPortals_MetaData), NewProp_NewPortals_MetaData) }; // 4019285765
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_RemovedPortalIndices_Inner = { "RemovedPortalIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_RemovedPortalIndices = { "RemovedPortalIndices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, RemovedPortalIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RemovedPortalIndices_MetaData), NewProp_RemovedPortalIndices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewAmbientColor = { "NewAmbientColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, NewAmbientColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewAmbientColor_MetaData), NewProp_NewAmbientColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewLightingIntensity = { "NewLightingIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, NewLightingIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewLightingIntensity_MetaData), NewProp_NewLightingIntensity_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionParticleSystem = { "EvolutionParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, EvolutionParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionParticleSystem_MetaData), NewProp_EvolutionParticleSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionSound = { "EvolutionSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMapEvolution, EvolutionSound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionSound_MetaData), NewProp_EvolutionSound_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bEvolutionActive_SetBit(void* Obj)
{
	((FAuracronMapEvolution*)Obj)->bEvolutionActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bEvolutionActive = { "bEvolutionActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMapEvolution), &Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bEvolutionActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEvolutionActive_MetaData), NewProp_bEvolutionActive_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bAffectsNavigation_SetBit(void* Obj)
{
	((FAuracronMapEvolution*)Obj)->bAffectsNavigation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bAffectsNavigation = { "bAffectsNavigation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMapEvolution), &Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bAffectsNavigation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectsNavigation_MetaData), NewProp_bAffectsNavigation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bRequiresPCGRegeneration_SetBit(void* Obj)
{
	((FAuracronMapEvolution*)Obj)->bRequiresPCGRegeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bRequiresPCGRegeneration = { "bRequiresPCGRegeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMapEvolution), &Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bRequiresPCGRegeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresPCGRegeneration_MetaData), NewProp_bRequiresPCGRegeneration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_AffectedRealms_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_AffectedRealms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_AffectedRealms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionPCGGraphs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionPCGGraphs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewPortals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewPortals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_RemovedPortalIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_RemovedPortalIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewAmbientColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_NewLightingIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_EvolutionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bEvolutionActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bAffectsNavigation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewProp_bRequiresPCGRegeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	&NewStructOps,
	"AuracronMapEvolution",
	Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::PropPointers),
	sizeof(FAuracronMapEvolution),
	alignof(FAuracronMapEvolution),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMapEvolution()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMapEvolution.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMapEvolution.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMapEvolution.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMapEvolution ***********************************************

// ********** Begin ScriptStruct FAuracronVerticalConnector ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector;
class UScriptStruct* FAuracronVerticalConnector::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronVerticalConnector, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("AuracronVerticalConnector"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para conector vertical entre Realms\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para conector vertical entre Realms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LowerRealm_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm inferior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm inferior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpperRealm_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm superior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm superior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorLocation_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do conector */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do conector" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorRadius_MetaData[] = {
		{ "Category", "Vertical Connector" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do conector */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do conector" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorHeight_MetaData[] = {
		{ "Category", "Vertical Connector" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "200.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura do conector */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura do conector" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorType_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de conector (escada, elevador, portal, etc.) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de conector (escada, elevador, portal, etc.)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSpeed_MetaData[] = {
		{ "Category", "Vertical Connector" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o vertical */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o vertical" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBidirectional_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite movimento bidirecional */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite movimento bidirecional" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bConnectorActive_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Conector ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conector ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresSpecialAbility_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer habilidade especial para usar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer habilidade especial para usar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorMesh_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh do conector */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh do conector" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorMaterial_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material do conector */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material do conector" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorParticleSystem_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos visuais do conector */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos visuais do conector" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectorSound_MetaData[] = {
		{ "Category", "Vertical Connector" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som do conector */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som do conector" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LowerRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LowerRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_UpperRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UpperRealm;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectorLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectorRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectorHeight;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConnectorType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionSpeed;
	static void NewProp_bBidirectional_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBidirectional;
	static void NewProp_bConnectorActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bConnectorActive;
	static void NewProp_bRequiresSpecialAbility_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresSpecialAbility;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ConnectorMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ConnectorMaterial;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ConnectorParticleSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ConnectorSound;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronVerticalConnector>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_LowerRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_LowerRealm = { "LowerRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, LowerRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LowerRealm_MetaData), NewProp_LowerRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_UpperRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_UpperRealm = { "UpperRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, UpperRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpperRealm_MetaData), NewProp_UpperRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorLocation = { "ConnectorLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorLocation_MetaData), NewProp_ConnectorLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorRadius = { "ConnectorRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorRadius_MetaData), NewProp_ConnectorRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorHeight = { "ConnectorHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorHeight_MetaData), NewProp_ConnectorHeight_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorType = { "ConnectorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorType_MetaData), NewProp_ConnectorType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_TransitionSpeed = { "TransitionSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, TransitionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSpeed_MetaData), NewProp_TransitionSpeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bBidirectional_SetBit(void* Obj)
{
	((FAuracronVerticalConnector*)Obj)->bBidirectional = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bBidirectional = { "bBidirectional", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVerticalConnector), &Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bBidirectional_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBidirectional_MetaData), NewProp_bBidirectional_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bConnectorActive_SetBit(void* Obj)
{
	((FAuracronVerticalConnector*)Obj)->bConnectorActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bConnectorActive = { "bConnectorActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVerticalConnector), &Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bConnectorActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bConnectorActive_MetaData), NewProp_bConnectorActive_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bRequiresSpecialAbility_SetBit(void* Obj)
{
	((FAuracronVerticalConnector*)Obj)->bRequiresSpecialAbility = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bRequiresSpecialAbility = { "bRequiresSpecialAbility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVerticalConnector), &Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bRequiresSpecialAbility_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresSpecialAbility_MetaData), NewProp_bRequiresSpecialAbility_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorMesh = { "ConnectorMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorMesh_MetaData), NewProp_ConnectorMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorMaterial = { "ConnectorMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorMaterial_MetaData), NewProp_ConnectorMaterial_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorParticleSystem = { "ConnectorParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorParticleSystem_MetaData), NewProp_ConnectorParticleSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorSound = { "ConnectorSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVerticalConnector, ConnectorSound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectorSound_MetaData), NewProp_ConnectorSound_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_LowerRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_LowerRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_UpperRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_UpperRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_TransitionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bBidirectional,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bConnectorActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_bRequiresSpecialAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewProp_ConnectorSound,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	&NewStructOps,
	"AuracronVerticalConnector",
	Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::PropPointers),
	sizeof(FAuracronVerticalConnector),
	alignof(FAuracronVerticalConnector),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronVerticalConnector()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector.InnerSingleton, Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector.InnerSingleton;
}
// ********** End ScriptStruct FAuracronVerticalConnector ******************************************

// ********** Begin ScriptStruct FAuracronProceduralGenerationConfig *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig;
class UScriptStruct* FAuracronProceduralGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("AuracronProceduralGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetRealm_MetaData[] = {
		{ "Category", "Procedural Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm alvo para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm alvo para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainPCGGraph_MetaData[] = {
		{ "Category", "Procedural Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gr\xc3\x83\xc2\xa1""fico PCG principal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gr\xc3\x83\xc2\xa1""fico PCG principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryPCGGraphs_MetaData[] = {
		{ "Category", "Procedural Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gr\xc3\x83\xc2\xa1""ficos PCG secund\xc3\x83\xc2\xa1rios */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gr\xc3\x83\xc2\xa1""ficos PCG secund\xc3\x83\xc2\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationSeed_MetaData[] = {
		{ "Category", "Procedural Generation" },
		{ "ClampMax", "999999" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seed para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o aleat\xc3\x83\xc2\xb3ria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seed para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o aleat\xc3\x83\xc2\xb3ria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationDensity_MetaData[] = {
		{ "Category", "Procedural Generation" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationArea_MetaData[] = {
		{ "Category", "Procedural Generation" },
		{ "ClampMax", "10000000.0" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x81rea de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em unidades quadradas) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x81rea de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em unidades quadradas)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegenerationInterval_MetaData[] = {
		{ "Category", "Procedural Generation" },
		{ "ClampMax", "600.0" },
		{ "ClampMin", "30.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerationActive_MetaData[] = {
		{ "Category", "Procedural Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o ativa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowDynamicRegeneration_MetaData[] = {
		{ "Category", "Procedural Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o din\xc3\x83\xc2\xa2mica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncStreaming_MetaData[] = {
		{ "Category", "Procedural Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usa streaming ass\xc3\x83\xc2\xadncrono */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usa streaming ass\xc3\x83\xc2\xadncrono" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMobileOptimized_MetaData[] = {
		{ "Category", "Procedural Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Otimizado para mobile */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizado para mobile" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetRealm;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MainPCGGraph;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SecondaryPCGGraphs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SecondaryPCGGraphs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GenerationSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationArea;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RegenerationInterval;
	static void NewProp_bGenerationActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerationActive;
	static void NewProp_bAllowDynamicRegeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowDynamicRegeneration;
	static void NewProp_bUseAsyncStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncStreaming;
	static void NewProp_bMobileOptimized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMobileOptimized;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronProceduralGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_TargetRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_TargetRealm = { "TargetRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfig, TargetRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetRealm_MetaData), NewProp_TargetRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_MainPCGGraph = { "MainPCGGraph", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfig, MainPCGGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainPCGGraph_MetaData), NewProp_MainPCGGraph_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_SecondaryPCGGraphs_Inner = { "SecondaryPCGGraphs", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_SecondaryPCGGraphs = { "SecondaryPCGGraphs", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfig, SecondaryPCGGraphs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryPCGGraphs_MetaData), NewProp_SecondaryPCGGraphs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_GenerationSeed = { "GenerationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfig, GenerationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationSeed_MetaData), NewProp_GenerationSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_GenerationDensity = { "GenerationDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfig, GenerationDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationDensity_MetaData), NewProp_GenerationDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_GenerationArea = { "GenerationArea", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfig, GenerationArea), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationArea_MetaData), NewProp_GenerationArea_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_RegenerationInterval = { "RegenerationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfig, RegenerationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegenerationInterval_MetaData), NewProp_RegenerationInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bGenerationActive_SetBit(void* Obj)
{
	((FAuracronProceduralGenerationConfig*)Obj)->bGenerationActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bGenerationActive = { "bGenerationActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralGenerationConfig), &Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bGenerationActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerationActive_MetaData), NewProp_bGenerationActive_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bAllowDynamicRegeneration_SetBit(void* Obj)
{
	((FAuracronProceduralGenerationConfig*)Obj)->bAllowDynamicRegeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bAllowDynamicRegeneration = { "bAllowDynamicRegeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralGenerationConfig), &Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bAllowDynamicRegeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowDynamicRegeneration_MetaData), NewProp_bAllowDynamicRegeneration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bUseAsyncStreaming_SetBit(void* Obj)
{
	((FAuracronProceduralGenerationConfig*)Obj)->bUseAsyncStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bUseAsyncStreaming = { "bUseAsyncStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralGenerationConfig), &Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bUseAsyncStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncStreaming_MetaData), NewProp_bUseAsyncStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bMobileOptimized_SetBit(void* Obj)
{
	((FAuracronProceduralGenerationConfig*)Obj)->bMobileOptimized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bMobileOptimized = { "bMobileOptimized", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralGenerationConfig), &Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bMobileOptimized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMobileOptimized_MetaData), NewProp_bMobileOptimized_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_TargetRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_TargetRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_MainPCGGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_SecondaryPCGGraphs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_SecondaryPCGGraphs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_GenerationSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_GenerationDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_GenerationArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_RegenerationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bGenerationActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bAllowDynamicRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bUseAsyncStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewProp_bMobileOptimized,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	&NewStructOps,
	"AuracronProceduralGenerationConfig",
	Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::PropPointers),
	sizeof(FAuracronProceduralGenerationConfig),
	alignof(FAuracronProceduralGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronProceduralGenerationConfig *********************************

// ********** Begin ScriptStruct FAuracronRealmConfigurationEntry **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry;
class UScriptStruct* FAuracronRealmConfigurationEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("AuracronRealmConfigurationEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de Realm (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de Realm (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmType_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmConfigurationEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfigurationEntry, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmType_MetaData), NewProp_RealmType_MetaData) }; // 2486602686
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmConfigurationEntry, Configuration), Z_Construct_UScriptStruct_FAuracronRealmConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 224665536
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmConfigurationEntry",
	Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::PropPointers),
	sizeof(FAuracronRealmConfigurationEntry),
	alignof(FAuracronRealmConfigurationEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmConfigurationEntry ************************************

// ********** Begin ScriptStruct FAuracronProceduralGenerationConfigEntry **************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry;
class UScriptStruct* FAuracronProceduralGenerationConfigEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry, (UObject*)Z_Construct_UPackage__Script_AuracronRealmsBridge(), TEXT("AuracronProceduralGenerationConfigEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o procedural (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o procedural (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmType_MetaData[] = {
		{ "Category", "Procedural Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "Category", "Procedural Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronProceduralGenerationConfigEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfigEntry, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmType_MetaData), NewProp_RealmType_MetaData) }; // 2486602686
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralGenerationConfigEntry, Configuration), Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1617504779
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
	nullptr,
	&NewStructOps,
	"AuracronProceduralGenerationConfigEntry",
	Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::PropPointers),
	sizeof(FAuracronProceduralGenerationConfigEntry),
	alignof(FAuracronProceduralGenerationConfigEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronProceduralGenerationConfigEntry ****************************

// ********** Begin Delegate FOnRealmActivated *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnRealmActivated_Parms
	{
		EAuracronRealmType RealmType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Realm \xc3\x83\xc2\xa9 ativado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Realm \xc3\x83\xc2\xa9 ativado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnRealmActivated_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::NewProp_RealmType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnRealmActivated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::AuracronRealmsBridge_eventOnRealmActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::AuracronRealmsBridge_eventOnRealmActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnRealmActivated_DelegateWrapper(const FMulticastScriptDelegate& OnRealmActivated, EAuracronRealmType RealmType)
{
	struct AuracronRealmsBridge_eventOnRealmActivated_Parms
	{
		EAuracronRealmType RealmType;
	};
	AuracronRealmsBridge_eventOnRealmActivated_Parms Parms;
	Parms.RealmType=RealmType;
	OnRealmActivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRealmActivated *******************************************************

// ********** Begin Delegate FOnRealmDeactivated ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnRealmDeactivated_Parms
	{
		EAuracronRealmType RealmType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Realm \xc3\x83\xc2\xa9 desativado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Realm \xc3\x83\xc2\xa9 desativado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnRealmDeactivated_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::NewProp_RealmType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnRealmDeactivated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::AuracronRealmsBridge_eventOnRealmDeactivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::AuracronRealmsBridge_eventOnRealmDeactivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnRealmDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnRealmDeactivated, EAuracronRealmType RealmType)
{
	struct AuracronRealmsBridge_eventOnRealmDeactivated_Parms
	{
		EAuracronRealmType RealmType;
	};
	AuracronRealmsBridge_eventOnRealmDeactivated_Parms Parms;
	Parms.RealmType=RealmType;
	OnRealmDeactivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRealmDeactivated *****************************************************

// ********** Begin Delegate FOnTransitionStarted **************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnTransitionStarted_Parms
	{
		EAuracronRealmType FromRealm;
		EAuracronRealmType ToRealm;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 iniciada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 iniciada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FromRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FromRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ToRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ToRealm;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_FromRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_FromRealm = { "FromRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnTransitionStarted_Parms, FromRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_ToRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_ToRealm = { "ToRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnTransitionStarted_Parms, ToRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_FromRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_FromRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_ToRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::NewProp_ToRealm,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnTransitionStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnTransitionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnTransitionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnTransitionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTransitionStarted, EAuracronRealmType FromRealm, EAuracronRealmType ToRealm)
{
	struct AuracronRealmsBridge_eventOnTransitionStarted_Parms
	{
		EAuracronRealmType FromRealm;
		EAuracronRealmType ToRealm;
	};
	AuracronRealmsBridge_eventOnTransitionStarted_Parms Parms;
	Parms.FromRealm=FromRealm;
	Parms.ToRealm=ToRealm;
	OnTransitionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTransitionStarted ****************************************************

// ********** Begin Delegate FOnTransitionCompleted ************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnTransitionCompleted_Parms
	{
		EAuracronRealmType FromRealm;
		EAuracronRealmType ToRealm;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FromRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FromRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ToRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ToRealm;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_FromRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_FromRealm = { "FromRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnTransitionCompleted_Parms, FromRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_ToRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_ToRealm = { "ToRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnTransitionCompleted_Parms, ToRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_FromRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_FromRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_ToRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::NewProp_ToRealm,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnTransitionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnTransitionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnTransitionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnTransitionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTransitionCompleted, EAuracronRealmType FromRealm, EAuracronRealmType ToRealm)
{
	struct AuracronRealmsBridge_eventOnTransitionCompleted_Parms
	{
		EAuracronRealmType FromRealm;
		EAuracronRealmType ToRealm;
	};
	AuracronRealmsBridge_eventOnTransitionCompleted_Parms Parms;
	Parms.FromRealm=FromRealm;
	Parms.ToRealm=ToRealm;
	OnTransitionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTransitionCompleted **************************************************

// ********** Begin Delegate FOnEvolutionStarted ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnEvolutionStarted_Parms
	{
		int32 EvolutionIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 iniciada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 iniciada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_EvolutionIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::NewProp_EvolutionIndex = { "EvolutionIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnEvolutionStarted_Parms, EvolutionIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::NewProp_EvolutionIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnEvolutionStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnEvolutionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnEvolutionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnEvolutionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnEvolutionStarted, int32 EvolutionIndex)
{
	struct AuracronRealmsBridge_eventOnEvolutionStarted_Parms
	{
		int32 EvolutionIndex;
	};
	AuracronRealmsBridge_eventOnEvolutionStarted_Parms Parms;
	Parms.EvolutionIndex=EvolutionIndex;
	OnEvolutionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnEvolutionStarted *****************************************************

// ********** Begin Delegate FOnEvolutionCompleted *************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnEvolutionCompleted_Parms
	{
		int32 EvolutionIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_EvolutionIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::NewProp_EvolutionIndex = { "EvolutionIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnEvolutionCompleted_Parms, EvolutionIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::NewProp_EvolutionIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnEvolutionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnEvolutionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnEvolutionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnEvolutionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnEvolutionCompleted, int32 EvolutionIndex)
{
	struct AuracronRealmsBridge_eventOnEvolutionCompleted_Parms
	{
		int32 EvolutionIndex;
	};
	AuracronRealmsBridge_eventOnEvolutionCompleted_Parms Parms;
	Parms.EvolutionIndex=EvolutionIndex;
	OnEvolutionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnEvolutionCompleted ***************************************************

// ********** Begin Delegate FOnPortalCreated ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnPortalCreated_Parms
	{
		int32 PortalIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando portal \xc3\x83\xc2\xa9 criado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando portal \xc3\x83\xc2\xa9 criado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PortalIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::NewProp_PortalIndex = { "PortalIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnPortalCreated_Parms, PortalIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::NewProp_PortalIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnPortalCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::AuracronRealmsBridge_eventOnPortalCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::AuracronRealmsBridge_eventOnPortalCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnPortalCreated_DelegateWrapper(const FMulticastScriptDelegate& OnPortalCreated, int32 PortalIndex)
{
	struct AuracronRealmsBridge_eventOnPortalCreated_Parms
	{
		int32 PortalIndex;
	};
	AuracronRealmsBridge_eventOnPortalCreated_Parms Parms;
	Parms.PortalIndex=PortalIndex;
	OnPortalCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPortalCreated ********************************************************

// ********** Begin Delegate FOnPortalRemoved ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnPortalRemoved_Parms
	{
		int32 PortalIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando portal \xc3\x83\xc2\xa9 removido */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando portal \xc3\x83\xc2\xa9 removido" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PortalIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::NewProp_PortalIndex = { "PortalIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnPortalRemoved_Parms, PortalIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::NewProp_PortalIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnPortalRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::AuracronRealmsBridge_eventOnPortalRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::AuracronRealmsBridge_eventOnPortalRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnPortalRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnPortalRemoved, int32 PortalIndex)
{
	struct AuracronRealmsBridge_eventOnPortalRemoved_Parms
	{
		int32 PortalIndex;
	};
	AuracronRealmsBridge_eventOnPortalRemoved_Parms Parms;
	Parms.PortalIndex=PortalIndex;
	OnPortalRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPortalRemoved ********************************************************

// ********** Begin Delegate FOnProceduralGenerationCompleted **************************************
struct Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics
{
	struct AuracronRealmsBridge_eventOnProceduralGenerationCompleted_Parms
	{
		EAuracronRealmType RealmType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural \xc3\x83\xc2\xa9 completada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural \xc3\x83\xc2\xa9 completada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventOnProceduralGenerationCompleted_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_RealmType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnProceduralGenerationCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnProceduralGenerationCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::AuracronRealmsBridge_eventOnProceduralGenerationCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronRealmsBridge::FOnProceduralGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnProceduralGenerationCompleted, EAuracronRealmType RealmType)
{
	struct AuracronRealmsBridge_eventOnProceduralGenerationCompleted_Parms
	{
		EAuracronRealmType RealmType;
	};
	AuracronRealmsBridge_eventOnProceduralGenerationCompleted_Parms Parms;
	Parms.RealmType=RealmType;
	OnProceduralGenerationCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnProceduralGenerationCompleted ****************************************

// ********** Begin Class UAuracronRealmsBridge Function ActivateRealm *****************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics
{
	struct AuracronRealmsBridge_eventActivateRealm_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar um Realm espec\xc3\x83\xc2\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar um Realm espec\xc3\x83\xc2\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventActivateRealm_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventActivateRealm_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventActivateRealm_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "ActivateRealm", Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::AuracronRealmsBridge_eventActivateRealm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::AuracronRealmsBridge_eventActivateRealm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execActivateRealm)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateRealm(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function ActivateRealm *******************************

// ********** Begin Class UAuracronRealmsBridge Function CancelRealmTransition *********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics
{
	struct AuracronRealmsBridge_eventCancelRealmTransition_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Cancelar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em andamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancelar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em andamento" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventCancelRealmTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventCancelRealmTransition_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "CancelRealmTransition", Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::AuracronRealmsBridge_eventCancelRealmTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::AuracronRealmsBridge_eventCancelRealmTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execCancelRealmTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelRealmTransition();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function CancelRealmTransition ***********************

// ********** Begin Class UAuracronRealmsBridge Function ClearProceduralContent ********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics
{
	struct AuracronRealmsBridge_eventClearProceduralContent_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpar conte\xc3\x83\xc2\xba""do procedural\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar conte\xc3\x83\xc2\xba""do procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventClearProceduralContent_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventClearProceduralContent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventClearProceduralContent_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "ClearProceduralContent", Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::AuracronRealmsBridge_eventClearProceduralContent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::AuracronRealmsBridge_eventClearProceduralContent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execClearProceduralContent)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ClearProceduralContent(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function ClearProceduralContent **********************

// ********** Begin Class UAuracronRealmsBridge Function CompleteRealmTransition *******************
struct Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics
{
	struct AuracronRealmsBridge_eventCompleteRealmTransition_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Completar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em andamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em andamento" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventCompleteRealmTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventCompleteRealmTransition_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "CompleteRealmTransition", Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::AuracronRealmsBridge_eventCompleteRealmTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::AuracronRealmsBridge_eventCompleteRealmTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execCompleteRealmTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CompleteRealmTransition();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function CompleteRealmTransition *********************

// ********** Begin Class UAuracronRealmsBridge Function CreateDimensionalPortal *******************
struct Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics
{
	struct AuracronRealmsBridge_eventCreateDimensionalPortal_Parms
	{
		FAuracronDimensionalPortal PortalConfig;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Portals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar portal dimensional\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar portal dimensional" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PortalConfig;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::NewProp_PortalConfig = { "PortalConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventCreateDimensionalPortal_Parms, PortalConfig), Z_Construct_UScriptStruct_FAuracronDimensionalPortal, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalConfig_MetaData), NewProp_PortalConfig_MetaData) }; // 4019285765
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventCreateDimensionalPortal_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::NewProp_PortalConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "CreateDimensionalPortal", Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::AuracronRealmsBridge_eventCreateDimensionalPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::AuracronRealmsBridge_eventCreateDimensionalPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execCreateDimensionalPortal)
{
	P_GET_STRUCT_REF(FAuracronDimensionalPortal,Z_Param_Out_PortalConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->CreateDimensionalPortal(Z_Param_Out_PortalConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function CreateDimensionalPortal *********************

// ********** Begin Class UAuracronRealmsBridge Function DeactivateRealm ***************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics
{
	struct AuracronRealmsBridge_eventDeactivateRealm_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Desativar um Realm espec\xc3\x83\xc2\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar um Realm espec\xc3\x83\xc2\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventDeactivateRealm_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventDeactivateRealm_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventDeactivateRealm_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "DeactivateRealm", Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::AuracronRealmsBridge_eventDeactivateRealm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::AuracronRealmsBridge_eventDeactivateRealm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execDeactivateRealm)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeactivateRealm(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function DeactivateRealm *****************************

// ********** Begin Class UAuracronRealmsBridge Function ExecuteProceduralGeneration ***************
struct Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics
{
	struct AuracronRealmsBridge_eventExecuteProceduralGeneration_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executar gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural para um Realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executar gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural para um Realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventExecuteProceduralGeneration_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventExecuteProceduralGeneration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventExecuteProceduralGeneration_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "ExecuteProceduralGeneration", Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::AuracronRealmsBridge_eventExecuteProceduralGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::AuracronRealmsBridge_eventExecuteProceduralGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execExecuteProceduralGeneration)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteProceduralGeneration(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function ExecuteProceduralGeneration *****************

// ********** Begin Class UAuracronRealmsBridge Function FindNearestPortal *************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics
{
	struct AuracronRealmsBridge_eventFindNearestPortal_Parms
	{
		FVector Location;
		float MaxDistance;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Portals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Encontrar portal mais pr\xc3\x83\xc2\xb3ximo\n     */" },
#endif
		{ "CPP_Default_MaxDistance", "1000.000000" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Encontrar portal mais pr\xc3\x83\xc2\xb3ximo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventFindNearestPortal_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::NewProp_MaxDistance = { "MaxDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventFindNearestPortal_Parms, MaxDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventFindNearestPortal_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::NewProp_MaxDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "FindNearestPortal", Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::AuracronRealmsBridge_eventFindNearestPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::AuracronRealmsBridge_eventFindNearestPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execFindNearestPortal)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->FindNearestPortal(Z_Param_Out_Location,Z_Param_MaxDistance);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function FindNearestPortal ***************************

// ********** Begin Class UAuracronRealmsBridge Function GetActivePortals **************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics
{
	struct AuracronRealmsBridge_eventGetActivePortals_Parms
	{
		TArray<FAuracronDimensionalPortal> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Portals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter todos os portais ativos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todos os portais ativos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDimensionalPortal, METADATA_PARAMS(0, nullptr) }; // 4019285765
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetActivePortals_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4019285765
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetActivePortals", Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::AuracronRealmsBridge_eventGetActivePortals_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::AuracronRealmsBridge_eventGetActivePortals_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetActivePortals)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronDimensionalPortal>*)Z_Param__Result=P_THIS->GetActivePortals();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetActivePortals ****************************

// ********** Begin Class UAuracronRealmsBridge Function GetActiveRealm ****************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics
{
	struct AuracronRealmsBridge_eventGetActiveRealm_Parms
	{
		EAuracronRealmType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter Realm ativo no momento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter Realm ativo no momento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetActiveRealm_Parms, ReturnValue), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetActiveRealm", Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::AuracronRealmsBridge_eventGetActiveRealm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::AuracronRealmsBridge_eventGetActiveRealm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetActiveRealm)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronRealmType*)Z_Param__Result=P_THIS->GetActiveRealm();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetActiveRealm ******************************

// ********** Begin Class UAuracronRealmsBridge Function GetActiveRealms ***************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics
{
	struct AuracronRealmsBridge_eventGetActiveRealms_Parms
	{
		TArray<EAuracronRealmType> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter todos os Realms ativos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todos os Realms ativos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::NewProp_ReturnValue_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetActiveRealms_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::NewProp_ReturnValue_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetActiveRealms", Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::AuracronRealmsBridge_eventGetActiveRealms_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::AuracronRealmsBridge_eventGetActiveRealms_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetActiveRealms)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<EAuracronRealmType>*)Z_Param__Result=P_THIS->GetActiveRealms();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetActiveRealms *****************************

// ********** Begin Class UAuracronRealmsBridge Function GetEvolutionProgress **********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics
{
	struct AuracronRealmsBridge_eventGetEvolutionProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter progresso da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progresso da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetEvolutionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetEvolutionProgress", Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::AuracronRealmsBridge_eventGetEvolutionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::AuracronRealmsBridge_eventGetEvolutionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetEvolutionProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEvolutionProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetEvolutionProgress ************************

// ********** Begin Class UAuracronRealmsBridge Function GetProceduralGenerationProgress ***********
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics
{
	struct AuracronRealmsBridge_eventGetProceduralGenerationProgress_Parms
	{
		EAuracronRealmType RealmType;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter progresso da gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progresso da gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetProceduralGenerationProgress_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetProceduralGenerationProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetProceduralGenerationProgress", Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::AuracronRealmsBridge_eventGetProceduralGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::AuracronRealmsBridge_eventGetProceduralGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetProceduralGenerationProgress)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetProceduralGenerationProgress(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetProceduralGenerationProgress *************

// ********** Begin Class UAuracronRealmsBridge Function GetRealmConfiguration *********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics
{
	struct AuracronRealmsBridge_eventGetRealmConfiguration_Parms
	{
		EAuracronRealmType RealmType;
		FAuracronRealmConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetRealmConfiguration_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetRealmConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronRealmConfiguration, METADATA_PARAMS(0, nullptr) }; // 224665536
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetRealmConfiguration", Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::AuracronRealmsBridge_eventGetRealmConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::AuracronRealmsBridge_eventGetRealmConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetRealmConfiguration)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronRealmConfiguration*)Z_Param__Result=P_THIS->GetRealmConfiguration(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetRealmConfiguration ***********************

// ********** Begin Class UAuracronRealmsBridge Function GetTransitionProgress *********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics
{
	struct AuracronRealmsBridge_eventGetTransitionProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter progresso da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (0.0 - 1.0)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progresso da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (0.0 - 1.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetTransitionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetTransitionProgress", Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::AuracronRealmsBridge_eventGetTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::AuracronRealmsBridge_eventGetTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetTransitionProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTransitionProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetTransitionProgress ***********************

// ********** Begin Class UAuracronRealmsBridge Function GetTransitionState ************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics
{
	struct AuracronRealmsBridge_eventGetTransitionState_Parms
	{
		EAuracronRealmTransitionState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estado atual da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estado atual da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventGetTransitionState_Parms, ReturnValue), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState, METADATA_PARAMS(0, nullptr) }; // 1102994998
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "GetTransitionState", Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::AuracronRealmsBridge_eventGetTransitionState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::AuracronRealmsBridge_eventGetTransitionState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execGetTransitionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronRealmTransitionState*)Z_Param__Result=P_THIS->GetTransitionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function GetTransitionState **************************

// ********** Begin Class UAuracronRealmsBridge Function IsEvolutionActive *************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics
{
	struct AuracronRealmsBridge_eventIsEvolutionActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o est\xc3\x83\xc2\xa1 ativa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o est\xc3\x83\xc2\xa1 ativa" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventIsEvolutionActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventIsEvolutionActive_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "IsEvolutionActive", Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::AuracronRealmsBridge_eventIsEvolutionActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::AuracronRealmsBridge_eventIsEvolutionActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execIsEvolutionActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsEvolutionActive();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function IsEvolutionActive ***************************

// ********** Begin Class UAuracronRealmsBridge Function IsRealmActive *****************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics
{
	struct AuracronRealmsBridge_eventIsRealmActive_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se um Realm est\xc3\x83\xc2\xa1 ativo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se um Realm est\xc3\x83\xc2\xa1 ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventIsRealmActive_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventIsRealmActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventIsRealmActive_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "IsRealmActive", Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::AuracronRealmsBridge_eventIsRealmActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::AuracronRealmsBridge_eventIsRealmActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execIsRealmActive)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsRealmActive(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function IsRealmActive *******************************

// ********** Begin Class UAuracronRealmsBridge Function LoadDefaultRealmConfigurations ************
struct Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics
{
	struct AuracronRealmsBridge_eventLoadDefaultRealmConfigurations_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es padr\xc3\x83\xc2\xa3o dos Realms\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es padr\xc3\x83\xc2\xa3o dos Realms" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventLoadDefaultRealmConfigurations_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventLoadDefaultRealmConfigurations_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "LoadDefaultRealmConfigurations", Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::AuracronRealmsBridge_eventLoadDefaultRealmConfigurations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::AuracronRealmsBridge_eventLoadDefaultRealmConfigurations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execLoadDefaultRealmConfigurations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadDefaultRealmConfigurations();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function LoadDefaultRealmConfigurations **************

// ********** Begin Class UAuracronRealmsBridge Function LoadRealmDataLayers ***********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics
{
	struct AuracronRealmsBridge_eventLoadRealmDataLayers_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar Data Layers de um Realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar Data Layers de um Realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventLoadRealmDataLayers_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventLoadRealmDataLayers_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventLoadRealmDataLayers_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "LoadRealmDataLayers", Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::AuracronRealmsBridge_eventLoadRealmDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::AuracronRealmsBridge_eventLoadRealmDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execLoadRealmDataLayers)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadRealmDataLayers(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function LoadRealmDataLayers *************************

// ********** Begin Class UAuracronRealmsBridge Function OnRep_ActiveRealm *************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_ActiveRealm_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_ActiveRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnRep_ActiveRealm", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_ActiveRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_ActiveRealm_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_ActiveRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_ActiveRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execOnRep_ActiveRealm)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ActiveRealm();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function OnRep_ActiveRealm ***************************

// ********** Begin Class UAuracronRealmsBridge Function OnRep_TransitionState *********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_TransitionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_TransitionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OnRep_TransitionState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_TransitionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_TransitionState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_TransitionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_TransitionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execOnRep_TransitionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_TransitionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function OnRep_TransitionState ***********************

// ********** Begin Class UAuracronRealmsBridge Function OptimizeWorldPartitionStreaming ***********
struct Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics
{
	struct AuracronRealmsBridge_eventOptimizeWorldPartitionStreaming_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Otimizar streaming de World Partition\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar streaming de World Partition" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventOptimizeWorldPartitionStreaming_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventOptimizeWorldPartitionStreaming_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "OptimizeWorldPartitionStreaming", Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::AuracronRealmsBridge_eventOptimizeWorldPartitionStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::AuracronRealmsBridge_eventOptimizeWorldPartitionStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execOptimizeWorldPartitionStreaming)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeWorldPartitionStreaming();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function OptimizeWorldPartitionStreaming *************

// ********** Begin Class UAuracronRealmsBridge Function RegenerateProceduralContent ***************
struct Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics
{
	struct AuracronRealmsBridge_eventRegenerateProceduralContent_Parms
	{
		EAuracronRealmType RealmType;
		bool bForceRegeneration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Regenerar conte\xc3\x83\xc2\xba""do procedural\n     */" },
#endif
		{ "CPP_Default_bForceRegeneration", "false" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenerar conte\xc3\x83\xc2\xba""do procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_bForceRegeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bForceRegeneration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventRegenerateProceduralContent_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_bForceRegeneration_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventRegenerateProceduralContent_Parms*)Obj)->bForceRegeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_bForceRegeneration = { "bForceRegeneration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventRegenerateProceduralContent_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_bForceRegeneration_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventRegenerateProceduralContent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventRegenerateProceduralContent_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_bForceRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "RegenerateProceduralContent", Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::AuracronRealmsBridge_eventRegenerateProceduralContent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::AuracronRealmsBridge_eventRegenerateProceduralContent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execRegenerateProceduralContent)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_GET_UBOOL(Z_Param_bForceRegeneration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegenerateProceduralContent(EAuracronRealmType(Z_Param_RealmType),Z_Param_bForceRegeneration);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function RegenerateProceduralContent *****************

// ********** Begin Class UAuracronRealmsBridge Function RemoveDimensionalPortal *******************
struct Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics
{
	struct AuracronRealmsBridge_eventRemoveDimensionalPortal_Parms
	{
		int32 PortalIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Portals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover portal dimensional\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover portal dimensional" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PortalIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::NewProp_PortalIndex = { "PortalIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventRemoveDimensionalPortal_Parms, PortalIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventRemoveDimensionalPortal_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventRemoveDimensionalPortal_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::NewProp_PortalIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "RemoveDimensionalPortal", Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::AuracronRealmsBridge_eventRemoveDimensionalPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::AuracronRealmsBridge_eventRemoveDimensionalPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execRemoveDimensionalPortal)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PortalIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveDimensionalPortal(Z_Param_PortalIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function RemoveDimensionalPortal *********************

// ********** Begin Class UAuracronRealmsBridge Function SetPortalActive ***************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics
{
	struct AuracronRealmsBridge_eventSetPortalActive_Parms
	{
		int32 PortalIndex;
		bool bActive;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Portals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar/Desativar portal\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar/Desativar portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PortalIndex;
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_PortalIndex = { "PortalIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventSetPortalActive_Parms, PortalIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventSetPortalActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventSetPortalActive_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventSetPortalActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventSetPortalActive_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_PortalIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_bActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "SetPortalActive", Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::AuracronRealmsBridge_eventSetPortalActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::AuracronRealmsBridge_eventSetPortalActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execSetPortalActive)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PortalIndex);
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetPortalActive(Z_Param_PortalIndex,Z_Param_bActive);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function SetPortalActive *****************************

// ********** Begin Class UAuracronRealmsBridge Function SetRealmConfiguration *********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics
{
	struct AuracronRealmsBridge_eventSetRealmConfiguration_Parms
	{
		EAuracronRealmType RealmType;
		FAuracronRealmConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventSetRealmConfiguration_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventSetRealmConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronRealmConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 224665536
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "SetRealmConfiguration", Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::AuracronRealmsBridge_eventSetRealmConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::AuracronRealmsBridge_eventSetRealmConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execSetRealmConfiguration)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_GET_STRUCT_REF(FAuracronRealmConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRealmConfiguration(EAuracronRealmType(Z_Param_RealmType),Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function SetRealmConfiguration ***********************

// ********** Begin Class UAuracronRealmsBridge Function StartMapEvolution *************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics
{
	struct AuracronRealmsBridge_eventStartMapEvolution_Parms
	{
		FAuracronMapEvolution EvolutionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do mapa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EvolutionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::NewProp_EvolutionConfig = { "EvolutionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventStartMapEvolution_Parms, EvolutionConfig), Z_Construct_UScriptStruct_FAuracronMapEvolution, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionConfig_MetaData), NewProp_EvolutionConfig_MetaData) }; // 555533603
void Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventStartMapEvolution_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventStartMapEvolution_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::NewProp_EvolutionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "StartMapEvolution", Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::AuracronRealmsBridge_eventStartMapEvolution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::AuracronRealmsBridge_eventStartMapEvolution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execStartMapEvolution)
{
	P_GET_STRUCT_REF(FAuracronMapEvolution,Z_Param_Out_EvolutionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartMapEvolution(Z_Param_Out_EvolutionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function StartMapEvolution ***************************

// ********** Begin Class UAuracronRealmsBridge Function StartRealmTransition **********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics
{
	struct AuracronRealmsBridge_eventStartRealmTransition_Parms
	{
		EAuracronRealmType FromRealm;
		EAuracronRealmType ToRealm;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre Realms\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre Realms" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FromRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FromRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ToRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ToRealm;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_FromRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_FromRealm = { "FromRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventStartRealmTransition_Parms, FromRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ToRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ToRealm = { "ToRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventStartRealmTransition_Parms, ToRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventStartRealmTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventStartRealmTransition_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_FromRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_FromRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ToRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ToRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "StartRealmTransition", Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::AuracronRealmsBridge_eventStartRealmTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::AuracronRealmsBridge_eventStartRealmTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execStartRealmTransition)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_FromRealm);
	P_GET_ENUM(EAuracronRealmType,Z_Param_ToRealm);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartRealmTransition(EAuracronRealmType(Z_Param_FromRealm),EAuracronRealmType(Z_Param_ToRealm));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function StartRealmTransition ************************

// ********** Begin Class UAuracronRealmsBridge Function StopMapEvolution **************************
struct Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics
{
	struct AuracronRealmsBridge_eventStopMapEvolution_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em andamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em andamento" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventStopMapEvolution_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventStopMapEvolution_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "StopMapEvolution", Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::AuracronRealmsBridge_eventStopMapEvolution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::AuracronRealmsBridge_eventStopMapEvolution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execStopMapEvolution)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopMapEvolution();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function StopMapEvolution ****************************

// ********** Begin Class UAuracronRealmsBridge Function ToggleRealmVisibility *********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics
{
	struct AuracronRealmsBridge_eventToggleRealmVisibility_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Alternar visibilidade de um Realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alternar visibilidade de um Realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventToggleRealmVisibility_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventToggleRealmVisibility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventToggleRealmVisibility_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "ToggleRealmVisibility", Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::AuracronRealmsBridge_eventToggleRealmVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::AuracronRealmsBridge_eventToggleRealmVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execToggleRealmVisibility)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ToggleRealmVisibility(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function ToggleRealmVisibility ***********************

// ********** Begin Class UAuracronRealmsBridge Function UnloadRealmDataLayers *********************
struct Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics
{
	struct AuracronRealmsBridge_eventUnloadRealmDataLayers_Parms
	{
		EAuracronRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Realms|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Descarregar Data Layers de um Realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descarregar Data Layers de um Realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmsBridge_eventUnloadRealmDataLayers_Parms, RealmType), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(0, nullptr) }; // 2486602686
void Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmsBridge_eventUnloadRealmDataLayers_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmsBridge_eventUnloadRealmDataLayers_Parms), &Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmsBridge, nullptr, "UnloadRealmDataLayers", Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::AuracronRealmsBridge_eventUnloadRealmDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::AuracronRealmsBridge_eventUnloadRealmDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmsBridge::execUnloadRealmDataLayers)
{
	P_GET_ENUM(EAuracronRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadRealmDataLayers(EAuracronRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmsBridge Function UnloadRealmDataLayers ***********************

// ********** Begin Class UAuracronRealmsBridge ****************************************************
void UAuracronRealmsBridge::StaticRegisterNativesUAuracronRealmsBridge()
{
	UClass* Class = UAuracronRealmsBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateRealm", &UAuracronRealmsBridge::execActivateRealm },
		{ "CancelRealmTransition", &UAuracronRealmsBridge::execCancelRealmTransition },
		{ "ClearProceduralContent", &UAuracronRealmsBridge::execClearProceduralContent },
		{ "CompleteRealmTransition", &UAuracronRealmsBridge::execCompleteRealmTransition },
		{ "CreateDimensionalPortal", &UAuracronRealmsBridge::execCreateDimensionalPortal },
		{ "DeactivateRealm", &UAuracronRealmsBridge::execDeactivateRealm },
		{ "ExecuteProceduralGeneration", &UAuracronRealmsBridge::execExecuteProceduralGeneration },
		{ "FindNearestPortal", &UAuracronRealmsBridge::execFindNearestPortal },
		{ "GetActivePortals", &UAuracronRealmsBridge::execGetActivePortals },
		{ "GetActiveRealm", &UAuracronRealmsBridge::execGetActiveRealm },
		{ "GetActiveRealms", &UAuracronRealmsBridge::execGetActiveRealms },
		{ "GetEvolutionProgress", &UAuracronRealmsBridge::execGetEvolutionProgress },
		{ "GetProceduralGenerationProgress", &UAuracronRealmsBridge::execGetProceduralGenerationProgress },
		{ "GetRealmConfiguration", &UAuracronRealmsBridge::execGetRealmConfiguration },
		{ "GetTransitionProgress", &UAuracronRealmsBridge::execGetTransitionProgress },
		{ "GetTransitionState", &UAuracronRealmsBridge::execGetTransitionState },
		{ "IsEvolutionActive", &UAuracronRealmsBridge::execIsEvolutionActive },
		{ "IsRealmActive", &UAuracronRealmsBridge::execIsRealmActive },
		{ "LoadDefaultRealmConfigurations", &UAuracronRealmsBridge::execLoadDefaultRealmConfigurations },
		{ "LoadRealmDataLayers", &UAuracronRealmsBridge::execLoadRealmDataLayers },
		{ "OnRep_ActiveRealm", &UAuracronRealmsBridge::execOnRep_ActiveRealm },
		{ "OnRep_TransitionState", &UAuracronRealmsBridge::execOnRep_TransitionState },
		{ "OptimizeWorldPartitionStreaming", &UAuracronRealmsBridge::execOptimizeWorldPartitionStreaming },
		{ "RegenerateProceduralContent", &UAuracronRealmsBridge::execRegenerateProceduralContent },
		{ "RemoveDimensionalPortal", &UAuracronRealmsBridge::execRemoveDimensionalPortal },
		{ "SetPortalActive", &UAuracronRealmsBridge::execSetPortalActive },
		{ "SetRealmConfiguration", &UAuracronRealmsBridge::execSetRealmConfiguration },
		{ "StartMapEvolution", &UAuracronRealmsBridge::execStartMapEvolution },
		{ "StartRealmTransition", &UAuracronRealmsBridge::execStartRealmTransition },
		{ "StopMapEvolution", &UAuracronRealmsBridge::execStopMapEvolution },
		{ "ToggleRealmVisibility", &UAuracronRealmsBridge::execToggleRealmVisibility },
		{ "UnloadRealmDataLayers", &UAuracronRealmsBridge::execUnloadRealmDataLayers },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronRealmsBridge;
UClass* UAuracronRealmsBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronRealmsBridge;
	if (!Z_Registration_Info_UClass_UAuracronRealmsBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronRealmsBridge"),
			Z_Registration_Info_UClass_UAuracronRealmsBridge.InnerSingleton,
			StaticRegisterNativesUAuracronRealmsBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronRealmsBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronRealmsBridge_NoRegister()
{
	return UAuracronRealmsBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronRealmsBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Realms" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Realms Din\xc3\x83\xc2\xa2micos\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento de 3 realms com transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es din\xc3\x83\xc2\xa2micas\n */" },
#endif
		{ "DisplayName", "AURACRON Realms Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronRealmsBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Realms Din\xc3\x83\xc2\xa2micos\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento de 3 realms com transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es din\xc3\x83\xc2\xa2micas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmConfigurations_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es dos Realms dispon\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es dos Realms dispon\xc3\x83\xc2\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DimensionalPortals_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Portais dimensionais ativos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Portais dimensionais ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticalConnectors_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Conectores verticais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conectores verticais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralConfigs_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScheduledEvolutions_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es programadas do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es programadas do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentActiveRealm_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransitionState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionStartTime_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de in\xc3\x83\xc2\xad""cio da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de in\xc3\x83\xc2\xad""cio da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionFromRealm_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm de origem da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm de origem da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionToRealm_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm de destino da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm de destino da transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEvolutionIndex_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atualmente ativa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atualmente ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionStartTime_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de in\xc3\x83\xc2\xad""cio da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de in\xc3\x83\xc2\xad""cio da evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTimeline_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de timeline para transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de timeline para transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionTimeline_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de timeline para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de timeline para evolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao World Partition Subsystem */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao World Partition Subsystem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao Data Layer Subsystem */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao Data Layer Subsystem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao PCG Subsystem */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao PCG Subsystem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRealmActivated_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRealmDeactivated_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTransitionStarted_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTransitionCompleted_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnEvolutionStarted_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnEvolutionCompleted_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPortalCreated_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPortalRemoved_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnProceduralGenerationCompleted_MetaData[] = {
		{ "Category", "AURACRON Realms|Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmConfigurations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmConfigurations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DimensionalPortals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DimensionalPortals;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VerticalConnectors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VerticalConnectors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProceduralConfigs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ProceduralConfigs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScheduledEvolutions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ScheduledEvolutions;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentActiveRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentActiveRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTransitionState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTransitionState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionStartTime;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionFromRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionFromRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionToRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionToRealm;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentEvolutionIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionStartTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionTimeline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EvolutionTimeline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DataLayerSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGSubsystem;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRealmActivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRealmDeactivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTransitionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTransitionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnEvolutionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnEvolutionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPortalCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPortalRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnProceduralGenerationCompleted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_ActivateRealm, "ActivateRealm" }, // 2390755149
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_CancelRealmTransition, "CancelRealmTransition" }, // 882921258
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_ClearProceduralContent, "ClearProceduralContent" }, // 104598171
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_CompleteRealmTransition, "CompleteRealmTransition" }, // 2219847224
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_CreateDimensionalPortal, "CreateDimensionalPortal" }, // 367753193
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_DeactivateRealm, "DeactivateRealm" }, // 1017571743
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_ExecuteProceduralGeneration, "ExecuteProceduralGeneration" }, // 1954366407
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_FindNearestPortal, "FindNearestPortal" }, // 183983398
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetActivePortals, "GetActivePortals" }, // 1521347064
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealm, "GetActiveRealm" }, // 1968437368
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetActiveRealms, "GetActiveRealms" }, // 524932146
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetEvolutionProgress, "GetEvolutionProgress" }, // 4202332242
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetProceduralGenerationProgress, "GetProceduralGenerationProgress" }, // 3127433338
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetRealmConfiguration, "GetRealmConfiguration" }, // 470214954
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionProgress, "GetTransitionProgress" }, // 4166945448
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_GetTransitionState, "GetTransitionState" }, // 1181319847
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_IsEvolutionActive, "IsEvolutionActive" }, // 2930838904
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_IsRealmActive, "IsRealmActive" }, // 542308564
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_LoadDefaultRealmConfigurations, "LoadDefaultRealmConfigurations" }, // 276152558
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_LoadRealmDataLayers, "LoadRealmDataLayers" }, // 921975901
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature, "OnEvolutionCompleted__DelegateSignature" }, // 1178968283
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature, "OnEvolutionStarted__DelegateSignature" }, // 2749681718
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature, "OnPortalCreated__DelegateSignature" }, // 972519848
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature, "OnPortalRemoved__DelegateSignature" }, // 2128603352
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature, "OnProceduralGenerationCompleted__DelegateSignature" }, // 1214975346
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature, "OnRealmActivated__DelegateSignature" }, // 3573756354
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature, "OnRealmDeactivated__DelegateSignature" }, // 3576273615
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_ActiveRealm, "OnRep_ActiveRealm" }, // 1430434115
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_OnRep_TransitionState, "OnRep_TransitionState" }, // 4198628546
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature, "OnTransitionCompleted__DelegateSignature" }, // 2641977522
		{ &Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature, "OnTransitionStarted__DelegateSignature" }, // 3856125538
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_OptimizeWorldPartitionStreaming, "OptimizeWorldPartitionStreaming" }, // 423047391
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_RegenerateProceduralContent, "RegenerateProceduralContent" }, // 1299735821
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_RemoveDimensionalPortal, "RemoveDimensionalPortal" }, // 1868433931
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_SetPortalActive, "SetPortalActive" }, // 132006905
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_SetRealmConfiguration, "SetRealmConfiguration" }, // 2594589915
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_StartMapEvolution, "StartMapEvolution" }, // 1877497963
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_StartRealmTransition, "StartRealmTransition" }, // 2219469778
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_StopMapEvolution, "StopMapEvolution" }, // 1330056343
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_ToggleRealmVisibility, "ToggleRealmVisibility" }, // 4267820240
		{ &Z_Construct_UFunction_UAuracronRealmsBridge_UnloadRealmDataLayers, "UnloadRealmDataLayers" }, // 2288284388
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronRealmsBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_RealmConfigurations_Inner = { "RealmConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry, METADATA_PARAMS(0, nullptr) }; // 1679149013
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_RealmConfigurations = { "RealmConfigurations", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, RealmConfigurations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmConfigurations_MetaData), NewProp_RealmConfigurations_MetaData) }; // 1679149013
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_DimensionalPortals_Inner = { "DimensionalPortals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDimensionalPortal, METADATA_PARAMS(0, nullptr) }; // 4019285765
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_DimensionalPortals = { "DimensionalPortals", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, DimensionalPortals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DimensionalPortals_MetaData), NewProp_DimensionalPortals_MetaData) }; // 4019285765
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_VerticalConnectors_Inner = { "VerticalConnectors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronVerticalConnector, METADATA_PARAMS(0, nullptr) }; // 2531106067
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_VerticalConnectors = { "VerticalConnectors", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, VerticalConnectors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticalConnectors_MetaData), NewProp_VerticalConnectors_MetaData) }; // 2531106067
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ProceduralConfigs_Inner = { "ProceduralConfigs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry, METADATA_PARAMS(0, nullptr) }; // 4233375354
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ProceduralConfigs = { "ProceduralConfigs", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, ProceduralConfigs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralConfigs_MetaData), NewProp_ProceduralConfigs_MetaData) }; // 4233375354
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ScheduledEvolutions_Inner = { "ScheduledEvolutions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronMapEvolution, METADATA_PARAMS(0, nullptr) }; // 555533603
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ScheduledEvolutions = { "ScheduledEvolutions", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, ScheduledEvolutions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScheduledEvolutions_MetaData), NewProp_ScheduledEvolutions_MetaData) }; // 555533603
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentActiveRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentActiveRealm = { "CurrentActiveRealm", "OnRep_ActiveRealm", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, CurrentActiveRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentActiveRealm_MetaData), NewProp_CurrentActiveRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentTransitionState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentTransitionState = { "CurrentTransitionState", "OnRep_TransitionState", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, CurrentTransitionState), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmTransitionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransitionState_MetaData), NewProp_CurrentTransitionState_MetaData) }; // 1102994998
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionStartTime = { "TransitionStartTime", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, TransitionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionStartTime_MetaData), NewProp_TransitionStartTime_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionFromRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionFromRealm = { "TransitionFromRealm", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, TransitionFromRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionFromRealm_MetaData), NewProp_TransitionFromRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionToRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionToRealm = { "TransitionToRealm", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, TransitionToRealm), Z_Construct_UEnum_AuracronRealmsBridge_EAuracronRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionToRealm_MetaData), NewProp_TransitionToRealm_MetaData) }; // 2486602686
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentEvolutionIndex = { "CurrentEvolutionIndex", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, CurrentEvolutionIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEvolutionIndex_MetaData), NewProp_CurrentEvolutionIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_EvolutionStartTime = { "EvolutionStartTime", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, EvolutionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionStartTime_MetaData), NewProp_EvolutionStartTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionTimeline = { "TransitionTimeline", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, TransitionTimeline), Z_Construct_UClass_UTimelineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTimeline_MetaData), NewProp_TransitionTimeline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_EvolutionTimeline = { "EvolutionTimeline", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, EvolutionTimeline), Z_Construct_UClass_UTimelineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionTimeline_MetaData), NewProp_EvolutionTimeline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_WorldPartitionSubsystem = { "WorldPartitionSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, WorldPartitionSubsystem), Z_Construct_UClass_UWorldPartitionSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionSubsystem_MetaData), NewProp_WorldPartitionSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_DataLayerSubsystem = { "DataLayerSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, DataLayerSubsystem), Z_Construct_UClass_UDataLayerSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerSubsystem_MetaData), NewProp_DataLayerSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_PCGSubsystem = { "PCGSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, PCGSubsystem), Z_Construct_UClass_UPCGSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGSubsystem_MetaData), NewProp_PCGSubsystem_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnRealmActivated = { "OnRealmActivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnRealmActivated), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmActivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRealmActivated_MetaData), NewProp_OnRealmActivated_MetaData) }; // 3573756354
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnRealmDeactivated = { "OnRealmDeactivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnRealmDeactivated), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnRealmDeactivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRealmDeactivated_MetaData), NewProp_OnRealmDeactivated_MetaData) }; // 3576273615
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnTransitionStarted = { "OnTransitionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnTransitionStarted), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTransitionStarted_MetaData), NewProp_OnTransitionStarted_MetaData) }; // 3856125538
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnTransitionCompleted = { "OnTransitionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnTransitionCompleted), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnTransitionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTransitionCompleted_MetaData), NewProp_OnTransitionCompleted_MetaData) }; // 2641977522
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnEvolutionStarted = { "OnEvolutionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnEvolutionStarted), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnEvolutionStarted_MetaData), NewProp_OnEvolutionStarted_MetaData) }; // 2749681718
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnEvolutionCompleted = { "OnEvolutionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnEvolutionCompleted), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnEvolutionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnEvolutionCompleted_MetaData), NewProp_OnEvolutionCompleted_MetaData) }; // 1178968283
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnPortalCreated = { "OnPortalCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnPortalCreated), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPortalCreated_MetaData), NewProp_OnPortalCreated_MetaData) }; // 972519848
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnPortalRemoved = { "OnPortalRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnPortalRemoved), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnPortalRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPortalRemoved_MetaData), NewProp_OnPortalRemoved_MetaData) }; // 2128603352
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnProceduralGenerationCompleted = { "OnProceduralGenerationCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmsBridge, OnProceduralGenerationCompleted), Z_Construct_UDelegateFunction_UAuracronRealmsBridge_OnProceduralGenerationCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnProceduralGenerationCompleted_MetaData), NewProp_OnProceduralGenerationCompleted_MetaData) }; // 1214975346
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronRealmsBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_RealmConfigurations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_RealmConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_DimensionalPortals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_DimensionalPortals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_VerticalConnectors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_VerticalConnectors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ProceduralConfigs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ProceduralConfigs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ScheduledEvolutions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_ScheduledEvolutions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentActiveRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentActiveRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentTransitionState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentTransitionState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionFromRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionFromRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionToRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionToRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_CurrentEvolutionIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_EvolutionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_TransitionTimeline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_EvolutionTimeline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_WorldPartitionSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_DataLayerSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_PCGSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnRealmActivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnRealmDeactivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnTransitionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnTransitionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnEvolutionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnEvolutionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnPortalCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnPortalRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmsBridge_Statics::NewProp_OnProceduralGenerationCompleted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmsBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronRealmsBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronRealmsBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmsBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronRealmsBridge_Statics::ClassParams = {
	&UAuracronRealmsBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronRealmsBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmsBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmsBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronRealmsBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronRealmsBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronRealmsBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronRealmsBridge.OuterSingleton, Z_Construct_UClass_UAuracronRealmsBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronRealmsBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronRealmsBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_RealmConfigurations(TEXT("RealmConfigurations"));
	static FName Name_DimensionalPortals(TEXT("DimensionalPortals"));
	static FName Name_VerticalConnectors(TEXT("VerticalConnectors"));
	static FName Name_ProceduralConfigs(TEXT("ProceduralConfigs"));
	static FName Name_ScheduledEvolutions(TEXT("ScheduledEvolutions"));
	static FName Name_CurrentActiveRealm(TEXT("CurrentActiveRealm"));
	static FName Name_CurrentTransitionState(TEXT("CurrentTransitionState"));
	static FName Name_TransitionStartTime(TEXT("TransitionStartTime"));
	static FName Name_TransitionFromRealm(TEXT("TransitionFromRealm"));
	static FName Name_TransitionToRealm(TEXT("TransitionToRealm"));
	static FName Name_CurrentEvolutionIndex(TEXT("CurrentEvolutionIndex"));
	static FName Name_EvolutionStartTime(TEXT("EvolutionStartTime"));
	const bool bIsValid = true
		&& Name_RealmConfigurations == ClassReps[(int32)ENetFields_Private::RealmConfigurations].Property->GetFName()
		&& Name_DimensionalPortals == ClassReps[(int32)ENetFields_Private::DimensionalPortals].Property->GetFName()
		&& Name_VerticalConnectors == ClassReps[(int32)ENetFields_Private::VerticalConnectors].Property->GetFName()
		&& Name_ProceduralConfigs == ClassReps[(int32)ENetFields_Private::ProceduralConfigs].Property->GetFName()
		&& Name_ScheduledEvolutions == ClassReps[(int32)ENetFields_Private::ScheduledEvolutions].Property->GetFName()
		&& Name_CurrentActiveRealm == ClassReps[(int32)ENetFields_Private::CurrentActiveRealm].Property->GetFName()
		&& Name_CurrentTransitionState == ClassReps[(int32)ENetFields_Private::CurrentTransitionState].Property->GetFName()
		&& Name_TransitionStartTime == ClassReps[(int32)ENetFields_Private::TransitionStartTime].Property->GetFName()
		&& Name_TransitionFromRealm == ClassReps[(int32)ENetFields_Private::TransitionFromRealm].Property->GetFName()
		&& Name_TransitionToRealm == ClassReps[(int32)ENetFields_Private::TransitionToRealm].Property->GetFName()
		&& Name_CurrentEvolutionIndex == ClassReps[(int32)ENetFields_Private::CurrentEvolutionIndex].Property->GetFName()
		&& Name_EvolutionStartTime == ClassReps[(int32)ENetFields_Private::EvolutionStartTime].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronRealmsBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronRealmsBridge);
UAuracronRealmsBridge::~UAuracronRealmsBridge() {}
// ********** End Class UAuracronRealmsBridge ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronRealmType_StaticEnum, TEXT("EAuracronRealmType"), &Z_Registration_Info_UEnum_EAuracronRealmType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2486602686U) },
		{ EAuracronRealmTransitionState_StaticEnum, TEXT("EAuracronRealmTransitionState"), &Z_Registration_Info_UEnum_EAuracronRealmTransitionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1102994998U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronRealmConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics::NewStructOps, TEXT("AuracronRealmConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronRealmConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmConfiguration), 224665536U) },
		{ FAuracronDimensionalPortal::StaticStruct, Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics::NewStructOps, TEXT("AuracronDimensionalPortal"), &Z_Registration_Info_UScriptStruct_FAuracronDimensionalPortal, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDimensionalPortal), 4019285765U) },
		{ FAuracronMapEvolution::StaticStruct, Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics::NewStructOps, TEXT("AuracronMapEvolution"), &Z_Registration_Info_UScriptStruct_FAuracronMapEvolution, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMapEvolution), 555533603U) },
		{ FAuracronVerticalConnector::StaticStruct, Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics::NewStructOps, TEXT("AuracronVerticalConnector"), &Z_Registration_Info_UScriptStruct_FAuracronVerticalConnector, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronVerticalConnector), 2531106067U) },
		{ FAuracronProceduralGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics::NewStructOps, TEXT("AuracronProceduralGenerationConfig"), &Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronProceduralGenerationConfig), 1617504779U) },
		{ FAuracronRealmConfigurationEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics::NewStructOps, TEXT("AuracronRealmConfigurationEntry"), &Z_Registration_Info_UScriptStruct_FAuracronRealmConfigurationEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmConfigurationEntry), 1679149013U) },
		{ FAuracronProceduralGenerationConfigEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics::NewStructOps, TEXT("AuracronProceduralGenerationConfigEntry"), &Z_Registration_Info_UScriptStruct_FAuracronProceduralGenerationConfigEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronProceduralGenerationConfigEntry), 4233375354U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronRealmsBridge, UAuracronRealmsBridge::StaticClass, TEXT("UAuracronRealmsBridge"), &Z_Registration_Info_UClass_UAuracronRealmsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronRealmsBridge), 4183621181U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_366254800(TEXT("/Script/AuracronRealmsBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h__Script_AuracronRealmsBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
