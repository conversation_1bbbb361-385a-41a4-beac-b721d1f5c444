// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronCombatBridge.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronCombatBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge();
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDamageConfiguration();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingConfiguration();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingResult();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundCue_NoRegister();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_ECollisionChannel();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
FIELDSYSTEMENGINE_API UClass* Z_Construct_UClass_UFieldSystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
METASOUNDENGINE_API UClass* Z_Construct_UClass_UMetaSoundSource_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronCombatBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronDamageType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDamageType;
static UEnum* EAuracronDamageType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDamageType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDamageType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronDamageType"));
	}
	return Z_Registration_Info_UEnum_EAuracronDamageType.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronDamageType>()
{
	return EAuracronDamageType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Area.DisplayName", "Area of Effect" },
		{ "Area.Name", "EAuracronDamageType::Area" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de dano\n */" },
#endif
		{ "Healing.DisplayName", "Healing" },
		{ "Healing.Name", "EAuracronDamageType::Healing" },
		{ "Magical.DisplayName", "Magical" },
		{ "Magical.Name", "EAuracronDamageType::Magical" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronDamageType::None" },
		{ "OverTime.DisplayName", "Over Time" },
		{ "OverTime.Name", "EAuracronDamageType::OverTime" },
		{ "Percentage.DisplayName", "Percentage" },
		{ "Percentage.Name", "EAuracronDamageType::Percentage" },
		{ "Physical.DisplayName", "Physical" },
		{ "Physical.Name", "EAuracronDamageType::Physical" },
		{ "Shield.DisplayName", "Shield" },
		{ "Shield.Name", "EAuracronDamageType::Shield" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de dano" },
#endif
		{ "TrueDamage.DisplayName", "True Damage" },
		{ "TrueDamage.Name", "EAuracronDamageType::TrueDamage" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDamageType::None", (int64)EAuracronDamageType::None },
		{ "EAuracronDamageType::Physical", (int64)EAuracronDamageType::Physical },
		{ "EAuracronDamageType::Magical", (int64)EAuracronDamageType::Magical },
		{ "EAuracronDamageType::TrueDamage", (int64)EAuracronDamageType::TrueDamage },
		{ "EAuracronDamageType::Healing", (int64)EAuracronDamageType::Healing },
		{ "EAuracronDamageType::Shield", (int64)EAuracronDamageType::Shield },
		{ "EAuracronDamageType::Percentage", (int64)EAuracronDamageType::Percentage },
		{ "EAuracronDamageType::OverTime", (int64)EAuracronDamageType::OverTime },
		{ "EAuracronDamageType::Area", (int64)EAuracronDamageType::Area },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronDamageType",
	"EAuracronDamageType",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType()
{
	if (!Z_Registration_Info_UEnum_EAuracronDamageType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDamageType.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDamageType.InnerSingleton;
}
// ********** End Enum EAuracronDamageType *********************************************************

// ********** Begin Enum EAuracronCombatLayer ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCombatLayer;
static UEnum* EAuracronCombatLayer_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCombatLayer.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCombatLayer.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronCombatLayer"));
	}
	return Z_Registration_Info_UEnum_EAuracronCombatLayer.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronCombatLayer>()
{
	return EAuracronCombatLayer_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "All.DisplayName", "All Layers" },
		{ "All.Name", "EAuracronCombatLayer::All" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para camadas de combate 3D\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "Sky.DisplayName", "Sky Layer" },
		{ "Sky.Name", "EAuracronCombatLayer::Sky" },
		{ "Surface.DisplayName", "Surface Layer" },
		{ "Surface.Name", "EAuracronCombatLayer::Surface" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para camadas de combate 3D" },
#endif
		{ "Underground.DisplayName", "Underground Layer" },
		{ "Underground.Name", "EAuracronCombatLayer::Underground" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCombatLayer::Surface", (int64)EAuracronCombatLayer::Surface },
		{ "EAuracronCombatLayer::Sky", (int64)EAuracronCombatLayer::Sky },
		{ "EAuracronCombatLayer::Underground", (int64)EAuracronCombatLayer::Underground },
		{ "EAuracronCombatLayer::All", (int64)EAuracronCombatLayer::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronCombatLayer",
	"EAuracronCombatLayer",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer()
{
	if (!Z_Registration_Info_UEnum_EAuracronCombatLayer.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCombatLayer.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCombatLayer.InnerSingleton;
}
// ********** End Enum EAuracronCombatLayer ********************************************************

// ********** Begin Enum EAuracronTargetingType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTargetingType;
static UEnum* EAuracronTargetingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTargetingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTargetingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronTargetingType"));
	}
	return Z_Registration_Info_UEnum_EAuracronTargetingType.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronTargetingType>()
{
	return EAuracronTargetingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AreaOfEffect.DisplayName", "Area of Effect" },
		{ "AreaOfEffect.Name", "EAuracronTargetingType::AreaOfEffect" },
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EAuracronTargetingType::Box" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de targeting\n */" },
#endif
		{ "Cone.DisplayName", "Cone" },
		{ "Cone.Name", "EAuracronTargetingType::Cone" },
		{ "CrossLayer.DisplayName", "Cross Layer" },
		{ "CrossLayer.Name", "EAuracronTargetingType::CrossLayer" },
		{ "Cylinder.DisplayName", "Cylinder" },
		{ "Cylinder.Name", "EAuracronTargetingType::Cylinder" },
		{ "GroundTarget.DisplayName", "Ground Target" },
		{ "GroundTarget.Name", "EAuracronTargetingType::GroundTarget" },
		{ "LineTrace.DisplayName", "Line Trace" },
		{ "LineTrace.Name", "EAuracronTargetingType::LineTrace" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronTargetingType::None" },
		{ "SingleTarget.DisplayName", "Single Target" },
		{ "SingleTarget.Name", "EAuracronTargetingType::SingleTarget" },
		{ "SkyTarget.DisplayName", "Sky Target" },
		{ "SkyTarget.Name", "EAuracronTargetingType::SkyTarget" },
		{ "Sphere.DisplayName", "Sphere" },
		{ "Sphere.Name", "EAuracronTargetingType::Sphere" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de targeting" },
#endif
		{ "UndergroundTarget.DisplayName", "Underground Target" },
		{ "UndergroundTarget.Name", "EAuracronTargetingType::UndergroundTarget" },
		{ "VerticalColumn.DisplayName", "Vertical Column" },
		{ "VerticalColumn.Name", "EAuracronTargetingType::VerticalColumn" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTargetingType::None", (int64)EAuracronTargetingType::None },
		{ "EAuracronTargetingType::SingleTarget", (int64)EAuracronTargetingType::SingleTarget },
		{ "EAuracronTargetingType::LineTrace", (int64)EAuracronTargetingType::LineTrace },
		{ "EAuracronTargetingType::AreaOfEffect", (int64)EAuracronTargetingType::AreaOfEffect },
		{ "EAuracronTargetingType::Cone", (int64)EAuracronTargetingType::Cone },
		{ "EAuracronTargetingType::Sphere", (int64)EAuracronTargetingType::Sphere },
		{ "EAuracronTargetingType::Box", (int64)EAuracronTargetingType::Box },
		{ "EAuracronTargetingType::Cylinder", (int64)EAuracronTargetingType::Cylinder },
		{ "EAuracronTargetingType::VerticalColumn", (int64)EAuracronTargetingType::VerticalColumn },
		{ "EAuracronTargetingType::CrossLayer", (int64)EAuracronTargetingType::CrossLayer },
		{ "EAuracronTargetingType::GroundTarget", (int64)EAuracronTargetingType::GroundTarget },
		{ "EAuracronTargetingType::SkyTarget", (int64)EAuracronTargetingType::SkyTarget },
		{ "EAuracronTargetingType::UndergroundTarget", (int64)EAuracronTargetingType::UndergroundTarget },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronTargetingType",
	"EAuracronTargetingType",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType()
{
	if (!Z_Registration_Info_UEnum_EAuracronTargetingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTargetingType.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTargetingType.InnerSingleton;
}
// ********** End Enum EAuracronTargetingType ******************************************************

// ********** Begin ScriptStruct FAuracronDamageConfiguration **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration;
class UScriptStruct* FAuracronDamageConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDamageConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronDamageConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dano\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageType_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de dano */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDamage_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor base do dano */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor base do dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamageScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com Attack Damage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com Attack Damage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityPowerScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com Ability Power */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com Ability Power" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealthScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com HP m\xc3\x83\xc2\xa1ximo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com HP m\xc3\x83\xc2\xa1ximo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealthScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com HP atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com HP atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanCrit_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode causar cr\xc3\x83\xc2\xadtico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode causar cr\xc3\x83\xc2\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoresArmor_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ignora armadura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ignora armadura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoresMagicResistance_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ignora resist\xc3\x83\xc2\xaancia m\xc3\x83\xc2\xa1gica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ignora resist\xc3\x83\xc2\xaancia m\xc3\x83\xc2\xa1gica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPenetratesShields_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Penetra shields */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Penetra shields" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDamageOverTime_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dano ao longo do tempo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dano ao longo do tempo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DotDuration_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do DoT (se aplic\xc3\x83\xc2\xa1vel) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do DoT (se aplic\xc3\x83\xc2\xa1vel)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DotInterval_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo do DoT (se aplic\xc3\x83\xc2\xa1vel) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo do DoT (se aplic\xc3\x83\xc2\xa1vel)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedLayers_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camadas afetadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camadas afetadas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DamageType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DamageType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDamageScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityPowerScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealthScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealthScaling;
	static void NewProp_bCanCrit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanCrit;
	static void NewProp_bIgnoresArmor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoresArmor;
	static void NewProp_bIgnoresMagicResistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoresMagicResistance;
	static void NewProp_bPenetratesShields_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPenetratesShields;
	static void NewProp_bDamageOverTime_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDamageOverTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DotDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DotInterval;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AffectedLayers_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AffectedLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AffectedLayers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDamageConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType = { "DamageType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, DamageType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageType_MetaData), NewProp_DamageType_MetaData) }; // 2470854689
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_BaseDamage = { "BaseDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, BaseDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDamage_MetaData), NewProp_BaseDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AttackDamageScaling = { "AttackDamageScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, AttackDamageScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamageScaling_MetaData), NewProp_AttackDamageScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AbilityPowerScaling = { "AbilityPowerScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, AbilityPowerScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityPowerScaling_MetaData), NewProp_AbilityPowerScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_MaxHealthScaling = { "MaxHealthScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, MaxHealthScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealthScaling_MetaData), NewProp_MaxHealthScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_CurrentHealthScaling = { "CurrentHealthScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, CurrentHealthScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealthScaling_MetaData), NewProp_CurrentHealthScaling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bCanCrit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit = { "bCanCrit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanCrit_MetaData), NewProp_bCanCrit_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bIgnoresArmor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor = { "bIgnoresArmor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoresArmor_MetaData), NewProp_bIgnoresArmor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bIgnoresMagicResistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance = { "bIgnoresMagicResistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoresMagicResistance_MetaData), NewProp_bIgnoresMagicResistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bPenetratesShields = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields = { "bPenetratesShields", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPenetratesShields_MetaData), NewProp_bPenetratesShields_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bDamageOverTime = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime = { "bDamageOverTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDamageOverTime_MetaData), NewProp_bDamageOverTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotDuration = { "DotDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, DotDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DotDuration_MetaData), NewProp_DotDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotInterval = { "DotInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, DotInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DotInterval_MetaData), NewProp_DotInterval_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner = { "AffectedLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers = { "AffectedLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, AffectedLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedLayers_MetaData), NewProp_AffectedLayers_MetaData) }; // 3955355270
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_BaseDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AttackDamageScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AbilityPowerScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_MaxHealthScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_CurrentHealthScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronDamageConfiguration",
	Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers),
	sizeof(FAuracronDamageConfiguration),
	alignof(FAuracronDamageConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDamageConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDamageConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronTargetingConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration;
class UScriptStruct* FAuracronTargetingConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronTargetingConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de targeting 3D\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de targeting 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingType_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRange_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alcance m\xc3\x83\xc2\xa1ximo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alcance m\xc3\x83\xc2\xa1ximo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinRange_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alcance m\xc3\x83\xc2\xadnimo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alcance m\xc3\x83\xc2\xadnimo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaOfEffectRadius_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da \xc3\x83\xc2\xa1rea de efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da \xc3\x83\xc2\xa1rea de efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaOfEffectHeight_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura da \xc3\x83\xc2\xa1rea de efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura da \xc3\x83\xc2\xa1rea de efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConeAngle_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "360.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\x9angulo do cone (para targeting c\xc3\x83\xc2\xb4nico) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\x9angulo do cone (para targeting c\xc3\x83\xc2\xb4nico)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowCrossLayerTargeting_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite targeting atrav\xc3\x83\xc2\xa9s de camadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite targeting atrav\xc3\x83\xc2\xa9s de camadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresLineOfSight_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer line of sight */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer line of sight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoresObstacles_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ignora obst\xc3\x83\xc2\xa1""culos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ignora obst\xc3\x83\xc2\xa1""culos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTargetAlliesOnly_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Targeting apenas aliados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Targeting apenas aliados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTargetEnemiesOnly_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Targeting apenas inimigos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Targeting apenas inimigos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeSelf_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inclui o pr\xc3\x83\xc2\xb3prio caster */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inclui o pr\xc3\x83\xc2\xb3prio caster" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedTargetingLayers_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camadas de targeting permitidas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camadas de targeting permitidas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineOfSightChannels_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canais de colis\xc3\x83\xc2\xa3o para line of sight */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canais de colis\xc3\x83\xc2\xa3o para line of sight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingChannels_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canais de colis\xc3\x83\xc2\xa3o para targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canais de colis\xc3\x83\xc2\xa3o para targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticalOffset_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "-1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Offset vertical para targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Offset vertical para targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingAccuracy_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Precis\xc3\x83\xc2\xa3o do targeting (0.0 = perfeito, 1.0 = muito impreciso) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Precis\xc3\x83\xc2\xa3o do targeting (0.0 = perfeito, 1.0 = muito impreciso)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetingType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaOfEffectRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaOfEffectHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConeAngle;
	static void NewProp_bAllowCrossLayerTargeting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowCrossLayerTargeting;
	static void NewProp_bRequiresLineOfSight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresLineOfSight;
	static void NewProp_bIgnoresObstacles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoresObstacles;
	static void NewProp_bTargetAlliesOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTargetAlliesOnly;
	static void NewProp_bTargetEnemiesOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTargetEnemiesOnly;
	static void NewProp_bIncludeSelf_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeSelf;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AllowedTargetingLayers_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AllowedTargetingLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedTargetingLayers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LineOfSightChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LineOfSightChannels;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetingChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetingChannels;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VerticalOffset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetingAccuracy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTargetingConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType = { "TargetingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, TargetingType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingType_MetaData), NewProp_TargetingType_MetaData) }; // 1075389459
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MaxRange = { "MaxRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, MaxRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRange_MetaData), NewProp_MaxRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MinRange = { "MinRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, MinRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinRange_MetaData), NewProp_MinRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectRadius = { "AreaOfEffectRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, AreaOfEffectRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaOfEffectRadius_MetaData), NewProp_AreaOfEffectRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectHeight = { "AreaOfEffectHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, AreaOfEffectHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaOfEffectHeight_MetaData), NewProp_AreaOfEffectHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_ConeAngle = { "ConeAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, ConeAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConeAngle_MetaData), NewProp_ConeAngle_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bAllowCrossLayerTargeting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting = { "bAllowCrossLayerTargeting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowCrossLayerTargeting_MetaData), NewProp_bAllowCrossLayerTargeting_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bRequiresLineOfSight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight = { "bRequiresLineOfSight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresLineOfSight_MetaData), NewProp_bRequiresLineOfSight_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bIgnoresObstacles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles = { "bIgnoresObstacles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoresObstacles_MetaData), NewProp_bIgnoresObstacles_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bTargetAlliesOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly = { "bTargetAlliesOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTargetAlliesOnly_MetaData), NewProp_bTargetAlliesOnly_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bTargetEnemiesOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly = { "bTargetEnemiesOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTargetEnemiesOnly_MetaData), NewProp_bTargetEnemiesOnly_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bIncludeSelf = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf = { "bIncludeSelf", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeSelf_MetaData), NewProp_bIncludeSelf_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner = { "AllowedTargetingLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers = { "AllowedTargetingLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, AllowedTargetingLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedTargetingLayers_MetaData), NewProp_AllowedTargetingLayers_MetaData) }; // 3955355270
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels_Inner = { "LineOfSightChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(0, nullptr) }; // 756624936
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels = { "LineOfSightChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, LineOfSightChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineOfSightChannels_MetaData), NewProp_LineOfSightChannels_MetaData) }; // 756624936
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels_Inner = { "TargetingChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(0, nullptr) }; // 756624936
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels = { "TargetingChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, TargetingChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingChannels_MetaData), NewProp_TargetingChannels_MetaData) }; // 756624936
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_VerticalOffset = { "VerticalOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, VerticalOffset), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticalOffset_MetaData), NewProp_VerticalOffset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingAccuracy = { "TargetingAccuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, TargetingAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingAccuracy_MetaData), NewProp_TargetingAccuracy_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MaxRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MinRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_ConeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_VerticalOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingAccuracy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronTargetingConfiguration",
	Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers),
	sizeof(FAuracronTargetingConfiguration),
	alignof(FAuracronTargetingConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTargetingConfiguration *************************************

// ********** Begin ScriptStruct FAuracronTargetingResult ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTargetingResult;
class UScriptStruct* FAuracronTargetingResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTargetingResult, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronTargetingResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para resultado de targeting\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para resultado de targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccessful_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Targeting foi bem-sucedido */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Targeting foi bem-sucedido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActors_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atores alvo encontrados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atores alvo encontrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocations_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es alvo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es alvo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitResults_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Informa\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de hit */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de hit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLayer_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camada onde o targeting ocorreu */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camada onde o targeting ocorreu" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceToTarget_MetaData[] = {
		{ "Category", "Targeting Result" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia at\xc3\x83\xc2\xa9 o alvo principal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia at\xc3\x83\xc2\xa9 o alvo principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngleToTarget_MetaData[] = {
		{ "Category", "Targeting Result" },
		{ "ClampMax", "360.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\x9angulo at\xc3\x83\xc2\xa9 o alvo principal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\x9angulo at\xc3\x83\xc2\xa9 o alvo principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasLineOfSight_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tem line of sight para o alvo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tem line of sight para o alvo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccessful_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccessful;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetLocations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HitResults_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HitResults;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToTarget;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngleToTarget;
	static void NewProp_bHasLineOfSight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasLineOfSight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTargetingResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful_SetBit(void* Obj)
{
	((FAuracronTargetingResult*)Obj)->bSuccessful = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful = { "bSuccessful", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingResult), &Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccessful_MetaData), NewProp_bSuccessful_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors_Inner = { "TargetActors", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors = { "TargetActors", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, TargetActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActors_MetaData), NewProp_TargetActors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations_Inner = { "TargetLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations = { "TargetLocations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, TargetLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocations_MetaData), NewProp_TargetLocations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults_Inner = { "HitResults", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(0, nullptr) }; // 267591329
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults = { "HitResults", nullptr, (EPropertyFlags)0x0010008000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, HitResults), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitResults_MetaData), NewProp_HitResults_MetaData) }; // 267591329
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, TargetLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLayer_MetaData), NewProp_TargetLayer_MetaData) }; // 3955355270
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_DistanceToTarget = { "DistanceToTarget", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, DistanceToTarget), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceToTarget_MetaData), NewProp_DistanceToTarget_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_AngleToTarget = { "AngleToTarget", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, AngleToTarget), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngleToTarget_MetaData), NewProp_AngleToTarget_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight_SetBit(void* Obj)
{
	((FAuracronTargetingResult*)Obj)->bHasLineOfSight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight = { "bHasLineOfSight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingResult), &Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasLineOfSight_MetaData), NewProp_bHasLineOfSight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_DistanceToTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_AngleToTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronTargetingResult",
	Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers),
	sizeof(FAuracronTargetingResult),
	alignof(FAuracronTargetingResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTargetingResult ********************************************

// ********** Begin ScriptStruct FAuracronCombatEffectsConfiguration *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration;
class UScriptStruct* FAuracronCombatEffectsConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronCombatEffectsConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de combate\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de impacto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de impacto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectileEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de proj\xc3\x83\xc2\xa9til */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de proj\xc3\x83\xc2\xa9til" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de \xc3\x83\xc2\xa1rea */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de \xc3\x83\xc2\xa1rea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de cura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de shield */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de shield" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactSound_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de impacto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de impacto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectileSound_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de proj\xc3\x83\xc2\xa9til */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de proj\xc3\x83\xc2\xa9til" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySound_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectDuration_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o dos efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o dos efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectScale_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala dos efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala dos efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectColor_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dos efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dos efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFieldSystemDestruction_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldSystemForce_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\x83\xc2\xa7""a do Field System */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\x83\xc2\xa7""a do Field System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldSystemRadius_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do Field System */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do Field System" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpactEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ProjectileEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AreaEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_HealingEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ShieldEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpactSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ProjectileSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilitySound;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectColor;
	static void NewProp_bUseFieldSystemDestruction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFieldSystemDestruction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FieldSystemForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FieldSystemRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCombatEffectsConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactEffect = { "ImpactEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ImpactEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactEffect_MetaData), NewProp_ImpactEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileEffect = { "ProjectileEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ProjectileEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectileEffect_MetaData), NewProp_ProjectileEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AreaEffect = { "AreaEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, AreaEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaEffect_MetaData), NewProp_AreaEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_HealingEffect = { "HealingEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, HealingEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingEffect_MetaData), NewProp_HealingEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ShieldEffect = { "ShieldEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ShieldEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldEffect_MetaData), NewProp_ShieldEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactSound = { "ImpactSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ImpactSound), Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactSound_MetaData), NewProp_ImpactSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileSound = { "ProjectileSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ProjectileSound), Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectileSound_MetaData), NewProp_ProjectileSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AbilitySound = { "AbilitySound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, AbilitySound), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySound_MetaData), NewProp_AbilitySound_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectDuration = { "EffectDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, EffectDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectDuration_MetaData), NewProp_EffectDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectScale = { "EffectScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, EffectScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectScale_MetaData), NewProp_EffectScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectColor = { "EffectColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, EffectColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectColor_MetaData), NewProp_EffectColor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction_SetBit(void* Obj)
{
	((FAuracronCombatEffectsConfiguration*)Obj)->bUseFieldSystemDestruction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction = { "bUseFieldSystemDestruction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatEffectsConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFieldSystemDestruction_MetaData), NewProp_bUseFieldSystemDestruction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemForce = { "FieldSystemForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, FieldSystemForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldSystemForce_MetaData), NewProp_FieldSystemForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemRadius = { "FieldSystemRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, FieldSystemRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldSystemRadius_MetaData), NewProp_FieldSystemRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AreaEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_HealingEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ShieldEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AbilitySound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronCombatEffectsConfiguration",
	Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers),
	sizeof(FAuracronCombatEffectsConfiguration),
	alignof(FAuracronCombatEffectsConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCombatEffectsConfiguration *********************************

// ********** Begin Delegate FOnTargetingExecuted **************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnTargetingExecuted_Parms
	{
		FAuracronTargetingResult Result;
		FAuracronTargetingConfiguration Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando targeting \xc3\x83\xc2\xa9 executado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando targeting \xc3\x83\xc2\xa9 executado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010008000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnTargetingExecuted_Parms, Result), Z_Construct_UScriptStruct_FAuracronTargetingResult, METADATA_PARAMS(0, nullptr) }; // 282061631
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnTargetingExecuted_Parms, Config), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(0, nullptr) }; // 1686714939
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnTargetingExecuted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::AuracronCombatBridge_eventOnTargetingExecuted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::AuracronCombatBridge_eventOnTargetingExecuted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnTargetingExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnTargetingExecuted, FAuracronTargetingResult Result, FAuracronTargetingConfiguration Config)
{
	struct AuracronCombatBridge_eventOnTargetingExecuted_Parms
	{
		FAuracronTargetingResult Result;
		FAuracronTargetingConfiguration Config;
	};
	AuracronCombatBridge_eventOnTargetingExecuted_Parms Parms;
	Parms.Result=Result;
	Parms.Config=Config;
	OnTargetingExecuted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTargetingExecuted ****************************************************

// ********** Begin Delegate FOnDamageApplied ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnDamageApplied_Parms
	{
		AActor* TargetActor;
		float DamageAmount;
		EAuracronDamageType DamageType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando dano \xc3\x83\xc2\xa9 aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando dano \xc3\x83\xc2\xa9 aplicado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DamageType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DamageType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnDamageApplied_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnDamageApplied_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType = { "DamageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnDamageApplied_Parms, DamageType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType, METADATA_PARAMS(0, nullptr) }; // 2470854689
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnDamageApplied__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::AuracronCombatBridge_eventOnDamageApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::AuracronCombatBridge_eventOnDamageApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnDamageApplied_DelegateWrapper(const FMulticastScriptDelegate& OnDamageApplied, AActor* TargetActor, float DamageAmount, EAuracronDamageType DamageType)
{
	struct AuracronCombatBridge_eventOnDamageApplied_Parms
	{
		AActor* TargetActor;
		float DamageAmount;
		EAuracronDamageType DamageType;
	};
	AuracronCombatBridge_eventOnDamageApplied_Parms Parms;
	Parms.TargetActor=TargetActor;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageType=DamageType;
	OnDamageApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDamageApplied ********************************************************

// ********** Begin Delegate FOnCombatLayerChanged *************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnCombatLayerChanged_Parms
	{
		EAuracronCombatLayer OldLayer;
		EAuracronCombatLayer NewLayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando camada de combate muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando camada de combate muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer = { "OldLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatLayerChanged_Parms, OldLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatLayerChanged_Parms, NewLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnCombatLayerChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatLayerChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatLayerChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnCombatLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCombatLayerChanged, EAuracronCombatLayer OldLayer, EAuracronCombatLayer NewLayer)
{
	struct AuracronCombatBridge_eventOnCombatLayerChanged_Parms
	{
		EAuracronCombatLayer OldLayer;
		EAuracronCombatLayer NewLayer;
	};
	AuracronCombatBridge_eventOnCombatLayerChanged_Parms Parms;
	Parms.OldLayer=OldLayer;
	Parms.NewLayer=NewLayer;
	OnCombatLayerChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCombatLayerChanged ***************************************************

// ********** Begin Delegate FOnCombatEffectsSpawned ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms
	{
		FVector Location;
		FAuracronCombatEffectsConfiguration EffectsConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando efeitos s\xc3\x83\xc2\xa3o spawnados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando efeitos s\xc3\x83\xc2\xa3o spawnados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectsConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_EffectsConfig = { "EffectsConfig", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms, EffectsConfig), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, METADATA_PARAMS(0, nullptr) }; // 1086571899
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_EffectsConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnCombatEffectsSpawned__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnCombatEffectsSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnCombatEffectsSpawned, FVector Location, FAuracronCombatEffectsConfiguration EffectsConfig)
{
	struct AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms
	{
		FVector Location;
		FAuracronCombatEffectsConfiguration EffectsConfig;
	};
	AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms Parms;
	Parms.Location=Location;
	Parms.EffectsConfig=EffectsConfig;
	OnCombatEffectsSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCombatEffectsSpawned *************************************************

// ********** Begin Class UAuracronCombatBridge Function ApplyAreaDamage ***************************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics
{
	struct AuracronCombatBridge_eventApplyAreaDamage_Parms
	{
		FVector Location;
		FAuracronDamageConfiguration DamageConfig;
		FAuracronTargetingConfiguration TargetingConfig;
		AActor* SourceActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar dano em \xc3\x83\xc2\xa1rea\n     */" },
#endif
		{ "CPP_Default_SourceActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar dano em \xc3\x83\xc2\xa1rea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_DamageConfig = { "DamageConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, DamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageConfig_MetaData), NewProp_DamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 1686714939
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyAreaDamage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyAreaDamage_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_DamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyAreaDamage", Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::AuracronCombatBridge_eventApplyAreaDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::AuracronCombatBridge_eventApplyAreaDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyAreaDamage)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronDamageConfiguration,Z_Param_Out_DamageConfig);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyAreaDamage(Z_Param_Out_Location,Z_Param_Out_DamageConfig,Z_Param_Out_TargetingConfig,Z_Param_SourceActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyAreaDamage *****************************

// ********** Begin Class UAuracronCombatBridge Function ApplyDamageToTarget ***********************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics
{
	struct AuracronCombatBridge_eventApplyDamageToTarget_Parms
	{
		AActor* TargetActor;
		FAuracronDamageConfiguration DamageConfig;
		AActor* SourceActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar dano a um alvo\n     */" },
#endif
		{ "CPP_Default_SourceActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar dano a um alvo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyDamageToTarget_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_DamageConfig = { "DamageConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyDamageToTarget_Parms, DamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageConfig_MetaData), NewProp_DamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyDamageToTarget_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyDamageToTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyDamageToTarget_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_DamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyDamageToTarget", Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::AuracronCombatBridge_eventApplyDamageToTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::AuracronCombatBridge_eventApplyDamageToTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyDamageToTarget)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FAuracronDamageConfiguration,Z_Param_Out_DamageConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyDamageToTarget(Z_Param_TargetActor,Z_Param_Out_DamageConfig,Z_Param_SourceActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyDamageToTarget *************************

// ********** Begin Class UAuracronCombatBridge Function ApplyFieldSystemDestruction ***************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics
{
	struct AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms
	{
		FVector Location;
		float Force;
		float Radius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyFieldSystemDestruction", Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyFieldSystemDestruction)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyFieldSystemDestruction(Z_Param_Out_Location,Z_Param_Force,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyFieldSystemDestruction *****************

// ********** Begin Class UAuracronCombatBridge Function CalculateFinalDamage **********************
struct Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics
{
	struct AuracronCombatBridge_eventCalculateFinalDamage_Parms
	{
		FAuracronDamageConfiguration DamageConfig;
		AActor* SourceActor;
		AActor* TargetActor;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Calcular dano final\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular dano final" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_DamageConfig = { "DamageConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, DamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageConfig_MetaData), NewProp_DamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_DamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CalculateFinalDamage", Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::AuracronCombatBridge_eventCalculateFinalDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::AuracronCombatBridge_eventCalculateFinalDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCalculateFinalDamage)
{
	P_GET_STRUCT_REF(FAuracronDamageConfiguration,Z_Param_Out_DamageConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateFinalDamage(Z_Param_Out_DamageConfig,Z_Param_SourceActor,Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CalculateFinalDamage ************************

// ********** Begin Class UAuracronCombatBridge Function CheckLineOfSight **************************
struct Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics
{
	struct AuracronCombatBridge_eventCheckLineOfSight_Parms
	{
		FVector StartLocation;
		FVector EndLocation;
		TArray<TEnumAsByte<ECollisionChannel>> CollisionChannels;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar line of sight entre dois pontos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar line of sight entre dois pontos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionChannels_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CollisionChannels;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCheckLineOfSight_Parms, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCheckLineOfSight_Parms, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels_Inner = { "CollisionChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(0, nullptr) }; // 756624936
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels = { "CollisionChannels", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCheckLineOfSight_Parms, CollisionChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionChannels_MetaData), NewProp_CollisionChannels_MetaData) }; // 756624936
void Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventCheckLineOfSight_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventCheckLineOfSight_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CheckLineOfSight", Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::AuracronCombatBridge_eventCheckLineOfSight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::AuracronCombatBridge_eventCheckLineOfSight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCheckLineOfSight)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndLocation);
	P_GET_TARRAY_REF(TEnumAsByte<ECollisionChannel>,Z_Param_Out_CollisionChannels);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckLineOfSight(Z_Param_Out_StartLocation,Z_Param_Out_EndLocation,Z_Param_Out_CollisionChannels);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CheckLineOfSight ****************************

// ********** Begin Class UAuracronCombatBridge Function CreateChaosExplosion **********************
struct Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics
{
	struct AuracronCombatBridge_eventCreateChaosExplosion_Parms
	{
		FVector Location;
		float Force;
		float Radius;
		bool bAffectAllLayers;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar explos\xc3\x83\xc2\xa3o com Chaos Physics\n     */" },
#endif
		{ "CPP_Default_bAffectAllLayers", "false" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar explos\xc3\x83\xc2\xa3o com Chaos Physics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_bAffectAllLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectAllLayers;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateChaosExplosion_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateChaosExplosion_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateChaosExplosion_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventCreateChaosExplosion_Parms*)Obj)->bAffectAllLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers = { "bAffectAllLayers", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventCreateChaosExplosion_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventCreateChaosExplosion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventCreateChaosExplosion_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CreateChaosExplosion", Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::AuracronCombatBridge_eventCreateChaosExplosion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::AuracronCombatBridge_eventCreateChaosExplosion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCreateChaosExplosion)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_UBOOL(Z_Param_bAffectAllLayers);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateChaosExplosion(Z_Param_Out_Location,Z_Param_Force,Z_Param_Radius,Z_Param_bAffectAllLayers);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CreateChaosExplosion ************************

// ********** Begin Class UAuracronCombatBridge Function ExecuteTargeting **************************
struct Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics
{
	struct AuracronCombatBridge_eventExecuteTargeting_Parms
	{
		FAuracronTargetingConfiguration TargetingConfig;
		FVector SourceLocation;
		FVector TargetDirection;
		FAuracronTargetingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executar targeting 3D\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executar targeting 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 1686714939
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_SourceLocation = { "SourceLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, SourceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLocation_MetaData), NewProp_SourceLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetDirection = { "TargetDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, TargetDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetDirection_MetaData), NewProp_TargetDirection_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTargetingResult, METADATA_PARAMS(0, nullptr) }; // 282061631
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_SourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ExecuteTargeting", Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::AuracronCombatBridge_eventExecuteTargeting_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::AuracronCombatBridge_eventExecuteTargeting_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execExecuteTargeting)
{
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SourceLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTargetingResult*)Z_Param__Result=P_THIS->ExecuteTargeting(Z_Param_Out_TargetingConfig,Z_Param_Out_SourceLocation,Z_Param_Out_TargetDirection);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ExecuteTargeting ****************************

// ********** Begin Class UAuracronCombatBridge Function GetActorCombatLayer ***********************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics
{
	struct AuracronCombatBridge_eventGetActorCombatLayer_Parms
	{
		AActor* Actor;
		EAuracronCombatLayer ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Layers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter camada de combate de um ator\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter camada de combate de um ator" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetActorCombatLayer_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetActorCombatLayer_Parms, ReturnValue), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetActorCombatLayer", Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::AuracronCombatBridge_eventGetActorCombatLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::AuracronCombatBridge_eventGetActorCombatLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetActorCombatLayer)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronCombatLayer*)Z_Param__Result=P_THIS->GetActorCombatLayer(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetActorCombatLayer *************************

// ********** Begin Class UAuracronCombatBridge Function GetTargetsInCone **************************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics
{
	struct AuracronCombatBridge_eventGetTargetsInCone_Parms
	{
		FVector SourceLocation;
		FVector Direction;
		float Range;
		float ConeAngle;
		FAuracronTargetingConfiguration TargetingConfig;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter alvos em cone\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter alvos em cone" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Direction_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Range;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConeAngle;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_SourceLocation = { "SourceLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, SourceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLocation_MetaData), NewProp_SourceLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Direction_MetaData), NewProp_Direction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Range = { "Range", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, Range), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ConeAngle = { "ConeAngle", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, ConeAngle), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 1686714939
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_SourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Range,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ConeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetTargetsInCone", Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::AuracronCombatBridge_eventGetTargetsInCone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::AuracronCombatBridge_eventGetTargetsInCone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetTargetsInCone)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SourceLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Direction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Range);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ConeAngle);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetTargetsInCone(Z_Param_Out_SourceLocation,Z_Param_Out_Direction,Z_Param_Range,Z_Param_ConeAngle,Z_Param_Out_TargetingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetTargetsInCone ****************************

// ********** Begin Class UAuracronCombatBridge Function GetTargetsInRadius ************************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics
{
	struct AuracronCombatBridge_eventGetTargetsInRadius_Parms
	{
		FVector Location;
		float Radius;
		FAuracronTargetingConfiguration TargetingConfig;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter alvos em raio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter alvos em raio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 1686714939
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetTargetsInRadius", Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::AuracronCombatBridge_eventGetTargetsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::AuracronCombatBridge_eventGetTargetsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetTargetsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetTargetsInRadius(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Out_TargetingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetTargetsInRadius **************************

// ********** Begin Class UAuracronCombatBridge Function GetTargetsInVerticalColumn ****************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics
{
	struct AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms
	{
		FVector Location;
		float Radius;
		float Height;
		FAuracronTargetingConfiguration TargetingConfig;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter alvos em coluna vertical\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter alvos em coluna vertical" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 1686714939
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetTargetsInVerticalColumn", Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetTargetsInVerticalColumn)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetTargetsInVerticalColumn(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Height,Z_Param_Out_TargetingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetTargetsInVerticalColumn ******************

// ********** Begin Class UAuracronCombatBridge Function IsValidTarget *****************************
struct Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics
{
	struct AuracronCombatBridge_eventIsValidTarget_Parms
	{
		AActor* TargetActor;
		FAuracronTargetingConfiguration TargetingConfig;
		AActor* SourceActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se alvo \xc3\x83\xc2\xa9 v\xc3\x83\xc2\xa1lido\n     */" },
#endif
		{ "CPP_Default_SourceActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se alvo \xc3\x83\xc2\xa9 v\xc3\x83\xc2\xa1lido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventIsValidTarget_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventIsValidTarget_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 1686714939
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventIsValidTarget_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventIsValidTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventIsValidTarget_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "IsValidTarget", Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::AuracronCombatBridge_eventIsValidTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::AuracronCombatBridge_eventIsValidTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execIsValidTarget)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidTarget(Z_Param_TargetActor,Z_Param_Out_TargetingConfig,Z_Param_SourceActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function IsValidTarget *******************************

// ********** Begin Class UAuracronCombatBridge Function OnRep_CurrentCombatLayer ******************
struct Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnRep_CurrentCombatLayer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execOnRep_CurrentCombatLayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CurrentCombatLayer();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function OnRep_CurrentCombatLayer ********************

// ********** Begin Class UAuracronCombatBridge Function SpawnCombatEffects ************************
struct Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics
{
	struct AuracronCombatBridge_eventSpawnCombatEffects_Parms
	{
		FVector Location;
		FAuracronCombatEffectsConfiguration EffectsConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar efeitos de combate\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar efeitos de combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectsConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectsConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSpawnCombatEffects_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_EffectsConfig = { "EffectsConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSpawnCombatEffects_Parms, EffectsConfig), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectsConfig_MetaData), NewProp_EffectsConfig_MetaData) }; // 1086571899
void Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventSpawnCombatEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventSpawnCombatEffects_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_EffectsConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "SpawnCombatEffects", Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::AuracronCombatBridge_eventSpawnCombatEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::AuracronCombatBridge_eventSpawnCombatEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execSpawnCombatEffects)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronCombatEffectsConfiguration,Z_Param_Out_EffectsConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpawnCombatEffects(Z_Param_Out_Location,Z_Param_Out_EffectsConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function SpawnCombatEffects **************************

// ********** Begin Class UAuracronCombatBridge ****************************************************
void UAuracronCombatBridge::StaticRegisterNativesUAuracronCombatBridge()
{
	UClass* Class = UAuracronCombatBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyAreaDamage", &UAuracronCombatBridge::execApplyAreaDamage },
		{ "ApplyDamageToTarget", &UAuracronCombatBridge::execApplyDamageToTarget },
		{ "ApplyFieldSystemDestruction", &UAuracronCombatBridge::execApplyFieldSystemDestruction },
		{ "CalculateFinalDamage", &UAuracronCombatBridge::execCalculateFinalDamage },
		{ "CheckLineOfSight", &UAuracronCombatBridge::execCheckLineOfSight },
		{ "CreateChaosExplosion", &UAuracronCombatBridge::execCreateChaosExplosion },
		{ "ExecuteTargeting", &UAuracronCombatBridge::execExecuteTargeting },
		{ "GetActorCombatLayer", &UAuracronCombatBridge::execGetActorCombatLayer },
		{ "GetTargetsInCone", &UAuracronCombatBridge::execGetTargetsInCone },
		{ "GetTargetsInRadius", &UAuracronCombatBridge::execGetTargetsInRadius },
		{ "GetTargetsInVerticalColumn", &UAuracronCombatBridge::execGetTargetsInVerticalColumn },
		{ "IsValidTarget", &UAuracronCombatBridge::execIsValidTarget },
		{ "OnRep_CurrentCombatLayer", &UAuracronCombatBridge::execOnRep_CurrentCombatLayer },
		{ "SpawnCombatEffects", &UAuracronCombatBridge::execSpawnCombatEffects },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronCombatBridge;
UClass* UAuracronCombatBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronCombatBridge;
	if (!Z_Registration_Info_UClass_UAuracronCombatBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronCombatBridge"),
			Z_Registration_Info_UClass_UAuracronCombatBridge.InnerSingleton,
			StaticRegisterNativesUAuracronCombatBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronCombatBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister()
{
	return UAuracronCombatBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronCombatBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Combate 3D Vertical\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de combate com targeting vertical\n */" },
#endif
		{ "DisplayName", "AURACRON Combat Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronCombatBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Combate 3D Vertical\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de combate com targeting vertical" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTargetingConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de targeting padr\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de targeting padr\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultDamageConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de dano padr\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de dano padr\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultEffectsConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de efeitos padr\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de efeitos padr\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentCombatLayer_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camada de combate atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camada de combate atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTargetingResult_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltimo resultado de targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltimo resultado de targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldSystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao FieldSystemComponent */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao FieldSystemComponent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTargetingExecuted_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDamageApplied_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCombatLayerChanged_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCombatEffectsSpawned_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultTargetingConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultDamageConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultEffectsConfig;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentCombatLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentCombatLayer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastTargetingResult;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FieldSystemComponent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTargetingExecuted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDamageApplied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCombatLayerChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCombatEffectsSpawned;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage, "ApplyAreaDamage" }, // 1770233431
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget, "ApplyDamageToTarget" }, // 4218748587
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction, "ApplyFieldSystemDestruction" }, // 3689865967
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage, "CalculateFinalDamage" }, // 351128093
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight, "CheckLineOfSight" }, // 3745041030
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion, "CreateChaosExplosion" }, // 3680153202
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting, "ExecuteTargeting" }, // 1480437961
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer, "GetActorCombatLayer" }, // 3560224605
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone, "GetTargetsInCone" }, // 3291117054
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius, "GetTargetsInRadius" }, // 1702822126
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn, "GetTargetsInVerticalColumn" }, // 2254702535
		{ &Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget, "IsValidTarget" }, // 283792736
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature, "OnCombatEffectsSpawned__DelegateSignature" }, // 1581484086
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature, "OnCombatLayerChanged__DelegateSignature" }, // 2955618232
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature, "OnDamageApplied__DelegateSignature" }, // 1820049788
		{ &Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer, "OnRep_CurrentCombatLayer" }, // 1472461616
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature, "OnTargetingExecuted__DelegateSignature" }, // 2193221293
		{ &Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects, "SpawnCombatEffects" }, // 3211144587
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronCombatBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultTargetingConfig = { "DefaultTargetingConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, DefaultTargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTargetingConfig_MetaData), NewProp_DefaultTargetingConfig_MetaData) }; // 1686714939
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultDamageConfig = { "DefaultDamageConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, DefaultDamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultDamageConfig_MetaData), NewProp_DefaultDamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultEffectsConfig = { "DefaultEffectsConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, DefaultEffectsConfig), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultEffectsConfig_MetaData), NewProp_DefaultEffectsConfig_MetaData) }; // 1086571899
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer = { "CurrentCombatLayer", "OnRep_CurrentCombatLayer", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, CurrentCombatLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentCombatLayer_MetaData), NewProp_CurrentCombatLayer_MetaData) }; // 3955355270
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_LastTargetingResult = { "LastTargetingResult", nullptr, (EPropertyFlags)0x0010008000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, LastTargetingResult), Z_Construct_UScriptStruct_FAuracronTargetingResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTargetingResult_MetaData), NewProp_LastTargetingResult_MetaData) }; // 282061631
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_FieldSystemComponent = { "FieldSystemComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, FieldSystemComponent), Z_Construct_UClass_UFieldSystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldSystemComponent_MetaData), NewProp_FieldSystemComponent_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnTargetingExecuted = { "OnTargetingExecuted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnTargetingExecuted), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTargetingExecuted_MetaData), NewProp_OnTargetingExecuted_MetaData) }; // 2193221293
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnDamageApplied = { "OnDamageApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnDamageApplied), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDamageApplied_MetaData), NewProp_OnDamageApplied_MetaData) }; // 1820049788
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatLayerChanged = { "OnCombatLayerChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnCombatLayerChanged), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCombatLayerChanged_MetaData), NewProp_OnCombatLayerChanged_MetaData) }; // 2955618232
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatEffectsSpawned = { "OnCombatEffectsSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnCombatEffectsSpawned), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCombatEffectsSpawned_MetaData), NewProp_OnCombatEffectsSpawned_MetaData) }; // 1581484086
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultTargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultDamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultEffectsConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_LastTargetingResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_FieldSystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnTargetingExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnDamageApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatLayerChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatEffectsSpawned,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronCombatBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronCombatBridge_Statics::ClassParams = {
	&UAuracronCombatBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronCombatBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronCombatBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronCombatBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronCombatBridge.OuterSingleton, Z_Construct_UClass_UAuracronCombatBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronCombatBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronCombatBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentCombatLayer(TEXT("CurrentCombatLayer"));
	const bool bIsValid = true
		&& Name_CurrentCombatLayer == ClassReps[(int32)ENetFields_Private::CurrentCombatLayer].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronCombatBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronCombatBridge);
UAuracronCombatBridge::~UAuracronCombatBridge() {}
// ********** End Class UAuracronCombatBridge ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronDamageType_StaticEnum, TEXT("EAuracronDamageType"), &Z_Registration_Info_UEnum_EAuracronDamageType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2470854689U) },
		{ EAuracronCombatLayer_StaticEnum, TEXT("EAuracronCombatLayer"), &Z_Registration_Info_UEnum_EAuracronCombatLayer, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3955355270U) },
		{ EAuracronTargetingType_StaticEnum, TEXT("EAuracronTargetingType"), &Z_Registration_Info_UEnum_EAuracronTargetingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1075389459U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronDamageConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewStructOps, TEXT("AuracronDamageConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDamageConfiguration), 2783236807U) },
		{ FAuracronTargetingConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewStructOps, TEXT("AuracronTargetingConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTargetingConfiguration), 1686714939U) },
		{ FAuracronTargetingResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewStructOps, TEXT("AuracronTargetingResult"), &Z_Registration_Info_UScriptStruct_FAuracronTargetingResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTargetingResult), 282061631U) },
		{ FAuracronCombatEffectsConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewStructOps, TEXT("AuracronCombatEffectsConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCombatEffectsConfiguration), 1086571899U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronCombatBridge, UAuracronCombatBridge::StaticClass, TEXT("UAuracronCombatBridge"), &Z_Registration_Info_UClass_UAuracronCombatBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronCombatBridge), 3755479446U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_2248145039(TEXT("/Script/AuracronCombatBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
