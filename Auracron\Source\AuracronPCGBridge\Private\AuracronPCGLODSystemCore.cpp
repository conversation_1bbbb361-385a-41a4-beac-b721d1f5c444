// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - LOD System Core Implementation
// Bridge 2.12: PCG Framework - LOD e Optimization

#include "AuracronPCGLODSystem.h"
#include "AuracronPCGBridge.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Engine/LODActor.h"
#include "WorldPartition/HLOD/HLODLayer.h"
#include "Curves/CurveFloat.h"
#include "Engine/Texture2D.h"

namespace AuracronPCGLODSystemUtils
{
    // =============================================================================
    // SAFE LOADING FUNCTIONS
    // =============================================================================

    UStaticMesh* LoadMeshSafe(const TSoftObjectPtr<UStaticMesh>& MeshPtr)
    {
        if (!MeshPtr.IsValid())
        {
            return nullptr;
        }

        UStaticMesh* Mesh = MeshPtr.LoadSynchronous();
        if (!Mesh)
        {
            // Try to load asynchronously if synchronous failed
            Mesh = MeshPtr.Get();
        }

        return Mesh;
    }

    UCurveFloat* LoadCurveSafe(const TSoftObjectPtr<UCurveFloat>& CurvePtr)
    {
        if (!CurvePtr.IsValid())
        {
            return nullptr;
        }

        UCurveFloat* Curve = CurvePtr.LoadSynchronous();
        if (!Curve)
        {
            // Try to load asynchronously if synchronous failed
            Curve = CurvePtr.Get();
        }

        return Curve;
    }

    UHLODLayer* LoadHLODLayerSafe(const TSoftObjectPtr<UHLODLayer>& HLODLayerPtr)
    {
        if (!HLODLayerPtr.IsValid())
        {
            return nullptr;
        }

        UHLODLayer* HLODLayer = HLODLayerPtr.LoadSynchronous();
        if (!HLODLayer)
        {
            // Try to load asynchronously if synchronous failed
            HLODLayer = HLODLayerPtr.Get();
        }

        return HLODLayer;
    }

    // =============================================================================
    // MESH SIMPLIFICATION FUNCTIONS
    // =============================================================================

    float CalculateQuadricError(const FVector& Vertex, const TArray<FVector4>& Planes)
    {
        float Error = 0.0f;
        
        for (const FVector4& Plane : Planes)
        {
            float Distance = FMath::Abs(FVector::DotProduct(Vertex, FVector(Plane.X, Plane.Y, Plane.Z)) + Plane.W);
            Error += Distance * Distance;
        }
        
        return Error;
    }

    TArray<FVector4> CalculateVertexPlanes(const FVector& Vertex, const TArray<FVector>& AdjacentVertices, const TArray<FVector>& FaceNormals)
    {
        TArray<FVector4> Planes;
        
        for (int32 i = 0; i < AdjacentVertices.Num() && i < FaceNormals.Num(); i++)
        {
            FVector Normal = FaceNormals[i];
            float D = -FVector::DotProduct(Normal, Vertex);
            Planes.Add(FVector4(Normal.X, Normal.Y, Normal.Z, D));
        }
        
        return Planes;
    }

    bool CanCollapseEdge(const FVector& VertexA, const FVector& VertexB, float Threshold)
    {
        float EdgeLength = FVector::Dist(VertexA, VertexB);
        return EdgeLength < Threshold;
    }

    // =============================================================================
    // INSTANCING OPTIMIZATION FUNCTIONS
    // =============================================================================

    void SortInstancesByDistance(TArray<FTransform>& Transforms, const FVector& ReferenceLocation)
    {
        Transforms.Sort([&ReferenceLocation](const FTransform& A, const FTransform& B) -> bool
        {
            float DistanceA = FVector::Dist(A.GetLocation(), ReferenceLocation);
            float DistanceB = FVector::Dist(B.GetLocation(), ReferenceLocation);
            return DistanceA < DistanceB;
        });
    }

    TArray<TArray<FTransform>> ClusterInstances(const TArray<FTransform>& Transforms, float ClusterRadius, int32 MaxClustersPerComponent)
    {
        TArray<TArray<FTransform>> Clusters;
        TArray<bool> Assigned(false, Transforms.Num());
        
        for (int32 i = 0; i < Transforms.Num() && Clusters.Num() < MaxClustersPerComponent; i++)
        {
            if (Assigned[i])
            {
                continue;
            }
            
            TArray<FTransform> NewCluster;
            NewCluster.Add(Transforms[i]);
            Assigned[i] = true;
            
            // Find nearby transforms to add to this cluster
            for (int32 j = i + 1; j < Transforms.Num(); j++)
            {
                if (Assigned[j])
                {
                    continue;
                }
                
                float Distance = FVector::Dist(Transforms[i].GetLocation(), Transforms[j].GetLocation());
                if (Distance <= ClusterRadius)
                {
                    NewCluster.Add(Transforms[j]);
                    Assigned[j] = true;
                }
            }
            
            Clusters.Add(NewCluster);
        }
        
        return Clusters;
    }

    void OptimizeInstanceBatches(TArray<FTransform>& Transforms, int32 MaxInstancesPerBatch)
    {
        if (Transforms.Num() <= MaxInstancesPerBatch)
        {
            return;
        }
        
        // Sort by location for better spatial locality
        Transforms.Sort([](const FTransform& A, const FTransform& B) -> bool
        {
            FVector LocationA = A.GetLocation();
            FVector LocationB = B.GetLocation();
            
            // Sort by X, then Y, then Z
            if (LocationA.X != LocationB.X)
            {
                return LocationA.X < LocationB.X;
            }
            if (LocationA.Y != LocationB.Y)
            {
                return LocationA.Y < LocationB.Y;
            }
            return LocationA.Z < LocationB.Z;
        });
        
        // Limit to max instances per batch
        if (Transforms.Num() > MaxInstancesPerBatch)
        {
            Transforms.SetNum(MaxInstancesPerBatch);
        }
    }

    // =============================================================================
    // CULLING OPTIMIZATION FUNCTIONS
    // =============================================================================

    float CalculateDistanceCullingFactor(float Distance, float MinDistance, float MaxDistance)
    {
        if (Distance < MinDistance)
        {
            return 0.0f; // Too close, cull
        }
        
        if (Distance > MaxDistance)
        {
            return 1.0f; // Too far, cull
        }
        
        // Linear interpolation between min and max distance
        return (Distance - MinDistance) / (MaxDistance - MinDistance);
    }

    float CalculateScreenSizeCullingFactor(float ScreenSize, float MinScreenSize)
    {
        if (ScreenSize < MinScreenSize)
        {
            return 1.0f; // Too small, cull
        }
        
        return 0.0f; // Visible
    }

    bool IsOccluded(const FVector& Location, const FVector& ViewerLocation, UWorld* World, float Accuracy)
    {
        if (!World)
        {
            return false;
        }

        // Advanced occlusion test using multiple rays and complex geometry
        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = true;
        QueryParams.bReturnPhysicalMaterial = false;
        QueryParams.AddIgnoredActors(TArray<AActor*>()); // Could add specific actors to ignore
        
        // Primary ray test
        FHitResult HitResult;
        bool bHit = World->LineTraceSingleByChannel(HitResult, ViewerLocation, Location, ECC_Visibility, QueryParams);
        
        // If primary ray hits, perform additional tests for better accuracy
        if (bHit && Accuracy > 0.5f)
        {
            // Test multiple rays around the target location for more accurate occlusion
            const int32 NumAdditionalRays = FMath::RoundToInt(Accuracy * 8.0f); // Up to 8 additional rays
            const float RaySpread = 50.0f; // Spread radius in units
            
            int32 HitCount = 1; // Primary ray already hit
            int32 TotalRays = 1;
            
            for (int32 i = 0; i < NumAdditionalRays; ++i)
            {
                // Generate random offset around the target location
                FVector RandomOffset = FVector(
                    FMath::RandRange(-RaySpread, RaySpread),
                    FMath::RandRange(-RaySpread, RaySpread),
                    FMath::RandRange(-RaySpread * 0.5f, RaySpread * 0.5f)
                );
                
                FVector OffsetLocation = Location + RandomOffset;
                FHitResult OffsetHitResult;
                
                if (World->LineTraceSingleByChannel(OffsetHitResult, ViewerLocation, OffsetLocation, ECC_Visibility, QueryParams))
                {
                    HitCount++;
                }
                TotalRays++;
            }
            
            // Calculate occlusion percentage
            float OcclusionPercentage = static_cast<float>(HitCount) / static_cast<float>(TotalRays);
            
            // Consider partially occluded if less than 70% of rays hit
            bHit = OcclusionPercentage > 0.7f;
        }
        
        // Apply accuracy factor
        if (bHit)
        {
            float HitDistance = FVector::Dist(ViewerLocation, HitResult.Location);
            float TotalDistance = FVector::Dist(ViewerLocation, Location);
            float OcclusionRatio = HitDistance / TotalDistance;
            
            return OcclusionRatio < Accuracy;
        }
        
        return false;
    }

    // =============================================================================
    // ADVANCED LOD FUNCTIONS
    // =============================================================================

    void EnableNaniteForMesh(UStaticMesh* Mesh, int32 TriangleThreshold)
    {
        if (!Mesh)
        {
            return;
        }

        // Real Nanite configuration using UE5.6 APIs
        ConfigureRealNaniteSettings(Mesh, TriangleThreshold);
    }

    void ConfigureHLODSettings(UStaticMesh* Mesh, const FAuracronPCGLODGenerationDescriptor& LODDescriptor)
    {
        if (!Mesh)
        {
            return;
        }

        // Real HLOD configuration using UE5.6 APIs
        if (LODDescriptor.GenerationMode == EAuracronPCGLODGenerationMode::HLOD)
        {
            ConfigureRealHLODSettings(Mesh, LODDescriptor);
        }
    }

    UTexture2D* GenerateImpostorTexture(UStaticMesh* Mesh, int32 TextureSize)
    {
        if (!Mesh)
        {
            return nullptr;
        }

        // Real impostor texture generation using UE5.6 rendering pipeline
        return GenerateRealImpostorTexture(Mesh, TextureSize);
    }

    // =============================================================================
    // PERFORMANCE ANALYSIS FUNCTIONS
    // =============================================================================

    FString GeneratePerformanceReport(const TArray<float>& RenderTimes, const TArray<int32>& DrawCalls, const TArray<int32>& TriangleCounts)
    {
        FString Report = TEXT("Performance Report:\n");
        
        if (RenderTimes.Num() > 0)
        {
            float AvgRenderTime = 0.0f;
            float MaxRenderTime = 0.0f;
            float MinRenderTime = FLT_MAX;
            
            for (float RenderTime : RenderTimes)
            {
                AvgRenderTime += RenderTime;
                MaxRenderTime = FMath::Max(MaxRenderTime, RenderTime);
                MinRenderTime = FMath::Min(MinRenderTime, RenderTime);
            }
            
            AvgRenderTime /= RenderTimes.Num();
            
            Report += FString::Printf(TEXT("Render Time - Avg: %.2fms, Min: %.2fms, Max: %.2fms\n"), 
                                    AvgRenderTime, MinRenderTime, MaxRenderTime);
        }
        
        if (DrawCalls.Num() > 0)
        {
            int32 TotalDrawCalls = 0;
            int32 MaxDrawCalls = 0;
            
            for (int32 DrawCall : DrawCalls)
            {
                TotalDrawCalls += DrawCall;
                MaxDrawCalls = FMath::Max(MaxDrawCalls, DrawCall);
            }
            
            Report += FString::Printf(TEXT("Draw Calls - Total: %d, Max: %d\n"), TotalDrawCalls, MaxDrawCalls);
        }
        
        if (TriangleCounts.Num() > 0)
        {
            int32 TotalTriangles = 0;
            int32 MaxTriangles = 0;
            
            for (int32 TriangleCount : TriangleCounts)
            {
                TotalTriangles += TriangleCount;
                MaxTriangles = FMath::Max(MaxTriangles, TriangleCount);
            }
            
            Report += FString::Printf(TEXT("Triangles - Total: %d, Max: %d\n"), TotalTriangles, MaxTriangles);
        }
        
        return Report;
    }

    TArray<FString> GenerateOptimizationSuggestions(float RenderTime, int32 DrawCalls, int32 TriangleCount, float MemoryUsage)
    {
        TArray<FString> Suggestions;
        
        if (RenderTime > 16.67f) // 60 FPS threshold
        {
            Suggestions.Add(TEXT("Consider reducing mesh complexity or using LOD"));
            Suggestions.Add(TEXT("Enable occlusion culling"));
        }
        
        if (DrawCalls > 1000)
        {
            Suggestions.Add(TEXT("Use instanced rendering to reduce draw calls"));
            Suggestions.Add(TEXT("Combine meshes with similar materials"));
        }
        
        if (TriangleCount > 1000000)
        {
            Suggestions.Add(TEXT("Implement aggressive LOD system"));
            Suggestions.Add(TEXT("Consider using Nanite for high-poly meshes"));
        }
        
        if (MemoryUsage > 512.0f)
        {
            Suggestions.Add(TEXT("Optimize texture sizes"));
            Suggestions.Add(TEXT("Use texture streaming"));
            Suggestions.Add(TEXT("Implement mesh streaming for distant objects"));
        }
        
        if (Suggestions.Num() == 0)
        {
            Suggestions.Add(TEXT("Performance is within acceptable limits"));
        }
        
        return Suggestions;
    }

    void ExportPerformanceData(const FString& FilePath, const TMap<FString, float>& PerformanceData)
    {
        // Robust file writing implementation using UE5.6 file system
        if (FilePath.IsEmpty())
        {
            UE_LOG(LogTemp, Warning, TEXT("ExportPerformanceData: Empty file path provided"));
            return;
        }
        
        // Ensure directory exists
        FString DirectoryPath = FPaths::GetPath(FilePath);
        if (!DirectoryPath.IsEmpty() && !FPaths::DirectoryExists(DirectoryPath))
        {
            if (!IFileManager::Get().MakeDirectory(*DirectoryPath, true))
            {
                UE_LOG(LogTemp, Error, TEXT("ExportPerformanceData: Failed to create directory: %s"), *DirectoryPath);
                return;
            }
        }
        
        // Build comprehensive CSV content with metadata
        FString CSVContent;
        CSVContent += TEXT("# Auracron PCG Performance Data Export\n");
        CSVContent += FString::Printf(TEXT("# Generated: %s\n"), *FDateTime::Now().ToString());
        CSVContent += FString::Printf(TEXT("# Engine Version: %s\n"), *FEngineVersion::Current().ToString());
        CSVContent += FString::Printf(TEXT("# Total Metrics: %d\n"), PerformanceData.Num());
        CSVContent += TEXT("#\n");
        CSVContent += TEXT("Metric,Value,Unit,Category\n");
        
        // Sort metrics by category for better organization
        TArray<TPair<FString, float>> SortedData;
        for (const auto& DataPair : PerformanceData)
        {
            SortedData.Add(DataPair);
        }
        
        SortedData.Sort([](const TPair<FString, float>& A, const TPair<FString, float>& B)
        {
            return A.Key < B.Key;
        });
        
        // Add performance data with enhanced formatting
        for (const auto& DataPair : SortedData)
        {
            FString MetricName = DataPair.Key;
            float Value = DataPair.Value;
            FString Unit = TEXT("");
            FString Category = TEXT("General");
            
            // Determine unit and category based on metric name
            if (MetricName.Contains(TEXT("Time")) || MetricName.Contains(TEXT("Duration")))
            {
                Unit = TEXT("ms");
                Category = TEXT("Performance");
            }
            else if (MetricName.Contains(TEXT("Memory")) || MetricName.Contains(TEXT("Size")))
            {
                Unit = TEXT("MB");
                Category = TEXT("Memory");
            }
            else if (MetricName.Contains(TEXT("Count")) || MetricName.Contains(TEXT("Number")))
            {
                Unit = TEXT("count");
                Category = TEXT("Statistics");
            }
            else if (MetricName.Contains(TEXT("FPS")) || MetricName.Contains(TEXT("Rate")))
            {
                Unit = TEXT("fps");
                Category = TEXT("Performance");
            }
            else if (MetricName.Contains(TEXT("Triangle")) || MetricName.Contains(TEXT("Vertex")))
            {
                Unit = TEXT("count");
                Category = TEXT("Geometry");
            }
            else if (MetricName.Contains(TEXT("Draw")) || MetricName.Contains(TEXT("Call")))
            {
                Unit = TEXT("count");
                Category = TEXT("Rendering");
            }
            
            CSVContent += FString::Printf(TEXT("%s,%.6f,%s,%s\n"), 
                *MetricName, Value, *Unit, *Category);
        }
        
        // Add summary statistics
        if (PerformanceData.Num() > 0)
        {
            CSVContent += TEXT("\n# Summary Statistics\n");
            
            TArray<float> Values;
            for (const auto& DataPair : PerformanceData)
            {
                Values.Add(DataPair.Value);
            }
            
            Values.Sort();
            float MinValue = Values[0];
            float MaxValue = Values.Last();
            float Sum = 0.0f;
            for (float Val : Values)
            {
                Sum += Val;
            }
            float Average = Sum / Values.Num();
            float Median = Values.Num() % 2 == 0 ? 
                (Values[Values.Num() / 2 - 1] + Values[Values.Num() / 2]) / 2.0f :
                Values[Values.Num() / 2];
            
            CSVContent += FString::Printf(TEXT("Summary_Min,%.6f,mixed,Summary\n"), MinValue);
            CSVContent += FString::Printf(TEXT("Summary_Max,%.6f,mixed,Summary\n"), MaxValue);
            CSVContent += FString::Printf(TEXT("Summary_Average,%.6f,mixed,Summary\n"), Average);
            CSVContent += FString::Printf(TEXT("Summary_Median,%.6f,mixed,Summary\n"), Median);
        }
        
        // Write to file with error handling
        if (!FFileHelper::SaveStringToFile(CSVContent, *FilePath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), EFileWrite::FILEWRITE_EvenIfReadOnly))
        {
            UE_LOG(LogTemp, Error, TEXT("ExportPerformanceData: Failed to write file: %s"), *FilePath);
            return;
        }
        
        // Verify file was written successfully
        if (!FPaths::FileExists(FilePath))
        {
            UE_LOG(LogTemp, Error, TEXT("ExportPerformanceData: File verification failed: %s"), *FilePath);
            return;
        }
        
        // Log success with file size
        int64 FileSize = IFileManager::Get().FileSize(*FilePath);
        UE_LOG(LogTemp, Log, TEXT("ExportPerformanceData: Successfully exported %d metrics to %s (%.2f KB)"), 
               PerformanceData.Num(), *FilePath, FileSize / 1024.0f);
        
        // Optional: Create backup if file is important
        if (PerformanceData.Num() > 100) // Large dataset
        {
            FString BackupPath = FilePath + TEXT(".backup");
            if (FFileHelper::SaveStringToFile(CSVContent, *BackupPath))
            {
                UE_LOG(LogTemp, Log, TEXT("ExportPerformanceData: Backup created: %s"), *BackupPath);
            }
        }
    }

    // =============================================================================
    // UTILITY HELPER FUNCTIONS
    // =============================================================================

    FVector CalculateBoundingBoxCenter(const TArray<FVector>& Vertices)
    {
        if (Vertices.Num() == 0)
        {
            return FVector::ZeroVector;
        }
        
        FVector Min = Vertices[0];
        FVector Max = Vertices[0];
        
        for (const FVector& Vertex : Vertices)
        {
            Min = FVector::Min(Min, Vertex);
            Max = FVector::Max(Max, Vertex);
        }
        
        return (Min + Max) * 0.5f;
    }

    float CalculateBoundingBoxVolume(const FVector& Min, const FVector& Max)
    {
        FVector Size = Max - Min;
        return Size.X * Size.Y * Size.Z;
    }

    bool IsValidLODDistance(float Distance)
    {
        return Distance > 0.0f && Distance < 100000.0f; // Reasonable range
    }

    bool IsValidScreenSize(float ScreenSize)
    {
        return ScreenSize >= 0.0f && ScreenSize <= 1.0f;
    }

    int32 CalculateOptimalClusterCount(int32 InstanceCount, int32 MaxInstancesPerCluster)
    {
        if (MaxInstancesPerCluster <= 0)
        {
            return 1;
        }
        
        return FMath::CeilToInt(static_cast<float>(InstanceCount) / MaxInstancesPerCluster);
    }

    float CalculateLODTransitionAlpha(float Distance, float LODDistance, float TransitionWidth)
    {
        float TransitionStart = LODDistance - TransitionWidth * 0.5f;
        float TransitionEnd = LODDistance + TransitionWidth * 0.5f;
        
        if (Distance <= TransitionStart)
        {
            return 0.0f;
        }
        else if (Distance >= TransitionEnd)
        {
            return 1.0f;
        }
        else
        {
            return (Distance - TransitionStart) / TransitionWidth;
        }
    }
}

// === Helper Functions Implementation ===

void ConfigureRealHLODSettings(UStaticMesh* Mesh, const FAuracronPCGLODGenerationDescriptor& LODDescriptor)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(ConfigureRealHLODSettings);

    if (!Mesh)
    {
        return;
    }

    // Real HLOD configuration using UE5.6 APIs
    if (UStaticMeshSourceModel* SourceModel = Mesh->GetSourceModel(0))
    {
        // Configure HLOD reduction settings
        FMeshReductionSettings& ReductionSettings = SourceModel->ReductionSettings;

        switch (LODDescriptor.QualityLevel)
        {
            case EAuracronPCGLODQualityLevel::Low:
                ReductionSettings.PercentTriangles = 0.1f;
                ReductionSettings.PercentVertices = 0.1f;
                break;
            case EAuracronPCGLODQualityLevel::Medium:
                ReductionSettings.PercentTriangles = 0.25f;
                ReductionSettings.PercentVertices = 0.25f;
                break;
            case EAuracronPCGLODQualityLevel::High:
                ReductionSettings.PercentTriangles = 0.5f;
                ReductionSettings.PercentVertices = 0.5f;
                break;
            default:
                ReductionSettings.PercentTriangles = 0.75f;
                ReductionSettings.PercentVertices = 0.75f;
                break;
        }

        ReductionSettings.MaxDeviation = LODDescriptor.MaxDeviation;
        ReductionSettings.PixelError = LODDescriptor.PixelError;
        ReductionSettings.WeldingThreshold = LODDescriptor.WeldingThreshold;
        ReductionSettings.bRecalculateNormals = true;
        ReductionSettings.bGenerateUniqueLightmapUVs = true;

        // Apply HLOD-specific settings
        ReductionSettings.ReductionMethod = EStaticMeshReductionTerimationCriterion::Triangles;
        ReductionSettings.bLockEdges = false;
        ReductionSettings.bLockColorBounaries = false;
    }
}

UTexture2D* GenerateRealImpostorTexture(UStaticMesh* Mesh, int32 TextureSize)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(GenerateRealImpostorTexture);

    if (!Mesh)
    {
        return nullptr;
    }

    // Real impostor texture generation using UE5.6 scene capture
    UTexture2D* ImpostorTexture = UTexture2D::CreateTransient(TextureSize, TextureSize, PF_B8G8R8A8);
    if (!ImpostorTexture)
    {
        return nullptr;
    }

    // Create scene capture component for impostor rendering
    USceneCaptureComponent2D* SceneCapture = NewObject<USceneCaptureComponent2D>();
    if (!SceneCapture)
    {
        return nullptr;
    }

    // Configure scene capture for impostor generation
    SceneCapture->ProjectionType = ECameraProjectionMode::Orthographic;
    SceneCapture->OrthoWidth = 1000.0f; // Adjust based on mesh bounds
    SceneCapture->TextureTarget = UTextureRenderTarget2D::CreateTransient(TextureSize, TextureSize, RTF_RGBA8);
    SceneCapture->CaptureSource = SCS_FinalColorLDR;
    SceneCapture->bCaptureEveryFrame = false;
    SceneCapture->bCaptureOnMovement = false;

    // Position camera to capture mesh
    FBox MeshBounds = Mesh->GetBoundingBox();
    FVector MeshCenter = MeshBounds.GetCenter();
    FVector MeshExtent = MeshBounds.GetExtent();

    // Set camera position and rotation for optimal capture
    SceneCapture->SetWorldLocation(MeshCenter + FVector(0, 0, MeshExtent.Z * 2.0f));
    SceneCapture->SetWorldRotation(FRotator(-90, 0, 0)); // Look down

    // Capture the impostor texture
    SceneCapture->CaptureScene();

    // Copy render target to texture using UE5.6 optimized pixel data transfer
    if (UTextureRenderTarget2D* RenderTarget = SceneCapture->TextureTarget)
    {
        // Copy render target data to impostor texture
        FTextureRenderTargetResource* RTResource = RenderTarget->GameThread_GetRenderTargetResource();
        if (RTResource && ImpostorTexture)
        {
            // Robust pixel data copying implementation
            ENQUEUE_RENDER_COMMAND(CopyImpostorPixelData)(
                [RTResource, ImpostorTexture, TextureSize](FRHICommandListImmediate& RHICmdList)
                {
                    // Ensure we're on the render thread
                    check(IsInRenderingThread());
                    
                    // Get texture resources
                    FTexture2DResource* TextureResource = static_cast<FTexture2DResource*>(ImpostorTexture->GetResource());
                    if (!TextureResource)
                    {
                        UE_LOG(LogTemp, Error, TEXT("GenerateRealImpostorTexture: Failed to get texture resource"));
                        return;
                    }
                    
                    // Validate texture dimensions
                    FRHITexture2D* SourceTexture = RTResource->GetRenderTargetTexture();
                    FRHITexture2D* DestTexture = TextureResource->GetTexture2DRHI();
                    
                    if (!SourceTexture || !DestTexture)
                    {
                        UE_LOG(LogTemp, Error, TEXT("GenerateRealImpostorTexture: Invalid texture handles"));
                        return;
                    }
                    
                    // Verify texture formats are compatible
                    if (SourceTexture->GetFormat() != DestTexture->GetFormat())
                    {
                        UE_LOG(LogTemp, Warning, TEXT("GenerateRealImpostorTexture: Texture format mismatch, attempting conversion"));
                    }
                    
                    // Method 1: Direct texture copy (fastest)
                    FRHICopyTextureInfo CopyInfo;
                    CopyInfo.Size = FIntVector(TextureSize, TextureSize, 1);
                    CopyInfo.SourcePosition = FIntVector::ZeroValue;
                    CopyInfo.DestPosition = FIntVector::ZeroValue;
                    CopyInfo.SourceSliceIndex = 0;
                    CopyInfo.DestSliceIndex = 0;
                    CopyInfo.NumSlices = 1;
                    
                    // Attempt direct copy first
                    bool bCopySuccessful = false;
                    try
                    {
                        RHICmdList.CopyTexture(SourceTexture, DestTexture, CopyInfo);
                        bCopySuccessful = true;
                        UE_LOG(LogTemp, Log, TEXT("GenerateRealImpostorTexture: Direct texture copy successful"));
                    }
                    catch (...)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("GenerateRealImpostorTexture: Direct copy failed, falling back to pixel reading"));
                    }
                    
                    // Method 2: Pixel-by-pixel copy (fallback)
                    if (!bCopySuccessful)
                    {
                        // Read pixels from render target
                        TArray<FColor> PixelData;
                        PixelData.SetNumUninitialized(TextureSize * TextureSize);
                        
                        // Read surface data
                        FReadSurfaceDataFlags ReadFlags(RCM_UNorm, CubeFace_MAX);
                        ReadFlags.SetLinearToGamma(false);
                        
                        if (RHICmdList.ReadSurfaceData(
                            SourceTexture,
                            FIntRect(0, 0, TextureSize, TextureSize),
                            PixelData,
                            ReadFlags))
                        {
                            // Process pixel data for impostor optimization
                            for (int32 i = 0; i < PixelData.Num(); ++i)
                            {
                                FColor& Pixel = PixelData[i];
                                
                                // Apply impostor-specific optimizations
                                // Enhance contrast for better visibility at distance
                                float Luminance = 0.299f * Pixel.R + 0.587f * Pixel.G + 0.114f * Pixel.B;
                                if (Luminance < 128)
                                {
                                    // Darken dark areas slightly
                                    Pixel.R = FMath::Clamp(Pixel.R * 0.9f, 0.0f, 255.0f);
                                    Pixel.G = FMath::Clamp(Pixel.G * 0.9f, 0.0f, 255.0f);
                                    Pixel.B = FMath::Clamp(Pixel.B * 0.9f, 0.0f, 255.0f);
                                }
                                else
                                {
                                    // Brighten bright areas slightly
                                    Pixel.R = FMath::Clamp(Pixel.R * 1.1f, 0.0f, 255.0f);
                                    Pixel.G = FMath::Clamp(Pixel.G * 1.1f, 0.0f, 255.0f);
                                    Pixel.B = FMath::Clamp(Pixel.B * 1.1f, 0.0f, 255.0f);
                                }
                                
                                // Preserve alpha for transparency
                                // Alpha is important for impostor silhouettes
                            }
                            
                            // Update texture with processed pixel data
                            uint32 DestStride = 0;
                            void* DestData = RHICmdList.LockTexture2D(DestTexture, 0, RLM_WriteOnly, DestStride, false);
                            if (DestData)
                            {
                                // Copy pixel data with proper stride handling
                                const uint32 SourceStride = TextureSize * sizeof(FColor);
                                if (DestStride == SourceStride)
                                {
                                    // Direct memory copy if strides match
                                    FMemory::Memcpy(DestData, PixelData.GetData(), PixelData.Num() * sizeof(FColor));
                                }
                                else
                                {
                                    // Row-by-row copy if strides differ
                                    uint8* DestPtr = static_cast<uint8*>(DestData);
                                    const uint8* SourcePtr = reinterpret_cast<const uint8*>(PixelData.GetData());
                                    
                                    for (int32 Row = 0; Row < TextureSize; ++Row)
                                    {
                                        FMemory::Memcpy(
                                            DestPtr + Row * DestStride,
                                            SourcePtr + Row * SourceStride,
                                            SourceStride
                                        );
                                    }
                                }
                                
                                RHICmdList.UnlockTexture2D(DestTexture, 0, false);
                                UE_LOG(LogTemp, Log, TEXT("GenerateRealImpostorTexture: Pixel data copy successful (%d pixels)"), PixelData.Num());
                            }
                            else
                            {
                                UE_LOG(LogTemp, Error, TEXT("GenerateRealImpostorTexture: Failed to lock destination texture"));
                            }
                        }
                        else
                        {
                            UE_LOG(LogTemp, Error, TEXT("GenerateRealImpostorTexture: Failed to read surface data"));
                        }
                    }
                    
                    // Generate mipmaps for better LOD performance
                    if (ImpostorTexture->GetNumMips() > 1)
                    {
                        RHICmdList.GenerateMips(DestTexture);
                        UE_LOG(LogTemp, Log, TEXT("GenerateRealImpostorTexture: Mipmaps generated"));
                    }
                    
                    // Update texture streaming data
                    ImpostorTexture->UpdateResource();
                });
            
            // Wait for render command completion
            FlushRenderingCommands();
            
            // Verify the copy was successful
            if (ImpostorTexture->GetResource())
            {
                UE_LOG(LogTemp, Log, TEXT("GenerateRealImpostorTexture: Impostor texture generation completed successfully"));
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("GenerateRealImpostorTexture: Impostor texture resource is invalid after copy"));
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("GenerateRealImpostorTexture: Invalid render target resource or impostor texture"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("GenerateRealImpostorTexture: Scene capture render target is null"));
    }

    return ImpostorTexture;
}

void ConfigureRealNaniteSettings(UStaticMesh* Mesh, int32 TriangleThreshold)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(ConfigureRealNaniteSettings);

    if (!Mesh)
    {
        return;
    }

    // Real Nanite configuration using UE5.6 APIs
    int32 ActualTriangleCount = 0;

    // Get actual triangle count from mesh
    if (Mesh->GetRenderData() && Mesh->GetRenderData()->LODResources.Num() > 0)
    {
        const FStaticMeshLODResources& LODResource = Mesh->GetRenderData()->LODResources[0];
        ActualTriangleCount = LODResource.GetNumTriangles();
    }

    // Check if mesh meets triangle threshold for Nanite
    if (ActualTriangleCount >= TriangleThreshold)
    {
        // Enable Nanite using UE5.6 APIs
        if (UStaticMeshSourceModel* SourceModel = Mesh->GetSourceModel(0))
        {
            // Configure Nanite settings
            FMeshNaniteSettings& NaniteSettings = Mesh->NaniteSettings;
            NaniteSettings.bEnabled = true;
            NaniteSettings.PositionPrecision = ENanitePositionPrecision::Auto;
            NaniteSettings.NormalPrecision = ENaniteNormalPrecision::Auto;
            NaniteSettings.TangentPrecision = ENaniteTangentPrecision::Auto;
            NaniteSettings.TargetMinimumResidencyInKB = 256;
            NaniteSettings.KeepPercentTriangles = 1.0f;
            NaniteSettings.TrimRelativeError = 0.0f;
            NaniteSettings.FallbackTarget = ENaniteFallbackTarget::Auto;
            NaniteSettings.FallbackPercentTriangles = 0.2f;
            NaniteSettings.FallbackRelativeError = 1.0f;

            // Mark mesh for rebuild
            Mesh->PostEditChange();

            UE_LOG(LogTemp, Log, TEXT("Nanite enabled for mesh %s with %d triangles"),
                   *Mesh->GetName(), ActualTriangleCount);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Mesh %s has %d triangles, below Nanite threshold of %d"),
               *Mesh->GetName(), ActualTriangleCount, TriangleThreshold);
    }
}
