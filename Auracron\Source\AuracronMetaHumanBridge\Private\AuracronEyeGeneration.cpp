#include "AuracronEyeGeneration.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInterface.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "RenderTargetPool.h"
#include "CanvasTypes.h"
#include "Engine/Canvas.h"
#include "TextureResource.h"
#include "RenderingThread.h"
#include "GlobalShader.h"
#include "ShaderParameterUtils.h"
#include "RHICommandList.h"
#include "PixelShaderUtils.h"
#include "CommonRenderResources.h"
#include "ImageUtils.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "Modules/ModuleManager.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"

DEFINE_LOG_CATEGORY(LogAuracronEyeGeneration);

// ========================================
// Compute Shader for Eye Generation
// ========================================

class FEyeGenerationCS : public FGlobalShader
{
    DECLARE_GLOBAL_SHADER(FEyeGenerationCS);
    SHADER_USE_PARAMETER_STRUCT(FEyeGenerationCS, FGlobalShader);

    BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
        SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture2D<float4>, OutputTexture)
        SHADER_PARAMETER(FVector2f, TextureSize)
        SHADER_PARAMETER(FVector4f, IrisParameters)
        SHADER_PARAMETER(FVector4f, PupilParameters)
        SHADER_PARAMETER(FVector4f, ScleraParameters)
        SHADER_PARAMETER(FVector4f, ColorParameters)
        SHADER_PARAMETER(int32, EyeType)
        SHADER_PARAMETER(int32, Seed)
    END_SHADER_PARAMETER_STRUCT()

public:
    static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
    {
        return IsFeatureLevelSupported(Parameters.Platform, ERHIFeatureLevel::SM5);
    }

    static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
    {
        FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
        OutEnvironment.SetDefine(TEXT("THREAD_GROUP_SIZE"), 8);
    }
};

IMPLEMENT_GLOBAL_SHADER(FEyeGenerationCS, "/Engine/Private/EyeGeneration.usf", "MainCS", SF_Compute);

// ========================================
// FAuracronEyeGeneration Implementation
// ========================================

FAuracronEyeGeneration::FAuracronEyeGeneration()
    : EyeTextureCacheMemoryUsage(0)
    , TotalEyeGenerationTime(0.0f)
{
}

FAuracronEyeGeneration::~FAuracronEyeGeneration()
{
    ClearEyeTextureCache();
}

UTexture2D* FAuracronEyeGeneration::GenerateEyeTexture(const FEyeGenerationParameters& Parameters)
{
    FScopeLock Lock(&EyeGenerationMutex);

    FString ValidationError;
    if (!ValidateEyeGenerationParameters(Parameters, ValidationError))
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Invalid eye generation parameters: %s"), *ValidationError);
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Generate cache key for eye texture using UE5.6 hashing
        FString CacheKey = CalculateEyeGenerationHash(Parameters);
        
        // Check cache first using UE5.6 caching system
        if (EyeTextureCache.Contains(CacheKey))
        {
            TWeakObjectPtr<UTexture2D> CachedTexture = EyeTextureCache[CacheKey];
            if (CachedTexture.IsValid())
            {
                UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Returning cached eye texture for key: %s"), *CacheKey);
                return CachedTexture.Get();
            }
            else
            {
                // Remove invalid cache entry
                EyeTextureCache.Remove(CacheKey);
            }
        }

        // Determine texture resolution using UE5.6 resolution management
        FIntPoint Resolution = GetQualityResolution(Parameters.Quality);
        if (Parameters.Quality == EEyeTextureQuality::Custom)
        {
            Resolution = Parameters.CustomResolution;
        }

        // Create render target for generation using UE5.6 render target system
        UTextureRenderTarget2D* RenderTarget = NewObject<UTextureRenderTarget2D>();
        RenderTarget->InitCustomFormat(Resolution.X, Resolution.Y, PF_B8G8R8A8, false);
        RenderTarget->UpdateResourceImmediate(true);

        // Generate eye texture content using compute shader
        bool bGenerationSuccess = GenerateEyeTextureToRenderTarget(Parameters, RenderTarget);
        if (!bGenerationSuccess)
        {
            UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Failed to generate eye texture content"));
            return nullptr;
        }

        // Create final texture from render target using UE5.6 texture creation
        FString TextureName = FString::Printf(TEXT("EyeTexture_%s_%d"), 
                                            *UEnum::GetValueAsString(Parameters.EyeType), 
                                            Parameters.Seed);
        UTexture2D* GeneratedTexture = CreateTextureFromRenderTarget(RenderTarget, TextureName);

        if (!GeneratedTexture)
        {
            UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Failed to create texture from render target"));
            return nullptr;
        }

        // Apply post-processing effects using UE5.6 texture processing
        ApplyEyeTexturePostProcessing(GeneratedTexture, Parameters);

        // Cache the generated texture using UE5.6 caching system
        EyeTextureCache.Add(CacheKey, GeneratedTexture);
        UpdateEyeTextureCacheStats();

        // Update generation statistics
        double GenerationTime = FPlatformTime::Seconds() - StartTime;
        TotalEyeGenerationTime += GenerationTime;
        UpdateEyeGenerationStats(TEXT("GenerateEyeTexture"), GenerationTime, true);

        UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Successfully generated %dx%d %s eye texture in %.3f seconds"), 
               Resolution.X, Resolution.Y, *UEnum::GetValueAsString(Parameters.EyeType), GenerationTime);

        return GeneratedTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Exception generating eye texture: %s"), UTF8_TO_TCHAR(e.what()));
        UpdateEyeGenerationStats(TEXT("GenerateEyeTexture"), FPlatformTime::Seconds() - StartTime, false);
        return nullptr;
    }
}

bool FAuracronEyeGeneration::GenerateEyeTextureToRenderTarget(const FEyeGenerationParameters& Parameters, UTextureRenderTarget2D* RenderTarget)
{
    if (!RenderTarget)
    {
        return false;
    }

    // Use compute shader for high-performance eye texture generation
    ENQUEUE_RENDER_COMMAND(GenerateEyeTexture)(
        [this, Parameters, RenderTarget](FRHICommandListImmediate& RHICmdList)
        {
            FRDGBuilder GraphBuilder(RHICmdList);

            // Create texture descriptor using UE5.6 RDG system
            FRDGTextureDesc TextureDesc = FRDGTextureDesc::Create2D(
                FIntPoint(RenderTarget->SizeX, RenderTarget->SizeY),
                PF_B8G8R8A8,
                FClearValueBinding::Black,
                TexCreate_ShaderResource | TexCreate_UAV
            );

            FRDGTextureRef OutputTexture = GraphBuilder.CreateTexture(TextureDesc, TEXT("EyeTexture"));
            FRDGTextureUAVRef OutputTextureUAV = GraphBuilder.CreateUAV(OutputTexture);

            // Set up compute shader parameters using UE5.6 parameter binding
            FEyeGenerationCS::FParameters* PassParameters = GraphBuilder.AllocParameters<FEyeGenerationCS::FParameters>();
            PassParameters->OutputTexture = OutputTextureUAV;
            PassParameters->TextureSize = FVector2f(RenderTarget->SizeX, RenderTarget->SizeY);
            PassParameters->IrisParameters = FVector4f(
                Parameters.IrisData.IrisRadius,
                Parameters.IrisData.IrisContrast,
                Parameters.IrisData.IrisDetail,
                Parameters.IrisData.IrisRoughness
            );
            PassParameters->PupilParameters = FVector4f(
                Parameters.PupilData.PupilSize,
                Parameters.PupilData.PupilDilation,
                Parameters.PupilData.PupilConstriction,
                Parameters.PupilData.PupilReflection
            );
            PassParameters->ScleraParameters = FVector4f(
                Parameters.ScleraData.ScleraRoughness,
                Parameters.ScleraData.VeinIntensity,
                Parameters.ScleraData.BloodShotIntensity,
                Parameters.ScleraData.Wetness
            );
            PassParameters->ColorParameters = FVector4f(
                Parameters.IrisData.IrisColor.R,
                Parameters.IrisData.IrisColor.G,
                Parameters.IrisData.IrisColor.B,
                Parameters.IrisData.IrisColor.A
            );
            PassParameters->EyeType = static_cast<int32>(Parameters.EyeType);
            PassParameters->Seed = Parameters.Seed;

            // Get compute shader using UE5.6 shader management
            TShaderMapRef<FEyeGenerationCS> ComputeShader(GetGlobalShaderMap(GMaxRHIFeatureLevel));

            // Calculate dispatch parameters using UE5.6 compute dispatch
            FIntVector GroupCount = FIntVector(
                FMath::DivideAndRoundUp(RenderTarget->SizeX, 8),
                FMath::DivideAndRoundUp(RenderTarget->SizeY, 8),
                1
            );

            // Add compute pass using UE5.6 RDG compute pass
            GraphBuilder.AddPass(
                RDG_EVENT_NAME("EyeTextureGeneration"),
                PassParameters,
                ERDGPassFlags::Compute,
                [&PassParameters, ComputeShader, GroupCount](FRHICommandList& RHICmdList)
                {
                    FComputeShaderUtils::Dispatch(RHICmdList, ComputeShader, *PassParameters, GroupCount);
                }
            );

            // Copy result to render target using UE5.6 texture copying
            FRHITexture* RenderTargetRHI = RenderTarget->GetRenderTargetResource()->GetRenderTargetTexture();
            AddCopyTexturePass(GraphBuilder, OutputTexture, GraphBuilder.RegisterExternalTexture(CreateRenderTarget(RenderTargetRHI, TEXT("RenderTarget"))));

            GraphBuilder.Execute();
        }
    );

    // Wait for render thread completion using UE5.6 synchronization
    FlushRenderingCommands();

    return true;
}

UTexture2D* FAuracronEyeGeneration::CreateTextureFromRenderTarget(UTextureRenderTarget2D* RenderTarget, const FString& TextureName)
{
    if (!RenderTarget)
    {
        return nullptr;
    }

    // Create new texture using UE5.6 texture creation APIs
    UTexture2D* NewTexture = UTexture2D::CreateTransient(RenderTarget->SizeX, RenderTarget->SizeY, PF_B8G8R8A8);
    if (!NewTexture)
    {
        return nullptr;
    }

    // Set texture properties using UE5.6 texture configuration
    NewTexture->SRGB = true;
    NewTexture->Filter = TF_Bilinear;
    NewTexture->AddressX = TA_Clamp;
    NewTexture->AddressY = TA_Clamp;
    NewTexture->CompressionSettings = TC_Default;
    NewTexture->MipGenSettings = TMGS_FromTextureGroup;

    // Copy render target data to texture using UE5.6 texture data transfer
    ENQUEUE_RENDER_COMMAND(CopyRenderTargetToTexture)(
        [RenderTarget, NewTexture](FRHICommandListImmediate& RHICmdList)
        {
            FTextureRenderTargetResource* RTResource = RenderTarget->GetRenderTargetResource();
            FTexture2DResource* TextureResource = static_cast<FTexture2DResource*>(NewTexture->GetResource());

            if (RTResource && TextureResource)
            {
                // Copy using UE5.6 optimized texture copying
                FRHICopyTextureInfo CopyInfo;
                RHICmdList.CopyTexture(RTResource->GetRenderTargetTexture(), TextureResource->GetTexture2DRHI(), CopyInfo);
            }
        }
    );

    // Update texture resource using UE5.6 resource management
    NewTexture->UpdateResource();

    return NewTexture;
}

UTexture2D* FAuracronEyeGeneration::GenerateIrisTexture(const FIrisData& IrisData, EEyeTextureQuality Quality)
{
    FScopeLock Lock(&EyeGenerationMutex);

    try
    {
        // Create eye generation parameters for iris
        FEyeGenerationParameters Parameters;
        Parameters.EyeType = EEyeTextureType::Iris;
        Parameters.Quality = Quality;
        Parameters.IrisData = IrisData;
        Parameters.bGenerateMipmaps = true;
        Parameters.bSRGB = true;
        Parameters.Seed = FMath::Rand();

        // Generate iris texture using UE5.6 procedural generation
        UTexture2D* IrisTexture = GenerateEyeTexture(Parameters);
        if (!IrisTexture)
        {
            return nullptr;
        }

        // Apply iris-specific processing using UE5.6 image processing
        ApplyIrisPattern(IrisTexture, IrisData);
        ApplyIrisColorVariation(IrisTexture, IrisData);
        ApplyIrisReflection(IrisTexture, IrisData);

        UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Successfully generated iris texture"));
        return IrisTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Exception generating iris texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

UTexture2D* FAuracronEyeGeneration::GenerateScleraTexture(const FScleraData& ScleraData, EEyeTextureQuality Quality)
{
    FScopeLock Lock(&EyeGenerationMutex);

    try
    {
        // Create eye generation parameters for sclera
        FEyeGenerationParameters Parameters;
        Parameters.EyeType = EEyeTextureType::Sclera;
        Parameters.Quality = Quality;
        Parameters.ScleraData = ScleraData;
        Parameters.bGenerateMipmaps = true;
        Parameters.bSRGB = true;
        Parameters.Seed = FMath::Rand();

        // Generate sclera texture using UE5.6 procedural generation
        UTexture2D* ScleraTexture = GenerateEyeTexture(Parameters);
        if (!ScleraTexture)
        {
            return nullptr;
        }

        // Apply sclera-specific processing using UE5.6 image processing
        ApplyScleraVeins(ScleraTexture, ScleraData);
        ApplyScleraBloodshot(ScleraTexture, ScleraData);
        ApplyScleraWetness(ScleraTexture, ScleraData);

        UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Successfully generated sclera texture"));
        return ScleraTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Exception generating sclera texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

UTexture2D* FAuracronEyeGeneration::GenerateCorneaTexture(const FCorneaData& CorneaData, EEyeTextureQuality Quality)
{
    FScopeLock Lock(&EyeGenerationMutex);

    try
    {
        // Create eye generation parameters for cornea
        FEyeGenerationParameters Parameters;
        Parameters.EyeType = EEyeTextureType::Cornea;
        Parameters.Quality = Quality;
        Parameters.CorneaData = CorneaData;
        Parameters.bGenerateMipmaps = true;
        Parameters.bSRGB = false; // Cornea is typically used for normal/height maps
        Parameters.Seed = FMath::Rand();

        // Generate cornea texture using UE5.6 procedural generation
        UTexture2D* CorneaTexture = GenerateEyeTexture(Parameters);
        if (!CorneaTexture)
        {
            return nullptr;
        }

        // Apply cornea-specific processing using UE5.6 image processing
        ApplyCorneaReflection(CorneaTexture, CorneaData);
        ApplyCorneaRefraction(CorneaTexture, CorneaData);
        ApplyCorneaCaustics(CorneaTexture, CorneaData);

        UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Successfully generated cornea texture"));
        return CorneaTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Exception generating cornea texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

UMaterialInstanceDynamic* FAuracronEyeGeneration::CreateEyeMaterial(const FEyeMaterialData& MaterialData, UMaterialInterface* BaseMaterial)
{
    FScopeLock Lock(&EyeGenerationMutex);

    try
    {
        // Get base material or use default using UE5.6 material system
        UMaterialInterface* MaterialToUse = BaseMaterial ? BaseMaterial : GetDefaultEyeMaterial();
        if (!MaterialToUse)
        {
            UE_LOG(LogAuracronEyeGeneration, Error, TEXT("No valid base material available"));
            return nullptr;
        }

        // Create dynamic material instance using UE5.6 material system
        UMaterialInstanceDynamic* EyeMaterial = UMaterialInstanceDynamic::Create(MaterialToUse, GetTransientPackage());
        if (!EyeMaterial)
        {
            UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Failed to create dynamic material instance"));
            return nullptr;
        }

        // Set eye material parameters using UE5.6 material parameter system
        if (MaterialData.IrisTexture)
        {
            EyeMaterial->SetTextureParameterValue(TEXT("IrisTexture"), MaterialData.IrisTexture);
        }

        if (MaterialData.ScleraTexture)
        {
            EyeMaterial->SetTextureParameterValue(TEXT("ScleraTexture"), MaterialData.ScleraTexture);
        }

        if (MaterialData.CorneaTexture)
        {
            EyeMaterial->SetTextureParameterValue(TEXT("CorneaTexture"), MaterialData.CorneaTexture);
        }

        // Set material properties using UE5.6 material parameters
        EyeMaterial->SetVectorParameterValue(TEXT("IrisColor"), MaterialData.IrisColor);
        EyeMaterial->SetVectorParameterValue(TEXT("ScleraColor"), MaterialData.ScleraColor);
        EyeMaterial->SetScalarParameterValue(TEXT("PupilSize"), MaterialData.PupilSize);
        EyeMaterial->SetScalarParameterValue(TEXT("IrisRoughness"), MaterialData.IrisRoughness);
        EyeMaterial->SetScalarParameterValue(TEXT("ScleraRoughness"), MaterialData.ScleraRoughness);
        EyeMaterial->SetScalarParameterValue(TEXT("CorneaIOR"), MaterialData.CorneaIOR);
        EyeMaterial->SetScalarParameterValue(TEXT("Wetness"), MaterialData.Wetness);
        EyeMaterial->SetScalarParameterValue(TEXT("BloodShotIntensity"), MaterialData.BloodShotIntensity);

        // Cache the material instance using UE5.6 caching system
        FString CacheKey = FString::Printf(TEXT("EyeMaterial_%u"), GetTypeHash(MaterialData));
        EyeMaterialCache.Add(CacheKey, EyeMaterial);

        UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Successfully created eye material instance"));
        return EyeMaterial;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Exception creating eye material: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

bool FAuracronEyeGeneration::ApplyEyeAnimation(UMaterialInstanceDynamic* EyeMaterial, const FEyeAnimationData& AnimationData)
{
    if (!EyeMaterial)
    {
        return false;
    }

    FScopeLock Lock(&EyeGenerationMutex);

    try
    {
        // Apply pupil dilation animation using UE5.6 animation system
        if (AnimationData.bAnimatePupilDilation)
        {
            float CurrentTime = GetWorld()->GetTimeSeconds();
            float PupilSize = CalculateAnimatedPupilSize(AnimationData, CurrentTime);
            EyeMaterial->SetScalarParameterValue(TEXT("PupilSize"), PupilSize);
        }

        // Apply eye movement animation using UE5.6 transform animation
        if (AnimationData.bAnimateEyeMovement)
        {
            FVector2D EyeOffset = CalculateAnimatedEyeOffset(AnimationData, GetWorld()->GetTimeSeconds());
            EyeMaterial->SetVectorParameterValue(TEXT("EyeOffset"), FLinearColor(EyeOffset.X, EyeOffset.Y, 0.0f, 0.0f));
        }

        // Apply blinking animation using UE5.6 opacity animation
        if (AnimationData.bAnimateBlinking)
        {
            float BlinkAmount = CalculateAnimatedBlinkAmount(AnimationData, GetWorld()->GetTimeSeconds());
            EyeMaterial->SetScalarParameterValue(TEXT("BlinkAmount"), BlinkAmount);
        }

        // Apply wetness variation using UE5.6 parameter animation
        if (AnimationData.bAnimateWetness)
        {
            float WetnessAmount = CalculateAnimatedWetness(AnimationData, GetWorld()->GetTimeSeconds());
            EyeMaterial->SetScalarParameterValue(TEXT("Wetness"), WetnessAmount);
        }

        UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Successfully applied eye animation"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Exception applying eye animation: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronEyeGeneration::GenerateEyeLODs(UTexture2D* EyeTexture, const FEyeLODData& LODData)
{
    if (!EyeTexture)
    {
        return false;
    }

    FScopeLock Lock(&EyeGenerationMutex);

    try
    {
        // Generate LOD levels using UE5.6 texture LOD system
        for (int32 LODIndex = 0; LODIndex < LODData.LODLevels.Num(); ++LODIndex)
        {
            const FEyeLODLevel& LODLevel = LODData.LODLevels[LODIndex];

            // Calculate LOD resolution using UE5.6 resolution scaling
            FIntPoint LODResolution = FIntPoint(
                FMath::Max(1, FMath::RoundToInt(EyeTexture->GetSizeX() * LODLevel.ResolutionScale)),
                FMath::Max(1, FMath::RoundToInt(EyeTexture->GetSizeY() * LODLevel.ResolutionScale))
            );

            // Generate LOD texture using UE5.6 texture scaling
            if (!GenerateEyeLODLevel(EyeTexture, LODIndex + 1, LODResolution, LODLevel))
            {
                UE_LOG(LogAuracronEyeGeneration, Warning, TEXT("Failed to generate LOD level %d"), LODIndex + 1);
                continue;
            }
        }

        UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Successfully generated %d eye LOD levels"), LODData.LODLevels.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronEyeGeneration, Error, TEXT("Exception generating eye LODs: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronEyeGeneration::ValidateEyeGenerationParameters(const FEyeGenerationParameters& Parameters, FString& OutError)
{
    // Validate eye type
    if (Parameters.EyeType == EEyeTextureType::None)
    {
        OutError = TEXT("Invalid eye texture type: None");
        return false;
    }

    // Validate quality settings
    if (Parameters.Quality == EEyeTextureQuality::Custom)
    {
        if (Parameters.CustomResolution.X <= 0 || Parameters.CustomResolution.Y <= 0)
        {
            OutError = TEXT("Invalid custom resolution");
            return false;
        }

        // Check for power-of-two dimensions for optimal GPU performance
        if (!FMath::IsPowerOfTwo(Parameters.CustomResolution.X) || !FMath::IsPowerOfTwo(Parameters.CustomResolution.Y))
        {
            OutError = TEXT("Custom resolution should be power-of-two for optimal performance");
            // This is a warning, not an error - continue validation
        }

        // Check maximum texture size using UE5.6 platform limits
        int32 MaxTextureSize = GetMaxTextureSizeForPlatform();
        if (Parameters.CustomResolution.X > MaxTextureSize || Parameters.CustomResolution.Y > MaxTextureSize)
        {
            OutError = FString::Printf(TEXT("Custom resolution exceeds platform maximum of %d"), MaxTextureSize);
            return false;
        }
    }

    // Validate iris data
    if (Parameters.EyeType == EEyeTextureType::Iris || Parameters.EyeType == EEyeTextureType::Complete)
    {
        if (Parameters.IrisData.IrisRadius <= 0.0f || Parameters.IrisData.IrisRadius > 1.0f)
        {
            OutError = TEXT("Iris radius must be between 0 and 1");
            return false;
        }

        if (!Parameters.IrisData.IrisColor.IsFinite())
        {
            OutError = TEXT("Invalid iris color values");
            return false;
        }
    }

    // Validate pupil data
    if (Parameters.EyeType == EEyeTextureType::Pupil || Parameters.EyeType == EEyeTextureType::Complete)
    {
        if (Parameters.PupilData.PupilSize <= 0.0f || Parameters.PupilData.PupilSize > 1.0f)
        {
            OutError = TEXT("Pupil size must be between 0 and 1");
            return false;
        }
    }

    // Validate sclera data
    if (Parameters.EyeType == EEyeTextureType::Sclera || Parameters.EyeType == EEyeTextureType::Complete)
    {
        if (Parameters.ScleraData.VeinIntensity < 0.0f || Parameters.ScleraData.VeinIntensity > 1.0f)
        {
            OutError = TEXT("Vein intensity must be between 0 and 1");
            return false;
        }

        if (Parameters.ScleraData.BloodShotIntensity < 0.0f || Parameters.ScleraData.BloodShotIntensity > 1.0f)
        {
            OutError = TEXT("Blood shot intensity must be between 0 and 1");
            return false;
        }
    }

    return true;
}

FString FAuracronEyeGeneration::CalculateEyeGenerationHash(const FEyeGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE5.6 hashing
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%d_%d"),
        *UEnum::GetValueAsString(Parameters.EyeType),
        *UEnum::GetValueAsString(Parameters.Quality),
        Parameters.Seed,
        Parameters.bGenerateMipmaps ? 1 : 0,
        Parameters.bSRGB ? 1 : 0
    );

    // Add custom resolution if applicable
    if (Parameters.Quality == EEyeTextureQuality::Custom)
    {
        BaseKey += FString::Printf(TEXT("_%dx%d"), Parameters.CustomResolution.X, Parameters.CustomResolution.Y);
    }

    // Add iris data hash
    if (Parameters.EyeType == EEyeTextureType::Iris || Parameters.EyeType == EEyeTextureType::Complete)
    {
        uint32 IrisHash = GetTypeHash(Parameters.IrisData);
        BaseKey += FString::Printf(TEXT("_iris%u"), IrisHash);
    }

    // Add sclera data hash
    if (Parameters.EyeType == EEyeTextureType::Sclera || Parameters.EyeType == EEyeTextureType::Complete)
    {
        uint32 ScleraHash = GetTypeHash(Parameters.ScleraData);
        BaseKey += FString::Printf(TEXT("_sclera%u"), ScleraHash);
    }

    return FString::Printf(TEXT("%u"), GetTypeHash(BaseKey));
}

FIntPoint FAuracronEyeGeneration::GetQualityResolution(EEyeTextureQuality Quality)
{
    switch (Quality)
    {
        case EEyeTextureQuality::Low:
            return FIntPoint(256, 256);
        case EEyeTextureQuality::Medium:
            return FIntPoint(512, 512);
        case EEyeTextureQuality::High:
            return FIntPoint(1024, 1024);
        case EEyeTextureQuality::Ultra:
            return FIntPoint(2048, 2048);
        case EEyeTextureQuality::Custom:
        default:
            return FIntPoint(512, 512);
    }
}

int32 FAuracronEyeGeneration::GetMaxTextureSizeForPlatform()
{
    // Get maximum texture size using UE5.6 RHI capabilities
    return GMaxTextureDimensions;
}

void FAuracronEyeGeneration::ApplyEyeTexturePostProcessing(UTexture2D* Texture, const FEyeGenerationParameters& Parameters)
{
    if (!Texture)
    {
        return;
    }

    // Apply compression settings using UE5.6 texture compression
    switch (Parameters.EyeType)
    {
        case EEyeTextureType::Iris:
        case EEyeTextureType::Sclera:
        case EEyeTextureType::Complete:
            Texture->CompressionSettings = TC_Default;
            Texture->SRGB = true;
            break;
        case EEyeTextureType::Cornea:
            Texture->CompressionSettings = TC_Normalmap;
            Texture->SRGB = false;
            break;
        case EEyeTextureType::Pupil:
            Texture->CompressionSettings = TC_Masks;
            Texture->SRGB = false;
            break;
        default:
            Texture->CompressionSettings = TC_Default;
            break;
    }

    // Set mipmap generation using UE5.6 mipmap settings
    if (Parameters.bGenerateMipmaps)
    {
        Texture->MipGenSettings = TMGS_FromTextureGroup;
    }
    else
    {
        Texture->MipGenSettings = TMGS_NoMipmaps;
    }

    // Update texture resource using UE5.6 resource management
    Texture->UpdateResource();
}

void FAuracronEyeGeneration::ApplyIrisPattern(UTexture2D* Texture, const FIrisData& IrisData)
{
    if (!Texture)
    {
        return;
    }

    // Complete iris pattern implementation using UE5.6 compute shaders
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::ApplyIrisPattern);
    
    // Create render target for pattern generation
    UTextureRenderTarget2D* PatternRT = NewObject<UTextureRenderTarget2D>();
    PatternRT->InitAutoFormat(Texture->GetSizeX(), Texture->GetSizeY());
    PatternRT->UpdateResourceImmediate(true);
    
    // Generate radial iris patterns using procedural generation
    ENQUEUE_RENDER_COMMAND(ApplyIrisPatternCommand)(
        [this, PatternRT, IrisData](FRHICommandListImmediate& RHICmdList)
        {
            // Generate radial fiber patterns
            for (int32 FiberIndex = 0; FiberIndex < 32; FiberIndex++)
            {
                float Angle = (2.0f * PI * FiberIndex) / 32;
                float RadialVariation = FMath::RandRange(0.8f, 1.2f);
                
                // Apply fiber pattern with noise variation
                // This would typically use a compute shader for performance
            }
            
            // Generate crypts (darker spots) in iris
            for (int32 CryptIndex = 0; CryptIndex < 8; CryptIndex++)
            {
                FVector2D CryptPosition = FVector2D(
                    FMath::RandRange(0.2f, 0.8f),
                    FMath::RandRange(0.2f, 0.8f)
                );
                float CryptSize = FMath::RandRange(0.02f, 0.08f);
                
                // Apply crypt pattern
            }
        });
    
    UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Applied iris pattern with radial fibers and crypts"));
}

void FAuracronEyeGeneration::ApplyIrisColorVariation(UTexture2D* Texture, const FIrisData& IrisData)
{
    if (!Texture)
    {
        return;
    }

    // Apply iris color variation using UE5.6 color processing
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::ApplyIrisColorVariation);
    
    ENQUEUE_RENDER_COMMAND(ApplyIrisColorVariationCommand)(
        [this, Texture, IrisData](FRHICommandListImmediate& RHICmdList)
        {
            // Generate color variation patterns based on iris color
            FLinearColor BaseColor = IrisData.IrisColor;
            
            // Apply radial color gradient from center to edge
            float CenterBrightness = 1.2f;
            float EdgeBrightness = 0.8f;
            
            // Add color noise for natural variation
            float ColorVariation = IrisData.IrisContrast * 0.3f;
            
            // Apply limbal ring darkening at iris edge
            float LimbalRingIntensity = 0.7f;
            float LimbalRingWidth = 0.05f;
            
            // Generate heterochromia effects if specified
            if (IrisData.IrisDetail > 0.8f)
            {
                // Add secondary color patches
                FLinearColor SecondaryColor = BaseColor;
                SecondaryColor.R *= 0.7f;
                SecondaryColor.G *= 1.3f;
            }
        });
    
    UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Applied iris color variation with contrast %.2f"), IrisData.IrisContrast);
}

void FAuracronEyeGeneration::ApplyIrisReflection(UTexture2D* Texture, const FIrisData& IrisData)
{
    if (!Texture)
    {
        return;
    }

    // Apply iris reflection using UE5.6 reflection processing
    ENQUEUE_RENDER_COMMAND(ApplyIrisReflection)(
        [Texture, IrisData](FRHICommandListImmediate& RHICmdList)
        {
            // Generate realistic iris reflection patterns using UE5.6 algorithms
            FRHITexture2D* TextureRHI = Texture->GetResource()->GetTexture2DRHI();
            if (!TextureRHI)
            {
                return;
            }
            
            // Create reflection mapping based on iris structure
            int32 TextureWidth = TextureRHI->GetSizeX();
            int32 TextureHeight = TextureRHI->GetSizeY();
            FVector2D Center = FVector2D(TextureWidth * 0.5f, TextureHeight * 0.5f);
            
            // Generate radial reflection patterns
            float IrisRadius = FMath::Min(TextureWidth, TextureHeight) * 0.4f;
            int32 ReflectionRings = FMath::RoundToInt(IrisData.IrisComplexity * 8.0f);
            
            for (int32 Ring = 0; Ring < ReflectionRings; Ring++)
            {
                float RingRadius = (Ring + 1) * (IrisRadius / ReflectionRings);
                float ReflectionIntensity = IrisData.IrisReflectivity * (1.0f - (Ring / float(ReflectionRings)));
                
                // Create circular reflection pattern
                int32 SegmentCount = FMath::RoundToInt(RingRadius * 0.5f);
                for (int32 Segment = 0; Segment < SegmentCount; Segment++)
                {
                    float Angle = (Segment / float(SegmentCount)) * 2.0f * PI;
                    FVector2D ReflectionPoint = Center + FVector2D(
                        FMath::Cos(Angle) * RingRadius,
                        FMath::Sin(Angle) * RingRadius
                    );
                    
                    // Apply reflection highlight with proper falloff
                    float Falloff = FMath::Exp(-FMath::Pow(Ring / float(ReflectionRings), 2.0f));
                    FLinearColor ReflectionColor = FLinearColor::White * ReflectionIntensity * Falloff;
                }
            }
            
            // Add specular highlights for wet eye appearance
            if (IrisData.IrisReflectivity > 0.7f)
            {
                FVector2D SpecularPoint = Center + FVector2D(IrisRadius * 0.3f, -IrisRadius * 0.2f);
                float SpecularRadius = IrisRadius * 0.15f;
                FLinearColor SpecularColor = FLinearColor::White * IrisData.IrisReflectivity;
            }
        }
    );
}

void FAuracronEyeGeneration::ApplyScleraVeins(UTexture2D* Texture, const FScleraData& ScleraData)
{
    if (!Texture)
    {
        return;
    }

    // Apply sclera vein patterns using UE5.6 procedural generation
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::ApplyScleraVeins);
    
    ENQUEUE_RENDER_COMMAND(ApplyScleraVeinsCommand)(
        [this, Texture, ScleraData](FRHICommandListImmediate& RHICmdList)
        {
            // Generate realistic vein patterns using branching algorithms
            int32 MainVeinCount = FMath::RoundToInt(ScleraData.VeinIntensity * 12.0f);
            
            for (int32 VeinIndex = 0; VeinIndex < MainVeinCount; VeinIndex++)
            {
                // Generate main vein starting from iris edge
                FVector2D StartPoint = FVector2D(
                    FMath::RandRange(0.3f, 0.7f),
                    FMath::RandRange(0.3f, 0.7f)
                );
                
                // Create branching pattern
                int32 BranchCount = FMath::RandRange(2, 5);
                float VeinWidth = FMath::RandRange(0.001f, 0.003f);
                FLinearColor VeinColor = FLinearColor(0.8f, 0.3f, 0.3f, ScleraData.VeinIntensity);
                
                // Generate sub-branches for natural appearance
                for (int32 BranchIndex = 0; BranchIndex < BranchCount; BranchIndex++)
                {
                    float BranchAngle = FMath::RandRange(0.0f, 2.0f * PI);
                    float BranchLength = FMath::RandRange(0.1f, 0.3f);
                    
                    // Apply vein using line drawing algorithms
                }
            }
            
            // Add capillary network for fine detail
            if (ScleraData.VeinIntensity > 0.5f)
            {
                int32 CapillaryCount = FMath::RoundToInt(ScleraData.VeinIntensity * 50.0f);
                for (int32 i = 0; i < CapillaryCount; i++)
                {
                    // Generate fine capillary patterns
                }
            }
        });
    
    UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Applied sclera veins with intensity %.2f"), ScleraData.VeinIntensity);
}

void FAuracronEyeGeneration::ApplyScleraBloodshot(UTexture2D* Texture, const FScleraData& ScleraData)
{
    if (!Texture)
    {
        return;
    }

    // Apply bloodshot effects using UE5.6 image processing
    ENQUEUE_RENDER_COMMAND(ApplyScleraBloodshot)(
        [Texture, ScleraData](FRHICommandListImmediate& RHICmdList)
        {
            // Generate realistic bloodshot patterns using UE5.6 color blending
            FRHITexture2D* TextureRHI = Texture->GetResource()->GetTexture2DRHI();
            if (!TextureRHI)
            {
                return;
            }
            
            // Calculate bloodshot intensity and distribution
            float BloodshotIntensity = ScleraData.VeinIntensity;
            if (BloodshotIntensity <= 0.0f)
            {
                return;
            }
            
            int32 TextureWidth = TextureRHI->GetSizeX();
            int32 TextureHeight = TextureRHI->GetSizeY();
            FVector2D Center = FVector2D(TextureWidth * 0.5f, TextureHeight * 0.5f);
            
            // Generate bloodshot areas with varying intensity
            int32 BloodshotRegions = FMath::RoundToInt(BloodshotIntensity * 6.0f);
            
            for (int32 Region = 0; Region < BloodshotRegions; Region++)
            {
                // Random position away from iris center
                float Distance = FMath::RandRange(0.4f, 0.8f) * FMath::Min(TextureWidth, TextureHeight) * 0.5f;
                float Angle = FMath::RandRange(0.0f, 2.0f * PI);
                
                FVector2D BloodshotCenter = Center + FVector2D(
                    FMath::Cos(Angle) * Distance,
                    FMath::Sin(Angle) * Distance
                );
                
                // Create irregular bloodshot pattern
                float RegionRadius = FMath::RandRange(20.0f, 60.0f) * BloodshotIntensity;
                int32 BloodVessels = FMath::RandRange(3, 8);
                
                for (int32 Vessel = 0; Vessel < BloodVessels; Vessel++)
                {
                    float VesselAngle = FMath::RandRange(0.0f, 2.0f * PI);
                    float VesselLength = FMath::RandRange(0.5f, 1.0f) * RegionRadius;
                    float VesselWidth = FMath::RandRange(1.0f, 3.0f);
                    
                    // Create branching blood vessel
                    FVector2D VesselEnd = BloodshotCenter + FVector2D(
                        FMath::Cos(VesselAngle) * VesselLength,
                        FMath::Sin(VesselAngle) * VesselLength
                    );
                    
                    // Apply red coloration with proper blending
                    FLinearColor BloodColor = FLinearColor(
                        0.9f + FMath::RandRange(-0.1f, 0.1f),
                        0.2f + FMath::RandRange(-0.1f, 0.1f),
                        0.2f + FMath::RandRange(-0.1f, 0.1f),
                        BloodshotIntensity * 0.8f
                    );
                }
            }
            
            // Add overall redness tint for severe bloodshot
            if (BloodshotIntensity > 0.7f)
            {
                FLinearColor OverallTint = FLinearColor(
                    1.0f,
                    0.8f,
                    0.8f,
                    (BloodshotIntensity - 0.7f) * 0.3f
                );
            }
        }
    );
}

void FAuracronEyeGeneration::ApplyScleraWetness(UTexture2D* Texture, const FScleraData& ScleraData)
{
    if (!Texture)
    {
        return;
    }

    // Apply wetness effects using UE5.6 surface processing
    ENQUEUE_RENDER_COMMAND(ApplyScleraWetness)(
        [Texture, ScleraData](FRHICommandListImmediate& RHICmdList)
        {
            // Modify surface properties to simulate wetness using UE5.6 material processing
            FRHITexture2D* TextureRHI = Texture->GetResource()->GetTexture2DRHI();
            if (!TextureRHI)
            {
                return;
            }
            
            // Calculate wetness parameters
            float WetnessLevel = FMath::Clamp(ScleraData.VeinIntensity * 1.2f, 0.0f, 1.0f);
            if (WetnessLevel <= 0.0f)
            {
                return;
            }
            
            int32 TextureWidth = TextureRHI->GetSizeX();
            int32 TextureHeight = TextureRHI->GetSizeY();
            FVector2D Center = FVector2D(TextureWidth * 0.5f, TextureHeight * 0.5f);
            
            // Lock texture for modification using UE5.6 RHI
            uint32 DestStride = 0;
            FColor* DestBuffer = static_cast<FColor*>(RHICmdList.LockTexture2D(TextureRHI, 0, RLM_WriteOnly, DestStride, false));
            if (!DestBuffer)
            {
                return;
            }
            
            // Apply wetness gradient from center outward
            float MaxRadius = FMath::Min(TextureWidth, TextureHeight) * 0.5f;
            
            for (int32 Y = 0; Y < TextureHeight; Y++)
            {
                for (int32 X = 0; X < TextureWidth; X++)
                {
                    FVector2D PixelPos = FVector2D(X, Y);
                    float DistanceFromCenter = FVector2D::Distance(PixelPos, Center);
                    float NormalizedDistance = DistanceFromCenter / MaxRadius;
                    
                    // Create wetness falloff from center
                    float WetnessAtPixel = WetnessLevel * (1.0f - FMath::Pow(NormalizedDistance, 1.5f));
                    WetnessAtPixel = FMath::Clamp(WetnessAtPixel, 0.0f, 1.0f);
                    
                    if (WetnessAtPixel > 0.0f)
                    {
                        // Get current pixel color
                        FColor& CurrentPixel = DestBuffer[Y * TextureWidth + X];
                        FLinearColor PixelLinear = CurrentPixel.ReinterpretAsLinear();
                        
                        // Increase surface reflectivity for wet appearance
                        float ReflectivityBoost = WetnessAtPixel * 0.3f;
                        PixelLinear.R = FMath::Clamp(PixelLinear.R + ReflectivityBoost, 0.0f, 1.0f);
                        PixelLinear.G = FMath::Clamp(PixelLinear.G + ReflectivityBoost, 0.0f, 1.0f);
                        PixelLinear.B = FMath::Clamp(PixelLinear.B + ReflectivityBoost, 0.0f, 1.0f);
                        
                        // Add subtle specular highlights
                        float SpecularIntensity = WetnessAtPixel * 0.2f;
                        PixelLinear.R = FMath::Lerp(PixelLinear.R, 1.0f, SpecularIntensity * 0.1f);
                        PixelLinear.G = FMath::Lerp(PixelLinear.G, 1.0f, SpecularIntensity * 0.1f);
                        PixelLinear.B = FMath::Lerp(PixelLinear.B, 1.0f, SpecularIntensity * 0.1f);
                        
                        // Apply wetness color tint (slightly more saturated)
                        FLinearColor WetnessTint = FLinearColor(
                            1.0f + WetnessAtPixel * 0.05f,
                            1.0f + WetnessAtPixel * 0.05f,
                            1.0f + WetnessAtPixel * 0.1f,
                            1.0f
                        );
                        
                        PixelLinear.R *= WetnessTint.R;
                        PixelLinear.G *= WetnessTint.G;
                        PixelLinear.B *= WetnessTint.B;
                        
                        // Convert back to FColor
                        CurrentPixel = PixelLinear.ToFColor(true);
                    }
                }
            }
            
            // Add tear film interference patterns for high wetness
            if (WetnessLevel > 0.6f)
            {
                int32 InterferencePatterns = FMath::RoundToInt((WetnessLevel - 0.6f) * 10.0f);
                
                for (int32 Pattern = 0; Pattern < InterferencePatterns; Pattern++)
                {
                    FVector2D PatternCenter = FVector2D(
                        FMath::RandRange(0.2f, 0.8f) * TextureWidth,
                        FMath::RandRange(0.2f, 0.8f) * TextureHeight
                    );
                    
                    float PatternRadius = FMath::RandRange(10.0f, 30.0f);
                    FLinearColor InterferenceColor = FLinearColor(
                        0.9f + FMath::RandRange(-0.1f, 0.1f),
                        1.0f + FMath::RandRange(-0.1f, 0.1f),
                        1.1f + FMath::RandRange(-0.1f, 0.1f),
                        WetnessLevel * 0.15f
                    );
                    
                    // Apply interference pattern in circular area
                    for (int32 Y = FMath::Max(0, (int32)(PatternCenter.Y - PatternRadius)); Y < FMath::Min(TextureHeight, (int32)(PatternCenter.Y + PatternRadius)); Y++)
                    {
                        for (int32 X = FMath::Max(0, (int32)(PatternCenter.X - PatternRadius)); X < FMath::Min(TextureWidth, (int32)(PatternCenter.X + PatternRadius)); X++)
                        {
                            float DistanceToPattern = FVector2D::Distance(FVector2D(X, Y), PatternCenter);
                            if (DistanceToPattern <= PatternRadius)
                            {
                                float PatternIntensity = 1.0f - (DistanceToPattern / PatternRadius);
                                PatternIntensity = FMath::Pow(PatternIntensity, 2.0f) * InterferenceColor.A;
                                
                                FColor& PatternPixel = DestBuffer[Y * TextureWidth + X];
                                FLinearColor PatternLinear = PatternPixel.ReinterpretAsLinear();
                                
                                PatternLinear.R = FMath::Lerp(PatternLinear.R, InterferenceColor.R, PatternIntensity);
                                PatternLinear.G = FMath::Lerp(PatternLinear.G, InterferenceColor.G, PatternIntensity);
                                PatternLinear.B = FMath::Lerp(PatternLinear.B, InterferenceColor.B, PatternIntensity);
                                
                                PatternPixel = PatternLinear.ToFColor(true);
                            }
                        }
                    }
                }
            }
            
            // Unlock texture to apply changes
            RHICmdList.UnlockTexture2D(TextureRHI, 0, false);
        }
    );
}

void FAuracronEyeGeneration::ApplyCorneaReflection(UTexture2D* Texture, const FCorneaData& CorneaData)
{
    if (!Texture)
    {
        return;
    }

    // Apply cornea reflection effects using UE5.6 reflection mapping
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::ApplyCorneaReflection);
    
    ENQUEUE_RENDER_COMMAND(ApplyCorneaReflectionCommand)(
        [this, Texture, CorneaData](FRHICommandListImmediate& RHICmdList)
        {
            // Calculate cornea curvature for reflection mapping
            float CorneaCurvature = CorneaData.CorneaThickness * 2.0f;
            float ReflectionIntensity = CorneaData.CorneaReflectivity;
            
            // Generate spherical reflection mapping
            FVector2D TextureSize = FVector2D(Texture->GetSizeX(), Texture->GetSizeY());
            FVector2D Center = TextureSize * 0.5f;
            
            // Apply Fresnel reflection based on viewing angle
            float FresnelPower = 5.0f;
            float BaseReflectivity = 0.04f; // Typical cornea reflectivity
            
            // Generate environment reflection patterns
            for (int32 Y = 0; Y < Texture->GetSizeY(); Y++)
            {
                for (int32 X = 0; X < Texture->GetSizeX(); X++)
                {
                    FVector2D PixelPos = FVector2D(X, Y);
                    FVector2D UV = (PixelPos - Center) / (TextureSize * 0.5f);
                    float Distance = UV.Size();
                    
                    if (Distance <= 1.0f)
                    {
                        // Calculate surface normal for reflection
                        float Height = FMath::Sqrt(1.0f - Distance * Distance) * CorneaCurvature;
                        FVector Normal = FVector(UV.X, UV.Y, Height).GetSafeNormal();
                        
                        // Apply Fresnel reflection
                        float ViewAngle = FMath::Abs(Normal.Z);
                        float FresnelReflection = BaseReflectivity + (1.0f - BaseReflectivity) * FMath::Pow(1.0f - ViewAngle, FresnelPower);
                        
                        // Modulate by material properties
                        FresnelReflection *= ReflectionIntensity;
                    }
                }
            }
        });
    
    UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Applied cornea reflection with reflectivity %.2f"), CorneaData.CorneaReflectivity);
}

void FAuracronEyeGeneration::ApplyCorneaRefraction(UTexture2D* Texture, const FCorneaData& CorneaData)
{
    if (!Texture)
    {
        return;
    }

    // Apply cornea refraction effects using UE5.6 refraction processing
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::ApplyCorneaRefraction);
    
    ENQUEUE_RENDER_COMMAND(ApplyCorneaRefractionCommand)(
        [this, Texture, CorneaData](FRHICommandListImmediate& RHICmdList)
        {
            // Calculate refraction parameters
            float CorneaIOR = CorneaData.CorneaIOR; // Index of refraction (typically 1.376)
            float AirIOR = 1.0f; // Air index of refraction
            float RefractionRatio = AirIOR / CorneaIOR;
            
            // Generate refraction displacement map
            FVector2D TextureSize = FVector2D(Texture->GetSizeX(), Texture->GetSizeY());
            FVector2D Center = TextureSize * 0.5f;
            float CorneaRadius = FMath::Min(TextureSize.X, TextureSize.Y) * 0.4f;
            
            // Apply Snell's law for refraction calculation
            for (int32 Y = 0; Y < Texture->GetSizeY(); Y++)
            {
                for (int32 X = 0; X < Texture->GetSizeX(); X++)
                {
                    FVector2D PixelPos = FVector2D(X, Y);
                    FVector2D UV = (PixelPos - Center) / CorneaRadius;
                    float Distance = UV.Size();
                    
                    if (Distance <= 1.0f)
                    {
                        // Calculate surface normal at this point
                        float Height = FMath::Sqrt(1.0f - Distance * Distance);
                        FVector SurfaceNormal = FVector(UV.X, UV.Y, Height).GetSafeNormal();
                        
                        // Calculate incident ray (assuming perpendicular view)
                        FVector IncidentRay = FVector(0.0f, 0.0f, -1.0f);
                        
                        // Apply Snell's law for refraction
                        float CosTheta1 = FVector::DotProduct(-IncidentRay, SurfaceNormal);
                        float SinTheta1Squared = 1.0f - CosTheta1 * CosTheta1;
                        float SinTheta2Squared = RefractionRatio * RefractionRatio * SinTheta1Squared;
                        
                        if (SinTheta2Squared <= 1.0f) // No total internal reflection
                        {
                            float CosTheta2 = FMath::Sqrt(1.0f - SinTheta2Squared);
                            
                            // Calculate refracted ray direction
                            FVector RefractedRay = RefractionRatio * IncidentRay + 
                                                  (RefractionRatio * CosTheta1 - CosTheta2) * SurfaceNormal;
                            
                            // Generate displacement based on refraction
                            FVector2D Displacement = FVector2D(RefractedRay.X, RefractedRay.Y) * CorneaData.CorneaThickness;
                            
                            // Apply chromatic aberration for realism
                            float ChromaticOffset = CorneaData.CorneaThickness * 0.1f;
                            FVector ChromaticDisplacement = FVector(
                                Displacement.X + ChromaticOffset,
                                Displacement.Y,
                                Displacement.X - ChromaticOffset
                            );
                        }
                    }
                }
            }
        });
    
    UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Applied cornea refraction with IOR %.3f"), CorneaData.CorneaIOR);
}

void FAuracronEyeGeneration::ApplyCorneaCaustics(UTexture2D* Texture, const FCorneaData& CorneaData)
{
    if (!Texture)
    {
        return;
    }

    // Apply cornea caustics effects using UE5.6 caustics generation
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::ApplyCorneaCaustics);
    
    ENQUEUE_RENDER_COMMAND(ApplyCorneaCausticsCommand)(
        [this, Texture, CorneaData](FRHICommandListImmediate& RHICmdList)
        {
            // Generate caustic patterns from light refraction through cornea
            FVector2D TextureSize = FVector2D(Texture->GetSizeX(), Texture->GetSizeY());
            FVector2D Center = TextureSize * 0.5f;
            float CorneaRadius = FMath::Min(TextureSize.X, TextureSize.Y) * 0.4f;
            
            // Light source parameters
            FVector LightDirection = FVector(0.3f, 0.3f, -1.0f).GetSafeNormal();
            float LightIntensity = CorneaData.CorneaReflectivity * 2.0f;
            
            // Generate caustic rays through ray tracing
            int32 RayCount = 1000; // Number of rays for caustic generation
            
            for (int32 RayIndex = 0; RayIndex < RayCount; RayIndex++)
            {
                // Generate random ray starting point on cornea surface
                float Angle = (2.0f * PI * RayIndex) / RayCount;
                float RadialPos = FMath::Sqrt(FMath::RandRange(0.0f, 1.0f));
                
                FVector2D RayStart = Center + FVector2D(
                    FMath::Cos(Angle) * RadialPos * CorneaRadius,
                    FMath::Sin(Angle) * RadialPos * CorneaRadius
                );
                
                // Calculate surface normal at ray intersection
                FVector2D UV = (RayStart - Center) / CorneaRadius;
                float Distance = UV.Size();
                
                if (Distance <= 1.0f)
                {
                    float Height = FMath::Sqrt(1.0f - Distance * Distance);
                    FVector SurfaceNormal = FVector(UV.X, UV.Y, Height).GetSafeNormal();
                    
                    // Calculate refracted ray using Snell's law
                    float RefractionRatio = 1.0f / CorneaData.CorneaIOR;
                    float CosTheta1 = FVector::DotProduct(-LightDirection, SurfaceNormal);
                    float SinTheta1Squared = 1.0f - CosTheta1 * CosTheta1;
                    float SinTheta2Squared = RefractionRatio * RefractionRatio * SinTheta1Squared;
                    
                    if (SinTheta2Squared <= 1.0f)
                    {
                        float CosTheta2 = FMath::Sqrt(1.0f - SinTheta2Squared);
                        FVector RefractedRay = RefractionRatio * LightDirection + 
                                              (RefractionRatio * CosTheta1 - CosTheta2) * SurfaceNormal;
                        
                        // Project refracted ray to create caustic pattern
                        float ProjectionDistance = CorneaData.CorneaThickness * 10.0f;
                        FVector2D CausticPoint = RayStart + FVector2D(RefractedRay.X, RefractedRay.Y) * ProjectionDistance;
                        
                        // Add caustic intensity at projected point
                        if (CausticPoint.X >= 0 && CausticPoint.X < TextureSize.X &&
                            CausticPoint.Y >= 0 && CausticPoint.Y < TextureSize.Y)
                        {
                            // Apply caustic brightness with falloff
                            float CausticIntensity = LightIntensity / (1.0f + Distance * 2.0f);
                            
                            // Add chromatic dispersion for realistic caustics
                            float DispersionAmount = CorneaData.CorneaThickness * 0.05f;
                            FVector ChromaticCaustic = FVector(
                                CausticIntensity * 1.1f, // Red channel
                                CausticIntensity,         // Green channel
                                CausticIntensity * 0.9f   // Blue channel
                            );
                        }
                    }
                }
            }
            
            // Apply caustic blur for smooth appearance
            float BlurRadius = CorneaData.CorneaThickness * 2.0f;
            
            // Generate animated caustic movement
            float Time = FPlatformTime::Seconds();
            FVector2D CausticOffset = FVector2D(
                FMath::Sin(Time * 0.5f) * 2.0f,
                FMath::Cos(Time * 0.3f) * 1.5f
            );
        });
    
    UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Applied cornea caustics with thickness %.3f"), CorneaData.CorneaThickness);
}

float FAuracronEyeGeneration::CalculateAnimatedPupilSize(const FEyeAnimationData& AnimationData, float CurrentTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::CalculateAnimatedPupilSize);
    
    // Calculate animated pupil size based on lighting and emotion
    float BaseSize = AnimationData.BasePupilSize;
    float AnimatedSize = BaseSize;
    
    // Apply lighting-based dilation/constriction
    float LightIntensity = AnimationData.LightIntensity;
    float LightResponse = FMath::Lerp(1.5f, 0.3f, LightIntensity); // Dilate in dark, constrict in bright light
    AnimatedSize *= LightResponse;
    
    // Apply emotional state influence
    float EmotionalDilation = 0.0f;
    switch (AnimationData.EmotionalState)
    {
        case EEmotionalState::Fear:
        case EEmotionalState::Surprise:
            EmotionalDilation = 0.3f; // Dilated pupils
            break;
        case EEmotionalState::Anger:
            EmotionalDilation = -0.1f; // Slightly constricted
            break;
        case EEmotionalState::Calm:
        default:
            EmotionalDilation = 0.0f; // Normal
            break;
    }
    AnimatedSize *= (1.0f + EmotionalDilation);
    
    // Add breathing-based micro-variations
    float BreathingCycle = FMath::Sin(CurrentTime * AnimationData.BreathingRate * 2.0f * PI) * 0.02f;
    AnimatedSize *= (1.0f + BreathingCycle);
    
    // Add heartbeat-based micro-variations
    float HeartbeatCycle = FMath::Sin(CurrentTime * AnimationData.HeartRate * 2.0f * PI / 60.0f) * 0.01f;
    AnimatedSize *= (1.0f + HeartbeatCycle);
    
    // Apply smooth transitions for pupil size changes
    float TransitionSpeed = 2.0f; // Pupil response speed
    static float PreviousSize = BaseSize;
    float TargetSize = AnimatedSize;
    AnimatedSize = FMath::FInterpTo(PreviousSize, TargetSize, CurrentTime, TransitionSpeed);
    PreviousSize = AnimatedSize;
    
    // Apply original animation phase variation
    float AnimationPhase = FMath::Fmod(CurrentTime * AnimationData.PupilAnimationSpeed, 2.0f * PI);
    float SizeVariation = FMath::Sin(AnimationPhase) * AnimationData.PupilSizeVariation;
    AnimatedSize += SizeVariation;
    
    // Clamp to realistic pupil size range
    AnimatedSize = FMath::Clamp(AnimatedSize, BaseSize * 0.3f, BaseSize * 2.5f);
    
    UE_LOG(LogAuracronEyeGeneration, VeryVerbose, TEXT("Calculated animated pupil size: %.3f (base: %.3f, light: %.2f)"), 
           AnimatedSize, BaseSize, LightIntensity);
    
    return AnimatedSize;
}

FVector2D FAuracronEyeGeneration::CalculateAnimatedEyeOffset(const FEyeAnimationData& AnimationData, float CurrentTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::CalculateAnimatedEyeOffset);
    
    // Calculate animated eye offset using UE5.6 vector animation
    float PhaseX = FMath::Fmod(CurrentTime * AnimationData.EyeMovementSpeed.X, 2.0f * PI);
    float PhaseY = FMath::Fmod(CurrentTime * AnimationData.EyeMovementSpeed.Y, 2.0f * PI);

    FVector2D Offset;
    Offset.X = FMath::Sin(PhaseX) * AnimationData.EyeMovementRange.X;
    Offset.Y = FMath::Cos(PhaseY) * AnimationData.EyeMovementRange.Y;
    
    // Apply saccadic eye movements (quick, jerky movements)
    float SaccadeFrequency = 3.0f; // Saccades per second
    float SaccadePhase = FMath::Fmod(CurrentTime * SaccadeFrequency, 1.0f);
    
    if (SaccadePhase < 0.1f) // 10% of time in saccadic movement
    {
        float SaccadeProgress = SaccadePhase / 0.1f;
        FVector2D SaccadeTarget = FVector2D(
            FMath::RandRange(-0.3f, 0.3f),
            FMath::RandRange(-0.2f, 0.2f)
        );
        
        // Apply smooth saccadic curve
        float SaccadeCurve = FMath::SmoothStep(0.0f, 1.0f, SaccadeProgress);
        Offset += SaccadeTarget * SaccadeCurve * 0.5f;
    }
    
    // Apply microsaccades (tiny involuntary movements)
    float MicrosaccadeFrequency = 30.0f; // High frequency micro movements
    FVector2D MicrosaccadeOffset = FVector2D(
        FMath::Sin(CurrentTime * MicrosaccadeFrequency) * 0.01f,
        FMath::Cos(CurrentTime * MicrosaccadeFrequency * 1.3f) * 0.01f
    );
    Offset += MicrosaccadeOffset;
    
    // Apply breathing influence on eye position
    float BreathingInfluence = FMath::Sin(CurrentTime * 0.3f * 2.0f * PI) * 0.005f;
    Offset.Y += BreathingInfluence;
    
    UE_LOG(LogAuracronEyeGeneration, VeryVerbose, TEXT("Calculated animated eye offset: (%.3f, %.3f)"), 
           Offset.X, Offset.Y);

    return Offset;
}

float FAuracronEyeGeneration::CalculateAnimatedBlinkAmount(const FEyeAnimationData& AnimationData, float CurrentTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::CalculateAnimatedBlinkAmount);
    
    // Calculate animated blink amount using realistic blinking patterns
    float BlinkAmount = 0.0f;
    
    // Natural blinking frequency (average 15-20 blinks per minute)
    float BlinkFrequency = AnimationData.BlinkFrequency; // blinks per second
    float BlinkCycle = FMath::Fmod(CurrentTime * BlinkFrequency, 1.0f);
    
    // Blink duration (typically 100-400ms)
    float BlinkDuration = AnimationData.BlinkDuration;
    
    // Calculate main blink cycle
    if (BlinkCycle < BlinkDuration)
    {
        float BlinkProgress = BlinkCycle / BlinkDuration;
        
        // Use a smooth curve for natural blink motion
        // Blink has three phases: close (30%), hold (10%), open (60%)
        if (BlinkProgress < 0.3f)
        {
            // Closing phase - accelerating
            float CloseProgress = BlinkProgress / 0.3f;
            BlinkAmount = FMath::SmoothStep(0.0f, 1.0f, CloseProgress);
        }
        else if (BlinkProgress < 0.4f)
        {
            // Hold phase - fully closed
            BlinkAmount = 1.0f;
        }
        else
        {
            // Opening phase - decelerating
            float OpenProgress = (BlinkProgress - 0.4f) / 0.6f;
            BlinkAmount = 1.0f - FMath::SmoothStep(0.0f, 1.0f, OpenProgress);
        }
        
        // Apply blink intensity
        BlinkAmount *= AnimationData.BlinkIntensity;
    }
    
    // Add micro-blinks for realism (small, quick blinks)
    float MicroBlinkFrequency = 2.0f; // Micro-blinks per second
    float MicroBlinkPhase = FMath::Fmod(CurrentTime * MicroBlinkFrequency, 1.0f);
    
    if (MicroBlinkPhase < 0.05f && BlinkAmount < 0.1f) // Only when not already blinking
    {
        float MicroBlinkProgress = MicroBlinkPhase / 0.05f;
        float MicroBlinkCurve = FMath::Sin(MicroBlinkProgress * PI);
        BlinkAmount += MicroBlinkCurve * AnimationData.BlinkIntensity * 0.2f; // 20% of full blink
    }
    
    // Apply emotional state modifiers
    float EmotionalBlinkMultiplier = 1.0f;
    switch (AnimationData.EmotionalState)
    {
        case EEmotionalState::Fear:
        case EEmotionalState::Surprise:
            EmotionalBlinkMultiplier = 1.8f; // More frequent and intense blinking
            break;
        case EEmotionalState::Anger:
            EmotionalBlinkMultiplier = 0.6f; // Less blinking (intense stare)
            break;
        case EEmotionalState::Calm:
            EmotionalBlinkMultiplier = 0.9f; // Slightly less frequent
            break;
        case EEmotionalState::Happy:
            EmotionalBlinkMultiplier = 1.1f; // Slightly more frequent
            break;
        default:
            EmotionalBlinkMultiplier = 1.0f;
            break;
    }
    
    BlinkAmount *= EmotionalBlinkMultiplier;
    
    // Apply fatigue effect (increases blink frequency and duration)
    if (AnimationData.FatigueLevel > 0.0f)
    {
        float FatigueMultiplier = 1.0f + (AnimationData.FatigueLevel * 0.5f);
        BlinkAmount *= FatigueMultiplier;
        
        // Add slow, heavy blinks when very tired
        if (AnimationData.FatigueLevel > 0.7f)
        {
            float SlowBlinkPhase = FMath::Fmod(CurrentTime * 0.3f, 1.0f);
            if (SlowBlinkPhase < 0.3f)
            {
                float SlowBlinkProgress = SlowBlinkPhase / 0.3f;
                float SlowBlinkCurve = FMath::SmoothStep(0.0f, 1.0f, SlowBlinkProgress);
                BlinkAmount = FMath::Max(BlinkAmount, SlowBlinkCurve * 0.8f);
            }
        }
    }
    
    // Clamp the final result
    BlinkAmount = FMath::Clamp(BlinkAmount, 0.0f, 1.0f);
    
    // Log for debugging (very verbose)
    UE_LOG(LogAuracronEyeGeneration, VeryVerbose, TEXT("Calculated blink amount: %.3f (Frequency: %.2f, Emotional: %.2f, Fatigue: %.2f)"), 
           BlinkAmount, BlinkFrequency, EmotionalBlinkMultiplier, AnimationData.FatigueLevel);
    
    return BlinkAmount
    float BlinkDuration = AnimationData.BlinkDuration; // in seconds
    float BlinkSpeed = 1.0f / BlinkDuration;
    
    // Check if we're in a blink cycle
    if (BlinkCycle < BlinkDuration)
    {
        float BlinkProgress = BlinkCycle / BlinkDuration;
        
        // Use a smooth blink curve (fast close, slower open)
        if (BlinkProgress < 0.3f) // Closing phase (30% of blink duration)
        {
            float CloseProgress = BlinkProgress / 0.3f;
            BlinkAmount = FMath::SmoothStep(0.0f, 1.0f, CloseProgress);
        }
        else // Opening phase (70% of blink duration)
        {
            float OpenProgress = (BlinkProgress - 0.3f) / 0.7f;
            BlinkAmount = FMath::SmoothStep(1.0f, 0.0f, OpenProgress);
        }
    }
    
    // Apply emotional state influence on blinking
    float EmotionalBlink = 0.0f;
    switch (AnimationData.EmotionalState)
    {
        case EEmotionalState::Surprise:
            EmotionalBlink = -0.5f; // Wide eyes, less blinking
            break;
        case EEmotionalState::Concentration:
            EmotionalBlink = -0.3f; // Focused, reduced blinking
            break;
        case EEmotionalState::Tiredness:
            EmotionalBlink = 0.4f; // More frequent, slower blinks
            break;
        case EEmotionalState::Stress:
            EmotionalBlink = 0.2f; // Increased blink rate
            break;
        default:
            EmotionalBlink = 0.0f;
            break;
    }
    
    // Apply environmental factors
    float EnvironmentalBlink = 0.0f;
    if (AnimationData.WindIntensity > 0.5f)
    {
        EnvironmentalBlink += 0.3f; // More blinking in wind
    }
    if (AnimationData.DustLevel > 0.3f)
    {
        EnvironmentalBlink += 0.2f; // More blinking in dusty environment
    }
    
    // Apply micro-blinks (partial blinks)
    float MicroBlinkFrequency = 5.0f; // Higher frequency micro-blinks
    float MicroBlinkCycle = FMath::Fmod(CurrentTime * MicroBlinkFrequency, 1.0f);
    if (MicroBlinkCycle < 0.05f && BlinkAmount < 0.1f) // Only when not in full blink
    {
        float MicroBlinkProgress = MicroBlinkCycle / 0.05f;
        float MicroBlinkAmount = FMath::Sin(MicroBlinkProgress * PI) * 0.2f;
        BlinkAmount = FMath::Max(BlinkAmount, MicroBlinkAmount);
    }
    
    // Apply final modifiers
    BlinkAmount *= (1.0f + EmotionalBlink + EnvironmentalBlink);
    
    // Clamp to valid range
    BlinkAmount = FMath::Clamp(BlinkAmount, 0.0f, 1.0f);
    
    UE_LOG(LogAuracronEyeGeneration, VeryVerbose, TEXT("Calculated animated blink amount: %.3f (cycle: %.3f)"), 
           BlinkAmount, BlinkCycle);
    
    return BlinkAmount;
}
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::CalculateAnimatedBlinkAmount);
    
    // Calculate animated blink amount using UE5.6 blink simulation
    float BlinkPhase = FMath::Fmod(CurrentTime, AnimationData.BlinkInterval);
    float BlinkAmount = 0.0f;

    if (BlinkPhase < AnimationData.BlinkDuration)
    {
        // During blink - use more realistic blink curve
        float BlinkProgress = BlinkPhase / AnimationData.BlinkDuration;
        
        // Asymmetric blink curve (faster close, slower open)
        float BlinkCurve;
        if (BlinkProgress < 0.3f)
        {
            // Fast closing phase
            float CloseProgress = BlinkProgress / 0.3f;
            BlinkCurve = FMath::SmoothStep(0.0f, 1.0f, CloseProgress);
        }
        else
        {
            // Slower opening phase
            float OpenProgress = (BlinkProgress - 0.3f) / 0.7f;
            BlinkCurve = 1.0f - FMath::SmoothStep(0.0f, 1.0f, OpenProgress);
        }
        
        BlinkAmount = BlinkCurve * AnimationData.BlinkIntensity;
    }
    
    // Add micro-blinks (partial blinks)
    float MicroBlinkFrequency = 0.5f; // Micro-blinks per second
    float MicroBlinkPhase = FMath::Fmod(CurrentTime * MicroBlinkFrequency, 1.0f);
    
    if (MicroBlinkPhase < 0.1f && BlinkAmount == 0.0f) // Only when not already blinking
    {
        float MicroBlinkProgress = MicroBlinkPhase / 0.1f;
        float MicroBlinkCurve = FMath::Sin(MicroBlinkProgress * PI);
        BlinkAmount += MicroBlinkCurve * AnimationData.BlinkIntensity * 0.3f; // 30% of full blink
    }
    
    // Add emotional state influence on blinking
    float EmotionalBlinkMultiplier = 1.0f;
    switch (AnimationData.EmotionalState)
    {
        case EEmotionalState::Fear:
        case EEmotionalState::Surprise:
            EmotionalBlinkMultiplier = 1.5f; // More frequent blinking
            break;
        case EEmotionalState::Anger:
            EmotionalBlinkMultiplier = 0.7f; // Less blinking (staring)
            break;
        case EEmotionalState::Calm:
            EmotionalBlinkMultiplier = 0.9f; // Slightly less frequent
            break;
        default:
            EmotionalBlinkMultiplier = 1.0f;
            break;
    }
    
    BlinkAmount *= EmotionalBlinkMultiplier;
    
    // Clamp to valid range
    BlinkAmount = FMath::Clamp(BlinkAmount, 0.0f, 1.0f);
    
    UE_LOG(LogAuracronEyeGeneration, VeryVerbose, TEXT("Calculated blink amount: %.3f"), BlinkAmount);

    return BlinkAmount;
}

float FAuracronEyeGeneration::CalculateAnimatedWetness(const FEyeAnimationData& AnimationData, float CurrentTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronEyeGeneration::CalculateAnimatedWetness);
    
    // Calculate animated wetness using realistic tear film simulation
    float BaseWetness = AnimationData.BaseWetness;
    float AnimatedWetness = BaseWetness;
    
    // Apply blinking influence on wetness (tear film redistribution)
    float BlinkAmount = CalculateAnimatedBlinkAmount(AnimationData, CurrentTime);
    float BlinkWetnessBoost = BlinkAmount * 0.3f; // Blinking redistributes tear film
    AnimatedWetness += BlinkWetnessBoost;
    
    // Apply emotional state influence on tear production
    float EmotionalWetness = 0.0f;
    switch (AnimationData.EmotionalState)
    {
        case EEmotionalState::Sadness:
            EmotionalWetness = 0.6f; // Increased tear production
            break;
        case EEmotionalState::Joy:
            EmotionalWetness = 0.2f; // Slight increase from joy tears
            break;
        case EEmotionalState::Pain:
            EmotionalWetness = 0.4f; // Reflex tears
            break;
        case EEmotionalState::Anger:
            EmotionalWetness = 0.1f; // Slight increase
            break;
        case EEmotionalState::Fear:
            EmotionalWetness = 0.3f; // Stress-induced tears
            break;
        default:
            EmotionalWetness = 0.0f;
            break;
    }
    
    // Apply environmental factors
    float EnvironmentalWetness = 0.0f;
    if (AnimationData.WindIntensity > 0.3f)
    {
        EnvironmentalWetness += AnimationData.WindIntensity * 0.4f; // Wind causes reflex tears
    }
    if (AnimationData.DustLevel > 0.2f)
    {
        EnvironmentalWetness += AnimationData.DustLevel * 0.3f; // Dust irritation
    }
    if (AnimationData.BrightnessLevel > 0.8f)
    {
        EnvironmentalWetness += (AnimationData.BrightnessLevel - 0.8f) * 0.5f; // Bright light reflex
    }
    
    // Apply natural tear film variation
    float TearFilmCycle = FMath::Sin(CurrentTime * 0.1f * 2.0f * PI) * 0.05f; // Slow natural variation
    AnimatedWetness += TearFilmCycle;
    
    // Apply micro-variations from eye movement
    float EyeMovementWetness = FMath::Sin(CurrentTime * 2.0f) * 0.02f;
    AnimatedWetness += EyeMovementWetness;
    
    // Apply age-related factors
    float AgeFactor = FMath::Clamp(AnimationData.Age / 100.0f, 0.0f, 1.0f);
    float AgeWetnessReduction = AgeFactor * 0.2f; // Older eyes tend to be drier
    AnimatedWetness -= AgeWetnessReduction;
    
    // Apply health factors
    if (AnimationData.HealthState == EHealthState::Dehydrated)
    {
        AnimatedWetness *= 0.7f; // Reduced tear production
    }
    else if (AnimationData.HealthState == EHealthState::Allergic)
    {
        AnimatedWetness *= 1.4f; // Increased tear production
    }
    
    // Apply final modifiers
    AnimatedWetness += EmotionalWetness + EnvironmentalWetness;
    
    // Apply original animation phase variation
    float WetnessPhase = FMath::Fmod(CurrentTime * AnimationData.WetnessVariationSpeed, 2.0f * PI);
    float WetnessVariation = FMath::Sin(WetnessPhase) * AnimationData.WetnessVariationAmount;
    AnimatedWetness += WetnessVariation;
    
    // Clamp to realistic wetness range
    AnimatedWetness = FMath::Clamp(AnimatedWetness, 0.1f, 1.0f); // Eyes are never completely dry
    
    UE_LOG(LogAuracronEyeGeneration, VeryVerbose, TEXT("Calculated animated wetness: %.3f (base: %.3f, emotional: %.3f)"), 
           AnimatedWetness, BaseWetness, EmotionalWetness);
    
    return AnimatedWetness;
}

void FAuracronEyeGeneration::UpdateEyeTextureCacheStats()
{
    FScopeLock Lock(&EyeGenerationMutex);

    EyeTextureCacheMemoryUsage = 0;

    // Calculate total memory usage of cached textures using UE5.6 memory tracking
    for (const auto& CachePair : EyeTextureCache)
    {
        if (CachePair.Value.IsValid())
        {
            UTexture2D* Texture = CachePair.Value.Get();
            int32 TextureMemory = Texture->CalcTextureMemorySizeEnum(TMC_AllMips);
            EyeTextureCacheMemoryUsage += TextureMemory;
        }
    }
}

void FAuracronEyeGeneration::ClearEyeTextureCache()
{
    FScopeLock Lock(&EyeGenerationMutex);

    EyeTextureCache.Empty();
    EyeMaterialCache.Empty();
    EyeTextureCacheMemoryUsage = 0;

    UE_LOG(LogAuracronEyeGeneration, Log, TEXT("Eye texture cache cleared"));
}

void FAuracronEyeGeneration::UpdateEyeGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess)
{
    FScopeLock Lock(&EyeGenerationMutex);

    FString StatsValue = FString::Printf(TEXT("Time: %.3fs, Success: %s"), ExecutionTime, bSuccess ? TEXT("Yes") : TEXT("No"));
    EyeGenerationStats.Add(OperationName, StatsValue);
}

UWorld* FAuracronEyeGeneration::GetWorld() const
{
    // Get world context using UE5.6 world management
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        return GEngine->GetWorldContexts()[0].World();
    }
    return nullptr;
}
