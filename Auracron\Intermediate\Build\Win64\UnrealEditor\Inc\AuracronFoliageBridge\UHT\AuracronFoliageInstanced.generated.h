// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageInstanced.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageInstanced_generated_h
#error "AuracronFoliageInstanced.generated.h already included, missing '#pragma once' in AuracronFoliageInstanced.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageInstanced_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronFoliageInstancedManager;
class UHierarchicalInstancedStaticMeshComponent;
class UInstancedStaticMeshComponent;
class UMaterialInterface;
class UStaticMesh;
class UWorld;
struct FAuracronInstanceBatchData;
struct FAuracronInstanceClusterData;
struct FAuracronInstancedMeshConfiguration;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronInstancedMeshConfiguration *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_101_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronInstancedMeshConfiguration;
// ********** End ScriptStruct FAuracronInstancedMeshConfiguration *********************************

// ********** Begin ScriptStruct FAuracronInstanceBatchData ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_243_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronInstanceBatchData;
// ********** End ScriptStruct FAuracronInstanceBatchData ******************************************

// ********** Begin ScriptStruct FAuracronInstanceClusterData **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_318_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronInstanceClusterData;
// ********** End ScriptStruct FAuracronInstanceClusterData ****************************************

// ********** Begin Delegate FOnBatchCreated *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_549_DELEGATE \
static void FOnBatchCreated_DelegateWrapper(const FMulticastScriptDelegate& OnBatchCreated, const FString& BatchId, FAuracronInstanceBatchData BatchData);


// ********** End Delegate FOnBatchCreated *********************************************************

// ********** Begin Delegate FOnBatchDestroyed *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_550_DELEGATE \
static void FOnBatchDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnBatchDestroyed, const FString& BatchId);


// ********** End Delegate FOnBatchDestroyed *******************************************************

// ********** Begin Delegate FOnInstanceAdded ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_551_DELEGATE \
static void FOnInstanceAdded_DelegateWrapper(const FMulticastScriptDelegate& OnInstanceAdded, const FString& BatchId, int32 InstanceIndex);


// ********** End Delegate FOnInstanceAdded ********************************************************

// ********** Begin Delegate FOnInstanceRemoved ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_552_DELEGATE \
static void FOnInstanceRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnInstanceRemoved, const FString& BatchId, int32 InstanceIndex);


// ********** End Delegate FOnInstanceRemoved ******************************************************

// ********** Begin Delegate FOnBatchOptimized *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_553_DELEGATE \
static void FOnBatchOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnBatchOptimized, const FString& BatchId);


// ********** End Delegate FOnBatchOptimized *******************************************************

// ********** Begin Class UAuracronFoliageInstancedManager *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_375_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetHierarchicalComponent); \
	DECLARE_FUNCTION(execGetInstancedComponent); \
	DECLARE_FUNCTION(execDrawDebugVisualization); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetRenderingStatistics); \
	DECLARE_FUNCTION(execGetBatchCount); \
	DECLARE_FUNCTION(execGetVisibleInstanceCount); \
	DECLARE_FUNCTION(execGetTotalInstanceCount); \
	DECLARE_FUNCTION(execCompressInstanceData); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execGetBatchMemoryUsageMB); \
	DECLARE_FUNCTION(execGetMemoryUsageMB); \
	DECLARE_FUNCTION(execFlushGPUCommands); \
	DECLARE_FUNCTION(execUpdateGPUInstances); \
	DECLARE_FUNCTION(execIsGPUCullingEnabled); \
	DECLARE_FUNCTION(execEnableGPUCulling); \
	DECLARE_FUNCTION(execCompactBatches); \
	DECLARE_FUNCTION(execSplitBatch); \
	DECLARE_FUNCTION(execMergeBatches); \
	DECLARE_FUNCTION(execOptimizeBatches); \
	DECLARE_FUNCTION(execSetBatchLOD); \
	DECLARE_FUNCTION(execSetBatchVisibility); \
	DECLARE_FUNCTION(execUpdateLOD); \
	DECLARE_FUNCTION(execUpdateCulling); \
	DECLARE_FUNCTION(execSetClusterLOD); \
	DECLARE_FUNCTION(execGetClusters); \
	DECLARE_FUNCTION(execRebuildClusters); \
	DECLARE_FUNCTION(execBuildClusters); \
	DECLARE_FUNCTION(execUpdateInstancesSimple); \
	DECLARE_FUNCTION(execUpdateInstances); \
	DECLARE_FUNCTION(execRemoveInstances); \
	DECLARE_FUNCTION(execAddInstancesSimple); \
	DECLARE_FUNCTION(execAddInstances); \
	DECLARE_FUNCTION(execClearInstances); \
	DECLARE_FUNCTION(execGetInstanceCount); \
	DECLARE_FUNCTION(execGetInstanceTransforms); \
	DECLARE_FUNCTION(execUpdateInstance); \
	DECLARE_FUNCTION(execRemoveInstance); \
	DECLARE_FUNCTION(execAddInstance); \
	DECLARE_FUNCTION(execUpdateInstanceBatch); \
	DECLARE_FUNCTION(execGetAllInstanceBatches); \
	DECLARE_FUNCTION(execGetInstanceBatch); \
	DECLARE_FUNCTION(execDestroyInstanceBatch); \
	DECLARE_FUNCTION(execCreateInstanceBatch); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_375_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageInstancedManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageInstancedManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageInstancedManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_375_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageInstancedManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageInstancedManager(UAuracronFoliageInstancedManager&&) = delete; \
	UAuracronFoliageInstancedManager(const UAuracronFoliageInstancedManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageInstancedManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageInstancedManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageInstancedManager) \
	NO_API virtual ~UAuracronFoliageInstancedManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_372_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_375_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_375_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_375_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h_375_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageInstancedManager;

// ********** End Class UAuracronFoliageInstancedManager *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h

// ********** Begin Enum EAuracronInstanceRenderingMode ********************************************
#define FOREACH_ENUM_EAURACRONINSTANCERENDERINGMODE(op) \
	op(EAuracronInstanceRenderingMode::Standard) \
	op(EAuracronInstanceRenderingMode::Hierarchical) \
	op(EAuracronInstanceRenderingMode::Clustered) \
	op(EAuracronInstanceRenderingMode::GPU) \
	op(EAuracronInstanceRenderingMode::Nanite) 

enum class EAuracronInstanceRenderingMode : uint8;
template<> struct TIsUEnumClass<EAuracronInstanceRenderingMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceRenderingMode>();
// ********** End Enum EAuracronInstanceRenderingMode **********************************************

// ********** Begin Enum EAuracronInstanceCullingMode **********************************************
#define FOREACH_ENUM_EAURACRONINSTANCECULLINGMODE(op) \
	op(EAuracronInstanceCullingMode::None) \
	op(EAuracronInstanceCullingMode::Frustum) \
	op(EAuracronInstanceCullingMode::Distance) \
	op(EAuracronInstanceCullingMode::Occlusion) \
	op(EAuracronInstanceCullingMode::Combined) \
	op(EAuracronInstanceCullingMode::GPU) 

enum class EAuracronInstanceCullingMode : uint8;
template<> struct TIsUEnumClass<EAuracronInstanceCullingMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceCullingMode>();
// ********** End Enum EAuracronInstanceCullingMode ************************************************

// ********** Begin Enum EAuracronInstanceLODMode **************************************************
#define FOREACH_ENUM_EAURACRONINSTANCELODMODE(op) \
	op(EAuracronInstanceLODMode::PerInstance) \
	op(EAuracronInstanceLODMode::PerCluster) \
	op(EAuracronInstanceLODMode::PerBatch) \
	op(EAuracronInstanceLODMode::Adaptive) \
	op(EAuracronInstanceLODMode::Performance) 

enum class EAuracronInstanceLODMode : uint8;
template<> struct TIsUEnumClass<EAuracronInstanceLODMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceLODMode>();
// ********** End Enum EAuracronInstanceLODMode ****************************************************

// ********** Begin Enum EAuracronInstanceUpdateMode ***********************************************
#define FOREACH_ENUM_EAURACRONINSTANCEUPDATEMODE(op) \
	op(EAuracronInstanceUpdateMode::Immediate) \
	op(EAuracronInstanceUpdateMode::Deferred) \
	op(EAuracronInstanceUpdateMode::Batched) \
	op(EAuracronInstanceUpdateMode::Async) \
	op(EAuracronInstanceUpdateMode::GPU) 

enum class EAuracronInstanceUpdateMode : uint8;
template<> struct TIsUEnumClass<EAuracronInstanceUpdateMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceUpdateMode>();
// ********** End Enum EAuracronInstanceUpdateMode *************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
