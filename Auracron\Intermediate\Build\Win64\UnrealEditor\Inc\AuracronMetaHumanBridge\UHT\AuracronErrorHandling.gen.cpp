// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronErrorHandling.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronErrorHandling() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FDNAValidationResult();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FErrorInfo();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EErrorSeverity ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EErrorSeverity;
static UEnum* EErrorSeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EErrorSeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EErrorSeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EErrorSeverity"));
	}
	return Z_Registration_Info_UEnum_EErrorSeverity.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EErrorSeverity>()
{
	return EErrorSeverity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for error handling\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EErrorSeverity::Critical" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EErrorSeverity::Error" },
		{ "Fatal.DisplayName", "Fatal" },
		{ "Fatal.Name", "EErrorSeverity::Fatal" },
		{ "Info.DisplayName", "Info" },
		{ "Info.Name", "EErrorSeverity::Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for error handling" },
#endif
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EErrorSeverity::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EErrorSeverity::Info", (int64)EErrorSeverity::Info },
		{ "EErrorSeverity::Warning", (int64)EErrorSeverity::Warning },
		{ "EErrorSeverity::Error", (int64)EErrorSeverity::Error },
		{ "EErrorSeverity::Critical", (int64)EErrorSeverity::Critical },
		{ "EErrorSeverity::Fatal", (int64)EErrorSeverity::Fatal },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EErrorSeverity",
	"EErrorSeverity",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity()
{
	if (!Z_Registration_Info_UEnum_EErrorSeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EErrorSeverity.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EErrorSeverity.InnerSingleton;
}
// ********** End Enum EErrorSeverity **************************************************************

// ********** Begin Enum EErrorCategory ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EErrorCategory;
static UEnum* EErrorCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EErrorCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EErrorCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EErrorCategory"));
	}
	return Z_Registration_Info_UEnum_EErrorCategory.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EErrorCategory>()
{
	return EErrorCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Animation.DisplayName", "Animation" },
		{ "Animation.Name", "EErrorCategory::Animation" },
		{ "BlueprintType", "true" },
		{ "Clothing.DisplayName", "Clothing" },
		{ "Clothing.Name", "EErrorCategory::Clothing" },
		{ "DNA.DisplayName", "DNA" },
		{ "DNA.Name", "EErrorCategory::DNA" },
		{ "General.DisplayName", "General" },
		{ "General.Name", "EErrorCategory::General" },
		{ "Hair.DisplayName", "Hair" },
		{ "Hair.Name", "EErrorCategory::Hair" },
		{ "IO.DisplayName", "Input/Output" },
		{ "IO.Name", "EErrorCategory::IO" },
		{ "Memory.DisplayName", "Memory" },
		{ "Memory.Name", "EErrorCategory::Memory" },
		{ "Mesh.DisplayName", "Mesh" },
		{ "Mesh.Name", "EErrorCategory::Mesh" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
		{ "Network.DisplayName", "Network" },
		{ "Network.Name", "EErrorCategory::Network" },
		{ "Performance.DisplayName", "Performance" },
		{ "Performance.Name", "EErrorCategory::Performance" },
		{ "Physics.DisplayName", "Physics" },
		{ "Physics.Name", "EErrorCategory::Physics" },
		{ "Texture.DisplayName", "Texture" },
		{ "Texture.Name", "EErrorCategory::Texture" },
		{ "Validation.DisplayName", "Validation" },
		{ "Validation.Name", "EErrorCategory::Validation" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EErrorCategory::General", (int64)EErrorCategory::General },
		{ "EErrorCategory::DNA", (int64)EErrorCategory::DNA },
		{ "EErrorCategory::Mesh", (int64)EErrorCategory::Mesh },
		{ "EErrorCategory::Animation", (int64)EErrorCategory::Animation },
		{ "EErrorCategory::Texture", (int64)EErrorCategory::Texture },
		{ "EErrorCategory::Hair", (int64)EErrorCategory::Hair },
		{ "EErrorCategory::Clothing", (int64)EErrorCategory::Clothing },
		{ "EErrorCategory::Physics", (int64)EErrorCategory::Physics },
		{ "EErrorCategory::Performance", (int64)EErrorCategory::Performance },
		{ "EErrorCategory::Memory", (int64)EErrorCategory::Memory },
		{ "EErrorCategory::IO", (int64)EErrorCategory::IO },
		{ "EErrorCategory::Network", (int64)EErrorCategory::Network },
		{ "EErrorCategory::Validation", (int64)EErrorCategory::Validation },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EErrorCategory",
	"EErrorCategory",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory()
{
	if (!Z_Registration_Info_UEnum_EErrorCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EErrorCategory.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EErrorCategory.InnerSingleton;
}
// ********** End Enum EErrorCategory **************************************************************

// ********** Begin Enum EDNAValidationType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDNAValidationType;
static UEnum* EDNAValidationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDNAValidationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDNAValidationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EDNAValidationType"));
	}
	return Z_Registration_Info_UEnum_EDNAValidationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EDNAValidationType>()
{
	return EDNAValidationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EDNAValidationType::Basic" },
		{ "BlueprintType", "true" },
		{ "Compatibility.DisplayName", "Compatibility" },
		{ "Compatibility.Name", "EDNAValidationType::Compatibility" },
		{ "Comprehensive.DisplayName", "Comprehensive" },
		{ "Comprehensive.Name", "EDNAValidationType::Comprehensive" },
		{ "Integrity.DisplayName", "Integrity" },
		{ "Integrity.Name", "EDNAValidationType::Integrity" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
		{ "Performance.DisplayName", "Performance" },
		{ "Performance.Name", "EDNAValidationType::Performance" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDNAValidationType::Basic", (int64)EDNAValidationType::Basic },
		{ "EDNAValidationType::Comprehensive", (int64)EDNAValidationType::Comprehensive },
		{ "EDNAValidationType::Integrity", (int64)EDNAValidationType::Integrity },
		{ "EDNAValidationType::Performance", (int64)EDNAValidationType::Performance },
		{ "EDNAValidationType::Compatibility", (int64)EDNAValidationType::Compatibility },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EDNAValidationType",
	"EDNAValidationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType()
{
	if (!Z_Registration_Info_UEnum_EDNAValidationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDNAValidationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EDNAValidationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDNAValidationType.InnerSingleton;
}
// ********** End Enum EDNAValidationType **********************************************************

// ********** Begin Enum ERecoveryAction ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERecoveryAction;
static UEnum* ERecoveryAction_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERecoveryAction.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERecoveryAction.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ERecoveryAction"));
	}
	return Z_Registration_Info_UEnum_ERecoveryAction.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ERecoveryAction>()
{
	return ERecoveryAction_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Abort.DisplayName", "Abort" },
		{ "Abort.Name", "ERecoveryAction::Abort" },
		{ "BlueprintType", "true" },
		{ "Fallback.DisplayName", "Fallback" },
		{ "Fallback.Name", "ERecoveryAction::Fallback" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ERecoveryAction::None" },
		{ "Reload.DisplayName", "Reload" },
		{ "Reload.Name", "ERecoveryAction::Reload" },
		{ "Reset.DisplayName", "Reset" },
		{ "Reset.Name", "ERecoveryAction::Reset" },
		{ "Retry.DisplayName", "Retry" },
		{ "Retry.Name", "ERecoveryAction::Retry" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERecoveryAction::None", (int64)ERecoveryAction::None },
		{ "ERecoveryAction::Retry", (int64)ERecoveryAction::Retry },
		{ "ERecoveryAction::Reset", (int64)ERecoveryAction::Reset },
		{ "ERecoveryAction::Reload", (int64)ERecoveryAction::Reload },
		{ "ERecoveryAction::Fallback", (int64)ERecoveryAction::Fallback },
		{ "ERecoveryAction::Abort", (int64)ERecoveryAction::Abort },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ERecoveryAction",
	"ERecoveryAction",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction()
{
	if (!Z_Registration_Info_UEnum_ERecoveryAction.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERecoveryAction.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ERecoveryAction_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERecoveryAction.InnerSingleton;
}
// ********** End Enum ERecoveryAction *************************************************************

// ********** Begin ScriptStruct FErrorInfo ********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FErrorInfo;
class UScriptStruct* FErrorInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FErrorInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FErrorInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FErrorInfo, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ErrorInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FErrorInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FErrorInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for error handling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for error handling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorCode_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLocation_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StackTrace_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalData_MetaData[] = {
		{ "Category", "Error Info" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ErrorCode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StackTrace;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AdditionalData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FErrorInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_ErrorCode = { "ErrorCode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, ErrorCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorCode_MetaData), NewProp_ErrorCode_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, Severity), Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 479364426
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, Category), Z_Construct_UEnum_AuracronMetaHumanBridge_EErrorCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 2527395808
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_SourceLocation = { "SourceLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, SourceLocation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLocation_MetaData), NewProp_SourceLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_StackTrace = { "StackTrace", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, StackTrace), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StackTrace_MetaData), NewProp_StackTrace_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_AdditionalData_ValueProp = { "AdditionalData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_AdditionalData_Key_KeyProp = { "AdditionalData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_AdditionalData = { "AdditionalData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FErrorInfo, AdditionalData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalData_MetaData), NewProp_AdditionalData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FErrorInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_ErrorCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_SourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_StackTrace,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_AdditionalData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_AdditionalData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FErrorInfo_Statics::NewProp_AdditionalData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FErrorInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FErrorInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ErrorInfo",
	Z_Construct_UScriptStruct_FErrorInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FErrorInfo_Statics::PropPointers),
	sizeof(FErrorInfo),
	alignof(FErrorInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FErrorInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FErrorInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FErrorInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FErrorInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FErrorInfo.InnerSingleton, Z_Construct_UScriptStruct_FErrorInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FErrorInfo.InnerSingleton;
}
// ********** End ScriptStruct FErrorInfo **********************************************************

// ********** Begin ScriptStruct FDNAValidationResult **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FDNAValidationResult;
class UScriptStruct* FDNAValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FDNAValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FDNAValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDNAValidationResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("DNAValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FDNAValidationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FDNAValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Errors_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Warnings_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationScore_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationSummary_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorruptedDataBlocks_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MissingDataBlocks_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeRepaired_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RepairSuggestions_MetaData[] = {
		{ "Category", "DNA Validation" },
		{ "ModuleRelativePath", "Public/AuracronErrorHandling.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Errors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Errors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Warnings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Warnings;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationScore;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationSummary;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CorruptedDataBlocks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MissingDataBlocks;
	static void NewProp_bCanBeRepaired_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeRepaired;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RepairSuggestions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RepairSuggestions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDNAValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FDNAValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDNAValidationResult), &Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Errors_Inner = { "Errors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FErrorInfo, METADATA_PARAMS(0, nullptr) }; // 1902152047
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Errors = { "Errors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDNAValidationResult, Errors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Errors_MetaData), NewProp_Errors_MetaData) }; // 1902152047
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Warnings_Inner = { "Warnings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FErrorInfo, METADATA_PARAMS(0, nullptr) }; // 1902152047
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Warnings = { "Warnings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDNAValidationResult, Warnings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Warnings_MetaData), NewProp_Warnings_MetaData) }; // 1902152047
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_ValidationScore = { "ValidationScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDNAValidationResult, ValidationScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationScore_MetaData), NewProp_ValidationScore_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_ValidationSummary = { "ValidationSummary", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDNAValidationResult, ValidationSummary), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationSummary_MetaData), NewProp_ValidationSummary_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_CorruptedDataBlocks = { "CorruptedDataBlocks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDNAValidationResult, CorruptedDataBlocks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorruptedDataBlocks_MetaData), NewProp_CorruptedDataBlocks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_MissingDataBlocks = { "MissingDataBlocks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDNAValidationResult, MissingDataBlocks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MissingDataBlocks_MetaData), NewProp_MissingDataBlocks_MetaData) };
void Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bCanBeRepaired_SetBit(void* Obj)
{
	((FDNAValidationResult*)Obj)->bCanBeRepaired = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bCanBeRepaired = { "bCanBeRepaired", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDNAValidationResult), &Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bCanBeRepaired_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeRepaired_MetaData), NewProp_bCanBeRepaired_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_RepairSuggestions_Inner = { "RepairSuggestions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_RepairSuggestions = { "RepairSuggestions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDNAValidationResult, RepairSuggestions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RepairSuggestions_MetaData), NewProp_RepairSuggestions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDNAValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Errors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Errors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Warnings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_Warnings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_ValidationScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_ValidationSummary,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_CorruptedDataBlocks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_MissingDataBlocks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_bCanBeRepaired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_RepairSuggestions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewProp_RepairSuggestions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDNAValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDNAValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"DNAValidationResult",
	Z_Construct_UScriptStruct_FDNAValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDNAValidationResult_Statics::PropPointers),
	sizeof(FDNAValidationResult),
	alignof(FDNAValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDNAValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDNAValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDNAValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FDNAValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FDNAValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FDNAValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FDNAValidationResult.InnerSingleton;
}
// ********** End ScriptStruct FDNAValidationResult ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EErrorSeverity_StaticEnum, TEXT("EErrorSeverity"), &Z_Registration_Info_UEnum_EErrorSeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 479364426U) },
		{ EErrorCategory_StaticEnum, TEXT("EErrorCategory"), &Z_Registration_Info_UEnum_EErrorCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2527395808U) },
		{ EDNAValidationType_StaticEnum, TEXT("EDNAValidationType"), &Z_Registration_Info_UEnum_EDNAValidationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1846685249U) },
		{ ERecoveryAction_StaticEnum, TEXT("ERecoveryAction"), &Z_Registration_Info_UEnum_ERecoveryAction, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3514355632U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FErrorInfo::StaticStruct, Z_Construct_UScriptStruct_FErrorInfo_Statics::NewStructOps, TEXT("ErrorInfo"), &Z_Registration_Info_UScriptStruct_FErrorInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FErrorInfo), 1902152047U) },
		{ FDNAValidationResult::StaticStruct, Z_Construct_UScriptStruct_FDNAValidationResult_Statics::NewStructOps, TEXT("DNAValidationResult"), &Z_Registration_Info_UScriptStruct_FDNAValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDNAValidationResult), 4140124378U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h__Script_AuracronMetaHumanBridge_4145645361(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
