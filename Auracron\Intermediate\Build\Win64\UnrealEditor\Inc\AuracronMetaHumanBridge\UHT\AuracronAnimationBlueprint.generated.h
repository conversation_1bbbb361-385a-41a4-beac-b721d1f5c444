// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAnimationBlueprint.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronAnimationBlueprint_generated_h
#error "AuracronAnimationBlueprint.generated.h already included, missing '#pragma once' in AuracronAnimationBlueprint.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronAnimationBlueprint_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAnimationStringArrayWrapper **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h_23_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAnimationStringArrayWrapper;
// ********** End ScriptStruct FAnimationStringArrayWrapper ****************************************

// ********** Begin ScriptStruct FFacialAnimationData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h_113_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FFacialAnimationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FFacialAnimationData;
// ********** End ScriptStruct FFacialAnimationData ************************************************

// ********** Begin ScriptStruct FLipSyncData ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h_146_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLipSyncData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FLipSyncData;
// ********** End ScriptStruct FLipSyncData ********************************************************

// ********** Begin ScriptStruct FEmotionMappingData ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h_179_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEmotionMappingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FEmotionMappingData;
// ********** End ScriptStruct FEmotionMappingData *************************************************

// ********** Begin ScriptStruct FAnimationBlueprintLODData ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h_206_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAnimationBlueprintLODData;
// ********** End ScriptStruct FAnimationBlueprintLODData ******************************************

// ********** Begin ScriptStruct FAnimationBlueprintGenerationParameters ***************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h_236_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAnimationBlueprintGenerationParameters;
// ********** End ScriptStruct FAnimationBlueprintGenerationParameters *****************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h

// ********** Begin Enum EAnimationBlueprintType ***************************************************
#define FOREACH_ENUM_EANIMATIONBLUEPRINTTYPE(op) \
	op(EAnimationBlueprintType::Basic) \
	op(EAnimationBlueprintType::Facial) \
	op(EAnimationBlueprintType::FullBody) \
	op(EAnimationBlueprintType::LipSync) \
	op(EAnimationBlueprintType::Emotion) \
	op(EAnimationBlueprintType::Advanced) 

enum class EAnimationBlueprintType : uint8;
template<> struct TIsUEnumClass<EAnimationBlueprintType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAnimationBlueprintType>();
// ********** End Enum EAnimationBlueprintType *****************************************************

// ********** Begin Enum EFacialAnimationType ******************************************************
#define FOREACH_ENUM_EFACIALANIMATIONTYPE(op) \
	op(EFacialAnimationType::BlendShapes) \
	op(EFacialAnimationType::BoneTransforms) \
	op(EFacialAnimationType::Hybrid) 

enum class EFacialAnimationType : uint8;
template<> struct TIsUEnumClass<EFacialAnimationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EFacialAnimationType>();
// ********** End Enum EFacialAnimationType ********************************************************

// ********** Begin Enum ELipSyncMethod ************************************************************
#define FOREACH_ENUM_ELIPSYNCMETHOD(op) \
	op(ELipSyncMethod::Phoneme) \
	op(ELipSyncMethod::Viseme) \
	op(ELipSyncMethod::AudioAnalysis) \
	op(ELipSyncMethod::ML) 

enum class ELipSyncMethod : uint8;
template<> struct TIsUEnumClass<ELipSyncMethod> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ELipSyncMethod>();
// ********** End Enum ELipSyncMethod **************************************************************

// ********** Begin Enum EEmotionBlendMode *********************************************************
#define FOREACH_ENUM_EEMOTIONBLENDMODE(op) \
	op(EEmotionBlendMode::Replace) \
	op(EEmotionBlendMode::Additive) \
	op(EEmotionBlendMode::Multiply) \
	op(EEmotionBlendMode::Overlay) 

enum class EEmotionBlendMode : uint8;
template<> struct TIsUEnumClass<EEmotionBlendMode> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEmotionBlendMode>();
// ********** End Enum EEmotionBlendMode ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
