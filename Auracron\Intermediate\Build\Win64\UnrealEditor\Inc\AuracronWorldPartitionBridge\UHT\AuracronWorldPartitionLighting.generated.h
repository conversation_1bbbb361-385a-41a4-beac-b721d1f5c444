// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionLighting.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionLighting_generated_h
#error "AuracronWorldPartitionLighting.generated.h already included, missing '#pragma once' in AuracronWorldPartitionLighting.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionLighting_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionLightingManager;
class UWorld;
enum class EAuracronLightingLODLevel : uint8;
enum class EAuracronLightingStreamingState : uint8;
enum class EAuracronLightType : uint8;
enum class EAuracronShadowQuality : uint8;
struct FAuracronLightingConfiguration;
struct FAuracronLightingDescriptor;
struct FAuracronLightingStatistics;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronLightingConfiguration ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_116_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLightingConfiguration;
// ********** End ScriptStruct FAuracronLightingConfiguration **************************************

// ********** Begin ScriptStruct FAuracronLightingDescriptor ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_230_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLightingDescriptor;
// ********** End ScriptStruct FAuracronLightingDescriptor *****************************************

// ********** Begin ScriptStruct FAuracronLightingStatistics ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_332_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLightingStatistics;
// ********** End ScriptStruct FAuracronLightingStatistics *****************************************

// ********** Begin Delegate FOnLightingLoaded *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_575_DELEGATE \
static void FOnLightingLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnLightingLoaded, const FString& LightingId, bool bSuccess);


// ********** End Delegate FOnLightingLoaded *******************************************************

// ********** Begin Delegate FOnLightingUnloaded ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_576_DELEGATE \
static void FOnLightingUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnLightingUnloaded, const FString& LightingId);


// ********** End Delegate FOnLightingUnloaded *****************************************************

// ********** Begin Delegate FOnLightingActivated **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_577_DELEGATE \
static void FOnLightingActivated_DelegateWrapper(const FMulticastScriptDelegate& OnLightingActivated, const FString& LightingId);


// ********** End Delegate FOnLightingActivated ****************************************************

// ********** Begin Delegate FOnLightingDeactivated ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_578_DELEGATE \
static void FOnLightingDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnLightingDeactivated, const FString& LightingId);


// ********** End Delegate FOnLightingDeactivated **************************************************

// ********** Begin Delegate FOnLightingLODChanged *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_579_DELEGATE \
static void FOnLightingLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLightingLODChanged, const FString& LightingId, EAuracronLightingLODLevel NewLOD);


// ********** End Delegate FOnLightingLODChanged ***************************************************

// ********** Begin Class UAuracronWorldPartitionLightingManager ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_401_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugLightingInfo); \
	DECLARE_FUNCTION(execLogLightingState); \
	DECLARE_FUNCTION(execIsLightingDebugEnabled); \
	DECLARE_FUNCTION(execEnableLightingDebug); \
	DECLARE_FUNCTION(execGetTotalMemoryUsage); \
	DECLARE_FUNCTION(execGetActiveLightSourceCount); \
	DECLARE_FUNCTION(execGetLoadedLightSourceCount); \
	DECLARE_FUNCTION(execGetTotalLightSourceCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetLightingStatistics); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execSetShadowQuality); \
	DECLARE_FUNCTION(execEnableShadowsForLight); \
	DECLARE_FUNCTION(execUnloadLightmapsForCell); \
	DECLARE_FUNCTION(execLoadLightmapsForCell); \
	DECLARE_FUNCTION(execBuildLightmapsForCell); \
	DECLARE_FUNCTION(execMoveLightSourceToCell); \
	DECLARE_FUNCTION(execGetLightSourceCell); \
	DECLARE_FUNCTION(execGetLightSourcesInCell); \
	DECLARE_FUNCTION(execGetInfluencingLightSources); \
	DECLARE_FUNCTION(execGetLightSourcesByType); \
	DECLARE_FUNCTION(execGetLightSourcesInRadius); \
	DECLARE_FUNCTION(execCalculateLODForDistance); \
	DECLARE_FUNCTION(execUpdateDistanceBasedLODs); \
	DECLARE_FUNCTION(execGetLightingLOD); \
	DECLARE_FUNCTION(execSetLightingLOD); \
	DECLARE_FUNCTION(execSetLightAttenuationRadius); \
	DECLARE_FUNCTION(execSetLightTemperature); \
	DECLARE_FUNCTION(execSetLightRotation); \
	DECLARE_FUNCTION(execSetLightLocation); \
	DECLARE_FUNCTION(execSetLightColor); \
	DECLARE_FUNCTION(execSetLightIntensity); \
	DECLARE_FUNCTION(execGetActiveLightSources); \
	DECLARE_FUNCTION(execDeactivateLightSource); \
	DECLARE_FUNCTION(execActivateLightSource); \
	DECLARE_FUNCTION(execGetStreamingLightSources); \
	DECLARE_FUNCTION(execGetLoadedLightSources); \
	DECLARE_FUNCTION(execGetLightingStreamingState); \
	DECLARE_FUNCTION(execUnloadLightSource); \
	DECLARE_FUNCTION(execLoadLightSource); \
	DECLARE_FUNCTION(execDoesLightSourceExist); \
	DECLARE_FUNCTION(execGetLightingIds); \
	DECLARE_FUNCTION(execGetAllLightSources); \
	DECLARE_FUNCTION(execGetLightingDescriptor); \
	DECLARE_FUNCTION(execRemoveLightSource); \
	DECLARE_FUNCTION(execCreateLightSource); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLightingManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_401_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionLightingManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLightingManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionLightingManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionLightingManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionLightingManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_401_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionLightingManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionLightingManager(UAuracronWorldPartitionLightingManager&&) = delete; \
	UAuracronWorldPartitionLightingManager(const UAuracronWorldPartitionLightingManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionLightingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionLightingManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionLightingManager) \
	NO_API virtual ~UAuracronWorldPartitionLightingManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_398_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_401_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_401_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_401_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h_401_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionLightingManager;

// ********** End Class UAuracronWorldPartitionLightingManager *************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h

// ********** Begin Enum EAuracronLightingStreamingState *******************************************
#define FOREACH_ENUM_EAURACRONLIGHTINGSTREAMINGSTATE(op) \
	op(EAuracronLightingStreamingState::Unloaded) \
	op(EAuracronLightingStreamingState::Loading) \
	op(EAuracronLightingStreamingState::Loaded) \
	op(EAuracronLightingStreamingState::Active) \
	op(EAuracronLightingStreamingState::Unloading) \
	op(EAuracronLightingStreamingState::Failed) 

enum class EAuracronLightingStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronLightingStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightingStreamingState>();
// ********** End Enum EAuracronLightingStreamingState *********************************************

// ********** Begin Enum EAuracronLightingLODLevel *************************************************
#define FOREACH_ENUM_EAURACRONLIGHTINGLODLEVEL(op) \
	op(EAuracronLightingLODLevel::LOD0) \
	op(EAuracronLightingLODLevel::LOD1) \
	op(EAuracronLightingLODLevel::LOD2) \
	op(EAuracronLightingLODLevel::LOD3) \
	op(EAuracronLightingLODLevel::LOD4) 

enum class EAuracronLightingLODLevel : uint8;
template<> struct TIsUEnumClass<EAuracronLightingLODLevel> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightingLODLevel>();
// ********** End Enum EAuracronLightingLODLevel ***************************************************

// ********** Begin Enum EAuracronLightType ********************************************************
#define FOREACH_ENUM_EAURACRONLIGHTTYPE(op) \
	op(EAuracronLightType::Directional) \
	op(EAuracronLightType::Point) \
	op(EAuracronLightType::Spot) \
	op(EAuracronLightType::Sky) \
	op(EAuracronLightType::Area) \
	op(EAuracronLightType::Volumetric) 

enum class EAuracronLightType : uint8;
template<> struct TIsUEnumClass<EAuracronLightType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightType>();
// ********** End Enum EAuracronLightType **********************************************************

// ********** Begin Enum EAuracronLightMobility ****************************************************
#define FOREACH_ENUM_EAURACRONLIGHTMOBILITY(op) \
	op(EAuracronLightMobility::Static) \
	op(EAuracronLightMobility::Stationary) \
	op(EAuracronLightMobility::Movable) 

enum class EAuracronLightMobility : uint8;
template<> struct TIsUEnumClass<EAuracronLightMobility> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightMobility>();
// ********** End Enum EAuracronLightMobility ******************************************************

// ********** Begin Enum EAuracronShadowQuality ****************************************************
#define FOREACH_ENUM_EAURACRONSHADOWQUALITY(op) \
	op(EAuracronShadowQuality::Low) \
	op(EAuracronShadowQuality::Medium) \
	op(EAuracronShadowQuality::High) \
	op(EAuracronShadowQuality::Epic) 

enum class EAuracronShadowQuality : uint8;
template<> struct TIsUEnumClass<EAuracronShadowQuality> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronShadowQuality>();
// ********** End Enum EAuracronShadowQuality ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
