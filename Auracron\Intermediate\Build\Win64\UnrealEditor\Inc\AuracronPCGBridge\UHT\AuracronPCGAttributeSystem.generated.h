// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGAttributeSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGAttributeSystem_generated_h
#error "AuracronPCGAttributeSystem.generated.h already included, missing '#pragma once' in AuracronPCGAttributeSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGAttributeSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPCGMetadata;
enum class EAuracronPCGAttributeAggregation : uint8;
enum class EAuracronPCGAttributeFilterMode : uint8;
enum class EAuracronPCGAttributeInterpolation : uint8;
enum class EAuracronPCGAttributeType : uint8;
struct FAuracronPCGAttributeDescriptor;
struct FAuracronPCGAttributeOperation;

// ********** Begin ScriptStruct FAuracronPCGAttributeDescriptor ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_122_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGAttributeDescriptor;
// ********** End ScriptStruct FAuracronPCGAttributeDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGAttributeOperation ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_179_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGAttributeOperation;
// ********** End ScriptStruct FAuracronPCGAttributeOperation **************************************

// ********** Begin Class UAuracronPCGAttributeCreatorSettings *************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_219_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAttributeCreatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAttributeCreatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAttributeCreatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_219_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAttributeCreatorSettings(UAuracronPCGAttributeCreatorSettings&&) = delete; \
	UAuracronPCGAttributeCreatorSettings(const UAuracronPCGAttributeCreatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAttributeCreatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAttributeCreatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAttributeCreatorSettings) \
	NO_API virtual ~UAuracronPCGAttributeCreatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_216_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_219_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_219_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_219_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAttributeCreatorSettings;

// ********** End Class UAuracronPCGAttributeCreatorSettings ***************************************

// ********** Begin Class UAuracronPCGAttributeModifierSettings ************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_271_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAttributeModifierSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAttributeModifierSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAttributeModifierSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_271_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAttributeModifierSettings(UAuracronPCGAttributeModifierSettings&&) = delete; \
	UAuracronPCGAttributeModifierSettings(const UAuracronPCGAttributeModifierSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAttributeModifierSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAttributeModifierSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAttributeModifierSettings) \
	NO_API virtual ~UAuracronPCGAttributeModifierSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_268_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_271_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_271_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_271_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAttributeModifierSettings;

// ********** End Class UAuracronPCGAttributeModifierSettings **************************************

// ********** Begin Class UAuracronPCGAttributeInterpolatorSettings ********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_326_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAttributeInterpolatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAttributeInterpolatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAttributeInterpolatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_326_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAttributeInterpolatorSettings(UAuracronPCGAttributeInterpolatorSettings&&) = delete; \
	UAuracronPCGAttributeInterpolatorSettings(const UAuracronPCGAttributeInterpolatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAttributeInterpolatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAttributeInterpolatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAttributeInterpolatorSettings) \
	NO_API virtual ~UAuracronPCGAttributeInterpolatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_323_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_326_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_326_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_326_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAttributeInterpolatorSettings;

// ********** End Class UAuracronPCGAttributeInterpolatorSettings **********************************

// ********** Begin Class UAuracronPCGAttributeFilterSettings **************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_385_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAttributeFilterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAttributeFilterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAttributeFilterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_385_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAttributeFilterSettings(UAuracronPCGAttributeFilterSettings&&) = delete; \
	UAuracronPCGAttributeFilterSettings(const UAuracronPCGAttributeFilterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAttributeFilterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAttributeFilterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAttributeFilterSettings) \
	NO_API virtual ~UAuracronPCGAttributeFilterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_382_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_385_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_385_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_385_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAttributeFilterSettings;

// ********** End Class UAuracronPCGAttributeFilterSettings ****************************************

// ********** Begin Class UAuracronPCGAttributeValidatorSettings ***********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_444_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAttributeValidatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAttributeValidatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAttributeValidatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_444_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAttributeValidatorSettings(UAuracronPCGAttributeValidatorSettings&&) = delete; \
	UAuracronPCGAttributeValidatorSettings(const UAuracronPCGAttributeValidatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAttributeValidatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAttributeValidatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAttributeValidatorSettings) \
	NO_API virtual ~UAuracronPCGAttributeValidatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_441_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_444_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_444_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_444_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAttributeValidatorSettings;

// ********** End Class UAuracronPCGAttributeValidatorSettings *************************************

// ********** Begin Class UAuracronPCGAttributeAggregatorSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_493_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAttributeAggregatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAttributeAggregatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAttributeAggregatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_493_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAttributeAggregatorSettings(UAuracronPCGAttributeAggregatorSettings&&) = delete; \
	UAuracronPCGAttributeAggregatorSettings(const UAuracronPCGAttributeAggregatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAttributeAggregatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAttributeAggregatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAttributeAggregatorSettings) \
	NO_API virtual ~UAuracronPCGAttributeAggregatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_490_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_493_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_493_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_493_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAttributeAggregatorSettings;

// ********** End Class UAuracronPCGAttributeAggregatorSettings ************************************

// ********** Begin Class UAuracronPCGAttributeSystemUtils *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_537_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execFilterAttributesByPattern); \
	DECLARE_FUNCTION(execAggregateAttribute); \
	DECLARE_FUNCTION(execBatchModifyAttributes); \
	DECLARE_FUNCTION(execBatchCreateAttributes); \
	DECLARE_FUNCTION(execGetAttributeEntryCount); \
	DECLARE_FUNCTION(execGetAttributeType); \
	DECLARE_FUNCTION(execGetAttributeNames); \
	DECLARE_FUNCTION(execInterpolateAttributeValues); \
	DECLARE_FUNCTION(execGetAttributeValue); \
	DECLARE_FUNCTION(execSetAttributeValue); \
	DECLARE_FUNCTION(execCopyAttribute); \
	DECLARE_FUNCTION(execValidateAttribute); \
	DECLARE_FUNCTION(execCreateAttribute);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_537_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAttributeSystemUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAttributeSystemUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAttributeSystemUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_537_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGAttributeSystemUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAttributeSystemUtils(UAuracronPCGAttributeSystemUtils&&) = delete; \
	UAuracronPCGAttributeSystemUtils(const UAuracronPCGAttributeSystemUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAttributeSystemUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAttributeSystemUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGAttributeSystemUtils) \
	NO_API virtual ~UAuracronPCGAttributeSystemUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_534_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_537_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_537_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_537_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h_537_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAttributeSystemUtils;

// ********** End Class UAuracronPCGAttributeSystemUtils *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h

// ********** Begin Enum EAuracronPCGAttributeType *************************************************
#define FOREACH_ENUM_EAURACRONPCGATTRIBUTETYPE(op) \
	op(EAuracronPCGAttributeType::Float) \
	op(EAuracronPCGAttributeType::Double) \
	op(EAuracronPCGAttributeType::Int32) \
	op(EAuracronPCGAttributeType::Int64) \
	op(EAuracronPCGAttributeType::Vector2) \
	op(EAuracronPCGAttributeType::Vector) \
	op(EAuracronPCGAttributeType::Vector4) \
	op(EAuracronPCGAttributeType::Rotator) \
	op(EAuracronPCGAttributeType::Quat) \
	op(EAuracronPCGAttributeType::Transform) \
	op(EAuracronPCGAttributeType::String) \
	op(EAuracronPCGAttributeType::Name) \
	op(EAuracronPCGAttributeType::Boolean) \
	op(EAuracronPCGAttributeType::SoftObjectPath) \
	op(EAuracronPCGAttributeType::SoftClassPath) 

enum class EAuracronPCGAttributeType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAttributeType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeType>();
// ********** End Enum EAuracronPCGAttributeType ***************************************************

// ********** Begin Enum EAuracronPCGAttributeInterpolation ****************************************
#define FOREACH_ENUM_EAURACRONPCGATTRIBUTEINTERPOLATION(op) \
	op(EAuracronPCGAttributeInterpolation::None) \
	op(EAuracronPCGAttributeInterpolation::Linear) \
	op(EAuracronPCGAttributeInterpolation::Cubic) \
	op(EAuracronPCGAttributeInterpolation::Smoothstep) \
	op(EAuracronPCGAttributeInterpolation::Smootherstep) \
	op(EAuracronPCGAttributeInterpolation::Curve) \
	op(EAuracronPCGAttributeInterpolation::Custom) 

enum class EAuracronPCGAttributeInterpolation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAttributeInterpolation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeInterpolation>();
// ********** End Enum EAuracronPCGAttributeInterpolation ******************************************

// ********** Begin Enum EAuracronPCGAttributeFilterMode *******************************************
#define FOREACH_ENUM_EAURACRONPCGATTRIBUTEFILTERMODE(op) \
	op(EAuracronPCGAttributeFilterMode::Include) \
	op(EAuracronPCGAttributeFilterMode::Exclude) \
	op(EAuracronPCGAttributeFilterMode::IncludePattern) \
	op(EAuracronPCGAttributeFilterMode::ExcludePattern) 

enum class EAuracronPCGAttributeFilterMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAttributeFilterMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeFilterMode>();
// ********** End Enum EAuracronPCGAttributeFilterMode *********************************************

// ********** Begin Enum EAuracronPCGAttributeValidation *******************************************
#define FOREACH_ENUM_EAURACRONPCGATTRIBUTEVALIDATION(op) \
	op(EAuracronPCGAttributeValidation::None) \
	op(EAuracronPCGAttributeValidation::Range) \
	op(EAuracronPCGAttributeValidation::NotNull) \
	op(EAuracronPCGAttributeValidation::NotEmpty) \
	op(EAuracronPCGAttributeValidation::Pattern) \
	op(EAuracronPCGAttributeValidation::Custom) 

enum class EAuracronPCGAttributeValidation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAttributeValidation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeValidation>();
// ********** End Enum EAuracronPCGAttributeValidation *********************************************

// ********** Begin Enum EAuracronPCGAttributeAggregation ******************************************
#define FOREACH_ENUM_EAURACRONPCGATTRIBUTEAGGREGATION(op) \
	op(EAuracronPCGAttributeAggregation::None) \
	op(EAuracronPCGAttributeAggregation::Sum) \
	op(EAuracronPCGAttributeAggregation::Average) \
	op(EAuracronPCGAttributeAggregation::Min) \
	op(EAuracronPCGAttributeAggregation::Max) \
	op(EAuracronPCGAttributeAggregation::Count) \
	op(EAuracronPCGAttributeAggregation::First) \
	op(EAuracronPCGAttributeAggregation::Last) \
	op(EAuracronPCGAttributeAggregation::Median) \
	op(EAuracronPCGAttributeAggregation::Mode) \
	op(EAuracronPCGAttributeAggregation::StandardDev) \
	op(EAuracronPCGAttributeAggregation::Variance) 

enum class EAuracronPCGAttributeAggregation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAttributeAggregation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeAggregation>();
// ********** End Enum EAuracronPCGAttributeAggregation ********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
