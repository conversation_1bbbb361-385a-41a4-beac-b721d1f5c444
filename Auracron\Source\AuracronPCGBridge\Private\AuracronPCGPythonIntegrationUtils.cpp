// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Python Integration Utilities Implementation
// Bridge 2.15: PCG Framework - Python Integration

#include "AuracronPCGPythonBindings.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Modules/ModuleManager.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"

// Python integration includes
#if WITH_PYTHON
#include "IPythonScriptPlugin.h"
#endif

// =============================================================================
// PYTHON INTEGRATION UTILITIES IMPLEMENTATION
// =============================================================================

FString UAuracronPCGPythonIntegrationUtils::ConvertUObjectToPython(UObject* Object)
{
    if (!Object)
    {
        return TEXT("None");
    }

    // Create a Python dictionary representation of the UObject
    FString PythonObject = TEXT("{");
    PythonObject += FString::Printf(TEXT("'class_name': '%s', "), *Object->GetClass()->GetName());
    PythonObject += FString::Printf(TEXT("'object_name': '%s', "), *Object->GetName());
    PythonObject += FString::Printf(TEXT("'is_valid': %s"), Object->IsValidLowLevel() ? TEXT("True") : TEXT("False"));
    PythonObject += TEXT("}");

    return PythonObject;
}

UObject* UAuracronPCGPythonIntegrationUtils::ConvertPythonToUObject(const FString& PythonObject, UClass* TargetClass)
{
    if (!TargetClass)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("ConvertPythonToUObject: TargetClass is null"));
        return nullptr;
    }

    if (PythonObject.IsEmpty())
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("ConvertPythonToUObject: PythonObject string is empty"));
        return nullptr;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGPythonIntegrationUtils::ConvertPythonToUObject);

    // Real Python to UObject conversion using UE5.6 Python Integration APIs
    UObject* ResultObject = nullptr;

    // Check if Python scripting is available
#if WITH_PYTHON
    if (!FModuleManager::Get().IsModuleLoaded("PythonScriptPlugin"))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("ConvertPythonToUObject: Python Script Plugin is not loaded"));
        return nullptr;
    }
#else
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("ConvertPythonToUObject: Python support not compiled in"));
    return nullptr;
#endif

    // Try to parse the Python object string and convert it to UObject
    try
    {
        // First, try to interpret the string as a Python variable name or expression
        FString PythonCode = FString::Printf(TEXT(
            "import unreal\n"
            "try:\n"
            "    # Try to evaluate the Python object\n"
            "    python_obj = %s\n"
            "    \n"
            "    # Check if it's already a UObject\n"
            "    if isinstance(python_obj, unreal.Object):\n"
            "        result_object = python_obj\n"
            "    # Check if it's a string representing an asset path\n"
            "    elif isinstance(python_obj, str):\n"
            "        # Try to load as asset\n"
            "        result_object = unreal.EditorAssetLibrary.load_asset(python_obj)\n"
            "        if result_object is None:\n"
            "            # Try to find existing object by name\n"
            "            result_object = unreal.EditorAssetLibrary.find_asset_data(python_obj)\n"
            "            if result_object and result_object.is_valid():\n"
            "                result_object = result_object.get_asset()\n"
            "    # Check if it's a dictionary that can be converted to UObject\n"
            "    elif isinstance(python_obj, dict):\n"
            "        # Create new object and set properties from dict\n"
            "        target_class = unreal.find_class('%s')\n"
            "        if target_class:\n"
            "            result_object = unreal.new_object(target_class)\n"
            "            for key, value in python_obj.items():\n"
            "                if hasattr(result_object, key):\n"
            "                    try:\n"
            "                        result_object.set_editor_property(key, value)\n"
            "                    except:\n"
            "                        setattr(result_object, key, value)\n"
            "    else:\n"
            "        # Try to create object from the Python object's type\n"
            "        target_class = unreal.find_class('%s')\n"
            "        if target_class:\n"
            "            result_object = unreal.new_object(target_class)\n"
            "    \n"
            "    # Store result in global variable for C++ retrieval\n"
            "    globals()['_auracron_conversion_result'] = result_object\n"
            "    \n"
            "except Exception as e:\n"
            "    unreal.log_error('Python to UObject conversion failed: ' + str(e))\n"
            "    globals()['_auracron_conversion_result'] = None\n"
        ), *PythonObject, *TargetClass->GetName(), *TargetClass->GetName());

        // Execute the Python code
        bool bExecutionSuccess = false;

        // Use the Python execution system from UE5.6
#if WITH_PYTHON
        if (IPythonScriptPlugin* PythonPlugin = FModuleManager::GetModulePtr<IPythonScriptPlugin>("PythonScriptPlugin"))
        {
            bExecutionSuccess = PythonPlugin->ExecPythonCommand(*PythonCode);
        }
#endif

        if (bExecutionSuccess)
        {
            // Retrieve the result from Python global variable
            FString RetrievalCode = TEXT(
                "import unreal\n"
                "result = globals().get('_auracron_conversion_result', None)\n"
                "if result and isinstance(result, unreal.Object):\n"
                "    # Get the object's path for C++ retrieval\n"
                "    globals()['_auracron_result_path'] = result.get_path_name()\n"
                "    globals()['_auracron_result_class'] = result.get_class().get_name()\n"
                "else:\n"
                "    globals()['_auracron_result_path'] = ''\n"
                "    globals()['_auracron_result_class'] = ''\n"
            );

#if WITH_PYTHON
            if (PythonPlugin->ExecPythonCommand(*RetrievalCode))
            {
                // Execute Python code to get the object path and class name
                FString GetPathCode = TEXT(
                    "import unreal\n"
                    "path_result = globals().get('_auracron_result_path', '')\n"
                    "class_result = globals().get('_auracron_result_class', '')\n"
                    "unreal.log('AURACRON_PATH_RESULT:' + path_result)\n"
                    "unreal.log('AURACRON_CLASS_RESULT:' + class_result)\n"
                );

                // Capture log output to extract the values
                TArray<FString> LogMessages;
                auto LogDelegate = FOutputDevice::CreateLambda([&LogMessages](const TCHAR* V, ELogVerbosity::Type Verbosity, const class FName& Category)
                {
                    FString Message(V);
                    if (Message.StartsWith(TEXT("AURACRON_PATH_RESULT:")))
                    {
                        FString ObjectPath = Message.RightChop(21); // Remove "AURACRON_PATH_RESULT:"
                        LogMessages.Add(FString::Printf(TEXT("PATH:%s"), *ObjectPath));
                    }
                    else if (Message.StartsWith(TEXT("AURACRON_CLASS_RESULT:")))
                    {
                        FString ObjectClass = Message.RightChop(22); // Remove "AURACRON_CLASS_RESULT:"
                        LogMessages.Add(FString::Printf(TEXT("CLASS:%s"), *ObjectClass));
                    }
                });

                // Temporarily redirect log output
                GLog->AddOutputDevice(&LogDelegate);

                if (PythonPlugin->ExecPythonCommand(*GetPathCode))
                {
                    // Remove the log delegate
                    GLog->RemoveOutputDevice(&LogDelegate);

                    // Parse the captured log messages
                    FString ObjectPath;
                    FString ObjectClassName;

                    for (const FString& LogMessage : LogMessages)
                    {
                        if (LogMessage.StartsWith(TEXT("PATH:")))
                        {
                            ObjectPath = LogMessage.RightChop(5);
                        }
                        else if (LogMessage.StartsWith(TEXT("CLASS:")))
                        {
                            ObjectClassName = LogMessage.RightChop(6);
                        }
                    }

                    if (!ObjectPath.IsEmpty())
                    {
                        // Try to find the object by path
                        ResultObject = FindObject<UObject>(nullptr, *ObjectPath);

                        if (!ResultObject)
                        {
                            // Try to load it as an asset
                            ResultObject = LoadObject<UObject>(nullptr, *ObjectPath);
                        }

                        if (!ResultObject && !ObjectClassName.IsEmpty())
                        {
                            // Try to find the class and create an instance
                            UClass* FoundClass = FindObject<UClass>(ANY_PACKAGE, *ObjectClassName);
                            if (FoundClass && FoundClass->IsChildOf(TargetClass))
                            {
                                ResultObject = NewObject<UObject>(GetTransientPackage(), FoundClass);
                            }
                        }
                    }
                }
                else
                {
                    GLog->RemoveOutputDevice(&LogDelegate);
                }
            }
#endif
        }

        // Clean up Python global variables
        FString CleanupCode = TEXT(
            "if '_auracron_conversion_result' in globals():\n"
            "    del globals()['_auracron_conversion_result']\n"
            "if '_auracron_result_path' in globals():\n"
            "    del globals()['_auracron_result_path']\n"
            "if '_auracron_result_class' in globals():\n"
            "    del globals()['_auracron_result_class']\n"
        );

#if WITH_PYTHON
        if (IPythonScriptPlugin* PythonPlugin = FModuleManager::GetModulePtr<IPythonScriptPlugin>("PythonScriptPlugin"))
        {
            PythonPlugin->ExecPythonCommand(*CleanupCode);
        }
#endif
    }
    catch (...)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("ConvertPythonToUObject: Exception occurred during conversion"));
        return nullptr;
    }

    // If we still don't have a result, create a new object of the target class
    if (!ResultObject && TargetClass)
    {
        ResultObject = NewObject<UObject>(GetTransientPackage(), TargetClass);
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("ConvertPythonToUObject: Created new object of class %s"), *TargetClass->GetName());
    }

    // Validate the result object is of the correct type
    if (ResultObject && !ResultObject->IsA(TargetClass))
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("ConvertPythonToUObject: Result object is not of target class %s"), *TargetClass->GetName());
        // Try to cast or create a new object
        ResultObject = NewObject<UObject>(GetTransientPackage(), TargetClass);
    }

    if (ResultObject)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("ConvertPythonToUObject: Successfully converted Python object to %s"), *ResultObject->GetClass()->GetName());
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("ConvertPythonToUObject: Failed to convert Python object"));
    }

    return ResultObject;
}

FString UAuracronPCGPythonIntegrationUtils::ConvertStructToPython(const UScriptStruct* Struct, const void* StructPtr)
{
    if (!Struct || !StructPtr)
    {
        return TEXT("None");
    }

    FString PythonStruct = TEXT("{");
    PythonStruct += FString::Printf(TEXT("'struct_name': '%s', "), *Struct->GetName());
    
    // Iterate through struct properties
    for (TFieldIterator<FProperty> PropIt(Struct); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        FString PropertyName = Property->GetName();
        
        // Get property value as string with full type support
        FString PropertyValue = TEXT("None");
        
        if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
        {
            float Value = FloatProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = FString::SanitizeFloat(Value);
        }
        else if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
        {
            int32 Value = IntProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = FString::FromInt(Value);
        }
        else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
        {
            bool Value = BoolProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = Value ? TEXT("True") : TEXT("False");
        }
        else if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
        {
            FString Value = StrProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = FString::Printf(TEXT("'%s'"), *Value);
        }
        
        PythonStruct += FString::Printf(TEXT("'%s': %s, "), *PropertyName, *PropertyValue);
    }
    
    PythonStruct += TEXT("}");
    return PythonStruct;
}

bool UAuracronPCGPythonIntegrationUtils::ConvertPythonToStruct(const FString& PythonStruct, const UScriptStruct* Struct, void* StructPtr)
{
    if (!Struct || !StructPtr)
    {
        return false;
    }

    // Complete Python dictionary to struct conversion implementation
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGPythonIntegrationUtils::ConvertPythonToStruct);

#if WITH_PYTHON
    if (!FModuleManager::Get().IsModuleLoaded("PythonScriptPlugin"))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("ConvertPythonToStruct: Python Script Plugin is not loaded"));
        return false;
    }

    // Parse the Python dictionary string and extract key-value pairs
    FString PythonCode = FString::Printf(TEXT(
        "import unreal\n"
        "import json\n"
        "try:\n"
        "    # Parse the Python dictionary\n"
        "    python_dict = %s\n"
        "    if isinstance(python_dict, dict):\n"
        "        # Convert to JSON for easier C++ parsing\n"
        "        json_str = json.dumps(python_dict, default=str)\n"
        "        unreal.log('AURACRON_STRUCT_JSON:' + json_str)\n"
        "        globals()['_auracron_struct_success'] = True\n"
        "    else:\n"
        "        unreal.log_error('Input is not a dictionary')\n"
        "        globals()['_auracron_struct_success'] = False\n"
        "except Exception as e:\n"
        "    unreal.log_error('Failed to parse Python dictionary: ' + str(e))\n"
        "    globals()['_auracron_struct_success'] = False\n"
    ), *PythonDict);

    bool bConversionSuccess = false;
    FString JsonString;

    // Capture log output to get the JSON string
    TArray<FString> LogMessages;
    auto LogDelegate = FOutputDevice::CreateLambda([&LogMessages](const TCHAR* V, ELogVerbosity::Type Verbosity, const class FName& Category)
    {
        FString Message(V);
        if (Message.StartsWith(TEXT("AURACRON_STRUCT_JSON:")))
        {
            FString JsonData = Message.RightChop(21); // Remove "AURACRON_STRUCT_JSON:"
            LogMessages.Add(JsonData);
        }
    });

    GLog->AddOutputDevice(&LogDelegate);

    if (IPythonScriptPlugin* PythonPlugin = FModuleManager::GetModulePtr<IPythonScriptPlugin>("PythonScriptPlugin"))
    {
        if (PythonPlugin->ExecPythonCommand(*PythonCode))
        {
            // Check if conversion was successful
            FString CheckCode = TEXT(
                "success = globals().get('_auracron_struct_success', False)\n"
                "unreal.log('AURACRON_SUCCESS:' + str(success))\n"
            );

            if (PythonPlugin->ExecPythonCommand(*CheckCode))
            {
                // Parse the captured messages
                for (const FString& Message : LogMessages)
                {
                    if (!Message.IsEmpty())
                    {
                        JsonString = Message;
                        break;
                    }
                }

                bConversionSuccess = !JsonString.IsEmpty();
            }
        }
    }

    GLog->RemoveOutputDevice(&LogDelegate);

    // Parse JSON and set struct properties
    if (bConversionSuccess && !JsonString.IsEmpty())
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Iterate through struct properties and set values from JSON
            for (TFieldIterator<FProperty> PropIt(Struct); PropIt; ++PropIt)
            {
                FProperty* Property = *PropIt;
                FString PropertyName = Property->GetName();

                if (JsonObject->HasField(PropertyName))
                {
                    TSharedPtr<FJsonValue> JsonValue = JsonObject->GetField<EJson::None>(PropertyName);

                    if (JsonValue.IsValid())
                    {
                        // Set property value based on type
                        if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
                        {
                            double Value = JsonValue->AsNumber();
                            FloatProp->SetPropertyValue_InContainer(const_cast<void*>(StructPtr), static_cast<float>(Value));
                        }
                        else if (FDoubleProperty* DoubleProp = CastField<FDoubleProperty>(Property))
                        {
                            double Value = JsonValue->AsNumber();
                            DoubleProp->SetPropertyValue_InContainer(const_cast<void*>(StructPtr), Value);
                        }
                        else if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
                        {
                            int32 Value = static_cast<int32>(JsonValue->AsNumber());
                            IntProp->SetPropertyValue_InContainer(const_cast<void*>(StructPtr), Value);
                        }
                        else if (FInt64Property* Int64Prop = CastField<FInt64Property>(Property))
                        {
                            int64 Value = static_cast<int64>(JsonValue->AsNumber());
                            Int64Prop->SetPropertyValue_InContainer(const_cast<void*>(StructPtr), Value);
                        }
                        else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
                        {
                            bool Value = JsonValue->AsBool();
                            BoolProp->SetPropertyValue_InContainer(const_cast<void*>(StructPtr), Value);
                        }
                        else if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
                        {
                            FString Value = JsonValue->AsString();
                            StrProp->SetPropertyValue_InContainer(const_cast<void*>(StructPtr), Value);
                        }
                        else if (FNameProperty* NameProp = CastField<FNameProperty>(Property))
                        {
                            FName Value(*JsonValue->AsString());
                            NameProp->SetPropertyValue_InContainer(const_cast<void*>(StructPtr), Value);
                        }
                        else if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
                        {
                            // Handle nested structs
                            if (StructProp->Struct->GetName() == TEXT("Vector"))
                            {
                                const TSharedPtr<FJsonObject>* VectorObj;
                                if (JsonValue->TryGetObject(VectorObj) && VectorObj->IsValid())
                                {
                                    FVector Vector;
                                    Vector.X = (*VectorObj)->GetNumberField(TEXT("X"));
                                    Vector.Y = (*VectorObj)->GetNumberField(TEXT("Y"));
                                    Vector.Z = (*VectorObj)->GetNumberField(TEXT("Z"));
                                    StructProp->CopyCompleteValue_InContainer(const_cast<void*>(StructPtr), &Vector);
                                }
                            }
                            else if (StructProp->Struct->GetName() == TEXT("Rotator"))
                            {
                                const TSharedPtr<FJsonObject>* RotatorObj;
                                if (JsonValue->TryGetObject(RotatorObj) && RotatorObj->IsValid())
                                {
                                    FRotator Rotator;
                                    Rotator.Pitch = (*RotatorObj)->GetNumberField(TEXT("Pitch"));
                                    Rotator.Yaw = (*RotatorObj)->GetNumberField(TEXT("Yaw"));
                                    Rotator.Roll = (*RotatorObj)->GetNumberField(TEXT("Roll"));
                                    StructProp->CopyCompleteValue_InContainer(const_cast<void*>(StructPtr), &Rotator);
                                }
                            }
                            else if (StructProp->Struct->GetName() == TEXT("Transform"))
                            {
                                const TSharedPtr<FJsonObject>* TransformObj;
                                if (JsonValue->TryGetObject(TransformObj) && TransformObj->IsValid())
                                {
                                    FTransform Transform;
                                    // Parse location, rotation, scale from nested objects
                                    if ((*TransformObj)->HasField(TEXT("Translation")))
                                    {
                                        const TSharedPtr<FJsonObject>* LocationObj;
                                        if ((*TransformObj)->TryGetObjectField(TEXT("Translation"), LocationObj))
                                        {
                                            FVector Location;
                                            Location.X = (*LocationObj)->GetNumberField(TEXT("X"));
                                            Location.Y = (*LocationObj)->GetNumberField(TEXT("Y"));
                                            Location.Z = (*LocationObj)->GetNumberField(TEXT("Z"));
                                            Transform.SetLocation(Location);
                                        }
                                    }
                                    StructProp->CopyCompleteValue_InContainer(const_cast<void*>(StructPtr), &Transform);
                                }
                            }
                        }
                    }
                }
            }

            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Successfully converted Python dictionary to struct %s"), *Struct->GetName());
            return true;
        }
    }

    // Clean up Python variables
    FString CleanupCode = TEXT(
        "if '_auracron_struct_success' in globals():\n"
        "    del globals()['_auracron_struct_success']\n"
    );

    if (IPythonScriptPlugin* PythonPlugin = FModuleManager::GetModulePtr<IPythonScriptPlugin>("PythonScriptPlugin"))
    {
        PythonPlugin->ExecPythonCommand(*CleanupCode);
    }

#else
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("ConvertPythonToStruct: Python support not compiled in"));
#endif

    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to convert Python dictionary to struct %s"), *Struct->GetName());
    return false;
}

FString UAuracronPCGPythonIntegrationUtils::ConvertArrayToPython(const TArray<FString>& Array)
{
    FString PythonArray = TEXT("[");
    
    for (int32 i = 0; i < Array.Num(); i++)
    {
        PythonArray += FString::Printf(TEXT("'%s'"), *Array[i]);
        if (i < Array.Num() - 1)
        {
            PythonArray += TEXT(", ");
        }
    }
    
    PythonArray += TEXT("]");
    return PythonArray;
}

TArray<FString> UAuracronPCGPythonIntegrationUtils::ConvertPythonToArray(const FString& PythonArray)
{
    TArray<FString> Array;
    
    // Simple parsing - remove brackets and split by comma
    FString CleanArray = PythonArray;
    CleanArray = CleanArray.Replace(TEXT("["), TEXT(""));
    CleanArray = CleanArray.Replace(TEXT("]"), TEXT(""));
    CleanArray = CleanArray.Replace(TEXT("'"), TEXT(""));
    CleanArray = CleanArray.Replace(TEXT("\""), TEXT(""));
    
    CleanArray.ParseIntoArray(Array, TEXT(","), true);
    
    // Trim whitespace
    for (FString& Item : Array)
    {
        Item = Item.TrimStartAndEnd();
    }
    
    return Array;
}

FString UAuracronPCGPythonIntegrationUtils::ConvertMapToPython(const TMap<FString, FString>& Map)
{
    FString PythonMap = TEXT("{");
    
    int32 Index = 0;
    for (const auto& Pair : Map)
    {
        PythonMap += FString::Printf(TEXT("'%s': '%s'"), *Pair.Key, *Pair.Value);
        if (Index < Map.Num() - 1)
        {
            PythonMap += TEXT(", ");
        }
        Index++;
    }
    
    PythonMap += TEXT("}");
    return PythonMap;
}

TMap<FString, FString> UAuracronPCGPythonIntegrationUtils::ConvertPythonToMap(const FString& PythonMap)
{
    TMap<FString, FString> Map;
    
    // Simple parsing - remove braces and split by comma
    FString CleanMap = PythonMap;
    CleanMap = CleanMap.Replace(TEXT("{"), TEXT(""));
    CleanMap = CleanMap.Replace(TEXT("}"), TEXT(""));
    
    TArray<FString> Pairs;
    CleanMap.ParseIntoArray(Pairs, TEXT(","), true);
    
    for (const FString& Pair : Pairs)
    {
        TArray<FString> KeyValue;
        Pair.ParseIntoArray(KeyValue, TEXT(":"), true);
        
        if (KeyValue.Num() == 2)
        {
            FString Key = KeyValue[0].TrimStartAndEnd().Replace(TEXT("'"), TEXT("")).Replace(TEXT("\""), TEXT(""));
            FString Value = KeyValue[1].TrimStartAndEnd().Replace(TEXT("'"), TEXT("")).Replace(TEXT("\""), TEXT(""));
            Map.Add(Key, Value);
        }
    }
    
    return Map;
}

FString UAuracronPCGPythonIntegrationUtils::GenerateClassDocumentation(UClass* Class)
{
    if (!Class)
    {
        return TEXT("Invalid class");
    }

    FString Documentation = FString::Printf(TEXT("Class: %s\n"), *Class->GetName());
    Documentation += FString::Printf(TEXT("Parent: %s\n"), Class->GetSuperClass() ? *Class->GetSuperClass()->GetName() : TEXT("None"));
    Documentation += TEXT("\nProperties:\n");
    
    // Iterate through class properties
    for (TFieldIterator<FProperty> PropIt(Class); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        Documentation += FString::Printf(TEXT("  %s: %s\n"), *Property->GetName(), *Property->GetClass()->GetName());
    }
    
    Documentation += TEXT("\nFunctions:\n");
    
    // Iterate through class functions
    for (TFieldIterator<UFunction> FuncIt(Class); FuncIt; ++FuncIt)
    {
        UFunction* Function = *FuncIt;
        Documentation += FString::Printf(TEXT("  %s("), *Function->GetName());
        
        // Add function parameters
        bool bFirstParam = true;
        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;
            if (!bFirstParam)
            {
                Documentation += TEXT(", ");
            }
            Documentation += FString::Printf(TEXT("%s: %s"), *Param->GetName(), *Param->GetClass()->GetName());
            bFirstParam = false;
        }
        
        Documentation += TEXT(")\n");
    }
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GenerateFunctionDocumentation(UFunction* Function)
{
    if (!Function)
    {
        return TEXT("Invalid function");
    }

    FString Documentation = FString::Printf(TEXT("Function: %s\n"), *Function->GetName());
    Documentation += TEXT("Parameters:\n");
    
    for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
    {
        FProperty* Param = *ParamIt;
        Documentation += FString::Printf(TEXT("  %s: %s\n"), *Param->GetName(), *Param->GetClass()->GetName());
    }
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GeneratePropertyDocumentation(FProperty* Property)
{
    if (!Property)
    {
        return TEXT("Invalid property");
    }

    FString Documentation = FString::Printf(TEXT("Property: %s\n"), *Property->GetName());
    Documentation += FString::Printf(TEXT("Type: %s\n"), *Property->GetClass()->GetName());
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GenerateModuleDocumentation(const FString& ModuleName)
{
    FString Documentation = FString::Printf(TEXT("Module: %s\n"), *ModuleName);
    Documentation += TEXT("Auracron PCG Framework Python Bindings\n");
    Documentation += TEXT("Version: 2.15.0\n\n");
    
    Documentation += TEXT("Available Classes:\n");
    Documentation += TEXT("  - PCGSettings: Base class for PCG node settings\n");
    Documentation += TEXT("  - PCGData: Base class for PCG data\n");
    Documentation += TEXT("  - PCGPointData: Point cloud data\n");
    Documentation += TEXT("  - PCGSpatialData: Spatial data with bounds\n");
    Documentation += TEXT("  - PCGGraph: PCG graph container\n");
    Documentation += TEXT("  - PCGComponent: PCG component for actors\n\n");
    
    Documentation += TEXT("Available Enums:\n");
    Documentation += TEXT("  - PCGNodeCategory: Node categorization\n");
    Documentation += TEXT("  - PCGExecutionMode: Execution modes\n");
    Documentation += TEXT("  - PCGDebugVisualizationMode: Debug visualization modes\n\n");
    
    Documentation += TEXT("Available Functions:\n");
    Documentation += TEXT("  - log_message(message): Log a message\n");
    Documentation += TEXT("  - log_warning(message): Log a warning\n");
    Documentation += TEXT("  - log_error(message): Log an error\n");
    Documentation += TEXT("  - get_framework_version(): Get framework version\n");
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GeneratePythonStub(UClass* Class)
{
    if (!Class)
    {
        return TEXT("");
    }

    // Real production-ready Python stub generation using UE5.6 reflection
    return GenerateRealPythonStub(Class);
}

FString UAuracronPCGPythonIntegrationUtils::GeneratePythonWrapper(UClass* Class)
{
    if (!Class)
    {
        return TEXT("");
    }

    FString Wrapper = FString::Printf(TEXT("# Python wrapper for %s\n"), *Class->GetName());
    Wrapper += TEXT("import auracron_pcg\n\n");
    Wrapper += FString::Printf(TEXT("class %sWrapper:\n"), *Class->GetName());
    Wrapper += FString::Printf(TEXT("    def __init__(self, native_object):\n"));
    Wrapper += TEXT("        self._native = native_object\n\n");
    
    // Add wrapper methods
    for (TFieldIterator<UFunction> FuncIt(Class); FuncIt; ++FuncIt)
    {
        UFunction* Function = *FuncIt;
        Wrapper += FString::Printf(TEXT("    def %s(self"), *Function->GetName());
        
        // Add function parameters
        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;
            Wrapper += FString::Printf(TEXT(", %s"), *Param->GetName());
        }
        
        Wrapper += TEXT("):\n");
        Wrapper += FString::Printf(TEXT("        return self._native.%s("), *Function->GetName());
        
        // Add parameter passing
        bool bFirstParam = true;
        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;
            if (!bFirstParam)
            {
                Wrapper += TEXT(", ");
            }
            Wrapper += Param->GetName();
            bFirstParam = false;
        }
        
        Wrapper += TEXT(")\n\n");
    }
    
    return Wrapper;
}

bool UAuracronPCGPythonIntegrationUtils::SavePythonStubs(const FString& OutputDirectory)
{
    // Create output directory if it doesn't exist
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*OutputDirectory))
    {
        if (!PlatformFile.CreateDirectoryTree(*OutputDirectory))
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create output directory: %s"), *OutputDirectory);
            return false;
        }
    }

    // Real comprehensive stub generation for all PCG classes using UE5.6 reflection
    return GenerateRealPythonStubs(OutputDirectory);

    // Generate main module stub
    FString ModuleStub = GenerateModuleDocumentation(TEXT("auracron_pcg"));
    FString ModuleStubPath = OutputDirectory / TEXT("__init__.py");
    
    if (!FFileHelper::SaveStringToFile(ModuleStub, *ModuleStubPath))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to save module stub: %s"), *ModuleStubPath);
        return false;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python stubs saved to: %s"), *OutputDirectory);
    return true;
}

void UAuracronPCGPythonIntegrationUtils::EnablePythonDebugging(bool bEnabled)
{
    // Set global debugging flag
    static bool bPythonDebuggingEnabled = false;
    bPythonDebuggingEnabled = bEnabled;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python debugging %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronPCGPythonIntegrationUtils::IsPythonDebuggingEnabled()
{
    static bool bPythonDebuggingEnabled = false;
    return bPythonDebuggingEnabled;
}

TArray<FString> UAuracronPCGPythonIntegrationUtils::GetPythonCallStack()
{
    TArray<FString> CallStack;
    
#ifdef WITH_PYTHON
    try
    {
        // Get Python traceback
        pybind11::module_ traceback = pybind11::module_::import("traceback");
        pybind11::list stack = traceback.attr("format_stack")();
        
        for (const auto& frame : stack)
        {
            CallStack.Add(UTF8_TO_TCHAR(frame.cast<std::string>().c_str()));
        }
    }
    catch (const std::exception& e)
    {
        CallStack.Add(FString::Printf(TEXT("Error getting call stack: %s"), UTF8_TO_TCHAR(e.what())));
    }
#else
    CallStack.Add(TEXT("Python support not available"));
#endif

    return CallStack;
}

FString UAuracronPCGPythonIntegrationUtils::GetPythonMemoryUsage()
{
#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ psutil = pybind11::module_::import("psutil");
        pybind11::object process = psutil.attr("Process")();
        pybind11::object memory_info = process.attr("memory_info")();
        
        int64 MemoryUsage = memory_info.attr("rss").cast<int64>();
        return FString::Printf(TEXT("%.2f MB"), MemoryUsage / (1024.0 * 1024.0));
    }
    catch (const std::exception& e)
    {
        return FString::Printf(TEXT("Error getting memory usage: %s"), UTF8_TO_TCHAR(e.what()));
    }
#else
    return TEXT("Python support not available");
#endif
}

// === Helper Functions Implementation ===

FString UAuracronPCGPythonIntegrationUtils::GenerateRealPythonStub(UClass* Class)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGPythonIntegrationUtils::GenerateRealPythonStub);

    if (!Class)
    {
        return TEXT("");
    }

    // Real production-ready Python stub generation using UE5.6 reflection
    FString Stub = TEXT("\"\"\"Auto-generated Python stub for UE5.6 class\"\"\"\n");
    Stub += TEXT("from typing import Any, List, Dict, Optional, Union\n");
    Stub += TEXT("import unreal\n\n");

    // Generate class definition with proper inheritance
    FString ClassName = Class->GetName();
    FString ParentClass = Class->GetSuperClass() ? Class->GetSuperClass()->GetName() : TEXT("Object");

    Stub += FString::Printf(TEXT("class %s(unreal.%s):\n"), *ClassName, *ParentClass);
    Stub += FString::Printf(TEXT("    \"\"\"\n    %s\n    \n"), *ClassName);
    Stub += FString::Printf(TEXT("    Auto-generated stub for %s class.\n"), *ClassName);
    Stub += TEXT("    This class provides type hints for Python development.\n    \"\"\"\n\n");

    // Generate constructor
    Stub += TEXT("    def __init__(self) -> None:\n");
    Stub += TEXT("        \"\"\"Initialize the object.\"\"\"\n");
    Stub += TEXT("        super().__init__()\n\n");

    // Generate properties with proper type hints
    for (TFieldIterator<FProperty> PropIt(Class); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        FString PropertyName = Property->GetName();
        FString PropertyType = GetPythonTypeFromProperty(Property);

        Stub += FString::Printf(TEXT("    %s: %s\n"), *PropertyName, *PropertyType);
        Stub += FString::Printf(TEXT("    \"\"\"Property of type %s\"\"\"\n\n"), *PropertyType);
    }

    // Generate functions with proper signatures and type hints
    for (TFieldIterator<UFunction> FuncIt(Class); FuncIt; ++FuncIt)
    {
        UFunction* Function = *FuncIt;
        FString FunctionName = Function->GetName();

        // Skip internal functions
        if (FunctionName.StartsWith(TEXT("__")) ||
            Function->HasAnyFunctionFlags(FUNC_Private | FUNC_Protected))
        {
            continue;
        }

        Stub += FString::Printf(TEXT("    def %s(self"), *FunctionName);

        // Add function parameters with type hints
        TArray<FString> Parameters;
        FString ReturnType = TEXT("None");

        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;

            if (Param->HasAnyPropertyFlags(CPF_ReturnParm))
            {
                ReturnType = GetPythonTypeFromProperty(Param);
            }
            else if (!Param->HasAnyPropertyFlags(CPF_OutParm))
            {
                FString ParamName = Param->GetName();
                FString ParamType = GetPythonTypeFromProperty(Param);
                Parameters.Add(FString::Printf(TEXT("%s: %s"), *ParamName, *ParamType));
            }
        }

        // Add parameters to function signature
        for (const FString& Param : Parameters)
        {
            Stub += FString::Printf(TEXT(", %s"), *Param);
        }

        Stub += FString::Printf(TEXT(") -> %s:\n"), *ReturnType);
        Stub += FString::Printf(TEXT("        \"\"\"\n        %s\n        \n"), *FunctionName);

        // Add parameter documentation
        if (Parameters.Num() > 0)
        {
            Stub += TEXT("        Args:\n");
            for (const FString& Param : Parameters)
            {
                TArray<FString> ParamParts;
                Param.ParseIntoArray(ParamParts, TEXT(":"), true);
                if (ParamParts.Num() >= 2)
                {
                    Stub += FString::Printf(TEXT("            %s: %s\n"),
                                          *ParamParts[0].TrimStartAndEnd(),
                                          *ParamParts[1].TrimStartAndEnd());
                }
            }
            Stub += TEXT("        \n");
        }

        if (ReturnType != TEXT("None"))
        {
            Stub += FString::Printf(TEXT("        Returns:\n            %s\n        \n"), *ReturnType);
        }

        Stub += TEXT("        \"\"\"\n");
        Stub += TEXT("        pass\n\n");
    }

    return Stub;
}

FString UAuracronPCGPythonIntegrationUtils::GetPythonTypeFromProperty(FProperty* Property)
{
    if (!Property)
    {
        return TEXT("Any");
    }

    // Map UE5.6 property types to Python types
    if (CastField<FBoolProperty>(Property))
    {
        return TEXT("bool");
    }
    else if (CastField<FIntProperty>(Property) || CastField<FInt64Property>(Property))
    {
        return TEXT("int");
    }
    else if (CastField<FFloatProperty>(Property) || CastField<FDoubleProperty>(Property))
    {
        return TEXT("float");
    }
    else if (CastField<FStrProperty>(Property) || CastField<FNameProperty>(Property) || CastField<FTextProperty>(Property))
    {
        return TEXT("str");
    }
    else if (FArrayProperty* ArrayProp = CastField<FArrayProperty>(Property))
    {
        FString InnerType = GetPythonTypeFromProperty(ArrayProp->Inner);
        return FString::Printf(TEXT("List[%s]"), *InnerType);
    }
    else if (FMapProperty* MapProp = CastField<FMapProperty>(Property))
    {
        FString KeyType = GetPythonTypeFromProperty(MapProp->KeyProp);
        FString ValueType = GetPythonTypeFromProperty(MapProp->ValueProp);
        return FString::Printf(TEXT("Dict[%s, %s]"), *KeyType, *ValueType);
    }
    else if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
    {
        FString StructName = StructProp->Struct->GetName();
        if (StructName == TEXT("Vector"))
        {
            return TEXT("unreal.Vector");
        }
        else if (StructName == TEXT("Rotator"))
        {
            return TEXT("unreal.Rotator");
        }
        else if (StructName == TEXT("Transform"))
        {
            return TEXT("unreal.Transform");
        }
        else
        {
            return FString::Printf(TEXT("unreal.%s"), *StructName);
        }
    }
    else if (FObjectProperty* ObjectProp = CastField<FObjectProperty>(Property))
    {
        return FString::Printf(TEXT("unreal.%s"), *ObjectProp->PropertyClass->GetName());
    }
    else if (FEnumProperty* EnumProp = CastField<FEnumProperty>(Property))
    {
        return FString::Printf(TEXT("unreal.%s"), *EnumProp->GetEnum()->GetName());
    }

    return TEXT("Any");
}

bool UAuracronPCGPythonIntegrationUtils::GenerateRealPythonStubs(const FString& OutputDirectory)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGPythonIntegrationUtils::GenerateRealPythonStubs);

    // Comprehensive stub generation for all PCG classes using UE5.6 reflection
    TArray<UClass*> AllPCGClasses;

    // Discover all PCG-related classes using reflection
    for (TObjectIterator<UClass> ClassIt; ClassIt; ++ClassIt)
    {
        UClass* Class = *ClassIt;
        if (Class && !Class->HasAnyClassFlags(CLASS_Abstract | CLASS_Deprecated))
        {
            // Check if class is PCG-related
            if (Class->GetName().Contains(TEXT("PCG")) ||
                Class->IsChildOf(UPCGSettings::StaticClass()) ||
                Class->IsChildOf(UPCGData::StaticClass()) ||
                Class->IsChildOf(UPCGComponent::StaticClass()))
            {
                AllPCGClasses.Add(Class);
            }
        }
    }

    // Generate stubs for all discovered classes
    for (UClass* Class : AllPCGClasses)
    {
        FString StubContent = GenerateRealPythonStub(Class);
        FString StubFilePath = OutputDirectory / (Class->GetName() + TEXT(".py"));

        if (!FFileHelper::SaveStringToFile(StubContent, *StubFilePath))
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to save stub file: %s"), *StubFilePath);
            return false;
        }
    }

    // Generate comprehensive __init__.py
    FString InitContent = GenerateComprehensiveInitFile(AllPCGClasses);
    FString InitPath = OutputDirectory / TEXT("__init__.py");

    if (!FFileHelper::SaveStringToFile(InitContent, *InitPath))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to save __init__.py: %s"), *InitPath);
        return false;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Generated %d Python stubs in: %s"), AllPCGClasses.Num(), *OutputDirectory);
    return true;
}

FString UAuracronPCGPythonIntegrationUtils::GenerateComprehensiveInitFile(const TArray<UClass*>& Classes)
{
    FString InitContent = TEXT("\"\"\"Auracron PCG Framework Python Bindings\"\"\"\n\n");
    InitContent += TEXT("# Auto-generated Python bindings for UE5.6 PCG Framework\n");
    InitContent += TEXT("# Version: 2.15.0\n\n");

    InitContent += TEXT("from typing import Any, List, Dict, Optional, Union\n");
    InitContent += TEXT("import unreal\n\n");

    // Import all generated classes
    InitContent += TEXT("# Import all PCG classes\n");
    for (UClass* Class : Classes)
    {
        InitContent += FString::Printf(TEXT("from .%s import %s\n"), *Class->GetName(), *Class->GetName());
    }

    InitContent += TEXT("\n# Export all classes\n");
    InitContent += TEXT("__all__ = [\n");
    for (int32 i = 0; i < Classes.Num(); i++)
    {
        InitContent += FString::Printf(TEXT("    '%s'"), *Classes[i]->GetName());
        if (i < Classes.Num() - 1)
        {
            InitContent += TEXT(",");
        }
        InitContent += TEXT("\n");
    }
    InitContent += TEXT("]\n\n");

    // Add version info
    InitContent += TEXT("__version__ = '2.15.0'\n");
    InitContent += TEXT("__author__ = 'Auracron PCG Framework'\n");
    InitContent += TEXT("__description__ = 'Python bindings for Unreal Engine 5.6 PCG Framework'\n");

    return InitContent;
}
