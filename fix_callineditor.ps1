# Script para corrigir CallInEditor = true/false para apenas CallInEditor

Write-Host "Corrigindo especificadores CallInEditor..."

# Encontrar todos os arquivos .h que contêm CallInEditor
$headerFiles = Get-ChildItem "Auracron\Source" -Recurse -Filter "*.h" | Where-Object { 
    (Get-Content $_.FullName -Raw) -match "CallInEditor\s*=" 
}

foreach ($file in $headerFiles) {
    Write-Host "Processando: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    
    # Corrigir CallInEditor = true para apenas CallInEditor
    $content = $content -replace 'CallInEditor\s*=\s*true', 'CallInEditor'
    
    # Corrigir CallInEditor = false removendo completamente (não é necessário)
    $content = $content -replace ',\s*CallInEditor\s*=\s*false', ''
    $content = $content -replace 'CallInEditor\s*=\s*false,\s*', ''
    $content = $content -replace 'CallInEditor\s*=\s*false', ''
    
    # Salvar o arquivo
    Set-Content $file.FullName $content -Encoding UTF8
    
    Write-Host "Corrigido: $($file.Name)"
}

Write-Host "Correção de CallInEditor concluída!"
