// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Spline System Advanced Utilities Implementation
// Bridge 2.8: PCG Framework - Spline System

#include "AuracronPCGSplineSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Spline includes
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "PCGSplineData.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

namespace AuracronPCGSplineSystemUtils
{
    // =============================================================================
    // PCG DATA CONVERSION
    // =============================================================================

    bool CreateSplineFromPCGData(const UPCGSplineData* SplineData, USplineComponent* OutSplineComponent)
    {
        if (!SplineData || !OutSplineComponent)
        {
            return false;
        }

        // Extract spline information from PCG spline data using UE5.6 APIs
        
        // Clear existing points
        OutSplineComponent->ClearSplinePoints();

        // Get the spline component from PCG spline data
        USplineComponent* SourceSpline = SplineData->GetSplineComponent();
        if (!SourceSpline)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("CreateSplineFromPCGData - No source spline component found in PCG data"));
            return false;
        }

        // Extract spline points with full transform data
        int32 NumSplinePoints = SourceSpline->GetNumberOfSplinePoints();
        TArray<FVector> SplinePoints;
        TArray<FVector> SplineTangents;
        TArray<FRotator> SplineRotations;
        TArray<FVector> SplineScales;
        
        SplinePoints.Reserve(NumSplinePoints);
        SplineTangents.Reserve(NumSplinePoints);
        SplineRotations.Reserve(NumSplinePoints);
        SplineScales.Reserve(NumSplinePoints);
        
        for (int32 i = 0; i < NumSplinePoints; ++i)
        {
            // Get point location in world space
            FVector PointLocation = SourceSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            SplinePoints.Add(PointLocation);
            
            // Get tangent vectors
            FVector ArriveTangent = SourceSpline->GetArriveTangentAtSplinePoint(i, ESplineCoordinateSpace::World);
            FVector LeaveTangent = SourceSpline->GetLeaveTangentAtSplinePoint(i, ESplineCoordinateSpace::World);
            SplineTangents.Add((ArriveTangent + LeaveTangent) * 0.5f); // Average tangent
            
            // Get rotation
            FRotator PointRotation = SourceSpline->GetRotationAtSplinePoint(i, ESplineCoordinateSpace::World);
            SplineRotations.Add(PointRotation);
            
            // Get scale
            FVector PointScale = SourceSpline->GetScaleAtSplinePoint(i);
            SplineScales.Add(PointScale);
        }
        
        // Copy spline properties
        OutSplineComponent->SetClosedLoop(SourceSpline->IsClosedLoop());
        OutSplineComponent->SetSplinePointType(0, SourceSpline->GetSplinePointType(0));
        
        // Add points to output spline component with full transform data
        for (int32 i = 0; i < SplinePoints.Num(); ++i)
        {
            OutSplineComponent->AddSplinePoint(SplinePoints[i], ESplineCoordinateSpace::World);
            
            // Set tangents
            if (i < SplineTangents.Num())
            {
                OutSplineComponent->SetTangentAtSplinePoint(i, SplineTangents[i], ESplineCoordinateSpace::World);
            }
            
            // Set rotation
            if (i < SplineRotations.Num())
            {
                OutSplineComponent->SetRotationAtSplinePoint(i, SplineRotations[i], ESplineCoordinateSpace::World);
            }
            
            // Set scale
            if (i < SplineScales.Num())
            {
                OutSplineComponent->SetScaleAtSplinePoint(i, SplineScales[i]);
            }
            
            // Set spline point type (copy from source)
            if (i < NumSplinePoints)
            {
                ESplinePointType::Type PointType = SourceSpline->GetSplinePointType(i);
                OutSplineComponent->SetSplinePointType(i, PointType);
            }
        }
        
        // Copy additional spline properties
        OutSplineComponent->SetDefaultUpVector(SourceSpline->GetDefaultUpVector(ESplineCoordinateSpace::World), ESplineCoordinateSpace::World);
        
        // Update the spline
        OutSplineComponent->UpdateSpline();
        
        AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("CreateSplineFromPCGData - Successfully created spline with %d points"), SplinePoints.Num());
        return true;
    }

    UPCGSplineData* CreatePCGDataFromSpline(USplineComponent* SplineComponent)
    {
        if (!SplineComponent)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("CreatePCGDataFromSpline - Invalid spline component"));
            return nullptr;
        }

        // Create new PCG spline data object
        UPCGSplineData* SplineData = NewObject<UPCGSplineData>();
        if (!SplineData)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("CreatePCGDataFromSpline - Failed to create PCG spline data object"));
            return nullptr;
        }
        
        // Initialize PCG spline data from spline component using UE5.6 APIs
        try
        {
            // Set the spline component reference
            SplineData->Initialize(SplineComponent);
            
            // Validate the spline has points
            int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
            if (NumPoints < 2)
            {
                AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("CreatePCGDataFromSpline - Spline must have at least 2 points, found %d"), NumPoints);
                return nullptr;
            }
            
            // Configure spline data properties
            FTransform SplineTransform = SplineComponent->GetComponentTransform();
            SplineData->SetTransform(SplineTransform);
            
            // Set spline metadata if available
            if (SplineComponent->GetOwner())
            {
                FString SplineName = SplineComponent->GetOwner()->GetName() + TEXT("_") + SplineComponent->GetName();
                // Note: PCG spline data doesn't have a direct SetName method in UE5.6
                // The name is typically managed by the PCG system internally
            }
            
            // Calculate and set bounds
            FBox SplineBounds = SplineComponent->CalcBounds(SplineTransform).GetBox();
            // Note: Bounds are typically calculated automatically by the PCG system
            
            // Log spline properties for debugging
            float SplineLength = SplineComponent->GetSplineLength();
            bool bIsClosedLoop = SplineComponent->IsClosedLoop();
            
            AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("CreatePCGDataFromSpline - Created PCG spline data: Points=%d, Length=%.2f, ClosedLoop=%s"), 
                   NumPoints, SplineLength, bIsClosedLoop ? TEXT("true") : TEXT("false"));
            
            // Validate the created spline data
            if (!SplineData->GetSplineComponent())
            {
                AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("CreatePCGDataFromSpline - Failed to properly initialize spline component reference"));
                return nullptr;
            }
            
            return SplineData;
        }
        catch (const std::exception& e)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("CreatePCGDataFromSpline - Exception during spline data creation: %s"), 
                   *FString(e.what()));
            return nullptr;
        }
        catch (...)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("CreatePCGDataFromSpline - Unknown exception during spline data creation"));
            return nullptr;
        }
    }

    // =============================================================================
    // SPLINE INTERPOLATION
    // =============================================================================

    bool InterpolateSplinePoints(const TArray<FVector>& ControlPoints, TArray<FVector>& OutInterpolatedPoints, int32 PointsPerSegment)
    {
        OutInterpolatedPoints.Empty();
        
        if (ControlPoints.Num() < 2)
        {
            return false;
        }

        OutInterpolatedPoints.Reserve((ControlPoints.Num() - 1) * PointsPerSegment + 1);

        // Interpolate between each pair of control points
        for (int32 i = 0; i < ControlPoints.Num() - 1; i++)
        {
            FVector StartPoint = ControlPoints[i];
            FVector EndPoint = ControlPoints[i + 1];

            // Add interpolated points for this segment
            for (int32 j = 0; j < PointsPerSegment; j++)
            {
                float T = static_cast<float>(j) / PointsPerSegment;
                FVector InterpolatedPoint = FMath::Lerp(StartPoint, EndPoint, T);
                OutInterpolatedPoints.Add(InterpolatedPoint);
            }
        }

        // Add the final point
        OutInterpolatedPoints.Add(ControlPoints.Last());

        return true;
    }

    // =============================================================================
    // PATH FINDING ALGORITHMS
    // =============================================================================

    float CalculatePathCost(const TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor)
    {
        if (Path.Num() < 2)
        {
            return FLT_MAX;
        }

        float TotalCost = 0.0f;

        for (int32 i = 1; i < Path.Num(); i++)
        {
            FVector Segment = Path[i] - Path[i-1];
            float SegmentLength = Segment.Size();
            
            // Distance cost
            if (PathFindingDescriptor.bUseDistanceCost)
            {
                TotalCost += SegmentLength * PathFindingDescriptor.DistanceCostMultiplier;
            }

            // Terrain cost (slope)
            if (PathFindingDescriptor.bUseTerrainCost)
            {
                float Slope = FMath::Abs(FMath::Atan2(Segment.Z, FVector2D(Segment.X, Segment.Y).Size()));
                float SlopeDegrees = FMath::RadiansToDegrees(Slope);
                float SlopeCost = (SlopeDegrees / 90.0f) * PathFindingDescriptor.SlopeCostMultiplier;
                TotalCost += SlopeCost * SegmentLength;
            }
        }

        return TotalCost;
    }

    bool ValidateSplineForMeshGeneration(USplineComponent* SplineComponent, const FAuracronPCGSplineMeshDescriptor& MeshDescriptor)
    {
        if (!SplineComponent)
        {
            return false;
        }

        // Check if spline has enough points
        if (SplineComponent->GetNumberOfSplinePoints() < 2)
        {
            return false;
        }

        // Check if mesh is valid
        UStaticMesh* StaticMesh = MeshDescriptor.StaticMesh.LoadSynchronous();
        if (!StaticMesh)
        {
            return false;
        }

        // Check spline length
        float SplineLength = SplineComponent->GetSplineLength();
        if (SplineLength <= 0.0f)
        {
            return false;
        }

        return true;
    }

    // =============================================================================
    // SPLINE DEFORMATION
    // =============================================================================

    void ApplySplineDeformation(USplineComponent* SplineComponent, EAuracronPCGSplineDeformationMode DeformationMode, float Strength, float Frequency)
    {
        if (!SplineComponent)
        {
            return;
        }

        int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
        
        for (int32 i = 0; i < NumPoints; i++)
        {
            FVector CurrentLocation = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            FVector DeformedLocation = CurrentLocation;

            float T = static_cast<float>(i) / FMath::Max(1, NumPoints - 1);

            switch (DeformationMode)
            {
                case EAuracronPCGSplineDeformationMode::Wave:
                {
                    float WaveOffset = FMath::Sin(T * Frequency * 2.0f * PI) * Strength;
                    DeformedLocation.Z += WaveOffset;
                    break;
                }
                case EAuracronPCGSplineDeformationMode::Noise:
                {
                    float NoiseValue = FMath::PerlinNoise1D(T * Frequency) * Strength;
                    DeformedLocation += FVector(0.0f, NoiseValue, NoiseValue * 0.5f);
                    break;
                }
                case EAuracronPCGSplineDeformationMode::Spiral:
                {
                    float Angle = T * Frequency * 2.0f * PI;
                    float Radius = Strength;
                    DeformedLocation.Y += FMath::Cos(Angle) * Radius;
                    DeformedLocation.Z += FMath::Sin(Angle) * Radius;
                    break;
                }
                case EAuracronPCGSplineDeformationMode::Bend:
                {
                    float BendAngle = T * Frequency * PI;
                    float BendRadius = 1000.0f / FMath::Max(0.001f, Strength);
                    
                    FVector BendCenter = FVector(BendRadius, 0.0f, 0.0f);
                    FVector RelativePos = CurrentLocation - BendCenter;
                    
                    float CosAngle = FMath::Cos(BendAngle);
                    float SinAngle = FMath::Sin(BendAngle);
                    
                    DeformedLocation.X = BendCenter.X + RelativePos.X * CosAngle - RelativePos.Z * SinAngle;
                    DeformedLocation.Z = BendCenter.Z + RelativePos.X * SinAngle + RelativePos.Z * CosAngle;
                    break;
                }
                default:
                    break;
            }

            SplineComponent->SetLocationAtSplinePoint(i, DeformedLocation, ESplineCoordinateSpace::World);
        }

        SplineComponent->UpdateSpline();
    }

    // =============================================================================
    // ADAPTIVE POINT GENERATION
    // =============================================================================

    TArray<FVector> GenerateAdaptivePoints(USplineComponent* SplineComponent, float CurvatureThreshold, float MinSpacing, float MaxSpacing)
    {
        TArray<FVector> AdaptivePoints;
        
        if (!SplineComponent)
        {
            return AdaptivePoints;
        }

        float SplineLength = SplineComponent->GetSplineLength();
        float CurrentDistance = 0.0f;

        AdaptivePoints.Add(SplineComponent->GetLocationAtDistanceAlongSpline(0.0f, ESplineCoordinateSpace::World));

        while (CurrentDistance < SplineLength)
        {
            // Calculate curvature at current position
            float Curvature = UAuracronPCGSplineSystemUtils::CalculateSplineCurvature(SplineComponent, CurrentDistance);
            
            // Determine spacing based on curvature
            float Spacing = MaxSpacing;
            if (Curvature > CurvatureThreshold)
            {
                float CurvatureFactor = FMath::Clamp(Curvature / CurvatureThreshold, 1.0f, 10.0f);
                Spacing = FMath::Lerp(MaxSpacing, MinSpacing, (CurvatureFactor - 1.0f) / 9.0f);
            }

            CurrentDistance += Spacing;
            
            if (CurrentDistance < SplineLength)
            {
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(CurrentDistance, ESplineCoordinateSpace::World);
                AdaptivePoints.Add(Location);
            }
        }

        // Ensure we include the end point
        if (AdaptivePoints.Num() > 0)
        {
            FVector EndLocation = SplineComponent->GetLocationAtDistanceAlongSpline(SplineLength, ESplineCoordinateSpace::World);
            if (FVector::Dist(AdaptivePoints.Last(), EndLocation) > MinSpacing)
            {
                AdaptivePoints.Add(EndLocation);
            }
        }

        return AdaptivePoints;
    }

    // =============================================================================
    // PATH FINDING IMPLEMENTATIONS
    // =============================================================================

    bool PerformAStarPathFinding(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& Descriptor, TArray<FVector>& OutPath)
    {
        OutPath.Empty();

        // Real A* pathfinding implementation using UE5.6 navigation system
        return PerformRealAStarPathFinding(Start, End, Descriptor, OutPath);
    }

    bool PerformRealAStarPathFinding(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& Descriptor, TArray<FVector>& OutPath)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(PerformRealAStarPathFinding);
        
        OutPath.Empty();

        // Validate input parameters
        if (FVector::Dist(Start, End) < KINDA_SMALL_NUMBER)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("PerformRealAStarPathFinding - Start and End points are too close"));
            OutPath.Add(Start);
            return true;
        }

        // A* node structure
        struct FAStarNode
        {
            FVector Position;
            float GCost; // Distance from start
            float HCost; // Heuristic distance to end
            float FCost; // GCost + HCost
            int32 ParentIndex;
            bool bInOpenSet;
            bool bInClosedSet;
            
            FAStarNode() : Position(FVector::ZeroVector), GCost(FLT_MAX), HCost(0.0f), FCost(FLT_MAX), ParentIndex(-1), bInOpenSet(false), bInClosedSet(false) {}
            FAStarNode(const FVector& InPos) : Position(InPos), GCost(FLT_MAX), HCost(0.0f), FCost(FLT_MAX), ParentIndex(-1), bInOpenSet(false), bInClosedSet(false) {}
        };

        // Grid-based A* implementation optimized for UE5.6
        const float GridSize = FMath::Max(Descriptor.GridSize, 50.0f);
        const int32 MaxIterations = FMath::Max(Descriptor.MaxIterations, 2000);
        const float MaxSearchDistance = FMath::Max(Descriptor.MaxSearchDistance, 15000.0f);
        
        // Create search bounds
        FVector MinBounds = FVector(
            FMath::Min(Start.X, End.X) - MaxSearchDistance * 0.4f,
            FMath::Min(Start.Y, End.Y) - MaxSearchDistance * 0.4f,
            FMath::Min(Start.Z, End.Z) - MaxSearchDistance * 0.4f
        );
        FVector MaxBounds = FVector(
            FMath::Max(Start.X, End.X) + MaxSearchDistance * 0.4f,
            FMath::Max(Start.Y, End.Y) + MaxSearchDistance * 0.4f,
            FMath::Max(Start.Z, End.Z) + MaxSearchDistance * 0.4f
        );
        
        // Calculate grid dimensions
        int32 GridX = FMath::CeilToInt((MaxBounds.X - MinBounds.X) / GridSize);
        int32 GridY = FMath::CeilToInt((MaxBounds.Y - MinBounds.Y) / GridSize);
        int32 GridZ = FMath::CeilToInt((MaxBounds.Z - MinBounds.Z) / GridSize);
        
        // Limit grid size for performance
        GridX = FMath::Min(GridX, 120);
        GridY = FMath::Min(GridY, 120);
        GridZ = FMath::Min(GridZ, 25);
        
        // Create nodes
        TArray<FAStarNode> Nodes;
        TMap<FIntVector, int32> GridToNodeIndex;
        
        // Heuristic function (Manhattan distance with diagonal movement)
        auto CalculateHeuristic = [](const FVector& From, const FVector& To) -> float
        {
            FVector Delta = (To - From).GetAbs();
            float D = 1.0f; // Cost of orthogonal movement
            float D2 = FMath::Sqrt(2.0f); // Cost of diagonal movement
            
            // 3D heuristic using octile distance
            float DeltaMax = FMath::Max3(Delta.X, Delta.Y, Delta.Z);
            float DeltaMid = FMath::Max(FMath::Min(Delta.X, Delta.Y), FMath::Min(FMath::Max(Delta.X, Delta.Y), Delta.Z));
            float DeltaMin = FMath::Min3(Delta.X, Delta.Y, Delta.Z);
            
            return (DeltaMax - DeltaMid) * D + (DeltaMid - DeltaMin) * D2 + DeltaMin * FMath::Sqrt(3.0f);
        };
        
        // Generate grid nodes with intelligent pruning
        for (int32 X = 0; X < GridX; X++)
        {
            for (int32 Y = 0; Y < GridY; Y++)
            {
                for (int32 Z = 0; Z < GridZ; Z++)
                {
                    FVector NodePos = MinBounds + FVector(
                        X * GridSize + GridSize * 0.5f,
                        Y * GridSize + GridSize * 0.5f,
                        Z * GridSize + GridSize * 0.5f
                    );
                    
                    // Intelligent pruning: only include nodes within reasonable distance from direct path
                    FVector ClosestPointOnLine = FMath::ClosestPointOnSegment(NodePos, Start, End);
                    float DistanceToLine = FVector::Dist(NodePos, ClosestPointOnLine);
                    if (DistanceToLine > MaxSearchDistance * 0.25f)
                    {
                        continue;
                    }
                    
                    int32 NodeIndex = Nodes.Num();
                    FAStarNode NewNode(NodePos);
                    NewNode.HCost = CalculateHeuristic(NodePos, End);
                    Nodes.Add(NewNode);
                    GridToNodeIndex.Add(FIntVector(X, Y, Z), NodeIndex);
                }
            }
        }
        
        if (Nodes.Num() == 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("PerformRealAStarPathFinding - No valid nodes generated, using direct path"));
            OutPath.Add(Start);
            OutPath.Add(End);
            return true;
        }
        
        // Find start and end nodes
        int32 StartNodeIndex = -1;
        int32 EndNodeIndex = -1;
        float MinStartDist = FLT_MAX;
        float MinEndDist = FLT_MAX;
        
        for (int32 i = 0; i < Nodes.Num(); i++)
        {
            float StartDist = FVector::Dist(Nodes[i].Position, Start);
            float EndDist = FVector::Dist(Nodes[i].Position, End);
            
            if (StartDist < MinStartDist)
            {
                MinStartDist = StartDist;
                StartNodeIndex = i;
            }
            
            if (EndDist < MinEndDist)
            {
                MinEndDist = EndDist;
                EndNodeIndex = i;
            }
        }
        
        if (StartNodeIndex == -1 || EndNodeIndex == -1)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("PerformRealAStarPathFinding - Could not find start or end nodes"));
            return false;
        }
        
        // Initialize A*
        Nodes[StartNodeIndex].GCost = 0.0f;
        Nodes[StartNodeIndex].FCost = Nodes[StartNodeIndex].HCost;
        Nodes[StartNodeIndex].bInOpenSet = true;
        
        TArray<int32> OpenSet;
        OpenSet.Add(StartNodeIndex);
        
        int32 Iterations = 0;
        
        // Main A* loop
        while (OpenSet.Num() > 0 && Iterations < MaxIterations)
        {
            Iterations++;
            
            // Find node with lowest F cost
            int32 CurrentIndex = 0;
            for (int32 i = 1; i < OpenSet.Num(); i++)
            {
                if (Nodes[OpenSet[i]].FCost < Nodes[OpenSet[CurrentIndex]].FCost ||
                    (FMath::IsNearlyEqual(Nodes[OpenSet[i]].FCost, Nodes[OpenSet[CurrentIndex]].FCost) &&
                     Nodes[OpenSet[i]].HCost < Nodes[OpenSet[CurrentIndex]].HCost))
                {
                    CurrentIndex = i;
                }
            }
            
            int32 CurrentNodeIndex = OpenSet[CurrentIndex];
            OpenSet.RemoveAt(CurrentIndex);
            Nodes[CurrentNodeIndex].bInOpenSet = false;
            Nodes[CurrentNodeIndex].bInClosedSet = true;
            
            // Check if we reached the end
            if (CurrentNodeIndex == EndNodeIndex)
            {
                // Reconstruct path
                TArray<int32> PathIndices;
                int32 PathNode = EndNodeIndex;
                
                while (PathNode != -1)
                {
                    PathIndices.Add(PathNode);
                    PathNode = Nodes[PathNode].ParentIndex;
                }
                
                // Reverse path and convert to world positions
                OutPath.Add(Start);
                for (int32 i = PathIndices.Num() - 1; i >= 0; i--)
                {
                    OutPath.Add(Nodes[PathIndices[i]].Position);
                }
                OutPath.Add(End);
                
                AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("A* pathfinding completed in %d iterations, path length: %d"), Iterations, OutPath.Num());
                return true;
            }
            
            // Get current grid position
            FVector CurrentPos = Nodes[CurrentNodeIndex].Position;
            FIntVector CurrentGrid = FIntVector(
                FMath::FloorToInt((CurrentPos.X - MinBounds.X) / GridSize),
                FMath::FloorToInt((CurrentPos.Y - MinBounds.Y) / GridSize),
                FMath::FloorToInt((CurrentPos.Z - MinBounds.Z) / GridSize)
            );
            
            // Check neighbors (26-connectivity in 3D)
            for (int32 dx = -1; dx <= 1; dx++)
            {
                for (int32 dy = -1; dy <= 1; dy++)
                {
                    for (int32 dz = -1; dz <= 1; dz++)
                    {
                        if (dx == 0 && dy == 0 && dz == 0) continue;
                        
                        FIntVector NeighborGrid = CurrentGrid + FIntVector(dx, dy, dz);
                        
                        // Check bounds
                        if (NeighborGrid.X < 0 || NeighborGrid.X >= GridX ||
                            NeighborGrid.Y < 0 || NeighborGrid.Y >= GridY ||
                            NeighborGrid.Z < 0 || NeighborGrid.Z >= GridZ)
                        {
                            continue;
                        }
                        
                        int32* NeighborIndexPtr = GridToNodeIndex.Find(NeighborGrid);
                        if (!NeighborIndexPtr) continue;
                        
                        int32 NeighborIndex = *NeighborIndexPtr;
                        if (Nodes[NeighborIndex].bInClosedSet) continue;
                        
                        // Calculate movement cost
                        float MovementCost = FVector::Dist(CurrentPos, Nodes[NeighborIndex].Position);
                        
                        // Apply terrain cost if specified
                        if (Descriptor.bUseTerrainCost)
                        {
                            MovementCost *= Descriptor.TerrainCostMultiplier;
                        }
                        
                        float TentativeGCost = Nodes[CurrentNodeIndex].GCost + MovementCost;
                        
                        if (!Nodes[NeighborIndex].bInOpenSet)
                        {
                            Nodes[NeighborIndex].bInOpenSet = true;
                            OpenSet.Add(NeighborIndex);
                        }
                        else if (TentativeGCost >= Nodes[NeighborIndex].GCost)
                        {
                            continue;
                        }
                        
                        // Update neighbor
                        Nodes[NeighborIndex].ParentIndex = CurrentNodeIndex;
                        Nodes[NeighborIndex].GCost = TentativeGCost;
                        Nodes[NeighborIndex].FCost = Nodes[NeighborIndex].GCost + Nodes[NeighborIndex].HCost;
                    }
                }
            }
        }
        
        // No path found, return direct path as fallback
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("A* pathfinding failed after %d iterations, using direct path"), Iterations);
        OutPath.Add(Start);
        OutPath.Add(End);
        return false;
    }
            if (CurrentNodeIndex == EndNodeIndex)
            {
                break;
            }
            
            // Examine neighbors (26-connectivity in 3D)
            FVector CurrentPos = Nodes[CurrentNodeIndex].Position;
            FIntVector CurrentGrid = FIntVector(
                FMath::FloorToInt((CurrentPos.X - MinBounds.X) / GridSize),
                FMath::FloorToInt((CurrentPos.Y - MinBounds.Y) / GridSize),
                FMath::FloorToInt((CurrentPos.Z - MinBounds.Z) / GridSize)
            );
            
            for (int32 dx = -1; dx <= 1; dx++)
            {
                for (int32 dy = -1; dy <= 1; dy++)
                {
                    for (int32 dz = -1; dz <= 1; dz++)
                    {
                        if (dx == 0 && dy == 0 && dz == 0) continue;
                        
                        FIntVector NeighborGrid = CurrentGrid + FIntVector(dx, dy, dz);
                        int32* NeighborIndexPtr = GridToNodeIndex.Find(NeighborGrid);
                        
                        if (!NeighborIndexPtr) continue;
                        
                        int32 NeighborIndex = *NeighborIndexPtr;
                        if (Nodes[NeighborIndex].bInClosedSet) continue;
                        
                        // Calculate movement cost
                        float MovementCost = FVector::Dist(CurrentPos, Nodes[NeighborIndex].Position);
                        
                        // Apply terrain cost modifiers
                        if (Descriptor.TerrainCostMultiplier > 0.0f)
                        {
                            float HeightDiff = FMath::Abs(Nodes[NeighborIndex].Position.Z - CurrentPos.Z);
                            MovementCost *= (1.0f + HeightDiff * Descriptor.TerrainCostMultiplier * 0.01f);
                        }
                        
                        float TentativeGCost = Nodes[CurrentNodeIndex].GCost + MovementCost;
                        
                        if (!Nodes[NeighborIndex].bInOpenSet)
                        {
                            // Add to open set
                            Nodes[NeighborIndex].bInOpenSet = true;
                            OpenSet.Add(NeighborIndex);
                        }
                        else if (TentativeGCost >= Nodes[NeighborIndex].GCost)
                        {
                            continue; // Not a better path
                        }
                        
                        // Update node
                        Nodes[NeighborIndex].ParentIndex = CurrentNodeIndex;
                        Nodes[NeighborIndex].GCost = TentativeGCost;
                        Nodes[NeighborIndex].FCost = Nodes[NeighborIndex].GCost + Nodes[NeighborIndex].HCost;
                    }
                }
            }
        }
        
        // Reconstruct path
        if (Nodes[EndNodeIndex].ParentIndex == -1 && EndNodeIndex != StartNodeIndex)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("PerformRealAStarPathFinding - No path found, using direct path"));
            OutPath.Add(Start);
            OutPath.Add(End);
            return true;
        }
        
        // Build path from end to start
        TArray<FVector> ReversePath;
        int32 CurrentIndex = EndNodeIndex;
        while (CurrentIndex != -1)
        {
            ReversePath.Add(Nodes[CurrentIndex].Position);
            CurrentIndex = Nodes[CurrentIndex].ParentIndex;
        }
        
        // Reverse to get start-to-end path
        OutPath.Add(Start); // Always start with exact start position
        for (int32 i = ReversePath.Num() - 2; i >= 1; i--) // Skip first (start) and last (end) nodes
        {
            OutPath.Add(ReversePath[i]);
        }
        OutPath.Add(End); // Always end with exact end position
        
        AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("PerformRealAStarPathFinding - Found path with %d points in %d iterations"), 
               OutPath.Num(), Iterations);
        
        return OutPath.Num() >= 2;
    }

    bool PerformDijkstraPathFinding(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& Descriptor, TArray<FVector>& OutPath)
    {
        OutPath.Empty();

        // Validate input parameters
        if (FVector::Dist(Start, End) < KINDA_SMALL_NUMBER)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("PerformDijkstraPathFinding - Start and End points are too close"));
            OutPath.Add(Start);
            return true;
        }

        // Grid-based Dijkstra implementation for PCG spline pathfinding
        const float GridSize = FMath::Max(Descriptor.GridSize, 50.0f); // Minimum 50 units
        const int32 MaxIterations = FMath::Max(Descriptor.MaxIterations, 1000);
        const float MaxSearchDistance = FMath::Max(Descriptor.MaxSearchDistance, 10000.0f);
        
        // Create a grid around the search area
        FVector MinBounds = FVector(
            FMath::Min(Start.X, End.X) - MaxSearchDistance * 0.5f,
            FMath::Min(Start.Y, End.Y) - MaxSearchDistance * 0.5f,
            FMath::Min(Start.Z, End.Z) - MaxSearchDistance * 0.5f
        );
        FVector MaxBounds = FVector(
            FMath::Max(Start.X, End.X) + MaxSearchDistance * 0.5f,
            FMath::Max(Start.Y, End.Y) + MaxSearchDistance * 0.5f,
            FMath::Max(Start.Z, End.Z) + MaxSearchDistance * 0.5f
        );
        
        // Calculate grid dimensions
        int32 GridX = FMath::CeilToInt((MaxBounds.X - MinBounds.X) / GridSize);
        int32 GridY = FMath::CeilToInt((MaxBounds.Y - MinBounds.Y) / GridSize);
        int32 GridZ = FMath::CeilToInt((MaxBounds.Z - MinBounds.Z) / GridSize);
        
        // Limit grid size for performance
        GridX = FMath::Min(GridX, 100);
        GridY = FMath::Min(GridY, 100);
        GridZ = FMath::Min(GridZ, 20);
        
        // Node structure for Dijkstra
        struct FDijkstraNode
        {
            FVector Position;
            float Distance;
            int32 ParentIndex;
            bool bVisited;
            
            FDijkstraNode() : Position(FVector::ZeroVector), Distance(FLT_MAX), ParentIndex(-1), bVisited(false) {}
            FDijkstraNode(const FVector& InPos) : Position(InPos), Distance(FLT_MAX), ParentIndex(-1), bVisited(false) {}
        };
        
        // Create grid nodes
        TArray<FDijkstraNode> Nodes;
        TMap<FIntVector, int32> GridToNodeIndex;
        
        // Generate grid nodes
        for (int32 X = 0; X < GridX; X++)
        {
            for (int32 Y = 0; Y < GridY; Y++)
            {
                for (int32 Z = 0; Z < GridZ; Z++)
                {
                    FVector NodePos = MinBounds + FVector(
                        X * GridSize + GridSize * 0.5f,
                        Y * GridSize + GridSize * 0.5f,
                        Z * GridSize + GridSize * 0.5f
                    );
                    
                    // Skip nodes that are too far from the direct path
                    FVector ClosestPointOnLine = FMath::ClosestPointOnSegment(NodePos, Start, End);
                    float DistanceToLine = FVector::Dist(NodePos, ClosestPointOnLine);
                    if (DistanceToLine > MaxSearchDistance * 0.3f)
                    {
                        continue;
                    }
                    
                    int32 NodeIndex = Nodes.Num();
                    Nodes.Add(FDijkstraNode(NodePos));
                    GridToNodeIndex.Add(FIntVector(X, Y, Z), NodeIndex);
                }
            }
        }
        
        if (Nodes.Num() == 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("PerformDijkstraPathFinding - No valid nodes generated, using direct path"));
            OutPath.Add(Start);
            OutPath.Add(End);
            return true;
        }
        
        // Find start and end nodes
        int32 StartNodeIndex = -1;
        int32 EndNodeIndex = -1;
        float MinStartDist = FLT_MAX;
        float MinEndDist = FLT_MAX;
        
        for (int32 i = 0; i < Nodes.Num(); i++)
        {
            float StartDist = FVector::Dist(Nodes[i].Position, Start);
            float EndDist = FVector::Dist(Nodes[i].Position, End);
            
            if (StartDist < MinStartDist)
            {
                MinStartDist = StartDist;
                StartNodeIndex = i;
            }
            
            if (EndDist < MinEndDist)
            {
                MinEndDist = EndDist;
                EndNodeIndex = i;
            }
        }
        
        if (StartNodeIndex == -1 || EndNodeIndex == -1)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("PerformDijkstraPathFinding - Could not find start or end nodes"));
            return false;
        }
        
        // Initialize Dijkstra
        Nodes[StartNodeIndex].Distance = 0.0f;
        TArray<int32> UnvisitedNodes;
        for (int32 i = 0; i < Nodes.Num(); i++)
        {
            UnvisitedNodes.Add(i);
        }
        
        // Dijkstra main loop
        int32 Iterations = 0;
        while (UnvisitedNodes.Num() > 0 && Iterations < MaxIterations)
        {
            Iterations++;
            
            // Find node with minimum distance
            int32 CurrentNodeIndex = -1;
            float MinDistance = FLT_MAX;
            int32 UnvisitedIndex = -1;
            
            for (int32 i = 0; i < UnvisitedNodes.Num(); i++)
            {
                int32 NodeIndex = UnvisitedNodes[i];
                if (Nodes[NodeIndex].Distance < MinDistance)
                {
                    MinDistance = Nodes[NodeIndex].Distance;
                    CurrentNodeIndex = NodeIndex;
                    UnvisitedIndex = i;
                }
            }
            
            if (CurrentNodeIndex == -1 || MinDistance == FLT_MAX)
            {
                break; // No more reachable nodes
            }
            
            // Remove from unvisited
            UnvisitedNodes.RemoveAt(UnvisitedIndex);
            Nodes[CurrentNodeIndex].bVisited = true;
            
            // Early exit if we reached the end
            if (CurrentNodeIndex == EndNodeIndex)
            {
                break;
            }
            
            // Update neighbors
            FVector CurrentPos = Nodes[CurrentNodeIndex].Position;
            for (int32 i = 0; i < Nodes.Num(); i++)
            {
                if (i == CurrentNodeIndex || Nodes[i].bVisited)
                {
                    continue;
                }
                
                float Distance = FVector::Dist(CurrentPos, Nodes[i].Position);
                if (Distance <= GridSize * 1.5f) // Only consider nearby nodes
                {
                    // Apply cost modifiers based on descriptor
                    float MovementCost = Distance;
                    
                    // Add terrain cost if specified
                    if (Descriptor.TerrainCostMultiplier > 0.0f)
                    {
                        float HeightDiff = FMath::Abs(Nodes[i].Position.Z - CurrentPos.Z);
                        MovementCost *= (1.0f + HeightDiff * Descriptor.TerrainCostMultiplier * 0.01f);
                    }
                    
                    float NewDistance = Nodes[CurrentNodeIndex].Distance + MovementCost;
                    if (NewDistance < Nodes[i].Distance)
                    {
                        Nodes[i].Distance = NewDistance;
                        Nodes[i].ParentIndex = CurrentNodeIndex;
                    }
                }
            }
        }
        
        // Reconstruct path
        if (Nodes[EndNodeIndex].ParentIndex == -1 && EndNodeIndex != StartNodeIndex)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("PerformDijkstraPathFinding - No path found, using direct path"));
            OutPath.Add(Start);
            OutPath.Add(End);
            return true;
        }
        
        // Build path from end to start
        TArray<FVector> ReversePath;
        int32 CurrentIndex = EndNodeIndex;
        while (CurrentIndex != -1)
        {
            ReversePath.Add(Nodes[CurrentIndex].Position);
            CurrentIndex = Nodes[CurrentIndex].ParentIndex;
        }
        
        // Reverse to get start-to-end path
        OutPath.Add(Start); // Always start with the exact start position
        for (int32 i = ReversePath.Num() - 2; i >= 1; i--) // Skip first (start) and last (end) nodes
        {
            OutPath.Add(ReversePath[i]);
        }
        OutPath.Add(End); // Always end with the exact end position
        
        AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("PerformDijkstraPathFinding - Found path with %d points in %d iterations"), 
               OutPath.Num(), Iterations);
        
        return OutPath.Num() >= 2;
    }

    // =============================================================================
    // SPLINE OPTIMIZATION
    // =============================================================================

    void OptimizeSplineTangents(USplineComponent* SplineComponent, float SmoothingFactor)
    {
        if (!SplineComponent)
        {
            return;
        }

        int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
        
        if (NumPoints < 3)
        {
            return;
        }

        // Optimize tangents for smoother curves
        for (int32 i = 1; i < NumPoints - 1; i++)
        {
            FVector PrevPoint = SplineComponent->GetLocationAtSplinePoint(i - 1, ESplineCoordinateSpace::Local);
            FVector CurrentPoint = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local);
            FVector NextPoint = SplineComponent->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::Local);

            // Calculate smooth tangent
            FVector SmoothTangent = (NextPoint - PrevPoint) * 0.5f * SmoothingFactor;

            SplineComponent->SetTangentsAtSplinePoint(i, SmoothTangent, SmoothTangent, ESplineCoordinateSpace::Local);
        }

        SplineComponent->UpdateSpline();
    }

    // =============================================================================
    // COLLISION GENERATION
    // =============================================================================

    bool GenerateSplineCollision(USplineComponent* SplineComponent, float CollisionWidth, bool bComplexCollision)
    {
        if (!SplineComponent)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("GenerateSplineCollision - Invalid spline component"));
            return false;
        }

        if (CollisionWidth <= 0.0f)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("GenerateSplineCollision - Invalid collision width: %.2f"), CollisionWidth);
            return false;
        }

        // Get the owner actor to attach collision components
        AActor* OwnerActor = SplineComponent->GetOwner();
        if (!OwnerActor)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("GenerateSplineCollision - Spline component has no owner actor"));
            return false;
        }

        // Clear existing collision components with our naming convention
        TArray<UActorComponent*> ExistingCollisionComponents = OwnerActor->GetRootComponent()->GetAttachChildren().FilterByPredicate(
            [](USceneComponent* Component)
            {
                return Component && Component->GetName().StartsWith(TEXT("SplineCollision_"));
            }
        );
        
        for (UActorComponent* Component : ExistingCollisionComponents)
        {
            if (USceneComponent* SceneComp = Cast<USceneComponent>(Component))
            {
                SceneComp->DestroyComponent();
            }
        }

        // Generate collision geometry along spline
        float SplineLength = SplineComponent->GetSplineLength();
        if (SplineLength <= 0.0f)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("GenerateSplineCollision - Spline has no length"));
            return false;
        }

        // Calculate optimal segment size based on spline curvature and collision complexity
        float BaseSegmentSize = bComplexCollision ? 50.0f : 100.0f;
        int32 NumSegments = FMath::Max(1, FMath::CeilToInt(SplineLength / BaseSegmentSize));
        
        // Limit segments for performance
        NumSegments = FMath::Min(NumSegments, bComplexCollision ? 200 : 100);
        
        float ActualSegmentLength = SplineLength / NumSegments;
        
        AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("GenerateSplineCollision - Creating %d collision segments (Length: %.2f, Width: %.2f, Complex: %s)"), 
               NumSegments, SplineLength, CollisionWidth, bComplexCollision ? TEXT("true") : TEXT("false"));

        int32 SuccessfulSegments = 0;
        
        for (int32 i = 0; i < NumSegments; i++)
        {
            try
            {
                // Calculate segment parameters
                float StartDistance = (static_cast<float>(i) / NumSegments) * SplineLength;
                float EndDistance = (static_cast<float>(i + 1) / NumSegments) * SplineLength;
                float MidDistance = (StartDistance + EndDistance) * 0.5f;
                
                // Get spline data at segment midpoint
                FVector SegmentLocation = SplineComponent->GetLocationAtDistanceAlongSpline(MidDistance, ESplineCoordinateSpace::World);
                FVector SegmentDirection = SplineComponent->GetDirectionAtDistanceAlongSpline(MidDistance, ESplineCoordinateSpace::World);
                FVector SegmentUpVector = SplineComponent->GetUpVectorAtDistanceAlongSpline(MidDistance, ESplineCoordinateSpace::World);
                FVector SegmentRightVector = SplineComponent->GetRightVectorAtDistanceAlongSpline(MidDistance, ESplineCoordinateSpace::World);
                
                // Validate vectors
                if (SegmentDirection.IsNearlyZero() || SegmentUpVector.IsNearlyZero())
                {
                    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("GenerateSplineCollision - Invalid spline vectors at segment %d"), i);
                    continue;
                }
                
                // Create collision component based on complexity setting
                UPrimitiveComponent* CollisionComponent = nullptr;
                
                if (bComplexCollision)
                {
                    // Create capsule collision for complex collision
                    UCapsuleComponent* CapsuleCollision = NewObject<UCapsuleComponent>(OwnerActor, 
                        *FString::Printf(TEXT("SplineCollision_Capsule_%d"), i));
                    
                    if (CapsuleCollision)
                    {
                        // Configure capsule properties
                        float CapsuleRadius = CollisionWidth * 0.5f;
                        float CapsuleHalfHeight = FMath::Max(ActualSegmentLength * 0.5f, CapsuleRadius);
                        
                        CapsuleCollision->SetCapsuleSize(CapsuleRadius, CapsuleHalfHeight);
                        CapsuleCollision->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                        CapsuleCollision->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
                        CapsuleCollision->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);
                        
                        // Position and orient the capsule
                        FRotator SegmentRotation = FRotationMatrix::MakeFromZX(SegmentDirection, SegmentUpVector).Rotator();
                        CapsuleCollision->SetWorldLocationAndRotation(SegmentLocation, SegmentRotation);
                        
                        // Attach to spline component
                        CapsuleCollision->AttachToComponent(SplineComponent, 
                            FAttachmentTransformRules::KeepWorldTransform);
                        
                        CollisionComponent = CapsuleCollision;
                    }
                }
                else
                {
                    // Create box collision for simple collision
                    UBoxComponent* BoxCollision = NewObject<UBoxComponent>(OwnerActor, 
                        *FString::Printf(TEXT("SplineCollision_Box_%d"), i));
                    
                    if (BoxCollision)
                    {
                        // Configure box properties
                        FVector BoxExtent = FVector(
                            ActualSegmentLength * 0.5f,  // Length along spline
                            CollisionWidth * 0.5f,       // Width
                            CollisionWidth * 0.25f       // Height (quarter of width)
                        );
                        
                        BoxCollision->SetBoxExtent(BoxExtent);
                        BoxCollision->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                        BoxCollision->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
                        BoxCollision->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);
                        
                        // Position and orient the box
                        FRotator SegmentRotation = FRotationMatrix::MakeFromXZ(SegmentDirection, SegmentUpVector).Rotator();
                        BoxCollision->SetWorldLocationAndRotation(SegmentLocation, SegmentRotation);
                        
                        // Attach to spline component
                        BoxCollision->AttachToComponent(SplineComponent, 
                            FAttachmentTransformRules::KeepWorldTransform);
                        
                        CollisionComponent = BoxCollision;
                    }
                }
                
                if (CollisionComponent)
                {
                    // Register the component with the actor
                    OwnerActor->AddInstanceComponent(CollisionComponent);
                    CollisionComponent->RegisterComponent();
                    
                    // Set additional properties
                    CollisionComponent->SetNotifyRigidBodyCollision(true);
                    CollisionComponent->SetGenerateOverlapEvents(true);
                    
                    // Add custom tags for identification
                    CollisionComponent->ComponentTags.Add(TEXT("SplineCollision"));
                    CollisionComponent->ComponentTags.Add(*FString::Printf(TEXT("Segment_%d"), i));
                    
                    SuccessfulSegments++;
                }
                else
                {
                    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("GenerateSplineCollision - Failed to create collision component for segment %d"), i);
                }
            }
            catch (const std::exception& e)
            {
                AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("GenerateSplineCollision - Exception creating segment %d: %s"), 
                       i, *FString(e.what()));
            }
            catch (...)
            {
                AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("GenerateSplineCollision - Unknown exception creating segment %d"), i);
            }
        }
        
        // Update actor's collision
        if (SuccessfulSegments > 0)
        {
            OwnerActor->RerunConstructionScripts();
            
            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("GenerateSplineCollision - Successfully created %d/%d collision segments"), 
                   SuccessfulSegments, NumSegments);
        }
        else
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("GenerateSplineCollision - Failed to create any collision segments"));
        }

        return SuccessfulSegments > 0;
    }

    // =============================================================================
    // LOD MANAGEMENT
    // =============================================================================

    void UpdateSplineMeshLODs(USplineMeshComponent* SplineMeshComponent, const TArray<float>& LODDistances)
    {
        if (!SplineMeshComponent)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("UpdateSplineMeshLODs - Invalid spline mesh component"));
            return;
        }

        if (LODDistances.Num() == 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("UpdateSplineMeshLODs - No LOD distances provided"));
            return;
        }

        // Get the static mesh from the spline mesh component
        UStaticMesh* StaticMesh = SplineMeshComponent->GetStaticMesh();
        if (!StaticMesh)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("UpdateSplineMeshLODs - Spline mesh component has no static mesh assigned"));
            return;
        }

        // Validate LOD distances are in ascending order
        TArray<float> ValidatedLODDistances = LODDistances;
        ValidatedLODDistances.Sort();
        
        // Remove any negative or zero distances
        ValidatedLODDistances.RemoveAll([](float Distance) { return Distance <= 0.0f; });
        
        if (ValidatedLODDistances.Num() == 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("UpdateSplineMeshLODs - No valid LOD distances after validation"));
            return;
        }

        // Ensure we don't exceed the number of available LODs in the static mesh
        int32 NumStaticMeshLODs = StaticMesh->GetNumLODs();
        int32 NumLODsToUse = FMath::Min(ValidatedLODDistances.Num(), NumStaticMeshLODs - 1); // -1 because LOD 0 is always used
        
        if (NumLODsToUse <= 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("UpdateSplineMeshLODs - Static mesh '%s' has only %d LOD(s), no LOD distances to set"), 
                   *StaticMesh->GetName(), NumStaticMeshLODs);
            return;
        }

        AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("UpdateSplineMeshLODs - Configuring %d LOD distances for mesh '%s' (has %d LODs)"), 
               NumLODsToUse, *StaticMesh->GetName(), NumStaticMeshLODs);

        try
        {
            // Configure LOD distances using UE5.6 spline mesh component API
            for (int32 LODIndex = 0; LODIndex < NumLODsToUse; LODIndex++)
            {
                float LODDistance = ValidatedLODDistances[LODIndex];
                
                // Validate distance is reasonable (not too small or too large)
                LODDistance = FMath::Clamp(LODDistance, 100.0f, 50000.0f);
                
                // Set the LOD distance for this level
                // Note: UE5.6 spline mesh components use the static mesh's LOD settings
                // We need to modify the static mesh's LOD settings if we want custom distances
                if (StaticMesh->GetRenderData() && StaticMesh->GetRenderData()->LODResources.IsValidIndex(LODIndex + 1))
                {
                    // Access the LOD settings through the static mesh
                    FStaticMeshSourceModel& SourceModel = StaticMesh->GetSourceModel(LODIndex + 1);
                    
                    // Configure screen size based on distance
                    // Screen size calculation: smaller screen size = further distance
                    float ScreenSize = FMath::Clamp(10000.0f / LODDistance, 0.01f, 1.0f);
                    
                    // Set the screen size for this LOD
                    StaticMesh->GetSourceModel(LODIndex + 1).ScreenSize = ScreenSize;
                    
                    AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("UpdateSplineMeshLODs - Set LOD %d: Distance=%.1f, ScreenSize=%.4f"), 
                           LODIndex + 1, LODDistance, ScreenSize);
                }
            }
            
            // Force the spline mesh component to update its LOD settings
            SplineMeshComponent->MarkRenderStateDirty();
            
            // Update the static mesh's LOD settings
            StaticMesh->PostEditChange();
            
            // Configure additional spline mesh specific LOD settings
            // Set the LOD distance scale factor
            float LODDistanceScale = 1.0f;
            if (ValidatedLODDistances.Num() > 0)
            {
                // Calculate a reasonable scale based on the first LOD distance
                LODDistanceScale = FMath::Clamp(ValidatedLODDistances[0] / 1000.0f, 0.1f, 10.0f);
            }
            
            // Apply LOD bias if available (UE5.6 feature)
            SplineMeshComponent->SetForcedLodModel(0); // Reset any forced LOD
            
            // Configure visibility distance if the component supports it
            float MaxVisibilityDistance = ValidatedLODDistances.Num() > 0 ? 
                ValidatedLODDistances.Last() * 1.5f : 10000.0f;
            
            SplineMeshComponent->SetVisibility(true);
            
            // Set component-level LOD settings
            if (SplineMeshComponent->GetOwner())
            {
                // Configure the actor's LOD settings if needed
                AActor* Owner = SplineMeshComponent->GetOwner();
                
                // Set the actor's max draw distance
                SplineMeshComponent->SetCachedMaxDrawDistance(MaxVisibilityDistance);
            }
            
            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("UpdateSplineMeshLODs - Successfully configured %d LOD levels for spline mesh component"), 
                   NumLODsToUse);
        }
        catch (const std::exception& e)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("UpdateSplineMeshLODs - Exception during LOD configuration: %s"), 
                   *FString(e.what()));
        }
        catch (...)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("UpdateSplineMeshLODs - Unknown exception during LOD configuration"));
        }
    }

    // =============================================================================
    // HELPER FUNCTIONS
    // =============================================================================

    void ApplyObstacleAvoidance(TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& Descriptor)
    {
        // Real obstacle avoidance using UE5.6 collision system
        ApplyRealObstacleAvoidance(Path, Descriptor);
    }

    void ApplyRealObstacleAvoidance(TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& Descriptor)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(ApplyRealObstacleAvoidance);

        if (Path.Num() < 3 || !Descriptor.bAvoidObstacles)
        {
            return;
        }

        // Real obstacle detection using UE5.6 collision system
        UWorld* World = GWorld;
        if (!World)
        {
            return;
        }

        // Configure collision parameters
        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = true;
        QueryParams.bReturnPhysicalMaterial = false;
        QueryParams.AddIgnoredActor(nullptr);

        // Process each path point (skip start and end)
        for (int32 i = 1; i < Path.Num() - 1; i++)
        {
            FVector CurrentPoint = Path[i];
            FVector PreviousPoint = Path[i - 1];
            FVector NextPoint = Path[i + 1];

            // Check for obstacles in multiple directions around the current point
            TArray<FVector> TestDirections = {
                FVector(1, 0, 0),   // Forward
                FVector(-1, 0, 0),  // Backward
                FVector(0, 1, 0),   // Right
                FVector(0, -1, 0),  // Left
                FVector(1, 1, 0).GetSafeNormal(),   // Forward-Right
                FVector(-1, 1, 0).GetSafeNormal(),  // Backward-Right
                FVector(1, -1, 0).GetSafeNormal(),  // Forward-Left
                FVector(-1, -1, 0).GetSafeNormal()  // Backward-Left
            };

            FVector BestAvoidanceDirection = FVector::ZeroVector;
            float MinObstacleDistance = FLT_MAX;
            bool bObstacleDetected = false;

            // Test each direction for obstacles
            for (const FVector& Direction : TestDirections)
            {
                FVector TestStart = CurrentPoint;
                FVector TestEnd = CurrentPoint + (Direction * Descriptor.ObstacleAvoidanceRadius);

                FHitResult HitResult;
                if (World->LineTraceSingleByChannel(HitResult, TestStart, TestEnd, ECC_WorldStatic, QueryParams))
                {
                    bObstacleDetected = true;
                    float ObstacleDistance = FVector::Dist(TestStart, HitResult.Location);

                    if (ObstacleDistance < MinObstacleDistance)
                    {
                        MinObstacleDistance = ObstacleDistance;
                        // Calculate avoidance direction perpendicular to obstacle normal
                        FVector ObstacleNormal = HitResult.Normal;
                        BestAvoidanceDirection = FVector::CrossProduct(ObstacleNormal, FVector::UpVector).GetSafeNormal();
                    }
                }
            }

            // Apply avoidance if obstacle detected
            if (bObstacleDetected && MinObstacleDistance < Descriptor.ObstacleAvoidanceRadius)
            {
                // Calculate avoidance strength based on distance to obstacle
                float AvoidanceStrength = 1.0f - (MinObstacleDistance / Descriptor.ObstacleAvoidanceRadius);
                AvoidanceStrength = FMath::Clamp(AvoidanceStrength, 0.0f, 1.0f);

                // Apply avoidance offset
                FVector AvoidanceOffset = BestAvoidanceDirection * Descriptor.ObstacleAvoidanceRadius * AvoidanceStrength;
                FVector NewPoint = CurrentPoint + AvoidanceOffset;

                // Validate the new point doesn't create sharp turns
                FVector ToPrevious = (PreviousPoint - NewPoint).GetSafeNormal();
                FVector ToNext = (NextPoint - NewPoint).GetSafeNormal();
                float DotProduct = FVector::DotProduct(ToPrevious, ToNext);

                // Only apply avoidance if it doesn't create too sharp a turn
                if (DotProduct > -0.5f) // Allow up to 120 degree turns
                {
                    Path[i] = NewPoint;
                }
                else
                {
                    // Apply smaller avoidance to maintain path smoothness
                    Path[i] = CurrentPoint + (AvoidanceOffset * 0.3f);
                }
            }
        }

        // Smooth the path after obstacle avoidance
        SmoothPathAfterAvoidance(Path, Descriptor.PathSmoothingIterations);
    }

    void SmoothPathAfterAvoidance(TArray<FVector>& Path, int32 SmoothingIterations)
    {
        if (Path.Num() < 3 || SmoothingIterations <= 0)
        {
            return;
        }

        // Apply Laplacian smoothing
        for (int32 Iteration = 0; Iteration < SmoothingIterations; Iteration++)
        {
            TArray<FVector> SmoothedPath = Path;

            for (int32 i = 1; i < Path.Num() - 1; i++)
            {
                FVector PreviousPoint = Path[i - 1];
                FVector CurrentPoint = Path[i];
                FVector NextPoint = Path[i + 1];

                // Calculate smoothed position
                FVector SmoothedPosition = (PreviousPoint + CurrentPoint + NextPoint) / 3.0f;

                // Blend with original position to maintain path integrity
                SmoothedPath[i] = FMath::Lerp(CurrentPoint, SmoothedPosition, 0.5f);
            }

            Path = SmoothedPath;
        }
    }
}
