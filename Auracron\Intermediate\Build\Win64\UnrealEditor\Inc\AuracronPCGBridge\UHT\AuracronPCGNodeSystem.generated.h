// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGNodeSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGNodeSystem_generated_h
#error "AuracronPCGNodeSystem.generated.h already included, missing '#pragma once' in AuracronPCGNodeSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGNodeSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronPCGNodeRegistry;
class UAuracronPCGNodeSettings;
enum class EAuracronPCGNodeCategory : uint8;
enum class EAuracronPCGNodeCategory : uint8; 
struct FAuracronPCGNodeMetadata;

// ********** Begin ScriptStruct FAuracronPCGNodeMetadata ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_58_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGNodeMetadata;
// ********** End ScriptStruct FAuracronPCGNodeMetadata ********************************************

// ********** Begin Class UAuracronPCGNodeSettings *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_111_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetNodeCategory); \
	DECLARE_FUNCTION(execGetNodeTooltip); \
	DECLARE_FUNCTION(execGetNodeDisplayName); \
	DECLARE_FUNCTION(execIsNodeConfigurationValid); \
	DECLARE_FUNCTION(execValidateNodeSettings);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_111_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGNodeSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGNodeSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGNodeSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGNodeSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_111_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGNodeSettings(UAuracronPCGNodeSettings&&) = delete; \
	UAuracronPCGNodeSettings(const UAuracronPCGNodeSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGNodeSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGNodeSettings); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGNodeSettings) \
	NO_API virtual ~UAuracronPCGNodeSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_108_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_111_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_111_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_111_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_111_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGNodeSettings;

// ********** End Class UAuracronPCGNodeSettings ***************************************************

// ********** Begin Class UAuracronPCGNodeRegistry *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_237_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetGlobalRegistry); \
	DECLARE_FUNCTION(execValidateNodeClass); \
	DECLARE_FUNCTION(execGetRegisteredNodeCount); \
	DECLARE_FUNCTION(execClearRegistry); \
	DECLARE_FUNCTION(execRefreshRegistry); \
	DECLARE_FUNCTION(execCreateNodeInstance); \
	DECLARE_FUNCTION(execGetNodeCountByCategory); \
	DECLARE_FUNCTION(execGetNodeMetadata); \
	DECLARE_FUNCTION(execGetNodesByTag); \
	DECLARE_FUNCTION(execSearchNodesByName); \
	DECLARE_FUNCTION(execGetNodesByCategory); \
	DECLARE_FUNCTION(execGetAllRegisteredNodes); \
	DECLARE_FUNCTION(execIsNodeRegistered); \
	DECLARE_FUNCTION(execUnregisterNode); \
	DECLARE_FUNCTION(execRegisterNode);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeRegistry_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_237_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGNodeRegistry(); \
	friend struct Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeRegistry_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGNodeRegistry, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGNodeRegistry_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGNodeRegistry)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_237_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGNodeRegistry(UAuracronPCGNodeRegistry&&) = delete; \
	UAuracronPCGNodeRegistry(const UAuracronPCGNodeRegistry&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGNodeRegistry); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGNodeRegistry); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGNodeRegistry) \
	NO_API virtual ~UAuracronPCGNodeRegistry();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_234_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_237_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_237_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_237_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h_237_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGNodeRegistry;

// ********** End Class UAuracronPCGNodeRegistry ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h

// ********** Begin Enum EAuracronPCGNodeCategory **************************************************
#define FOREACH_ENUM_EAURACRONPCGNODECATEGORY(op) \
	op(EAuracronPCGNodeCategory::Input) \
	op(EAuracronPCGNodeCategory::Output) \
	op(EAuracronPCGNodeCategory::Generator) \
	op(EAuracronPCGNodeCategory::Filter) \
	op(EAuracronPCGNodeCategory::Transform) \
	op(EAuracronPCGNodeCategory::Sampler) \
	op(EAuracronPCGNodeCategory::Utility) \
	op(EAuracronPCGNodeCategory::Custom) \
	op(EAuracronPCGNodeCategory::Debug) \
	op(EAuracronPCGNodeCategory::Advanced) 

enum class EAuracronPCGNodeCategory : uint8;
template<> struct TIsUEnumClass<EAuracronPCGNodeCategory> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNodeCategory>();
// ********** End Enum EAuracronPCGNodeCategory ****************************************************

// ********** Begin Enum EAuracronPCGNodePriority **************************************************
#define FOREACH_ENUM_EAURACRONPCGNODEPRIORITY(op) \
	op(EAuracronPCGNodePriority::Lowest) \
	op(EAuracronPCGNodePriority::Low) \
	op(EAuracronPCGNodePriority::Normal) \
	op(EAuracronPCGNodePriority::High) \
	op(EAuracronPCGNodePriority::Highest) \
	op(EAuracronPCGNodePriority::Critical) 

enum class EAuracronPCGNodePriority : uint8;
template<> struct TIsUEnumClass<EAuracronPCGNodePriority> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNodePriority>();
// ********** End Enum EAuracronPCGNodePriority ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
