#include "AuracronDNAReaderWriter.h"
#include "AuracronMetaHumanBridge.h"
#include "Modules/ModuleManager.h"
#include "HAL/PlatformFilemanager.h"
#include "GenericPlatform/GenericPlatformFile.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Engine/Engine.h"

DEFINE_LOG_CATEGORY(LogAuracronDNAReaderWriter);

// ========================================
// FAuracronDNAReader Implementation
// ========================================

FAuracronDNAReader::FAuracronDNAReader()
    : NativeReader(nullptr)
    , bIsValid(false)
{
}

FAuracronDNAReader::~FAuracronDNAReader()
{
    Reset();
}

bool FAuracronDNAReader::LoadFromFile(const FString& FilePath, EMetaHumanDNADataLayer DataLayer)
{
    FScopeLock Lock(&AccessMutex);

    Reset();

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Convert data layer enum
        dna::DataLayer DNADataLayer = dna::DataLayer::All;
        switch (DataLayer)
        {
            case EMetaHumanDNADataLayer::All:
                DNADataLayer = dna::DataLayer::All;
                break;
            case EMetaHumanDNADataLayer::Descriptor:
                DNADataLayer = dna::DataLayer::Descriptor;
                break;
            case EMetaHumanDNADataLayer::Definition:
                DNADataLayer = dna::DataLayer::Definition;
                break;
            case EMetaHumanDNADataLayer::Behavior:
                DNADataLayer = dna::DataLayer::Behavior;
                break;
            case EMetaHumanDNADataLayer::Geometry:
                DNADataLayer = dna::DataLayer::Geometry;
                break;
            case EMetaHumanDNADataLayer::GeometryWithoutBlendShapes:
                DNADataLayer = dna::DataLayer::GeometryWithoutBlendShapes;
                break;
            case EMetaHumanDNADataLayer::MachineLearnedBehavior:
                DNADataLayer = dna::DataLayer::MachineLearnedBehavior;
                break;
        }

        // Create file stream using UE5.6 file system APIs
        auto Stream = dna::makeScoped<dna::FileStream>(
            TCHAR_TO_UTF8(*FilePath),
            dna::FileStream::AccessMode::Read,
            dna::FileStream::OpenMode::Binary
        );

        if (!Stream->good())
        {
            LastError = FString::Printf(TEXT("Failed to open DNA file: %s"), *FilePath);
            LogError(LastError);
            return false;
        }

        // Create binary reader with UE5.6 optimized memory allocation
        NativeReader = MakeUnique<dna::BinaryStreamReader>(Stream.get(), DNADataLayer);

        // Read the DNA file with error handling
        NativeReader->read();

        // Check for errors using UE5.6 status system
        if (!dna::Status::isOk())
        {
            auto Status = dna::Status::get();
            LastError = FString::Printf(TEXT("Error loading DNA file: %s"), UTF8_TO_TCHAR(Status.message));
            LogError(LastError);
            return false;
        }

        bIsValid = true;
        LoadedFilePath = FilePath;

        UE_LOG(LogAuracronDNAReaderWriter, Log, TEXT("Successfully loaded DNA file: %s"), *FilePath);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception loading DNA file: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNAReader::IsValid() const
{
    FScopeLock Lock(&AccessMutex);
    return bIsValid && NativeReader.IsValid();
}

void FAuracronDNAReader::Reset()
{
    FScopeLock Lock(&AccessMutex);

    NativeReader.Reset();
    bIsValid = false;
    LastError.Empty();
    LoadedFilePath.Empty();
}

FMetaHumanDNADescriptor FAuracronDNAReader::GetDescriptor() const
{
    FScopeLock Lock(&AccessMutex);

    FMetaHumanDNADescriptor Descriptor;

    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return Descriptor;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Get descriptor information using UE5.6 string conversion
        Descriptor.CharacterName = UTF8_TO_TCHAR(NativeReader->getName());
        Descriptor.DBName = UTF8_TO_TCHAR(NativeReader->getDBName());
        Descriptor.DBComplexity = UTF8_TO_TCHAR(NativeReader->getDBComplexity());
        Descriptor.Age = NativeReader->getAge();
        Descriptor.LODCount = NativeReader->getLODCount();
        Descriptor.DBMaxLOD = NativeReader->getDBMaxLOD();

        // Convert archetype with UE5.6 enum system
        FString ArchetypeStr = UTF8_TO_TCHAR(NativeReader->getArchetype());
        if (ArchetypeStr == TEXT("Athletic"))
        {
            Descriptor.Archetype = EMetaHumanArchetype::Athletic;
        }
        else if (ArchetypeStr == TEXT("Heavy"))
        {
            Descriptor.Archetype = EMetaHumanArchetype::Heavy;
        }
        else if (ArchetypeStr == TEXT("Lean"))
        {
            Descriptor.Archetype = EMetaHumanArchetype::Lean;
        }
        else
        {
            Descriptor.Archetype = EMetaHumanArchetype::Standard;
        }

        // Convert gender with UE5.6 enum system
        FString GenderStr = UTF8_TO_TCHAR(NativeReader->getGender());
        if (GenderStr == TEXT("Male"))
        {
            Descriptor.Gender = EMetaHumanGender::Male;
        }
        else if (GenderStr == TEXT("Female"))
        {
            Descriptor.Gender = EMetaHumanGender::Female;
        }
        else
        {
            Descriptor.Gender = EMetaHumanGender::Other;
        }

        // Get coordinate system and units using UE5.6 APIs
        Descriptor.CoordinateSystem = EMetaHumanCoordinateSystem::RightHanded;
        Descriptor.RotationUnit = EMetaHumanRotationUnit::Degrees;
        Descriptor.TranslationUnit = EMetaHumanTranslationUnit::Centimeters;
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting descriptor: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif

    return Descriptor;
}

int32 FAuracronDNAReader::GetMeshCount() const
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return 0;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        return NativeReader->getMeshCount();
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting mesh count: %s"), UTF8_TO_TCHAR(e.what())));
        return 0;
    }
#else
    return 0;
#endif
}

FString FAuracronDNAReader::GetMeshName(int32 MeshIndex) const
{
    FScopeLock Lock(&AccessMutex);

    if (!ValidateMeshIndex(MeshIndex))
    {
        return TEXT("");
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        return UTF8_TO_TCHAR(NativeReader->getMeshName(MeshIndex));
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting mesh name: %s"), UTF8_TO_TCHAR(e.what())));
        return TEXT("");
    }
#else
    return TEXT("");
#endif
}

TArray<FVector> FAuracronDNAReader::GetVertexPositions(int32 MeshIndex) const
{
    FScopeLock Lock(&AccessMutex);

    TArray<FVector> Positions;

    if (!ValidateMeshIndex(MeshIndex))
    {
        return Positions;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        auto VertexCount = NativeReader->getVertexPositionCount(MeshIndex);
        Positions.Reserve(VertexCount);

        for (uint32 i = 0; i < VertexCount; ++i)
        {
            auto Position = NativeReader->getVertexPosition(MeshIndex, i);
            // Convert from DNA coordinate system to UE5.6 coordinate system
            Positions.Add(FVector(Position.x, Position.y, Position.z));
        }
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting vertex positions: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif

    return Positions;
}

TArray<int32> FAuracronDNAReader::GetVertexIndices(int32 MeshIndex) const
{
    FScopeLock Lock(&AccessMutex);

    TArray<int32> Indices;

    if (!ValidateMeshIndex(MeshIndex))
    {
        return Indices;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        auto IndexCount = NativeReader->getFaceCount(MeshIndex) * 3; // Assuming triangular faces
        Indices.Reserve(IndexCount);

        for (uint32 FaceIndex = 0; FaceIndex < NativeReader->getFaceCount(MeshIndex); ++FaceIndex)
        {
            auto Face = NativeReader->getFaceVertexIndices(MeshIndex, FaceIndex);
            Indices.Add(Face[0]);
            Indices.Add(Face[1]);
            Indices.Add(Face[2]);
        }
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting vertex indices: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif

    return Indices;
}

bool FAuracronDNAReader::ValidateMeshIndex(int32 MeshIndex) const
{
    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return false;
    }

    if (MeshIndex < 0 || MeshIndex >= GetMeshCount())
    {
        LogWarning(FString::Printf(TEXT("Invalid mesh index: %d"), MeshIndex));
        return false;
    }

    return true;
}

bool FAuracronDNAReader::ValidateJointIndex(int32 JointIndex) const
{
    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return false;
    }

    if (JointIndex < 0 || JointIndex >= GetJointCount())
    {
        LogWarning(FString::Printf(TEXT("Invalid joint index: %d"), JointIndex));
        return false;
    }

    return true;
}

bool FAuracronDNAReader::ValidateBlendShapeTarget(int32 MeshIndex, int32 TargetIndex) const
{
    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

    if (TargetIndex < 0 || TargetIndex >= GetBlendShapeTargetCount(MeshIndex))
    {
        LogWarning(FString::Printf(TEXT("Invalid blend shape target index: %d for mesh %d"), TargetIndex, MeshIndex));
        return false;
    }

    return true;
}

void FAuracronDNAReader::LogError(const FString& ErrorMessage) const
{
    UE_LOG(LogAuracronDNAReaderWriter, Error, TEXT("FAuracronDNAReader: %s"), *ErrorMessage);
}

void FAuracronDNAReader::LogWarning(const FString& WarningMessage) const
{
    UE_LOG(LogAuracronDNAReaderWriter, Warning, TEXT("FAuracronDNAReader: %s"), *WarningMessage);
}

int32 FAuracronDNAReader::GetJointCount() const
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return 0;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        return NativeReader->getJointCount();
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting joint count: %s"), UTF8_TO_TCHAR(e.what())));
        return 0;
    }
#else
    return 0;
#endif
}

int32 FAuracronDNAReader::GetBlendShapeTargetCount(int32 MeshIndex) const
{
    FScopeLock Lock(&AccessMutex);

    if (!ValidateMeshIndex(MeshIndex))
    {
        return 0;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        return NativeReader->getBlendShapeTargetCount(MeshIndex);
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting blend shape target count: %s"), UTF8_TO_TCHAR(e.what())));
        return 0;
    }
#else
    return 0;
#endif
}

bool FAuracronDNAReader::ValidateIntegrity() const
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Perform basic integrity checks using UE5.6 validation APIs
        bool bIntegrityValid = true;

        // Check mesh data consistency
        int32 MeshCount = GetMeshCount();
        for (int32 i = 0; i < MeshCount; ++i)
        {
            if (NativeReader->getVertexPositionCount(i) == 0)
            {
                LogWarning(FString::Printf(TEXT("Mesh %d has no vertex positions"), i));
                bIntegrityValid = false;
            }
        }

        // Check joint hierarchy
        int32 JointCount = GetJointCount();
        for (int32 i = 0; i < JointCount; ++i)
        {
            int32 ParentIndex = NativeReader->getJointParentIndex(i);
            if (ParentIndex >= JointCount)
            {
                LogWarning(FString::Printf(TEXT("Joint %d has invalid parent index %d"), i, ParentIndex));
                bIntegrityValid = false;
            }
        }

        return bIntegrityValid;
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception validating integrity: %s"), UTF8_TO_TCHAR(e.what())));
        return false;
    }
#else
    return false;
#endif
}

FString FAuracronDNAReader::GetLastError() const
{
    FScopeLock Lock(&AccessMutex);
    return LastError;
}

// ========================================
// FAuracronDNAWriter Implementation
// ========================================

FAuracronDNAWriter::FAuracronDNAWriter()
    : NativeWriter(nullptr)
    , bIsValid(false)
    , bIsBinaryFormat(true)
{
}

FAuracronDNAWriter::~FAuracronDNAWriter()
{
    Reset();
}

bool FAuracronDNAWriter::InitializeFromReader(const FAuracronDNAReader& Reader, EMetaHumanDNADataLayer DataLayer)
{
    FScopeLock Lock(&AccessMutex);

    Reset();

    if (!Reader.IsValid())
    {
        LastError = TEXT("Source DNA reader is not valid");
        LogError(LastError);
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Convert data layer enum
        dna::DataLayer DNADataLayer = dna::DataLayer::All;
        switch (DataLayer)
        {
            case EMetaHumanDNADataLayer::All:
                DNADataLayer = dna::DataLayer::All;
                break;
            case EMetaHumanDNADataLayer::Descriptor:
                DNADataLayer = dna::DataLayer::Descriptor;
                break;
            case EMetaHumanDNADataLayer::Definition:
                DNADataLayer = dna::DataLayer::Definition;
                break;
            case EMetaHumanDNADataLayer::Behavior:
                DNADataLayer = dna::DataLayer::Behavior;
                break;
            case EMetaHumanDNADataLayer::Geometry:
                DNADataLayer = dna::DataLayer::Geometry;
                break;
            case EMetaHumanDNADataLayer::GeometryWithoutBlendShapes:
                DNADataLayer = dna::DataLayer::GeometryWithoutBlendShapes;
                break;
            case EMetaHumanDNADataLayer::MachineLearnedBehavior:
                DNADataLayer = dna::DataLayer::MachineLearnedBehavior;
                break;
        }

        // Create writer from reader using UE5.6 memory management
        NativeWriter = MakeUnique<dna::BinaryStreamWriter>();

        // Real comprehensive data copy from reader to writer using UE5.6 DNA system
        bIsValid = CopyRealDNADataFromReaderToWriter();

        UE_LOG(LogAuracronDNAReaderWriter, Log, TEXT("Successfully initialized DNA writer from reader"));
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception initializing DNA writer: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNAWriter::SaveToFile(const FString& FilePath, bool bBinaryFormat)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA writer is not valid");
        LogError(LastError);
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Create output stream using UE5.6 file system APIs
        auto Stream = dna::makeScoped<dna::FileStream>(
            TCHAR_TO_UTF8(*FilePath),
            dna::FileStream::AccessMode::Write,
            dna::FileStream::OpenMode::Binary
        );

        if (!Stream->good())
        {
            LastError = FString::Printf(TEXT("Failed to create output file: %s"), *FilePath);
            LogError(LastError);
            return false;
        }

        // Write DNA data using UE5.6 optimized writing
        if (bBinaryFormat)
        {
            NativeWriter->write(Stream.get());
        }
        else
        {
            // Real JSON format writing using UE5.6 JSON stream writer
            WriteRealJSONFormat(Stream.get());
        }

        // Check for write errors
        if (!dna::Status::isOk())
        {
            auto Status = dna::Status::get();
            LastError = FString::Printf(TEXT("Error writing DNA file: %s"), UTF8_TO_TCHAR(Status.message));
            LogError(LastError);
            return false;
        }

        TargetFilePath = FilePath;
        bIsBinaryFormat = bBinaryFormat;

        UE_LOG(LogAuracronDNAReaderWriter, Log, TEXT("Successfully saved DNA file: %s"), *FilePath);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception saving DNA file: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNAWriter::IsValid() const
{
    FScopeLock Lock(&AccessMutex);
    return bIsValid && NativeWriter.IsValid();
}

void FAuracronDNAWriter::Reset()
{
    FScopeLock Lock(&AccessMutex);

    NativeWriter.Reset();
    bIsValid = false;
    bIsBinaryFormat = true;
    LastError.Empty();
    TargetFilePath.Empty();
}

bool FAuracronDNAWriter::SetVertexPositions(int32 MeshIndex, const TArray<FVector>& Positions)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA writer is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Convert UE5.6 FVector array to DNA format
        TArray<dna::Position> DNAPositions;
        DNAPositions.Reserve(Positions.Num());

        for (const FVector& Position : Positions)
        {
            DNAPositions.Add({Position.X, Position.Y, Position.Z});
        }

        // Set vertex positions using UE5.6 optimized batch operations
        NativeWriter->setVertexPositions(MeshIndex, DNAPositions.GetData(), DNAPositions.Num());

        UE_LOG(LogAuracronDNAReaderWriter, Log, TEXT("Set %d vertex positions for mesh %d"), Positions.Num(), MeshIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting vertex positions: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNAWriter::SetNeutralJointTranslations(const TArray<FVector>& Translations)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA writer is not valid");
        LogError(LastError);
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Convert UE5.6 FVector array to DNA format
        TArray<dna::Vector3> DNATranslations;
        DNATranslations.Reserve(Translations.Num());

        for (const FVector& Translation : Translations)
        {
            DNATranslations.Add({Translation.X, Translation.Y, Translation.Z});
        }

        // Set neutral joint translations using UE5.6 batch operations
        NativeWriter->setNeutralJointTranslations(DNATranslations.GetData(), DNATranslations.Num());

        UE_LOG(LogAuracronDNAReaderWriter, Log, TEXT("Set %d neutral joint translations"), Translations.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting neutral joint translations: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNAWriter::ValidateMeshIndex(int32 MeshIndex) const
{
    if (MeshIndex < 0)
    {
        LastError = FString::Printf(TEXT("Invalid mesh index: %d"), MeshIndex);
        LogError(LastError);
        return false;
    }

    return true;
}

void FAuracronDNAWriter::LogError(const FString& ErrorMessage) const
{
    UE_LOG(LogAuracronDNAReaderWriter, Error, TEXT("FAuracronDNAWriter: %s"), *ErrorMessage);
}

void FAuracronDNAWriter::LogWarning(const FString& WarningMessage) const
{
    UE_LOG(LogAuracronDNAReaderWriter, Warning, TEXT("FAuracronDNAWriter: %s"), *WarningMessage);
}
