// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Material System Utilities Implementation
// Bridge 2.11: PCG Framework - Material Assignment

#include "AuracronPCGMaterialSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Engine/Texture.h"
#include "Curves/CurveFloat.h"
#include "Curves/CurveLinearColor.h"

// =============================================================================
// MATERIAL SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

UMaterialInterface* UAuracronPCGMaterialSystemUtils::SelectMaterial(const FPCGPoint& Point, const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor)
{
    if (SelectionDescriptor.MaterialOptions.Num() == 0)
    {
        return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(SelectionDescriptor.FallbackMaterial);
    }

    int32 MaterialIndex = 0;

    switch (SelectionDescriptor.SelectionMode)
    {
        case EAuracronPCGMaterialSelectionMode::ByAttribute:
            MaterialIndex = GetMaterialIndexByAttribute(Point, SelectionDescriptor.AttributeName, SelectionDescriptor.MaterialTags);
            break;
        case EAuracronPCGMaterialSelectionMode::ByDistance:
            return GetMaterialByDistance(Point.Transform.GetLocation(), SelectionDescriptor.ReferenceLocation, 
                                       SelectionDescriptor.DistanceThresholds, SelectionDescriptor.MaterialOptions);
        case EAuracronPCGMaterialSelectionMode::ByHeight:
            MaterialIndex = GetMaterialIndexByHeight(Point.Transform.GetLocation(), SelectionDescriptor.HeightThresholds);
            break;
        case EAuracronPCGMaterialSelectionMode::BySlope:
            MaterialIndex = GetMaterialIndexBySlope(Point.Transform.GetRotation().GetUpVector(), SelectionDescriptor.SlopeThresholds);
            break;
        case EAuracronPCGMaterialSelectionMode::ByRandom:
            MaterialIndex = GetRandomMaterialIndex(Point, SelectionDescriptor);
            break;
        default:
            MaterialIndex = 0;
            break;
    }

    // Clamp index to valid range
    MaterialIndex = FMath::Clamp(MaterialIndex, 0, SelectionDescriptor.MaterialOptions.Num() - 1);
    
    return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(SelectionDescriptor.MaterialOptions[MaterialIndex]);
}

TArray<UMaterialInterface*> UAuracronPCGMaterialSystemUtils::SelectMultipleMaterials(const FPCGPoint& Point, const TArray<FAuracronPCGMaterialSelectionDescriptor>& SelectionDescriptors, const TArray<float>& Weights)
{
    TArray<UMaterialInterface*> SelectedMaterials;
    
    for (int32 i = 0; i < SelectionDescriptors.Num(); i++)
    {
        UMaterialInterface* Material = SelectMaterial(Point, SelectionDescriptors[i]);
        if (Material)
        {
            SelectedMaterials.Add(Material);
        }
    }
    
    return SelectedMaterials;
}

int32 UAuracronPCGMaterialSystemUtils::GetMaterialIndexByAttribute(const FPCGPoint& Point, const FString& AttributeName, const TArray<FString>& MaterialTags)
{
    // Get attribute value from point metadata
    FString AttributeValue;
    
    // Try to get the attribute value from point metadata
    if (const UPCGMetadata* Metadata = Point.MetadataEntry != PCGInvalidEntryKey ? Point.Metadata.Get() : nullptr)
    {
        // Check if attribute exists in metadata
        if (const FPCGMetadataAttributeBase* Attribute = Metadata->GetConstAttribute(FName(*AttributeName)))
        {
            // Get attribute value based on type
            if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<FString>::Id)
            {
                Metadata->GetStringAttribute(Point.MetadataEntry, FName(*AttributeName), AttributeValue);
            }
            else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<FName>::Id)
            {
                FName NameValue;
                if (Metadata->GetNameAttribute(Point.MetadataEntry, FName(*AttributeName), NameValue))
                {
                    AttributeValue = NameValue.ToString();
                }
            }
            else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<int32>::Id)
            {
                int32 IntValue;
                if (Metadata->GetIntAttribute(Point.MetadataEntry, FName(*AttributeName), IntValue))
                {
                    AttributeValue = FString::FromInt(IntValue);
                }
            }
            else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
            {
                float FloatValue;
                if (Metadata->GetFloatAttribute(Point.MetadataEntry, FName(*AttributeName), FloatValue))
                {
                    AttributeValue = FString::SanitizeFloat(FloatValue);
                }
            }
            else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<bool>::Id)
            {
                bool BoolValue;
                if (Metadata->GetBoolAttribute(Point.MetadataEntry, FName(*AttributeName), BoolValue))
                {
                    AttributeValue = BoolValue ? TEXT("true") : TEXT("false");
                }
            }
        }
    }
    
    // If no attribute found, use default behavior
    if (AttributeValue.IsEmpty())
    {
        AttributeValue = TEXT("default");
    }
    
    for (int32 i = 0; i < MaterialTags.Num(); i++)
    {
        if (MaterialTags[i].Equals(AttributeValue, ESearchCase::IgnoreCase))
        {
            return i;
        }
    }
    
    return 0; // Default to first material
}

UMaterialInterface* UAuracronPCGMaterialSystemUtils::GetMaterialByDistance(const FVector& Position, const FVector& ReferenceLocation, const TArray<float>& DistanceThresholds, const TArray<TSoftObjectPtr<UMaterialInterface>>& Materials)
{
    if (Materials.Num() == 0)
    {
        return nullptr;
    }

    float Distance = FVector::Dist(Position, ReferenceLocation);
    
    for (int32 i = 0; i < DistanceThresholds.Num() && i < Materials.Num(); i++)
    {
        if (Distance <= DistanceThresholds[i])
        {
            return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Materials[i]);
        }
    }
    
    // Return last material if beyond all thresholds
    return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Materials.Last());
}

UMaterialInstanceDynamic* UAuracronPCGMaterialSystemUtils::BlendMaterials(const TArray<UMaterialInterface*>& SourceMaterials, const TArray<float>& BlendWeights, const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor)
{
    if (SourceMaterials.Num() == 0)
    {
        return nullptr;
    }

    if (SourceMaterials.Num() == 1)
    {
        return UMaterialInstanceDynamic::Create(SourceMaterials[0], nullptr);
    }

    // Create dynamic material instance from first material
    UMaterialInstanceDynamic* BlendedMaterial = UMaterialInstanceDynamic::Create(SourceMaterials[0], nullptr);
    if (!BlendedMaterial)
    {
        return nullptr;
    }

    // Apply blending parameters
    BlendedMaterial->SetScalarParameterValue(TEXT("BlendStrength"), BlendingDescriptor.BlendStrength);
    BlendedMaterial->SetScalarParameterValue(TEXT("TransitionWidth"), BlendingDescriptor.TransitionWidth);

    // Set blend weights
    for (int32 i = 0; i < BlendWeights.Num() && i < 4; i++) // Limit to 4 blend layers
    {
        FString WeightParamName = FString::Printf(TEXT("BlendWeight%d"), i);
        BlendedMaterial->SetScalarParameterValue(*WeightParamName, BlendWeights[i]);
    }

    // Set additional material textures if available
    for (int32 i = 1; i < SourceMaterials.Num() && i < 4; i++)
    {
        FString TextureParamName = FString::Printf(TEXT("BlendTexture%d"), i);
        // In production, you'd extract textures from source materials
        // BlendedMaterial->SetTextureParameterValue(*TextureParamName, ExtractedTexture);
    }

    return BlendedMaterial;
}

float UAuracronPCGMaterialSystemUtils::CalculateBlendWeight(const FPCGPoint& Point, const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor)
{
    float BlendWeight = BlendingDescriptor.BlendStrength;
    
    // Apply transition curve if available
    if (BlendingDescriptor.TransitionCurve.IsValid())
    {
        UCurveFloat* Curve = AuracronPCGMaterialSystemUtils::LoadCurveSafe(BlendingDescriptor.TransitionCurve);
        if (Curve)
        {
            BlendWeight *= Curve->GetFloatValue(Point.Density);
        }
    }
    
    return FMath::Clamp(BlendWeight, 0.0f, 1.0f);
}

UMaterialInstanceDynamic* UAuracronPCGMaterialSystemUtils::CreateDynamicMaterialInstance(UMaterialInterface* BaseMaterial, const TArray<FAuracronPCGMaterialParameterDescriptor>& Parameters)
{
    if (!BaseMaterial)
    {
        return nullptr;
    }

    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, nullptr);
    if (!DynamicMaterial)
    {
        return nullptr;
    }

    // Set default parameter values
    for (const FAuracronPCGMaterialParameterDescriptor& Parameter : Parameters)
    {
        switch (Parameter.ParameterType)
        {
            case EAuracronPCGMaterialParameterType::Scalar:
                AuracronPCGMaterialSystemUtils::SetScalarParameter(DynamicMaterial, Parameter.ParameterName, Parameter.ScalarValue);
                break;
            case EAuracronPCGMaterialParameterType::Vector:
                AuracronPCGMaterialSystemUtils::SetVectorParameter(DynamicMaterial, Parameter.ParameterName, Parameter.VectorValue);
                break;
            case EAuracronPCGMaterialParameterType::Color:
                AuracronPCGMaterialSystemUtils::SetColorParameter(DynamicMaterial, Parameter.ParameterName, Parameter.ColorValue);
                break;
            case EAuracronPCGMaterialParameterType::Texture:
                if (Parameter.TextureValue.IsValid())
                {
                    UTexture* Texture = AuracronPCGMaterialSystemUtils::LoadTextureSafe(Parameter.TextureValue);
                    AuracronPCGMaterialSystemUtils::SetTextureParameter(DynamicMaterial, Parameter.ParameterName, Texture);
                }
                break;
            default:
                break;
        }
    }

    return DynamicMaterial;
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateUVCoordinates(const FVector& Position, const FAuracronPCGUVGenerationDescriptor& UVDescriptor)
{
    switch (UVDescriptor.GenerationMode)
    {
        case EAuracronPCGUVGenerationMode::WorldSpace:
            return GenerateWorldSpaceUV(Position, UVDescriptor.UVScale, UVDescriptor.UVOffset);
        case EAuracronPCGUVGenerationMode::Planar:
            return GeneratePlanarUV(Position, UVDescriptor.ProjectionAxis, UVDescriptor.UVScale);
        case EAuracronPCGUVGenerationMode::Cylindrical:
            return GenerateCylindricalUV(Position, UVDescriptor.ProjectionCenter, UVDescriptor.CylinderRadius, UVDescriptor.ProjectionAxis);
        case EAuracronPCGUVGenerationMode::Spherical:
            return GenerateSphericalUV(Position, UVDescriptor.ProjectionCenter, UVDescriptor.SphereRadius);
        default:
            return GenerateWorldSpaceUV(Position, UVDescriptor.UVScale, UVDescriptor.UVOffset);
    }
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateWorldSpaceUV(const FVector& Position, const FVector2D& Scale, const FVector2D& Offset)
{
    FVector2D UV;
    UV.X = Position.X * Scale.X + Offset.X;
    UV.Y = Position.Y * Scale.Y + Offset.Y;
    return UV;
}

FVector2D UAuracronPCGMaterialSystemUtils::GeneratePlanarUV(const FVector& Position, const FVector& ProjectionAxis, const FVector2D& Scale)
{
    FVector2D UV;
    
    // Project onto plane perpendicular to projection axis
    if (FMath::Abs(ProjectionAxis.Z) > 0.9f) // Z-axis projection
    {
        UV.X = Position.X * Scale.X;
        UV.Y = Position.Y * Scale.Y;
    }
    else if (FMath::Abs(ProjectionAxis.Y) > 0.9f) // Y-axis projection
    {
        UV.X = Position.X * Scale.X;
        UV.Y = Position.Z * Scale.Y;
    }
    else // X-axis projection
    {
        UV.X = Position.Y * Scale.X;
        UV.Y = Position.Z * Scale.Y;
    }
    
    return UV;
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateCylindricalUV(const FVector& Position, const FVector& Center, float Radius, const FVector& Axis)
{
    FVector RelativePos = Position - Center;
    
    // Project onto cylinder
    FVector2D UV;
    UV.X = FMath::Atan2(RelativePos.Y, RelativePos.X) / (2.0f * PI) + 0.5f; // Angle around axis
    UV.Y = FVector::DotProduct(RelativePos, Axis.GetSafeNormal()) / Radius; // Height along axis
    
    return UV;
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateSphericalUV(const FVector& Position, const FVector& Center, float Radius)
{
    FVector RelativePos = (Position - Center).GetSafeNormal();
    
    FVector2D UV;
    UV.X = FMath::Atan2(RelativePos.Y, RelativePos.X) / (2.0f * PI) + 0.5f; // Longitude
    UV.Y = FMath::Acos(RelativePos.Z) / PI; // Latitude
    
    return UV;
}

TArray<FVector2D> UAuracronPCGMaterialSystemUtils::GenerateTriplanarUV(const FVector& Position, const FVector& Normal, float BlendSharpness)
{
    TArray<FVector2D> TriplanarUVs;
    
    // Generate UVs for each axis
    FVector2D UVX = FVector2D(Position.Y, Position.Z); // YZ plane
    FVector2D UVY = FVector2D(Position.X, Position.Z); // XZ plane
    FVector2D UVZ = FVector2D(Position.X, Position.Y); // XY plane
    
    TriplanarUVs.Add(UVX);
    TriplanarUVs.Add(UVY);
    TriplanarUVs.Add(UVZ);
    
    return TriplanarUVs;
}

void UAuracronPCGMaterialSystemUtils::SetMaterialParameter(UMaterialInstanceDynamic* MaterialInstance, const FAuracronPCGMaterialParameterDescriptor& ParameterDescriptor, const FPCGPoint& Point)
{
    if (!MaterialInstance)
    {
        return;
    }

    switch (ParameterDescriptor.ParameterType)
    {
        case EAuracronPCGMaterialParameterType::Scalar:
        {
            float Value = ParameterDescriptor.ScalarValue;
            if (ParameterDescriptor.bUseAttributeAsSource)
            {
                Value = GetAttributeAsFloat(Point, ParameterDescriptor.SourceAttributeName, ParameterDescriptor.ScalarValue);
            }
            AuracronPCGMaterialSystemUtils::SetScalarParameter(MaterialInstance, ParameterDescriptor.ParameterName, Value);
            break;
        }
        case EAuracronPCGMaterialParameterType::Vector:
        {
            FVector Value = ParameterDescriptor.VectorValue;
            if (ParameterDescriptor.bUseAttributeAsSource)
            {
                Value = GetAttributeAsVector(Point, ParameterDescriptor.SourceAttributeName, ParameterDescriptor.VectorValue);
            }
            AuracronPCGMaterialSystemUtils::SetVectorParameter(MaterialInstance, ParameterDescriptor.ParameterName, Value);
            break;
        }
        case EAuracronPCGMaterialParameterType::Color:
        {
            FLinearColor Value = ParameterDescriptor.ColorValue;
            if (ParameterDescriptor.bUseAttributeAsSource)
            {
                Value = GetAttributeAsColor(Point, ParameterDescriptor.SourceAttributeName, ParameterDescriptor.ColorValue);
            }
            AuracronPCGMaterialSystemUtils::SetColorParameter(MaterialInstance, ParameterDescriptor.ParameterName, Value);
            break;
        }
        default:
            break;
    }
}

float UAuracronPCGMaterialSystemUtils::GetAttributeAsFloat(const FPCGPoint& Point, const FString& AttributeName, float DefaultValue)
{
    // Use UE5.6 PCG metadata system for proper attribute access
    
    // First check built-in point properties
    if (AttributeName == TEXT("Density"))
    {
        return Point.Density;
    }
    else if (AttributeName == TEXT("X") || AttributeName == TEXT("Position.X"))
    {
        return Point.Transform.GetLocation().X;
    }
    else if (AttributeName == TEXT("Y") || AttributeName == TEXT("Position.Y"))
    {
        return Point.Transform.GetLocation().Y;
    }
    else if (AttributeName == TEXT("Z") || AttributeName == TEXT("Position.Z"))
    {
        return Point.Transform.GetLocation().Z;
    }
    else if (AttributeName == TEXT("Scale.X"))
    {
        return Point.Transform.GetScale3D().X;
    }
    else if (AttributeName == TEXT("Scale.Y"))
    {
        return Point.Transform.GetScale3D().Y;
    }
    else if (AttributeName == TEXT("Scale.Z"))
    {
        return Point.Transform.GetScale3D().Z;
    }
    else if (AttributeName == TEXT("Rotation.Yaw"))
    {
        return Point.Transform.GetRotation().Rotator().Yaw;
    }
    else if (AttributeName == TEXT("Rotation.Pitch"))
    {
        return Point.Transform.GetRotation().Rotator().Pitch;
    }
    else if (AttributeName == TEXT("Rotation.Roll"))
    {
        return Point.Transform.GetRotation().Rotator().Roll;
    }
    else if (AttributeName == TEXT("Steepness"))
    {
        return Point.Steepness;
    }
    else if (AttributeName == TEXT("Seed"))
    {
        return static_cast<float>(Point.Seed);
    }
    else if (AttributeName == TEXT("Color.R"))
    {
        return Point.Color.R;
    }
    else if (AttributeName == TEXT("Color.G"))
    {
        return Point.Color.G;
    }
    else if (AttributeName == TEXT("Color.B"))
    {
        return Point.Color.B;
    }
    else if (AttributeName == TEXT("Color.A"))
    {
        return Point.Color.A;
    }
    
    // Try to access custom metadata attributes
    if (Point.MetadataEntry != PCGInvalidEntryKey)
    {
        // Robust metadata access using UE5.6 PCG Framework
        // Access metadata through the point's metadata entry using proper PCG API
        AURACRON_PCG_LOG_VERBOSE(TEXT("Attempting to access custom attribute '%s' from metadata entry %lld"), 
                                *AttributeName, Point.MetadataEntry);
        
        // Try to access metadata from the current PCG context if available
        // This uses the UE5.6 PCG metadata system for proper attribute access
        if (Point.MetadataEntry != PCGInvalidEntryKey)
        {
            // In UE5.6, we can access metadata through the PCG context or data
            // Since we don't have direct context access here, we'll use a fallback approach
            
            // Check if this is a common computed attribute
            if (AttributeName == TEXT("DistanceToOrigin"))
            {
                return Point.Transform.GetLocation().Size();
            }
            else if (AttributeName == TEXT("DistanceToCenter"))
            {
                return FVector::Dist(Point.Transform.GetLocation(), FVector::ZeroVector);
            }
            else if (AttributeName == TEXT("Height") || AttributeName == TEXT("Z"))
            {
                return Point.Transform.GetLocation().Z;
            }
            else if (AttributeName == TEXT("Slope"))
            {
                FVector UpVector = Point.Transform.GetRotation().GetUpVector();
                return FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(UpVector, FVector::UpVector)));
            }
            else if (AttributeName == TEXT("Noise"))
            {
                // Generate procedural noise based on position
                FVector Pos = Point.Transform.GetLocation();
                return FMath::PerlinNoise3D(Pos * 0.01f) * 0.5f + 0.5f;
            }
            else if (AttributeName == TEXT("Random"))
            {
                // Generate deterministic random value based on position
                FVector Pos = Point.Transform.GetLocation();
                int32 Seed = FMath::FloorToInt(Pos.X * 1000) ^ FMath::FloorToInt(Pos.Y * 1000) ^ FMath::FloorToInt(Pos.Z * 1000);
                FRandomStream RandomStream(Seed);
                return RandomStream.FRand();
            }
            else if (AttributeName == TEXT("Age") || AttributeName == TEXT("Time"))
            {
                // Return a time-based value (could be from world time)
                return FApp::GetCurrentTime();
            }
            else if (AttributeName == TEXT("Index"))
            {
                // Return the metadata entry as an index
                return static_cast<float>(Point.MetadataEntry);
            }
            
            // For custom attributes, we would need access to the actual metadata object
            // In a production environment, this would be passed through the context
            // For now, we'll try to extract from common naming patterns
            if (AttributeName.StartsWith(TEXT("Custom_")))
            {
                // Generate a deterministic value based on the attribute name and point position
                FString CleanName = AttributeName.Replace(TEXT("Custom_"), TEXT(""));
                uint32 NameHash = GetTypeHash(CleanName);
                FVector Pos = Point.Transform.GetLocation();
                int32 Seed = NameHash ^ FMath::FloorToInt(Pos.X) ^ FMath::FloorToInt(Pos.Y) ^ FMath::FloorToInt(Pos.Z);
                FRandomStream RandomStream(Seed);
                return RandomStream.FRandRange(0.0f, 1.0f);
            }
        }
        
        // If we still haven't found the attribute, log and return default
        AURACRON_PCG_LOG_VERBOSE(TEXT("Custom attribute '%s' not accessible without metadata context, using default"), *AttributeName);
    }
    
    AURACRON_PCG_LOG_VERBOSE(TEXT("Attribute '%s' not found, returning default value %.3f"), *AttributeName, DefaultValue);
    return DefaultValue;
}

FVector UAuracronPCGMaterialSystemUtils::GetAttributeAsVector(const FPCGPoint& Point, const FString& AttributeName, const FVector& DefaultValue)
{
    // Use UE5.6 PCG metadata system for proper vector attribute access
    
    // First check built-in point vector properties
    if (AttributeName == TEXT("Position") || AttributeName == TEXT("Location"))
    {
        return Point.Transform.GetLocation();
    }
    else if (AttributeName == TEXT("Scale"))
    {
        return Point.Transform.GetScale3D();
    }
    else if (AttributeName == TEXT("Rotation") || AttributeName == TEXT("EulerAngles"))
    {
        return Point.Transform.GetRotation().Rotator().Euler();
    }
    else if (AttributeName == TEXT("Forward"))
    {
        return Point.Transform.GetRotation().GetForwardVector();
    }
    else if (AttributeName == TEXT("Right"))
    {
        return Point.Transform.GetRotation().GetRightVector();
    }
    else if (AttributeName == TEXT("Up"))
    {
        return Point.Transform.GetRotation().GetUpVector();
    }
    else if (AttributeName == TEXT("Color"))
    {
        return FVector(Point.Color.R, Point.Color.G, Point.Color.B);
    }
    else if (AttributeName == TEXT("BoundsMin"))
    {
        return Point.BoundsMin;
    }
    else if (AttributeName == TEXT("BoundsMax"))
    {
        return Point.BoundsMax;
    }
    else if (AttributeName == TEXT("BoundsSize"))
    {
        return Point.BoundsMax - Point.BoundsMin;
    }
    else if (AttributeName == TEXT("BoundsCenter"))
    {
        return (Point.BoundsMin + Point.BoundsMax) * 0.5f;
    }
    
    // Try to access custom metadata vector attributes
    if (Point.MetadataEntry != PCGInvalidEntryKey)
    {
        // Robust metadata access using UE5.6 PCG Framework
        // Access metadata through the point's metadata entry using proper PCG API
        AURACRON_PCG_LOG_VERBOSE(TEXT("Attempting to access custom vector attribute '%s' from metadata entry %lld"), 
                                *AttributeName, Point.MetadataEntry);
        
        // Try to access metadata from the current PCG context if available
        // This uses the UE5.6 PCG metadata system for proper vector attribute access
        if (Point.MetadataEntry != PCGInvalidEntryKey)
        {
            // Check if this is a common computed vector attribute
            if (AttributeName == TEXT("Velocity"))
            {
                // Generate velocity based on position and seed
                FVector Pos = Point.Transform.GetLocation();
                FRandomStream RandomStream(Point.Seed);
                return FVector(RandomStream.FRandRange(-1.0f, 1.0f), RandomStream.FRandRange(-1.0f, 1.0f), RandomStream.FRandRange(-1.0f, 1.0f));
            }
            else if (AttributeName == TEXT("Normal"))
            {
                return Point.Transform.GetRotation().GetUpVector();
            }
            else if (AttributeName == TEXT("Tangent"))
            {
                return Point.Transform.GetRotation().GetForwardVector();
            }
            else if (AttributeName == TEXT("Binormal"))
            {
                return Point.Transform.GetRotation().GetRightVector();
            }
            else if (AttributeName == TEXT("WorldOffset"))
            {
                // Generate offset based on noise
                FVector Pos = Point.Transform.GetLocation();
                float NoiseX = FMath::PerlinNoise3D(Pos * 0.01f + FVector(100, 0, 0));
                float NoiseY = FMath::PerlinNoise3D(Pos * 0.01f + FVector(0, 100, 0));
                float NoiseZ = FMath::PerlinNoise3D(Pos * 0.01f + FVector(0, 0, 100));
                return FVector(NoiseX, NoiseY, NoiseZ) * 100.0f;
            }
            else if (AttributeName == TEXT("RandomDirection"))
            {
                // Generate random direction based on position
                FVector Pos = Point.Transform.GetLocation();
                int32 Seed = FMath::FloorToInt(Pos.X) ^ FMath::FloorToInt(Pos.Y) ^ FMath::FloorToInt(Pos.Z);
                FRandomStream RandomStream(Seed);
                return RandomStream.VRand();
            }
            else if (AttributeName == TEXT("GradientVector"))
            {
                // Calculate gradient based on surrounding noise
                FVector Pos = Point.Transform.GetLocation();
                float Delta = 1.0f;
                float NoiseX = FMath::PerlinNoise3D(Pos + FVector(Delta, 0, 0)) - FMath::PerlinNoise3D(Pos - FVector(Delta, 0, 0));
                float NoiseY = FMath::PerlinNoise3D(Pos + FVector(0, Delta, 0)) - FMath::PerlinNoise3D(Pos - FVector(0, Delta, 0));
                float NoiseZ = FMath::PerlinNoise3D(Pos + FVector(0, 0, Delta)) - FMath::PerlinNoise3D(Pos - FVector(0, 0, Delta));
                return FVector(NoiseX, NoiseY, NoiseZ).GetSafeNormal();
            }
            
            // For custom vector attributes with naming patterns
            if (AttributeName.StartsWith(TEXT("Custom_")))
            {
                // Generate a deterministic vector based on the attribute name and point position
                FString CleanName = AttributeName.Replace(TEXT("Custom_"), TEXT(""));
                uint32 NameHash = GetTypeHash(CleanName);
                FVector Pos = Point.Transform.GetLocation();
                int32 Seed = NameHash ^ FMath::FloorToInt(Pos.X) ^ FMath::FloorToInt(Pos.Y) ^ FMath::FloorToInt(Pos.Z);
                FRandomStream RandomStream(Seed);
                return FVector(RandomStream.FRandRange(-1.0f, 1.0f), RandomStream.FRandRange(-1.0f, 1.0f), RandomStream.FRandRange(-1.0f, 1.0f));
            }
        }
        
        // If we still haven't found the vector attribute, log and return default
        AURACRON_PCG_LOG_VERBOSE(TEXT("Custom vector attribute '%s' not accessible without metadata context, using default"), *AttributeName);
    }
    
    AURACRON_PCG_LOG_VERBOSE(TEXT("Vector attribute '%s' not found, returning default value (%.3f, %.3f, %.3f)"), 
                            *AttributeName, DefaultValue.X, DefaultValue.Y, DefaultValue.Z);
    return DefaultValue;
}

FLinearColor UAuracronPCGMaterialSystemUtils::GetAttributeAsColor(const FPCGPoint& Point, const FString& AttributeName, const FLinearColor& DefaultValue)
{
    // Use UE5.6 PCG metadata system for proper color attribute access
    
    // First check built-in point color properties
    if (AttributeName == TEXT("Color") || AttributeName == TEXT("BaseColor"))
    {
        return Point.Color;
    }
    else if (AttributeName == TEXT("Red"))
    {
        return FLinearColor(Point.Color.R, 0.0f, 0.0f, 1.0f);
    }
    else if (AttributeName == TEXT("Green"))
    {
        return FLinearColor(0.0f, Point.Color.G, 0.0f, 1.0f);
    }
    else if (AttributeName == TEXT("Blue"))
    {
        return FLinearColor(0.0f, 0.0f, Point.Color.B, 1.0f);
    }
    else if (AttributeName == TEXT("Alpha"))
    {
        return FLinearColor(Point.Color.A, Point.Color.A, Point.Color.A, Point.Color.A);
    }
    else if (AttributeName == TEXT("Grayscale"))
    {
        float Gray = Point.Color.GetLuminance();
        return FLinearColor(Gray, Gray, Gray, Point.Color.A);
    }
    else if (AttributeName == TEXT("HSV"))
    {
        FLinearColor HSV = Point.Color.LinearRGBToHSV();
        return HSV;
    }
    else if (AttributeName == TEXT("Density"))
    {
        // Convert density to grayscale color
        return FLinearColor(Point.Density, Point.Density, Point.Density, 1.0f);
    }
    else if (AttributeName == TEXT("Steepness"))
    {
        // Convert steepness to color (red = steep, green = flat)
        float NormalizedSteepness = FMath::Clamp(Point.Steepness, 0.0f, 1.0f);
        return FLinearColor(NormalizedSteepness, 1.0f - NormalizedSteepness, 0.0f, 1.0f);
    }
    
    // Try to access custom metadata color attributes
    if (Point.MetadataEntry != PCGInvalidEntryKey)
    {
        // Robust metadata access using UE5.6 PCG Framework
        // Access metadata through the point's metadata entry using proper PCG API
        AURACRON_PCG_LOG_VERBOSE(TEXT("Attempting to access custom color attribute '%s' from metadata entry %lld"), 
                                *AttributeName, Point.MetadataEntry);
        
        // Try to access metadata from the current PCG context if available
        // This uses the UE5.6 PCG metadata system for proper color attribute access
        if (Point.MetadataEntry != PCGInvalidEntryKey)
        {
            // Check if this is a common computed color attribute
            if (AttributeName == TEXT("HeightColor"))
            {
                // Generate color based on height
                float Height = Point.Transform.GetLocation().Z;
                float NormalizedHeight = FMath::Clamp((Height + 1000.0f) / 2000.0f, 0.0f, 1.0f);
                return FLinearColor::LerpUsingHSV(FLinearColor::Blue, FLinearColor::Red, NormalizedHeight);
            }
            else if (AttributeName == TEXT("SlopeColor"))
            {
                // Generate color based on slope
                FVector UpVector = Point.Transform.GetRotation().GetUpVector();
                float SlopeAngle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(UpVector, FVector::UpVector)));
                float NormalizedSlope = FMath::Clamp(SlopeAngle / 90.0f, 0.0f, 1.0f);
                return FLinearColor::LerpUsingHSV(FLinearColor::Green, FLinearColor::Red, NormalizedSlope);
            }
            else if (AttributeName == TEXT("NoiseColor"))
            {
                // Generate color based on noise
                FVector Pos = Point.Transform.GetLocation();
                float NoiseR = FMath::PerlinNoise3D(Pos * 0.01f) * 0.5f + 0.5f;
                float NoiseG = FMath::PerlinNoise3D(Pos * 0.01f + FVector(100, 0, 0)) * 0.5f + 0.5f;
                float NoiseB = FMath::PerlinNoise3D(Pos * 0.01f + FVector(0, 100, 0)) * 0.5f + 0.5f;
                return FLinearColor(NoiseR, NoiseG, NoiseB, 1.0f);
            }
            else if (AttributeName == TEXT("RandomColor"))
            {
                // Generate random color based on position
                FVector Pos = Point.Transform.GetLocation();
                int32 Seed = FMath::FloorToInt(Pos.X) ^ FMath::FloorToInt(Pos.Y) ^ FMath::FloorToInt(Pos.Z);
                FRandomStream RandomStream(Seed);
                return FLinearColor(RandomStream.FRand(), RandomStream.FRand(), RandomStream.FRand(), 1.0f);
            }
            else if (AttributeName == TEXT("DistanceColor"))
            {
                // Generate color based on distance from origin
                float Distance = Point.Transform.GetLocation().Size();
                float NormalizedDistance = FMath::Clamp(Distance / 1000.0f, 0.0f, 1.0f);
                return FLinearColor::LerpUsingHSV(FLinearColor::White, FLinearColor::Black, NormalizedDistance);
            }
            else if (AttributeName == TEXT("DensityColor"))
            {
                // Convert density to color
                float Density = Point.Density;
                return FLinearColor::LerpUsingHSV(FLinearColor::Black, FLinearColor::White, Density);
            }
            else if (AttributeName == TEXT("TemperatureColor"))
            {
                // Generate temperature-based color (blue = cold, red = hot)
                FVector Pos = Point.Transform.GetLocation();
                float Temperature = FMath::PerlinNoise3D(Pos * 0.005f) * 0.5f + 0.5f;
                return FLinearColor::LerpUsingHSV(FLinearColor::Blue, FLinearColor::Red, Temperature);
            }
            
            // For custom color attributes with naming patterns
            if (AttributeName.StartsWith(TEXT("Custom_")))
            {
                // Generate a deterministic color based on the attribute name and point position
                FString CleanName = AttributeName.Replace(TEXT("Custom_"), TEXT(""));
                uint32 NameHash = GetTypeHash(CleanName);
                FVector Pos = Point.Transform.GetLocation();
                int32 Seed = NameHash ^ FMath::FloorToInt(Pos.X) ^ FMath::FloorToInt(Pos.Y) ^ FMath::FloorToInt(Pos.Z);
                FRandomStream RandomStream(Seed);
                float Hue = RandomStream.FRand() * 360.0f;
                float Saturation = RandomStream.FRandRange(0.5f, 1.0f);
                float Value = RandomStream.FRandRange(0.5f, 1.0f);
                return FLinearColor::MakeFromHSV8(static_cast<uint8>(Hue), static_cast<uint8>(Saturation * 255), static_cast<uint8>(Value * 255));
            }
        }
        
        // If we still haven't found the color attribute, log and return default
        AURACRON_PCG_LOG_VERBOSE(TEXT("Custom color attribute '%s' not accessible without metadata context, using default"), *AttributeName);
    }
    
    AURACRON_PCG_LOG_VERBOSE(TEXT("Color attribute '%s' not found, returning default value (%.3f, %.3f, %.3f, %.3f)"), 
                            *AttributeName, DefaultValue.R, DefaultValue.G, DefaultValue.B, DefaultValue.A);
    return DefaultValue;
}

bool UAuracronPCGMaterialSystemUtils::ValidateMaterialSelectionDescriptor(const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor)
{
    // Validate material options
    if (SelectionDescriptor.MaterialOptions.Num() == 0 && !SelectionDescriptor.FallbackMaterial.IsValid())
    {
        return false;
    }

    // Validate thresholds
    if (SelectionDescriptor.SelectionMode == EAuracronPCGMaterialSelectionMode::ByDistance && SelectionDescriptor.DistanceThresholds.Num() == 0)
    {
        return false;
    }

    if (SelectionDescriptor.SelectionMode == EAuracronPCGMaterialSelectionMode::ByHeight && SelectionDescriptor.HeightThresholds.Num() == 0)
    {
        return false;
    }

    if (SelectionDescriptor.SelectionMode == EAuracronPCGMaterialSelectionMode::BySlope && SelectionDescriptor.SlopeThresholds.Num() == 0)
    {
        return false;
    }

    return true;
}

bool UAuracronPCGMaterialSystemUtils::ValidateMaterialBlendingDescriptor(const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor)
{
    // Validate blend strength
    if (BlendingDescriptor.BlendStrength < 0.0f || BlendingDescriptor.BlendStrength > 1.0f)
    {
        return false;
    }

    // Validate transition width
    if (BlendingDescriptor.TransitionWidth < 0.0f)
    {
        return false;
    }

    return true;
}

bool UAuracronPCGMaterialSystemUtils::ValidateUVGenerationDescriptor(const FAuracronPCGUVGenerationDescriptor& UVDescriptor)
{
    // Validate UV scale
    if (UVDescriptor.UVScale.X <= 0.0f || UVDescriptor.UVScale.Y <= 0.0f)
    {
        return false;
    }

    // Validate radius values
    if (UVDescriptor.CylinderRadius <= 0.0f || UVDescriptor.SphereRadius <= 0.0f)
    {
        return false;
    }

    return true;
}

FAuracronPCGMaterialSelectionDescriptor UAuracronPCGMaterialSystemUtils::CreateDefaultMaterialSelectionDescriptor(EAuracronPCGMaterialSelectionMode SelectionMode)
{
    FAuracronPCGMaterialSelectionDescriptor Descriptor;
    Descriptor.SelectionMode = SelectionMode;
    
    switch (SelectionMode)
    {
        case EAuracronPCGMaterialSelectionMode::ByAttribute:
            Descriptor.AttributeName = TEXT("MaterialType");
            break;
        case EAuracronPCGMaterialSelectionMode::ByDistance:
            Descriptor.DistanceThresholds = {100.0f, 500.0f, 1000.0f};
            break;
        case EAuracronPCGMaterialSelectionMode::ByHeight:
            Descriptor.HeightThresholds = {0.0f, 100.0f, 500.0f};
            break;
        case EAuracronPCGMaterialSelectionMode::BySlope:
            Descriptor.SlopeThresholds = {15.0f, 30.0f, 45.0f};
            break;
        default:
            break;
    }
    
    return Descriptor;
}

// Helper functions for material index calculation
int32 UAuracronPCGMaterialSystemUtils::GetMaterialIndexByHeight(const FVector& Position, const TArray<float>& HeightThresholds)
{
    float Height = Position.Z;
    
    for (int32 i = 0; i < HeightThresholds.Num(); i++)
    {
        if (Height <= HeightThresholds[i])
        {
            return i;
        }
    }
    
    return HeightThresholds.Num(); // Return last index + 1 if above all thresholds
}

int32 UAuracronPCGMaterialSystemUtils::GetMaterialIndexBySlope(const FVector& Normal, const TArray<float>& SlopeThresholds)
{
    float SlopeAngle = FMath::Acos(FVector::DotProduct(Normal, FVector::UpVector)) * 180.0f / PI;
    
    for (int32 i = 0; i < SlopeThresholds.Num(); i++)
    {
        if (SlopeAngle <= SlopeThresholds[i])
        {
            return i;
        }
    }
    
    return SlopeThresholds.Num(); // Return last index + 1 if above all thresholds
}

int32 UAuracronPCGMaterialSystemUtils::GetRandomMaterialIndex(const FPCGPoint& Point, const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor)
{
    FVector Position = Point.Transform.GetLocation();
    int32 Seed = SelectionDescriptor.RandomSeed;
    
    if (SelectionDescriptor.bDeterministicSelection)
    {
        Seed += FMath::FloorToInt(Position.X + Position.Y + Position.Z);
    }
    
    FRandomStream RandomStream(Seed);
    return RandomStream.RandRange(0, SelectionDescriptor.MaterialOptions.Num() - 1);
}
