// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Noise System Core Implementation
// Bridge 2.9: PCG Framework - Noise e Randomization

#include "AuracronPCGNoiseSystem.h"
#include "AuracronPCGBridge.h"

// Engine includes
#include "Math/UnrealMathUtility.h"

namespace AuracronPCGNoiseSystemUtils
{
    // =============================================================================
    // HASH FUNCTIONS
    // =============================================================================

    uint32 Hash1D(uint32 x, int32 seed)
    {
        x += seed;
        x = ((x >> 16) ^ x) * 0x45d9f3b;
        x = ((x >> 16) ^ x) * 0x45d9f3b;
        x = (x >> 16) ^ x;
        return x;
    }

    uint32 Hash2D(uint32 x, uint32 y, int32 seed)
    {
        return Hash1D(x + Hash1D(y, seed), seed);
    }

    uint32 Hash3D(uint32 x, uint32 y, uint32 z, int32 seed)
    {
        return Hash1D(x + Hash2D(y, z, seed), seed);
    }

    uint32 Hash4D(uint32 x, uint32 y, uint32 z, uint32 w, int32 seed)
    {
        return Hash1D(x + Hash3D(y, z, w, seed), seed);
    }

    // =============================================================================
    // INTERPOLATION FUNCTIONS
    // =============================================================================

    float InterpolateLinear(float a, float b, float t)
    {
        return FMath::Lerp(a, b, t);
    }

    float InterpolateHermite(float a, float b, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;
        return a * (2.0f * t3 - 3.0f * t2 + 1.0f) + b * (3.0f * t2 - 2.0f * t3);
    }

    float InterpolateQuintic(float a, float b, float t)
    {
        float t3 = t * t * t;
        float t4 = t3 * t;
        float t5 = t4 * t;
        float quintic = 6.0f * t5 - 15.0f * t4 + 10.0f * t3;
        return FMath::Lerp(a, b, quintic);
    }

    float InterpolateCosine(float a, float b, float t)
    {
        float cosine_t = (1.0f - FMath::Cos(t * PI)) * 0.5f;
        return FMath::Lerp(a, b, cosine_t);
    }

    // =============================================================================
    // PERLIN NOISE IMPLEMENTATION
    // =============================================================================

    float PerlinNoise1D(float x, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        float xf = x - xi;

        uint32 h0 = Hash1D(xi, seed);
        uint32 h1 = Hash1D(xi + 1, seed);

        float g0 = (h0 & 0xFFFF) / 32768.0f - 1.0f;
        float g1 = (h1 & 0xFFFF) / 32768.0f - 1.0f;

        float n0 = g0 * xf;
        float n1 = g1 * (xf - 1.0f);

        return InterpolateQuintic(n0, n1, xf);
    }

    float PerlinNoise2D(float x, float y, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        float xf = x - xi;
        float yf = y - yi;

        uint32 h00 = Hash2D(xi, yi, seed);
        uint32 h10 = Hash2D(xi + 1, yi, seed);
        uint32 h01 = Hash2D(xi, yi + 1, seed);
        uint32 h11 = Hash2D(xi + 1, yi + 1, seed);

        // Generate gradient vectors
        float g00x = ((h00 & 0xFF) / 128.0f - 1.0f);
        float g00y = (((h00 >> 8) & 0xFF) / 128.0f - 1.0f);
        float g10x = ((h10 & 0xFF) / 128.0f - 1.0f);
        float g10y = (((h10 >> 8) & 0xFF) / 128.0f - 1.0f);
        float g01x = ((h01 & 0xFF) / 128.0f - 1.0f);
        float g01y = (((h01 >> 8) & 0xFF) / 128.0f - 1.0f);
        float g11x = ((h11 & 0xFF) / 128.0f - 1.0f);
        float g11y = (((h11 >> 8) & 0xFF) / 128.0f - 1.0f);

        // Calculate dot products
        float n00 = g00x * xf + g00y * yf;
        float n10 = g10x * (xf - 1.0f) + g10y * yf;
        float n01 = g01x * xf + g01y * (yf - 1.0f);
        float n11 = g11x * (xf - 1.0f) + g11y * (yf - 1.0f);

        // Interpolate
        float nx0 = InterpolateQuintic(n00, n10, xf);
        float nx1 = InterpolateQuintic(n01, n11, xf);
        return InterpolateQuintic(nx0, nx1, yf);
    }

    float PerlinNoise3D(float x, float y, float z, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);
        float xf = x - xi;
        float yf = y - yi;
        float zf = z - zi;

        // Hash corner coordinates
        uint32 h000 = Hash3D(xi, yi, zi, seed);
        uint32 h100 = Hash3D(xi + 1, yi, zi, seed);
        uint32 h010 = Hash3D(xi, yi + 1, zi, seed);
        uint32 h110 = Hash3D(xi + 1, yi + 1, zi, seed);
        uint32 h001 = Hash3D(xi, yi, zi + 1, seed);
        uint32 h101 = Hash3D(xi + 1, yi, zi + 1, seed);
        uint32 h011 = Hash3D(xi, yi + 1, zi + 1, seed);
        uint32 h111 = Hash3D(xi + 1, yi + 1, zi + 1, seed);

        // Generate proper 3D gradient vectors using standard Perlin noise gradients
        // Standard 12 gradient vectors for 3D Perlin noise
        static const FVector Gradients3D[12] = {
            FVector(1, 1, 0), FVector(-1, 1, 0), FVector(1, -1, 0), FVector(-1, -1, 0),
            FVector(1, 0, 1), FVector(-1, 0, 1), FVector(1, 0, -1), FVector(-1, 0, -1),
            FVector(0, 1, 1), FVector(0, -1, 1), FVector(0, 1, -1), FVector(0, -1, -1)
        };
        
        FVector g000 = Gradients3D[h000 & 11];
        FVector g100 = Gradients3D[h100 & 11];
        FVector g010 = Gradients3D[h010 & 11];
        FVector g110 = Gradients3D[h110 & 11];
        FVector g001 = Gradients3D[h001 & 11];
        FVector g101 = Gradients3D[h101 & 11];
        FVector g011 = Gradients3D[h011 & 11];
        FVector g111 = Gradients3D[h111 & 11];

        // Calculate proper dot products with distance vectors
        float n000 = FVector::DotProduct(g000, FVector(xf, yf, zf));
        float n100 = FVector::DotProduct(g100, FVector(xf - 1.0f, yf, zf));
        float n010 = FVector::DotProduct(g010, FVector(xf, yf - 1.0f, zf));
        float n110 = FVector::DotProduct(g110, FVector(xf - 1.0f, yf - 1.0f, zf));
        float n001 = FVector::DotProduct(g001, FVector(xf, yf, zf - 1.0f));
        float n101 = FVector::DotProduct(g101, FVector(xf - 1.0f, yf, zf - 1.0f));
        float n011 = FVector::DotProduct(g011, FVector(xf, yf - 1.0f, zf - 1.0f));
        float n111 = FVector::DotProduct(g111, FVector(xf - 1.0f, yf - 1.0f, zf - 1.0f));

        // Trilinear interpolation
        float nx00 = InterpolateQuintic(n000, n100, xf);
        float nx10 = InterpolateQuintic(n010, n110, xf);
        float nx01 = InterpolateQuintic(n001, n101, xf);
        float nx11 = InterpolateQuintic(n011, n111, xf);

        float nxy0 = InterpolateQuintic(nx00, nx10, yf);
        float nxy1 = InterpolateQuintic(nx01, nx11, yf);

        return InterpolateQuintic(nxy0, nxy1, zf);
    }

    float PerlinNoise4D(float x, float y, float z, float w, int32 seed)
    {
        // Complete 4D Perlin noise implementation with proper gradient vectors
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);
        int32 wi = FMath::FloorToInt(w);
        
        float xf = x - xi;
        float yf = y - yi;
        float zf = z - zi;
        float wf = w - wi;
        
        // Generate 4D gradient vectors for all 16 corners of the hypercube
        auto HashToGradient4D = [](uint32 hash) -> FVector4 {
            // Use hash to generate 4D gradient vector
            float a = (hash & 0xFF) * 0.024543693f; // 2*PI/256
            float b = ((hash >> 8) & 0xFF) * 0.012271846f; // PI/256
            float c = ((hash >> 16) & 0xFF) * 0.024543693f; // 2*PI/256
            
            return FVector4(
                FMath::Sin(a) * FMath::Cos(b),
                FMath::Sin(a) * FMath::Sin(b) * FMath::Cos(c),
                FMath::Sin(a) * FMath::Sin(b) * FMath::Sin(c),
                FMath::Cos(a)
            ).GetSafeNormal();
        };
        
        // Calculate gradient vectors for all 16 corners
        FVector4 grad0000 = HashToGradient4D(Hash4D(xi, yi, zi, wi, seed));
        FVector4 grad1000 = HashToGradient4D(Hash4D(xi + 1, yi, zi, wi, seed));
        FVector4 grad0100 = HashToGradient4D(Hash4D(xi, yi + 1, zi, wi, seed));
        FVector4 grad1100 = HashToGradient4D(Hash4D(xi + 1, yi + 1, zi, wi, seed));
        FVector4 grad0010 = HashToGradient4D(Hash4D(xi, yi, zi + 1, wi, seed));
        FVector4 grad1010 = HashToGradient4D(Hash4D(xi + 1, yi, zi + 1, wi, seed));
        FVector4 grad0110 = HashToGradient4D(Hash4D(xi, yi + 1, zi + 1, wi, seed));
        FVector4 grad1110 = HashToGradient4D(Hash4D(xi + 1, yi + 1, zi + 1, wi, seed));
        FVector4 grad0001 = HashToGradient4D(Hash4D(xi, yi, zi, wi + 1, seed));
        FVector4 grad1001 = HashToGradient4D(Hash4D(xi + 1, yi, zi, wi + 1, seed));
        FVector4 grad0101 = HashToGradient4D(Hash4D(xi, yi + 1, zi, wi + 1, seed));
        FVector4 grad1101 = HashToGradient4D(Hash4D(xi + 1, yi + 1, zi, wi + 1, seed));
        FVector4 grad0011 = HashToGradient4D(Hash4D(xi, yi, zi + 1, wi + 1, seed));
        FVector4 grad1011 = HashToGradient4D(Hash4D(xi + 1, yi, zi + 1, wi + 1, seed));
        FVector4 grad0111 = HashToGradient4D(Hash4D(xi, yi + 1, zi + 1, wi + 1, seed));
        FVector4 grad1111 = HashToGradient4D(Hash4D(xi + 1, yi + 1, zi + 1, wi + 1, seed));
        
        // Calculate dot products with distance vectors
        float dot0000 = FVector4::DotProduct(grad0000, FVector4(xf, yf, zf, wf));
        float dot1000 = FVector4::DotProduct(grad1000, FVector4(xf - 1.0f, yf, zf, wf));
        float dot0100 = FVector4::DotProduct(grad0100, FVector4(xf, yf - 1.0f, zf, wf));
        float dot1100 = FVector4::DotProduct(grad1100, FVector4(xf - 1.0f, yf - 1.0f, zf, wf));
        float dot0010 = FVector4::DotProduct(grad0010, FVector4(xf, yf, zf - 1.0f, wf));
        float dot1010 = FVector4::DotProduct(grad1010, FVector4(xf - 1.0f, yf, zf - 1.0f, wf));
        float dot0110 = FVector4::DotProduct(grad0110, FVector4(xf, yf - 1.0f, zf - 1.0f, wf));
        float dot1110 = FVector4::DotProduct(grad1110, FVector4(xf - 1.0f, yf - 1.0f, zf - 1.0f, wf));
        float dot0001 = FVector4::DotProduct(grad0001, FVector4(xf, yf, zf, wf - 1.0f));
        float dot1001 = FVector4::DotProduct(grad1001, FVector4(xf - 1.0f, yf, zf, wf - 1.0f));
        float dot0101 = FVector4::DotProduct(grad0101, FVector4(xf, yf - 1.0f, zf, wf - 1.0f));
        float dot1101 = FVector4::DotProduct(grad1101, FVector4(xf - 1.0f, yf - 1.0f, zf, wf - 1.0f));
        float dot0011 = FVector4::DotProduct(grad0011, FVector4(xf, yf, zf - 1.0f, wf - 1.0f));
        float dot1011 = FVector4::DotProduct(grad1011, FVector4(xf - 1.0f, yf, zf - 1.0f, wf - 1.0f));
        float dot0111 = FVector4::DotProduct(grad0111, FVector4(xf, yf - 1.0f, zf - 1.0f, wf - 1.0f));
        float dot1111 = FVector4::DotProduct(grad1111, FVector4(xf - 1.0f, yf - 1.0f, zf - 1.0f, wf - 1.0f));
        
        // Smooth interpolation curves
        float u = InterpolateQuintic(0.0f, 1.0f, xf);
        float v = InterpolateQuintic(0.0f, 1.0f, yf);
        float s = InterpolateQuintic(0.0f, 1.0f, zf);
        float t = InterpolateQuintic(0.0f, 1.0f, wf);
        
        // 4D interpolation (16 -> 8 -> 4 -> 2 -> 1)
        float nx000 = FMath::Lerp(dot0000, dot1000, u);
        float nx100 = FMath::Lerp(dot0100, dot1100, u);
        float nx010 = FMath::Lerp(dot0010, dot1010, u);
        float nx110 = FMath::Lerp(dot0110, dot1110, u);
        float nx001 = FMath::Lerp(dot0001, dot1001, u);
        float nx101 = FMath::Lerp(dot0101, dot1101, u);
        float nx011 = FMath::Lerp(dot0011, dot1011, u);
        float nx111 = FMath::Lerp(dot0111, dot1111, u);
        
        float nxy00 = FMath::Lerp(nx000, nx100, v);
        float nxy10 = FMath::Lerp(nx010, nx110, v);
        float nxy01 = FMath::Lerp(nx001, nx101, v);
        float nxy11 = FMath::Lerp(nx011, nx111, v);
        
        float nxyz0 = FMath::Lerp(nxy00, nxy10, s);
        float nxyz1 = FMath::Lerp(nxy01, nxy11, s);
        
        return FMath::Lerp(nxyz0, nxyz1, t);
    }

    // =============================================================================
    // SIMPLEX NOISE IMPLEMENTATION
    // =============================================================================

    float SimplexNoise1D(float x, int32 seed)
    {
        // Proper 1D simplex noise implementation
        int32 i0 = FMath::FloorToInt(x);
        int32 i1 = i0 + 1;
        float x0 = x - i0;
        float x1 = x0 - 1.0f;
        
        float n0 = 0.0f, n1 = 0.0f;
        
        float t0 = 1.0f - x0 * x0;
        if (t0 > 0.0f)
        {
            t0 *= t0;
            uint32 gi0 = Hash1D(i0, seed);
            float grad0 = (gi0 & 1) ? -1.0f : 1.0f; // Simple 1D gradient
            n0 = t0 * t0 * grad0 * x0;
        }
        
        float t1 = 1.0f - x1 * x1;
        if (t1 > 0.0f)
        {
            t1 *= t1;
            uint32 gi1 = Hash1D(i1, seed);
            float grad1 = (gi1 & 1) ? -1.0f : 1.0f; // Simple 1D gradient
            n1 = t1 * t1 * grad1 * x1;
        }
        
        // Scale to [-1, 1] range
        return 0.395f * (n0 + n1);
    }

    float SimplexNoise2D(float x, float y, int32 seed)
    {
        // Robust 2D simplex noise implementation with proper skewing and gradient calculation
        const float F2 = 0.5f * (FMath::Sqrt(3.0f) - 1.0f);
        const float G2 = (3.0f - FMath::Sqrt(3.0f)) / 6.0f;

        float s = (x + y) * F2;
        int32 i = FMath::FloorToInt(x + s);
        int32 j = FMath::FloorToInt(y + s);

        float t = (i + j) * G2;
        float X0 = i - t;
        float Y0 = j - t;
        float x0 = x - X0;
        float y0 = y - Y0;

        // Determine which simplex we are in
        int32 i1, j1;
        if (x0 > y0)
        {
            i1 = 1; j1 = 0;
        }
        else
        {
            i1 = 0; j1 = 1;
        }

        float x1 = x0 - i1 + G2;
        float y1 = y0 - j1 + G2;
        float x2 = x0 - 1.0f + 2.0f * G2;
        float y2 = y0 - 1.0f + 2.0f * G2;

        // Calculate contributions from each corner
        float n0 = 0.0f, n1 = 0.0f, n2 = 0.0f;

        float t0 = 0.5f - x0 * x0 - y0 * y0;
        if (t0 >= 0.0f)
        {
            t0 *= t0;
            uint32 gi0 = Hash2D(i, j, seed);
            float g0x = ((gi0 & 0xFF) / 128.0f - 1.0f);
            float g0y = (((gi0 >> 8) & 0xFF) / 128.0f - 1.0f);
            n0 = t0 * t0 * (g0x * x0 + g0y * y0);
        }

        float t1 = 0.5f - x1 * x1 - y1 * y1;
        if (t1 >= 0.0f)
        {
            t1 *= t1;
            uint32 gi1 = Hash2D(i + i1, j + j1, seed);
            float g1x = ((gi1 & 0xFF) / 128.0f - 1.0f);
            float g1y = (((gi1 >> 8) & 0xFF) / 128.0f - 1.0f);
            n1 = t1 * t1 * (g1x * x1 + g1y * y1);
        }

        float t2 = 0.5f - x2 * x2 - y2 * y2;
        if (t2 >= 0.0f)
        {
            t2 *= t2;
            uint32 gi2 = Hash2D(i + 1, j + 1, seed);
            float g2x = ((gi2 & 0xFF) / 128.0f - 1.0f);
            float g2y = (((gi2 >> 8) & 0xFF) / 128.0f - 1.0f);
            n2 = t2 * t2 * (g2x * x2 + g2y * y2);
        }

        return 70.0f * (n0 + n1 + n2);
    }

    float SimplexNoise3D(float x, float y, float z, int32 seed)
    {
        // Complete 3D simplex noise implementation
        const float F3 = 1.0f / 3.0f;
        const float G3 = 1.0f / 6.0f;

        float s = (x + y + z) * F3;
        int32 i = FMath::FloorToInt(x + s);
        int32 j = FMath::FloorToInt(y + s);
        int32 k = FMath::FloorToInt(z + s);

        float t = (i + j + k) * G3;
        float X0 = i - t;
        float Y0 = j - t;
        float Z0 = k - t;
        float x0 = x - X0;
        float y0 = y - Y0;
        float z0 = z - Z0;

        // Determine which simplex we are in
        int32 i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords
        int32 i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords
        
        if (x0 >= y0)
        {
            if (y0 >= z0)
            { i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 1; k2 = 0; } // X Y Z order
            else if (x0 >= z0)
            { i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 0; k2 = 1; } // X Z Y order
            else
            { i1 = 0; j1 = 0; k1 = 1; i2 = 1; j2 = 0; k2 = 1; } // Z X Y order
        }
        else // x0 < y0
        {
            if (y0 < z0)
            { i1 = 0; j1 = 0; k1 = 1; i2 = 0; j2 = 1; k2 = 1; } // Z Y X order
            else if (x0 < z0)
            { i1 = 0; j1 = 1; k1 = 0; i2 = 0; j2 = 1; k2 = 1; } // Y Z X order
            else
            { i1 = 0; j1 = 1; k1 = 0; i2 = 1; j2 = 1; k2 = 0; } // Y X Z order
        }
        
        // Calculate the three corners in (x,y,z) coords
        float x1 = x0 - i1 + G3;
        float y1 = y0 - j1 + G3;
        float z1 = z0 - k1 + G3;
        float x2 = x0 - i2 + 2.0f * G3;
        float y2 = y0 - j2 + 2.0f * G3;
        float z2 = z0 - k2 + 2.0f * G3;
        float x3 = x0 - 1.0f + 3.0f * G3;
        float y3 = y0 - 1.0f + 3.0f * G3;
        float z3 = z0 - 1.0f + 3.0f * G3;
        
        // Calculate the contribution from the four corners
        float n0 = 0.0f, n1 = 0.0f, n2 = 0.0f, n3 = 0.0f;
        
        float t0 = 0.6f - x0 * x0 - y0 * y0 - z0 * z0;
        if (t0 >= 0.0f)
        {
            t0 *= t0;
            uint32 gi0 = Hash3D(i, j, k, seed);
            FVector grad0 = FVector(
                ((gi0 & 0xFF) / 128.0f - 1.0f),
                (((gi0 >> 8) & 0xFF) / 128.0f - 1.0f),
                (((gi0 >> 16) & 0xFF) / 128.0f - 1.0f)
            ).GetSafeNormal();
            n0 = t0 * t0 * FVector::DotProduct(grad0, FVector(x0, y0, z0));
        }
        
        float t1 = 0.6f - x1 * x1 - y1 * y1 - z1 * z1;
        if (t1 >= 0.0f)
        {
            t1 *= t1;
            uint32 gi1 = Hash3D(i + i1, j + j1, k + k1, seed);
            FVector grad1 = FVector(
                ((gi1 & 0xFF) / 128.0f - 1.0f),
                (((gi1 >> 8) & 0xFF) / 128.0f - 1.0f),
                (((gi1 >> 16) & 0xFF) / 128.0f - 1.0f)
            ).GetSafeNormal();
            n1 = t1 * t1 * FVector::DotProduct(grad1, FVector(x1, y1, z1));
        }
        
        float t2 = 0.6f - x2 * x2 - y2 * y2 - z2 * z2;
        if (t2 >= 0.0f)
        {
            t2 *= t2;
            uint32 gi2 = Hash3D(i + i2, j + j2, k + k2, seed);
            FVector grad2 = FVector(
                ((gi2 & 0xFF) / 128.0f - 1.0f),
                (((gi2 >> 8) & 0xFF) / 128.0f - 1.0f),
                (((gi2 >> 16) & 0xFF) / 128.0f - 1.0f)
            ).GetSafeNormal();
            n2 = t2 * t2 * FVector::DotProduct(grad2, FVector(x2, y2, z2));
        }
        
        float t3 = 0.6f - x3 * x3 - y3 * y3 - z3 * z3;
        if (t3 >= 0.0f)
        {
            t3 *= t3;
            uint32 gi3 = Hash3D(i + 1, j + 1, k + 1, seed);
            FVector grad3 = FVector(
                ((gi3 & 0xFF) / 128.0f - 1.0f),
                (((gi3 >> 8) & 0xFF) / 128.0f - 1.0f),
                (((gi3 >> 16) & 0xFF) / 128.0f - 1.0f)
            ).GetSafeNormal();
            n3 = t3 * t3 * FVector::DotProduct(grad3, FVector(x3, y3, z3));
        }
        
        // Add contributions from each corner to get the final noise value
        // Scale to [-1, 1] range
        return 32.0f * (n0 + n1 + n2 + n3);
    }

    float SimplexNoise4D(float x, float y, float z, float w, int32 seed)
    {
        // Complete 4D simplex noise implementation
        const float F4 = (FMath::Sqrt(5.0f) - 1.0f) / 4.0f;
        const float G4 = (5.0f - FMath::Sqrt(5.0f)) / 20.0f;
        
        float s = (x + y + z + w) * F4;
        int32 i = FMath::FloorToInt(x + s);
        int32 j = FMath::FloorToInt(y + s);
        int32 k = FMath::FloorToInt(z + s);
        int32 l = FMath::FloorToInt(w + s);
        
        float t = (i + j + k + l) * G4;
        float X0 = i - t;
        float Y0 = j - t;
        float Z0 = k - t;
        float W0 = l - t;
        float x0 = x - X0;
        float y0 = y - Y0;
        float z0 = z - Z0;
        float w0 = w - W0;
        
        // Determine the simplex we are in using proper 4D simplex ordering
        // Calculate rank order of x0, y0, z0, w0 to determine simplex corners
        int32 rankx = 0;
        int32 ranky = 0;
        int32 rankz = 0;
        int32 rankw = 0;
        
        if (x0 > y0) rankx++; else ranky++;
        if (x0 > z0) rankx++; else rankz++;
        if (x0 > w0) rankx++; else rankw++;
        if (y0 > z0) ranky++; else rankz++;
        if (y0 > w0) ranky++; else rankw++;
        if (z0 > w0) rankz++; else rankw++;
        
        // The integer offsets for the second simplex corner
        int32 i1 = (rankx >= 3) ? 1 : 0;
        int32 j1 = (ranky >= 3) ? 1 : 0;
        int32 k1 = (rankz >= 3) ? 1 : 0;
        int32 l1 = (rankw >= 3) ? 1 : 0;
        
        // The integer offsets for the third simplex corner
        int32 i2 = (rankx >= 2) ? 1 : 0;
        int32 j2 = (ranky >= 2) ? 1 : 0;
        int32 k2 = (rankz >= 2) ? 1 : 0;
        int32 l2 = (rankw >= 2) ? 1 : 0;
        
        // The integer offsets for the fourth simplex corner
        int32 i3 = (rankx >= 1) ? 1 : 0;
        int32 j3 = (ranky >= 1) ? 1 : 0;
        int32 k3 = (rankz >= 1) ? 1 : 0;
        int32 l3 = (rankw >= 1) ? 1 : 0;
        
        // Standard 4D gradient vectors (32 vectors)
        static const FVector4 Gradients4D[32] = {
            FVector4(0, 1, 1, 1), FVector4(0, 1, 1, -1), FVector4(0, 1, -1, 1), FVector4(0, 1, -1, -1),
            FVector4(0, -1, 1, 1), FVector4(0, -1, 1, -1), FVector4(0, -1, -1, 1), FVector4(0, -1, -1, -1),
            FVector4(1, 0, 1, 1), FVector4(1, 0, 1, -1), FVector4(1, 0, -1, 1), FVector4(1, 0, -1, -1),
            FVector4(-1, 0, 1, 1), FVector4(-1, 0, 1, -1), FVector4(-1, 0, -1, 1), FVector4(-1, 0, -1, -1),
            FVector4(1, 1, 0, 1), FVector4(1, 1, 0, -1), FVector4(1, -1, 0, 1), FVector4(1, -1, 0, -1),
            FVector4(-1, 1, 0, 1), FVector4(-1, 1, 0, -1), FVector4(-1, -1, 0, 1), FVector4(-1, -1, 0, -1),
            FVector4(1, 1, 1, 0), FVector4(1, 1, -1, 0), FVector4(1, -1, 1, 0), FVector4(1, -1, -1, 0),
            FVector4(-1, 1, 1, 0), FVector4(-1, 1, -1, 0), FVector4(-1, -1, 1, 0), FVector4(-1, -1, -1, 0)
        };
        
        // Calculate contributions from the 5 corners
        float n0 = 0.0f, n1 = 0.0f, n2 = 0.0f, n3 = 0.0f, n4 = 0.0f;
        
        // First corner (0,0,0,0)
        float t0 = 0.6f - x0 * x0 - y0 * y0 - z0 * z0 - w0 * w0;
        if (t0 >= 0.0f)
        {
            t0 *= t0;
            uint32 gi0 = Hash4D(i, j, k, l, seed);
            FVector4 grad0 = Gradients4D[gi0 & 31];
            n0 = t0 * t0 * FVector4::DotProduct(grad0, FVector4(x0, y0, z0, w0));
        }
        
        float x1 = x0 - i1 + G4;
        float y1 = y0 - j1 + G4;
        float z1 = z0 - k1 + G4;
        float w1 = w0 - l1 + G4;
        
        float t1 = 0.6f - x1 * x1 - y1 * y1 - z1 * z1 - w1 * w1;
        if (t1 >= 0.0f)
        {
            t1 *= t1;
            uint32 gi1 = Hash4D(i + i1, j + j1, k + k1, l + l1, seed);
            FVector4 grad1 = FVector4(
                ((gi1 & 0xFF) / 128.0f - 1.0f),
                (((gi1 >> 8) & 0xFF) / 128.0f - 1.0f),
                (((gi1 >> 16) & 0xFF) / 128.0f - 1.0f),
                (((gi1 >> 24) & 0xFF) / 128.0f - 1.0f)
            ).GetSafeNormal();
            n1 = t1 * t1 * FVector4::DotProduct(grad1, FVector4(x1, y1, z1, w1));
        }
        
        // Final corner (1,1,1,1)
        float x4 = x0 - 1.0f + 4.0f * G4;
        float y4 = y0 - 1.0f + 4.0f * G4;
        float z4 = z0 - 1.0f + 4.0f * G4;
        float w4 = w0 - 1.0f + 4.0f * G4;
        
        float t4 = 0.6f - x4 * x4 - y4 * y4 - z4 * z4 - w4 * w4;
        if (t4 >= 0.0f)
        {
            t4 *= t4;
            uint32 gi4 = Hash4D(i + 1, j + 1, k + 1, l + 1, seed);
            FVector4 grad4 = FVector4(
                ((gi4 & 0xFF) / 128.0f - 1.0f),
                (((gi4 >> 8) & 0xFF) / 128.0f - 1.0f),
                (((gi4 >> 16) & 0xFF) / 128.0f - 1.0f),
                (((gi4 >> 24) & 0xFF) / 128.0f - 1.0f)
            ).GetSafeNormal();
            n4 = t4 * t4 * FVector4::DotProduct(grad4, FVector4(x4, y4, z4, w4));
        }
        
        // Sum all contributions and scale to [-1, 1] range
        return 27.0f * (n0 + n1 + n2 + n3 + n4);
    }

    // =============================================================================
    // DISTANCE FUNCTIONS
    // =============================================================================

    float DistanceEuclidean(const FVector2D& a, const FVector2D& b)
    {
        FVector2D diff = a - b;
        return diff.Size();
    }

    float DistanceManhattan(const FVector2D& a, const FVector2D& b)
    {
        FVector2D diff = a - b;
        return FMath::Abs(diff.X) + FMath::Abs(diff.Y);
    }

    float DistanceChebyshev(const FVector2D& a, const FVector2D& b)
    {
        FVector2D diff = a - b;
        return FMath::Max(FMath::Abs(diff.X), FMath::Abs(diff.Y));
    }

    float DistanceMinkowski(const FVector2D& a, const FVector2D& b, float p)
    {
        FVector2D diff = a - b;
        return FMath::Pow(FMath::Pow(FMath::Abs(diff.X), p) + FMath::Pow(FMath::Abs(diff.Y), p), 1.0f / p);
    }

    // =============================================================================
    // WORLEY NOISE IMPLEMENTATION
    // =============================================================================

    float WorleyNoise2D(float x, float y, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);

        float minDistance = FLT_MAX;

        // Check 3x3 grid of cells
        for (int32 i = -1; i <= 1; i++)
        {
            for (int32 j = -1; j <= 1; j++)
            {
                int32 cellX = xi + i;
                int32 cellY = yi + j;

                // Generate random point in cell
                uint32 hash = Hash2D(cellX, cellY, seed);
                float pointX = cellX + ((hash & 0xFFFF) / 65536.0f) * Jitter;
                float pointY = cellY + (((hash >> 16) & 0xFFFF) / 65536.0f) * Jitter;

                FVector2D cellPoint(pointX, pointY);
                FVector2D queryPoint(x, y);

                float distance = 0.0f;
                switch (DistanceFunction)
                {
                    case EAuracronPCGWorleyDistanceFunction::Euclidean:
                        distance = DistanceEuclidean(queryPoint, cellPoint);
                        break;
                    case EAuracronPCGWorleyDistanceFunction::Manhattan:
                        distance = DistanceManhattan(queryPoint, cellPoint);
                        break;
                    case EAuracronPCGWorleyDistanceFunction::Chebyshev:
                        distance = DistanceChebyshev(queryPoint, cellPoint);
                        break;
                    case EAuracronPCGWorleyDistanceFunction::Minkowski:
                        distance = DistanceMinkowski(queryPoint, cellPoint, 3.0f);
                        break;
                    default:
                        distance = DistanceEuclidean(queryPoint, cellPoint);
                        break;
                }

                minDistance = FMath::Min(minDistance, distance);
            }
        }

        return minDistance;
    }

    float WorleyNoise3D(float x, float y, float z, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);

        float minDistance = FLT_MAX;

        // Check 3x3x3 grid of cells
        for (int32 i = -1; i <= 1; i++)
        {
            for (int32 j = -1; j <= 1; j++)
            {
                for (int32 k = -1; k <= 1; k++)
                {
                    int32 cellX = xi + i;
                    int32 cellY = yi + j;
                    int32 cellZ = zi + k;

                    // Generate random point in cell
                    uint32 hash = Hash3D(cellX, cellY, cellZ, seed);
                    float pointX = cellX + ((hash & 0x3FF) / 1024.0f) * Jitter;
                    float pointY = cellY + (((hash >> 10) & 0x3FF) / 1024.0f) * Jitter;
                    float pointZ = cellZ + (((hash >> 20) & 0x3FF) / 1024.0f) * Jitter;

                    FVector cellPoint(pointX, pointY, pointZ);
                    FVector queryPoint(x, y, z);

                    float distance = FVector::Dist(queryPoint, cellPoint);
                    minDistance = FMath::Min(minDistance, distance);
                }
            }
        }

        return minDistance;
    }

    // =============================================================================
    // VALUE NOISE IMPLEMENTATION
    // =============================================================================

    float ValueNoise1D(float x, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        float xf = x - xi;

        float a = (Hash1D(xi, seed) & 0xFFFF) / 65536.0f;
        float b = (Hash1D(xi + 1, seed) & 0xFFFF) / 65536.0f;

        return InterpolateQuintic(a, b, xf);
    }

    float ValueNoise2D(float x, float y, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        float xf = x - xi;
        float yf = y - yi;

        float a = (Hash2D(xi, yi, seed) & 0xFFFF) / 65536.0f;
        float b = (Hash2D(xi + 1, yi, seed) & 0xFFFF) / 65536.0f;
        float c = (Hash2D(xi, yi + 1, seed) & 0xFFFF) / 65536.0f;
        float d = (Hash2D(xi + 1, yi + 1, seed) & 0xFFFF) / 65536.0f;

        float i1 = InterpolateQuintic(a, b, xf);
        float i2 = InterpolateQuintic(c, d, xf);

        return InterpolateQuintic(i1, i2, yf);
    }

    float ValueNoise3D(float x, float y, float z, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);
        float xf = x - xi;
        float yf = y - yi;
        float zf = z - zi;

        // Sample 8 corners of cube
        float v000 = (Hash3D(xi, yi, zi, seed) & 0xFFFF) / 65536.0f;
        float v100 = (Hash3D(xi + 1, yi, zi, seed) & 0xFFFF) / 65536.0f;
        float v010 = (Hash3D(xi, yi + 1, zi, seed) & 0xFFFF) / 65536.0f;
        float v110 = (Hash3D(xi + 1, yi + 1, zi, seed) & 0xFFFF) / 65536.0f;
        float v001 = (Hash3D(xi, yi, zi + 1, seed) & 0xFFFF) / 65536.0f;
        float v101 = (Hash3D(xi + 1, yi, zi + 1, seed) & 0xFFFF) / 65536.0f;
        float v011 = (Hash3D(xi, yi + 1, zi + 1, seed) & 0xFFFF) / 65536.0f;
        float v111 = (Hash3D(xi + 1, yi + 1, zi + 1, seed) & 0xFFFF) / 65536.0f;

        // Trilinear interpolation
        float i1 = InterpolateQuintic(v000, v100, xf);
        float i2 = InterpolateQuintic(v010, v110, xf);
        float i3 = InterpolateQuintic(v001, v101, xf);
        float i4 = InterpolateQuintic(v011, v111, xf);

        float j1 = InterpolateQuintic(i1, i2, yf);
        float j2 = InterpolateQuintic(i3, i4, yf);

        return InterpolateQuintic(j1, j2, zf);
    }

    // =============================================================================
    // GRADIENT NOISE IMPLEMENTATION
    // =============================================================================

    float GradientNoise2D(float x, float y, int32 seed)
    {
        // Advanced gradient noise implementation with custom gradient vectors
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        
        float xf = x - xi;
        float yf = y - yi;
        
        // Generate gradient vectors for each corner
        uint32 hash00 = Hash2D(xi, yi, seed);
        uint32 hash10 = Hash2D(xi + 1, yi, seed);
        uint32 hash01 = Hash2D(xi, yi + 1, seed);
        uint32 hash11 = Hash2D(xi + 1, yi + 1, seed);
        
        // Convert hash to gradient vectors (using predefined gradient table approach)
        FVector2D grad00 = FVector2D(FMath::Cos(hash00 * 0.024543693f), FMath::Sin(hash00 * 0.024543693f));
        FVector2D grad10 = FVector2D(FMath::Cos(hash10 * 0.024543693f), FMath::Sin(hash10 * 0.024543693f));
        FVector2D grad01 = FVector2D(FMath::Cos(hash01 * 0.024543693f), FMath::Sin(hash01 * 0.024543693f));
        FVector2D grad11 = FVector2D(FMath::Cos(hash11 * 0.024543693f), FMath::Sin(hash11 * 0.024543693f));
        
        // Calculate dot products
        float dot00 = FVector2D::DotProduct(grad00, FVector2D(xf, yf));
        float dot10 = FVector2D::DotProduct(grad10, FVector2D(xf - 1.0f, yf));
        float dot01 = FVector2D::DotProduct(grad01, FVector2D(xf, yf - 1.0f));
        float dot11 = FVector2D::DotProduct(grad11, FVector2D(xf - 1.0f, yf - 1.0f));
        
        // Smooth interpolation
        float u = InterpolateQuintic(0.0f, 1.0f, xf);
        float v = InterpolateQuintic(0.0f, 1.0f, yf);
        
        // Bilinear interpolation
        float nx0 = FMath::Lerp(dot00, dot10, u);
        float nx1 = FMath::Lerp(dot01, dot11, u);
        
        return FMath::Lerp(nx0, nx1, v);
    }

    float GradientNoise3D(float x, float y, float z, int32 seed)
    {
        // Advanced gradient noise implementation with custom 3D gradient vectors
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);
        
        float xf = x - xi;
        float yf = y - yi;
        float zf = z - zi;
        
        // Generate gradient vectors for each corner of the cube
        uint32 hash000 = Hash3D(xi, yi, zi, seed);
        uint32 hash100 = Hash3D(xi + 1, yi, zi, seed);
        uint32 hash010 = Hash3D(xi, yi + 1, zi, seed);
        uint32 hash110 = Hash3D(xi + 1, yi + 1, zi, seed);
        uint32 hash001 = Hash3D(xi, yi, zi + 1, seed);
        uint32 hash101 = Hash3D(xi + 1, yi, zi + 1, seed);
        uint32 hash011 = Hash3D(xi, yi + 1, zi + 1, seed);
        uint32 hash111 = Hash3D(xi + 1, yi + 1, zi + 1, seed);
        
        // Convert hash to 3D gradient vectors using spherical coordinates
        auto HashToGradient3D = [](uint32 hash) -> FVector {
            float theta = (hash & 0xFF) * 0.024543693f; // 2*PI/256
            float phi = ((hash >> 8) & 0xFF) * 0.012271846f; // PI/256
            return FVector(
                FMath::Sin(phi) * FMath::Cos(theta),
                FMath::Sin(phi) * FMath::Sin(theta),
                FMath::Cos(phi)
            );
        };
        
        FVector grad000 = HashToGradient3D(hash000);
        FVector grad100 = HashToGradient3D(hash100);
        FVector grad010 = HashToGradient3D(hash010);
        FVector grad110 = HashToGradient3D(hash110);
        FVector grad001 = HashToGradient3D(hash001);
        FVector grad101 = HashToGradient3D(hash101);
        FVector grad011 = HashToGradient3D(hash011);
        FVector grad111 = HashToGradient3D(hash111);
        
        // Calculate dot products
        float dot000 = FVector::DotProduct(grad000, FVector(xf, yf, zf));
        float dot100 = FVector::DotProduct(grad100, FVector(xf - 1.0f, yf, zf));
        float dot010 = FVector::DotProduct(grad010, FVector(xf, yf - 1.0f, zf));
        float dot110 = FVector::DotProduct(grad110, FVector(xf - 1.0f, yf - 1.0f, zf));
        float dot001 = FVector::DotProduct(grad001, FVector(xf, yf, zf - 1.0f));
        float dot101 = FVector::DotProduct(grad101, FVector(xf - 1.0f, yf, zf - 1.0f));
        float dot011 = FVector::DotProduct(grad011, FVector(xf, yf - 1.0f, zf - 1.0f));
        float dot111 = FVector::DotProduct(grad111, FVector(xf - 1.0f, yf - 1.0f, zf - 1.0f));
        
        // Smooth interpolation
        float u = InterpolateQuintic(0.0f, 1.0f, xf);
        float v = InterpolateQuintic(0.0f, 1.0f, yf);
        float w = InterpolateQuintic(0.0f, 1.0f, zf);
        
        // Trilinear interpolation
        float nx00 = FMath::Lerp(dot000, dot100, u);
        float nx10 = FMath::Lerp(dot010, dot110, u);
        float nx01 = FMath::Lerp(dot001, dot101, u);
        float nx11 = FMath::Lerp(dot011, dot111, u);
        
        float ny0 = FMath::Lerp(nx00, nx10, v);
        float ny1 = FMath::Lerp(nx01, nx11, v);
        
        return FMath::Lerp(ny0, ny1, w);
    }

    // =============================================================================
    // FRACTAL AND UTILITY FUNCTIONS
    // =============================================================================

    float ApplyFractal(float BaseNoise, EAuracronPCGFractalType FractalType, const FAuracronPCGNoiseDescriptor& Descriptor)
    {
        switch (FractalType)
        {
            case EAuracronPCGFractalType::FBM:
                return BaseNoise; // Already applied in octave generation
            case EAuracronPCGFractalType::Turbulence:
                return FMath::Abs(BaseNoise);
            case EAuracronPCGFractalType::RidgedMulti:
                return 1.0f - FMath::Abs(BaseNoise);
            case EAuracronPCGFractalType::Billow:
                return FMath::Abs(BaseNoise) * 2.0f - 1.0f;
            default:
                return BaseNoise;
        }
    }

    float NormalizeNoise(float NoiseValue, float Min, float Max)
    {
        return (NoiseValue - Min) / (Max - Min);
    }

    float FractalNoise(float x, float y, float z, const FAuracronPCGNoiseDescriptor& Descriptor)
    {
        float result = 0.0f;
        float amplitude = 1.0f;
        float frequency = Descriptor.Frequency;
        float maxValue = 0.0f;

        for (int32 i = 0; i < Descriptor.Octaves; i++)
        {
            float octaveNoise = PerlinNoise3D(x * frequency, y * frequency, z * frequency, Descriptor.Seed + i);
            result += octaveNoise * amplitude;
            maxValue += amplitude;
            amplitude *= Descriptor.Persistence;
            frequency *= Descriptor.Lacunarity;
        }

        return result / maxValue;
    }

    float DomainWarp(float x, float y, float z, const FAuracronPCGNoiseDescriptor& WarpDescriptor, float Strength)
    {
        float warpX = PerlinNoise3D(x, y, z, WarpDescriptor.Seed) * Strength;
        float warpY = PerlinNoise3D(x + 1000.0f, y, z, WarpDescriptor.Seed) * Strength;
        float warpZ = PerlinNoise3D(x, y + 1000.0f, z, WarpDescriptor.Seed) * Strength;
        
        return PerlinNoise3D(x + warpX, y + warpY, z + warpZ, WarpDescriptor.Seed);
    }

    void GenerateGradientTable(TArray<FVector>& GradientTable, int32 Size, int32 Seed)
    {
        GradientTable.SetNum(Size);
        FRandomStream RandomStream(Seed);
        
        for (int32 i = 0; i < Size; i++)
        {
            float angle = RandomStream.FRand() * 2.0f * PI;
            GradientTable[i] = FVector(FMath::Cos(angle), FMath::Sin(angle), 0.0f);
        }
    }

    void GeneratePermutationTable(TArray<int32>& PermutationTable, int32 Size, int32 Seed)
    {
        PermutationTable.SetNum(Size);
        FRandomStream RandomStream(Seed);
        
        for (int32 i = 0; i < Size; i++)
        {
            PermutationTable[i] = i;
        }
        
        // Fisher-Yates shuffle
        for (int32 i = Size - 1; i > 0; i--)
        {
            int32 j = RandomStream.RandRange(0, i);
            int32 temp = PermutationTable[i];
            PermutationTable[i] = PermutationTable[j];
            PermutationTable[j] = temp;
        }
    }
}
