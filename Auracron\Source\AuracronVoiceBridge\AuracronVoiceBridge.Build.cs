// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Comunicação por Voz Bridge Build Configuration
using UnrealBuildTool;
public class AuracronVoiceBridge : ModuleRules
{
    public AuracronVoiceBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine","AudioMixer",
                "AudioExtensions",
                "SignalProcessing","OnlineSubsystemUtils","EOSShared",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "Sockets",
                "Networking",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "UnrealEd",
                "PropertyEditor",
                "KismetCompiler","BlueprintGraph",
                "Kismet",
                "ToolMenus",
                "ApplicationCore",
                "RenderCore",
                "RHI","Json","AudioEditor",
                "AudioEditor",
                "MediaAssets",
                "MediaUtils","OpusAudioDecoder","SoundFieldRendering",
                "Synthesis","MetasoundFrontend"
            }
        );
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_VOICE=1");
        PublicDefinitions.Add("WITH_VOICE_CHAT=1");
        PublicDefinitions.Add("WITH_WEBRTC=1");
        PublicDefinitions.Add("WITH_OPUS=1");
        PublicDefinitions.Add("WITH_AUDIO_CAPTURE=1");
        PublicDefinitions.Add("WITH_AUDIO_MIXER=1");
        PublicDefinitions.Add("WITH_SPATIAL_VOICE=1");
        PublicDefinitions.Add("WITH_VOICE_PROCESSING=1");
        PublicDefinitions.Add("WITH_NOISE_SUPPRESSION=1");
        PublicDefinitions.Add("WITH_ECHO_CANCELLATION=1");
        // Voice features
        PublicDefinitions.Add("AURACRON_TEAM_VOICE=1");
        PublicDefinitions.Add("AURACRON_PROXIMITY_VOICE=1");
        PublicDefinitions.Add("AURACRON_3D_VOICE=1");
        PublicDefinitions.Add("AURACRON_VOICE_EFFECTS=1");
        PublicDefinitions.Add("AURACRON_VOICE_MODULATION=1");
        PublicDefinitions.Add("AURACRON_VOICE_RECORDING=1");
        PublicDefinitions.Add("AURACRON_VOICE_PLAYBACK=1");
        PublicDefinitions.Add("AURACRON_VOICE_FILTERING=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_VOICE=1");
            PublicDefinitions.Add("AURACRON_COMPRESSED_VOICE=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_VOICE=0");
            PublicDefinitions.Add("AURACRON_COMPRESSED_VOICE=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_VOICE_DEBUG=1");
            PublicDefinitions.Add("AURACRON_VOICE_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_VOICE_DEBUG=0");
            PublicDefinitions.Add("AURACRON_VOICE_PROFILING=0");
        }
        // Privacy and security
        PublicDefinitions.Add("AURACRON_VOICE_ENCRYPTION=1");
        PublicDefinitions.Add("AURACRON_VOICE_PRIVACY=1");
        PublicDefinitions.Add("AURACRON_VOICE_MODERATION=1");
        PublicDefinitions.Add("AURACRON_VOICE_RECORDING_CONSENT=1");
    }
}
