﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de FÃ­sica Chaos Bridge
// IntegraÃ§Ã£o C++ para fÃ­sica usando Chaos Physics e APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "ModularGameplay/Public/GameFrameworkComponent.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "PhysicsEngine/BodyInstance.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Chaos/ChaosSolverActor.h"
#include "FieldSystem/FieldSystemComponent.h"
#include "FieldSystem/FieldSystemActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "AuracronPhysicsBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de fÃ­sica
 */
UENUM(BlueprintType)
enum class EAuracronPhysicsType : uint8
{
    None                UMETA(DisplayName = "None"),
    RigidBody           UMETA(DisplayName = "Rigid Body"),
    SoftBody            UMETA(DisplayName = "Soft Body"),
    Fluid               UMETA(DisplayName = "Fluid"),
    Cloth               UMETA(DisplayName = "Cloth"),
    Destruction         UMETA(DisplayName = "Destruction"),
    FieldSystem         UMETA(DisplayName = "Field System"),
    Constraint          UMETA(DisplayName = "Constraint"),
    Vehicle             UMETA(DisplayName = "Vehicle"),
    Character           UMETA(DisplayName = "Character")
};

/**
 * EnumeraÃ§Ã£o para tipos de destruiÃ§Ã£o
 */
UENUM(BlueprintType)
enum class EAuracronDestructionType : uint8
{
    None                UMETA(DisplayName = "None"),
    Fracture            UMETA(DisplayName = "Fracture"),
    Explosion           UMETA(DisplayName = "Explosion"),
    Slice               UMETA(DisplayName = "Slice"),
    Crumble             UMETA(DisplayName = "Crumble"),
    Shatter             UMETA(DisplayName = "Shatter"),
    Melt                UMETA(DisplayName = "Melt"),
    Dissolve            UMETA(DisplayName = "Dissolve"),
    Vaporize            UMETA(DisplayName = "Vaporize")
};

/**
 * Estrutura para configuraÃ§Ã£o de fÃ­sica Chaos
 */
USTRUCT(BlueprintType)
struct AURACRONPHYSICSBRIDGE_API FAuracronChaosPhysicsConfiguration
{
    GENERATED_BODY()

    /** Usar simulaÃ§Ã£o de fÃ­sica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration")
    bool bUsePhysicsSimulation = true;

    /** Gravidade customizada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration")
    FVector CustomGravity = FVector(0.0f, 0.0f, -980.0f);

    /** Usar gravidade customizada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration")
    bool bUseCustomGravity = false;

    /** Densidade do ar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float AirDensity = 1.225f;

    /** ResistÃªncia do ar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AirResistance = 0.1f;

    /** Usar sub-stepping */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration")
    bool bUseSubStepping = true;

    /** NÃºmero de sub-steps */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "1", ClampMax = "10"))
    int32 SubSteps = 4;

    /** Delta time mÃ¡ximo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "0.001", ClampMax = "0.1"))
    float MaxDeltaTime = 0.033f;

    /** Usar CCD (Continuous Collision Detection) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration")
    bool bUseCCD = true;

    /** Threshold para CCD */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float CCDThreshold = 1.0f;

    /** Usar async physics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration")
    bool bUseAsyncPhysics = true;

    /** NÃºmero de threads de fÃ­sica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "1", ClampMax = "16"))
    int32 PhysicsThreads = 4;

    /** Usar deterministic physics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration")
    bool bUseDeterministicPhysics = true;

    /** Solver iterations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "1", ClampMax = "20"))
    int32 SolverIterations = 8;

    /** Collision iterations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chaos Physics Configuration", meta = (ClampMin = "1", ClampMax = "10"))
    int32 CollisionIterations = 4;
};

/**
 * Estrutura para configuraÃ§Ã£o de destruiÃ§Ã£o
 */
USTRUCT(BlueprintType)
struct AURACRONPHYSICSBRIDGE_API FAuracronDestructionConfiguration
{
    GENERATED_BODY()

    /** Tipo de destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    EAuracronDestructionType DestructionType = EAuracronDestructionType::Fracture;

    /** ForÃ§a da destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float DestructionForce = 1000.0f;

    /** Raio da destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration", meta = (ClampMin = "0.0", ClampMax = "5000.0"))
    float DestructionRadius = 500.0f;

    /** Threshold de dano para destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float DamageThreshold = 100.0f;

    /** NÃºmero mÃ¡ximo de fragmentos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration", meta = (ClampMin = "1", ClampMax = "1000"))
    int32 MaxFragments = 100;

    /** Tamanho mÃ­nimo de fragmento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration", meta = (ClampMin = "1.0", ClampMax = "100.0"))
    float MinFragmentSize = 10.0f;

    /** Usar debris */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    bool bUseDebris = true;

    /** Tempo de vida dos fragmentos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration", meta = (ClampMin = "1.0", ClampMax = "60.0"))
    float FragmentLifetime = 10.0f;

    /** Usar fade out */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    bool bUseFadeOut = true;

    /** Tempo de fade out */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration", meta = (ClampMin = "0.5", ClampMax = "10.0"))
    float FadeOutTime = 3.0f;

    /** Material de fragmento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    TSoftObjectPtr<UMaterialInterface> FragmentMaterial;

    /** Usar som de destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    bool bUseDestructionSound = true;

    /** Som de destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    TSoftObjectPtr<USoundBase> DestructionSound;

    /** Usar efeitos de partÃ­culas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    bool bUseParticleEffects = true;

    /** Efeito de partÃ­culas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destruction Configuration")
    TSoftObjectPtr<UNiagaraSystem> DestructionParticles;
};

/**
 * Estrutura para configuraÃ§Ã£o de Field System
 */
USTRUCT(BlueprintType)
struct AURACRONPHYSICSBRIDGE_API FAuracronFieldSystemConfiguration
{
    GENERATED_BODY()

    /** Tipo de campo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    FString FieldType = TEXT("RadialForce");

    /** ForÃ§a do campo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float FieldForce = 1000.0f;

    /** Raio do campo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration", meta = (ClampMin = "0.0", ClampMax = "5000.0"))
    float FieldRadius = 500.0f;

    /** DuraÃ§Ã£o do campo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration", meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float FieldDuration = 2.0f;

    /** Usar falloff */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    bool bUseFalloff = true;

    /** Tipo de falloff */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    FString FalloffType = TEXT("Linear");

    /** Afetar apenas objetos destrutÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    bool bAffectDestructibleOnly = false;

    /** Afetar fÃ­sica de personagens */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    bool bAffectCharacterPhysics = true;

    /** Multiplicador de forÃ§a para personagens */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration", meta = (ClampMin = "0.0", ClampMax = "5.0"))
    float CharacterForceMultiplier = 0.5f;

    /** Usar direÃ§Ã£o customizada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    bool bUseCustomDirection = false;

    /** DireÃ§Ã£o customizada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    FVector CustomDirection = FVector::UpVector;

    /** Usar noise */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration")
    bool bUseNoise = false;

    /** Intensidade do noise */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float NoiseIntensity = 0.1f;

    /** FrequÃªncia do noise */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field System Configuration", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float NoiseFrequency = 1.0f;
};

/**
 * Classe principal do Bridge para Sistema de FÃ­sica Chaos
 * ResponsÃ¡vel pelo gerenciamento completo de fÃ­sica e destruiÃ§Ã£o
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Physics", meta = (DisplayName = "AURACRON Physics Bridge", BlueprintSpawnableComponent))
class AURACRONPHYSICSBRIDGE_API UAuracronPhysicsBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronPhysicsBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Physics Management ===

    /**
     * Aplicar forÃ§a a objeto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Forces", CallInEditor)
    bool ApplyForceToObject(AActor* TargetActor, const FVector& Force, const FVector& Location = FVector::ZeroVector);

    /**
     * Aplicar impulso a objeto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Forces", CallInEditor)
    bool ApplyImpulseToObject(AActor* TargetActor, const FVector& Impulse, const FVector& Location = FVector::ZeroVector);

    /**
     * Aplicar torque a objeto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Forces", CallInEditor)
    bool ApplyTorqueToObject(AActor* TargetActor, const FVector& Torque);

    /**
     * Definir gravidade customizada
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Gravity", CallInEditor)
    bool SetCustomGravity(const FVector& NewGravity);

    /**
     * Aplicar gravidade a objeto especÃ­fico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Gravity", CallInEditor)
    bool ApplyCustomGravityToObject(AActor* TargetActor, const FVector& Gravity);

    // === Destruction System ===

    /**
     * Destruir objeto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Destruction", CallInEditor)
    bool DestroyObject(AActor* TargetActor, const FAuracronDestructionConfiguration& DestructionConfig);

    /**
     * Criar explosÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Destruction", CallInEditor)
    bool CreateExplosion(const FVector& Location, const FAuracronDestructionConfiguration& DestructionConfig);

    /**
     * Fraturar objeto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Destruction", CallInEditor)
    bool FractureObject(AActor* TargetActor, const FVector& ImpactLocation, float Force);

    /**
     * Converter para Geometry Collection
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Destruction", CallInEditor)
    AGeometryCollectionActor* ConvertToGeometryCollection(AActor* TargetActor);

    // === Field System ===

    /**
     * Aplicar Field System
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|FieldSystem", CallInEditor)
    bool ApplyFieldSystem(const FVector& Location, const FAuracronFieldSystemConfiguration& FieldConfig);

    /**
     * Criar campo de forÃ§a radial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|FieldSystem", CallInEditor)
    bool CreateRadialForceField(const FVector& Location, float Force, float Radius, float Duration = 2.0f);

    /**
     * Criar campo de forÃ§a direcional
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|FieldSystem", CallInEditor)
    bool CreateDirectionalForceField(const FVector& Location, const FVector& Direction, float Force, float Radius, float Duration = 2.0f);

    /**
     * Criar campo de vÃ³rtice
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|FieldSystem", CallInEditor)
    bool CreateVortexField(const FVector& Location, const FVector& Axis, float Force, float Radius, float Duration = 2.0f);

    // === Realm Physics ===

    /**
     * Configurar fÃ­sica para realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Realm", CallInEditor)
    bool ConfigureRealmPhysics(int32 RealmIndex);

    /**
     * Aplicar efeitos de realm a objeto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Realm", CallInEditor)
    bool ApplyRealmPhysicsToObject(AActor* TargetActor, int32 RealmIndex);

    /**
     * Criar zona de fÃ­sica especial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Realm", CallInEditor)
    bool CreateSpecialPhysicsZone(const FVector& Location, float Radius, const FString& ZoneType);

    // === Performance Management ===

    /**
     * Otimizar fÃ­sica por distÃ¢ncia
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Performance", CallInEditor)
    bool OptimizePhysicsByDistance(const FVector& ViewerLocation);

    /**
     * Limpar objetos fÃ­sicos inativos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Performance", CallInEditor)
    bool CleanupInactivePhysicsObjects();

    /**
     * Definir qualidade de fÃ­sica
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Physics|Performance", CallInEditor)
    bool SetPhysicsQuality(int32 QualityLevel);

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de fÃ­sica */
    bool InitializePhysicsSystem();
    
    /** Configurar Chaos Solver */
    bool SetupChaosSolver();
    
    /** Configurar Field System */
    bool SetupFieldSystem();
    
    /** Processar fÃ­sica ativa */
    void ProcessActivePhysics(float DeltaTime);
    
    /** Validar configuraÃ§Ã£o de destruiÃ§Ã£o */
    bool ValidateDestructionConfiguration(const FAuracronDestructionConfiguration& Config) const;
    
    /** Validar configuraÃ§Ã£o de Field System */
    bool ValidateFieldSystemConfiguration(const FAuracronFieldSystemConfiguration& Config) const;

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ã£o de fÃ­sica Chaos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    FAuracronChaosPhysicsConfiguration ChaosPhysicsConfiguration;

    /** ConfiguraÃ§Ã£o padrÃ£o de destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronDestructionConfiguration DefaultDestructionConfiguration;

    /** ConfiguraÃ§Ã£o padrÃ£o de Field System */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronFieldSystemConfiguration DefaultFieldSystemConfiguration;

    /** Objetos fÃ­sicos ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<TObjectPtr<AActor>> ActivePhysicsObjects;

    /** Componentes de Field System ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<TObjectPtr<UFieldSystemComponent>> ActiveFieldComponents;

    /** ReferÃªncia ao Chaos Solver */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<AChaosSolverActor> ChaosSolver;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para limpeza */
    FTimerHandle CleanupTimer;
    
    /** Timer para otimizaÃ§Ã£o */
    FTimerHandle OptimizationTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection PhysicsMutex;

public:
    // === Delegates ===
    
    /** Delegate chamado quando objeto Ã© destruÃ­do */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnObjectDestroyed, AActor*, DestroyedActor, FAuracronDestructionConfiguration, DestructionConfig);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Physics|Events")
    FOnObjectDestroyed OnObjectDestroyed;
    
    /** Delegate chamado quando Field System Ã© aplicado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFieldSystemApplied, FVector, Location, FAuracronFieldSystemConfiguration, FieldConfig);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Physics|Events")
    FOnFieldSystemApplied OnFieldSystemApplied;
};

