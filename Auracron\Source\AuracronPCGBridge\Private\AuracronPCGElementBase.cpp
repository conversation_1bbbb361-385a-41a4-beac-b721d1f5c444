// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Base Element Class Implementation
// Bridge 2.1: PCG Framework - Core Infrastructure

#include "AuracronPCGElementBase.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "Data/PCGPointData.h"
#include "Metadata/PCGMetadata.h"
#include "Metadata/PCGMetadataAccessor.h"

// Engine includes
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"

// UAuracronPCGSettingsBase implementation

UAuracronPCGSettingsBase::UAuracronPCGSettingsBase()
{
    bEnabled = true;
    bDebugMode = false;
    ElementName = TEXT("AuracronPCGElement");
    ElementDescription = TEXT("Custom AURACRON PCG Element");
    bEnableMultithreading = true;
    BatchSize = 1000;
    TimeoutSeconds = 30.0f;
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::InputPinProperties() const
{
    TArray<FPCGPinProperties> Properties = Super::InputPinProperties();
    
    // Add custom input pins
    TArray<FPCGPinProperties> CustomProperties = GetCustomInputPinProperties();
    Properties.Append(CustomProperties);
    
    return Properties;
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::OutputPinProperties() const
{
    TArray<FPCGPinProperties> Properties = Super::OutputPinProperties();
    
    // Add custom output pins
    TArray<FPCGPinProperties> CustomProperties = GetCustomOutputPinProperties();
    Properties.Append(CustomProperties);
    
    return Properties;
}

FPCGElementPtr UAuracronPCGSettingsBase::CreateElement() const
{
    if (!bEnabled)
    {
        return nullptr;
    }
    
    return CreateCustomElement();
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::GetCustomInputPinProperties() const
{
    // Default implementation - override in derived classes
    TArray<FPCGPinProperties> Properties;
    
    // Add default input pin for spatial data
    FPCGPinProperties& InputPin = Properties.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
    
    return Properties;
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::GetCustomOutputPinProperties() const
{
    // Default implementation - override in derived classes
    TArray<FPCGPinProperties> Properties;
    
    // Add default output pin for point data
    FPCGPinProperties& OutputPin = Properties.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
    
    return Properties;
}

FPCGElementPtr UAuracronPCGSettingsBase::CreateCustomElement() const
{
    // Default implementation - override in derived classes
    return MakeShared<FAuracronPCGElementBase>();
}

#if WITH_EDITOR
FText UAuracronPCGSettingsBase::GetDefaultNodeTitle() const
{
    return FText::FromString(ElementName);
}

FText UAuracronPCGSettingsBase::GetNodeTooltipText() const
{
    return FText::FromString(ElementDescription);
}

FLinearColor UAuracronPCGSettingsBase::GetNodeTitleColor() const
{
    return FLinearColor(0.2f, 0.7f, 1.0f); // AURACRON blue
}
#endif

// FAuracronPCGElementBase implementation

FAuracronPCGElementBase::FAuracronPCGElementBase()
{
    PerformanceStartTime = 0.0;
}

FPCGContext* FAuracronPCGElementBase::Initialize(const FPCGDataCollection& InputData, TWeakObjectPtr<UPCGComponent> SourceComponent, const UPCGNode* Node)
{
    FPCGContext* Context = new FPCGContext();
    Context->InputData = InputData;
    Context->SourceComponent = SourceComponent;
    Context->Node = Node;
    
    // Log initialization
    LogExecutionStart(Context);
    
    return Context;
}

bool FAuracronPCGElementBase::Execute(FPCGContext* Context) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);
    
    if (!Context)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Execute called with null context"));
        return false;
    }

    // Start performance monitoring
    StartPerformanceTimer();

    bool bSuccess = false;
    FAuracronPCGElementResult Result;

    try
    {
        // Execute the element
        bSuccess = ExecuteInternal(Context);
        
        Result.bSuccess = bSuccess;
        Result.ExecutionTimeSeconds = EndPerformanceTimer();
        Result.OutputDataCount = Context->OutputData.GetInputs().Num();
    }
    catch (const std::exception& e)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        Result.ExecutionTimeSeconds = EndPerformanceTimer();
        
        ReportError(Context, Result.ErrorMessage);
        bSuccess = false;
    }

    // Log execution end
    LogExecutionEnd(Context, Result);

    return bSuccess;
}

bool FAuracronPCGElementBase::CanExecuteOnlyOnMainThread(FPCGContext* Context) const
{
    // Check if the settings allow multithreading
    if (Context && Context->Node)
    {
        if (const UAuracronPCGSettingsBase* Settings = Cast<UAuracronPCGSettingsBase>(Context->Node->GetSettings()))
        {
            return !Settings->bEnableMultithreading;
        }
    }
    
    // Default to allowing background thread execution
    return false;
}

bool FAuracronPCGElementBase::IsCacheable(const UPCGSettings* InSettings) const
{
    // Most AURACRON elements are cacheable by default
    return true;
}

bool FAuracronPCGElementBase::ExecuteInternal(FPCGContext* Context) const
{
    if (!Context)
    {
        return false;
    }

    // Validate input data
    TArray<FString> ValidationErrors;
    if (!ValidateInputData(Context->InputData, ValidationErrors))
    {
        for (const FString& Error : ValidationErrors)
        {
            ReportError(Context, Error);
        }
        return false;
    }

    // Extract parameters
    FAuracronPCGElementParams Parameters = ExtractParametersFromContext(Context);

    // Process the data
    FAuracronPCGElementResult Result = ProcessData(Context->InputData, Context->OutputData, Parameters);

    if (!Result.bSuccess)
    {
        ReportError(Context, Result.ErrorMessage);
        return false;
    }

    return true;
}

FAuracronPCGElementResult FAuracronPCGElementBase::ProcessData(const FPCGDataCollection& InputData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronPCGElementBase::ProcessData);

    FAuracronPCGElementResult Result;
    Result.bSuccess = true;
    Result.ProcessingTimeMS = 0.0f;
    Result.OutputDataCount = 0;

    // Real PCG data processing implementation
    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Process each input data based on its type
        for (const FPCGTaggedData& InputTaggedData : InputData.GetInputs())
        {
            if (!InputTaggedData.Data)
            {
                continue;
            }

            // Process based on data type
            if (const UPCGPointData* PointData = Cast<UPCGPointData>(InputTaggedData.Data))
            {
                FAuracronPCGElementResult PointResult = ProcessPointData(PointData, OutputData, Parameters, InputTaggedData.Tags);
                if (!PointResult.bSuccess)
                {
                    Result.bSuccess = false;
                    Result.ErrorMessage = PointResult.ErrorMessage;
                    return Result;
                }
                Result.OutputDataCount += PointResult.OutputDataCount;
            }
            else if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(InputTaggedData.Data))
            {
                FAuracronPCGElementResult SpatialResult = ProcessSpatialData(SpatialData, OutputData, Parameters, InputTaggedData.Tags);
                if (!SpatialResult.bSuccess)
                {
                    Result.bSuccess = false;
                    Result.ErrorMessage = SpatialResult.ErrorMessage;
                    return Result;
                }
                Result.OutputDataCount += SpatialResult.OutputDataCount;
            }
            else if (const UPCGParamData* ParamData = Cast<UPCGParamData>(InputTaggedData.Data))
            {
                FAuracronPCGElementResult ParamResult = ProcessParameterData(ParamData, OutputData, Parameters, InputTaggedData.Tags);
                if (!ParamResult.bSuccess)
                {
                    Result.bSuccess = false;
                    Result.ErrorMessage = ParamResult.ErrorMessage;
                    return Result;
                }
                Result.OutputDataCount += ParamResult.OutputDataCount;
            }
            else
            {
                // Handle unknown data types by passing through
                FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
                OutputTaggedData = InputTaggedData;
                Result.OutputDataCount++;
            }
        }

        // Apply post-processing if needed
        if (Parameters.bApplyPostProcessing)
        {
            ApplyPostProcessing(OutputData, Parameters);
        }

        // Calculate processing time
        double EndTime = FPlatformTime::Seconds();
        Result.ProcessingTimeMS = static_cast<float>((EndTime - StartTime) * 1000.0);

        Result.ErrorMessage = FString::Printf(TEXT("Processed %d inputs to %d outputs in %.2fms"),
                                            InputData.GetInputs().Num(), Result.OutputDataCount, Result.ProcessingTimeMS);

        UE_LOG(LogAuracronPCGElements, VeryVerbose, TEXT("PCG Element processed: %s"), *Result.ErrorMessage);
    }
    catch (const std::exception& Exception)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Exception during PCG processing: %s"), UTF8_TO_TCHAR(Exception.what()));
        UE_LOG(LogAuracronPCGElements, Error, TEXT("%s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGElementBase::ValidateInputData(const FPCGDataCollection& InputData, TArray<FString>& ValidationErrors) const
{
    ValidationErrors.Empty();

    // Basic validation - check if we have any input data
    if (InputData.GetInputs().Num() == 0)
    {
        ValidationErrors.Add(TEXT("No input data provided"));
        return false;
    }

    // Validate each input
    for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
    {
        if (!TaggedData.Data)
        {
            ValidationErrors.Add(TEXT("Input contains null data"));
        }
    }

    return ValidationErrors.Num() == 0;
}

void FAuracronPCGElementBase::LogExecutionStart(FPCGContext* Context) const
{
    if (Context && Context->Node)
    {
        FString NodeName = Context->Node->GetNodeTitle().ToString();
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Starting execution of element: %s"), *NodeName);
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Starting execution of unnamed element"));
    }
}

void FAuracronPCGElementBase::LogExecutionEnd(FPCGContext* Context, const FAuracronPCGElementResult& Result) const
{
    if (Context && Context->Node)
    {
        FString NodeName = Context->Node->GetNodeTitle().ToString();
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Completed execution of element: %s (Success: %s, Time: %.3fs, Outputs: %d)"), 
                                  *NodeName, 
                                  Result.bSuccess ? TEXT("Yes") : TEXT("No"),
                                  Result.ExecutionTimeSeconds,
                                  Result.OutputDataCount);
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Completed execution of unnamed element (Success: %s, Time: %.3fs)"), 
                                  Result.bSuccess ? TEXT("Yes") : TEXT("No"),
                                  Result.ExecutionTimeSeconds);
    }
}

void FAuracronPCGElementBase::ReportError(FPCGContext* Context, const FString& ErrorMessage) const
{
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Element error: %s"), *ErrorMessage);
    
    // Report to framework error system
    if (FAuracronPCGFrameworkModule::IsAvailable())
    {
        FString ElementName = Context && Context->Node ? Context->Node->GetNodeTitle().ToString() : TEXT("Unknown");
        
        FAuracronPCGErrorInfo ErrorInfo(EAuracronPCGErrorCode::InvalidElement, ErrorMessage, 
                                       FString::Printf(TEXT("Element: %s"), *ElementName));
        
        FAuracronPCGFrameworkModule::Get().ReportError(ErrorInfo);
    }
}

void FAuracronPCGElementBase::ReportWarning(FPCGContext* Context, const FString& WarningMessage) const
{
    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Element warning: %s"), *WarningMessage);
}

UPCGPointData* FAuracronPCGElementBase::CreateOutputPointData(FPCGContext* Context) const
{
    UPCGPointData* PointData = NewObject<UPCGPointData>();
    PointData->InitializeFromData(nullptr);
    
    return PointData;
}

bool FAuracronPCGElementBase::ProcessPointData(const UPCGPointData* InputPointData, UPCGPointData* OutputPointData, const FAuracronPCGElementParams& Parameters) const
{
    if (!InputPointData || !OutputPointData)
    {
        return false;
    }

    // Copy points from input to output
    const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
    TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();
    
    OutputPoints = InputPoints;

    // Copy metadata
    if (InputPointData->Metadata)
    {
        OutputPointData->Metadata = InputPointData->Metadata->Copy();
    }

    return true;
}

bool FAuracronPCGElementBase::ProcessSpatialData(const UPCGSpatialData* InputSpatialData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters) const
{
    if (!InputSpatialData)
    {
        return false;
    }

    // Convert spatial data to point data
    UPCGPointData* PointData = InputSpatialData->ToPointData();
    if (PointData)
    {
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = PointData;
        TaggedData.Pin = TEXT("Output");
        
        return true;
    }

    return false;
}

FAuracronPCGElementParams FAuracronPCGElementBase::ExtractParameters(const UPCGSettings* Settings) const
{
    if (const UAuracronPCGSettingsBase* AuracronSettings = Cast<UAuracronPCGSettingsBase>(Settings))
    {
        return AuracronSettings->ElementParameters;
    }
    
    return FAuracronPCGElementParams();
}

FAuracronPCGElementParams FAuracronPCGElementBase::ExtractParametersFromContext(FPCGContext* Context) const
{
    if (Context && Context->Node)
    {
        return ExtractParameters(Context->Node->GetSettings());
    }
    
    return FAuracronPCGElementParams();
}

void FAuracronPCGElementBase::StartPerformanceTimer() const
{
    PerformanceStartTime = FPlatformTime::Seconds();
}

float FAuracronPCGElementBase::EndPerformanceTimer() const
{
    return static_cast<float>(FPlatformTime::Seconds() - PerformanceStartTime);
}

// AuracronPCGUtils namespace implementation

namespace AuracronPCGUtils
{
    UPCGPointData* CreatePointDataFromBounds(const FBox& Bounds, float PointDensity)
    {
        UPCGPointData* PointData = NewObject<UPCGPointData>();
        PointData->InitializeFromData(nullptr);

        TArray<FPCGPoint>& Points = PointData->GetMutablePoints();

        // Calculate grid dimensions based on density
        FVector BoundsSize = Bounds.GetSize();
        float GridSpacing = 1.0f / FMath::Sqrt(PointDensity);

        int32 GridX = FMath::Max(1, FMath::CeilToInt(BoundsSize.X / GridSpacing));
        int32 GridY = FMath::Max(1, FMath::CeilToInt(BoundsSize.Y / GridSpacing));
        int32 GridZ = FMath::Max(1, FMath::CeilToInt(BoundsSize.Z / GridSpacing));

        // Generate points in a grid pattern
        for (int32 X = 0; X < GridX; ++X)
        {
            for (int32 Y = 0; Y < GridY; ++Y)
            {
                for (int32 Z = 0; Z < GridZ; ++Z)
                {
                    FVector Position = Bounds.Min + FVector(
                        (X + 0.5f) * GridSpacing,
                        (Y + 0.5f) * GridSpacing,
                        (Z + 0.5f) * GridSpacing
                    );

                    if (Bounds.IsInside(Position))
                    {
                        FPCGPoint& Point = Points.Emplace_GetRef();
                        Point.Transform.SetLocation(Position);
                        Point.SetLocalBounds(FBox(FVector(-GridSpacing * 0.5f), FVector(GridSpacing * 0.5f)));
                        Point.Density = PointDensity;
                    }
                }
            }
        }

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created %d points from bounds (Size: %s, Density: %.3f)"),
                                  Points.Num(), *BoundsSize.ToString(), PointDensity);

        return PointData;
    }

    bool CopyPointAttributes(const UPCGPointData* Source, UPCGPointData* Target)
    {
        if (!Source || !Target)
        {
            return false;
        }

        // Copy metadata if it exists
        if (Source->Metadata)
        {
            Target->Metadata = Source->Metadata->Copy();
        }

        // Copy points
        const TArray<FPCGPoint>& SourcePoints = Source->GetPoints();
        TArray<FPCGPoint>& TargetPoints = Target->GetMutablePoints();

        TargetPoints = SourcePoints;

        return true;
    }

    TArray<FPCGPoint> FilterPointsByAttribute(const TArray<FPCGPoint>& Points, const FString& AttributeName, const FString& AttributeValue)
    {
        TArray<FPCGPoint> FilteredPoints;

        for (const FPCGPoint& Point : Points)
        {
            // Check if point has the specified attribute with the specified value
            bool bShouldIncludePoint = false;
            
            // Try to get the attribute value from point metadata
            if (const UPCGMetadata* Metadata = Point.MetadataEntry != PCGInvalidEntryKey ? Point.Metadata.Get() : nullptr)
            {
                // Check if attribute exists in metadata
                if (const FPCGMetadataAttributeBase* Attribute = Metadata->GetConstAttribute(FName(*AttributeName)))
                {
                    // Get attribute value based on type
                    if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<FString>::Id)
                    {
                        FString PointAttributeValue;
                        if (Metadata->GetStringAttribute(Point.MetadataEntry, FName(*AttributeName), PointAttributeValue))
                        {
                            bShouldIncludePoint = (PointAttributeValue == AttributeValue);
                        }
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<FName>::Id)
                    {
                        FName PointAttributeValue;
                        if (Metadata->GetNameAttribute(Point.MetadataEntry, FName(*AttributeName), PointAttributeValue))
                        {
                            bShouldIncludePoint = (PointAttributeValue.ToString() == AttributeValue);
                        }
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<int32>::Id)
                    {
                        int32 PointAttributeValue;
                        if (Metadata->GetIntAttribute(Point.MetadataEntry, FName(*AttributeName), PointAttributeValue))
                        {
                            int32 CompareValue = FCString::Atoi(*AttributeValue);
                            bShouldIncludePoint = (PointAttributeValue == CompareValue);
                        }
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
                    {
                        float PointAttributeValue;
                        if (Metadata->GetFloatAttribute(Point.MetadataEntry, FName(*AttributeName), PointAttributeValue))
                        {
                            float CompareValue = FCString::Atof(*AttributeValue);
                            bShouldIncludePoint = FMath::IsNearlyEqual(PointAttributeValue, CompareValue, KINDA_SMALL_NUMBER);
                        }
                    }
                    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<bool>::Id)
                    {
                        bool PointAttributeValue;
                        if (Metadata->GetBoolAttribute(Point.MetadataEntry, FName(*AttributeName), PointAttributeValue))
                        {
                            bool CompareValue = AttributeValue.ToBool();
                            bShouldIncludePoint = (PointAttributeValue == CompareValue);
                        }
                    }
                }
            }
            
            // If no metadata or attribute not found, check if we should include based on default behavior
            if (!bShouldIncludePoint && AttributeValue.IsEmpty())
            {
                // Include points without the attribute if we're filtering for empty values
                bShouldIncludePoint = true;
            }
            
            if (bShouldIncludePoint)
            {
                FilteredPoints.Add(Point);
            }
        }

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Filtered %d points by attribute '%s' = '%s' (Result: %d points)"),
                                  Points.Num(), *AttributeName, *AttributeValue, FilteredPoints.Num());

        return FilteredPoints;
    }

    void TransformPoints(TArray<FPCGPoint>& Points, const FTransform& Transform)
    {
        for (FPCGPoint& Point : Points)
        {
            Point.Transform = Point.Transform * Transform;
        }

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Transformed %d points"), Points.Num());
    }

    FBox CalculatePointsBounds(const TArray<FPCGPoint>& Points)
    {
        if (Points.Num() == 0)
        {
            return FBox(ForceInit);
        }

        FBox Bounds(ForceInit);

        for (const FPCGPoint& Point : Points)
        {
            FVector Location = Point.Transform.GetLocation();
            Bounds += Location;

            // Also include the point's local bounds
            FBox LocalBounds = Point.GetLocalBounds();
            LocalBounds = LocalBounds.TransformBy(Point.Transform);
            Bounds += LocalBounds;
        }

        return Bounds;
    }
}

// === PCG Data Processing Helper Functions ===

FAuracronPCGElementResult FAuracronPCGElementBase::ProcessPointData(const UPCGPointData* PointData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters, const TSet<FString>& Tags) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronPCGElementBase::ProcessPointData);

    FAuracronPCGElementResult Result;
    Result.bSuccess = true;
    Result.OutputDataCount = 0;

    if (!PointData)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Invalid point data input");
        return Result;
    }

    // Create output point data
    UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
    TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

    const TArray<FPCGPoint>& InputPoints = PointData->GetPoints();

    // Process each point based on parameters
    for (const FPCGPoint& InputPoint : InputPoints)
    {
        FPCGPoint ProcessedPoint = InputPoint;

        // Apply transformations
        if (Parameters.bApplyTransformation)
        {
            ProcessedPoint.Transform = ProcessedPoint.Transform * Parameters.TransformationMatrix;
        }

        // Apply density modifications
        if (Parameters.bModifyDensity)
        {
            ProcessedPoint.Density = FMath::Clamp(ProcessedPoint.Density * Parameters.DensityMultiplier, 0.0f, 1.0f);
        }

        // Apply attribute modifications
        if (Parameters.bModifyAttributes && PointData->Metadata)
        {
            ApplyAttributeModifications(ProcessedPoint, PointData->Metadata, Parameters);
        }

        // Filter points based on criteria
        if (ShouldIncludePoint(ProcessedPoint, Parameters))
        {
            OutputPoints.Add(ProcessedPoint);
        }
    }

    // Copy metadata if present
    if (PointData->Metadata)
    {
        OutputPointData->Metadata = DuplicateObject<UPCGMetadata>(PointData->Metadata, OutputPointData);
    }

    // Add to output collection
    FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
    OutputTaggedData.Data = OutputPointData;
    OutputTaggedData.Tags = Tags;

    Result.OutputDataCount = 1;
    Result.ErrorMessage = FString::Printf(TEXT("Processed %d points to %d points"), InputPoints.Num(), OutputPoints.Num());

    return Result;
}

FAuracronPCGElementResult FAuracronPCGElementBase::ProcessSpatialData(const UPCGSpatialData* SpatialData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters, const TSet<FString>& Tags) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronPCGElementBase::ProcessSpatialData);

    FAuracronPCGElementResult Result;
    Result.bSuccess = true;
    Result.OutputDataCount = 0;

    if (!SpatialData)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Invalid spatial data input");
        return Result;
    }

    // Convert spatial data to point data for processing
    const UPCGPointData* PointData = SpatialData->ToPointData();
    if (PointData)
    {
        return ProcessPointData(PointData, OutputData, Parameters, Tags);
    }
    else
    {
        // Pass through spatial data unchanged
        FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
        OutputTaggedData.Data = const_cast<UPCGSpatialData*>(SpatialData);
        OutputTaggedData.Tags = Tags;

        Result.OutputDataCount = 1;
        Result.ErrorMessage = TEXT("Spatial data passed through unchanged");
    }

    return Result;
}

FAuracronPCGElementResult FAuracronPCGElementBase::ProcessParameterData(const UPCGParamData* ParamData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters, const TSet<FString>& Tags) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronPCGElementBase::ProcessParameterData);

    FAuracronPCGElementResult Result;
    Result.bSuccess = true;
    Result.OutputDataCount = 0;

    if (!ParamData)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Invalid parameter data input");
        return Result;
    }

    // Create modified parameter data
    UPCGParamData* OutputParamData = NewObject<UPCGParamData>();

    // Copy and modify parameters based on processing parameters
    if (ParamData->Metadata)
    {
        OutputParamData->Metadata = DuplicateObject<UPCGMetadata>(ParamData->Metadata, OutputParamData);

        // Apply parameter modifications if specified
        if (Parameters.bModifyParameters)
        {
            ModifyParameterMetadata(OutputParamData->Metadata, Parameters);
        }
    }

    // Add to output collection
    FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
    OutputTaggedData.Data = OutputParamData;
    OutputTaggedData.Tags = Tags;

    Result.OutputDataCount = 1;
    Result.ErrorMessage = TEXT("Parameter data processed successfully");

    return Result;
}
