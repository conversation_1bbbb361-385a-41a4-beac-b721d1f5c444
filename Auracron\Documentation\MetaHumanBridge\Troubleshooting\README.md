# MetaHuman Bridge Troubleshooting Guide

**Solutions for common issues and problems with the AURACRON MetaHuman Bridge**

## Table of Contents

1. [Installation Issues](#installation-issues)
2. [DNA Loading Problems](#dna-loading-problems)
3. [Performance Issues](#performance-issues)
4. [Python Binding Issues](#python-binding-issues)
5. [Memory Problems](#memory-problems)
6. [Platform-Specific Issues](#platform-specific-issues)
7. [Error Code Reference](#error-code-reference)

## Installation Issues

### Problem: Bridge Module Not Found

**Symptoms:**
- "Module 'AuracronMetaHumanBridge' could not be loaded" error
- Bridge classes not available in Blueprint

**Solutions:**

1. **Check module dependencies** in your project's `.Build.cs` file:
   ```csharp
   PublicDependencyModuleNames.AddRange(new string[] {
       "AuracronMetaHumanBridge",
       "MetaHumanSDK",
       "DNAReader",
       "DNAWriter"
   });
   ```

2. **Verify module is listed** in your `.uproject` file:
   ```json
   {
       "Modules": [
           {
               "Name": "AuracronMetaHumanBridge",
               "Type": "Runtime",
               "LoadingPhase": "Default"
           }
       ]
   }
   ```

3. **Regenerate project files** and rebuild:
   ```bash
   # Delete Binaries and Intermediate folders
   rm -rf Binaries/ Intermediate/
   
   # Regenerate project files
   GenerateProjectFiles.bat  # Windows
   ./GenerateProjectFiles.sh # macOS/Linux
   
   # Rebuild project
   ```

### Problem: Compilation Errors

**Symptoms:**
- C++ compilation errors related to MetaHuman SDK
- Missing header files

**Solutions:**

1. **Ensure MetaHuman SDK is installed** in your UE5.6 installation
2. **Check include paths** in your module's `.Build.cs`:
   ```csharp
   PublicIncludePaths.AddRange(new string[] {
       Path.Combine(ModuleDirectory, "Public"),
       Path.Combine(EngineDirectory, "Plugins/Runtime/MetaHuman/Source/MetaHumanSDK/Public")
   });
   ```

3. **Verify UE5.6 version compatibility**:
   ```cpp
   #if ENGINE_MAJOR_VERSION != 5 || ENGINE_MINOR_VERSION < 6
   #error "This bridge requires Unreal Engine 5.6 or later"
   #endif
   ```

### Problem: Python Bindings Not Working

**Symptoms:**
- Python import errors
- "MetaHuman module not found" in Python

**Solutions:**

1. **Run the Python setup script**:
   ```bash
   cd Scripts/Python
   python setup_python_bindings.py --project /path/to/project --engine /path/to/UE5.6
   ```

2. **Check Python path configuration**:
   ```python
   import sys
   sys.path.append('/path/to/your/project/Scripts/Python')
   import MetaHuman
   ```

3. **Verify Python plugin is enabled** in Unreal Editor:
   - Edit > Plugins > Search "Python"
   - Enable "Python Editor Script Plugin"

## DNA Loading Problems

### Problem: DNA File Not Loading

**Symptoms:**
- `LoadDNAFromFile` returns false
- "Failed to load DNA file" errors

**Solutions:**

1. **Verify file path and existence**:
   ```cpp
   FString DNAPath = TEXT("/Game/MetaHuman/Character.dna");
   
   // Check if file exists
   if (!FPaths::FileExists(DNAPath))
   {
       UE_LOG(LogTemp, Error, TEXT("DNA file does not exist: %s"), *DNAPath);
       return false;
   }
   ```

2. **Validate DNA file before loading**:
   ```cpp
   FDNAValidationResult ValidationResult = Bridge->ValidateDNAFile(DNAPath, EDNAValidationType::Complete);
   if (!ValidationResult.bIsValid)
   {
       for (const FString& Error : ValidationResult.Errors)
       {
           UE_LOG(LogTemp, Error, TEXT("DNA Validation Error: %s"), *Error);
       }
       return false;
   }
   ```

3. **Check file permissions**:
   ```bash
   # Ensure file is readable
   chmod 644 /path/to/dna/file.dna
   ```

### Problem: Corrupted DNA Data

**Symptoms:**
- DNA loads but data appears incorrect
- Unexpected joint/blend shape counts

**Solutions:**

1. **Run corruption detection**:
   ```cpp
   if (Bridge->DetectDNACorruption())
   {
       UE_LOG(LogTemp, Error, TEXT("DNA corruption detected"));
       
       // Try to restore from backup
       FString BackupPath = Bridge->FindLatestBackup();
       if (!BackupPath.IsEmpty())
       {
           Bridge->RestoreDNAFromBackup(BackupPath);
       }
   }
   ```

2. **Create backup before modifications**:
   ```cpp
   FString BackupPath = Bridge->CreateDNABackup(TEXT("BeforeModification"));
   UE_LOG(LogTemp, Log, TEXT("Backup created: %s"), *BackupPath);
   ```

3. **Use safe loading with retry**:
   ```cpp
   bool LoadDNAWithRetry(const FString& DNAPath, int32 MaxRetries = 3)
   {
       for (int32 Attempt = 0; Attempt < MaxRetries; ++Attempt)
       {
           if (Bridge->LoadDNAFromFile(DNAPath))
           {
               if (Bridge->IsValidDNA())
               {
                   return true;
               }
           }
           
           UE_LOG(LogTemp, Warning, TEXT("Load attempt %d failed, retrying..."), Attempt + 1);
           FPlatformProcess::Sleep(0.1f);
       }
       return false;
   }
   ```

## Performance Issues

### Problem: Slow DNA Loading

**Symptoms:**
- DNA files take a long time to load
- UI freezes during loading

**Solutions:**

1. **Enable performance optimization**:
   ```cpp
   TMap<FString, FString> PerfConfig;
   PerfConfig.Add(TEXT("EnableGPU"), TEXT("true"));
   PerfConfig.Add(TEXT("MemoryPoolSize"), TEXT("2048"));
   PerfConfig.Add(TEXT("ThreadCount"), TEXT("8"));
   
   Bridge->InitializePerformanceOptimization(PerfConfig);
   ```

2. **Use async loading**:
   ```cpp
   void LoadDNAAsync(const FString& DNAPath)
   {
       AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, DNAPath]()
       {
           UAuracronMetaHumanBridge* Bridge = NewObject<UAuracronMetaHumanBridge>();
           
           if (Bridge->LoadDNAFromFile(DNAPath))
           {
               AsyncTask(ENamedThreads::GameThread, [this, Bridge]()
               {
                   OnDNALoadedDelegate.Broadcast(Bridge);
               });
           }
       });
   }
   ```

3. **Monitor performance metrics**:
   ```cpp
   TMap<FString, FString> Metrics = Bridge->GetCurrentPerformanceMetrics();
   
   if (Metrics.Contains(TEXT("LoadTimeMs")))
   {
       float LoadTime = FCString::Atof(*Metrics[TEXT("LoadTimeMs")]);
       if (LoadTime > 1000.0f) // Over 1 second
       {
           UE_LOG(LogTemp, Warning, TEXT("Slow DNA load detected: %.2f ms"), LoadTime);
       }
   }
   ```

### Problem: High Memory Usage

**Symptoms:**
- Memory usage increases over time
- Out of memory errors

**Solutions:**

1. **Regular memory optimization**:
   ```cpp
   void OptimizeMemoryRegularly()
   {
       // Check memory usage
       TMap<FString, FString> Metrics = Bridge->GetCurrentPerformanceMetrics();
       
       if (Metrics.Contains(TEXT("MemoryUsageMB")))
       {
           float MemoryUsage = FCString::Atof(*Metrics[TEXT("MemoryUsageMB")]);
           
           if (MemoryUsage > 1024.0f) // Over 1GB
           {
               Bridge->OptimizeMemoryUsage(true); // Force garbage collection
           }
       }
   }
   ```

2. **Use object pooling**:
   ```cpp
   class UBridgePool : public UObject
   {
   private:
       TArray<UAuracronMetaHumanBridge*> AvailableBridges;
       
   public:
       UAuracronMetaHumanBridge* GetBridge()
       {
           if (AvailableBridges.Num() > 0)
           {
               return AvailableBridges.Pop();
           }
           return NewObject<UAuracronMetaHumanBridge>(this);
       }
       
       void ReturnBridge(UAuracronMetaHumanBridge* Bridge)
       {
           Bridge->ClearDNAData();
           AvailableBridges.Add(Bridge);
       }
   };
   ```

3. **Clear unused data**:
   ```cpp
   void CleanupUnusedData()
   {
       // Clear DNA data when not needed
       Bridge->ClearDNAData();
       
       // Force garbage collection
       GEngine->ForceGarbageCollection(true);
   }
   ```

## Python Binding Issues

### Problem: Python Import Errors

**Symptoms:**
- "No module named 'MetaHuman'" error
- Python functions not available

**Solutions:**

1. **Check Python path**:
   ```python
   import sys
   print(sys.path)
   
   # Add project path if missing
   sys.path.append('/path/to/your/project/Scripts/Python')
   ```

2. **Verify Python plugin configuration**:
   ```python
   # In Unreal Editor console
   py exec("import unreal; print(unreal.get_engine_subsystem(unreal.PythonScriptPluginSettings))")
   ```

3. **Test basic Python functionality**:
   ```python
   # Test in Unreal Editor console
   py exec("print('Python is working')")
   
   # Test MetaHuman import
   py exec("import MetaHuman; print('MetaHuman bridge imported successfully')")
   ```

### Problem: Python Functions Not Working

**Symptoms:**
- Python functions return None or fail
- Bridge not initialized for Python

**Solutions:**

1. **Check bridge initialization**:
   ```python
   import MetaHuman
   
   if not MetaHuman.is_bridge_initialized_for_python():
       print("Bridge not initialized for Python")
       # Run initialization
       MetaHuman.initialize_python_bindings()
   ```

2. **Validate Python bindings setup**:
   ```python
   result = MetaHuman.validate_python_bindings_setup()
   if not result:
       print("Python bindings setup validation failed")
   ```

3. **Test individual functions**:
   ```python
   # Test basic functions
   version = MetaHuman.get_bridge_version_string()
   print(f"Bridge version: {version}")
   
   supported_versions = MetaHuman.get_supported_dna_versions()
   print(f"Supported DNA versions: {supported_versions}")
   ```

## Memory Problems

### Problem: Memory Leaks

**Symptoms:**
- Memory usage continuously increases
- Application becomes slower over time

**Solutions:**

1. **Use proper object lifecycle management**:
   ```cpp
   class UDNAProcessor : public UObject
   {
   private:
       UPROPERTY()
       UAuracronMetaHumanBridge* Bridge;
       
   public:
       virtual void BeginDestroy() override
       {
           if (Bridge)
           {
               Bridge->ConditionalBeginDestroy();
               Bridge = nullptr;
           }
           Super::BeginDestroy();
       }
   };
   ```

2. **Monitor memory with profiling**:
   ```cpp
   void ProfileMemoryUsage()
   {
       FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
       
       UE_LOG(LogTemp, Log, TEXT("Available Physical: %llu MB"), 
              MemStats.AvailablePhysical / 1024 / 1024);
       UE_LOG(LogTemp, Log, TEXT("Used Physical: %llu MB"), 
              MemStats.UsedPhysical / 1024 / 1024);
   }
   ```

3. **Use memory debugging tools**:
   ```cpp
   // Enable memory debugging in Development builds
   #if UE_BUILD_DEVELOPMENT
   DEFINE_STAT(STAT_MetaHumanBridgeMemory);
   
   void TrackMemoryUsage()
   {
       SET_MEMORY_STAT(STAT_MetaHumanBridgeMemory, GetAllocatedSize());
   }
   #endif
   ```

## Platform-Specific Issues

### Windows Issues

**Problem: Visual Studio Compilation Errors**

**Solutions:**
1. Ensure Visual Studio 2022 is installed
2. Install Windows 10/11 SDK
3. Check Windows Defender exclusions for project folder

### macOS Issues

**Problem: Xcode Build Failures**

**Solutions:**
1. Ensure Xcode 14+ is installed
2. Install Command Line Tools: `xcode-select --install`
3. Check macOS version compatibility (12+)

### Linux Issues

**Problem: Missing Dependencies**

**Solutions:**
1. Install required packages:
   ```bash
   sudo apt-get update
   sudo apt-get install build-essential clang-12 libc++-dev libc++abi-dev
   ```

2. Set environment variables:
   ```bash
   export CC=clang-12
   export CXX=clang++-12
   ```

## Error Code Reference

| Code | Description | Severity | Solution |
|------|-------------|----------|----------|
| DNA_001 | File not found | Error | Check file path |
| DNA_002 | Invalid DNA format | Error | Use valid DNA file |
| DNA_003 | Corrupted DNA data | Error | Restore from backup |
| DNA_004 | Unsupported DNA version | Warning | Update bridge |
| JOINT_001 | Invalid joint index | Error | Check joint count |
| JOINT_002 | Joint constraint violation | Warning | Adjust values |
| BLEND_001 | Invalid blend shape index | Error | Check blend shape count |
| BLEND_002 | Weight out of range | Warning | Clamp to 0.0-1.0 |
| PERF_001 | Memory allocation failed | Error | Free memory |
| PERF_002 | GPU acceleration unavailable | Warning | Use CPU fallback |
| PYTHON_001 | Python binding not initialized | Error | Initialize bindings |
| PYTHON_002 | Python function call failed | Error | Check parameters |

### Getting More Help

If you continue to experience issues:

1. **Check the logs** in `Saved/Logs/` for detailed error information
2. **Run system diagnostics**:
   ```cpp
   TArray<FErrorInfo> Diagnostics = Bridge->RunSystemDiagnostics();
   ```
3. **Contact support** with:
   - Error logs
   - System specifications
   - Steps to reproduce the issue
   - DNA file samples (if applicable)

---

For additional support, visit our [GitHub Issues](https://github.com/auracron/metahuman-bridge/issues) page.
