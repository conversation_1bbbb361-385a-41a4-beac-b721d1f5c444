// AURACRON - Bridge C++ para Sistema de Criaturas Neutras Adaptativas
// Integração com Unreal Engine 5.6 Mass Entity, State Trees e AI
// Autor: Augment Agent
// Data: 2025-08-03
// Versão: 1.0.0

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/Engine.h"
#include "MassEntitySubsystem.h"
#include "MassEntityTypes.h"
#include "MassProcessor.h"
#include "StateTree.h"
#include "StateTreeExecutionContext.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "AIController.h"
#include "GameFramework/Character.h"
#include "NavigationSystem.h"
#include "TimerManager.h"
#include "AuracronAdaptiveCreaturesBridge.generated.h"

// Forward Declarations
class UMassEntitySubsystem;
class UStateTree;
class UBehaviorTreeComponent;
class UAIPerceptionComponent;
class UNavigationSystemV1;

UENUM(BlueprintType)
enum class EAuracronAdaptiveRealmType : uint8
{
    PlanicieRadiante    UMETA(DisplayName = "Planície Radiante"),
    FirmamentoZephyr    UMETA(DisplayName = "Firmamento Zephyr"),
    AbismoUmbrio        UMETA(DisplayName = "Abismo Umbrio")
};

UENUM(BlueprintType)
enum class ECreatureType : uint8
{
    // Planície Radiante
    GuardiaoTerrestre   UMETA(DisplayName = "Guardião Terrestre"),
    CacadorAgil         UMETA(DisplayName = "Caçador Ágil"),
    ColetorMistico      UMETA(DisplayName = "Coletor Místico"),
    
    // Firmamento Zephyr
    SentinelaVoadora    UMETA(DisplayName = "Sentinela Voadora"),
    TempestadeViva      UMETA(DisplayName = "Tempestade Viva"),
    NavegadorEtereo     UMETA(DisplayName = "Navegador Etéreo"),
    
    // Abismo Umbrio
    SombraAdaptativa    UMETA(DisplayName = "Sombra Adaptativa"),
    Cristalizador       UMETA(DisplayName = "Cristalizador"),
    EcoSombrio          UMETA(DisplayName = "Eco Sombrio")
};

UENUM(BlueprintType)
enum class EBehaviorState : uint8
{
    Passive             UMETA(DisplayName = "Passive"),
    Alert               UMETA(DisplayName = "Alert"),
    Aggressive          UMETA(DisplayName = "Aggressive"),
    Defensive           UMETA(DisplayName = "Defensive"),
    Hunting             UMETA(DisplayName = "Hunting"),
    Fleeing             UMETA(DisplayName = "Fleeing"),
    Territorial         UMETA(DisplayName = "Territorial"),
    Cooperative         UMETA(DisplayName = "Cooperative")
};

UENUM(BlueprintType)
enum class EAdaptationType : uint8
{
    AggressionScaling   UMETA(DisplayName = "Aggression Scaling"),
    TerritoryShifting   UMETA(DisplayName = "Territory Shifting"),
    PackCoordination    UMETA(DisplayName = "Pack Coordination"),
    ResourceCompetition UMETA(DisplayName = "Resource Competition"),
    StealthAdaptation   UMETA(DisplayName = "Stealth Adaptation"),
    PatrolOptimization  UMETA(DisplayName = "Patrol Optimization")
};

USTRUCT(BlueprintType)
struct AURACRONADAPTIVECREATURES_API FCreatureProperties
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    float Health = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    float Damage = 25.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    float Speed = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    float DetectionRange = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    float AttackRange = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    float TerritoryRadius = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    float AdaptationRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    bool bCanFly = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    bool bIsNocturnal = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Properties")
    bool bPackHunter = false;
};

USTRUCT(BlueprintType)
struct AURACRONADAPTIVECREATURES_API FAdaptationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    EAdaptationType AdaptationType = EAdaptationType::AggressionScaling;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float AdaptationStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float TriggerThreshold = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float DecayRate = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    bool bPermanentAdaptation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    FDateTime LastAdaptationTime;
};

USTRUCT(BlueprintType)
struct AURACRONADAPTIVECREATURES_API FCreatureSpawnData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    ECreatureType CreatureType = ECreatureType::GuardiaoTerrestre;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    EAuracronAdaptiveRealmType RealmType = EAuracronAdaptiveRealmType::PlanicieRadiante;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    FVector SpawnLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    FRotator SpawnRotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    FCreatureProperties Properties;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    TArray<FAdaptationData> InitialAdaptations;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    TSoftObjectPtr<USkeletalMesh> CreatureMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    TSoftObjectPtr<UAnimBlueprint> AnimationBlueprint;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    TSoftObjectPtr<UStateTree> BehaviorStateTree;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    int32 PackSize = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Data")
    float SpawnRadius = 100.0f;
};

USTRUCT(BlueprintType)
struct AURACRONADAPTIVECREATURES_API FMassCreatureFragment : public FMassFragment
{
    GENERATED_BODY()

    ECreatureType CreatureType = ECreatureType::GuardiaoTerrestre;
    EAuracronAdaptiveRealmType RealmType = EAuracronAdaptiveRealmType::PlanicieRadiante;
    EBehaviorState CurrentBehaviorState = EBehaviorState::Passive;
    FCreatureProperties Properties;
    TArray<FAdaptationData> ActiveAdaptations;
    FVector HomeLocation = FVector::ZeroVector;
    FVector TargetLocation = FVector::ZeroVector;
    float LastPlayerInteractionTime = 0.0f;
    float ThreatLevel = 0.0f;
    int32 PackID = -1;
    bool bIsPackLeader = false;
};

USTRUCT(BlueprintType)
struct AURACRONADAPTIVECREATURES_API FMassCreatureTag : public FMassTag
{
    GENERATED_BODY()
};

/**
 * Estrutura wrapper para TArray<FAdaptationData> em TMap
 */
USTRUCT(BlueprintType)
struct AURACRONADAPTIVECREATURES_API FAdaptationDataArrayWrapper
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation Data")
    TArray<FAdaptationData> AdaptationData;

    FAdaptationDataArrayWrapper()
    {
    }

    FAdaptationDataArrayWrapper(const TArray<FAdaptationData>& InAdaptationData)
        : AdaptationData(InAdaptationData)
    {
    }
};

/**
 * Classe principal do Bridge para Sistema de Criaturas Neutras Adaptativas
 * Integra Mass Entity System, State Trees e AI para criaturas que se adaptam ao comportamento do jogador
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Adaptive Creatures", meta = (DisplayName = "AURACRON Adaptive Creatures Bridge"))
class AURACRONADAPTIVECREATURES_API UAuracronAdaptiveCreaturesBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronAdaptiveCreaturesBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Creature Management ===

    /**
     * Spawnar criaturas usando Mass Entity System
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Spawning", CallInEditor)
    bool SpawnCreatures(const TArray<FCreatureSpawnData>& SpawnData);

    /**
     * Spawnar criatura individual (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    FMassEntityHandle SpawnSingleCreature(const FCreatureSpawnData& SpawnData);

    /**
     * Spawnar pack de criaturas (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    TArray<FMassEntityHandle> SpawnCreaturePack(const FCreatureSpawnData& LeaderSpawnData);

    /**
     * Remover criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    bool RemoveCreature(FMassEntityHandle EntityHandle);

    /**
     * Remover todas as criaturas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Management", CallInEditor)
    void RemoveAllCreatures();

    // === Adaptation System ===

    /**
     * Aplicar adaptação a uma criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    bool ApplyAdaptation(FMassEntityHandle EntityHandle, const FAdaptationData& AdaptationData);

    /**
     * Processar adaptações baseadas no comportamento do jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Adaptation", CallInEditor)
    void ProcessPlayerBehaviorAdaptations(const FVector& PlayerLocation, float PlayerThreatLevel);

    /**
     * Atualizar adaptações de todas as criaturas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Adaptation", CallInEditor)
    void UpdateAllAdaptations(float DeltaTime);

    /**
     * Obter nível de adaptação de uma criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    float GetCreatureAdaptationLevel(FMassEntityHandle EntityHandle, EAdaptationType AdaptationType) const;

    // === Behavior Management ===

    /**
     * Definir estado comportamental de uma criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    bool SetCreatureBehaviorState(FMassEntityHandle EntityHandle, EBehaviorState NewState);

    /**
     * Obter estado comportamental de uma criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    EBehaviorState GetCreatureBehaviorState(FMassEntityHandle EntityHandle) const;

    /**
     * Configurar State Tree para uma criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    bool SetCreatureStateTree(FMassEntityHandle EntityHandle, UStateTree* StateTree);

    /**
     * Processar comportamentos de pack
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Behavior", CallInEditor)
    void ProcessPackBehaviors();

    // === Territory and Navigation ===

    /**
     * Definir território de uma criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    bool SetCreatureTerritory(FMassEntityHandle EntityHandle, const FVector& Center, float Radius);

    /**
     * Verificar se posição está em território de criatura (C++ only - FMassEntityHandle não suportado pelo Blueprint)
     */
    bool IsLocationInCreatureTerritory(FMassEntityHandle EntityHandle, const FVector& Location) const;

    /**
     * Atualizar navegação 3D para criaturas voadoras
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Navigation", CallInEditor)
    void Update3DNavigation();

    // === Realm-Specific Behaviors ===

    /**
     * Configurar comportamentos específicos do realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Realm", CallInEditor)
    bool ConfigureRealmSpecificBehaviors(EAuracronAdaptiveRealmType RealmType);

    /**
     * Aplicar efeitos ambientais do realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Realm", CallInEditor)
    void ApplyRealmEnvironmentalEffects(EAuracronAdaptiveRealmType RealmType);

public:
    // === Configuration Properties ===

    /** Subsistema Mass Entity */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UMassEntitySubsystem> MassEntitySubsystem;

    /** Configurações de spawn por tipo de criatura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<ECreatureType, FCreatureSpawnData> CreatureTypeConfigurations;

    /** Configurações de adaptação por realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<ERealmType, FAdaptationDataArrayWrapper> RealmAdaptationConfigurations;

    /** Máximo de criaturas simultâneas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "10000"))
    int32 MaxSimultaneousCreatures = 1000;

    /** Usar multi-threading para processamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseMultiThreading = true;

    /** Intervalo de atualização de adaptações (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float AdaptationUpdateInterval = 1.0f;

    /** Raio de detecção do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "100.0", ClampMax = "5000.0"))
    float PlayerDetectionRadius = 1000.0f;

    /** Seed para geração procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "0", ClampMax = "999999"))
    int32 GenerationSeed = 54321;

private:
    // === Internal State ===
    
    /** Entidades de criaturas ativas */
    TArray<FMassEntityHandle> ActiveCreatures;
    
    /** Dados de packs */
    TMap<int32, TArray<FMassEntityHandle>> CreaturePacks;
    
    /** Próximo ID de pack */
    int32 NextPackID = 0;
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para atualizações */
    FTimerHandle AdaptationUpdateTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection CreatureManagementMutex;
    
    /** Última posição conhecida do jogador */
    FVector LastKnownPlayerLocation = FVector::ZeroVector;
    
    /** Último nível de ameaça do jogador */
    float LastPlayerThreatLevel = 0.0f;

public:
    // === Python Integration ===
    
    /**
     * Inicializar bindings Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Python", CallInEditor)
    bool InitializePythonBindings();
    
    /**
     * Executar script Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Python", CallInEditor)
    bool ExecutePythonScript(const FString& ScriptPath);
    
    /**
     * Obter dados de criaturas para Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Python", CallInEditor)
    FString GetCreatureDataForPython() const;

    // === Utility Functions ===
    
    /**
     * Obter estatísticas do sistema
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Utility", CallInEditor)
    FString GetSystemStatistics() const;
    
    /**
     * Validar integridade do sistema
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Utility", CallInEditor)
    bool ValidateSystemIntegrity() const;
    
    /**
     * Resetar todas as adaptações
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Adaptive Creatures|Utility", CallInEditor)
    void ResetAllAdaptations();
};

