// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Python Module Implementation
// Bridge 2.15: PCG Framework - Python Integration

#include "AuracronPCGPythonBindings.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"

#ifdef WITH_PYTHON
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>

// =============================================================================
// PYTHON MODULE DEFINITION
// =============================================================================

/**
 * Python module entry point for Auracron PCG Framework
 * This creates the main auracron_pcg Python module
 */
PYBIND11_MODULE(auracron_pcg, m) {
    m.doc() = "Auracron PCG Framework Python Bindings";
    m.attr("__version__") = "2.15.0";
    
    // Core PCG classes
    pybind11::class_<UPCGSettings>(m, "PCGSettings")
        .def("get_class_name", [](const UPCGSettings& self) {
            return TCHAR_TO_UTF8(*self.GetClass()->GetName());
        }, "Get the class name of the PCG settings")
        .def("__repr__", [](const UPCGSettings& self) {
            return std::string("<PCGSettings: ") + TCHAR_TO_UTF8(*self.GetClass()->GetName()) + ">";
        });

    pybind11::class_<UPCGData>(m, "PCGData")
        .def("get_class_name", [](const UPCGData& self) {
            return TCHAR_TO_UTF8(*self.GetClass()->GetName());
        }, "Get the class name of the PCG data")
        .def("__repr__", [](const UPCGData& self) {
            return std::string("<PCGData: ") + TCHAR_TO_UTF8(*self.GetClass()->GetName()) + ">";
        });

    pybind11::class_<UPCGPointData, UPCGData>(m, "PCGPointData")
        .def("get_points_count", [](const UPCGPointData& self) {
            return self.GetPoints().Num();
        }, "Get the number of points in the point data")
        .def("get_bounds", [](const UPCGPointData& self) {
            FBox Bounds = self.GetBounds();
            return std::make_tuple(
                std::make_tuple(Bounds.Min.X, Bounds.Min.Y, Bounds.Min.Z),
                std::make_tuple(Bounds.Max.X, Bounds.Max.Y, Bounds.Max.Z)
            );
        }, "Get the bounding box of the point data")
        .def("__repr__", [](const UPCGPointData& self) {
            return std::string("<PCGPointData: ") + std::to_string(self.GetPoints().Num()) + " points>";
        });

    pybind11::class_<UPCGSpatialData, UPCGData>(m, "PCGSpatialData")
        .def("get_bounds", [](const UPCGSpatialData& self) {
            FBox Bounds = self.GetBounds();
            return std::make_tuple(
                std::make_tuple(Bounds.Min.X, Bounds.Min.Y, Bounds.Min.Z),
                std::make_tuple(Bounds.Max.X, Bounds.Max.Y, Bounds.Max.Z)
            );
        }, "Get the bounding box of the spatial data")
        .def("get_volume", [](const UPCGSpatialData& self) {
            return self.GetBounds().GetVolume();
        }, "Get the volume of the spatial data")
        .def("__repr__", [](const UPCGSpatialData& self) {
            FBox Bounds = self.GetBounds();
            return std::string("<PCGSpatialData: volume=") + std::to_string(Bounds.GetVolume()) + ">";
        });

    pybind11::class_<UPCGGraph>(m, "PCGGraph")
        .def("get_class_name", [](const UPCGGraph& self) {
            return TCHAR_TO_UTF8(*self.GetClass()->GetName());
        }, "Get the class name of the PCG graph")
        .def("get_name", [](const UPCGGraph& self) {
            return TCHAR_TO_UTF8(*self.GetName());
        }, "Get the name of the PCG graph")
        .def("__repr__", [](const UPCGGraph& self) {
            return std::string("<PCGGraph: ") + TCHAR_TO_UTF8(*self.GetName()) + ">";
        });

    pybind11::class_<UPCGComponent>(m, "PCGComponent")
        .def("get_class_name", [](const UPCGComponent& self) {
            return TCHAR_TO_UTF8(*self.GetClass()->GetName());
        }, "Get the class name of the PCG component")
        .def("__repr__", [](const UPCGComponent& self) {
            return std::string("<PCGComponent: ") + TCHAR_TO_UTF8(*self.GetClass()->GetName()) + ">";
        });

    // PCG Point structure
    pybind11::class_<FPCGPoint>(m, "PCGPoint")
        .def(pybind11::init<>(), "Create a new PCG point")
        .def_readwrite("density", &FPCGPoint::Density, "Point density value")
        .def_property("position", 
            [](const FPCGPoint& self) {
                FVector Pos = self.Transform.GetLocation();
                return std::make_tuple(Pos.X, Pos.Y, Pos.Z);
            },
            [](FPCGPoint& self, const std::tuple<float, float, float>& pos) {
                FVector NewPos(std::get<0>(pos), std::get<1>(pos), std::get<2>(pos));
                self.Transform.SetLocation(NewPos);
            }, "Point position as (x, y, z) tuple")
        .def_property("rotation",
            [](const FPCGPoint& self) {
                FRotator Rot = self.Transform.GetRotation().Rotator();
                return std::make_tuple(Rot.Pitch, Rot.Yaw, Rot.Roll);
            },
            [](FPCGPoint& self, const std::tuple<float, float, float>& rot) {
                FRotator NewRot(std::get<0>(rot), std::get<1>(rot), std::get<2>(rot));
                self.Transform.SetRotation(NewRot.Quaternion());
            }, "Point rotation as (pitch, yaw, roll) tuple")
        .def_property("scale",
            [](const FPCGPoint& self) {
                FVector Scale = self.Transform.GetScale3D();
                return std::make_tuple(Scale.X, Scale.Y, Scale.Z);
            },
            [](FPCGPoint& self, const std::tuple<float, float, float>& scale) {
                FVector NewScale(std::get<0>(scale), std::get<1>(scale), std::get<2>(scale));
                self.Transform.SetScale3D(NewScale);
            }, "Point scale as (x, y, z) tuple")
        .def_property("color",
            [](const FPCGPoint& self) {
                return std::make_tuple(self.Color.X, self.Color.Y, self.Color.Z, self.Color.W);
            },
            [](FPCGPoint& self, const std::tuple<float, float, float, float>& color) {
                self.Color = FVector4(std::get<0>(color), std::get<1>(color), std::get<2>(color), std::get<3>(color));
            }, "Point color as (r, g, b, a) tuple")
        .def("__repr__", [](const FPCGPoint& self) {
            FVector Pos = self.Transform.GetLocation();
            return std::string("<PCGPoint: pos=(") + 
                   std::to_string(Pos.X) + ", " + std::to_string(Pos.Y) + ", " + std::to_string(Pos.Z) + 
                   "), density=" + std::to_string(self.Density) + ">";
        });

    // Vector and Color utilities
    pybind11::class_<FVector>(m, "Vector")
        .def(pybind11::init<float, float, float>(), "Create a new vector", 
             pybind11::arg("x") = 0.0f, pybind11::arg("y") = 0.0f, pybind11::arg("z") = 0.0f)
        .def_readwrite("x", &FVector::X, "X component")
        .def_readwrite("y", &FVector::Y, "Y component")
        .def_readwrite("z", &FVector::Z, "Z component")
        .def("length", &FVector::Size, "Get the length of the vector")
        .def("normalize", [](FVector& self) { self.Normalize(); }, "Normalize the vector")
        .def("__repr__", [](const FVector& self) {
            return std::string("<Vector: (") + std::to_string(self.X) + ", " + 
                   std::to_string(self.Y) + ", " + std::to_string(self.Z) + ")>";
        });

    pybind11::class_<FLinearColor>(m, "LinearColor")
        .def(pybind11::init<float, float, float, float>(), "Create a new linear color",
             pybind11::arg("r") = 1.0f, pybind11::arg("g") = 1.0f, pybind11::arg("b") = 1.0f, pybind11::arg("a") = 1.0f)
        .def_readwrite("r", &FLinearColor::R, "Red component")
        .def_readwrite("g", &FLinearColor::G, "Green component")
        .def_readwrite("b", &FLinearColor::B, "Blue component")
        .def_readwrite("a", &FLinearColor::A, "Alpha component")
        .def("__repr__", [](const FLinearColor& self) {
            return std::string("<LinearColor: (") + std::to_string(self.R) + ", " + 
                   std::to_string(self.G) + ", " + std::to_string(self.B) + ", " + std::to_string(self.A) + ")>";
        });

    // Enums
    pybind11::enum_<EAuracronPCGNodeCategory>(m, "PCGNodeCategory", "PCG node categories")
        .value("Generator", EAuracronPCGNodeCategory::Generator, "Generator nodes")
        .value("Modifier", EAuracronPCGNodeCategory::Modifier, "Modifier nodes")
        .value("Filter", EAuracronPCGNodeCategory::Filter, "Filter nodes")
        .value("Sampler", EAuracronPCGNodeCategory::Sampler, "Sampler nodes")
        .value("Debug", EAuracronPCGNodeCategory::Debug, "Debug nodes")
        .value("Utility", EAuracronPCGNodeCategory::Utility, "Utility nodes");

    pybind11::enum_<EAuracronPCGExecutionMode>(m, "PCGExecutionMode", "PCG execution modes")
        .value("Synchronous", EAuracronPCGExecutionMode::Synchronous, "Synchronous execution")
        .value("Asynchronous", EAuracronPCGExecutionMode::Asynchronous, "Asynchronous execution")
        .value("Threaded", EAuracronPCGExecutionMode::Threaded, "Threaded execution");

    pybind11::enum_<EAuracronPCGDebugVisualizationMode>(m, "PCGDebugVisualizationMode", "PCG debug visualization modes")
        .value("None", EAuracronPCGDebugVisualizationMode::None, "No visualization")
        .value("Points", EAuracronPCGDebugVisualizationMode::Points, "Show points")
        .value("Connections", EAuracronPCGDebugVisualizationMode::Connections, "Show connections")
        .value("BoundingBoxes", EAuracronPCGDebugVisualizationMode::BoundingBoxes, "Show bounding boxes")
        .value("Attributes", EAuracronPCGDebugVisualizationMode::Attributes, "Show attributes")
        .value("Performance", EAuracronPCGDebugVisualizationMode::Performance, "Show performance info")
        .value("DataFlow", EAuracronPCGDebugVisualizationMode::DataFlow, "Show data flow")
        .value("Errors", EAuracronPCGDebugVisualizationMode::Errors, "Show errors")
        .value("All", EAuracronPCGDebugVisualizationMode::All, "Show all");

    pybind11::enum_<EAuracronPCGCustomNodeTemplateType>(m, "PCGCustomNodeTemplateType", "PCG custom node template types")
        .value("Generator", EAuracronPCGCustomNodeTemplateType::Generator, "Generator template")
        .value("Modifier", EAuracronPCGCustomNodeTemplateType::Modifier, "Modifier template")
        .value("Filter", EAuracronPCGCustomNodeTemplateType::Filter, "Filter template")
        .value("Sampler", EAuracronPCGCustomNodeTemplateType::Sampler, "Sampler template")
        .value("Transformer", EAuracronPCGCustomNodeTemplateType::Transformer, "Transformer template")
        .value("Analyzer", EAuracronPCGCustomNodeTemplateType::Analyzer, "Analyzer template")
        .value("Utility", EAuracronPCGCustomNodeTemplateType::Utility, "Utility template")
        .value("Debug", EAuracronPCGCustomNodeTemplateType::Debug, "Debug template")
        .value("Custom", EAuracronPCGCustomNodeTemplateType::Custom, "Custom template");

    // Structures
    pybind11::class_<FAuracronPCGNodeMetadata>(m, "PCGNodeMetadata", "PCG node metadata")
        .def(pybind11::init<>(), "Create new node metadata")
        .def_readwrite("node_name", &FAuracronPCGNodeMetadata::NodeName, "Node name")
        .def_readwrite("node_description", &FAuracronPCGNodeMetadata::NodeDescription, "Node description")
        .def_readwrite("category", &FAuracronPCGNodeMetadata::Category, "Node category");

    pybind11::class_<FAuracronPCGElementResult>(m, "PCGElementResult", "PCG element execution result")
        .def(pybind11::init<>(), "Create new element result")
        .def_readwrite("success", &FAuracronPCGElementResult::bSuccess, "Success flag")
        .def_readwrite("error_message", &FAuracronPCGElementResult::ErrorMessage, "Error message")
        .def_readwrite("execution_time", &FAuracronPCGElementResult::ExecutionTime, "Execution time in seconds")
        .def_readwrite("points_processed", &FAuracronPCGElementResult::PointsProcessed, "Number of points processed")
        .def_readwrite("output_data_count", &FAuracronPCGElementResult::OutputDataCount, "Number of output data objects");

    pybind11::class_<FAuracronPCGDebugVisualizationDescriptor>(m, "PCGDebugVisualizationDescriptor", "PCG debug visualization descriptor")
        .def(pybind11::init<>(), "Create new debug visualization descriptor")
        .def_readwrite("visualization_mode", &FAuracronPCGDebugVisualizationDescriptor::VisualizationMode, "Visualization mode")
        .def_readwrite("show_points", &FAuracronPCGDebugVisualizationDescriptor::bShowPoints, "Show points flag")
        .def_readwrite("point_size", &FAuracronPCGDebugVisualizationDescriptor::PointSize, "Point size")
        .def_readwrite("show_connections", &FAuracronPCGDebugVisualizationDescriptor::bShowConnections, "Show connections flag")
        .def_readwrite("show_errors", &FAuracronPCGDebugVisualizationDescriptor::bShowErrors, "Show errors flag");

    pybind11::class_<FAuracronPCGCustomNodeTemplate>(m, "PCGCustomNodeTemplate", "PCG custom node template")
        .def(pybind11::init<>(), "Create new custom node template")
        .def_readwrite("template_name", &FAuracronPCGCustomNodeTemplate::TemplateName, "Template name")
        .def_readwrite("display_name", &FAuracronPCGCustomNodeTemplate::DisplayName, "Display name")
        .def_readwrite("description", &FAuracronPCGCustomNodeTemplate::Description, "Description")
        .def_readwrite("template_type", &FAuracronPCGCustomNodeTemplate::TemplateType, "Template type");

    // Utility functions
    m.def("log_message", [](const std::string& message) {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python: %s"), UTF8_TO_TCHAR(message.c_str()));
    }, "Log a message", pybind11::arg("message"));

    m.def("log_warning", [](const std::string& message) {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Python: %s"), UTF8_TO_TCHAR(message.c_str()));
    }, "Log a warning", pybind11::arg("message"));

    m.def("log_error", [](const std::string& message) {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Python: %s"), UTF8_TO_TCHAR(message.c_str()));
    }, "Log an error", pybind11::arg("message"));

    m.def("get_framework_version", []() {
        return "2.15.0";
    }, "Get the framework version");

    m.def("get_build_info", []() {
        return std::make_tuple("2.15.0", "Release", __DATE__, __TIME__);
    }, "Get build information as (version, config, date, time)");

    // Math utilities
    m.def("distance", [](const std::tuple<float, float, float>& a, const std::tuple<float, float, float>& b) {
        FVector VecA(std::get<0>(a), std::get<1>(a), std::get<2>(a));
        FVector VecB(std::get<0>(b), std::get<1>(b), std::get<2>(b));
        return FVector::Dist(VecA, VecB);
    }, "Calculate distance between two points", pybind11::arg("point_a"), pybind11::arg("point_b"));

    m.def("lerp", [](float a, float b, float t) {
        return FMath::Lerp(a, b, t);
    }, "Linear interpolation", pybind11::arg("a"), pybind11::arg("b"), pybind11::arg("t"));

    m.def("clamp", [](float value, float min_val, float max_val) {
        return FMath::Clamp(value, min_val, max_val);
    }, "Clamp value between min and max", pybind11::arg("value"), pybind11::arg("min"), pybind11::arg("max"));
}

#endif // WITH_PYTHON
