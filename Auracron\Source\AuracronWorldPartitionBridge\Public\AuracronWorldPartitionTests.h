// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Testing Suite Header
// Bridge 3.14: World Partition - Testing Suite

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionStreaming.h"
#include "AuracronWorldPartitionPerformance.h"
#include "AuracronWorldPartitionDebug.h"

// Testing framework includes for UE5.6
#include "Tests/AutomationCommon.h"
#include "Engine/Engine.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationEditorCommon.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Stats/Stats.h"

// Performance testing includes
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "HAL/PlatformMemory.h"
#include "HAL/ThreadHeartBeat.h"

#include "AuracronWorldPartitionTests.generated.h"

// Forward declarations
class UAuracronWorldPartitionTestManager;

// =============================================================================
// TEST TYPES AND ENUMS
// =============================================================================

// Test categories
UENUM(BlueprintType)
enum class EAuracronTestCategory : uint8
{
    Unit                    UMETA(DisplayName = "Unit Tests"),
    Integration             UMETA(DisplayName = "Integration Tests"),
    Performance             UMETA(DisplayName = "Performance Tests"),
    Streaming               UMETA(DisplayName = "Streaming Tests"),
    LargeWorld              UMETA(DisplayName = "Large World Tests"),
    Stress                  UMETA(DisplayName = "Stress Tests"),
    Regression              UMETA(DisplayName = "Regression Tests"),
    Validation              UMETA(DisplayName = "Validation Tests")
};

// Test severity levels
UENUM(BlueprintType)
enum class EAuracronTestSeverity : uint8
{
    Low                     UMETA(DisplayName = "Low"),
    Medium                  UMETA(DisplayName = "Medium"),
    High                    UMETA(DisplayName = "High"),
    Critical                UMETA(DisplayName = "Critical")
};

// Test execution modes
UENUM(BlueprintType)
enum class EAuracronTestExecutionMode : uint8
{
    Sequential              UMETA(DisplayName = "Sequential"),
    Parallel                UMETA(DisplayName = "Parallel"),
    Isolated                UMETA(DisplayName = "Isolated"),
    Continuous              UMETA(DisplayName = "Continuous")
};

// =============================================================================
// TEST CONFIGURATION
// =============================================================================

/**
 * Test Configuration
 * Configuration settings for World Partition testing
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronTestConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bEnableUnitTests = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bEnableIntegrationTests = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bEnablePerformanceTests = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bEnableStreamingTests = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bEnableLargeWorldTests = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bEnableStressTests = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    EAuracronTestExecutionMode ExecutionMode = EAuracronTestExecutionMode::Sequential;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    float TestTimeout = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    int32 MaxConcurrentTests = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bGenerateDetailedReports = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bCapturePerformanceMetrics = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    bool bValidateMemoryUsage = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    float PerformanceThresholdMs = 16.67f; // 60 FPS

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    float MemoryThresholdMB = 1024.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    int32 LargeWorldCellCount = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    int32 StressTestDuration = 300; // seconds

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Configuration")
    FString TestOutputDirectory = TEXT("Tests/WorldPartition");

    FAuracronTestConfiguration()
    {
        bEnableUnitTests = true;
        bEnableIntegrationTests = true;
        bEnablePerformanceTests = true;
        bEnableStreamingTests = true;
        bEnableLargeWorldTests = false;
        bEnableStressTests = false;
        ExecutionMode = EAuracronTestExecutionMode::Sequential;
        TestTimeout = 30.0f;
        MaxConcurrentTests = 4;
        bGenerateDetailedReports = true;
        bCapturePerformanceMetrics = true;
        bValidateMemoryUsage = true;
        PerformanceThresholdMs = 16.67f;
        MemoryThresholdMB = 1024.0f;
        LargeWorldCellCount = 1000;
        StressTestDuration = 300;
        TestOutputDirectory = TEXT("Tests/WorldPartition");
    }
};

// =============================================================================
// TEST RESULT DATA
// =============================================================================

/**
 * Test Result
 * Result data from a single test execution
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronTestResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    FString TestName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    EAuracronTestCategory TestCategory = EAuracronTestCategory::Unit;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    bool bPassed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    FString ErrorMessage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    float ExecutionTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    FDateTime ExecutionTimestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    TMap<FString, FString> AdditionalData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Result")
    TArray<FString> Warnings;

    FAuracronTestResult()
    {
        TestCategory = EAuracronTestCategory::Unit;
        bPassed = false;
        ExecutionTime = 0.0f;
        MemoryUsageMB = 0.0f;
        ExecutionTimestamp = FDateTime::Now();
    }
};

// =============================================================================
// TEST SUITE DATA
// =============================================================================

/**
 * Test Suite Result
 * Aggregated results from a test suite execution
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronTestSuiteResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    FString SuiteName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    int32 TotalTests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    int32 PassedTests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    int32 FailedTests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    int32 SkippedTests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    float TotalExecutionTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    float AverageExecutionTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    float PeakMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    FDateTime ExecutionTimestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    TArray<FAuracronTestResult> TestResults;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Suite Result")
    TMap<FString, float> PerformanceMetrics;

    FAuracronTestSuiteResult()
    {
        TotalTests = 0;
        PassedTests = 0;
        FailedTests = 0;
        SkippedTests = 0;
        TotalExecutionTime = 0.0f;
        AverageExecutionTime = 0.0f;
        PeakMemoryUsageMB = 0.0f;
        ExecutionTimestamp = FDateTime::Now();
    }
};

// =============================================================================
// WORLD PARTITION TEST MANAGER
// =============================================================================

/**
 * World Partition Test Manager
 * Central manager for World Partition testing suite
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronWorldPartitionTestManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    static UAuracronWorldPartitionTestManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    void Initialize(const FAuracronTestConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    bool IsInitialized() const;

    // Test execution
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunAllTests();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunTestCategory(EAuracronTestCategory Category);

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult RunSingleTest(const FString& TestName);

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    void RunTestsAsync(EAuracronTestCategory Category);

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    bool IsTestRunning() const;

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    void CancelRunningTests();

    // Unit tests
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunUnitTests();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestGridSystemCreation();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestCellManagement();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestStreamingSystem();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestPerformanceMonitoring();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestDebugSystem();

    // Integration tests
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunIntegrationTests();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestWorldPartitionIntegration();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestPCGIntegration();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestLumenIntegration();

    // Performance tests
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunPerformanceTests();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestStreamingPerformance();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestMemoryUsage();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestCPUPerformance();

    // Streaming tests
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunStreamingTests();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestCellLoadingUnloading();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestStreamingPriority();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestStreamingStability();

    // Large world tests
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunLargeWorldTests();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestLargeWorldCreation();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestMassiveStreamingLoad();

    // Stress tests
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestSuiteResult RunStressTests();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestContinuousStreaming();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestResult TestMemoryStress();

    // Test reporting
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    bool GenerateTestReport(const FAuracronTestSuiteResult& SuiteResult, const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FString GenerateTestSummary(const FAuracronTestSuiteResult& SuiteResult);

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    TArray<FAuracronTestSuiteResult> GetTestHistory();

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    void ClearTestHistory();

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    void SetConfiguration(const FAuracronTestConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Test Manager")
    FAuracronTestConfiguration GetConfiguration() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestCompleted, FString, TestName, FAuracronTestResult, Result);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestSuiteCompleted, FString, SuiteName, FAuracronTestSuiteResult, Result);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestFailed, FString, TestName, FString, ErrorMessage);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTestCompleted OnTestCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTestSuiteCompleted OnTestSuiteCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTestFailed OnTestFailed;

private:
    static UAuracronWorldPartitionTestManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronTestConfiguration Configuration;

    // Test state
    bool bTestsRunning = false;
    int32 CurrentRunningTests = 0;
    TArray<FAuracronTestSuiteResult> TestHistory;

    // Thread safety
    mutable FCriticalSection TestLock;

    // Internal functions
    void ValidateConfiguration();
    FAuracronTestResult ExecuteTest(const FString& TestName, TFunction<bool()> TestFunction);
    void CapturePerformanceMetrics(FAuracronTestResult& Result);
    bool ValidateTestResult(const FAuracronTestResult& Result);
    void LogTestResult(const FAuracronTestResult& Result);
    FString FormatTestReport(const FAuracronTestSuiteResult& SuiteResult);
};
