// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionGrid.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionGrid_generated_h
#error "AuracronWorldPartitionGrid.generated.h already included, missing '#pragma once' in AuracronWorldPartitionGrid.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionGrid_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionGridManager;
class UWorld;
struct FAuracronGridCell;
struct FAuracronGridConfiguration;
struct FAuracronGridSpatialQueryResult;
struct FAuracronGridStatistics;
struct FAuracronSpatialQueryParams;

// ********** Begin ScriptStruct FAuracronGridConfiguration ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_99_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGridConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGridConfiguration;
// ********** End ScriptStruct FAuracronGridConfiguration ******************************************

// ********** Begin ScriptStruct FAuracronGridCell *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_181_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGridCell_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGridCell;
// ********** End ScriptStruct FAuracronGridCell ***************************************************

// ********** Begin ScriptStruct FAuracronSpatialQueryParams ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_256_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSpatialQueryParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSpatialQueryParams;
// ********** End ScriptStruct FAuracronSpatialQueryParams *****************************************

// ********** Begin ScriptStruct FAuracronGridSpatialQueryResult ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_313_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGridSpatialQueryResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGridSpatialQueryResult;
// ********** End ScriptStruct FAuracronGridSpatialQueryResult *************************************

// ********** Begin ScriptStruct FAuracronGridStatistics *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_352_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGridStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGridStatistics;
// ********** End ScriptStruct FAuracronGridStatistics *********************************************

// ********** Begin Delegate FOnCellCreated ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_577_DELEGATE \
static void FOnCellCreated_DelegateWrapper(const FMulticastScriptDelegate& OnCellCreated, FAuracronGridCell Cell);


// ********** End Delegate FOnCellCreated **********************************************************

// ********** Begin Delegate FOnCellRemoved ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_578_DELEGATE \
static void FOnCellRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnCellRemoved, const FString& CellId);


// ********** End Delegate FOnCellRemoved **********************************************************

// ********** Begin Delegate FOnCellSubdivided *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_579_DELEGATE \
static void FOnCellSubdivided_DelegateWrapper(const FMulticastScriptDelegate& OnCellSubdivided, const FString& ParentCellId, const TArray<FAuracronGridCell>& ChildCells);


// ********** End Delegate FOnCellSubdivided *******************************************************

// ********** Begin Delegate FOnActorAddedToCell ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_580_DELEGATE \
static void FOnActorAddedToCell_DelegateWrapper(const FMulticastScriptDelegate& OnActorAddedToCell, const FString& ActorId, const FString& CellId);


// ********** End Delegate FOnActorAddedToCell *****************************************************

// ********** Begin Delegate FOnCellMerged *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_637_DELEGATE \
static void FOnCellMerged_DelegateWrapper(const FMulticastScriptDelegate& OnCellMerged, const FString& MergedCellId, const TArray<FString>& OriginalCellIds);


// ********** End Delegate FOnCellMerged ***********************************************************

// ********** Begin Class UAuracronWorldPartitionGridManager ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_421_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogGridState); \
	DECLARE_FUNCTION(execDrawDebugCell); \
	DECLARE_FUNCTION(execDrawDebugGrid); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetAverageCellDensity); \
	DECLARE_FUNCTION(execGetPopulatedCellCount); \
	DECLARE_FUNCTION(execGetTotalCellCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetGridStatistics); \
	DECLARE_FUNCTION(execGetCellSize); \
	DECLARE_FUNCTION(execSetCellSize); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execCellIdToCoordinates); \
	DECLARE_FUNCTION(execCoordinatesToCellId); \
	DECLARE_FUNCTION(execGridToWorldCoordinates); \
	DECLARE_FUNCTION(execWorldToGridCoordinates); \
	DECLARE_FUNCTION(execQueryCellsByRay); \
	DECLARE_FUNCTION(execQueryCellsByBox); \
	DECLARE_FUNCTION(execQueryCellsBySphere); \
	DECLARE_FUNCTION(execQueryCellsByPoint); \
	DECLARE_FUNCTION(execExecuteSpatialQuery); \
	DECLARE_FUNCTION(execGetActorsInRadius); \
	DECLARE_FUNCTION(execGetActorsInCell); \
	DECLARE_FUNCTION(execUpdateActorLocation); \
	DECLARE_FUNCTION(execRemoveActorFromGrid); \
	DECLARE_FUNCTION(execAddActorToGrid); \
	DECLARE_FUNCTION(execAutoMergeCells); \
	DECLARE_FUNCTION(execAutoSubdivideCells); \
	DECLARE_FUNCTION(execMergeCells); \
	DECLARE_FUNCTION(execSubdivideCell); \
	DECLARE_FUNCTION(execGetAllCells); \
	DECLARE_FUNCTION(execGetCellsInBox); \
	DECLARE_FUNCTION(execGetCellsInRadius); \
	DECLARE_FUNCTION(execGetCellAtLocation); \
	DECLARE_FUNCTION(execGetCell); \
	DECLARE_FUNCTION(execRemoveCell); \
	DECLARE_FUNCTION(execCreateCell); \
	DECLARE_FUNCTION(execOptimizeGrid); \
	DECLARE_FUNCTION(execRebuildGrid); \
	DECLARE_FUNCTION(execDestroyGrid); \
	DECLARE_FUNCTION(execCreateGrid); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionGridManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_421_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionGridManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionGridManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionGridManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionGridManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionGridManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionGridManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_421_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionGridManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionGridManager(UAuracronWorldPartitionGridManager&&) = delete; \
	UAuracronWorldPartitionGridManager(const UAuracronWorldPartitionGridManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionGridManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionGridManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionGridManager) \
	NO_API virtual ~UAuracronWorldPartitionGridManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_418_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_421_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_421_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_421_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h_421_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionGridManager;

// ********** End Class UAuracronWorldPartitionGridManager *****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionGrid_h

// ********** Begin Enum EAuracronGridSubdivisionType **********************************************
#define FOREACH_ENUM_EAURACRONGRIDSUBDIVISIONTYPE(op) \
	op(EAuracronGridSubdivisionType::Uniform) \
	op(EAuracronGridSubdivisionType::Adaptive) \
	op(EAuracronGridSubdivisionType::Hierarchical) \
	op(EAuracronGridSubdivisionType::QuadTree) \
	op(EAuracronGridSubdivisionType::Octree) \
	op(EAuracronGridSubdivisionType::Custom) 

enum class EAuracronGridSubdivisionType : uint8;
template<> struct TIsUEnumClass<EAuracronGridSubdivisionType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridSubdivisionType>();
// ********** End Enum EAuracronGridSubdivisionType ************************************************

// ********** Begin Enum EAuracronGridCellState ****************************************************
#define FOREACH_ENUM_EAURACRONGRIDCELLSTATE(op) \
	op(EAuracronGridCellState::Empty) \
	op(EAuracronGridCellState::Populated) \
	op(EAuracronGridCellState::Loading) \
	op(EAuracronGridCellState::Loaded) \
	op(EAuracronGridCellState::Unloading) \
	op(EAuracronGridCellState::Error) 

enum class EAuracronGridCellState : uint8;
template<> struct TIsUEnumClass<EAuracronGridCellState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridCellState>();
// ********** End Enum EAuracronGridCellState ******************************************************

// ********** Begin Enum EAuracronGridSpatialQueryType *********************************************
#define FOREACH_ENUM_EAURACRONGRIDSPATIALQUERYTYPE(op) \
	op(EAuracronGridSpatialQueryType::Point) \
	op(EAuracronGridSpatialQueryType::Sphere) \
	op(EAuracronGridSpatialQueryType::Box) \
	op(EAuracronGridSpatialQueryType::Frustum) \
	op(EAuracronGridSpatialQueryType::Ray) \
	op(EAuracronGridSpatialQueryType::Custom) 

enum class EAuracronGridSpatialQueryType : uint8;
template<> struct TIsUEnumClass<EAuracronGridSpatialQueryType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridSpatialQueryType>();
// ********** End Enum EAuracronGridSpatialQueryType ***********************************************

// ********** Begin Enum EAuracronGridOptimizationLevel ********************************************
#define FOREACH_ENUM_EAURACRONGRIDOPTIMIZATIONLEVEL(op) \
	op(EAuracronGridOptimizationLevel::None) \
	op(EAuracronGridOptimizationLevel::Basic) \
	op(EAuracronGridOptimizationLevel::Moderate) \
	op(EAuracronGridOptimizationLevel::Aggressive) \
	op(EAuracronGridOptimizationLevel::Maximum) 

enum class EAuracronGridOptimizationLevel : uint8;
template<> struct TIsUEnumClass<EAuracronGridOptimizationLevel> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronGridOptimizationLevel>();
// ********** End Enum EAuracronGridOptimizationLevel **********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
