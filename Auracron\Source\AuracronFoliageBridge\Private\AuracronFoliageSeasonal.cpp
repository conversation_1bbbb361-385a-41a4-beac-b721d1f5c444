// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Seasonal Changes System Implementation
// Bridge 4.8: Foliage - Seasonal Changes

#include "AuracronFoliageSeasonal.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageWind.h"
#include "AuracronFoliageBridge.h"

// UE5.6 Material includes
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Foliage includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageType.h"
#include "FoliageInstancedStaticMeshComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Curve includes
#include "Curves/CurveFloat.h"
#include "Curves/CurveLinearColor.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Color.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// FOLIAGE SEASONAL MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageSeasonalManager* UAuracronFoliageSeasonalManager::Instance = nullptr;

UAuracronFoliageSeasonalManager* UAuracronFoliageSeasonalManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageSeasonalManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageSeasonalManager::Initialize(const FAuracronSeasonalConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Seasonal Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    SeasonalColors.Empty();
    GrowthSimulations.Empty();
    LifecycleManagement.Empty();

    // Initialize seasonal state
    CurrentSeason = Configuration.DefaultSeason;
    SeasonProgress = 0.0f;
    GameTime = 0.0f;
    CurrentGameDay = 0;

    // Initialize performance data
    PerformanceData = FAuracronSeasonalPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastSeasonalUpdate = 0.0f;
    LastColorUpdate = 0.0f;
    LastGrowthUpdate = 0.0f;
    LastLifecycleUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    // Initialize material parameter collection
    if (Configuration.SeasonalParameterCollection.IsValid())
    {
        SeasonalParameterCollection = Configuration.SeasonalParameterCollection.LoadSynchronous();
        if (SeasonalParameterCollection.IsValid() && ManagedWorld.IsValid())
        {
            SeasonalParameterInstance = ManagedWorld->GetParameterCollectionInstance(SeasonalParameterCollection.Get());
        }
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Seasonal Manager initialized with season: %s, duration: %.1f days"), 
                              *UEnum::GetValueAsString(Configuration.DefaultSeason),
                              Configuration.SeasonDurationDays);
}

void UAuracronFoliageSeasonalManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    SeasonalColors.Empty();
    GrowthSimulations.Empty();
    LifecycleManagement.Empty();

    // Reset references
    ManagedWorld.Reset();
    SeasonalParameterCollection.Reset();
    SeasonalParameterInstance.Reset();
    BiomeManager.Reset();
    WindManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Seasonal Manager shutdown completed"));
}

bool UAuracronFoliageSeasonalManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageSeasonalManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update seasonal progress
    UpdateSeasonalProgressInternal(DeltaTime);

    // Update color variation
    if (Configuration.bEnableColorVariation)
    {
        LastColorUpdate += DeltaTime;
        if (LastColorUpdate >= Configuration.SeasonalUpdateInterval)
        {
            UpdateColorVariationInternal(DeltaTime);
            LastColorUpdate = 0.0f;
        }
    }

    // Update growth simulations
    if (Configuration.bEnableGrowthSimulation)
    {
        LastGrowthUpdate += DeltaTime;
        if (LastGrowthUpdate >= Configuration.SeasonalUpdateInterval)
        {
            UpdateGrowthSimulations(DeltaTime);
            LastGrowthUpdate = 0.0f;
        }
    }

    // Update lifecycle management
    if (Configuration.bEnableLifecycleManagement)
    {
        LastLifecycleUpdate += DeltaTime;
        if (LastLifecycleUpdate >= Configuration.SeasonalUpdateInterval)
        {
            UpdateLifecycleManagement(DeltaTime);
            LastLifecycleUpdate = 0.0f;
        }
    }

    // Update density changes
    if (Configuration.bEnableDensityChanges)
    {
        UpdateDensityChanges(DeltaTime);
    }

    // Update material parameters
    UpdateMaterialParametersInternal();

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageSeasonalManager::SetConfiguration(const FAuracronSeasonalConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Seasonal configuration updated"));
}

FAuracronSeasonalConfiguration UAuracronFoliageSeasonalManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliageSeasonalManager::SetCurrentSeason(EAuracronSeasonType Season)
{
    if (CurrentSeason != Season)
    {
        EAuracronSeasonType PreviousSeason = CurrentSeason;
        CurrentSeason = Season;
        SeasonProgress = 0.0f;

        OnSeasonChanged.Broadcast(CurrentSeason);

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Season changed from %s to %s"), 
                                  *UEnum::GetValueAsString(PreviousSeason),
                                  *UEnum::GetValueAsString(CurrentSeason));
    }
}

EAuracronSeasonType UAuracronFoliageSeasonalManager::GetCurrentSeason() const
{
    return CurrentSeason;
}

float UAuracronFoliageSeasonalManager::GetSeasonProgress() const
{
    return SeasonProgress;
}

void UAuracronFoliageSeasonalManager::SetSeasonProgress(float Progress)
{
    SeasonProgress = FMath::Clamp(Progress, 0.0f, 1.0f);
}

void UAuracronFoliageSeasonalManager::AdvanceSeasonByDays(float Days)
{
    float DaysToProgress = Days / Configuration.SeasonDurationDays;
    SeasonProgress += DaysToProgress;

    while (SeasonProgress >= 1.0f)
    {
        SeasonProgress -= 1.0f;
        
        // Advance to next season
        switch (CurrentSeason)
        {
            case EAuracronSeasonType::Spring:
                SetCurrentSeason(EAuracronSeasonType::Summer);
                break;
            case EAuracronSeasonType::Summer:
                SetCurrentSeason(EAuracronSeasonType::Autumn);
                break;
            case EAuracronSeasonType::Autumn:
                SetCurrentSeason(EAuracronSeasonType::Winter);
                break;
            case EAuracronSeasonType::Winter:
                SetCurrentSeason(EAuracronSeasonType::Spring);
                break;
            default:
                break;
        }
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Advanced season by %.1f days, progress: %.2f"), Days, SeasonProgress);
}

void UAuracronFoliageSeasonalManager::SetTimeAcceleration(float Acceleration)
{
    Configuration.TimeAcceleration = FMath::Max(0.1f, Acceleration);
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Time acceleration set to: %.2f"), Configuration.TimeAcceleration);
}

float UAuracronFoliageSeasonalManager::GetTimeAcceleration() const
{
    return Configuration.TimeAcceleration;
}

float UAuracronFoliageSeasonalManager::GetCurrentGameTime() const
{
    return GameTime;
}

int32 UAuracronFoliageSeasonalManager::GetCurrentGameDay() const
{
    return CurrentGameDay;
}

void UAuracronFoliageSeasonalManager::SetSeasonalColors(const FString& FoliageTypeId, const FAuracronSeasonalColorData& ColorData)
{
    FScopeLock Lock(&SeasonalLock);

    FAuracronSeasonalColorData NewColorData = ColorData;
    NewColorData.ColorDataId = FoliageTypeId;

    SeasonalColors.Add(FoliageTypeId, NewColorData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Seasonal colors set for foliage type: %s"), *FoliageTypeId);
}

FAuracronSeasonalColorData UAuracronFoliageSeasonalManager::GetSeasonalColors(const FString& FoliageTypeId) const
{
    FScopeLock Lock(&SeasonalLock);

    if (const FAuracronSeasonalColorData* ColorData = SeasonalColors.Find(FoliageTypeId))
    {
        return *ColorData;
    }

    return FAuracronSeasonalColorData();
}

FLinearColor UAuracronFoliageSeasonalManager::GetCurrentSeasonalColor(const FString& FoliageTypeId, bool bUseAccentColor) const
{
    FScopeLock Lock(&SeasonalLock);

    const FAuracronSeasonalColorData* ColorData = SeasonalColors.Find(FoliageTypeId);
    if (!ColorData)
    {
        return FLinearColor::White;
    }

    return CalculateSeasonalColor(*ColorData, CurrentSeason, SeasonProgress);
}

void UAuracronFoliageSeasonalManager::ValidateConfiguration()
{
    // Validate time settings
    Configuration.SeasonDurationDays = FMath::Max(1.0f, Configuration.SeasonDurationDays);
    Configuration.DayLengthSeconds = FMath::Max(60.0f, Configuration.DayLengthSeconds);
    Configuration.TimeAcceleration = FMath::Max(0.1f, Configuration.TimeAcceleration);

    // Validate color settings
    Configuration.ColorTransitionSpeed = FMath::Max(0.1f, Configuration.ColorTransitionSpeed);
    Configuration.ColorVariationIntensity = FMath::Clamp(Configuration.ColorVariationIntensity, 0.0f, 2.0f);

    // Validate density settings
    Configuration.DensityChangeSpeed = FMath::Max(0.1f, Configuration.DensityChangeSpeed);
    Configuration.MaxDensityVariation = FMath::Clamp(Configuration.MaxDensityVariation, 0.0f, 1.0f);

    // Validate growth settings
    Configuration.GrowthRate = FMath::Max(0.1f, Configuration.GrowthRate);
    Configuration.MaxGrowthScale = FMath::Max(0.1f, Configuration.MaxGrowthScale);

    // Validate lifecycle settings
    Configuration.LifecycleSpeed = FMath::Max(0.1f, Configuration.LifecycleSpeed);

    // Validate performance settings
    Configuration.MaxSeasonalUpdatesPerFrame = FMath::Max(1, Configuration.MaxSeasonalUpdatesPerFrame);
    Configuration.SeasonalUpdateInterval = FMath::Max(0.01f, Configuration.SeasonalUpdateInterval);
    Configuration.MaxSeasonalDistance = FMath::Max(1000.0f, Configuration.MaxSeasonalDistance);
}

FString UAuracronFoliageSeasonalManager::GenerateGrowthId() const
{
    return FString::Printf(TEXT("Growth_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageSeasonalManager::GenerateLifecycleId() const
{
    return FString::Printf(TEXT("Lifecycle_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageSeasonalManager::UpdateSeasonalProgressInternal(float DeltaTime)
{
    // Update game time
    GameTime += DeltaTime * Configuration.TimeAcceleration;

    // Calculate current game day
    int32 NewGameDay = FMath::FloorToInt(GameTime / Configuration.DayLengthSeconds);
    if (NewGameDay != CurrentGameDay)
    {
        CurrentGameDay = NewGameDay;

        // Update season progress based on days passed
        float DaysInSeason = Configuration.SeasonDurationDays;
        float DayProgress = (CurrentGameDay % FMath::FloorToInt(DaysInSeason)) / DaysInSeason;

        if (FMath::Abs(DayProgress - SeasonProgress) > 0.01f)
        {
            SeasonProgress = DayProgress;

            // Check if we need to advance to next season
            if (SeasonProgress >= 1.0f)
            {
                AdvanceSeasonByDays(0.0f); // This will handle season transition
            }
        }
    }
}

void UAuracronFoliageSeasonalManager::UpdateColorVariationInternal(float DeltaTime)
{
    FScopeLock Lock(&SeasonalLock);

    for (auto& ColorPair : SeasonalColors)
    {
        const FString& FoliageTypeId = ColorPair.Key;
        FAuracronSeasonalColorData& ColorData = ColorPair.Value;

        // Calculate current seasonal color
        FLinearColor CurrentColor = CalculateSeasonalColor(ColorData, CurrentSeason, SeasonProgress);

        // Broadcast color update event
        OnColorVariationUpdated.Broadcast(FoliageTypeId, CurrentColor);
    }
}

void UAuracronFoliageSeasonalManager::UpdateGrowthSimulationInternal(FAuracronGrowthSimulationData& GrowthData, float DeltaTime)
{
    if (!GrowthData.bIsGrowing)
    {
        return;
    }

    // Calculate growth rate based on environmental factors
    float EffectiveGrowthRate = CalculateGrowthRate(GrowthData);

    // Update growth progress
    float GrowthIncrement = EffectiveGrowthRate * DeltaTime * Configuration.GrowthRate;
    GrowthData.GrowthProgress += GrowthIncrement;

    // Check for phase advancement
    if (GrowthData.PhaseDurations.Contains(GrowthData.CurrentPhase))
    {
        float PhaseDuration = GrowthData.PhaseDurations[GrowthData.CurrentPhase];
        float PhaseProgress = GrowthData.GrowthProgress / PhaseDuration;

        if (PhaseProgress >= 1.0f)
        {
            AdvanceGrowthPhase(GrowthData);
        }
    }

    // Update scale based on current phase
    if (GrowthData.PhaseScales.Contains(GrowthData.CurrentPhase))
    {
        float TargetScale = GrowthData.PhaseScales[GrowthData.CurrentPhase] * GrowthData.MaxScale;
        GrowthData.CurrentScale = FMath::FInterpTo(GrowthData.CurrentScale, TargetScale, DeltaTime, 2.0f);
    }

    GrowthData.LastGrowthUpdate = FPlatformTime::Seconds();
}

void UAuracronFoliageSeasonalManager::UpdateLifecycleManagementInternal(FAuracronLifecycleData& LifecycleData, float DeltaTime)
{
    if (!LifecycleData.bIsAlive)
    {
        return;
    }

    // Update age
    float DaysIncrement = (DeltaTime * Configuration.TimeAcceleration) / Configuration.DayLengthSeconds;
    LifecycleData.CurrentAge += DaysIncrement * Configuration.LifecycleSpeed;

    // Update lifecycle progress
    LifecycleData.LifecycleProgress = LifecycleData.CurrentAge / LifecycleData.TotalLifespan;

    // Process lifecycle events
    ProcessLifecycleEvents(LifecycleData);

    // Update health
    if (LifecycleData.CurrentAge > LifecycleData.TotalLifespan * 0.8f)
    {
        // Start health decay in old age
        float HealthDecay = LifecycleData.HealthDecayRate * DaysIncrement;
        LifecycleData.Health = FMath::Max(0.0f, LifecycleData.Health - HealthDecay);

        if (LifecycleData.Health <= 0.0f)
        {
            LifecycleData.bIsAlive = false;
            TriggerLifecycleEvent(LifecycleData.LifecycleId, EAuracronLifecycleEvent::Death);
        }
    }

    LifecycleData.LastLifecycleUpdate = FDateTime::Now();
}

void UAuracronFoliageSeasonalManager::UpdateMaterialParametersInternal()
{
    if (!SeasonalParameterInstance.IsValid())
    {
        return;
    }

    UMaterialParameterCollectionInstance* ParamInstance = SeasonalParameterInstance.Get();

    // Update seasonal parameters
    ParamInstance->SetScalarParameterValue(*Configuration.SeasonProgressParameterName, SeasonProgress);
    ParamInstance->SetScalarParameterValue(*Configuration.SeasonTypeParameterName, static_cast<float>(CurrentSeason));
    ParamInstance->SetScalarParameterValue(*Configuration.LifecycleProgressParameterName, GameTime);

    // Update performance counter
    PerformanceData.MaterialParameterUpdates++;
}

void UAuracronFoliageSeasonalManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&SeasonalLock);

    // Reset counters
    PerformanceData.TotalSeasonalInstances = SeasonalColors.Num();
    PerformanceData.ActiveSeasonalInstances = 0;
    PerformanceData.GrowingInstances = 0;
    PerformanceData.LifecycleInstances = LifecycleManagement.Num();

    // Count active instances
    for (const auto& ColorPair : SeasonalColors)
    {
        PerformanceData.ActiveSeasonalInstances++;
    }

    // Count growing instances
    for (const auto& GrowthPair : GrowthSimulations)
    {
        if (GrowthPair.Value.bIsGrowing)
        {
            PerformanceData.GrowingInstances++;
        }
    }

    // Calculate real memory usage using UE5.6 memory tracking
    PerformanceData.MemoryUsageMB = CalculateRealSeasonalMemoryUsage();
}

FLinearColor UAuracronFoliageSeasonalManager::CalculateSeasonalColor(const FAuracronSeasonalColorData& ColorData, EAuracronSeasonType Season, float Progress) const
{
    FLinearColor BaseColor = FLinearColor::White;
    FLinearColor AccentColor = FLinearColor::White;

    // Get base colors for current season
    switch (Season)
    {
        case EAuracronSeasonType::Spring:
            BaseColor = ColorData.SpringBaseColor;
            AccentColor = ColorData.SpringAccentColor;
            break;
        case EAuracronSeasonType::Summer:
            BaseColor = ColorData.SummerBaseColor;
            AccentColor = ColorData.SummerAccentColor;
            break;
        case EAuracronSeasonType::Autumn:
            BaseColor = ColorData.AutumnBaseColor;
            AccentColor = ColorData.AutumnAccentColor;
            break;
        case EAuracronSeasonType::Winter:
            BaseColor = ColorData.WinterBaseColor;
            AccentColor = ColorData.WinterAccentColor;
            break;
        default:
            BaseColor = ColorData.SpringBaseColor;
            AccentColor = ColorData.SpringAccentColor;
            break;
    }

    // Interpolate between base and accent color based on progress
    FLinearColor FinalColor = FMath::Lerp(BaseColor, AccentColor, Progress);

    // Apply color variation
    if (ColorData.ColorVariationRange > 0.0f)
    {
        float VariationFactor = FMath::FRandRange(-ColorData.ColorVariationRange, ColorData.ColorVariationRange);
        FinalColor.R = FMath::Clamp(FinalColor.R + VariationFactor, 0.0f, 1.0f);
        FinalColor.G = FMath::Clamp(FinalColor.G + VariationFactor, 0.0f, 1.0f);
        FinalColor.B = FMath::Clamp(FinalColor.B + VariationFactor, 0.0f, 1.0f);
    }

    // Apply saturation and brightness multipliers
    FinalColor.R *= ColorData.ColorSaturationMultiplier * ColorData.ColorBrightnessMultiplier;
    FinalColor.G *= ColorData.ColorSaturationMultiplier * ColorData.ColorBrightnessMultiplier;
    FinalColor.B *= ColorData.ColorSaturationMultiplier * ColorData.ColorBrightnessMultiplier;

    return FinalColor;
}
