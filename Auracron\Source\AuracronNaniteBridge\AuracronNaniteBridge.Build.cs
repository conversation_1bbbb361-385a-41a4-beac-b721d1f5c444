// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Geometria Virtualizada Nanite Bridge Build Configuration
using UnrealBuildTool;
public class AuracronNaniteBridge : ModuleRules
{
    public AuracronNaniteBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "RenderCore",
                "RHI","Landscape",
                "Foliage","MeshDescription",
                "StaticMeshDescription",
                "MeshConversion",
                "MeshUtilities",
                "MeshUtilitiesCommon",
                "GeometryCore",
                "DynamicMesh",
                "ModelingComponents",
                "ModelingOperators",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "UnrealEd",
                "PropertyEditor",
                "KismetCompiler","BlueprintGraph",
                "Kismet",
                "ToolMenus",
                "ApplicationCore",
                "RenderCore",
                "RHI","Json","AudioMixer",
                "SignalProcessing","NaniteUtilities",
                "StaticMeshEditor",
                "MeshPaint",
                "MeshPaint",
                "LevelEditor",
                "SceneOutliner",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "GraphEditor",
                "KismetWidgets",
                "MaterialEditor",
                "UnrealEd",
                "ToolMenus",
                "EditorSubsystem",
                "AssetTools",
                "AssetRegistry",
                "ContentBrowser",
                "EditorStyle",
                "EditorWidgets",
                "SourceControl",
                "TextureCompressor",
                "ImageCore",
                "ImageWrapper",
                "RawMesh",
                "MeshBuilder",
                "MeshReductionInterface","ProxyLODMeshReduction"
            }
        );
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_NANITE=1");
        PublicDefinitions.Add("WITH_VIRTUALIZED_GEOMETRY=1");
        PublicDefinitions.Add("WITH_MESH_STREAMING=1");
        PublicDefinitions.Add("WITH_LOD_STREAMING=1");
        PublicDefinitions.Add("WITH_DYNAMIC_MESH=1");
        PublicDefinitions.Add("WITH_PROCEDURAL_MESH=1");
        PublicDefinitions.Add("WITH_MESH_OPTIMIZATION=1");
        PublicDefinitions.Add("WITH_MESH_SIMPLIFICATION=1");
        // Nanite features
        PublicDefinitions.Add("AURACRON_NANITE_MESHES=1");
        PublicDefinitions.Add("AURACRON_VIRTUALIZED_GEOMETRY=1");
        PublicDefinitions.Add("AURACRON_DYNAMIC_LOD=1");
        PublicDefinitions.Add("AURACRON_MESH_STREAMING=1");
        PublicDefinitions.Add("AURACRON_PROCEDURAL_GENERATION=1");
        PublicDefinitions.Add("AURACRON_REALM_GEOMETRY=1");
        PublicDefinitions.Add("AURACRON_DESTRUCTIBLE_NANITE=1");
        PublicDefinitions.Add("AURACRON_ADAPTIVE_DETAIL=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_NANITE=0"); // Nanite não suportado em mobile
            PublicDefinitions.Add("AURACRON_FALLBACK_GEOMETRY=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_NANITE=0");
            PublicDefinitions.Add("AURACRON_FALLBACK_GEOMETRY=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_NANITE_DEBUG=1");
            PublicDefinitions.Add("AURACRON_GEOMETRY_DEBUG=1");
            PublicDefinitions.Add("AURACRON_LOD_DEBUG=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_NANITE_DEBUG=0");
            PublicDefinitions.Add("AURACRON_GEOMETRY_DEBUG=0");
            PublicDefinitions.Add("AURACRON_LOD_DEBUG=0");
        }
        // Performance optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_NANITE=1");
            PublicDefinitions.Add("AURACRON_AGGRESSIVE_CULLING=1");
            PublicDefinitions.Add("AURACRON_MESH_COMPRESSION=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_NANITE=0");
            PublicDefinitions.Add("AURACRON_AGGRESSIVE_CULLING=0");
            PublicDefinitions.Add("AURACRON_MESH_COMPRESSION=0");
        }
    }
}
