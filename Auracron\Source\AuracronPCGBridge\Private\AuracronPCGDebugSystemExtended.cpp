// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Debug System Extended Implementation
// Bridge 2.14: PCG Framework - Debugging Tools

#include "AuracronPCGDebugSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "HAL/IConsoleManager.h"

// =============================================================================
// GRAPH VISUALIZER IMPLEMENTATION
// =============================================================================

void UAuracronPCGGraphVisualizer::VisualizeGraph(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Calculate node layout
    TMap<FString, FVector> NodeLayout = CalculateNodeLayout(Graph, CenterLocation);
    
    // Draw nodes and connections
    DrawNodeConnections(World, Graph, CenterLocation);
    DrawNodeLabels(World, Graph, CenterLocation);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Graph visualization drawn at location: %s"), *CenterLocation.ToString());
}

void UAuracronPCGGraphVisualizer::VisualizeNodeHierarchy(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Robust node hierarchy visualization using UE5.6 PCG graph analysis
    TMap<const UPCGNode*, int32> NodeLevels;
    TMap<const UPCGNode*, TArray<const UPCGNode*>> NodeChildren;
    TArray<const UPCGNode*> RootNodes;
    
    // Analyze graph structure to build hierarchy
    const TArray<UPCGNode*>& GraphNodes = Graph->GetNodes();
    
    // Find root nodes (nodes with no input connections)
    for (const UPCGNode* Node : GraphNodes)
    {
        if (Node && Node->GetSettings())
        {
            bool bHasInputConnections = false;
            for (const UPCGPin* InputPin : Node->GetInputPins())
            {
                if (InputPin && InputPin->EdgeCount() > 0)
                {
                    bHasInputConnections = true;
                    break;
                }
            }
            
            if (!bHasInputConnections)
            {
                RootNodes.Add(Node);
                NodeLevels.Add(Node, 0);
            }
        }
    }
    
    // Build hierarchy levels using breadth-first traversal
    TQueue<const UPCGNode*> ProcessingQueue;
    for (const UPCGNode* RootNode : RootNodes)
    {
        ProcessingQueue.Enqueue(RootNode);
    }
    
    while (!ProcessingQueue.IsEmpty())
    {
        const UPCGNode* CurrentNode;
        ProcessingQueue.Dequeue(CurrentNode);
        
        int32 CurrentLevel = NodeLevels[CurrentNode];
        
        // Process output connections
        for (const UPCGPin* OutputPin : CurrentNode->GetOutputPins())
        {
            if (OutputPin)
            {
                for (const UPCGEdge* Edge : OutputPin->Edges)
                {
                    if (Edge && Edge->InputPin && Edge->InputPin->Node)
                    {
                        const UPCGNode* ChildNode = Edge->InputPin->Node;
                        
                        // Update child level if this path provides a deeper level
                        int32 NewLevel = CurrentLevel + 1;
                        if (!NodeLevels.Contains(ChildNode) || NodeLevels[ChildNode] < NewLevel)
                        {
                            NodeLevels.Add(ChildNode, NewLevel);
                            ProcessingQueue.Enqueue(ChildNode);
                        }
                        
                        // Track parent-child relationships
                        NodeChildren.FindOrAdd(CurrentNode).AddUnique(ChildNode);
                    }
                }
            }
        }
    }
    
    // Visualize hierarchy with level-based positioning
    const float LevelSpacing = 300.0f;
    const float NodeSpacing = 150.0f;
    
    TMap<int32, TArray<const UPCGNode*>> NodesByLevel;
    for (const auto& NodeLevel : NodeLevels)
    {
        NodesByLevel.FindOrAdd(NodeLevel.Value).Add(NodeLevel.Key);
    }
    
    // Draw nodes by level
    for (const auto& LevelNodes : NodesByLevel)
    {
        int32 Level = LevelNodes.Key;
        const TArray<const UPCGNode*>& NodesAtLevel = LevelNodes.Value;
        
        float LevelY = CenterLocation.Y + (Level * LevelSpacing);
        float StartX = CenterLocation.X - ((NodesAtLevel.Num() - 1) * NodeSpacing * 0.5f);
        
        for (int32 NodeIndex = 0; NodeIndex < NodesAtLevel.Num(); NodeIndex++)
        {
            const UPCGNode* Node = NodesAtLevel[NodeIndex];
            FVector NodeLocation = FVector(StartX + (NodeIndex * NodeSpacing), LevelY, CenterLocation.Z + (Level * 50.0f));
            
            // Draw node with level-based color
            FLinearColor LevelColor = FLinearColor::LerpUsingHSV(
                FLinearColor::Blue, 
                FLinearColor::Red, 
                FMath::Clamp(Level / 10.0f, 0.0f, 1.0f)
            );
            
            DrawNode(World, Node->GetSettings(), NodeLocation, LevelColor);
            
            // Draw hierarchy connections
            if (NodeChildren.Contains(Node))
            {
                for (const UPCGNode* ChildNode : NodeChildren[Node])
                {
                    if (NodeLevels.Contains(ChildNode))
                    {
                        int32 ChildLevel = NodeLevels[ChildNode];
                        const TArray<const UPCGNode*>& ChildLevelNodes = NodesByLevel[ChildLevel];
                        int32 ChildIndex = ChildLevelNodes.Find(ChildNode);
                        
                        if (ChildIndex != INDEX_NONE)
                        {
                            float ChildLevelY = CenterLocation.Y + (ChildLevel * LevelSpacing);
                            float ChildStartX = CenterLocation.X - ((ChildLevelNodes.Num() - 1) * NodeSpacing * 0.5f);
                            FVector ChildLocation = FVector(ChildStartX + (ChildIndex * NodeSpacing), ChildLevelY, CenterLocation.Z + (ChildLevel * 50.0f));
                            
                            DrawDebugLine(World, NodeLocation, ChildLocation, FColor::Yellow, false, 5.0f, 0, 2.0f);
                            DrawDebugSphere(World, FMath::Lerp(NodeLocation, ChildLocation, 0.5f), 5.0f, 8, FColor::Orange, false, 5.0f);
                        }
                    }
                }
            }
        }
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Node hierarchy visualization completed with %d levels and %d nodes"), NodesByLevel.Num(), GraphNodes.Num());
}

void UAuracronPCGGraphVisualizer::VisualizeDataFlow(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation, float AnimationTime)
{
    if (!World || !Graph)
    {
        return;
    }

    // Robust animated data flow visualization using UE5.6 PCG graph analysis
    float AnimationPhase = FMath::Fmod(AnimationTime, 2.0f * PI);
    const TArray<UPCGNode*>& GraphNodes = Graph->GetNodes();
    
    // Build data flow paths
    TMap<const UPCGNode*, FVector> NodePositions;
    TArray<TPair<const UPCGNode*, const UPCGNode*>> DataFlowConnections;
    
    // Calculate node positions for visualization
    for (int32 NodeIndex = 0; NodeIndex < GraphNodes.Num(); NodeIndex++)
    {
        const UPCGNode* Node = GraphNodes[NodeIndex];
        if (Node && Node->GetSettings())
        {
            FVector NodePosition = CalculateNodePosition(Node->GetSettings(), NodeIndex, GraphNodes.Num(), CenterLocation);
            NodePositions.Add(Node, NodePosition);
        }
    }
    
    // Collect all data flow connections
    for (const UPCGNode* Node : GraphNodes)
    {
        if (Node)
        {
            for (const UPCGPin* OutputPin : Node->GetOutputPins())
            {
                if (OutputPin)
                {
                    for (const UPCGEdge* Edge : OutputPin->Edges)
                    {
                        if (Edge && Edge->InputPin && Edge->InputPin->Node)
                        {
                            DataFlowConnections.Add(TPair<const UPCGNode*, const UPCGNode*>(Node, Edge->InputPin->Node));
                        }
                    }
                }
            }
        }
    }
    
    // Animate data flow along connections
    for (const auto& Connection : DataFlowConnections)
    {
        const UPCGNode* SourceNode = Connection.Key;
        const UPCGNode* TargetNode = Connection.Value;
        
        if (NodePositions.Contains(SourceNode) && NodePositions.Contains(TargetNode))
        {
            FVector SourcePos = NodePositions[SourceNode];
            FVector TargetPos = NodePositions[TargetNode];
            
            // Draw connection line
            DrawDebugLine(World, SourcePos, TargetPos, FColor::Cyan, false, 0.1f, 0, 1.0f);
            
            // Animate data packets along the connection
            const int32 NumPackets = 3;
            for (int32 PacketIndex = 0; PacketIndex < NumPackets; PacketIndex++)
            {
                float PacketOffset = (PacketIndex / float(NumPackets)) * 2.0f * PI;
                float PacketPhase = FMath::Fmod(AnimationPhase + PacketOffset, 2.0f * PI);
                float PacketProgress = (FMath::Sin(PacketPhase) + 1.0f) * 0.5f; // Normalize to [0,1]
                
                FVector PacketPosition = FMath::Lerp(SourcePos, TargetPos, PacketProgress);
                
                // Draw animated data packet
                float PacketSize = 8.0f + 4.0f * FMath::Sin(AnimationPhase * 3.0f + PacketIndex);
                FColor PacketColor = FColor::MakeRedToGreenColorFromScalar(PacketProgress);
                
                DrawDebugSphere(World, PacketPosition, PacketSize, 8, PacketColor, false, 0.1f);
                
                // Add particle trail effect
                for (int32 TrailIndex = 1; TrailIndex <= 5; TrailIndex++)
                {
                    float TrailProgress = FMath::Max(0.0f, PacketProgress - (TrailIndex * 0.05f));
                    FVector TrailPosition = FMath::Lerp(SourcePos, TargetPos, TrailProgress);
                    float TrailAlpha = FMath::Max(0.0f, 1.0f - (TrailIndex * 0.2f));
                    
                    FColor TrailColor = PacketColor;
                    TrailColor.A = uint8(255 * TrailAlpha);
                    
                    DrawDebugSphere(World, TrailPosition, PacketSize * 0.5f, 6, TrailColor, false, 0.1f);
                }
            }
            
            // Draw data type information
            FVector MidPoint = (SourcePos + TargetPos) * 0.5f;
            FString DataTypeInfo = TEXT("PCGData");
            
            // Try to get actual data type from pin
            for (const UPCGPin* OutputPin : SourceNode->GetOutputPins())
            {
                if (OutputPin && OutputPin->Edges.Num() > 0)
                {
                    // Get data type from pin properties
                    if (OutputPin->Properties.AllowedTypes != EPCGDataType::None)
                    {
                        if (OutputPin->Properties.AllowedTypes & EPCGDataType::Point)
                        {
                            DataTypeInfo = TEXT("Points");
                        }
                        else if (OutputPin->Properties.AllowedTypes & EPCGDataType::Spline)
                        {
                            DataTypeInfo = TEXT("Splines");
                        }
                        else if (OutputPin->Properties.AllowedTypes & EPCGDataType::Surface)
                        {
                            DataTypeInfo = TEXT("Surface");
                        }
                        else if (OutputPin->Properties.AllowedTypes & EPCGDataType::Volume)
                        {
                            DataTypeInfo = TEXT("Volume");
                        }
                    }
                    break;
                }
            }
            
            DrawDebugString(World, MidPoint + FVector(0, 0, 30), DataTypeInfo, nullptr, FColor::White, 0.1f);
        }
    }
    
    // Draw data flow statistics
    FString FlowStats = FString::Printf(TEXT("Data Flow: %d connections, %d nodes"), DataFlowConnections.Num(), GraphNodes.Num());
    DrawDebugString(World, CenterLocation + FVector(0, 0, 200), FlowStats, nullptr, FColor::Yellow, 0.1f);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Data flow visualization rendered with %d connections at time %.3f"), DataFlowConnections.Num(), AnimationTime);
}

void UAuracronPCGGraphVisualizer::VisualizeExecutionOrder(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Robust execution order visualization using UE5.6 PCG dependency analysis
    const TArray<UPCGNode*>& GraphNodes = Graph->GetNodes();
    TArray<const UPCGNode*> ExecutionOrder;
    TMap<const UPCGNode*, int32> NodeDependencyCount;
    TMap<const UPCGNode*, TArray<const UPCGNode*>> NodeDependents;
    
    // Calculate dependencies for topological sort
    for (const UPCGNode* Node : GraphNodes)
    {
        if (Node)
        {
            int32 DependencyCount = 0;
            
            // Count input dependencies
            for (const UPCGPin* InputPin : Node->GetInputPins())
            {
                if (InputPin)
                {
                    for (const UPCGEdge* Edge : InputPin->Edges)
                    {
                        if (Edge && Edge->OutputPin && Edge->OutputPin->Node)
                        {
                            DependencyCount++;
                            NodeDependents.FindOrAdd(Edge->OutputPin->Node).AddUnique(Node);
                        }
                    }
                }
            }
            
            NodeDependencyCount.Add(Node, DependencyCount);
        }
    }
    
    // Perform topological sort to determine execution order
    TQueue<const UPCGNode*> ReadyNodes;
    
    // Find nodes with no dependencies
    for (const auto& NodeDep : NodeDependencyCount)
    {
        if (NodeDep.Value == 0)
        {
            ReadyNodes.Enqueue(NodeDep.Key);
        }
    }
    
    // Process nodes in dependency order
    while (!ReadyNodes.IsEmpty())
    {
        const UPCGNode* CurrentNode;
        ReadyNodes.Dequeue(CurrentNode);
        ExecutionOrder.Add(CurrentNode);
        
        // Update dependent nodes
        if (NodeDependents.Contains(CurrentNode))
        {
            for (const UPCGNode* DependentNode : NodeDependents[CurrentNode])
            {
                int32& DepCount = NodeDependencyCount[DependentNode];
                DepCount--;
                
                if (DepCount == 0)
                {
                    ReadyNodes.Enqueue(DependentNode);
                }
            }
        }
    }
    
    // Visualize execution order with numbered sequence
    const float ExecutionSpacing = 200.0f;
    const float TimelineHeight = 100.0f;
    
    for (int32 ExecutionIndex = 0; ExecutionIndex < ExecutionOrder.Num(); ExecutionIndex++)
    {
        const UPCGNode* Node = ExecutionOrder[ExecutionIndex];
        
        // Calculate position along execution timeline
        FVector ExecutionPosition = CenterLocation + FVector(
            (ExecutionIndex - ExecutionOrder.Num() * 0.5f) * ExecutionSpacing,
            0,
            TimelineHeight
        );
        
        // Draw execution step
        FLinearColor ExecutionColor = FLinearColor::LerpUsingHSV(
            FLinearColor::Green,
            FLinearColor::Red,
            ExecutionIndex / float(FMath::Max(1, ExecutionOrder.Num() - 1))
        );
        
        DrawNode(World, Node->GetSettings(), ExecutionPosition, ExecutionColor);
        
        // Draw execution order number
        FString ExecutionNumber = FString::Printf(TEXT("%d"), ExecutionIndex + 1);
        DrawDebugString(World, ExecutionPosition + FVector(0, 0, 60), ExecutionNumber, nullptr, FColor::White, 5.0f, true);
        
        // Draw timeline connection
        if (ExecutionIndex > 0)
        {
            FVector PrevPosition = CenterLocation + FVector(
                ((ExecutionIndex - 1) - ExecutionOrder.Num() * 0.5f) * ExecutionSpacing,
                0,
                TimelineHeight
            );
            
            DrawDebugLine(World, PrevPosition, ExecutionPosition, FColor::Orange, false, 5.0f, 0, 3.0f);
            
            // Draw arrow indicating direction
            FVector ArrowDirection = (ExecutionPosition - PrevPosition).GetSafeNormal();
            FVector ArrowTip = ExecutionPosition - ArrowDirection * 30.0f;
            FVector ArrowLeft = ArrowTip + FVector(ArrowDirection.Y, -ArrowDirection.X, 0) * 10.0f;
            FVector ArrowRight = ArrowTip + FVector(-ArrowDirection.Y, ArrowDirection.X, 0) * 10.0f;
            
            DrawDebugLine(World, ArrowTip, ArrowLeft, FColor::Orange, false, 5.0f, 0, 2.0f);
            DrawDebugLine(World, ArrowTip, ArrowRight, FColor::Orange, false, 5.0f, 0, 2.0f);
        }
        
        // Draw node name and execution info
        FString NodeInfo = GetNodeDisplayName(Node->GetSettings());
        DrawDebugString(World, ExecutionPosition + FVector(0, 0, -40), NodeInfo, nullptr, FColor::Cyan, 5.0f);
        
        // Draw dependency information
        int32 OriginalDependencyCount = 0;
        for (const UPCGPin* InputPin : Node->GetInputPins())
        {
            if (InputPin)
            {
                OriginalDependencyCount += InputPin->Edges.Num();
            }
        }
        
        if (OriginalDependencyCount > 0)
        {
            FString DependencyInfo = FString::Printf(TEXT("Deps: %d"), OriginalDependencyCount);
            DrawDebugString(World, ExecutionPosition + FVector(0, 0, -60), DependencyInfo, nullptr, FColor::Yellow, 5.0f);
        }
    }
    
    // Draw execution timeline base
    if (ExecutionOrder.Num() > 1)
    {
        FVector TimelineStart = CenterLocation + FVector(
            -ExecutionOrder.Num() * 0.5f * ExecutionSpacing,
            0,
            TimelineHeight - 50.0f
        );
        FVector TimelineEnd = CenterLocation + FVector(
            ExecutionOrder.Num() * 0.5f * ExecutionSpacing,
            0,
            TimelineHeight - 50.0f
        );
        
        DrawDebugLine(World, TimelineStart, TimelineEnd, FColor::White, false, 5.0f, 0, 5.0f);
        
        // Draw timeline labels
        DrawDebugString(World, TimelineStart + FVector(0, 0, -20), TEXT("Start"), nullptr, FColor::Green, 5.0f);
        DrawDebugString(World, TimelineEnd + FVector(0, 0, -20), TEXT("End"), nullptr, FColor::Red, 5.0f);
    }
    
    // Draw execution statistics
    FString ExecutionStats = FString::Printf(TEXT("Execution Order: %d nodes, %d steps"), GraphNodes.Num(), ExecutionOrder.Num());
    DrawDebugString(World, CenterLocation + FVector(0, 0, 250), ExecutionStats, nullptr, FColor::White, 5.0f);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution order visualization completed with %d nodes in sequence"), ExecutionOrder.Num());
}

void UAuracronPCGGraphVisualizer::DrawNode(UWorld* World, const UPCGSettings* NodeSettings, const FVector& Location, const FLinearColor& Color)
{
    if (!World || !NodeSettings)
    {
        return;
    }

    // Draw node as a box
    FVector BoxExtent(50.0f, 50.0f, 25.0f);
    DrawDebugBox(World, Location, BoxExtent, Color.ToFColor(true), false, 5.0f, 0, 2.0f);
    
    // Draw node name
    FString NodeName = GetNodeDisplayName(NodeSettings);
    DrawDebugString(World, Location + FVector(0, 0, 30), NodeName, nullptr, Color.ToFColor(true), 5.0f);
}

void UAuracronPCGGraphVisualizer::DrawNodeConnections(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Draw actual node connections using UE5.6 PCG graph structure
    const TArray<UPCGNode*>& GraphNodes = Graph->GetNodes();
    TMap<const UPCGNode*, FVector> NodePositions;
    
    // Calculate positions for all nodes
    for (int32 NodeIndex = 0; NodeIndex < GraphNodes.Num(); NodeIndex++)
    {
        if (GraphNodes[NodeIndex] && GraphNodes[NodeIndex]->GetSettings())
        {
            FVector NodePosition = CalculateNodePosition(GraphNodes[NodeIndex]->GetSettings(), NodeIndex, GraphNodes.Num(), CenterLocation);
            NodePositions.Add(GraphNodes[NodeIndex], NodePosition);
        }
    }
    
    // Draw connections between nodes
    for (const UPCGNode* Node : GraphNodes)
    {
        if (!Node || !NodePositions.Contains(Node))
        {
            continue;
        }
        
        FVector FromPosition = NodePositions[Node];
        
        // Draw output connections
        for (const UPCGPin* OutputPin : Node->GetOutputPins())
        {
            if (!OutputPin)
            {
                continue;
            }
            
            for (const UPCGEdge* Edge : OutputPin->Edges)
            {
                if (Edge && Edge->InputPin && Edge->InputPin->Node && NodePositions.Contains(Edge->InputPin->Node))
                {
                    FVector ToPosition = NodePositions[Edge->InputPin->Node];
                    
                    // Determine connection color based on data type
                    FLinearColor ConnectionColor = FLinearColor::White;
                    if (OutputPin->Properties.Label.ToString().Contains(TEXT("Point")))
                    {
                        ConnectionColor = FLinearColor::Green;
                    }
                    else if (OutputPin->Properties.Label.ToString().Contains(TEXT("Spatial")))
                    {
                        ConnectionColor = FLinearColor::Blue;
                    }
                    else if (OutputPin->Properties.Label.ToString().Contains(TEXT("Param")))
                    {
                        ConnectionColor = FLinearColor::Yellow;
                    }
                    
                    // Draw connection line with arrow
                    DrawDebugLine(World, FromPosition, ToPosition, ConnectionColor.ToFColor(true), false, -1.0f, 0, 2.0f);
                    
                    // Draw arrow head
                    FVector Direction = (ToPosition - FromPosition).GetSafeNormal();
                    FVector ArrowBase = ToPosition - Direction * 20.0f;
                    FVector ArrowSide1 = ArrowBase + FVector::CrossProduct(Direction, FVector::UpVector) * 10.0f;
                    FVector ArrowSide2 = ArrowBase - FVector::CrossProduct(Direction, FVector::UpVector) * 10.0f;
                    
                    DrawDebugLine(World, ToPosition, ArrowSide1, ConnectionColor.ToFColor(true), false, -1.0f, 0, 2.0f);
                    DrawDebugLine(World, ToPosition, ArrowSide2, ConnectionColor.ToFColor(true), false, -1.0f, 0, 2.0f);
                    
                    // Draw data flow indicator (animated dot)
                    float AnimationOffset = FMath::Fmod(World->GetTimeSeconds() * 100.0f, 100.0f) / 100.0f;
                    FVector FlowPosition = FMath::Lerp(FromPosition, ToPosition, AnimationOffset);
                    DrawDebugSphere(World, FlowPosition, 3.0f, 8, ConnectionColor.ToFColor(true), false, -1.0f, 0);
                }
            }
        }
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drew %d node connections"), GraphNodes.Num());
}

void UAuracronPCGGraphVisualizer::DrawNodeLabels(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Draw actual node labels using UE5.6 PCG graph structure
    const TArray<UPCGNode*>& GraphNodes = Graph->GetNodes();
    
    for (int32 NodeIndex = 0; NodeIndex < GraphNodes.Num(); NodeIndex++)
    {
        const UPCGNode* Node = GraphNodes[NodeIndex];
        if (!Node || !Node->GetSettings())
        {
            continue;
        }
        
        FVector NodePosition = CalculateNodePosition(Node->GetSettings(), NodeIndex, GraphNodes.Num(), CenterLocation);
        FString NodeDisplayName = GetNodeDisplayName(Node->GetSettings());
        FLinearColor NodeColor = GetNodeColor(Node->GetSettings());
        
        // Draw node as a box
        FVector BoxExtent(50.0f, 30.0f, 10.0f);
        DrawDebugBox(World, NodePosition, BoxExtent, NodeColor.ToFColor(true), false, -1.0f, 0, 2.0f);
        
        // Draw node label above the box
        FVector LabelPosition = NodePosition + FVector(0, 0, BoxExtent.Z + 20.0f);
        DrawDebugString(World, LabelPosition, NodeDisplayName, nullptr, NodeColor.ToFColor(true), -1.0f, true, 1.2f);
        
        // Draw node type indicator
        FString NodeType = Node->GetSettings()->GetClass()->GetName();
        if (NodeType.StartsWith(TEXT("UPCG")))
        {
            NodeType = NodeType.RightChop(4); // Remove "UPCG" prefix
        }
        if (NodeType.EndsWith(TEXT("Settings")))
        {
            NodeType = NodeType.LeftChop(8); // Remove "Settings" suffix
        }
        
        FVector TypeLabelPosition = NodePosition + FVector(0, 0, -BoxExtent.Z - 15.0f);
        DrawDebugString(World, TypeLabelPosition, NodeType, nullptr, FColor::Gray, -1.0f, true, 0.8f);
        
        // Draw input/output pin indicators
        const TArray<UPCGPin*>& InputPins = Node->GetInputPins();
        const TArray<UPCGPin*>& OutputPins = Node->GetOutputPins();
        
        // Draw input pins on the left
        for (int32 PinIndex = 0; PinIndex < InputPins.Num(); PinIndex++)
        {
            if (InputPins[PinIndex])
            {
                float PinOffset = (PinIndex - (InputPins.Num() - 1) * 0.5f) * 15.0f;
                FVector PinPosition = NodePosition + FVector(-BoxExtent.X - 5.0f, PinOffset, 0);
                DrawDebugSphere(World, PinPosition, 3.0f, 8, FColor::Blue, false, -1.0f, 0);
                
                // Draw pin label
                FString PinLabel = InputPins[PinIndex]->Properties.Label.ToString();
                if (!PinLabel.IsEmpty())
                {
                    FVector PinLabelPos = PinPosition + FVector(-20.0f, 0, 0);
                    DrawDebugString(World, PinLabelPos, PinLabel, nullptr, FColor::Blue, -1.0f, true, 0.6f);
                }
            }
        }
        
        // Draw output pins on the right
        for (int32 PinIndex = 0; PinIndex < OutputPins.Num(); PinIndex++)
        {
            if (OutputPins[PinIndex])
            {
                float PinOffset = (PinIndex - (OutputPins.Num() - 1) * 0.5f) * 15.0f;
                FVector PinPosition = NodePosition + FVector(BoxExtent.X + 5.0f, PinOffset, 0);
                DrawDebugSphere(World, PinPosition, 3.0f, 8, FColor::Red, false, -1.0f, 0);
                
                // Draw pin label
                FString PinLabel = OutputPins[PinIndex]->Properties.Label.ToString();
                if (!PinLabel.IsEmpty())
                {
                    FVector PinLabelPos = PinPosition + FVector(20.0f, 0, 0);
                    DrawDebugString(World, PinLabelPos, PinLabel, nullptr, FColor::Red, -1.0f, true, 0.6f);
                }
            }
        }
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drew labels for %d nodes"), GraphNodes.Num());
}

TMap<FString, FVector> UAuracronPCGGraphVisualizer::CalculateNodeLayout(const UPCGGraph* Graph, const FVector& CenterLocation)
{
    TMap<FString, FVector> NodeLayout;
    
    if (!Graph)
    {
        return NodeLayout;
    }

    // Advanced hierarchical layout calculation using UE5.6 PCG graph structure
    const TArray<UPCGNode*>& GraphNodes = Graph->GetNodes();
    
    if (GraphNodes.Num() == 0)
    {
        return NodeLayout;
    }
    
    // Analyze graph structure to determine hierarchy levels
    TMap<const UPCGNode*, int32> NodeLevels;
    TMap<int32, TArray<const UPCGNode*>> LevelNodes;
    TSet<const UPCGNode*> ProcessedNodes;
    
    // Find root nodes (nodes with no input connections)
    TArray<const UPCGNode*> RootNodes;
    for (const UPCGNode* Node : GraphNodes)
    {
        if (Node && Node->GetSettings())
        {
            bool bHasInputConnections = false;
            for (const UPCGPin* InputPin : Node->GetInputPins())
            {
                if (InputPin && InputPin->EdgeCount() > 0)
                {
                    bHasInputConnections = true;
                    break;
                }
            }
            
            if (!bHasInputConnections)
            {
                RootNodes.Add(Node);
                NodeLevels.Add(Node, 0);
                LevelNodes.FindOrAdd(0).Add(Node);
                ProcessedNodes.Add(Node);
            }
        }
    }
    
    // If no root nodes found, treat all nodes as level 0
    if (RootNodes.Num() == 0)
    {
        for (const UPCGNode* Node : GraphNodes)
        {
            if (Node && Node->GetSettings())
            {
                NodeLevels.Add(Node, 0);
                LevelNodes.FindOrAdd(0).Add(Node);
            }
        }
    }
    else
    {
        // Propagate levels through the graph using breadth-first traversal
        TQueue<const UPCGNode*> ProcessingQueue;
        for (const UPCGNode* RootNode : RootNodes)
        {
            ProcessingQueue.Enqueue(RootNode);
        }
        
        while (!ProcessingQueue.IsEmpty())
        {
            const UPCGNode* CurrentNode;
            ProcessingQueue.Dequeue(CurrentNode);
            
            int32 CurrentLevel = NodeLevels[CurrentNode];
            
            // Process output connections
            for (const UPCGPin* OutputPin : CurrentNode->GetOutputPins())
            {
                if (OutputPin)
                {
                    for (const UPCGEdge* Edge : OutputPin->Edges)
                    {
                        if (Edge && Edge->InputPin && Edge->InputPin->Node)
                        {
                            const UPCGNode* ChildNode = Edge->InputPin->Node;
                            
                            if (!ProcessedNodes.Contains(ChildNode))
                            {
                                int32 NewLevel = CurrentLevel + 1;
                                NodeLevels.Add(ChildNode, NewLevel);
                                LevelNodes.FindOrAdd(NewLevel).Add(ChildNode);
                                ProcessedNodes.Add(ChildNode);
                                ProcessingQueue.Enqueue(ChildNode);
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Calculate positions based on hierarchy
    float LevelSpacing = 300.0f; // Horizontal spacing between levels
    float NodeSpacing = 150.0f;  // Vertical spacing between nodes in same level
    
    for (const auto& LevelPair : LevelNodes)
    {
        int32 Level = LevelPair.Key;
        const TArray<const UPCGNode*>& NodesInLevel = LevelPair.Value;
        
        // Calculate X position for this level
        float LevelX = CenterLocation.X + (Level * LevelSpacing);
        
        // Calculate Y positions for nodes in this level
        float TotalHeight = (NodesInLevel.Num() - 1) * NodeSpacing;
        float StartY = CenterLocation.Y - (TotalHeight * 0.5f);
        
        for (int32 NodeIndex = 0; NodeIndex < NodesInLevel.Num(); NodeIndex++)
        {
            const UPCGNode* Node = NodesInLevel[NodeIndex];
            if (Node && Node->GetSettings())
            {
                FVector NodePosition;
                NodePosition.X = LevelX;
                NodePosition.Y = StartY + (NodeIndex * NodeSpacing);
                NodePosition.Z = CenterLocation.Z;
                
                // Add some variation based on node type
                FString NodeTypeName = Node->GetSettings()->GetClass()->GetName();
                if (NodeTypeName.Contains(TEXT("Input")))
                {
                    NodePosition.Z += 20.0f;
                }
                else if (NodeTypeName.Contains(TEXT("Output")))
                {
                    NodePosition.Z -= 20.0f;
                }
                
                FString NodeKey = FString::Printf(TEXT("%s_%d"), *NodeTypeName, NodeIndex);
                NodeLayout.Add(NodeKey, NodePosition);
                
                // Also add by node pointer for easier lookup
                FString NodePtrKey = FString::Printf(TEXT("Node_%p"), Node);
                NodeLayout.Add(NodePtrKey, NodePosition);
            }
        }
    }
    
    return NodeLayout;
}

FVector UAuracronPCGGraphVisualizer::CalculateNodePosition(const UPCGSettings* NodeSettings, int32 NodeIndex, int32 TotalNodes, const FVector& CenterLocation)
{
    if (!NodeSettings || TotalNodes == 0)
    {
        return CenterLocation;
    }

    // Simple circular layout
    float Angle = (2.0f * PI * NodeIndex) / TotalNodes;
    float Radius = 200.0f;
    
    FVector Position;
    Position.X = CenterLocation.X + FMath::Cos(Angle) * Radius;
    Position.Y = CenterLocation.Y + FMath::Sin(Angle) * Radius;
    Position.Z = CenterLocation.Z;
    
    return Position;
}

void UAuracronPCGGraphVisualizer::ClearGraphVisualization(UWorld* World)
{
    if (World)
    {
        FlushDebugStrings(World);
        FlushPersistentDebugLines(World);
    }
}

FLinearColor UAuracronPCGGraphVisualizer::GetNodeColor(const UPCGSettings* NodeSettings)
{
    if (!NodeSettings)
    {
        return FLinearColor::White;
    }

    // Color coding based on node type
    FString ClassName = NodeSettings->GetClass()->GetName();
    
    if (ClassName.Contains(TEXT("Generator")))
    {
        return FLinearColor::Green;
    }
    else if (ClassName.Contains(TEXT("Modifier")))
    {
        return FLinearColor::Blue;
    }
    else if (ClassName.Contains(TEXT("Filter")))
    {
        return FLinearColor::Yellow;
    }
    else if (ClassName.Contains(TEXT("Sampler")))
    {
        return FLinearColor::Red;
    }
    
    return FLinearColor::White;
}

FString UAuracronPCGGraphVisualizer::GetNodeDisplayName(const UPCGSettings* NodeSettings)
{
    if (!NodeSettings)
    {
        return TEXT("Unknown Node");
    }

    return NodeSettings->GetClass()->GetName();
}

// =============================================================================
// STEP BY STEP EXECUTOR IMPLEMENTATION
// =============================================================================

bool UAuracronPCGStepByStepExecutor::bIsExecutionActive = false;
bool UAuracronPCGStepByStepExecutor::bIsExecutionPaused = false;
bool UAuracronPCGStepByStepExecutor::bIsReplayActive = false;
FString UAuracronPCGStepByStepExecutor::CurrentNodeName;
int32 UAuracronPCGStepByStepExecutor::CurrentStepIndex = 0;
TArray<FString> UAuracronPCGStepByStepExecutor::ExecutionHistory;
TArray<FString> UAuracronPCGStepByStepExecutor::Breakpoints;
FAuracronPCGDebugExecutionDescriptor UAuracronPCGStepByStepExecutor::CurrentDescriptor;

void UAuracronPCGStepByStepExecutor::StartStepByStepExecution(const UPCGGraph* Graph, const FAuracronPCGDebugExecutionDescriptor& Descriptor)
{
    if (!Graph)
    {
        return;
    }

    CurrentDescriptor = Descriptor;
    bIsExecutionActive = true;
    bIsExecutionPaused = false;
    CurrentStepIndex = 0;
    ExecutionHistory.Empty();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Step-by-step execution started"));
}

void UAuracronPCGStepByStepExecutor::StopStepByStepExecution()
{
    bIsExecutionActive = false;
    bIsExecutionPaused = false;
    bIsReplayActive = false;
    CurrentNodeName.Empty();
    CurrentStepIndex = 0;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Step-by-step execution stopped"));
}

void UAuracronPCGStepByStepExecutor::PauseExecution()
{
    if (bIsExecutionActive)
    {
        bIsExecutionPaused = true;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution paused"));
    }
}

void UAuracronPCGStepByStepExecutor::ResumeExecution()
{
    if (bIsExecutionActive && bIsExecutionPaused)
    {
        bIsExecutionPaused = false;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution resumed"));
    }
}

void UAuracronPCGStepByStepExecutor::StepForward()
{
    if (!bIsExecutionActive)
    {
        return;
    }

    CurrentStepIndex++;
    FString StepInfo = FString::Printf(TEXT("Step %d"), CurrentStepIndex);
    ExecutionHistory.Add(StepInfo);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Stepped forward to step %d"), CurrentStepIndex);
}

void UAuracronPCGStepByStepExecutor::StepBackward()
{
    if (!bIsExecutionActive || CurrentStepIndex <= 0)
    {
        return;
    }

    CurrentStepIndex--;
    if (ExecutionHistory.Num() > 0)
    {
        ExecutionHistory.RemoveAt(ExecutionHistory.Num() - 1);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Stepped backward to step %d"), CurrentStepIndex);
}

bool UAuracronPCGStepByStepExecutor::IsExecutionActive()
{
    return bIsExecutionActive;
}

bool UAuracronPCGStepByStepExecutor::IsExecutionPaused()
{
    return bIsExecutionPaused;
}

void UAuracronPCGStepByStepExecutor::AddBreakpoint(const FString& NodeName)
{
    if (!Breakpoints.Contains(NodeName))
    {
        Breakpoints.Add(NodeName);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Breakpoint added for node: %s"), *NodeName);
    }
}

void UAuracronPCGStepByStepExecutor::RemoveBreakpoint(const FString& NodeName)
{
    if (Breakpoints.Remove(NodeName) > 0)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Breakpoint removed for node: %s"), *NodeName);
    }
}

void UAuracronPCGStepByStepExecutor::ClearAllBreakpoints()
{
    int32 RemovedCount = Breakpoints.Num();
    Breakpoints.Empty();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleared %d breakpoints"), RemovedCount);
}

TArray<FString> UAuracronPCGStepByStepExecutor::GetBreakpoints()
{
    return Breakpoints;
}

bool UAuracronPCGStepByStepExecutor::HasBreakpoint(const FString& NodeName)
{
    return Breakpoints.Contains(NodeName);
}

FString UAuracronPCGStepByStepExecutor::GetCurrentNode()
{
    return CurrentNodeName;
}

int32 UAuracronPCGStepByStepExecutor::GetCurrentStep()
{
    return CurrentStepIndex;
}

int32 UAuracronPCGStepByStepExecutor::GetTotalSteps()
{
    return ExecutionHistory.Num();
}

TArray<FString> UAuracronPCGStepByStepExecutor::GetExecutionHistory()
{
    return ExecutionHistory;
}

void UAuracronPCGStepByStepExecutor::StartReplay()
{
    if (ExecutionHistory.Num() > 0)
    {
        bIsReplayActive = true;
        CurrentStepIndex = 0;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Replay started"));
    }
}

void UAuracronPCGStepByStepExecutor::StopReplay()
{
    bIsReplayActive = false;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Replay stopped"));
}

void UAuracronPCGStepByStepExecutor::SetReplaySpeed(float Speed)
{
    // Store replay speed in descriptor
    CurrentDescriptor.SlowMotionFactor = Speed;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Replay speed set to: %.2f"), Speed);
}

bool UAuracronPCGStepByStepExecutor::IsReplayActive()
{
    return bIsReplayActive;
}

// =============================================================================
// DEBUG CONSOLE COMMANDS IMPLEMENTATION
// =============================================================================

TArray<IConsoleCommand*> UAuracronPCGDebugConsoleCommands::RegisteredCommands;

void UAuracronPCGDebugConsoleCommands::RegisterConsoleCommands()
{
    // Register console commands for PCG debugging
    IConsoleCommand* ToggleVisCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.togglevis"),
        TEXT("Toggle PCG debug visualization"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::ToggleVisualization),
        ECVF_Default
    );
    RegisteredCommands.Add(ToggleVisCmd);

    IConsoleCommand* SetVisModeCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.setvismode"),
        TEXT("Set PCG debug visualization mode (0=None, 1=Points, 2=Connections, etc.)"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::SetVisualizationMode),
        ECVF_Default
    );
    RegisteredCommands.Add(SetVisModeCmd);

    IConsoleCommand* StartProfilingCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.startprofiling"),
        TEXT("Start PCG performance profiling"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::StartProfiling),
        ECVF_Default
    );
    RegisteredCommands.Add(StartProfilingCmd);

    IConsoleCommand* StopProfilingCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.stopprofiling"),
        TEXT("Stop PCG performance profiling"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::StopProfiling),
        ECVF_Default
    );
    RegisteredCommands.Add(StopProfilingCmd);

    IConsoleCommand* AddBreakpointCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.addbreakpoint"),
        TEXT("Add breakpoint for specified node name"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::AddBreakpoint),
        ECVF_Default
    );
    RegisteredCommands.Add(AddBreakpointCmd);

    IConsoleCommand* RemoveBreakpointCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.removebreakpoint"),
        TEXT("Remove breakpoint for specified node name"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::RemoveBreakpoint),
        ECVF_Default
    );
    RegisteredCommands.Add(RemoveBreakpointCmd);

    IConsoleCommand* ClearDebugCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.clear"),
        TEXT("Clear all debug display"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::ClearDebugDisplay),
        ECVF_Default
    );
    RegisteredCommands.Add(ClearDebugCmd);

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug console commands registered"));
}

void UAuracronPCGDebugConsoleCommands::UnregisterConsoleCommands()
{
    for (IConsoleCommand* Command : RegisteredCommands)
    {
        if (Command)
        {
            IConsoleManager::Get().UnregisterConsoleObject(Command);
        }
    }
    RegisteredCommands.Empty();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug console commands unregistered"));
}

void UAuracronPCGDebugConsoleCommands::ToggleVisualization(const TArray<FString>& Args)
{
    bool bCurrentlyEnabled = UAuracronPCGVisualDebugger::IsVisualizationEnabled();
    UAuracronPCGVisualDebugger::ToggleVisualization(!bCurrentlyEnabled);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug visualization %s"), 
                              !bCurrentlyEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronPCGDebugConsoleCommands::SetVisualizationMode(const TArray<FString>& Args)
{
    if (Args.Num() > 0)
    {
        int32 ModeValue = FCString::Atoi(*Args[0]);
        EAuracronPCGDebugVisualizationMode Mode = static_cast<EAuracronPCGDebugVisualizationMode>(ModeValue);
        UAuracronPCGVisualDebugger::SetVisualizationMode(Mode);
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug visualization mode set to: %d"), ModeValue);
    }
}

void UAuracronPCGDebugConsoleCommands::StartProfiling(const TArray<FString>& Args)
{
    FAuracronPCGDebugProfilingDescriptor Descriptor;
    UAuracronPCGPerformanceProfiler::StartProfiling(Descriptor);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling started via console"));
}

void UAuracronPCGDebugConsoleCommands::StopProfiling(const TArray<FString>& Args)
{
    UAuracronPCGPerformanceProfiler::StopProfiling();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling stopped via console"));
}

void UAuracronPCGDebugConsoleCommands::InspectGraph(const TArray<FString>& Args)
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Graph inspection requested via console"));
}

void UAuracronPCGDebugConsoleCommands::StepByStepExecution(const TArray<FString>& Args)
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Step-by-step execution requested via console"));
}

void UAuracronPCGDebugConsoleCommands::AddBreakpoint(const TArray<FString>& Args)
{
    if (Args.Num() > 0)
    {
        UAuracronPCGStepByStepExecutor::AddBreakpoint(Args[0]);
    }
}

void UAuracronPCGDebugConsoleCommands::RemoveBreakpoint(const TArray<FString>& Args)
{
    if (Args.Num() > 0)
    {
        UAuracronPCGStepByStepExecutor::RemoveBreakpoint(Args[0]);
    }
}

void UAuracronPCGDebugConsoleCommands::ClearDebugDisplay(const TArray<FString>& Args)
{
    if (UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull))
    {
        UAuracronPCGVisualDebugger::ClearDebugDisplay(World);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug display cleared"));
    }
}

void UAuracronPCGDebugConsoleCommands::ExportProfilingData(const TArray<FString>& Args)
{
    FString FilePath = TEXT("Saved/Profiling/PCG/ConsoleExport.csv");
    if (Args.Num() > 0)
    {
        FilePath = Args[0];
    }
    
    bool bSuccess = UAuracronPCGPerformanceProfiler::ExportProfilingData(FilePath, true);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Profiling data export %s: %s"), 
                              bSuccess ? TEXT("successful") : TEXT("failed"), *FilePath);
}
