// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronEyeGeneration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronEyeGeneration() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FEyeAnimationData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FEyeGenerationParameters();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FEyeGeometryData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FEyeMaterialData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FGeneratedEyeData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EEyeType ******************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EEyeType;
static UEnum* EEyeType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EEyeType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EEyeType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EEyeType"));
	}
	return Z_Registration_Info_UEnum_EEyeType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEyeType>()
{
	return EEyeType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumeration for different eye types\n */" },
#endif
		{ "Creature.DisplayName", "Creature" },
		{ "Creature.Name", "EEyeType::Creature" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EEyeType::Custom" },
		{ "Fantasy.DisplayName", "Fantasy" },
		{ "Fantasy.Name", "EEyeType::Fantasy" },
		{ "Human.DisplayName", "Human" },
		{ "Human.Name", "EEyeType::Human" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
		{ "Robotic.DisplayName", "Robotic" },
		{ "Robotic.Name", "EEyeType::Robotic" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumeration for different eye types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EEyeType::Human", (int64)EEyeType::Human },
		{ "EEyeType::Creature", (int64)EEyeType::Creature },
		{ "EEyeType::Robotic", (int64)EEyeType::Robotic },
		{ "EEyeType::Fantasy", (int64)EEyeType::Fantasy },
		{ "EEyeType::Custom", (int64)EEyeType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EEyeType",
	"EEyeType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType()
{
	if (!Z_Registration_Info_UEnum_EEyeType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EEyeType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EEyeType.InnerSingleton;
}
// ********** End Enum EEyeType ********************************************************************

// ********** Begin Enum EEyeColor *****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EEyeColor;
static UEnum* EEyeColor_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EEyeColor.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EEyeColor.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EEyeColor"));
	}
	return Z_Registration_Info_UEnum_EEyeColor.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEyeColor>()
{
	return EEyeColor_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Amber.DisplayName", "Amber" },
		{ "Amber.Name", "EEyeColor::Amber" },
		{ "Blue.DisplayName", "Blue" },
		{ "Blue.Name", "EEyeColor::Blue" },
		{ "BlueprintType", "true" },
		{ "Brown.DisplayName", "Brown" },
		{ "Brown.Name", "EEyeColor::Brown" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumeration for eye colors\n */" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EEyeColor::Custom" },
		{ "Gray.DisplayName", "Gray" },
		{ "Gray.Name", "EEyeColor::Gray" },
		{ "Green.DisplayName", "Green" },
		{ "Green.Name", "EEyeColor::Green" },
		{ "Hazel.DisplayName", "Hazel" },
		{ "Hazel.Name", "EEyeColor::Hazel" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
		{ "Purple.DisplayName", "Purple" },
		{ "Purple.Name", "EEyeColor::Purple" },
		{ "Red.DisplayName", "Red" },
		{ "Red.Name", "EEyeColor::Red" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumeration for eye colors" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EEyeColor::Brown", (int64)EEyeColor::Brown },
		{ "EEyeColor::Blue", (int64)EEyeColor::Blue },
		{ "EEyeColor::Green", (int64)EEyeColor::Green },
		{ "EEyeColor::Hazel", (int64)EEyeColor::Hazel },
		{ "EEyeColor::Gray", (int64)EEyeColor::Gray },
		{ "EEyeColor::Amber", (int64)EEyeColor::Amber },
		{ "EEyeColor::Red", (int64)EEyeColor::Red },
		{ "EEyeColor::Purple", (int64)EEyeColor::Purple },
		{ "EEyeColor::Custom", (int64)EEyeColor::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EEyeColor",
	"EEyeColor",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor()
{
	if (!Z_Registration_Info_UEnum_EEyeColor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EEyeColor.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EEyeColor.InnerSingleton;
}
// ********** End Enum EEyeColor *******************************************************************

// ********** Begin ScriptStruct FEyeGeometryData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FEyeGeometryData;
class UScriptStruct* FEyeGeometryData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeGeometryData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FEyeGeometryData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEyeGeometryData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EyeGeometryData"));
	}
	return Z_Registration_Info_UScriptStruct_FEyeGeometryData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FEyeGeometryData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for eye geometry data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for eye geometry data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeballRadius_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaRadius_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisRadius_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PupilRadius_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaHeight_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeballSubdivisions_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaSubdivisions_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EyeballRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CorneaRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IrisRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PupilRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CorneaHeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EyeballSubdivisions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CorneaSubdivisions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEyeGeometryData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_EyeballRadius = { "EyeballRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGeometryData, EyeballRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeballRadius_MetaData), NewProp_EyeballRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_CorneaRadius = { "CorneaRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGeometryData, CorneaRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaRadius_MetaData), NewProp_CorneaRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_IrisRadius = { "IrisRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGeometryData, IrisRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisRadius_MetaData), NewProp_IrisRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_PupilRadius = { "PupilRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGeometryData, PupilRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PupilRadius_MetaData), NewProp_PupilRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_CorneaHeight = { "CorneaHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGeometryData, CorneaHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaHeight_MetaData), NewProp_CorneaHeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_EyeballSubdivisions = { "EyeballSubdivisions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGeometryData, EyeballSubdivisions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeballSubdivisions_MetaData), NewProp_EyeballSubdivisions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_CorneaSubdivisions = { "CorneaSubdivisions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGeometryData, CorneaSubdivisions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaSubdivisions_MetaData), NewProp_CorneaSubdivisions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEyeGeometryData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_EyeballRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_CorneaRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_IrisRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_PupilRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_CorneaHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_EyeballSubdivisions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewProp_CorneaSubdivisions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeGeometryData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEyeGeometryData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"EyeGeometryData",
	Z_Construct_UScriptStruct_FEyeGeometryData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeGeometryData_Statics::PropPointers),
	sizeof(FEyeGeometryData),
	alignof(FEyeGeometryData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeGeometryData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEyeGeometryData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEyeGeometryData()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeGeometryData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FEyeGeometryData.InnerSingleton, Z_Construct_UScriptStruct_FEyeGeometryData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FEyeGeometryData.InnerSingleton;
}
// ********** End ScriptStruct FEyeGeometryData ****************************************************

// ********** Begin ScriptStruct FEyeMaterialData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FEyeMaterialData;
class UScriptStruct* FEyeMaterialData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeMaterialData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FEyeMaterialData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEyeMaterialData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EyeMaterialData"));
	}
	return Z_Registration_Info_UScriptStruct_FEyeMaterialData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FEyeMaterialData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for eye material data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for eye material data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeballMaterial_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaMaterial_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisTexture_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScleraTexture_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisColor_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScleraColor_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisRoughness_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaRoughness_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeballMetallic_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaRefraction_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubsurfaceScattering_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EyeballMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CorneaMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IrisTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScleraTexture;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IrisColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScleraColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IrisRoughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CorneaRoughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EyeballMetallic;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CorneaRefraction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SubsurfaceScattering;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEyeMaterialData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_EyeballMaterial = { "EyeballMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, EyeballMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeballMaterial_MetaData), NewProp_EyeballMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_CorneaMaterial = { "CorneaMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, CorneaMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaMaterial_MetaData), NewProp_CorneaMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_IrisTexture = { "IrisTexture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, IrisTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisTexture_MetaData), NewProp_IrisTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_ScleraTexture = { "ScleraTexture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, ScleraTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScleraTexture_MetaData), NewProp_ScleraTexture_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_IrisColor = { "IrisColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, IrisColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisColor_MetaData), NewProp_IrisColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_ScleraColor = { "ScleraColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, ScleraColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScleraColor_MetaData), NewProp_ScleraColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_IrisRoughness = { "IrisRoughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, IrisRoughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisRoughness_MetaData), NewProp_IrisRoughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_CorneaRoughness = { "CorneaRoughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, CorneaRoughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaRoughness_MetaData), NewProp_CorneaRoughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_EyeballMetallic = { "EyeballMetallic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, EyeballMetallic), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeballMetallic_MetaData), NewProp_EyeballMetallic_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_CorneaRefraction = { "CorneaRefraction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, CorneaRefraction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaRefraction_MetaData), NewProp_CorneaRefraction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_SubsurfaceScattering = { "SubsurfaceScattering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeMaterialData, SubsurfaceScattering), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubsurfaceScattering_MetaData), NewProp_SubsurfaceScattering_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEyeMaterialData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_EyeballMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_CorneaMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_IrisTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_ScleraTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_IrisColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_ScleraColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_IrisRoughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_CorneaRoughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_EyeballMetallic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_CorneaRefraction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewProp_SubsurfaceScattering,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeMaterialData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEyeMaterialData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"EyeMaterialData",
	Z_Construct_UScriptStruct_FEyeMaterialData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeMaterialData_Statics::PropPointers),
	sizeof(FEyeMaterialData),
	alignof(FEyeMaterialData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeMaterialData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEyeMaterialData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEyeMaterialData()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeMaterialData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FEyeMaterialData.InnerSingleton, Z_Construct_UScriptStruct_FEyeMaterialData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FEyeMaterialData.InnerSingleton;
}
// ********** End ScriptStruct FEyeMaterialData ****************************************************

// ********** Begin ScriptStruct FEyeAnimationData *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FEyeAnimationData;
class UScriptStruct* FEyeAnimationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeAnimationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FEyeAnimationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEyeAnimationData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EyeAnimationData"));
	}
	return Z_Registration_Info_UScriptStruct_FEyeAnimationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FEyeAnimationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for eye animation data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for eye animation data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBlinking_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlinkFrequency_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlinkDuration_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEyeTracking_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeTrackingSpeed_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxEyeRotation_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePupilDilation_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PupilDilationSpeed_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinPupilSize_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPupilSize_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableBlinking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBlinking;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlinkFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlinkDuration;
	static void NewProp_bEnableEyeTracking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEyeTracking;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EyeTrackingSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxEyeRotation;
	static void NewProp_bEnablePupilDilation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePupilDilation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PupilDilationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinPupilSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxPupilSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEyeAnimationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableBlinking_SetBit(void* Obj)
{
	((FEyeAnimationData*)Obj)->bEnableBlinking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableBlinking = { "bEnableBlinking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEyeAnimationData), &Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableBlinking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBlinking_MetaData), NewProp_bEnableBlinking_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_BlinkFrequency = { "BlinkFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeAnimationData, BlinkFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlinkFrequency_MetaData), NewProp_BlinkFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_BlinkDuration = { "BlinkDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeAnimationData, BlinkDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlinkDuration_MetaData), NewProp_BlinkDuration_MetaData) };
void Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableEyeTracking_SetBit(void* Obj)
{
	((FEyeAnimationData*)Obj)->bEnableEyeTracking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableEyeTracking = { "bEnableEyeTracking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEyeAnimationData), &Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableEyeTracking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEyeTracking_MetaData), NewProp_bEnableEyeTracking_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_EyeTrackingSpeed = { "EyeTrackingSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeAnimationData, EyeTrackingSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeTrackingSpeed_MetaData), NewProp_EyeTrackingSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_MaxEyeRotation = { "MaxEyeRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeAnimationData, MaxEyeRotation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxEyeRotation_MetaData), NewProp_MaxEyeRotation_MetaData) };
void Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnablePupilDilation_SetBit(void* Obj)
{
	((FEyeAnimationData*)Obj)->bEnablePupilDilation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnablePupilDilation = { "bEnablePupilDilation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEyeAnimationData), &Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnablePupilDilation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePupilDilation_MetaData), NewProp_bEnablePupilDilation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_PupilDilationSpeed = { "PupilDilationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeAnimationData, PupilDilationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PupilDilationSpeed_MetaData), NewProp_PupilDilationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_MinPupilSize = { "MinPupilSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeAnimationData, MinPupilSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinPupilSize_MetaData), NewProp_MinPupilSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_MaxPupilSize = { "MaxPupilSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeAnimationData, MaxPupilSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPupilSize_MetaData), NewProp_MaxPupilSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEyeAnimationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableBlinking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_BlinkFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_BlinkDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnableEyeTracking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_EyeTrackingSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_MaxEyeRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_bEnablePupilDilation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_PupilDilationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_MinPupilSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewProp_MaxPupilSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeAnimationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEyeAnimationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"EyeAnimationData",
	Z_Construct_UScriptStruct_FEyeAnimationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeAnimationData_Statics::PropPointers),
	sizeof(FEyeAnimationData),
	alignof(FEyeAnimationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeAnimationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEyeAnimationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEyeAnimationData()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeAnimationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FEyeAnimationData.InnerSingleton, Z_Construct_UScriptStruct_FEyeAnimationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FEyeAnimationData.InnerSingleton;
}
// ********** End ScriptStruct FEyeAnimationData ***************************************************

// ********** Begin ScriptStruct FEyeGenerationParameters ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FEyeGenerationParameters;
class UScriptStruct* FEyeGenerationParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeGenerationParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FEyeGenerationParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEyeGenerationParameters, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EyeGenerationParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FEyeGenerationParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for eye generation parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for eye generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeName_MetaData[] = {
		{ "Category", "General" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeType_MetaData[] = {
		{ "Category", "General" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeColor_MetaData[] = {
		{ "Category", "General" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryData_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialData_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAnimation_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationData_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateLODs_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevels_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EyeName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EyeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EyeType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EyeColor_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EyeColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GeometryData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaterialData;
	static void NewProp_bEnableAnimation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAnimation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AnimationData;
	static void NewProp_bGenerateLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevels;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEyeGenerationParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeName = { "EyeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGenerationParameters, EyeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeName_MetaData), NewProp_EyeName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeType = { "EyeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGenerationParameters, EyeType), Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeType_MetaData), NewProp_EyeType_MetaData) }; // 878595679
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeColor_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeColor = { "EyeColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGenerationParameters, EyeColor), Z_Construct_UEnum_AuracronMetaHumanBridge_EEyeColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeColor_MetaData), NewProp_EyeColor_MetaData) }; // 2650715176
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_GeometryData = { "GeometryData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGenerationParameters, GeometryData), Z_Construct_UScriptStruct_FEyeGeometryData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryData_MetaData), NewProp_GeometryData_MetaData) }; // 1493839193
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_MaterialData = { "MaterialData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGenerationParameters, MaterialData), Z_Construct_UScriptStruct_FEyeMaterialData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialData_MetaData), NewProp_MaterialData_MetaData) }; // 2318631099
void Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bEnableAnimation_SetBit(void* Obj)
{
	((FEyeGenerationParameters*)Obj)->bEnableAnimation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bEnableAnimation = { "bEnableAnimation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEyeGenerationParameters), &Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bEnableAnimation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAnimation_MetaData), NewProp_bEnableAnimation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_AnimationData = { "AnimationData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGenerationParameters, AnimationData), Z_Construct_UScriptStruct_FEyeAnimationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationData_MetaData), NewProp_AnimationData_MetaData) }; // 3195145311
void Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bGenerateLODs_SetBit(void* Obj)
{
	((FEyeGenerationParameters*)Obj)->bGenerateLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bGenerateLODs = { "bGenerateLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEyeGenerationParameters), &Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bGenerateLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateLODs_MetaData), NewProp_bGenerateLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_MaxLODLevels = { "MaxLODLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEyeGenerationParameters, MaxLODLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevels_MetaData), NewProp_MaxLODLevels_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeColor_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_EyeColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_GeometryData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_MaterialData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bEnableAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_AnimationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_bGenerateLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewProp_MaxLODLevels,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"EyeGenerationParameters",
	Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::PropPointers),
	sizeof(FEyeGenerationParameters),
	alignof(FEyeGenerationParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEyeGenerationParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FEyeGenerationParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FEyeGenerationParameters.InnerSingleton, Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FEyeGenerationParameters.InnerSingleton;
}
// ********** End ScriptStruct FEyeGenerationParameters ********************************************

// ********** Begin ScriptStruct FGeneratedEyeData *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FGeneratedEyeData;
class UScriptStruct* FGeneratedEyeData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FGeneratedEyeData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FGeneratedEyeData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FGeneratedEyeData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("GeneratedEyeData"));
	}
	return Z_Registration_Info_UScriptStruct_FGeneratedEyeData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FGeneratedEyeData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure for generated eye data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure for generated eye data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeballMesh_MetaData[] = {
		{ "Category", "Generated" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaMesh_MetaData[] = {
		{ "Category", "Generated" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeballMaterial_MetaData[] = {
		{ "Category", "Generated" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorneaMaterial_MetaData[] = {
		{ "Category", "Generated" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODMeshes_MetaData[] = {
		{ "Category", "Generated" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationHash_MetaData[] = {
		{ "Category", "Generated" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Generated" },
		{ "ModuleRelativePath", "Public/AuracronEyeGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EyeballMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CorneaMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EyeballMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CorneaMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LODMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODMeshes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationHash;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FGeneratedEyeData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_EyeballMesh = { "EyeballMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeneratedEyeData, EyeballMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeballMesh_MetaData), NewProp_EyeballMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_CorneaMesh = { "CorneaMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeneratedEyeData, CorneaMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaMesh_MetaData), NewProp_CorneaMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_EyeballMaterial = { "EyeballMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeneratedEyeData, EyeballMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeballMaterial_MetaData), NewProp_EyeballMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_CorneaMaterial = { "CorneaMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeneratedEyeData, CorneaMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorneaMaterial_MetaData), NewProp_CorneaMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_LODMeshes_Inner = { "LODMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_LODMeshes = { "LODMeshes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeneratedEyeData, LODMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODMeshes_MetaData), NewProp_LODMeshes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_GenerationHash = { "GenerationHash", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeneratedEyeData, GenerationHash), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationHash_MetaData), NewProp_GenerationHash_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeneratedEyeData, GenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_EyeballMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_CorneaMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_EyeballMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_CorneaMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_LODMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_LODMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_GenerationHash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewProp_GenerationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"GeneratedEyeData",
	Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::PropPointers),
	sizeof(FGeneratedEyeData),
	alignof(FGeneratedEyeData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FGeneratedEyeData()
{
	if (!Z_Registration_Info_UScriptStruct_FGeneratedEyeData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FGeneratedEyeData.InnerSingleton, Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FGeneratedEyeData.InnerSingleton;
}
// ********** End ScriptStruct FGeneratedEyeData ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EEyeType_StaticEnum, TEXT("EEyeType"), &Z_Registration_Info_UEnum_EEyeType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 878595679U) },
		{ EEyeColor_StaticEnum, TEXT("EEyeColor"), &Z_Registration_Info_UEnum_EEyeColor, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2650715176U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FEyeGeometryData::StaticStruct, Z_Construct_UScriptStruct_FEyeGeometryData_Statics::NewStructOps, TEXT("EyeGeometryData"), &Z_Registration_Info_UScriptStruct_FEyeGeometryData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEyeGeometryData), 1493839193U) },
		{ FEyeMaterialData::StaticStruct, Z_Construct_UScriptStruct_FEyeMaterialData_Statics::NewStructOps, TEXT("EyeMaterialData"), &Z_Registration_Info_UScriptStruct_FEyeMaterialData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEyeMaterialData), 2318631099U) },
		{ FEyeAnimationData::StaticStruct, Z_Construct_UScriptStruct_FEyeAnimationData_Statics::NewStructOps, TEXT("EyeAnimationData"), &Z_Registration_Info_UScriptStruct_FEyeAnimationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEyeAnimationData), 3195145311U) },
		{ FEyeGenerationParameters::StaticStruct, Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics::NewStructOps, TEXT("EyeGenerationParameters"), &Z_Registration_Info_UScriptStruct_FEyeGenerationParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEyeGenerationParameters), 4271749138U) },
		{ FGeneratedEyeData::StaticStruct, Z_Construct_UScriptStruct_FGeneratedEyeData_Statics::NewStructOps, TEXT("GeneratedEyeData"), &Z_Registration_Info_UScriptStruct_FGeneratedEyeData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FGeneratedEyeData), 558000496U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h__Script_AuracronMetaHumanBridge_4039840096(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
