# Script para corrigir últimos duplicados

Write-Host "Corrigindo últimos duplicados..."

# 1. Renomear FAuracronPCGPerformanceMetrics para FAuracronPCGTestingPerformanceMetrics em AuracronPCGTestingFramework.h
$file1 = "Auracron\Source\AuracronPCGBridge\Public\AuracronPCGTestingFramework.h"
$content1 = Get-Content $file1 -Raw
$content1 = $content1 -replace "struct FAuracronPCGPerformanceMetrics", "struct FAuracronPCGTestingPerformanceMetrics"
$content1 = $content1 -replace "FAuracronPCGPerformanceMetrics ", "FAuracronPCGTestingPerformanceMetrics "
Set-Content -Path $file1 -Value $content1 -NoNewline
Write-Host "Renomeado FAuracronPCGPerformanceMetrics para FAuracronPCGTestingPerformanceMetrics"

# 2. Renomear EAuracronPCGNoiseType para EAuracronPCGAdvancedNoiseType em AuracronPCGNoiseSystem.h
$file2 = "Auracron\Source\AuracronPCGBridge\Public\AuracronPCGNoiseSystem.h"
$content2 = Get-Content $file2 -Raw
$content2 = $content2 -replace "enum class EAuracronPCGNoiseType", "enum class EAuracronPCGAdvancedNoiseType"
$content2 = $content2 -replace "EAuracronPCGNoiseType::", "EAuracronPCGAdvancedNoiseType::"
$content2 = $content2 -replace "EAuracronPCGNoiseType ", "EAuracronPCGAdvancedNoiseType "
Set-Content -Path $file2 -Value $content2 -NoNewline
Write-Host "Renomeado EAuracronPCGNoiseType para EAuracronPCGAdvancedNoiseType"

# 3. Renomear EAuracronPCGDebugVisualizationMode para EAuracronPCGDebugSystemVisualizationMode em AuracronPCGDebugSystem.h
$file3 = "Auracron\Source\AuracronPCGBridge\Public\AuracronPCGDebugSystem.h"
$content3 = Get-Content $file3 -Raw
$content3 = $content3 -replace "enum class EAuracronPCGDebugVisualizationMode", "enum class EAuracronPCGDebugSystemVisualizationMode"
$content3 = $content3 -replace "EAuracronPCGDebugVisualizationMode::", "EAuracronPCGDebugSystemVisualizationMode::"
$content3 = $content3 -replace "EAuracronPCGDebugVisualizationMode ", "EAuracronPCGDebugSystemVisualizationMode "
Set-Content -Path $file3 -Value $content3 -NoNewline
Write-Host "Renomeado EAuracronPCGDebugVisualizationMode para EAuracronPCGDebugSystemVisualizationMode"

# 4. Renomear FAuracronSessionConfiguration para FAuracronNetworkingSessionConfiguration em AuracronNetworkingBridge.h
$file4 = "Auracron\Source\AuracronNetworkingBridge\Public\AuracronNetworkingBridge.h"
$content4 = Get-Content $file4 -Raw
$content4 = $content4 -replace "struct FAuracronSessionConfiguration", "struct FAuracronNetworkingSessionConfiguration"
$content4 = $content4 -replace "FAuracronSessionConfiguration ", "FAuracronNetworkingSessionConfiguration "
Set-Content -Path $file4 -Value $content4 -NoNewline
Write-Host "Renomeado FAuracronSessionConfiguration para FAuracronNetworkingSessionConfiguration"

# 5. Renomear FAuracronAntiCheatConfiguration para FAuracronNetworkingAntiCheatConfiguration em AuracronNetworkingBridge.h
$content4 = Get-Content $file4 -Raw
$content4 = $content4 -replace "struct FAuracronAntiCheatConfiguration", "struct FAuracronNetworkingAntiCheatConfiguration"
$content4 = $content4 -replace "FAuracronAntiCheatConfiguration ", "FAuracronNetworkingAntiCheatConfiguration "
Set-Content -Path $file4 -Value $content4 -NoNewline
Write-Host "Renomeado FAuracronAntiCheatConfiguration para FAuracronNetworkingAntiCheatConfiguration"

Write-Host "Correções de últimos duplicados aplicadas com sucesso!"
