// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronMeshDeformation.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMeshDeformation() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FLODGenerationSettings();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FMeshDeformationData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FMeshValidationResult();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FVertexData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EVertexManipulationType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EVertexManipulationType;
static UEnum* EVertexManipulationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EVertexManipulationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EVertexManipulationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EVertexManipulationType"));
	}
	return Z_Registration_Info_UEnum_EVertexManipulationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EVertexManipulationType>()
{
	return EVertexManipulationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Absolute.DisplayName", "Absolute" },
		{ "Absolute.Name", "EVertexManipulationType::Absolute" },
		{ "Additive.DisplayName", "Additive" },
		{ "Additive.Name", "EVertexManipulationType::Additive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for mesh deformation operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
		{ "Multiplicative.DisplayName", "Multiplicative" },
		{ "Multiplicative.Name", "EVertexManipulationType::Multiplicative" },
		{ "Relative.DisplayName", "Relative" },
		{ "Relative.Name", "EVertexManipulationType::Relative" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for mesh deformation operations" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EVertexManipulationType::Absolute", (int64)EVertexManipulationType::Absolute },
		{ "EVertexManipulationType::Relative", (int64)EVertexManipulationType::Relative },
		{ "EVertexManipulationType::Additive", (int64)EVertexManipulationType::Additive },
		{ "EVertexManipulationType::Multiplicative", (int64)EVertexManipulationType::Multiplicative },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EVertexManipulationType",
	"EVertexManipulationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType()
{
	if (!Z_Registration_Info_UEnum_EVertexManipulationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EVertexManipulationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EVertexManipulationType.InnerSingleton;
}
// ********** End Enum EVertexManipulationType *****************************************************

// ********** Begin Enum ENormalRecalculationType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ENormalRecalculationType;
static UEnum* ENormalRecalculationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ENormalRecalculationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ENormalRecalculationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ENormalRecalculationType"));
	}
	return Z_Registration_Info_UEnum_ENormalRecalculationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ENormalRecalculationType>()
{
	return ENormalRecalculationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AngleWeighted.DisplayName", "Angle Weighted" },
		{ "AngleWeighted.Name", "ENormalRecalculationType::AngleWeighted" },
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ENormalRecalculationType::None" },
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "ENormalRecalculationType::Uniform" },
		{ "Weighted.DisplayName", "Weighted" },
		{ "Weighted.Name", "ENormalRecalculationType::Weighted" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ENormalRecalculationType::None", (int64)ENormalRecalculationType::None },
		{ "ENormalRecalculationType::Weighted", (int64)ENormalRecalculationType::Weighted },
		{ "ENormalRecalculationType::Uniform", (int64)ENormalRecalculationType::Uniform },
		{ "ENormalRecalculationType::AngleWeighted", (int64)ENormalRecalculationType::AngleWeighted },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ENormalRecalculationType",
	"ENormalRecalculationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType()
{
	if (!Z_Registration_Info_UEnum_ENormalRecalculationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ENormalRecalculationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ENormalRecalculationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ENormalRecalculationType.InnerSingleton;
}
// ********** End Enum ENormalRecalculationType ****************************************************

// ********** Begin Enum EUVPreservationType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EUVPreservationType;
static UEnum* EUVPreservationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EUVPreservationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EUVPreservationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EUVPreservationType"));
	}
	return Z_Registration_Info_UEnum_EUVPreservationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EUVPreservationType>()
{
	return EUVPreservationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Authalic.DisplayName", "Authalic" },
		{ "Authalic.Name", "EUVPreservationType::Authalic" },
		{ "BlueprintType", "true" },
		{ "Conformal.DisplayName", "Conformal" },
		{ "Conformal.Name", "EUVPreservationType::Conformal" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EUVPreservationType::None" },
		{ "Stretch.DisplayName", "Stretch" },
		{ "Stretch.Name", "EUVPreservationType::Stretch" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EUVPreservationType::None", (int64)EUVPreservationType::None },
		{ "EUVPreservationType::Stretch", (int64)EUVPreservationType::Stretch },
		{ "EUVPreservationType::Conformal", (int64)EUVPreservationType::Conformal },
		{ "EUVPreservationType::Authalic", (int64)EUVPreservationType::Authalic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EUVPreservationType",
	"EUVPreservationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType()
{
	if (!Z_Registration_Info_UEnum_EUVPreservationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EUVPreservationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EUVPreservationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EUVPreservationType.InnerSingleton;
}
// ********** End Enum EUVPreservationType *********************************************************

// ********** Begin Enum EMeshValidationType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMeshValidationType;
static UEnum* EMeshValidationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMeshValidationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMeshValidationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMeshValidationType"));
	}
	return Z_Registration_Info_UEnum_EMeshValidationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMeshValidationType>()
{
	return EMeshValidationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EMeshValidationType::Basic" },
		{ "BlueprintType", "true" },
		{ "Comprehensive.DisplayName", "Comprehensive" },
		{ "Comprehensive.Name", "EMeshValidationType::Comprehensive" },
		{ "Geometry.DisplayName", "Geometry" },
		{ "Geometry.Name", "EMeshValidationType::Geometry" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
		{ "Topology.DisplayName", "Topology" },
		{ "Topology.Name", "EMeshValidationType::Topology" },
		{ "UVMapping.DisplayName", "UV Mapping" },
		{ "UVMapping.Name", "EMeshValidationType::UVMapping" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMeshValidationType::Basic", (int64)EMeshValidationType::Basic },
		{ "EMeshValidationType::Comprehensive", (int64)EMeshValidationType::Comprehensive },
		{ "EMeshValidationType::Topology", (int64)EMeshValidationType::Topology },
		{ "EMeshValidationType::Geometry", (int64)EMeshValidationType::Geometry },
		{ "EMeshValidationType::UVMapping", (int64)EMeshValidationType::UVMapping },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMeshValidationType",
	"EMeshValidationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType()
{
	if (!Z_Registration_Info_UEnum_EMeshValidationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMeshValidationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshValidationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMeshValidationType.InnerSingleton;
}
// ********** End Enum EMeshValidationType *********************************************************

// ********** Begin ScriptStruct FVertexData *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FVertexData;
class UScriptStruct* FVertexData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FVertexData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FVertexData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FVertexData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("VertexData"));
	}
	return Z_Registration_Info_UScriptStruct_FVertexData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FVertexData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for mesh deformation data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for mesh deformation data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normal_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tangent_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Binormal_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVChannels_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexColor_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendWeights_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendIndices_MetaData[] = {
		{ "Category", "Vertex Data" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normal;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tangent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Binormal;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UVChannels;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VertexColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendWeights;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BlendIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendIndices;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FVertexData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Normal = { "Normal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, Normal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normal_MetaData), NewProp_Normal_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Tangent = { "Tangent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, Tangent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tangent_MetaData), NewProp_Tangent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Binormal = { "Binormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, Binormal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Binormal_MetaData), NewProp_Binormal_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_UVChannels_Inner = { "UVChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_UVChannels = { "UVChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, UVChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVChannels_MetaData), NewProp_UVChannels_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_VertexColor = { "VertexColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, VertexColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexColor_MetaData), NewProp_VertexColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendWeights_Inner = { "BlendWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendWeights = { "BlendWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, BlendWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendWeights_MetaData), NewProp_BlendWeights_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendIndices_Inner = { "BlendIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendIndices = { "BlendIndices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVertexData, BlendIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendIndices_MetaData), NewProp_BlendIndices_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FVertexData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Normal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Tangent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_Binormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_UVChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_UVChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_VertexColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVertexData_Statics::NewProp_BlendIndices,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVertexData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FVertexData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"VertexData",
	Z_Construct_UScriptStruct_FVertexData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVertexData_Statics::PropPointers),
	sizeof(FVertexData),
	alignof(FVertexData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVertexData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FVertexData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FVertexData()
{
	if (!Z_Registration_Info_UScriptStruct_FVertexData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FVertexData.InnerSingleton, Z_Construct_UScriptStruct_FVertexData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FVertexData.InnerSingleton;
}
// ********** End ScriptStruct FVertexData *********************************************************

// ********** Begin ScriptStruct FMeshDeformationData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMeshDeformationData;
class UScriptStruct* FMeshDeformationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMeshDeformationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMeshDeformationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMeshDeformationData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("MeshDeformationData"));
	}
	return Z_Registration_Info_UScriptStruct_FMeshDeformationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMeshDeformationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshIndex_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexIndices_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPositions_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManipulationType_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendWeight_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRecalculateNormals_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveUVs_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUpdateTangents_MetaData[] = {
		{ "Category", "Mesh Deformation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VertexIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VertexIndices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetPositions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetPositions;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ManipulationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ManipulationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeight;
	static void NewProp_bRecalculateNormals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRecalculateNormals;
	static void NewProp_bPreserveUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveUVs;
	static void NewProp_bUpdateTangents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUpdateTangents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMeshDeformationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_MeshIndex = { "MeshIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshDeformationData, MeshIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshIndex_MetaData), NewProp_MeshIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_VertexIndices_Inner = { "VertexIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_VertexIndices = { "VertexIndices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshDeformationData, VertexIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexIndices_MetaData), NewProp_VertexIndices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_TargetPositions_Inner = { "TargetPositions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_TargetPositions = { "TargetPositions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshDeformationData, TargetPositions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPositions_MetaData), NewProp_TargetPositions_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_ManipulationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_ManipulationType = { "ManipulationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshDeformationData, ManipulationType), Z_Construct_UEnum_AuracronMetaHumanBridge_EVertexManipulationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManipulationType_MetaData), NewProp_ManipulationType_MetaData) }; // 2156960843
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_BlendWeight = { "BlendWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshDeformationData, BlendWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendWeight_MetaData), NewProp_BlendWeight_MetaData) };
void Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bRecalculateNormals_SetBit(void* Obj)
{
	((FMeshDeformationData*)Obj)->bRecalculateNormals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bRecalculateNormals = { "bRecalculateNormals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMeshDeformationData), &Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bRecalculateNormals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRecalculateNormals_MetaData), NewProp_bRecalculateNormals_MetaData) };
void Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bPreserveUVs_SetBit(void* Obj)
{
	((FMeshDeformationData*)Obj)->bPreserveUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bPreserveUVs = { "bPreserveUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMeshDeformationData), &Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bPreserveUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveUVs_MetaData), NewProp_bPreserveUVs_MetaData) };
void Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bUpdateTangents_SetBit(void* Obj)
{
	((FMeshDeformationData*)Obj)->bUpdateTangents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bUpdateTangents = { "bUpdateTangents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMeshDeformationData), &Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bUpdateTangents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUpdateTangents_MetaData), NewProp_bUpdateTangents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMeshDeformationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_MeshIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_VertexIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_VertexIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_TargetPositions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_TargetPositions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_ManipulationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_ManipulationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_BlendWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bRecalculateNormals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bPreserveUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewProp_bUpdateTangents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMeshDeformationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMeshDeformationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"MeshDeformationData",
	Z_Construct_UScriptStruct_FMeshDeformationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMeshDeformationData_Statics::PropPointers),
	sizeof(FMeshDeformationData),
	alignof(FMeshDeformationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMeshDeformationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMeshDeformationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMeshDeformationData()
{
	if (!Z_Registration_Info_UScriptStruct_FMeshDeformationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMeshDeformationData.InnerSingleton, Z_Construct_UScriptStruct_FMeshDeformationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMeshDeformationData.InnerSingleton;
}
// ********** End ScriptStruct FMeshDeformationData ************************************************

// ********** Begin ScriptStruct FLODGenerationSettings ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FLODGenerationSettings;
class UScriptStruct* FLODGenerationSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FLODGenerationSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FLODGenerationSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLODGenerationSettings, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("LODGenerationSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FLODGenerationSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FLODGenerationSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumLODs_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReductionPercentages_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveUVBoundaries_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveHardEdges_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeldingThreshold_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRecalculateNormals_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateLightmapUVs_MetaData[] = {
		{ "Category", "LOD Generation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumLODs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReductionPercentages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReductionPercentages;
	static void NewProp_bPreserveUVBoundaries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveUVBoundaries;
	static void NewProp_bPreserveHardEdges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveHardEdges;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeldingThreshold;
	static void NewProp_bRecalculateNormals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRecalculateNormals;
	static void NewProp_bGenerateLightmapUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateLightmapUVs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLODGenerationSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_NumLODs = { "NumLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLODGenerationSettings, NumLODs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumLODs_MetaData), NewProp_NumLODs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_ReductionPercentages_Inner = { "ReductionPercentages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_ReductionPercentages = { "ReductionPercentages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLODGenerationSettings, ReductionPercentages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReductionPercentages_MetaData), NewProp_ReductionPercentages_MetaData) };
void Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveUVBoundaries_SetBit(void* Obj)
{
	((FLODGenerationSettings*)Obj)->bPreserveUVBoundaries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveUVBoundaries = { "bPreserveUVBoundaries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLODGenerationSettings), &Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveUVBoundaries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveUVBoundaries_MetaData), NewProp_bPreserveUVBoundaries_MetaData) };
void Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveHardEdges_SetBit(void* Obj)
{
	((FLODGenerationSettings*)Obj)->bPreserveHardEdges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveHardEdges = { "bPreserveHardEdges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLODGenerationSettings), &Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveHardEdges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveHardEdges_MetaData), NewProp_bPreserveHardEdges_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_WeldingThreshold = { "WeldingThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLODGenerationSettings, WeldingThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeldingThreshold_MetaData), NewProp_WeldingThreshold_MetaData) };
void Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bRecalculateNormals_SetBit(void* Obj)
{
	((FLODGenerationSettings*)Obj)->bRecalculateNormals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bRecalculateNormals = { "bRecalculateNormals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLODGenerationSettings), &Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bRecalculateNormals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRecalculateNormals_MetaData), NewProp_bRecalculateNormals_MetaData) };
void Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bGenerateLightmapUVs_SetBit(void* Obj)
{
	((FLODGenerationSettings*)Obj)->bGenerateLightmapUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bGenerateLightmapUVs = { "bGenerateLightmapUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLODGenerationSettings), &Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bGenerateLightmapUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateLightmapUVs_MetaData), NewProp_bGenerateLightmapUVs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_NumLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_ReductionPercentages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_ReductionPercentages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveUVBoundaries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bPreserveHardEdges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_WeldingThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bRecalculateNormals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewProp_bGenerateLightmapUVs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"LODGenerationSettings",
	Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::PropPointers),
	sizeof(FLODGenerationSettings),
	alignof(FLODGenerationSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLODGenerationSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FLODGenerationSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FLODGenerationSettings.InnerSingleton, Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FLODGenerationSettings.InnerSingleton;
}
// ********** End ScriptStruct FLODGenerationSettings **********************************************

// ********** Begin ScriptStruct FMeshValidationResult *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMeshValidationResult;
class UScriptStruct* FMeshValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMeshValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMeshValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMeshValidationResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("MeshValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FMeshValidationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMeshValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Errors_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Warnings_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexCount_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleCount_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DegenerateTriangles_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DuplicateVertices_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityScore_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceScore_MetaData[] = {
		{ "Category", "Mesh Validation" },
		{ "ModuleRelativePath", "Public/AuracronMeshDeformation.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Errors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Errors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Warnings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Warnings;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VertexCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TriangleCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DegenerateTriangles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DuplicateVertices;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceScore;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMeshValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FMeshValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMeshValidationResult), &Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Errors_Inner = { "Errors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Errors = { "Errors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, Errors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Errors_MetaData), NewProp_Errors_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Warnings_Inner = { "Warnings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Warnings = { "Warnings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, Warnings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Warnings_MetaData), NewProp_Warnings_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_VertexCount = { "VertexCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, VertexCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexCount_MetaData), NewProp_VertexCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_TriangleCount = { "TriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, TriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleCount_MetaData), NewProp_TriangleCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_DegenerateTriangles = { "DegenerateTriangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, DegenerateTriangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DegenerateTriangles_MetaData), NewProp_DegenerateTriangles_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_DuplicateVertices = { "DuplicateVertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, DuplicateVertices), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DuplicateVertices_MetaData), NewProp_DuplicateVertices_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_QualityScore = { "QualityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, QualityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityScore_MetaData), NewProp_QualityScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_PerformanceScore = { "PerformanceScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMeshValidationResult, PerformanceScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceScore_MetaData), NewProp_PerformanceScore_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMeshValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Errors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Errors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Warnings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_Warnings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_VertexCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_TriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_DegenerateTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_DuplicateVertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_QualityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewProp_PerformanceScore,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMeshValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMeshValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"MeshValidationResult",
	Z_Construct_UScriptStruct_FMeshValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMeshValidationResult_Statics::PropPointers),
	sizeof(FMeshValidationResult),
	alignof(FMeshValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMeshValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMeshValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMeshValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FMeshValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMeshValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FMeshValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMeshValidationResult.InnerSingleton;
}
// ********** End ScriptStruct FMeshValidationResult ***********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EVertexManipulationType_StaticEnum, TEXT("EVertexManipulationType"), &Z_Registration_Info_UEnum_EVertexManipulationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2156960843U) },
		{ ENormalRecalculationType_StaticEnum, TEXT("ENormalRecalculationType"), &Z_Registration_Info_UEnum_ENormalRecalculationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1792928113U) },
		{ EUVPreservationType_StaticEnum, TEXT("EUVPreservationType"), &Z_Registration_Info_UEnum_EUVPreservationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4007960916U) },
		{ EMeshValidationType_StaticEnum, TEXT("EMeshValidationType"), &Z_Registration_Info_UEnum_EMeshValidationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 224447317U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FVertexData::StaticStruct, Z_Construct_UScriptStruct_FVertexData_Statics::NewStructOps, TEXT("VertexData"), &Z_Registration_Info_UScriptStruct_FVertexData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FVertexData), 1049633120U) },
		{ FMeshDeformationData::StaticStruct, Z_Construct_UScriptStruct_FMeshDeformationData_Statics::NewStructOps, TEXT("MeshDeformationData"), &Z_Registration_Info_UScriptStruct_FMeshDeformationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMeshDeformationData), 3447824284U) },
		{ FLODGenerationSettings::StaticStruct, Z_Construct_UScriptStruct_FLODGenerationSettings_Statics::NewStructOps, TEXT("LODGenerationSettings"), &Z_Registration_Info_UScriptStruct_FLODGenerationSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLODGenerationSettings), 3184517826U) },
		{ FMeshValidationResult::StaticStruct, Z_Construct_UScriptStruct_FMeshValidationResult_Statics::NewStructOps, TEXT("MeshValidationResult"), &Z_Registration_Info_UScriptStruct_FMeshValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMeshValidationResult), 2448291413U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h__Script_AuracronMetaHumanBridge_3382163400(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
