// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Mesh Generation Nodes Header
// Bridge 2.6: PCG Framework - Mesh Generation Nodes

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "Elements/PCGStaticMeshSpawner.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Materials/MaterialInterface.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "Math/UnrealMathUtility.h"

#include "AuracronPCGMeshGeneration.generated.h"

// Forward declarations
class UStaticMesh;
class UMaterialInterface;
class UProceduralMeshComponent;
class UInstancedStaticMeshComponent;

// Mesh generation types
UENUM(BlueprintType)
enum class EAuracronPCGMeshGenerationType : uint8
{
    StaticMesh          UMETA(DisplayName = "Static Mesh"),
    InstancedMesh       UMETA(DisplayName = "Instanced Mesh"),
    HierarchicalMesh    UMETA(DisplayName = "Hierarchical Instanced Mesh"),
    ProceduralMesh      UMETA(DisplayName = "Procedural Mesh"),
    SplineMesh          UMETA(DisplayName = "Spline Mesh"),
    SkeletalMesh        UMETA(DisplayName = "Skeletal Mesh")
};

// Mesh selection modes
UENUM(BlueprintType)
enum class EAuracronPCGMeshSelectionMode : uint8
{
    Random              UMETA(DisplayName = "Random"),
    ByAttribute         UMETA(DisplayName = "By Attribute"),
    ByDensity           UMETA(DisplayName = "By Density"),
    ByDistance          UMETA(DisplayName = "By Distance"),
    ByNormal            UMETA(DisplayName = "By Normal"),
    Sequential          UMETA(DisplayName = "Sequential"),
    Weighted            UMETA(DisplayName = "Weighted"),
    Custom              UMETA(DisplayName = "Custom Expression")
};

// Mesh combination modes
UENUM(BlueprintType)
enum class EAuracronPCGMeshCombineMode : uint8
{
    Merge               UMETA(DisplayName = "Merge"),
    Union               UMETA(DisplayName = "Union"),
    Subtract            UMETA(DisplayName = "Subtract"),
    Intersect           UMETA(DisplayName = "Intersect"),
    Append              UMETA(DisplayName = "Append"),
    Weld                UMETA(DisplayName = "Weld"),
    Simplify            UMETA(DisplayName = "Simplify")
};

// LOD generation modes
UENUM(BlueprintType)
enum class EAuracronPCGMeshLODGenerationMode : uint8
{
    None                UMETA(DisplayName = "None"),
    Automatic           UMETA(DisplayName = "Automatic"),
    Distance            UMETA(DisplayName = "Distance Based"),
    Density             UMETA(DisplayName = "Density Based"),
    Custom              UMETA(DisplayName = "Custom Rules")
};

// Collision generation modes
UENUM(BlueprintType)
enum class EAuracronPCGCollisionMode : uint8
{
    None                UMETA(DisplayName = "None"),
    Simple              UMETA(DisplayName = "Simple Collision"),
    Complex             UMETA(DisplayName = "Complex Collision"),
    ConvexHull          UMETA(DisplayName = "Convex Hull"),
    BoundingBox         UMETA(DisplayName = "Bounding Box"),
    BoundingSphere      UMETA(DisplayName = "Bounding Sphere"),
    Custom              UMETA(DisplayName = "Custom Collision")
};

// =============================================================================
// MESH ENTRY DESCRIPTOR
// =============================================================================

/**
 * Mesh Entry Descriptor
 * Describes a mesh entry with all its properties and settings
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGMeshGenerationEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    TSoftObjectPtr<UStaticMesh> Mesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    float Weight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialOverrides;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
    FVector LocalOffset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
    FRotator LocalRotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
    FVector LocalScale = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    FString SelectionAttribute = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    FVector2D AttributeRange = FVector2D(0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bGenerateLODs = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD", meta = (EditCondition = "bGenerateLODs"))
    int32 LODCount = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    EAuracronPCGCollisionMode CollisionMode = EAuracronPCGCollisionMode::Simple;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rendering")
    bool bCastShadows = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rendering")
    bool bReceiveDecals = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rendering")
    int32 RenderingLODBias = 0;

    FAuracronPCGMeshEntry()
    {
        Weight = 1.0f;
        LocalOffset = FVector::ZeroVector;
        LocalRotation = FRotator::ZeroRotator;
        LocalScale = FVector::OneVector;
        SelectionAttribute = TEXT("");
        AttributeRange = FVector2D(0.0f, 1.0f);
        bGenerateLODs = false;
        LODCount = 3;
        CollisionMode = EAuracronPCGCollisionMode::Simple;
        bCastShadows = true;
        bReceiveDecals = true;
        RenderingLODBias = 0;
    }
};

// =============================================================================
// PROCEDURAL MESH DESCRIPTOR
// =============================================================================

/**
 * Procedural Mesh Descriptor
 * Describes parameters for procedural mesh generation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGProceduralMeshDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FVector> Vertices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<int32> Triangles;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FVector> Normals;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FVector2D> UVs;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FColor> VertexColors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FProcMeshTangent> Tangents;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    TSoftObjectPtr<UMaterialInterface> Material;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bAutoGenerateNormals = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bAutoGenerateUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bAutoGenerateTangents = true;

    FAuracronPCGProceduralMeshDescriptor()
    {
        bAutoGenerateNormals = true;
        bAutoGenerateUVs = true;
        bAutoGenerateTangents = true;
    }
};

// =============================================================================
// ADVANCED STATIC MESH SPAWNER
// =============================================================================

/**
 * Advanced Static Mesh Spawner
 * Enhanced version of the native Static Mesh Spawner with advanced features
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedStaticMeshSpawnerSettings, FAuracronPCGAdvancedStaticMeshSpawnerElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedStaticMeshSpawnerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedStaticMeshSpawnerSettings();

    // Mesh entries
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Settings")
    TArray<FAuracronPCGMeshEntry> MeshEntries;

    // Selection settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    EAuracronPCGMeshSelectionMode SelectionMode = EAuracronPCGMeshSelectionMode::Random;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection", meta = (EditCondition = "SelectionMode == EAuracronPCGMeshSelectionMode::ByAttribute"))
    FString SelectionAttribute = TEXT("MeshIndex");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection", meta = (EditCondition = "SelectionMode == EAuracronPCGMeshSelectionMode::Custom"))
    FString SelectionExpression = TEXT("Density * 10");

    // Generation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    EAuracronPCGMeshGenerationType GenerationType = EAuracronPCGMeshGenerationType::StaticMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bUseHierarchicalInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 MaxInstancesPerComponent = 1000;

    // LOD settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    EAuracronPCGMeshLODGenerationMode LODMode = EAuracronPCGMeshLODGenerationMode::Automatic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD", meta = (EditCondition = "LODMode != EAuracronPCGMeshLODGenerationMode::None"))
    TArray<float> LODDistances;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD", meta = (EditCondition = "LODMode == EAuracronPCGMeshLODGenerationMode::Density"))
    float DensityLODThreshold = 0.5f;

    // Culling settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    bool bUseFrustumCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    bool bUseOcclusionCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    float CullingDistance = 10000.0f;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseGPUInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseBatching = true;

    // BatchSize is inherited from UAuracronPCGSettingsBase, no need to redeclare

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedStaticMeshSpawnerElement, UAuracronPCGAdvancedStaticMeshSpawnerSettings)

// =============================================================================
// INSTANCED MESH GENERATOR
// =============================================================================

/**
 * Instanced Mesh Generator
 * Generates instanced static meshes with advanced instancing features
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGInstancedMeshGeneratorSettings, FAuracronPCGInstancedMeshGeneratorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGInstancedMeshGeneratorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGInstancedMeshGeneratorSettings();

    // Mesh settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Settings")
    TArray<FAuracronPCGMeshEntry> MeshEntries;

    // Instancing settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing")
    bool bUseHierarchicalInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing")
    int32 MaxInstancesPerComponent = 2000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing")
    bool bAutoClusterInstances = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing", meta = (EditCondition = "bAutoClusterInstances"))
    float ClusterDistance = 1000.0f;

    // Variation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    bool bUsePerInstanceVariation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (EditCondition = "bUsePerInstanceVariation"))
    FVector ScaleVariation = FVector(0.1f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (EditCondition = "bUsePerInstanceVariation"))
    FVector RotationVariation = FVector(0.0f, 0.0f, 180.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (EditCondition = "bUsePerInstanceVariation"))
    bool bUseColorVariation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (EditCondition = "bUseColorVariation"))
    FLinearColor BaseColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (EditCondition = "bUseColorVariation"))
    FLinearColor ColorVariation = FLinearColor(0.1f, 0.1f, 0.1f, 0.0f);

    // Optimization settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bUseLODOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (EditCondition = "bUseLODOptimization"))
    TArray<float> LODScreenSizes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bUseImpostors = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (EditCondition = "bUseImpostors"))
    float ImpostorDistance = 5000.0f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGInstancedMeshGeneratorElement, UAuracronPCGInstancedMeshGeneratorSettings)

// =============================================================================
// PROCEDURAL MESH CREATOR
// =============================================================================

/**
 * Procedural Mesh Creator
 * Creates procedural meshes from point data and geometric primitives
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGProceduralMeshCreatorSettings, FAuracronPCGProceduralMeshCreatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGProceduralMeshCreatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGProceduralMeshCreatorSettings();

    // Mesh generation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    TArray<FAuracronPCGProceduralMeshDescriptor> MeshDescriptors;

    // Primitive generation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Primitives")
    bool bGenerateFromPrimitives = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Primitives", meta = (EditCondition = "bGenerateFromPrimitives"))
    bool bGenerateBoxes = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Primitives", meta = (EditCondition = "bGenerateFromPrimitives"))
    bool bGenerateSpheres = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Primitives", meta = (EditCondition = "bGenerateFromPrimitives"))
    bool bGenerateCylinders = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Primitives", meta = (EditCondition = "bGenerateFromPrimitives"))
    bool bGeneratePlanes = false;

    // Box generation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Box Generation", meta = (EditCondition = "bGenerateBoxes"))
    FVector BoxSize = FVector(100.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Box Generation", meta = (EditCondition = "bGenerateBoxes"))
    bool bUseAttributeForBoxSize = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Box Generation", meta = (EditCondition = "bUseAttributeForBoxSize"))
    FString BoxSizeAttribute = TEXT("BoxSize");

    // Sphere generation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sphere Generation", meta = (EditCondition = "bGenerateSpheres"))
    float SphereRadius = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sphere Generation", meta = (EditCondition = "bGenerateSpheres"))
    int32 SphereSegments = 16;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sphere Generation", meta = (EditCondition = "bGenerateSpheres"))
    bool bUseAttributeForRadius = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sphere Generation", meta = (EditCondition = "bUseAttributeForRadius"))
    FString RadiusAttribute = TEXT("Radius");

    // Mesh optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bOptimizeMesh = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (EditCondition = "bOptimizeMesh"))
    bool bWeldVertices = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (EditCondition = "bOptimizeMesh"))
    float WeldThreshold = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (EditCondition = "bOptimizeMesh"))
    bool bRemoveDegenerateTriangles = true;

    // UV generation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Generation")
    bool bGenerateUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Generation", meta = (EditCondition = "bGenerateUVs"))
    float UVScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Generation", meta = (EditCondition = "bGenerateUVs"))
    bool bUseWorldSpaceUVs = false;

    // Material settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    TSoftObjectPtr<UMaterialInterface> DefaultMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    bool bUsePerPointMaterials = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials", meta = (EditCondition = "bUsePerPointMaterials"))
    FString MaterialAttribute = TEXT("Material");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGProceduralMeshCreatorElement, UAuracronPCGProceduralMeshCreatorSettings)

// =============================================================================
// MESH COMBINER
// =============================================================================

/**
 * Mesh Combiner
 * Combines multiple meshes using various combination modes
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGMeshCombinerSettings, FAuracronPCGMeshCombinerElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGMeshCombinerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGMeshCombinerSettings();

    // Combination settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combination")
    EAuracronPCGMeshCombineMode CombineMode = EAuracronPCGMeshCombineMode::Merge;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combination")
    bool bPreserveMaterials = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combination")
    bool bPreserveUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combination")
    bool bPreserveVertexColors = false;

    // Merge settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Merge Settings", meta = (EditCondition = "CombineMode == EAuracronPCGMeshCombineMode::Merge"))
    bool bWeldVerticesOnMerge = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Merge Settings", meta = (EditCondition = "bWeldVerticesOnMerge"))
    float MergeWeldThreshold = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Merge Settings", meta = (EditCondition = "CombineMode == EAuracronPCGMeshCombineMode::Merge"))
    bool bOptimizeAfterMerge = true;

    // Boolean operation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Boolean Operations", meta = (EditCondition = "CombineMode == EAuracronPCGMeshCombineMode::Union || CombineMode == EAuracronPCGMeshCombineMode::Subtract || CombineMode == EAuracronPCGMeshCombineMode::Intersect"))
    float BooleanTolerance = 0.001f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Boolean Operations", meta = (EditCondition = "CombineMode == EAuracronPCGMeshCombineMode::Union || CombineMode == EAuracronPCGMeshCombineMode::Subtract || CombineMode == EAuracronPCGMeshCombineMode::Intersect"))
    bool bFillHoles = true;

    // Simplification settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification", meta = (EditCondition = "CombineMode == EAuracronPCGMeshCombineMode::Simplify"))
    float SimplificationRatio = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification", meta = (EditCondition = "CombineMode == EAuracronPCGMeshCombineMode::Simplify"))
    bool bPreserveBoundaries = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification", meta = (EditCondition = "CombineMode == EAuracronPCGMeshCombineMode::Simplify"))
    bool bPreserveUVBoundaries = true;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bGenerateCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output", meta = (EditCondition = "bGenerateCollision"))
    EAuracronPCGCollisionMode CollisionMode = EAuracronPCGCollisionMode::ConvexHull;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bGenerateLightmapUVs = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output", meta = (EditCondition = "bGenerateLightmapUVs"))
    int32 LightmapResolution = 64;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseMultithreading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "16"))
    int32 MaxConcurrentOperations = 4;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGMeshCombinerElement, UAuracronPCGMeshCombinerSettings)

// =============================================================================
// MESH GENERATION UTILITIES
// =============================================================================

/**
 * Mesh Generation Utilities
 * Utility functions for advanced mesh generation operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGMeshGenerationUtils : public UObject
{
    GENERATED_BODY()

public:
    // Mesh selection utilities
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static int32 SelectMeshIndex(const TArray<FAuracronPCGMeshEntry>& MeshEntries, const FPCGPoint& Point, const UPCGMetadata* Metadata, EAuracronPCGMeshSelectionMode SelectionMode, const FString& SelectionAttribute = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool ValidateMeshEntry(const FAuracronPCGMeshEntry& MeshEntry, FString& ValidationError);

    // Procedural mesh generation
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateBoxMesh(const FVector& Size, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateSphereMesh(float Radius, int32 Segments, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateCylinderMesh(float Radius, float Height, int32 Segments, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GeneratePlaneMesh(const FVector2D& Size, const FIntPoint& Subdivisions, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor);

    // Mesh optimization
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool OptimizeMesh(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, bool bWeldVertices = true, float WeldThreshold = 0.01f, bool bRemoveDegenerateTriangles = true);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateNormals(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, bool bSmoothNormals = true);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateUVs(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, float UVScale = 1.0f, bool bWorldSpace = false);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateTangents(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor);

    // Mesh combination
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool CombineMeshes(const TArray<FAuracronPCGProceduralMeshDescriptor>& InputMeshes, FAuracronPCGProceduralMeshDescriptor& OutCombinedMesh, EAuracronPCGMeshCombineMode CombineMode);

    // LOD generation
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateLODs(UStaticMesh* StaticMesh, const TArray<float>& LODDistances, int32 MaxLODCount = 4);

    // Collision generation
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool GenerateCollision(UStaticMesh* StaticMesh, EAuracronPCGCollisionMode CollisionMode);

    // Performance utilities
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static int32 GetOptimalInstanceCount(int32 TotalInstances, int32 MaxInstancesPerComponent = 1000);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation Utils")
    static bool ShouldUseHierarchicalInstancing(int32 InstanceCount, int32 Threshold = 100);
};

// Namespace for mesh generation utility functions
namespace AuracronPCGMeshGenerationUtils
{
    AURACRONPCGFRAMEWORK_API UStaticMesh* CreateStaticMeshFromProcMesh(const FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, const FString& MeshName);
    AURACRONPCGFRAMEWORK_API UInstancedStaticMeshComponent* CreateInstancedMeshComponent(AActor* Owner, UStaticMesh* Mesh, bool bUseHierarchical = true);
    AURACRONPCGFRAMEWORK_API bool SetupMeshMaterials(UMeshComponent* MeshComponent, const TArray<TSoftObjectPtr<UMaterialInterface>>& Materials);
    AURACRONPCGFRAMEWORK_API FTransform CalculateInstanceTransform(const FPCGPoint& Point, const FAuracronPCGMeshEntry& MeshEntry);
    AURACRONPCGFRAMEWORK_API bool ValidateMeshForInstancing(UStaticMesh* Mesh);
    AURACRONPCGFRAMEWORK_API void OptimizeInstancedMeshComponent(UInstancedStaticMeshComponent* Component);
    AURACRONPCGFRAMEWORK_API bool PerformBooleanOperation(const FAuracronPCGProceduralMeshDescriptor& MeshA, const FAuracronPCGProceduralMeshDescriptor& MeshB, EAuracronPCGMeshCombineMode Operation, FAuracronPCGProceduralMeshDescriptor& Result);
    AURACRONPCGFRAMEWORK_API void SimplifyMesh(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, float SimplificationRatio, bool bPreserveBoundaries = true);
    AURACRONPCGFRAMEWORK_API bool GenerateLightmapUVs(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, int32 Resolution = 64);
}
