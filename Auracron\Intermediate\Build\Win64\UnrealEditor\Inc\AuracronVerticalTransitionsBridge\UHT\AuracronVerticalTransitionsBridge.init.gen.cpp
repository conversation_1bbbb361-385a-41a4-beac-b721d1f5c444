// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronVerticalTransitionsBridge_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronVerticalTransitionsBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronVerticalTransitionsBridge.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronVerticalTransitionsBridge",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0xB89A3465,
				0x93228C2C,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronVerticalTransitionsBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronVerticalTransitionsBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronVerticalTransitionsBridge(Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge, TEXT("/Script/AuracronVerticalTransitionsBridge"), Z_Registration_Info_UPackage__Script_AuracronVerticalTransitionsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xB89A3465, 0x93228C2C));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
