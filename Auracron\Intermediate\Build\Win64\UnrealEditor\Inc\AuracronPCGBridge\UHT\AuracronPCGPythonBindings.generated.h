// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGPythonBindings.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGPythonBindings_generated_h
#error "AuracronPCGPythonBindings.generated.h already included, missing '#pragma once' in AuracronPCGPythonBindings.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGPythonBindings_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronPCGPythonBindingManager;
class UEnum;
class UFunction;
class UObject;
class UPCGGraph;
class UScriptStruct;
struct FAuracronPCGPythonBindingDescriptor;
struct FAuracronPCGPythonExecutionResult;

// ********** Begin ScriptStruct FAuracronPCGPythonBindingDescriptor *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_100_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGPythonBindingDescriptor;
// ********** End ScriptStruct FAuracronPCGPythonBindingDescriptor *********************************

// ********** Begin ScriptStruct FAuracronPCGPythonExecutionResult *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_202_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGPythonExecutionResult;
// ********** End ScriptStruct FAuracronPCGPythonExecutionResult ***********************************

// ********** Begin Class UAuracronPCGPythonBindingManager *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_244_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execHasErrors); \
	DECLARE_FUNCTION(execClearErrors); \
	DECLARE_FUNCTION(execGetLastErrors); \
	DECLARE_FUNCTION(execGetBindingDescriptor); \
	DECLARE_FUNCTION(execSetBindingDescriptor); \
	DECLARE_FUNCTION(execGetPythonVersion); \
	DECLARE_FUNCTION(execAddToPythonPath); \
	DECLARE_FUNCTION(execGetPythonPath); \
	DECLARE_FUNCTION(execReloadPythonModule); \
	DECLARE_FUNCTION(execImportPythonModule); \
	DECLARE_FUNCTION(execExecutePythonFunction); \
	DECLARE_FUNCTION(execExecutePythonFile); \
	DECLARE_FUNCTION(execExecutePythonCode); \
	DECLARE_FUNCTION(execBindStruct); \
	DECLARE_FUNCTION(execBindEnum); \
	DECLARE_FUNCTION(execBindFunction); \
	DECLARE_FUNCTION(execBindClass); \
	DECLARE_FUNCTION(execIsModuleRegistered); \
	DECLARE_FUNCTION(execGetRegisteredModules); \
	DECLARE_FUNCTION(execUnregisterModule); \
	DECLARE_FUNCTION(execRegisterModule); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdownPythonBindings); \
	DECLARE_FUNCTION(execInitializePythonBindings); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonBindingManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_244_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPythonBindingManager(); \
	friend struct Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonBindingManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPythonBindingManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPythonBindingManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPythonBindingManager)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_244_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGPythonBindingManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPythonBindingManager(UAuracronPCGPythonBindingManager&&) = delete; \
	UAuracronPCGPythonBindingManager(const UAuracronPCGPythonBindingManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPythonBindingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPythonBindingManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGPythonBindingManager) \
	NO_API virtual ~UAuracronPCGPythonBindingManager();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_241_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_244_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_244_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_244_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_244_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPythonBindingManager;

// ********** End Class UAuracronPCGPythonBindingManager *******************************************

// ********** Begin Class UAuracronPCGPythonScriptExecutor *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_377_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execHasScriptFunction); \
	DECLARE_FUNCTION(execGetScriptDependencies); \
	DECLARE_FUNCTION(execFormatScript); \
	DECLARE_FUNCTION(execValidateScriptFile); \
	DECLARE_FUNCTION(execValidateScript); \
	DECLARE_FUNCTION(execExecuteScriptFileAsync); \
	DECLARE_FUNCTION(execExecuteScriptAsync); \
	DECLARE_FUNCTION(execExecuteScriptWithGraph); \
	DECLARE_FUNCTION(execExecuteScriptFileSimple); \
	DECLARE_FUNCTION(execExecuteScriptFile); \
	DECLARE_FUNCTION(execExecuteScriptSimple); \
	DECLARE_FUNCTION(execExecuteScript);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_377_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPythonScriptExecutor(); \
	friend struct Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPythonScriptExecutor, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPythonScriptExecutor)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_377_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGPythonScriptExecutor(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPythonScriptExecutor(UAuracronPCGPythonScriptExecutor&&) = delete; \
	UAuracronPCGPythonScriptExecutor(const UAuracronPCGPythonScriptExecutor&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPythonScriptExecutor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPythonScriptExecutor); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGPythonScriptExecutor) \
	NO_API virtual ~UAuracronPCGPythonScriptExecutor();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_374_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_377_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_377_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_377_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_377_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPythonScriptExecutor;

// ********** End Class UAuracronPCGPythonScriptExecutor *******************************************

// ********** Begin Class UAuracronPCGPythonIntegrationUtils ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_436_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetPythonMemoryUsage); \
	DECLARE_FUNCTION(execGetPythonCallStack); \
	DECLARE_FUNCTION(execIsPythonDebuggingEnabled); \
	DECLARE_FUNCTION(execEnablePythonDebugging); \
	DECLARE_FUNCTION(execSavePythonStubs); \
	DECLARE_FUNCTION(execGeneratePythonWrapper); \
	DECLARE_FUNCTION(execGeneratePythonStub); \
	DECLARE_FUNCTION(execGenerateModuleDocumentation); \
	DECLARE_FUNCTION(execGenerateFunctionDocumentation); \
	DECLARE_FUNCTION(execGenerateClassDocumentation); \
	DECLARE_FUNCTION(execConvertPythonToMap); \
	DECLARE_FUNCTION(execConvertMapToPython); \
	DECLARE_FUNCTION(execConvertPythonToArray); \
	DECLARE_FUNCTION(execConvertArrayToPython); \
	DECLARE_FUNCTION(execConvertPythonToUObject); \
	DECLARE_FUNCTION(execConvertUObjectToPython);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_436_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPythonIntegrationUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPythonIntegrationUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPythonIntegrationUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_436_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGPythonIntegrationUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPythonIntegrationUtils(UAuracronPCGPythonIntegrationUtils&&) = delete; \
	UAuracronPCGPythonIntegrationUtils(const UAuracronPCGPythonIntegrationUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPythonIntegrationUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPythonIntegrationUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGPythonIntegrationUtils) \
	NO_API virtual ~UAuracronPCGPythonIntegrationUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_433_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_436_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_436_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_436_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h_436_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPythonIntegrationUtils;

// ********** End Class UAuracronPCGPythonIntegrationUtils *****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h

// ********** Begin Enum EAuracronPCGPythonBindingCategory *****************************************
#define FOREACH_ENUM_EAURACRONPCGPYTHONBINDINGCATEGORY(op) \
	op(EAuracronPCGPythonBindingCategory::Core) \
	op(EAuracronPCGPythonBindingCategory::Nodes) \
	op(EAuracronPCGPythonBindingCategory::Data) \
	op(EAuracronPCGPythonBindingCategory::Graph) \
	op(EAuracronPCGPythonBindingCategory::Execution) \
	op(EAuracronPCGPythonBindingCategory::Debug) \
	op(EAuracronPCGPythonBindingCategory::Custom) \
	op(EAuracronPCGPythonBindingCategory::Utilities) \
	op(EAuracronPCGPythonBindingCategory::All) 

enum class EAuracronPCGPythonBindingCategory : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPythonBindingCategory> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPythonBindingCategory>();
// ********** End Enum EAuracronPCGPythonBindingCategory *******************************************

// ********** Begin Enum EAuracronPCGPythonBindingMode *********************************************
#define FOREACH_ENUM_EAURACRONPCGPYTHONBINDINGMODE(op) \
	op(EAuracronPCGPythonBindingMode::ReadOnly) \
	op(EAuracronPCGPythonBindingMode::ReadWrite) \
	op(EAuracronPCGPythonBindingMode::ExecuteOnly) \
	op(EAuracronPCGPythonBindingMode::Full) 

enum class EAuracronPCGPythonBindingMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPythonBindingMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPythonBindingMode>();
// ********** End Enum EAuracronPCGPythonBindingMode ***********************************************

// ********** Begin Enum EAuracronPCGPythonExecutionContext ****************************************
#define FOREACH_ENUM_EAURACRONPCGPYTHONEXECUTIONCONTEXT(op) \
	op(EAuracronPCGPythonExecutionContext::Editor) \
	op(EAuracronPCGPythonExecutionContext::Runtime) \
	op(EAuracronPCGPythonExecutionContext::Commandlet) \
	op(EAuracronPCGPythonExecutionContext::Server) \
	op(EAuracronPCGPythonExecutionContext::Client) \
	op(EAuracronPCGPythonExecutionContext::Any) 

enum class EAuracronPCGPythonExecutionContext : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPythonExecutionContext> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPythonExecutionContext>();
// ********** End Enum EAuracronPCGPythonExecutionContext ******************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
