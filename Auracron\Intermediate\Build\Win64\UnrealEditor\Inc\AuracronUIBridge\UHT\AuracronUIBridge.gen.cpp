// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronUIBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronUIBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONUIBRIDGE_API UClass* Z_Construct_UClass_UAuracronUIBridge();
AURACRONUIBRIDGE_API UClass* Z_Construct_UClass_UAuracronUIBridge_NoRegister();
AURACRONUIBRIDGE_API UEnum* Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform();
AURACRONUIBRIDGE_API UEnum* Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState();
AURACRONUIBRIDGE_API UEnum* Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType();
AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature();
AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature();
AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature();
AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature();
AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature();
AURACRONUIBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration();
AURACRONUIBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration();
AURACRONUIBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMinimapConfiguration();
AURACRONUIBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronUIConfiguration();
AURACRONUIBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronUIStateEntry();
COMMONINPUT_API UClass* Z_Construct_UClass_UCommonInputSubsystem_NoRegister();
COMMONUI_API UClass* Z_Construct_UClass_UCommonUserWidget_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UEnhancedInputComponent_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputMappingContext_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UPackage* Z_Construct_UPackage__Script_AuracronUIBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronUIType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronUIType;
static UEnum* EAuracronUIType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronUIType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronUIType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("EAuracronUIType"));
	}
	return Z_Registration_Info_UEnum_EAuracronUIType.OuterSingleton;
}
template<> AURACRONUIBRIDGE_API UEnum* StaticEnum<EAuracronUIType>()
{
	return EAuracronUIType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AbilityBar.DisplayName", "Ability Bar" },
		{ "AbilityBar.Name", "EAuracronUIType::AbilityBar" },
		{ "BlueprintType", "true" },
		{ "ChampionSelect.DisplayName", "Champion Select" },
		{ "ChampionSelect.Name", "EAuracronUIType::ChampionSelect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de UI\n */" },
#endif
		{ "ConfirmDialog.DisplayName", "Confirm Dialog" },
		{ "ConfirmDialog.Name", "EAuracronUIType::ConfirmDialog" },
		{ "ErrorDialog.DisplayName", "Error Dialog" },
		{ "ErrorDialog.Name", "EAuracronUIType::ErrorDialog" },
		{ "InGameHUD.DisplayName", "In-Game HUD" },
		{ "InGameHUD.Name", "EAuracronUIType::InGameHUD" },
		{ "InventoryMenu.DisplayName", "Inventory Menu" },
		{ "InventoryMenu.Name", "EAuracronUIType::InventoryMenu" },
		{ "LoadingScreen.DisplayName", "Loading Screen" },
		{ "LoadingScreen.Name", "EAuracronUIType::LoadingScreen" },
		{ "MainMenu.DisplayName", "Main Menu" },
		{ "MainMenu.Name", "EAuracronUIType::MainMenu" },
		{ "Minimap.DisplayName", "Minimap" },
		{ "Minimap.Name", "EAuracronUIType::Minimap" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronUIType::None" },
		{ "ProgressionMenu.DisplayName", "Progression Menu" },
		{ "ProgressionMenu.Name", "EAuracronUIType::ProgressionMenu" },
		{ "ScoreBoard.DisplayName", "Score Board" },
		{ "ScoreBoard.Name", "EAuracronUIType::ScoreBoard" },
		{ "SettingsMenu.DisplayName", "Settings Menu" },
		{ "SettingsMenu.Name", "EAuracronUIType::SettingsMenu" },
		{ "ShopMenu.DisplayName", "Shop Menu" },
		{ "ShopMenu.Name", "EAuracronUIType::ShopMenu" },
		{ "SigilosMenu.DisplayName", "Sigilos Menu" },
		{ "SigilosMenu.Name", "EAuracronUIType::SigilosMenu" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de UI" },
#endif
		{ "Tutorial.DisplayName", "Tutorial" },
		{ "Tutorial.Name", "EAuracronUIType::Tutorial" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronUIType::None", (int64)EAuracronUIType::None },
		{ "EAuracronUIType::MainMenu", (int64)EAuracronUIType::MainMenu },
		{ "EAuracronUIType::InGameHUD", (int64)EAuracronUIType::InGameHUD },
		{ "EAuracronUIType::ChampionSelect", (int64)EAuracronUIType::ChampionSelect },
		{ "EAuracronUIType::SigilosMenu", (int64)EAuracronUIType::SigilosMenu },
		{ "EAuracronUIType::ProgressionMenu", (int64)EAuracronUIType::ProgressionMenu },
		{ "EAuracronUIType::SettingsMenu", (int64)EAuracronUIType::SettingsMenu },
		{ "EAuracronUIType::ScoreBoard", (int64)EAuracronUIType::ScoreBoard },
		{ "EAuracronUIType::Minimap", (int64)EAuracronUIType::Minimap },
		{ "EAuracronUIType::AbilityBar", (int64)EAuracronUIType::AbilityBar },
		{ "EAuracronUIType::InventoryMenu", (int64)EAuracronUIType::InventoryMenu },
		{ "EAuracronUIType::ShopMenu", (int64)EAuracronUIType::ShopMenu },
		{ "EAuracronUIType::Tutorial", (int64)EAuracronUIType::Tutorial },
		{ "EAuracronUIType::LoadingScreen", (int64)EAuracronUIType::LoadingScreen },
		{ "EAuracronUIType::ErrorDialog", (int64)EAuracronUIType::ErrorDialog },
		{ "EAuracronUIType::ConfirmDialog", (int64)EAuracronUIType::ConfirmDialog },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	"EAuracronUIType",
	"EAuracronUIType",
	Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType()
{
	if (!Z_Registration_Info_UEnum_EAuracronUIType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronUIType.InnerSingleton, Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronUIType.InnerSingleton;
}
// ********** End Enum EAuracronUIType *************************************************************

// ********** Begin Enum EAuracronInputPlatform ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronInputPlatform;
static UEnum* EAuracronInputPlatform_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronInputPlatform.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronInputPlatform.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("EAuracronInputPlatform"));
	}
	return Z_Registration_Info_UEnum_EAuracronInputPlatform.OuterSingleton;
}
template<> AURACRONUIBRIDGE_API UEnum* StaticEnum<EAuracronInputPlatform>()
{
	return EAuracronInputPlatform_StaticEnum();
}
struct Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Auto.DisplayName", "Auto Detect" },
		{ "Auto.Name", "EAuracronInputPlatform::Auto" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para plataformas de input\n */" },
#endif
		{ "Console.DisplayName", "Console (Gamepad)" },
		{ "Console.Name", "EAuracronInputPlatform::Console" },
		{ "Mobile.DisplayName", "Mobile (Touch)" },
		{ "Mobile.Name", "EAuracronInputPlatform::Mobile" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
		{ "PC.DisplayName", "PC (Keyboard/Mouse)" },
		{ "PC.Name", "EAuracronInputPlatform::PC" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para plataformas de input" },
#endif
		{ "VR.DisplayName", "VR" },
		{ "VR.Name", "EAuracronInputPlatform::VR" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronInputPlatform::PC", (int64)EAuracronInputPlatform::PC },
		{ "EAuracronInputPlatform::Console", (int64)EAuracronInputPlatform::Console },
		{ "EAuracronInputPlatform::Mobile", (int64)EAuracronInputPlatform::Mobile },
		{ "EAuracronInputPlatform::VR", (int64)EAuracronInputPlatform::VR },
		{ "EAuracronInputPlatform::Auto", (int64)EAuracronInputPlatform::Auto },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	"EAuracronInputPlatform",
	"EAuracronInputPlatform",
	Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform()
{
	if (!Z_Registration_Info_UEnum_EAuracronInputPlatform.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronInputPlatform.InnerSingleton, Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronInputPlatform.InnerSingleton;
}
// ********** End Enum EAuracronInputPlatform ******************************************************

// ********** Begin Enum EAuracronUIState **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronUIState;
static UEnum* EAuracronUIState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronUIState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronUIState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("EAuracronUIState"));
	}
	return Z_Registration_Info_UEnum_EAuracronUIState.OuterSingleton;
}
template<> AURACRONUIBRIDGE_API UEnum* StaticEnum<EAuracronUIState>()
{
	return EAuracronUIState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados de UI\n */" },
#endif
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EAuracronUIState::Disabled" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronUIState::Error" },
		{ "Hidden.DisplayName", "Hidden" },
		{ "Hidden.Name", "EAuracronUIState::Hidden" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronUIState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados de UI" },
#endif
		{ "Transitioning.DisplayName", "Transitioning" },
		{ "Transitioning.Name", "EAuracronUIState::Transitioning" },
		{ "Visible.DisplayName", "Visible" },
		{ "Visible.Name", "EAuracronUIState::Visible" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronUIState::Hidden", (int64)EAuracronUIState::Hidden },
		{ "EAuracronUIState::Visible", (int64)EAuracronUIState::Visible },
		{ "EAuracronUIState::Transitioning", (int64)EAuracronUIState::Transitioning },
		{ "EAuracronUIState::Loading", (int64)EAuracronUIState::Loading },
		{ "EAuracronUIState::Error", (int64)EAuracronUIState::Error },
		{ "EAuracronUIState::Disabled", (int64)EAuracronUIState::Disabled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	"EAuracronUIState",
	"EAuracronUIState",
	Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState()
{
	if (!Z_Registration_Info_UEnum_EAuracronUIState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronUIState.InnerSingleton, Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronUIState.InnerSingleton;
}
// ********** End Enum EAuracronUIState ************************************************************

// ********** Begin ScriptStruct FAuracronUIStateEntry *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry;
class UScriptStruct* FAuracronUIStateEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronUIStateEntry, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("AuracronUIStateEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de estado de UI (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de estado de UI (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIType_MetaData[] = {
		{ "Category", "UI State" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIState_MetaData[] = {
		{ "Category", "UI State" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronUIStateEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIStateEntry, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIType_MetaData), NewProp_UIType_MetaData) }; // 1177635711
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIState = { "UIState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIStateEntry, UIState), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIState_MetaData), NewProp_UIState_MetaData) }; // 182551264
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewProp_UIState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	&NewStructOps,
	"AuracronUIStateEntry",
	Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::PropPointers),
	sizeof(FAuracronUIStateEntry),
	alignof(FAuracronUIStateEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronUIStateEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronUIStateEntry ***********************************************

// ********** Begin ScriptStruct FAuracronUIConfiguration ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration;
class UScriptStruct* FAuracronUIConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronUIConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("AuracronUIConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de UI adaptativa\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de UI adaptativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIScale_MetaData[] = {
		{ "Category", "UI Configuration" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala da UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala da UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIOpacity_MetaData[] = {
		{ "Category", "UI Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Opacidade da UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Opacidade da UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputPlatform_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Plataforma de input atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Plataforma de input atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseUIAnimations_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationSpeed_MetaData[] = {
		{ "Category", "UI Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade das anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade das anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseUIParticleEffects_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar efeitos de part\xc3\x83\xc2\xad""culas na UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar efeitos de part\xc3\x83\xc2\xad""culas na UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseUISounds_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar sons de UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar sons de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UISoundVolume_MetaData[] = {
		{ "Category", "UI Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume dos sons de UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume dos sons de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHapticFeedback_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar haptic feedback */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar haptic feedback" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HapticIntensity_MetaData[] = {
		{ "Category", "UI Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do haptic feedback */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do haptic feedback" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHighContrastMode_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Modo de alto contraste */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modo de alto contraste" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bColorBlindSupport_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Suporte a daltonismo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Suporte a daltonismo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorBlindnessType_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de daltonismo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de daltonismo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FontScale_MetaData[] = {
		{ "Category", "UI Configuration" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho da fonte */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho da fonte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bScreenReaderSupport_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar leitor de tela */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar leitor de tela" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UILanguage_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Idioma da UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Idioma da UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIRegion_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regi\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseResponsiveLayout_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar layout responsivo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar layout responsivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferenceResolution_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de refer\xc3\x83\xc2\xaancia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de refer\xc3\x83\xc2\xaancia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSafeZones_MetaData[] = {
		{ "Category", "UI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar safe zones */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar safe zones" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SafeZoneMargin_MetaData[] = {
		{ "Category", "UI Configuration" },
		{ "ClampMax", "0.2" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Margem das safe zones */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Margem das safe zones" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UIScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UIOpacity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InputPlatform_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InputPlatform;
	static void NewProp_bUseUIAnimations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseUIAnimations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationSpeed;
	static void NewProp_bUseUIParticleEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseUIParticleEffects;
	static void NewProp_bUseUISounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseUISounds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UISoundVolume;
	static void NewProp_bUseHapticFeedback_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHapticFeedback;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HapticIntensity;
	static void NewProp_bHighContrastMode_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHighContrastMode;
	static void NewProp_bColorBlindSupport_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bColorBlindSupport;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ColorBlindnessType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FontScale;
	static void NewProp_bScreenReaderSupport_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bScreenReaderSupport;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UILanguage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UIRegion;
	static void NewProp_bUseResponsiveLayout_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseResponsiveLayout;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReferenceResolution;
	static void NewProp_bUseSafeZones_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSafeZones;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SafeZoneMargin;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronUIConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UIScale = { "UIScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, UIScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIScale_MetaData), NewProp_UIScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UIOpacity = { "UIOpacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, UIOpacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIOpacity_MetaData), NewProp_UIOpacity_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_InputPlatform_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_InputPlatform = { "InputPlatform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, InputPlatform), Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputPlatform_MetaData), NewProp_InputPlatform_MetaData) }; // 2897940699
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIAnimations_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bUseUIAnimations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIAnimations = { "bUseUIAnimations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIAnimations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseUIAnimations_MetaData), NewProp_bUseUIAnimations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_AnimationSpeed = { "AnimationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, AnimationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationSpeed_MetaData), NewProp_AnimationSpeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIParticleEffects_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bUseUIParticleEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIParticleEffects = { "bUseUIParticleEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIParticleEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseUIParticleEffects_MetaData), NewProp_bUseUIParticleEffects_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUISounds_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bUseUISounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUISounds = { "bUseUISounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUISounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseUISounds_MetaData), NewProp_bUseUISounds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UISoundVolume = { "UISoundVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, UISoundVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UISoundVolume_MetaData), NewProp_UISoundVolume_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseHapticFeedback_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bUseHapticFeedback = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseHapticFeedback = { "bUseHapticFeedback", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseHapticFeedback_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHapticFeedback_MetaData), NewProp_bUseHapticFeedback_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_HapticIntensity = { "HapticIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, HapticIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HapticIntensity_MetaData), NewProp_HapticIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bHighContrastMode_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bHighContrastMode = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bHighContrastMode = { "bHighContrastMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bHighContrastMode_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHighContrastMode_MetaData), NewProp_bHighContrastMode_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bColorBlindSupport_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bColorBlindSupport = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bColorBlindSupport = { "bColorBlindSupport", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bColorBlindSupport_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bColorBlindSupport_MetaData), NewProp_bColorBlindSupport_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_ColorBlindnessType = { "ColorBlindnessType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, ColorBlindnessType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorBlindnessType_MetaData), NewProp_ColorBlindnessType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_FontScale = { "FontScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, FontScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FontScale_MetaData), NewProp_FontScale_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bScreenReaderSupport_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bScreenReaderSupport = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bScreenReaderSupport = { "bScreenReaderSupport", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bScreenReaderSupport_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bScreenReaderSupport_MetaData), NewProp_bScreenReaderSupport_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UILanguage = { "UILanguage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, UILanguage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UILanguage_MetaData), NewProp_UILanguage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UIRegion = { "UIRegion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, UIRegion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIRegion_MetaData), NewProp_UIRegion_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseResponsiveLayout_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bUseResponsiveLayout = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseResponsiveLayout = { "bUseResponsiveLayout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseResponsiveLayout_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseResponsiveLayout_MetaData), NewProp_bUseResponsiveLayout_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_ReferenceResolution = { "ReferenceResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, ReferenceResolution), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferenceResolution_MetaData), NewProp_ReferenceResolution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseSafeZones_SetBit(void* Obj)
{
	((FAuracronUIConfiguration*)Obj)->bUseSafeZones = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseSafeZones = { "bUseSafeZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUIConfiguration), &Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseSafeZones_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSafeZones_MetaData), NewProp_bUseSafeZones_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_SafeZoneMargin = { "SafeZoneMargin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUIConfiguration, SafeZoneMargin), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SafeZoneMargin_MetaData), NewProp_SafeZoneMargin_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UIScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UIOpacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_InputPlatform_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_InputPlatform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIAnimations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_AnimationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUIParticleEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseUISounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UISoundVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseHapticFeedback,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_HapticIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bHighContrastMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bColorBlindSupport,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_ColorBlindnessType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_FontScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bScreenReaderSupport,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UILanguage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_UIRegion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseResponsiveLayout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_ReferenceResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_bUseSafeZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewProp_SafeZoneMargin,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	&NewStructOps,
	"AuracronUIConfiguration",
	Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::PropPointers),
	sizeof(FAuracronUIConfiguration),
	alignof(FAuracronUIConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronUIConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronUIConfiguration ********************************************

// ********** Begin ScriptStruct FAuracronMinimapConfiguration *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration;
class UScriptStruct* FAuracronMinimapConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMinimapConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("AuracronMinimapConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de minimapa 3D\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de minimapa 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimapSize_MetaData[] = {
		{ "Category", "Minimap Configuration" },
		{ "ClampMax", "500.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho do minimapa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho do minimapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimapPosition_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do minimapa na tela */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do minimapa na tela" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimapZoom_MetaData[] = {
		{ "Category", "Minimap Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Zoom do minimapa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Zoom do minimapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimapRotation_MetaData[] = {
		{ "Category", "Minimap Configuration" },
		{ "ClampMax", "360.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do minimapa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do minimapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowAllRealms_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar todos os realms */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar todos os realms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowAlliedPlayers_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar jogadores aliados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar jogadores aliados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowEnemyPlayers_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar jogadores inimigos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar jogadores inimigos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowObjectives_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowWards_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar wards */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar wards" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseTeamColors_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar cores de time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar cores de time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlliedTeamColor_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do time aliado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do time aliado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyTeamColor_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do time inimigo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do time inimigo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveColor_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dos objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dos objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRealTimeUpdate_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em tempo real */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em tempo real" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateFrequency_MetaData[] = {
		{ "Category", "Minimap Configuration" },
		{ "ClampMax", "60" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\x83\xc2\xaancia de atualiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (Hz) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\x83\xc2\xaancia de atualiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o (Hz)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFogOfWar_MetaData[] = {
		{ "Category", "Minimap Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar fog of war */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar fog of war" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionRadius_MetaData[] = {
		{ "Category", "Minimap Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de vis\xc3\x83\xc2\xa3o no minimapa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de vis\xc3\x83\xc2\xa3o no minimapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MinimapSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MinimapPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinimapZoom;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinimapRotation;
	static void NewProp_bShowAllRealms_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowAllRealms;
	static void NewProp_bShowAlliedPlayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowAlliedPlayers;
	static void NewProp_bShowEnemyPlayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowEnemyPlayers;
	static void NewProp_bShowObjectives_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowObjectives;
	static void NewProp_bShowWards_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowWards;
	static void NewProp_bUseTeamColors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseTeamColors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AlliedTeamColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnemyTeamColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveColor;
	static void NewProp_bRealTimeUpdate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRealTimeUpdate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpdateFrequency;
	static void NewProp_bUseFogOfWar_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFogOfWar;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMinimapConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapSize = { "MinimapSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, MinimapSize), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimapSize_MetaData), NewProp_MinimapSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapPosition = { "MinimapPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, MinimapPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimapPosition_MetaData), NewProp_MinimapPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapZoom = { "MinimapZoom", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, MinimapZoom), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimapZoom_MetaData), NewProp_MinimapZoom_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapRotation = { "MinimapRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, MinimapRotation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimapRotation_MetaData), NewProp_MinimapRotation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAllRealms_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bShowAllRealms = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAllRealms = { "bShowAllRealms", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAllRealms_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowAllRealms_MetaData), NewProp_bShowAllRealms_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAlliedPlayers_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bShowAlliedPlayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAlliedPlayers = { "bShowAlliedPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAlliedPlayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowAlliedPlayers_MetaData), NewProp_bShowAlliedPlayers_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowEnemyPlayers_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bShowEnemyPlayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowEnemyPlayers = { "bShowEnemyPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowEnemyPlayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowEnemyPlayers_MetaData), NewProp_bShowEnemyPlayers_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowObjectives_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bShowObjectives = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowObjectives = { "bShowObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowObjectives_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowObjectives_MetaData), NewProp_bShowObjectives_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowWards_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bShowWards = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowWards = { "bShowWards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowWards_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowWards_MetaData), NewProp_bShowWards_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseTeamColors_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bUseTeamColors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseTeamColors = { "bUseTeamColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseTeamColors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseTeamColors_MetaData), NewProp_bUseTeamColors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_AlliedTeamColor = { "AlliedTeamColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, AlliedTeamColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlliedTeamColor_MetaData), NewProp_AlliedTeamColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_EnemyTeamColor = { "EnemyTeamColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, EnemyTeamColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyTeamColor_MetaData), NewProp_EnemyTeamColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_ObjectiveColor = { "ObjectiveColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, ObjectiveColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveColor_MetaData), NewProp_ObjectiveColor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bRealTimeUpdate_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bRealTimeUpdate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bRealTimeUpdate = { "bRealTimeUpdate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bRealTimeUpdate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRealTimeUpdate_MetaData), NewProp_bRealTimeUpdate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_UpdateFrequency = { "UpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, UpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateFrequency_MetaData), NewProp_UpdateFrequency_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseFogOfWar_SetBit(void* Obj)
{
	((FAuracronMinimapConfiguration*)Obj)->bUseFogOfWar = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseFogOfWar = { "bUseFogOfWar", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMinimapConfiguration), &Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseFogOfWar_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFogOfWar_MetaData), NewProp_bUseFogOfWar_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_VisionRadius = { "VisionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMinimapConfiguration, VisionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionRadius_MetaData), NewProp_VisionRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapZoom,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_MinimapRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAllRealms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowAlliedPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowEnemyPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bShowWards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseTeamColors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_AlliedTeamColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_EnemyTeamColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_ObjectiveColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bRealTimeUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_UpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_bUseFogOfWar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewProp_VisionRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	&NewStructOps,
	"AuracronMinimapConfiguration",
	Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::PropPointers),
	sizeof(FAuracronMinimapConfiguration),
	alignof(FAuracronMinimapConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMinimapConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMinimapConfiguration ***************************************

// ********** Begin ScriptStruct FAuracronCombatHUDConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration;
class UScriptStruct* FAuracronCombatHUDConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("AuracronCombatHUDConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de HUD de combate\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de HUD de combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowHealthBar_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar barra de HP */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar barra de HP" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowManaBar_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar barra de mana */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar barra de mana" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowAbilityCooldowns_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar cooldowns de habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar cooldowns de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDamageIndicators_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar indicadores de dano */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar indicadores de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowHealingIndicators_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar indicadores de cura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar indicadores de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowStatusEffects_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar buffs/debuffs */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar buffs/debuffs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowExperienceBar_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar XP e n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar XP e n\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowGold_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar gold */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar gold" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowKDA_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar KDA */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar KDA" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowMatchTimer_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar timer da partida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar timer da partida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthBarPosition_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da barra de HP */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da barra de HP" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthBarSize_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho da barra de HP */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho da barra de HP" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaBarPosition_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da barra de mana */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da barra de mana" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaBarSize_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho da barra de mana */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho da barra de mana" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitiesPosition_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o das habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o das habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityIconSize_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
		{ "ClampMax", "128.0" },
		{ "ClampMin", "32.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho dos \xc3\x83\xc2\xad""cones de habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho dos \xc3\x83\xc2\xad""cones de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySpacing_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
		{ "ClampMax", "20.0" },
		{ "ClampMin", "2.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Espa\xc3\x83\xc2\xa7""amento entre habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Espa\xc3\x83\xc2\xa7""amento entre habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCompactLayout_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar layout compacto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar layout compacto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowTooltips_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mostrar tooltips */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar tooltips" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TooltipDelay_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delay para mostrar tooltips */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delay para mostrar tooltips" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseTeamColors_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar cores de time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar cores de time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthColor_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do HP */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do HP" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaColor_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da mana */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da mana" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceColor_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do XP */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do XP" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownColor_MetaData[] = {
		{ "Category", "Combat HUD Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dos cooldowns */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dos cooldowns" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bShowHealthBar_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowHealthBar;
	static void NewProp_bShowManaBar_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowManaBar;
	static void NewProp_bShowAbilityCooldowns_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowAbilityCooldowns;
	static void NewProp_bShowDamageIndicators_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDamageIndicators;
	static void NewProp_bShowHealingIndicators_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowHealingIndicators;
	static void NewProp_bShowStatusEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowStatusEffects;
	static void NewProp_bShowExperienceBar_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowExperienceBar;
	static void NewProp_bShowGold_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowGold;
	static void NewProp_bShowKDA_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowKDA;
	static void NewProp_bShowMatchTimer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowMatchTimer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HealthBarPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HealthBarSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ManaBarPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ManaBarSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AbilitiesPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityIconSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilitySpacing;
	static void NewProp_bUseCompactLayout_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCompactLayout;
	static void NewProp_bShowTooltips_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowTooltips;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TooltipDelay;
	static void NewProp_bUseTeamColors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseTeamColors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HealthColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ManaColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExperienceColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CooldownColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCombatHUDConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealthBar_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowHealthBar = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealthBar = { "bShowHealthBar", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealthBar_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowHealthBar_MetaData), NewProp_bShowHealthBar_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowManaBar_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowManaBar = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowManaBar = { "bShowManaBar", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowManaBar_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowManaBar_MetaData), NewProp_bShowManaBar_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowAbilityCooldowns_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowAbilityCooldowns = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowAbilityCooldowns = { "bShowAbilityCooldowns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowAbilityCooldowns_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowAbilityCooldowns_MetaData), NewProp_bShowAbilityCooldowns_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowDamageIndicators_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowDamageIndicators = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowDamageIndicators = { "bShowDamageIndicators", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowDamageIndicators_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDamageIndicators_MetaData), NewProp_bShowDamageIndicators_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealingIndicators_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowHealingIndicators = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealingIndicators = { "bShowHealingIndicators", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealingIndicators_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowHealingIndicators_MetaData), NewProp_bShowHealingIndicators_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowStatusEffects_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowStatusEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowStatusEffects = { "bShowStatusEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowStatusEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowStatusEffects_MetaData), NewProp_bShowStatusEffects_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowExperienceBar_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowExperienceBar = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowExperienceBar = { "bShowExperienceBar", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowExperienceBar_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowExperienceBar_MetaData), NewProp_bShowExperienceBar_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowGold_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowGold = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowGold = { "bShowGold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowGold_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowGold_MetaData), NewProp_bShowGold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowKDA_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowKDA = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowKDA = { "bShowKDA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowKDA_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowKDA_MetaData), NewProp_bShowKDA_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowMatchTimer_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowMatchTimer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowMatchTimer = { "bShowMatchTimer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowMatchTimer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowMatchTimer_MetaData), NewProp_bShowMatchTimer_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_HealthBarPosition = { "HealthBarPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, HealthBarPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthBarPosition_MetaData), NewProp_HealthBarPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_HealthBarSize = { "HealthBarSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, HealthBarSize), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthBarSize_MetaData), NewProp_HealthBarSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ManaBarPosition = { "ManaBarPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, ManaBarPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaBarPosition_MetaData), NewProp_ManaBarPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ManaBarSize = { "ManaBarSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, ManaBarSize), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaBarSize_MetaData), NewProp_ManaBarSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_AbilitiesPosition = { "AbilitiesPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, AbilitiesPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitiesPosition_MetaData), NewProp_AbilitiesPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_AbilityIconSize = { "AbilityIconSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, AbilityIconSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityIconSize_MetaData), NewProp_AbilityIconSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_AbilitySpacing = { "AbilitySpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, AbilitySpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySpacing_MetaData), NewProp_AbilitySpacing_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseCompactLayout_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bUseCompactLayout = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseCompactLayout = { "bUseCompactLayout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseCompactLayout_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCompactLayout_MetaData), NewProp_bUseCompactLayout_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowTooltips_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bShowTooltips = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowTooltips = { "bShowTooltips", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowTooltips_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowTooltips_MetaData), NewProp_bShowTooltips_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_TooltipDelay = { "TooltipDelay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, TooltipDelay), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TooltipDelay_MetaData), NewProp_TooltipDelay_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseTeamColors_SetBit(void* Obj)
{
	((FAuracronCombatHUDConfiguration*)Obj)->bUseTeamColors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseTeamColors = { "bUseTeamColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatHUDConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseTeamColors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseTeamColors_MetaData), NewProp_bUseTeamColors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_HealthColor = { "HealthColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, HealthColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthColor_MetaData), NewProp_HealthColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ManaColor = { "ManaColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, ManaColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaColor_MetaData), NewProp_ManaColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ExperienceColor = { "ExperienceColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, ExperienceColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceColor_MetaData), NewProp_ExperienceColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_CooldownColor = { "CooldownColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatHUDConfiguration, CooldownColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownColor_MetaData), NewProp_CooldownColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealthBar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowManaBar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowAbilityCooldowns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowDamageIndicators,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowHealingIndicators,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowStatusEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowExperienceBar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowGold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowKDA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowMatchTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_HealthBarPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_HealthBarSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ManaBarPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ManaBarSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_AbilitiesPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_AbilityIconSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_AbilitySpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseCompactLayout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bShowTooltips,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_TooltipDelay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_bUseTeamColors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_HealthColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ManaColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_ExperienceColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewProp_CooldownColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	&NewStructOps,
	"AuracronCombatHUDConfiguration",
	Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::PropPointers),
	sizeof(FAuracronCombatHUDConfiguration),
	alignof(FAuracronCombatHUDConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCombatHUDConfiguration *************************************

// ********** Begin ScriptStruct FAuracronCrossPlatformInputConfiguration **************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration;
class UScriptStruct* FAuracronCrossPlatformInputConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronUIBridge(), TEXT("AuracronCrossPlatformInputConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input cross-platform\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input cross-platform" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCInputContext_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contexto de mapeamento para PC */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contexto de mapeamento para PC" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConsoleInputContext_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contexto de mapeamento para Console */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contexto de mapeamento para Console" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MobileInputContext_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contexto de mapeamento para Mobile */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contexto de mapeamento para Mobile" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MouseSensitivity_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sensibilidade do mouse */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensibilidade do mouse" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GamepadSensitivity_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sensibilidade do gamepad */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensibilidade do gamepad" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TouchSensitivity_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sensibilidade do touch */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensibilidade do touch" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertYAxis_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inverter eixo Y */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inverter eixo Y" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAutoAimOnMobile_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar auto-aim em mobile */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar auto-aim em mobile" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AutoAimIntensity_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do auto-aim */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do auto-aim" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseTouchGestures_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar gestos touch */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar gestos touch" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TouchButtonSize_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
		{ "ClampMax", "128.0" },
		{ "ClampMin", "32.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho dos bot\xc3\x83\xc2\xb5""es touch */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho dos bot\xc3\x83\xc2\xb5""es touch" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TouchButtonOpacity_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Opacidade dos bot\xc3\x83\xc2\xb5""es touch */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Opacidade dos bot\xc3\x83\xc2\xb5""es touch" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVibration_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar vibra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar vibra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VibrationIntensity_MetaData[] = {
		{ "Category", "Cross-Platform Input" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da vibra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da vibra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PCInputContext;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ConsoleInputContext;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MobileInputContext;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MouseSensitivity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GamepadSensitivity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TouchSensitivity;
	static void NewProp_bInvertYAxis_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertYAxis;
	static void NewProp_bUseAutoAimOnMobile_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAutoAimOnMobile;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AutoAimIntensity;
	static void NewProp_bUseTouchGestures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseTouchGestures;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TouchButtonSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TouchButtonOpacity;
	static void NewProp_bUseVibration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVibration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VibrationIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCrossPlatformInputConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_PCInputContext = { "PCInputContext", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, PCInputContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCInputContext_MetaData), NewProp_PCInputContext_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_ConsoleInputContext = { "ConsoleInputContext", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, ConsoleInputContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConsoleInputContext_MetaData), NewProp_ConsoleInputContext_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_MobileInputContext = { "MobileInputContext", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, MobileInputContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MobileInputContext_MetaData), NewProp_MobileInputContext_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_MouseSensitivity = { "MouseSensitivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, MouseSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MouseSensitivity_MetaData), NewProp_MouseSensitivity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_GamepadSensitivity = { "GamepadSensitivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, GamepadSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GamepadSensitivity_MetaData), NewProp_GamepadSensitivity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_TouchSensitivity = { "TouchSensitivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, TouchSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TouchSensitivity_MetaData), NewProp_TouchSensitivity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bInvertYAxis_SetBit(void* Obj)
{
	((FAuracronCrossPlatformInputConfiguration*)Obj)->bInvertYAxis = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bInvertYAxis = { "bInvertYAxis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCrossPlatformInputConfiguration), &Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bInvertYAxis_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertYAxis_MetaData), NewProp_bInvertYAxis_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseAutoAimOnMobile_SetBit(void* Obj)
{
	((FAuracronCrossPlatformInputConfiguration*)Obj)->bUseAutoAimOnMobile = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseAutoAimOnMobile = { "bUseAutoAimOnMobile", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCrossPlatformInputConfiguration), &Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseAutoAimOnMobile_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAutoAimOnMobile_MetaData), NewProp_bUseAutoAimOnMobile_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_AutoAimIntensity = { "AutoAimIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, AutoAimIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AutoAimIntensity_MetaData), NewProp_AutoAimIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseTouchGestures_SetBit(void* Obj)
{
	((FAuracronCrossPlatformInputConfiguration*)Obj)->bUseTouchGestures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseTouchGestures = { "bUseTouchGestures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCrossPlatformInputConfiguration), &Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseTouchGestures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseTouchGestures_MetaData), NewProp_bUseTouchGestures_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_TouchButtonSize = { "TouchButtonSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, TouchButtonSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TouchButtonSize_MetaData), NewProp_TouchButtonSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_TouchButtonOpacity = { "TouchButtonOpacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, TouchButtonOpacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TouchButtonOpacity_MetaData), NewProp_TouchButtonOpacity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseVibration_SetBit(void* Obj)
{
	((FAuracronCrossPlatformInputConfiguration*)Obj)->bUseVibration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseVibration = { "bUseVibration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCrossPlatformInputConfiguration), &Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseVibration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVibration_MetaData), NewProp_bUseVibration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_VibrationIntensity = { "VibrationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCrossPlatformInputConfiguration, VibrationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VibrationIntensity_MetaData), NewProp_VibrationIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_PCInputContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_ConsoleInputContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_MobileInputContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_MouseSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_GamepadSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_TouchSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bInvertYAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseAutoAimOnMobile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_AutoAimIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseTouchGestures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_TouchButtonSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_TouchButtonOpacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_bUseVibration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewProp_VibrationIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronUIBridge,
	nullptr,
	&NewStructOps,
	"AuracronCrossPlatformInputConfiguration",
	Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::PropPointers),
	sizeof(FAuracronCrossPlatformInputConfiguration),
	alignof(FAuracronCrossPlatformInputConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCrossPlatformInputConfiguration ****************************

// ********** Begin Delegate FOnUIShown ************************************************************
struct Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics
{
	struct AuracronUIBridge_eventOnUIShown_Parms
	{
		EAuracronUIType UIType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando UI \xc3\x83\xc2\xa9 mostrada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando UI \xc3\x83\xc2\xa9 mostrada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventOnUIShown_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::NewProp_UIType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "OnUIShown__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::AuracronUIBridge_eventOnUIShown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::AuracronUIBridge_eventOnUIShown_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronUIBridge::FOnUIShown_DelegateWrapper(const FMulticastScriptDelegate& OnUIShown, EAuracronUIType UIType)
{
	struct AuracronUIBridge_eventOnUIShown_Parms
	{
		EAuracronUIType UIType;
	};
	AuracronUIBridge_eventOnUIShown_Parms Parms;
	Parms.UIType=UIType;
	OnUIShown.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnUIShown **************************************************************

// ********** Begin Delegate FOnUIHidden ***********************************************************
struct Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics
{
	struct AuracronUIBridge_eventOnUIHidden_Parms
	{
		EAuracronUIType UIType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando UI \xc3\x83\xc2\xa9 escondida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando UI \xc3\x83\xc2\xa9 escondida" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventOnUIHidden_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::NewProp_UIType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "OnUIHidden__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::AuracronUIBridge_eventOnUIHidden_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::AuracronUIBridge_eventOnUIHidden_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronUIBridge::FOnUIHidden_DelegateWrapper(const FMulticastScriptDelegate& OnUIHidden, EAuracronUIType UIType)
{
	struct AuracronUIBridge_eventOnUIHidden_Parms
	{
		EAuracronUIType UIType;
	};
	AuracronUIBridge_eventOnUIHidden_Parms Parms;
	Parms.UIType=UIType;
	OnUIHidden.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnUIHidden *************************************************************

// ********** Begin Delegate FOnInputPlatformChanged ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics
{
	struct AuracronUIBridge_eventOnInputPlatformChanged_Parms
	{
		EAuracronInputPlatform OldPlatform;
		EAuracronInputPlatform NewPlatform;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando plataforma de input muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando plataforma de input muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldPlatform_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldPlatform;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPlatform_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPlatform;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_OldPlatform_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_OldPlatform = { "OldPlatform", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventOnInputPlatformChanged_Parms, OldPlatform), Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform, METADATA_PARAMS(0, nullptr) }; // 2897940699
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_NewPlatform_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_NewPlatform = { "NewPlatform", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventOnInputPlatformChanged_Parms, NewPlatform), Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform, METADATA_PARAMS(0, nullptr) }; // 2897940699
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_OldPlatform_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_OldPlatform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_NewPlatform_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::NewProp_NewPlatform,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "OnInputPlatformChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::AuracronUIBridge_eventOnInputPlatformChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::AuracronUIBridge_eventOnInputPlatformChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronUIBridge::FOnInputPlatformChanged_DelegateWrapper(const FMulticastScriptDelegate& OnInputPlatformChanged, EAuracronInputPlatform OldPlatform, EAuracronInputPlatform NewPlatform)
{
	struct AuracronUIBridge_eventOnInputPlatformChanged_Parms
	{
		EAuracronInputPlatform OldPlatform;
		EAuracronInputPlatform NewPlatform;
	};
	AuracronUIBridge_eventOnInputPlatformChanged_Parms Parms;
	Parms.OldPlatform=OldPlatform;
	Parms.NewPlatform=NewPlatform;
	OnInputPlatformChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnInputPlatformChanged *************************************************

// ********** Begin Delegate FOnUIConfigurationUpdated *********************************************
struct Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics
{
	struct AuracronUIBridge_eventOnUIConfigurationUpdated_Parms
	{
		FAuracronUIConfiguration NewConfiguration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de UI \xc3\x83\xc2\xa9 atualizada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de UI \xc3\x83\xc2\xa9 atualizada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfiguration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::NewProp_NewConfiguration = { "NewConfiguration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventOnUIConfigurationUpdated_Parms, NewConfiguration), Z_Construct_UScriptStruct_FAuracronUIConfiguration, METADATA_PARAMS(0, nullptr) }; // 552496867
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::NewProp_NewConfiguration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "OnUIConfigurationUpdated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::AuracronUIBridge_eventOnUIConfigurationUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::AuracronUIBridge_eventOnUIConfigurationUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronUIBridge::FOnUIConfigurationUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnUIConfigurationUpdated, FAuracronUIConfiguration NewConfiguration)
{
	struct AuracronUIBridge_eventOnUIConfigurationUpdated_Parms
	{
		FAuracronUIConfiguration NewConfiguration;
	};
	AuracronUIBridge_eventOnUIConfigurationUpdated_Parms Parms;
	Parms.NewConfiguration=NewConfiguration;
	OnUIConfigurationUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnUIConfigurationUpdated ***********************************************

// ********** Begin Delegate FOnMinimapUpdated *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando minimapa \xc3\x83\xc2\xa9 atualizado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando minimapa \xc3\x83\xc2\xa9 atualizado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "OnMinimapUpdated__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronUIBridge::FOnMinimapUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnMinimapUpdated)
{
	OnMinimapUpdated.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnMinimapUpdated *******************************************************

// ********** Begin Class UAuracronUIBridge Function AddMinimapMarker ******************************
struct Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics
{
	struct AuracronUIBridge_eventAddMinimapMarker_Parms
	{
		FVector WorldLocation;
		FString MarkerType;
		FLinearColor MarkerColor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Minimap" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar marcador no minimapa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar marcador no minimapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MarkerType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MarkerColor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MarkerType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MarkerColor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventAddMinimapMarker_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_MarkerType = { "MarkerType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventAddMinimapMarker_Parms, MarkerType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MarkerType_MetaData), NewProp_MarkerType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_MarkerColor = { "MarkerColor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventAddMinimapMarker_Parms, MarkerColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MarkerColor_MetaData), NewProp_MarkerColor_MetaData) };
void Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventAddMinimapMarker_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventAddMinimapMarker_Parms), &Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_MarkerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_MarkerColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "AddMinimapMarker", Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::AuracronUIBridge_eventAddMinimapMarker_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::AuracronUIBridge_eventAddMinimapMarker_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execAddMinimapMarker)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_GET_PROPERTY(FStrProperty,Z_Param_MarkerType);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_MarkerColor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddMinimapMarker(Z_Param_Out_WorldLocation,Z_Param_MarkerType,Z_Param_Out_MarkerColor);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function AddMinimapMarker ********************************

// ********** Begin Class UAuracronUIBridge Function ApplyUIConfiguration **************************
struct Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics
{
	struct AuracronUIBridge_eventApplyUIConfiguration_Parms
	{
		FAuracronUIConfiguration Configuration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de UI\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventApplyUIConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronUIConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 552496867
void Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventApplyUIConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventApplyUIConfiguration_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ApplyUIConfiguration", Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::AuracronUIBridge_eventApplyUIConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::AuracronUIBridge_eventApplyUIConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execApplyUIConfiguration)
{
	P_GET_STRUCT_REF(FAuracronUIConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyUIConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ApplyUIConfiguration ****************************

// ********** Begin Class UAuracronUIBridge Function DetectInputPlatform ***************************
struct Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics
{
	struct AuracronUIBridge_eventDetectInputPlatform_Parms
	{
		EAuracronInputPlatform ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Detectar plataforma automaticamente\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detectar plataforma automaticamente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventDetectInputPlatform_Parms, ReturnValue), Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform, METADATA_PARAMS(0, nullptr) }; // 2897940699
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "DetectInputPlatform", Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::AuracronUIBridge_eventDetectInputPlatform_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::AuracronUIBridge_eventDetectInputPlatform_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execDetectInputPlatform)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronInputPlatform*)Z_Param__Result=P_THIS->DetectInputPlatform();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function DetectInputPlatform *****************************

// ********** Begin Class UAuracronUIBridge Function GetUIState ************************************
struct Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics
{
	struct AuracronUIBridge_eventGetUIState_Parms
	{
		EAuracronUIType UIType;
		EAuracronUIState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estado de UI\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estado de UI" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventGetUIState_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventGetUIState_Parms, ReturnValue), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIState, METADATA_PARAMS(0, nullptr) }; // 182551264
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "GetUIState", Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::AuracronUIBridge_eventGetUIState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::AuracronUIBridge_eventGetUIState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_GetUIState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_GetUIState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execGetUIState)
{
	P_GET_ENUM(EAuracronUIType,Z_Param_UIType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronUIState*)Z_Param__Result=P_THIS->GetUIState(EAuracronUIType(Z_Param_UIType));
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function GetUIState **************************************

// ********** Begin Class UAuracronUIBridge Function GetUIWidget ***********************************
struct Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics
{
	struct AuracronUIBridge_eventGetUIWidget_Parms
	{
		EAuracronUIType UIType;
		UCommonUserWidget* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter widget de UI\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter widget de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventGetUIWidget_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventGetUIWidget_Parms, ReturnValue), Z_Construct_UClass_UCommonUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "GetUIWidget", Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::AuracronUIBridge_eventGetUIWidget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::AuracronUIBridge_eventGetUIWidget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execGetUIWidget)
{
	P_GET_ENUM(EAuracronUIType,Z_Param_UIType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UCommonUserWidget**)Z_Param__Result=P_THIS->GetUIWidget(EAuracronUIType(Z_Param_UIType));
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function GetUIWidget *************************************

// ********** Begin Class UAuracronUIBridge Function HideUI ****************************************
struct Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics
{
	struct AuracronUIBridge_eventHideUI_Parms
	{
		EAuracronUIType UIType;
		bool bAnimate;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Esconder UI espec\xc3\x83\xc2\xad""fica\n     */" },
#endif
		{ "CPP_Default_bAnimate", "true" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Esconder UI espec\xc3\x83\xc2\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static void NewProp_bAnimate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAnimate;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventHideUI_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
void Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_bAnimate_SetBit(void* Obj)
{
	((AuracronUIBridge_eventHideUI_Parms*)Obj)->bAnimate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_bAnimate = { "bAnimate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventHideUI_Parms), &Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_bAnimate_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventHideUI_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventHideUI_Parms), &Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_bAnimate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "HideUI", Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::AuracronUIBridge_eventHideUI_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::AuracronUIBridge_eventHideUI_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_HideUI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_HideUI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execHideUI)
{
	P_GET_ENUM(EAuracronUIType,Z_Param_UIType);
	P_GET_UBOOL(Z_Param_bAnimate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HideUI(EAuracronUIType(Z_Param_UIType),Z_Param_bAnimate);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function HideUI ******************************************

// ********** Begin Class UAuracronUIBridge Function IsUIVisible ***********************************
struct Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics
{
	struct AuracronUIBridge_eventIsUIVisible_Parms
	{
		EAuracronUIType UIType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se UI est\xc3\x83\xc2\xa1 vis\xc3\x83\xc2\xadvel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se UI est\xc3\x83\xc2\xa1 vis\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventIsUIVisible_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
void Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventIsUIVisible_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventIsUIVisible_Parms), &Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "IsUIVisible", Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::AuracronUIBridge_eventIsUIVisible_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::AuracronUIBridge_eventIsUIVisible_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execIsUIVisible)
{
	P_GET_ENUM(EAuracronUIType,Z_Param_UIType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUIVisible(EAuracronUIType(Z_Param_UIType));
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function IsUIVisible *************************************

// ********** Begin Class UAuracronUIBridge Function LoadUISettings ********************************
struct Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics
{
	struct AuracronUIBridge_eventLoadUISettings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de UI\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de UI" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventLoadUISettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventLoadUISettings_Parms), &Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "LoadUISettings", Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::AuracronUIBridge_eventLoadUISettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::AuracronUIBridge_eventLoadUISettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execLoadUISettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadUISettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function LoadUISettings **********************************

// ********** Begin Class UAuracronUIBridge Function OnRep_InputPlatform ***************************
struct Z_Construct_UFunction_UAuracronUIBridge_OnRep_InputPlatform_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_OnRep_InputPlatform_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "OnRep_InputPlatform", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_OnRep_InputPlatform_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_OnRep_InputPlatform_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronUIBridge_OnRep_InputPlatform()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_OnRep_InputPlatform_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execOnRep_InputPlatform)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_InputPlatform();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function OnRep_InputPlatform *****************************

// ********** Begin Class UAuracronUIBridge Function OnRep_UIStates ********************************
struct Z_Construct_UFunction_UAuracronUIBridge_OnRep_UIStates_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_OnRep_UIStates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "OnRep_UIStates", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_OnRep_UIStates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_OnRep_UIStates_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronUIBridge_OnRep_UIStates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_OnRep_UIStates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execOnRep_UIStates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_UIStates();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function OnRep_UIStates **********************************

// ********** Begin Class UAuracronUIBridge Function ProcessUIInput ********************************
struct Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics
{
	struct AuracronUIBridge_eventProcessUIInput_Parms
	{
		FString InputAction;
		FVector2D InputValue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Processar input de UI\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processar input de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputAction_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InputAction;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InputValue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_InputAction = { "InputAction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventProcessUIInput_Parms, InputAction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputAction_MetaData), NewProp_InputAction_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_InputValue = { "InputValue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventProcessUIInput_Parms, InputValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputValue_MetaData), NewProp_InputValue_MetaData) };
void Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventProcessUIInput_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventProcessUIInput_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_InputAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_InputValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ProcessUIInput", Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::AuracronUIBridge_eventProcessUIInput_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::AuracronUIBridge_eventProcessUIInput_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execProcessUIInput)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InputAction);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_InputValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ProcessUIInput(Z_Param_InputAction,Z_Param_Out_InputValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ProcessUIInput **********************************

// ********** Begin Class UAuracronUIBridge Function RemoveMinimapMarker ***************************
struct Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics
{
	struct AuracronUIBridge_eventRemoveMinimapMarker_Parms
	{
		FString MarkerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Minimap" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover marcador do minimapa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover marcador do minimapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MarkerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MarkerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::NewProp_MarkerID = { "MarkerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventRemoveMinimapMarker_Parms, MarkerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MarkerID_MetaData), NewProp_MarkerID_MetaData) };
void Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventRemoveMinimapMarker_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventRemoveMinimapMarker_Parms), &Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::NewProp_MarkerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "RemoveMinimapMarker", Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::AuracronUIBridge_eventRemoveMinimapMarker_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::AuracronUIBridge_eventRemoveMinimapMarker_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execRemoveMinimapMarker)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MarkerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveMinimapMarker(Z_Param_MarkerID);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function RemoveMinimapMarker *****************************

// ********** Begin Class UAuracronUIBridge Function ResetToDefaultSettings ************************
struct Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics
{
	struct AuracronUIBridge_eventResetToDefaultSettings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Resetar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es para padr\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resetar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es para padr\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventResetToDefaultSettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventResetToDefaultSettings_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ResetToDefaultSettings", Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::AuracronUIBridge_eventResetToDefaultSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::AuracronUIBridge_eventResetToDefaultSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execResetToDefaultSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ResetToDefaultSettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ResetToDefaultSettings **************************

// ********** Begin Class UAuracronUIBridge Function SaveUISettings ********************************
struct Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics
{
	struct AuracronUIBridge_eventSaveUISettings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Salvar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de UI\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Salvar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de UI" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventSaveUISettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventSaveUISettings_Parms), &Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "SaveUISettings", Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::AuracronUIBridge_eventSaveUISettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::AuracronUIBridge_eventSaveUISettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execSaveUISettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveUISettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function SaveUISettings **********************************

// ********** Begin Class UAuracronUIBridge Function SetMinimapZoom ********************************
struct Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics
{
	struct AuracronUIBridge_eventSetMinimapZoom_Parms
	{
		float ZoomLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Minimap" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir zoom do minimapa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir zoom do minimapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ZoomLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::NewProp_ZoomLevel = { "ZoomLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventSetMinimapZoom_Parms, ZoomLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventSetMinimapZoom_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventSetMinimapZoom_Parms), &Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::NewProp_ZoomLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "SetMinimapZoom", Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::AuracronUIBridge_eventSetMinimapZoom_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::AuracronUIBridge_eventSetMinimapZoom_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execSetMinimapZoom)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ZoomLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetMinimapZoom(Z_Param_ZoomLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function SetMinimapZoom **********************************

// ********** Begin Class UAuracronUIBridge Function SetUIWidget ***********************************
struct Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics
{
	struct AuracronUIBridge_eventSetUIWidget_Parms
	{
		EAuracronUIType UIType;
		TSubclassOf<UCommonUserWidget> WidgetClass;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir widget para tipo de UI\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir widget para tipo de UI" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static const UECodeGen_Private::FClassPropertyParams NewProp_WidgetClass;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventSetUIWidget_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_WidgetClass = { "WidgetClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventSetUIWidget_Parms, WidgetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UCommonUserWidget_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventSetUIWidget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventSetUIWidget_Parms), &Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_WidgetClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "SetUIWidget", Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::AuracronUIBridge_eventSetUIWidget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::AuracronUIBridge_eventSetUIWidget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execSetUIWidget)
{
	P_GET_ENUM(EAuracronUIType,Z_Param_UIType);
	P_GET_OBJECT(UClass,Z_Param_WidgetClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetUIWidget(EAuracronUIType(Z_Param_UIType),Z_Param_WidgetClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function SetUIWidget *************************************

// ********** Begin Class UAuracronUIBridge Function SetupPlatformInput ****************************
struct Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics
{
	struct AuracronUIBridge_eventSetupPlatformInput_Parms
	{
		EAuracronInputPlatform Platform;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar input para plataforma\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar input para plataforma" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Platform_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Platform;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_Platform_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_Platform = { "Platform", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventSetupPlatformInput_Parms, Platform), Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform, METADATA_PARAMS(0, nullptr) }; // 2897940699
void Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventSetupPlatformInput_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventSetupPlatformInput_Parms), &Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_Platform_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_Platform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "SetupPlatformInput", Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::AuracronUIBridge_eventSetupPlatformInput_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::AuracronUIBridge_eventSetupPlatformInput_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execSetupPlatformInput)
{
	P_GET_ENUM(EAuracronInputPlatform,Z_Param_Platform);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupPlatformInput(EAuracronInputPlatform(Z_Param_Platform));
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function SetupPlatformInput ******************************

// ********** Begin Class UAuracronUIBridge Function SetupTouchControls ****************************
struct Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics
{
	struct AuracronUIBridge_eventSetupTouchControls_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar controles touch\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar controles touch" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventSetupTouchControls_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventSetupTouchControls_Parms), &Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "SetupTouchControls", Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::AuracronUIBridge_eventSetupTouchControls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::AuracronUIBridge_eventSetupTouchControls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execSetupTouchControls)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupTouchControls();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function SetupTouchControls ******************************

// ********** Begin Class UAuracronUIBridge Function ShowDamageIndicator ***************************
struct Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics
{
	struct AuracronUIBridge_eventShowDamageIndicator_Parms
	{
		FVector WorldLocation;
		float DamageAmount;
		bool bIsCritical;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mostrar indicador de dano\n     */" },
#endif
		{ "CPP_Default_bIsCritical", "false" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar indicador de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static void NewProp_bIsCritical_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCritical;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventShowDamageIndicator_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventShowDamageIndicator_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_bIsCritical_SetBit(void* Obj)
{
	((AuracronUIBridge_eventShowDamageIndicator_Parms*)Obj)->bIsCritical = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_bIsCritical = { "bIsCritical", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventShowDamageIndicator_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_bIsCritical_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventShowDamageIndicator_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventShowDamageIndicator_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_bIsCritical,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ShowDamageIndicator", Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::AuracronUIBridge_eventShowDamageIndicator_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::AuracronUIBridge_eventShowDamageIndicator_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execShowDamageIndicator)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_UBOOL(Z_Param_bIsCritical);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShowDamageIndicator(Z_Param_Out_WorldLocation,Z_Param_DamageAmount,Z_Param_bIsCritical);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ShowDamageIndicator *****************************

// ********** Begin Class UAuracronUIBridge Function ShowUI ****************************************
struct Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics
{
	struct AuracronUIBridge_eventShowUI_Parms
	{
		EAuracronUIType UIType;
		bool bAnimate;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mostrar UI espec\xc3\x83\xc2\xad""fica\n     */" },
#endif
		{ "CPP_Default_bAnimate", "true" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar UI espec\xc3\x83\xc2\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static void NewProp_bAnimate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAnimate;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventShowUI_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
void Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_bAnimate_SetBit(void* Obj)
{
	((AuracronUIBridge_eventShowUI_Parms*)Obj)->bAnimate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_bAnimate = { "bAnimate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventShowUI_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_bAnimate_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventShowUI_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventShowUI_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_bAnimate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ShowUI", Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::AuracronUIBridge_eventShowUI_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::AuracronUIBridge_eventShowUI_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ShowUI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ShowUI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execShowUI)
{
	P_GET_ENUM(EAuracronUIType,Z_Param_UIType);
	P_GET_UBOOL(Z_Param_bAnimate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShowUI(EAuracronUIType(Z_Param_UIType),Z_Param_bAnimate);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ShowUI ******************************************

// ********** Begin Class UAuracronUIBridge Function ToggleMinimapRealm ****************************
struct Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics
{
	struct AuracronUIBridge_eventToggleMinimapRealm_Parms
	{
		int32 RealmIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Minimap" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Alternar realm no minimapa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alternar realm no minimapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventToggleMinimapRealm_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventToggleMinimapRealm_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventToggleMinimapRealm_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ToggleMinimapRealm", Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::AuracronUIBridge_eventToggleMinimapRealm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::AuracronUIBridge_eventToggleMinimapRealm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execToggleMinimapRealm)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ToggleMinimapRealm(Z_Param_RealmIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ToggleMinimapRealm ******************************

// ********** Begin Class UAuracronUIBridge Function ToggleTouchControls ***************************
struct Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics
{
	struct AuracronUIBridge_eventToggleTouchControls_Parms
	{
		bool bShow;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mostrar/esconder controles touch\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar/esconder controles touch" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bShow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShow;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_bShow_SetBit(void* Obj)
{
	((AuracronUIBridge_eventToggleTouchControls_Parms*)Obj)->bShow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_bShow = { "bShow", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventToggleTouchControls_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_bShow_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventToggleTouchControls_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventToggleTouchControls_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_bShow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ToggleTouchControls", Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::AuracronUIBridge_eventToggleTouchControls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::AuracronUIBridge_eventToggleTouchControls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execToggleTouchControls)
{
	P_GET_UBOOL(Z_Param_bShow);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ToggleTouchControls(Z_Param_bShow);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ToggleTouchControls *****************************

// ********** Begin Class UAuracronUIBridge Function ToggleUI **************************************
struct Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics
{
	struct AuracronUIBridge_eventToggleUI_Parms
	{
		EAuracronUIType UIType;
		bool bAnimate;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Alternar visibilidade de UI\n     */" },
#endif
		{ "CPP_Default_bAnimate", "true" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alternar visibilidade de UI" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UIType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UIType;
	static void NewProp_bAnimate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAnimate;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_UIType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_UIType = { "UIType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventToggleUI_Parms, UIType), Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
void Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_bAnimate_SetBit(void* Obj)
{
	((AuracronUIBridge_eventToggleUI_Parms*)Obj)->bAnimate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_bAnimate = { "bAnimate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventToggleUI_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_bAnimate_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventToggleUI_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventToggleUI_Parms), &Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_UIType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_UIType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_bAnimate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "ToggleUI", Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::AuracronUIBridge_eventToggleUI_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::AuracronUIBridge_eventToggleUI_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_ToggleUI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_ToggleUI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execToggleUI)
{
	P_GET_ENUM(EAuracronUIType,Z_Param_UIType);
	P_GET_UBOOL(Z_Param_bAnimate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ToggleUI(EAuracronUIType(Z_Param_UIType),Z_Param_bAnimate);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function ToggleUI ****************************************

// ********** Begin Class UAuracronUIBridge Function UpdateAbilityCooldown *************************
struct Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics
{
	struct AuracronUIBridge_eventUpdateAbilityCooldown_Parms
	{
		FString AbilitySlot;
		float CooldownRemaining;
		float MaxCooldown;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar cooldown de habilidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar cooldown de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownRemaining;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxCooldown;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateAbilityCooldown_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_CooldownRemaining = { "CooldownRemaining", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateAbilityCooldown_Parms, CooldownRemaining), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_MaxCooldown = { "MaxCooldown", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateAbilityCooldown_Parms, MaxCooldown), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventUpdateAbilityCooldown_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventUpdateAbilityCooldown_Parms), &Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_CooldownRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_MaxCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "UpdateAbilityCooldown", Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::AuracronUIBridge_eventUpdateAbilityCooldown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::AuracronUIBridge_eventUpdateAbilityCooldown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execUpdateAbilityCooldown)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CooldownRemaining);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxCooldown);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateAbilityCooldown(Z_Param_AbilitySlot,Z_Param_CooldownRemaining,Z_Param_MaxCooldown);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function UpdateAbilityCooldown ***************************

// ********** Begin Class UAuracronUIBridge Function UpdateExperienceBar ***************************
struct Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics
{
	struct AuracronUIBridge_eventUpdateExperienceBar_Parms
	{
		int32 CurrentXP;
		int32 XPToNextLevel;
		int32 CurrentLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar barra de experi\xc3\x83\xc2\xaancia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar barra de experi\xc3\x83\xc2\xaancia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentXP;
	static const UECodeGen_Private::FIntPropertyParams NewProp_XPToNextLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_CurrentXP = { "CurrentXP", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateExperienceBar_Parms, CurrentXP), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_XPToNextLevel = { "XPToNextLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateExperienceBar_Parms, XPToNextLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateExperienceBar_Parms, CurrentLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventUpdateExperienceBar_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventUpdateExperienceBar_Parms), &Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_CurrentXP,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_XPToNextLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "UpdateExperienceBar", Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::AuracronUIBridge_eventUpdateExperienceBar_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::AuracronUIBridge_eventUpdateExperienceBar_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execUpdateExperienceBar)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CurrentXP);
	P_GET_PROPERTY(FIntProperty,Z_Param_XPToNextLevel);
	P_GET_PROPERTY(FIntProperty,Z_Param_CurrentLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateExperienceBar(Z_Param_CurrentXP,Z_Param_XPToNextLevel,Z_Param_CurrentLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function UpdateExperienceBar *****************************

// ********** Begin Class UAuracronUIBridge Function UpdateGold ************************************
struct Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics
{
	struct AuracronUIBridge_eventUpdateGold_Parms
	{
		int32 CurrentGold;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar gold\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar gold" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentGold;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::NewProp_CurrentGold = { "CurrentGold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateGold_Parms, CurrentGold), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventUpdateGold_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventUpdateGold_Parms), &Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::NewProp_CurrentGold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "UpdateGold", Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::AuracronUIBridge_eventUpdateGold_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::AuracronUIBridge_eventUpdateGold_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_UpdateGold()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_UpdateGold_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execUpdateGold)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CurrentGold);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateGold(Z_Param_CurrentGold);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function UpdateGold **************************************

// ********** Begin Class UAuracronUIBridge Function UpdateHealthBar *******************************
struct Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics
{
	struct AuracronUIBridge_eventUpdateHealthBar_Parms
	{
		float CurrentHealth;
		float MaxHealth;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar barra de HP\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar barra de HP" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateHealthBar_Parms, CurrentHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateHealthBar_Parms, MaxHealth), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventUpdateHealthBar_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventUpdateHealthBar_Parms), &Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "UpdateHealthBar", Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::AuracronUIBridge_eventUpdateHealthBar_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::AuracronUIBridge_eventUpdateHealthBar_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execUpdateHealthBar)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurrentHealth);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxHealth);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateHealthBar(Z_Param_CurrentHealth,Z_Param_MaxHealth);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function UpdateHealthBar *********************************

// ********** Begin Class UAuracronUIBridge Function UpdateKDA *************************************
struct Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics
{
	struct AuracronUIBridge_eventUpdateKDA_Parms
	{
		int32 Kills;
		int32 Deaths;
		int32 Assists;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar KDA\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar KDA" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Kills;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Deaths;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Assists;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_Kills = { "Kills", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateKDA_Parms, Kills), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_Deaths = { "Deaths", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateKDA_Parms, Deaths), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_Assists = { "Assists", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateKDA_Parms, Assists), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventUpdateKDA_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventUpdateKDA_Parms), &Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_Kills,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_Deaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_Assists,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "UpdateKDA", Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::AuracronUIBridge_eventUpdateKDA_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::AuracronUIBridge_eventUpdateKDA_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execUpdateKDA)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Kills);
	P_GET_PROPERTY(FIntProperty,Z_Param_Deaths);
	P_GET_PROPERTY(FIntProperty,Z_Param_Assists);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateKDA(Z_Param_Kills,Z_Param_Deaths,Z_Param_Assists);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function UpdateKDA ***************************************

// ********** Begin Class UAuracronUIBridge Function UpdateManaBar *********************************
struct Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics
{
	struct AuracronUIBridge_eventUpdateManaBar_Parms
	{
		float CurrentMana;
		float MaxMana;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar barra de mana\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar barra de mana" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentMana;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMana;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_CurrentMana = { "CurrentMana", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateManaBar_Parms, CurrentMana), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_MaxMana = { "MaxMana", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronUIBridge_eventUpdateManaBar_Parms, MaxMana), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventUpdateManaBar_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventUpdateManaBar_Parms), &Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_CurrentMana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_MaxMana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "UpdateManaBar", Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::AuracronUIBridge_eventUpdateManaBar_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::AuracronUIBridge_eventUpdateManaBar_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execUpdateManaBar)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurrentMana);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxMana);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateManaBar(Z_Param_CurrentMana,Z_Param_MaxMana);
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function UpdateManaBar ***********************************

// ********** Begin Class UAuracronUIBridge Function UpdateMinimap3D *******************************
struct Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics
{
	struct AuracronUIBridge_eventUpdateMinimap3D_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON UI|Minimap" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar minimapa 3D\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar minimapa 3D" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronUIBridge_eventUpdateMinimap3D_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronUIBridge_eventUpdateMinimap3D_Parms), &Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronUIBridge, nullptr, "UpdateMinimap3D", Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::AuracronUIBridge_eventUpdateMinimap3D_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::AuracronUIBridge_eventUpdateMinimap3D_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronUIBridge::execUpdateMinimap3D)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateMinimap3D();
	P_NATIVE_END;
}
// ********** End Class UAuracronUIBridge Function UpdateMinimap3D *********************************

// ********** Begin Class UAuracronUIBridge ********************************************************
void UAuracronUIBridge::StaticRegisterNativesUAuracronUIBridge()
{
	UClass* Class = UAuracronUIBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddMinimapMarker", &UAuracronUIBridge::execAddMinimapMarker },
		{ "ApplyUIConfiguration", &UAuracronUIBridge::execApplyUIConfiguration },
		{ "DetectInputPlatform", &UAuracronUIBridge::execDetectInputPlatform },
		{ "GetUIState", &UAuracronUIBridge::execGetUIState },
		{ "GetUIWidget", &UAuracronUIBridge::execGetUIWidget },
		{ "HideUI", &UAuracronUIBridge::execHideUI },
		{ "IsUIVisible", &UAuracronUIBridge::execIsUIVisible },
		{ "LoadUISettings", &UAuracronUIBridge::execLoadUISettings },
		{ "OnRep_InputPlatform", &UAuracronUIBridge::execOnRep_InputPlatform },
		{ "OnRep_UIStates", &UAuracronUIBridge::execOnRep_UIStates },
		{ "ProcessUIInput", &UAuracronUIBridge::execProcessUIInput },
		{ "RemoveMinimapMarker", &UAuracronUIBridge::execRemoveMinimapMarker },
		{ "ResetToDefaultSettings", &UAuracronUIBridge::execResetToDefaultSettings },
		{ "SaveUISettings", &UAuracronUIBridge::execSaveUISettings },
		{ "SetMinimapZoom", &UAuracronUIBridge::execSetMinimapZoom },
		{ "SetUIWidget", &UAuracronUIBridge::execSetUIWidget },
		{ "SetupPlatformInput", &UAuracronUIBridge::execSetupPlatformInput },
		{ "SetupTouchControls", &UAuracronUIBridge::execSetupTouchControls },
		{ "ShowDamageIndicator", &UAuracronUIBridge::execShowDamageIndicator },
		{ "ShowUI", &UAuracronUIBridge::execShowUI },
		{ "ToggleMinimapRealm", &UAuracronUIBridge::execToggleMinimapRealm },
		{ "ToggleTouchControls", &UAuracronUIBridge::execToggleTouchControls },
		{ "ToggleUI", &UAuracronUIBridge::execToggleUI },
		{ "UpdateAbilityCooldown", &UAuracronUIBridge::execUpdateAbilityCooldown },
		{ "UpdateExperienceBar", &UAuracronUIBridge::execUpdateExperienceBar },
		{ "UpdateGold", &UAuracronUIBridge::execUpdateGold },
		{ "UpdateHealthBar", &UAuracronUIBridge::execUpdateHealthBar },
		{ "UpdateKDA", &UAuracronUIBridge::execUpdateKDA },
		{ "UpdateManaBar", &UAuracronUIBridge::execUpdateManaBar },
		{ "UpdateMinimap3D", &UAuracronUIBridge::execUpdateMinimap3D },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronUIBridge;
UClass* UAuracronUIBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronUIBridge;
	if (!Z_Registration_Info_UClass_UAuracronUIBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronUIBridge"),
			Z_Registration_Info_UClass_UAuracronUIBridge.InnerSingleton,
			StaticRegisterNativesUAuracronUIBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronUIBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronUIBridge_NoRegister()
{
	return UAuracronUIBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronUIBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|UI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Interface do Usu\xc3\x83\xc2\xa1rio\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de UI/UX cross-platform\n */" },
#endif
		{ "DisplayName", "AURACRON UI Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronUIBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Interface do Usu\xc3\x83\xc2\xa1rio\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de UI/UX cross-platform" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o geral de UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o geral de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimapConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do minimapa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do minimapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatHUDConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do HUD de combate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do HUD de combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrossPlatformInputConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input cross-platform */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input cross-platform" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveUIWidgets_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Widgets de UI ativos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widgets de UI ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIStates_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estados de UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentInputPlatform_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Plataforma de input atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Plataforma de input atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommonInputSubsystem_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao Common Input Subsystem */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao Common Input Subsystem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnhancedInputComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao Enhanced Input Component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao Enhanced Input Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnUIShown_MetaData[] = {
		{ "Category", "AURACRON UI|Events" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnUIHidden_MetaData[] = {
		{ "Category", "AURACRON UI|Events" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInputPlatformChanged_MetaData[] = {
		{ "Category", "AURACRON UI|Events" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnUIConfigurationUpdated_MetaData[] = {
		{ "Category", "AURACRON UI|Events" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMinimapUpdated_MetaData[] = {
		{ "Category", "AURACRON UI|Events" },
		{ "ModuleRelativePath", "Public/AuracronUIBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_UIConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MinimapConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CombatHUDConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CrossPlatformInputConfiguration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveUIWidgets_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActiveUIWidgets_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActiveUIWidgets_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveUIWidgets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UIStates_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UIStates;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentInputPlatform_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentInputPlatform;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CommonInputSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnhancedInputComponent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnUIShown;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnUIHidden;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInputPlatformChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnUIConfigurationUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMinimapUpdated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronUIBridge_AddMinimapMarker, "AddMinimapMarker" }, // 2345481193
		{ &Z_Construct_UFunction_UAuracronUIBridge_ApplyUIConfiguration, "ApplyUIConfiguration" }, // 3249262304
		{ &Z_Construct_UFunction_UAuracronUIBridge_DetectInputPlatform, "DetectInputPlatform" }, // 3981406723
		{ &Z_Construct_UFunction_UAuracronUIBridge_GetUIState, "GetUIState" }, // 2512146233
		{ &Z_Construct_UFunction_UAuracronUIBridge_GetUIWidget, "GetUIWidget" }, // 2810449377
		{ &Z_Construct_UFunction_UAuracronUIBridge_HideUI, "HideUI" }, // 587797511
		{ &Z_Construct_UFunction_UAuracronUIBridge_IsUIVisible, "IsUIVisible" }, // 4168385604
		{ &Z_Construct_UFunction_UAuracronUIBridge_LoadUISettings, "LoadUISettings" }, // 2585667633
		{ &Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature, "OnInputPlatformChanged__DelegateSignature" }, // 4737880
		{ &Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature, "OnMinimapUpdated__DelegateSignature" }, // 2186299058
		{ &Z_Construct_UFunction_UAuracronUIBridge_OnRep_InputPlatform, "OnRep_InputPlatform" }, // 2857962936
		{ &Z_Construct_UFunction_UAuracronUIBridge_OnRep_UIStates, "OnRep_UIStates" }, // 3274088307
		{ &Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature, "OnUIConfigurationUpdated__DelegateSignature" }, // 37762139
		{ &Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature, "OnUIHidden__DelegateSignature" }, // 910787344
		{ &Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature, "OnUIShown__DelegateSignature" }, // 1747643786
		{ &Z_Construct_UFunction_UAuracronUIBridge_ProcessUIInput, "ProcessUIInput" }, // 1527468997
		{ &Z_Construct_UFunction_UAuracronUIBridge_RemoveMinimapMarker, "RemoveMinimapMarker" }, // 1001798223
		{ &Z_Construct_UFunction_UAuracronUIBridge_ResetToDefaultSettings, "ResetToDefaultSettings" }, // 2026608046
		{ &Z_Construct_UFunction_UAuracronUIBridge_SaveUISettings, "SaveUISettings" }, // 1355728929
		{ &Z_Construct_UFunction_UAuracronUIBridge_SetMinimapZoom, "SetMinimapZoom" }, // 3748912643
		{ &Z_Construct_UFunction_UAuracronUIBridge_SetUIWidget, "SetUIWidget" }, // 614441353
		{ &Z_Construct_UFunction_UAuracronUIBridge_SetupPlatformInput, "SetupPlatformInput" }, // 649018179
		{ &Z_Construct_UFunction_UAuracronUIBridge_SetupTouchControls, "SetupTouchControls" }, // 3750201547
		{ &Z_Construct_UFunction_UAuracronUIBridge_ShowDamageIndicator, "ShowDamageIndicator" }, // 518136826
		{ &Z_Construct_UFunction_UAuracronUIBridge_ShowUI, "ShowUI" }, // 3377298644
		{ &Z_Construct_UFunction_UAuracronUIBridge_ToggleMinimapRealm, "ToggleMinimapRealm" }, // 1456729389
		{ &Z_Construct_UFunction_UAuracronUIBridge_ToggleTouchControls, "ToggleTouchControls" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_ToggleUI, "ToggleUI" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_UpdateAbilityCooldown, "UpdateAbilityCooldown" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_UpdateExperienceBar, "UpdateExperienceBar" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_UpdateGold, "UpdateGold" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_UpdateHealthBar, "UpdateHealthBar" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_UpdateKDA, "UpdateKDA" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_UpdateManaBar, "UpdateManaBar" }, // **********
		{ &Z_Construct_UFunction_UAuracronUIBridge_UpdateMinimap3D, "UpdateMinimap3D" }, // 210949721
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronUIBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_UIConfiguration = { "UIConfiguration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, UIConfiguration), Z_Construct_UScriptStruct_FAuracronUIConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIConfiguration_MetaData), NewProp_UIConfiguration_MetaData) }; // 552496867
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_MinimapConfiguration = { "MinimapConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, MinimapConfiguration), Z_Construct_UScriptStruct_FAuracronMinimapConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimapConfiguration_MetaData), NewProp_MinimapConfiguration_MetaData) }; // 821615854
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CombatHUDConfiguration = { "CombatHUDConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, CombatHUDConfiguration), Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatHUDConfiguration_MetaData), NewProp_CombatHUDConfiguration_MetaData) }; // 3921064051
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CrossPlatformInputConfiguration = { "CrossPlatformInputConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, CrossPlatformInputConfiguration), Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrossPlatformInputConfiguration_MetaData), NewProp_CrossPlatformInputConfiguration_MetaData) }; // 1095680642
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets_ValueProp = { "ActiveUIWidgets", nullptr, (EPropertyFlags)0x01040000000a0009, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UCommonUserWidget_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets_Key_KeyProp = { "ActiveUIWidgets_Key", nullptr, (EPropertyFlags)0x01000000000a0009, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronUIBridge_EAuracronUIType, METADATA_PARAMS(0, nullptr) }; // 1177635711
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets = { "ActiveUIWidgets", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, ActiveUIWidgets), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveUIWidgets_MetaData), NewProp_ActiveUIWidgets_MetaData) }; // 1177635711
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_UIStates_Inner = { "UIStates", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronUIStateEntry, METADATA_PARAMS(0, nullptr) }; // 945707003
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_UIStates = { "UIStates", "OnRep_UIStates", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, UIStates), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIStates_MetaData), NewProp_UIStates_MetaData) }; // 945707003
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CurrentInputPlatform_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CurrentInputPlatform = { "CurrentInputPlatform", "OnRep_InputPlatform", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, CurrentInputPlatform), Z_Construct_UEnum_AuracronUIBridge_EAuracronInputPlatform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentInputPlatform_MetaData), NewProp_CurrentInputPlatform_MetaData) }; // 2897940699
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CommonInputSubsystem = { "CommonInputSubsystem", nullptr, (EPropertyFlags)0x0114000000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, CommonInputSubsystem), Z_Construct_UClass_UCommonInputSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommonInputSubsystem_MetaData), NewProp_CommonInputSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_EnhancedInputComponent = { "EnhancedInputComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, EnhancedInputComponent), Z_Construct_UClass_UEnhancedInputComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnhancedInputComponent_MetaData), NewProp_EnhancedInputComponent_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnUIShown = { "OnUIShown", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, OnUIShown), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnUIShown_MetaData), NewProp_OnUIShown_MetaData) }; // 1747643786
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnUIHidden = { "OnUIHidden", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, OnUIHidden), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnUIHidden_MetaData), NewProp_OnUIHidden_MetaData) }; // 910787344
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnInputPlatformChanged = { "OnInputPlatformChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, OnInputPlatformChanged), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInputPlatformChanged_MetaData), NewProp_OnInputPlatformChanged_MetaData) }; // 4737880
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnUIConfigurationUpdated = { "OnUIConfigurationUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, OnUIConfigurationUpdated), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnUIConfigurationUpdated_MetaData), NewProp_OnUIConfigurationUpdated_MetaData) }; // 37762139
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnMinimapUpdated = { "OnMinimapUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronUIBridge, OnMinimapUpdated), Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMinimapUpdated_MetaData), NewProp_OnMinimapUpdated_MetaData) }; // 2186299058
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronUIBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_UIConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_MinimapConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CombatHUDConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CrossPlatformInputConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_ActiveUIWidgets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_UIStates_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_UIStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CurrentInputPlatform_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CurrentInputPlatform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_CommonInputSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_EnhancedInputComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnUIShown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnUIHidden,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnInputPlatformChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnUIConfigurationUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronUIBridge_Statics::NewProp_OnMinimapUpdated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronUIBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronUIBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronUIBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronUIBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronUIBridge_Statics::ClassParams = {
	&UAuracronUIBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronUIBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronUIBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronUIBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronUIBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronUIBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronUIBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronUIBridge.OuterSingleton, Z_Construct_UClass_UAuracronUIBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronUIBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronUIBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_UIConfiguration(TEXT("UIConfiguration"));
	static FName Name_UIStates(TEXT("UIStates"));
	static FName Name_CurrentInputPlatform(TEXT("CurrentInputPlatform"));
	const bool bIsValid = true
		&& Name_UIConfiguration == ClassReps[(int32)ENetFields_Private::UIConfiguration].Property->GetFName()
		&& Name_UIStates == ClassReps[(int32)ENetFields_Private::UIStates].Property->GetFName()
		&& Name_CurrentInputPlatform == ClassReps[(int32)ENetFields_Private::CurrentInputPlatform].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronUIBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronUIBridge);
UAuracronUIBridge::~UAuracronUIBridge() {}
// ********** End Class UAuracronUIBridge **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronUIType_StaticEnum, TEXT("EAuracronUIType"), &Z_Registration_Info_UEnum_EAuracronUIType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1177635711U) },
		{ EAuracronInputPlatform_StaticEnum, TEXT("EAuracronInputPlatform"), &Z_Registration_Info_UEnum_EAuracronInputPlatform, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2897940699U) },
		{ EAuracronUIState_StaticEnum, TEXT("EAuracronUIState"), &Z_Registration_Info_UEnum_EAuracronUIState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 182551264U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronUIStateEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics::NewStructOps, TEXT("AuracronUIStateEntry"), &Z_Registration_Info_UScriptStruct_FAuracronUIStateEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronUIStateEntry), 945707003U) },
		{ FAuracronUIConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics::NewStructOps, TEXT("AuracronUIConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronUIConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronUIConfiguration), 552496867U) },
		{ FAuracronMinimapConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics::NewStructOps, TEXT("AuracronMinimapConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronMinimapConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMinimapConfiguration), 821615854U) },
		{ FAuracronCombatHUDConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics::NewStructOps, TEXT("AuracronCombatHUDConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronCombatHUDConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCombatHUDConfiguration), 3921064051U) },
		{ FAuracronCrossPlatformInputConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics::NewStructOps, TEXT("AuracronCrossPlatformInputConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronCrossPlatformInputConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCrossPlatformInputConfiguration), 1095680642U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronUIBridge, UAuracronUIBridge::StaticClass, TEXT("UAuracronUIBridge"), &Z_Registration_Info_UClass_UAuracronUIBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronUIBridge), 3547761664U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_3709144709(TEXT("/Script/AuracronUIBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h__Script_AuracronUIBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
