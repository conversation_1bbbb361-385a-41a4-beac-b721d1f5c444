// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronPhysicsBridge_init() {}
	AURACRONPHYSICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature();
	AURACRONPHYSICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronPhysicsBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronPhysicsBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronPhysicsBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronPhysicsBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xBBCDFC55,
				0xD4B843B0,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronPhysicsBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronPhysicsBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronPhysicsBridge(Z_Construct_UPackage__Script_AuracronPhysicsBridge, TEXT("/Script/AuracronPhysicsBridge"), Z_Registration_Info_UPackage__Script_AuracronPhysicsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xBBCDFC55, 0xD4B843B0));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
