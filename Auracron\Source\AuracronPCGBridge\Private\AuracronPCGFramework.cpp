// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Main Module Implementation
// Bridge 2.1: PCG Framework - Core Infrastructure

#include "AuracronPCGBridge.h"
#include "AuracronPCGManager.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Stats/StatsHierarchical.h"

// Editor includes
#if WITH_EDITOR
#include "LevelEditor.h"
#include "WorkspaceMenuStructure.h"
#include "WorkspaceMenuStructureModule.h"
#include "Framework/Docking/TabManager.h"
#include "Widgets/Docking/SDockTab.h"
#include "Framework/Commands/UIAction.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "ToolMenus.h"
#endif

// Define logging categories
DEFINE_LOG_CATEGORY(LogAuracronPCG);
DEFINE_LOG_CATEGORY(LogAuracronPCGCore);
DEFINE_LOG_CATEGORY(LogAuracronPCGElements);
DEFINE_LOG_CATEGORY(LogAuracronPCGGenerators);
DEFINE_LOG_CATEGORY(LogAuracronPCGPerformance);

// Define stats
DEFINE_STAT(STAT_AuracronPCG_ElementExecution);
DEFINE_STAT(STAT_AuracronPCG_GraphGeneration);
DEFINE_STAT(STAT_AuracronPCG_PointProcessing);
DEFINE_STAT(STAT_AuracronPCG_PointsGenerated);
DEFINE_STAT(STAT_AuracronPCG_MemoryUsage);

#if WITH_EDITOR
const FName FAuracronPCGFrameworkModule::PCGTabName = TEXT("AuracronPCGFramework");
#endif

void FAuracronPCGFrameworkModule::StartupModule()
{
    AURACRON_PCG_LOG(Log, TEXT("Starting AURACRON PCG Framework Module v%s"), AURACRON_PCG_VERSION_STRING);

    // Initialize core components
    InitializeCore();

#if WITH_EDITOR
    // Initialize editor components
    InitializeEditor();
#endif

    AURACRON_PCG_LOG(Log, TEXT("AURACRON PCG Framework Module started successfully"));
}

void FAuracronPCGFrameworkModule::ShutdownModule()
{
    AURACRON_PCG_LOG(Log, TEXT("Shutting down AURACRON PCG Framework Module"));

#if WITH_EDITOR
    // Shutdown editor components
    ShutdownEditor();
#endif

    // Shutdown core components
    ShutdownCore();

    AURACRON_PCG_LOG(Log, TEXT("AURACRON PCG Framework Module shut down successfully"));
}

FAuracronPCGFrameworkModule& FAuracronPCGFrameworkModule::Get()
{
    return FModuleManager::LoadModuleChecked<FAuracronPCGFrameworkModule>("AuracronPCGFramework");
}

bool FAuracronPCGFrameworkModule::IsAvailable()
{
    return FModuleManager::Get().IsModuleLoaded("AuracronPCGFramework");
}

void FAuracronPCGFrameworkModule::SetConfiguration(const FAuracronPCGConfiguration& NewConfiguration)
{
    Configuration = NewConfiguration;
    
    AURACRON_PCG_LOG(Log, TEXT("PCG Framework configuration updated:"));
    AURACRON_PCG_LOG(Log, TEXT("  - Execution Mode: %d"), (int32)Configuration.ExecutionMode);
    AURACRON_PCG_LOG(Log, TEXT("  - Quality Level: %d"), (int32)Configuration.QualityLevel);
    AURACRON_PCG_LOG(Log, TEXT("  - Thread Count: %d"), Configuration.ThreadCount);
    AURACRON_PCG_LOG(Log, TEXT("  - Max Points Per Batch: %d"), Configuration.MaxPointsPerBatch);
    AURACRON_PCG_LOG(Log, TEXT("  - Memory Pool Size: %d MB"), Configuration.MemoryPoolSizeMB);
    AURACRON_PCG_LOG(Log, TEXT("  - GPU Acceleration: %s"), Configuration.bEnableGPUAcceleration ? TEXT("Enabled") : TEXT("Disabled"));

    // Apply configuration to PCG Manager
    if (PCGManager)
    {
        PCGManager->ApplyConfiguration(Configuration);
    }
}

void FAuracronPCGFrameworkModule::ResetPerformanceMetrics()
{
    PerformanceMetrics = FAuracronPCGPerformanceMetrics();
    AURACRON_PCG_LOG_PERFORMANCE(Log, TEXT("Performance metrics reset"));
}

void FAuracronPCGFrameworkModule::ReportError(const FAuracronPCGErrorInfo& ErrorInfo)
{
    // Add to error history
    ErrorHistory.Add(ErrorInfo);
    
    // Keep only the last 100 errors
    if (ErrorHistory.Num() > 100)
    {
        ErrorHistory.RemoveAt(0, ErrorHistory.Num() - 100);
    }

    // Log the error
    FString ErrorTypeString = UEnum::GetValueAsString(ErrorInfo.ErrorCode);
    AURACRON_PCG_LOG(Error, TEXT("PCG Error [%s]: %s (Context: %s)"), 
                     *ErrorTypeString, *ErrorInfo.ErrorMessage, *ErrorInfo.ErrorContext);

    // Report to logger if available
    if (Logger.IsValid())
    {
        Logger->LogError(ErrorInfo);
    }
}

TArray<FAuracronPCGErrorInfo> FAuracronPCGFrameworkModule::GetErrorHistory(int32 MaxEntries) const
{
    TArray<FAuracronPCGErrorInfo> Result;
    
    int32 StartIndex = FMath::Max(0, ErrorHistory.Num() - MaxEntries);
    for (int32 i = StartIndex; i < ErrorHistory.Num(); ++i)
    {
        Result.Add(ErrorHistory[i]);
    }
    
    return Result;
}

void FAuracronPCGFrameworkModule::ClearErrorHistory()
{
    ErrorHistory.Empty();
    AURACRON_PCG_LOG(Log, TEXT("Error history cleared"));
}

void FAuracronPCGFrameworkModule::GetVersionNumbers(int32& Major, int32& Minor, int32& Patch) const
{
    Major = AURACRON_PCG_VERSION_MAJOR;
    Minor = AURACRON_PCG_VERSION_MINOR;
    Patch = AURACRON_PCG_VERSION_PATCH;
}

void FAuracronPCGFrameworkModule::InitializeCore()
{
    AURACRON_PCG_LOG_CORE(Log, TEXT("Initializing PCG Framework core components"));

    // Initialize logger
    Logger = MakeUnique<FAuracronPCGLogger>();
    if (Logger.IsValid())
    {
        Logger->Initialize();
        AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Logger initialized"));
    }

    // Create PCG Manager
    PCGManager = NewObject<UAuracronPCGManager>();
    if (PCGManager)
    {
        PCGManager->AddToRoot(); // Prevent garbage collection
        PCGManager->Initialize();
        AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Manager created and initialized"));
    }
    else
    {
        AURACRON_PCG_LOG_CORE(Error, TEXT("Failed to create PCG Manager"));
    }

    // Initialize default configuration
    Configuration = FAuracronPCGConfiguration();
    
    // Initialize performance metrics
    PerformanceMetrics = FAuracronPCGPerformanceMetrics();

    // Clear error history
    ErrorHistory.Empty();

    AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Framework core components initialized successfully"));
}

void FAuracronPCGFrameworkModule::InitializeEditor()
{
#if WITH_EDITOR
    AURACRON_PCG_LOG_CORE(Log, TEXT("Initializing PCG Framework editor components"));

    // Register menu extensions
    RegisterMenuExtensions();

    // Register tab spawner
    FGlobalTabmanager::Get()->RegisterNomadTabSpawner(PCGTabName, FOnSpawnTab::CreateRaw(this, &FAuracronPCGFrameworkModule::SpawnPCGTab))
        .SetDisplayName(NSLOCTEXT("AuracronPCGFramework", "PCGTabTitle", "AURACRON PCG Framework"))
        .SetMenuType(ETabSpawnerMenuType::Hidden)
        .SetGroup(WorkspaceMenu::GetMenuStructure().GetDeveloperToolsCategory());

    AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Framework editor components initialized successfully"));
#endif
}

void FAuracronPCGFrameworkModule::ShutdownCore()
{
    AURACRON_PCG_LOG_CORE(Log, TEXT("Shutting down PCG Framework core components"));

    // Cleanup PCG Manager
    if (PCGManager)
    {
        PCGManager->Shutdown();
        PCGManager->RemoveFromRoot();
        PCGManager = nullptr;
        AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Manager shut down"));
    }

    // Cleanup logger
    if (Logger.IsValid())
    {
        Logger->Shutdown();
        Logger.Reset();
        AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Logger shut down"));
    }

    // Clear error history
    ErrorHistory.Empty();

    AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Framework core components shut down successfully"));
}

void FAuracronPCGFrameworkModule::ShutdownEditor()
{
#if WITH_EDITOR
    AURACRON_PCG_LOG_CORE(Log, TEXT("Shutting down PCG Framework editor components"));

    // Unregister menu extensions
    UnregisterMenuExtensions();

    // Unregister tab spawner
    FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(PCGTabName);

    AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Framework editor components shut down successfully"));
#endif
}

#if WITH_EDITOR
void FAuracronPCGFrameworkModule::RegisterMenuExtensions()
{
    // Create command list
    CommandList = MakeShareable(new FUICommandList);

    // Register menu extensions with the level editor
    FLevelEditorModule& LevelEditorModule = FModuleManager::LoadModuleChecked<FLevelEditorModule>("LevelEditor");
    
    // Add menu extension
    TSharedPtr<FExtender> MenuExtender = MakeShareable(new FExtender());
    MenuExtender->AddMenuExtension("WindowLayout", EExtensionHook::After, CommandList, FMenuExtensionDelegate::CreateLambda([this](FMenuBuilder& Builder)
    {
        Builder.AddMenuEntry(
            NSLOCTEXT("AuracronPCGFramework", "OpenPCGFramework", "AURACRON PCG Framework"),
            NSLOCTEXT("AuracronPCGFramework", "OpenPCGFrameworkTooltip", "Open the AURACRON PCG Framework window"),
            FSlateIcon(),
            FUIAction(FExecuteAction::CreateLambda([this]()
            {
                FGlobalTabmanager::Get()->TryInvokeTab(PCGTabName);
            }))
        );
    }));

    LevelEditorModule.GetMenuExtensibilityManager()->AddExtender(MenuExtender);

    AURACRON_PCG_LOG_CORE(Log, TEXT("Menu extensions registered"));
}

void FAuracronPCGFrameworkModule::UnregisterMenuExtensions()
{
    // Menu extensions are automatically cleaned up when the module shuts down
    CommandList.Reset();
    AURACRON_PCG_LOG_CORE(Log, TEXT("Menu extensions unregistered"));
}

TSharedRef<SDockTab> FAuracronPCGFrameworkModule::SpawnPCGTab(const FSpawnTabArgs& Args)
{
    return SNew(SDockTab)
        .TabRole(ETabRole::NomadTab)
        [
            SNew(SVerticalBox)
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(10)
            [
                SNew(STextBlock)
                .Text(NSLOCTEXT("AuracronPCGFramework", "WelcomeText", "Welcome to AURACRON PCG Framework"))
                .Font(FCoreStyle::GetDefaultFontStyle("Bold", 16))
            ]
            + SVerticalBox::Slot()
            .FillHeight(1.0f)
            .Padding(10)
            [
                SNew(STextBlock)
                .Text(NSLOCTEXT("AuracronPCGFramework", "DescriptionText", "Advanced Procedural Content Generation Framework for Unreal Engine 5.6"))
                .AutoWrapText(true)
            ]
        ];
}
#endif

// Implement the module
IMPLEMENT_MODULE(FAuracronPCGFrameworkModule, AuracronPCGFramework)
