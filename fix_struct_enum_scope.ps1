# Script para identificar e reportar problemas de USTRUCT/UENUM em escopo incorreto

Write-Host "Identificando problemas de escopo de USTRUCT/UENUM..."

# Encontrar todos os arquivos .h que contêm USTRUCT ou UENUM
$headerFiles = Get-ChildItem "Auracron\Source" -Recurse -Filter "*.h" | Where-Object { 
    $content = Get-Content $_.FullName -Raw
    $content -match "USTRUCT|UENUM" 
}

$problemFiles = @()

foreach ($file in $headerFiles) {
    Write-Host "Analisando: $($file.Name)"
    
    $lines = Get-Content $file.FullName
    $inClass = $false
    $braceLevel = 0
    $classStartLine = 0
    
    for ($i = 0; $i -lt $lines.Length; $i++) {
        $line = $lines[$i]
        
        # Detectar início de classe
        if ($line -match "^\s*(UCLASS|class\s+\w+.*\s*:\s*public|class\s+\w+\s*{)") {
            $inClass = $true
            $classStartLine = $i + 1
            $braceLevel = 0
        }
        
        # Contar chaves para determinar se estamos dentro da classe
        if ($inClass) {
            $openBraces = ($line -split '\{').Length - 1
            $closeBraces = ($line -split '\}').Length - 1
            $braceLevel += $openBraces - $closeBraces
            
            # Se saímos da classe
            if ($braceLevel -lt 0) {
                $inClass = $false
                $braceLevel = 0
            }
        }
        
        # Verificar se há USTRUCT ou UENUM dentro da classe
        if ($inClass -and $braceLevel -gt 0 -and ($line -match "^\s*(USTRUCT|UENUM)")) {
            $problemFiles += @{
                File = $file.FullName
                Line = $i + 1
                Content = $line.Trim()
                Type = if ($line -match "USTRUCT") { "USTRUCT" } else { "UENUM" }
            }
            Write-Host "  PROBLEMA encontrado na linha $($i + 1): $($line.Trim())" -ForegroundColor Red
        }
    }
}

if ($problemFiles.Count -gt 0) {
    Write-Host "`nResumo dos problemas encontrados:" -ForegroundColor Yellow
    Write-Host "=================================" -ForegroundColor Yellow
    
    foreach ($problem in $problemFiles) {
        Write-Host "Arquivo: $($problem.File)" -ForegroundColor Cyan
        Write-Host "Linha: $($problem.Line)" -ForegroundColor Cyan
        Write-Host "Tipo: $($problem.Type)" -ForegroundColor Cyan
        Write-Host "Conteúdo: $($problem.Content)" -ForegroundColor Cyan
        Write-Host "---"
    }
    
    Write-Host "`nEsses USTRUCT/UENUM precisam ser movidos para escopo global!" -ForegroundColor Red
} else {
    Write-Host "`nNenhum problema de escopo encontrado!" -ForegroundColor Green
}

Write-Host "`nAnálise concluída!"
