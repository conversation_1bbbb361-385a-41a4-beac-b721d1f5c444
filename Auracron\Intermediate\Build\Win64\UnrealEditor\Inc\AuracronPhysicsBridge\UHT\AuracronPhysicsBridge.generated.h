// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPhysicsBridge.h"

#ifdef AURACRONPHYSICSBRIDGE_AuracronPhysicsBridge_generated_h
#error "AuracronPhysicsBridge.generated.h already included, missing '#pragma once' in AuracronPhysicsBridge.h"
#endif
#define AURACRONPHYSICSBRIDGE_AuracronPhysicsBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class AGeometryCollectionActor;
struct FAuracronDestructionConfiguration;
struct FAuracronFieldSystemConfiguration;

// ********** Begin ScriptStruct FAuracronChaosPhysicsConfiguration ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_73_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChaosPhysicsConfiguration;
// ********** End ScriptStruct FAuracronChaosPhysicsConfiguration **********************************

// ********** Begin ScriptStruct FAuracronDestructionConfiguration *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_142_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDestructionConfiguration;
// ********** End ScriptStruct FAuracronDestructionConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronFieldSystemConfiguration *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_211_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFieldSystemConfiguration;
// ********** End ScriptStruct FAuracronFieldSystemConfiguration ***********************************

// ********** Begin Delegate FOnObjectDestroyed ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_481_DELEGATE \
static void FOnObjectDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnObjectDestroyed, AActor* DestroyedActor, FAuracronDestructionConfiguration DestructionConfig);


// ********** End Delegate FOnObjectDestroyed ******************************************************

// ********** Begin Delegate FOnFieldSystemApplied *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_486_DELEGATE \
static void FOnFieldSystemApplied_DelegateWrapper(const FMulticastScriptDelegate& OnFieldSystemApplied, FVector Location, FAuracronFieldSystemConfiguration FieldConfig);


// ********** End Delegate FOnFieldSystemApplied ***************************************************

// ********** Begin Class UAuracronPhysicsBridge ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_277_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetPhysicsQuality); \
	DECLARE_FUNCTION(execCleanupInactivePhysicsObjects); \
	DECLARE_FUNCTION(execOptimizePhysicsByDistance); \
	DECLARE_FUNCTION(execCreateSpecialPhysicsZone); \
	DECLARE_FUNCTION(execApplyRealmPhysicsToObject); \
	DECLARE_FUNCTION(execConfigureRealmPhysics); \
	DECLARE_FUNCTION(execCreateVortexField); \
	DECLARE_FUNCTION(execCreateDirectionalForceField); \
	DECLARE_FUNCTION(execCreateRadialForceField); \
	DECLARE_FUNCTION(execApplyFieldSystem); \
	DECLARE_FUNCTION(execConvertToGeometryCollection); \
	DECLARE_FUNCTION(execFractureObject); \
	DECLARE_FUNCTION(execCreateExplosion); \
	DECLARE_FUNCTION(execDestroyObject); \
	DECLARE_FUNCTION(execApplyCustomGravityToObject); \
	DECLARE_FUNCTION(execSetCustomGravity); \
	DECLARE_FUNCTION(execApplyTorqueToObject); \
	DECLARE_FUNCTION(execApplyImpulseToObject); \
	DECLARE_FUNCTION(execApplyForceToObject);


AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_277_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPhysicsBridge(); \
	friend struct Z_Construct_UClass_UAuracronPhysicsBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPhysicsBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronPhysicsBridge"), Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPhysicsBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		ChaosPhysicsConfiguration=NETFIELD_REP_START, \
		NETFIELD_REP_END=ChaosPhysicsConfiguration	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_277_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPhysicsBridge(UAuracronPhysicsBridge&&) = delete; \
	UAuracronPhysicsBridge(const UAuracronPhysicsBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPhysicsBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPhysicsBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPhysicsBridge) \
	NO_API virtual ~UAuracronPhysicsBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_274_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_277_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_277_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_277_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_277_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPhysicsBridge;

// ********** End Class UAuracronPhysicsBridge *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h

// ********** Begin Enum EAuracronPhysicsType ******************************************************
#define FOREACH_ENUM_EAURACRONPHYSICSTYPE(op) \
	op(EAuracronPhysicsType::None) \
	op(EAuracronPhysicsType::RigidBody) \
	op(EAuracronPhysicsType::SoftBody) \
	op(EAuracronPhysicsType::Fluid) \
	op(EAuracronPhysicsType::Cloth) \
	op(EAuracronPhysicsType::Destruction) \
	op(EAuracronPhysicsType::FieldSystem) \
	op(EAuracronPhysicsType::Constraint) \
	op(EAuracronPhysicsType::Vehicle) \
	op(EAuracronPhysicsType::Character) 

enum class EAuracronPhysicsType : uint8;
template<> struct TIsUEnumClass<EAuracronPhysicsType> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronPhysicsType>();
// ********** End Enum EAuracronPhysicsType ********************************************************

// ********** Begin Enum EAuracronDestructionType **************************************************
#define FOREACH_ENUM_EAURACRONDESTRUCTIONTYPE(op) \
	op(EAuracronDestructionType::None) \
	op(EAuracronDestructionType::Fracture) \
	op(EAuracronDestructionType::Explosion) \
	op(EAuracronDestructionType::Slice) \
	op(EAuracronDestructionType::Crumble) \
	op(EAuracronDestructionType::Shatter) \
	op(EAuracronDestructionType::Melt) \
	op(EAuracronDestructionType::Dissolve) \
	op(EAuracronDestructionType::Vaporize) 

enum class EAuracronDestructionType : uint8;
template<> struct TIsUEnumClass<EAuracronDestructionType> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronDestructionType>();
// ********** End Enum EAuracronDestructionType ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
