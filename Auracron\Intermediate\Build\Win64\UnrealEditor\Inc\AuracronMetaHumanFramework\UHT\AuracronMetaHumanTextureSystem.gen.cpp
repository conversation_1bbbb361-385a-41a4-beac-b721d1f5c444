// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/Systems/AuracronMetaHumanTextureSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanTextureSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanTextureSystem();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality();
AURACRONMETAHUMANFRAMEWORK_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSkinTextureConfig();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTextureParams();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTextureResult();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UEnum* Z_Construct_UEnum_CoreUObject_EPixelFormat();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTextureRenderTarget2D_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronMetaHumanTextureType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType;
static UEnum* EAuracronMetaHumanTextureType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronMetaHumanTextureType"));
	}
	return Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronMetaHumanTextureType>()
{
	return EAuracronMetaHumanTextureType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AmbientOcclusion.DisplayName", "Ambient Occlusion" },
		{ "AmbientOcclusion.Name", "EAuracronMetaHumanTextureType::AmbientOcclusion" },
		{ "BlueprintType", "true" },
		{ "Cavity.DisplayName", "Cavity" },
		{ "Cavity.Name", "EAuracronMetaHumanTextureType::Cavity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Texture Processing Types\n */" },
#endif
		{ "Curvature.DisplayName", "Curvature" },
		{ "Curvature.Name", "EAuracronMetaHumanTextureType::Curvature" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronMetaHumanTextureType::Custom" },
		{ "Diffuse.DisplayName", "Diffuse/Albedo" },
		{ "Diffuse.Name", "EAuracronMetaHumanTextureType::Diffuse" },
		{ "Displacement.DisplayName", "Displacement" },
		{ "Displacement.Name", "EAuracronMetaHumanTextureType::Displacement" },
		{ "Emissive.DisplayName", "Emissive" },
		{ "Emissive.Name", "EAuracronMetaHumanTextureType::Emissive" },
		{ "Metallic.DisplayName", "Metallic" },
		{ "Metallic.Name", "EAuracronMetaHumanTextureType::Metallic" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
		{ "Normal.DisplayName", "Normal Map" },
		{ "Normal.Name", "EAuracronMetaHumanTextureType::Normal" },
		{ "Opacity.DisplayName", "Opacity" },
		{ "Opacity.Name", "EAuracronMetaHumanTextureType::Opacity" },
		{ "Roughness.DisplayName", "Roughness" },
		{ "Roughness.Name", "EAuracronMetaHumanTextureType::Roughness" },
		{ "Specular.DisplayName", "Specular" },
		{ "Specular.Name", "EAuracronMetaHumanTextureType::Specular" },
		{ "SubsurfaceColor.DisplayName", "Subsurface Color" },
		{ "SubsurfaceColor.Name", "EAuracronMetaHumanTextureType::SubsurfaceColor" },
		{ "SubsurfaceProfile.DisplayName", "Subsurface Profile" },
		{ "SubsurfaceProfile.Name", "EAuracronMetaHumanTextureType::SubsurfaceProfile" },
		{ "Thickness.DisplayName", "Thickness" },
		{ "Thickness.Name", "EAuracronMetaHumanTextureType::Thickness" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture Processing Types" },
#endif
		{ "WorldPositionOffset.DisplayName", "World Position Offset" },
		{ "WorldPositionOffset.Name", "EAuracronMetaHumanTextureType::WorldPositionOffset" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronMetaHumanTextureType::Diffuse", (int64)EAuracronMetaHumanTextureType::Diffuse },
		{ "EAuracronMetaHumanTextureType::Normal", (int64)EAuracronMetaHumanTextureType::Normal },
		{ "EAuracronMetaHumanTextureType::Roughness", (int64)EAuracronMetaHumanTextureType::Roughness },
		{ "EAuracronMetaHumanTextureType::Metallic", (int64)EAuracronMetaHumanTextureType::Metallic },
		{ "EAuracronMetaHumanTextureType::Specular", (int64)EAuracronMetaHumanTextureType::Specular },
		{ "EAuracronMetaHumanTextureType::Emissive", (int64)EAuracronMetaHumanTextureType::Emissive },
		{ "EAuracronMetaHumanTextureType::Opacity", (int64)EAuracronMetaHumanTextureType::Opacity },
		{ "EAuracronMetaHumanTextureType::SubsurfaceColor", (int64)EAuracronMetaHumanTextureType::SubsurfaceColor },
		{ "EAuracronMetaHumanTextureType::SubsurfaceProfile", (int64)EAuracronMetaHumanTextureType::SubsurfaceProfile },
		{ "EAuracronMetaHumanTextureType::Displacement", (int64)EAuracronMetaHumanTextureType::Displacement },
		{ "EAuracronMetaHumanTextureType::AmbientOcclusion", (int64)EAuracronMetaHumanTextureType::AmbientOcclusion },
		{ "EAuracronMetaHumanTextureType::Cavity", (int64)EAuracronMetaHumanTextureType::Cavity },
		{ "EAuracronMetaHumanTextureType::Curvature", (int64)EAuracronMetaHumanTextureType::Curvature },
		{ "EAuracronMetaHumanTextureType::Thickness", (int64)EAuracronMetaHumanTextureType::Thickness },
		{ "EAuracronMetaHumanTextureType::WorldPositionOffset", (int64)EAuracronMetaHumanTextureType::WorldPositionOffset },
		{ "EAuracronMetaHumanTextureType::Custom", (int64)EAuracronMetaHumanTextureType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronMetaHumanTextureType",
	"EAuracronMetaHumanTextureType",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType()
{
	if (!Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType.InnerSingleton;
}
// ********** End Enum EAuracronMetaHumanTextureType ***********************************************

// ********** Begin Enum EAuracronTextureQuality ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTextureQuality;
static UEnum* EAuracronTextureQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTextureQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTextureQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronTextureQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronTextureQuality.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronTextureQuality>()
{
	return EAuracronTextureQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Texture Quality Levels\n */" },
#endif
		{ "Custom.DisplayName", "Custom Resolution" },
		{ "Custom.Name", "EAuracronTextureQuality::Custom" },
		{ "High.DisplayName", "High (2048x2048)" },
		{ "High.Name", "EAuracronTextureQuality::High" },
		{ "Low.DisplayName", "Low (512x512)" },
		{ "Low.Name", "EAuracronTextureQuality::Low" },
		{ "Medium.DisplayName", "Medium (1024x1024)" },
		{ "Medium.Name", "EAuracronTextureQuality::Medium" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture Quality Levels" },
#endif
		{ "Ultra.DisplayName", "Ultra (4096x4096)" },
		{ "Ultra.Name", "EAuracronTextureQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTextureQuality::Low", (int64)EAuracronTextureQuality::Low },
		{ "EAuracronTextureQuality::Medium", (int64)EAuracronTextureQuality::Medium },
		{ "EAuracronTextureQuality::High", (int64)EAuracronTextureQuality::High },
		{ "EAuracronTextureQuality::Ultra", (int64)EAuracronTextureQuality::Ultra },
		{ "EAuracronTextureQuality::Custom", (int64)EAuracronTextureQuality::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronTextureQuality",
	"EAuracronTextureQuality",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronTextureQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTextureQuality.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTextureQuality.InnerSingleton;
}
// ********** End Enum EAuracronTextureQuality *****************************************************

// ********** Begin ScriptStruct FAuracronTextureParams ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTextureParams;
class UScriptStruct* FAuracronTextureParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTextureParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTextureParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTextureParams, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronTextureParams"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTextureParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTextureParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Texture Generation Parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture Generation Parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureType_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Texture type to generate */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture type to generate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality level */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomResolution_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custom resolution (if Quality is Custom) */" },
#endif
		{ "EditCondition", "Quality == EAuracronTextureQuality::Custom" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom resolution (if Quality is Custom)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGPUAcceleration_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable GPU acceleration */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable GPU acceleration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreaming_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable texture streaming */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable texture streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionSettings_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Compression settings */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compression settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateMips_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mip generation settings */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mip generation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSRGB_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** SRGB color space */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "SRGB color space" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalParams_MetaData[] = {
		{ "Category", "Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Additional parameters as JSON */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additional parameters as JSON" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TextureType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TextureType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomResolution;
	static void NewProp_bUseGPUAcceleration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGPUAcceleration;
	static void NewProp_bEnableStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreaming;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompressionSettings;
	static void NewProp_bGenerateMips_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateMips;
	static void NewProp_bSRGB_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSRGB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalParams;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTextureParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_TextureType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_TextureType = { "TextureType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureParams, TextureType), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureType_MetaData), NewProp_TextureType_MetaData) }; // 959042091
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureParams, Quality), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronTextureQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 4189780467
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_CustomResolution = { "CustomResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureParams, CustomResolution), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomResolution_MetaData), NewProp_CustomResolution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bUseGPUAcceleration_SetBit(void* Obj)
{
	((FAuracronTextureParams*)Obj)->bUseGPUAcceleration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bUseGPUAcceleration = { "bUseGPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureParams), &Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bUseGPUAcceleration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGPUAcceleration_MetaData), NewProp_bUseGPUAcceleration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bEnableStreaming_SetBit(void* Obj)
{
	((FAuracronTextureParams*)Obj)->bEnableStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bEnableStreaming = { "bEnableStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureParams), &Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bEnableStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreaming_MetaData), NewProp_bEnableStreaming_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_CompressionSettings = { "CompressionSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureParams, CompressionSettings), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionSettings_MetaData), NewProp_CompressionSettings_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bGenerateMips_SetBit(void* Obj)
{
	((FAuracronTextureParams*)Obj)->bGenerateMips = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bGenerateMips = { "bGenerateMips", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureParams), &Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bGenerateMips_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateMips_MetaData), NewProp_bGenerateMips_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bSRGB_SetBit(void* Obj)
{
	((FAuracronTextureParams*)Obj)->bSRGB = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bSRGB = { "bSRGB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureParams), &Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bSRGB_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSRGB_MetaData), NewProp_bSRGB_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_AdditionalParams = { "AdditionalParams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureParams, AdditionalParams), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalParams_MetaData), NewProp_AdditionalParams_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_TextureType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_TextureType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_CustomResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bUseGPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bEnableStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_CompressionSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bGenerateMips,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_bSRGB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewProp_AdditionalParams,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronTextureParams",
	Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::PropPointers),
	sizeof(FAuracronTextureParams),
	alignof(FAuracronTextureParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTextureParams()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTextureParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTextureParams.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTextureParams.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTextureParams **********************************************

// ********** Begin ScriptStruct FAuracronTextureResult ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTextureResult;
class UScriptStruct* FAuracronTextureResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTextureResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTextureResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTextureResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronTextureResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTextureResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTextureResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Texture Processing Result\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture Processing Result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether the operation was successful */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the operation was successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Result message */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Result message" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedTexture_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generated texture */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generated texture" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingTimeMS_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Processing time in milliseconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processing time in milliseconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputPath_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Output file path */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output file path" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureStats_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Texture statistics */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture statistics" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedTexture;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTimeMS;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputPath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TextureStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTextureResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronTextureResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTextureResult), &Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureResult, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_GeneratedTexture = { "GeneratedTexture", nullptr, (EPropertyFlags)0x0114000000000014, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureResult, GeneratedTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedTexture_MetaData), NewProp_GeneratedTexture_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_ProcessingTimeMS = { "ProcessingTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureResult, ProcessingTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingTimeMS_MetaData), NewProp_ProcessingTimeMS_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_OutputPath = { "OutputPath", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureResult, OutputPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputPath_MetaData), NewProp_OutputPath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_TextureStats = { "TextureStats", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTextureResult, TextureStats), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureStats_MetaData), NewProp_TextureStats_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_GeneratedTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_ProcessingTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_OutputPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewProp_TextureStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronTextureResult",
	Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::PropPointers),
	sizeof(FAuracronTextureResult),
	alignof(FAuracronTextureResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTextureResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTextureResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTextureResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTextureResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTextureResult **********************************************

// ********** Begin ScriptStruct FAuracronSkinTextureConfig ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig;
class UScriptStruct* FAuracronSkinTextureConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSkinTextureConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronSkinTextureConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Skin Texture Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Skin Texture Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkinTone_MetaData[] = {
		{ "Category", "Skin" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Skin tone (0.0 = very light, 1.0 = very dark) */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Skin tone (0.0 = very light, 1.0 = very dark)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkinRoughness_MetaData[] = {
		{ "Category", "Skin" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Skin roughness */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Skin roughness" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubsurfaceIntensity_MetaData[] = {
		{ "Category", "Skin" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Subsurface scattering intensity */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subsurface scattering intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoreDetailIntensity_MetaData[] = {
		{ "Category", "Skin" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pore detail intensity */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pore detail intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WrinkleIntensity_MetaData[] = {
		{ "Category", "Skin" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Wrinkle intensity */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wrinkle intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgeFactor_MetaData[] = {
		{ "Category", "Skin" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Age factor (0.0 = young, 1.0 = old) */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Age factor (0.0 = young, 1.0 = old)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFreckles_MetaData[] = {
		{ "Category", "Skin" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable freckles */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable freckles" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FreckleIntensity_MetaData[] = {
		{ "Category", "Skin" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Freckle intensity */" },
#endif
		{ "EditCondition", "bEnableFreckles" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Freckle intensity" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SkinTone;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SkinRoughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SubsurfaceIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PoreDetailIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WrinkleIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AgeFactor;
	static void NewProp_bEnableFreckles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFreckles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FreckleIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSkinTextureConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_SkinTone = { "SkinTone", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSkinTextureConfig, SkinTone), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkinTone_MetaData), NewProp_SkinTone_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_SkinRoughness = { "SkinRoughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSkinTextureConfig, SkinRoughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkinRoughness_MetaData), NewProp_SkinRoughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_SubsurfaceIntensity = { "SubsurfaceIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSkinTextureConfig, SubsurfaceIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubsurfaceIntensity_MetaData), NewProp_SubsurfaceIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_PoreDetailIntensity = { "PoreDetailIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSkinTextureConfig, PoreDetailIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoreDetailIntensity_MetaData), NewProp_PoreDetailIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_WrinkleIntensity = { "WrinkleIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSkinTextureConfig, WrinkleIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WrinkleIntensity_MetaData), NewProp_WrinkleIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_AgeFactor = { "AgeFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSkinTextureConfig, AgeFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgeFactor_MetaData), NewProp_AgeFactor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_bEnableFreckles_SetBit(void* Obj)
{
	((FAuracronSkinTextureConfig*)Obj)->bEnableFreckles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_bEnableFreckles = { "bEnableFreckles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSkinTextureConfig), &Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_bEnableFreckles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFreckles_MetaData), NewProp_bEnableFreckles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_FreckleIntensity = { "FreckleIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSkinTextureConfig, FreckleIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FreckleIntensity_MetaData), NewProp_FreckleIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_SkinTone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_SkinRoughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_SubsurfaceIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_PoreDetailIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_WrinkleIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_AgeFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_bEnableFreckles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewProp_FreckleIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronSkinTextureConfig",
	Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::PropPointers),
	sizeof(FAuracronSkinTextureConfig),
	alignof(FAuracronSkinTextureConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSkinTextureConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSkinTextureConfig ******************************************

// ********** Begin Delegate FAuracronTextureComplete **********************************************
struct Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronTextureComplete_Parms
	{
		FAuracronTextureResult Result;
		FString OperationID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronTextureComplete_Parms, Result), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 1415548636
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronTextureComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::NewProp_OperationID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework, nullptr, "AuracronTextureComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronTextureComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronTextureComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FAuracronTextureComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronTextureComplete, FAuracronTextureResult const& Result, const FString& OperationID)
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronTextureComplete_Parms
	{
		FAuracronTextureResult Result;
		FString OperationID;
	};
	_Script_AuracronMetaHumanFramework_eventAuracronTextureComplete_Parms Parms;
	Parms.Result=Result;
	Parms.OperationID=OperationID;
	AuracronTextureComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FAuracronTextureComplete ************************************************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function AnalyzeTextureQuality ***********
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics
{
	struct AuracronMetaHumanTextureSystem_eventAnalyzeTextureQuality_Parms
	{
		UTexture2D* Texture;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Analyze texture quality\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analyze texture quality" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Texture;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventAnalyzeTextureQuality_Parms, Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventAnalyzeTextureQuality_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::NewProp_Texture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "AnalyzeTextureQuality", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::AuracronMetaHumanTextureSystem_eventAnalyzeTextureQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::AuracronMetaHumanTextureSystem_eventAnalyzeTextureQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execAnalyzeTextureQuality)
{
	P_GET_OBJECT(UTexture2D,Z_Param_Texture);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->AnalyzeTextureQuality(Z_Param_Texture);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function AnalyzeTextureQuality *************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function ApplyTextureFilterGPU ***********
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics
{
	struct AuracronMetaHumanTextureSystem_eventApplyTextureFilterGPU_Parms
	{
		UTexture2D* InputTexture;
		FString FilterType;
		float FilterStrength;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply texture filter using GPU\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply texture filter using GPU" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputTexture;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilterType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FilterStrength;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_InputTexture = { "InputTexture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventApplyTextureFilterGPU_Parms, InputTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_FilterType = { "FilterType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventApplyTextureFilterGPU_Parms, FilterType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterType_MetaData), NewProp_FilterType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_FilterStrength = { "FilterStrength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventApplyTextureFilterGPU_Parms, FilterStrength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventApplyTextureFilterGPU_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_InputTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_FilterType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_FilterStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "ApplyTextureFilterGPU", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::AuracronMetaHumanTextureSystem_eventApplyTextureFilterGPU_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::AuracronMetaHumanTextureSystem_eventApplyTextureFilterGPU_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execApplyTextureFilterGPU)
{
	P_GET_OBJECT(UTexture2D,Z_Param_InputTexture);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilterType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FilterStrength);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->ApplyTextureFilterGPU(Z_Param_InputTexture,Z_Param_FilterType,Z_Param_FilterStrength);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function ApplyTextureFilterGPU *************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function BlendTexturesGPU ****************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics
{
	struct AuracronMetaHumanTextureSystem_eventBlendTexturesGPU_Parms
	{
		TArray<UTexture2D*> InputTextures;
		TArray<float> BlendWeights;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Blend textures using GPU\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blend textures using GPU" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputTextures_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendWeights_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputTextures_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputTextures;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendWeights;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_InputTextures_Inner = { "InputTextures", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_InputTextures = { "InputTextures", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventBlendTexturesGPU_Parms, InputTextures), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputTextures_MetaData), NewProp_InputTextures_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_BlendWeights_Inner = { "BlendWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_BlendWeights = { "BlendWeights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventBlendTexturesGPU_Parms, BlendWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendWeights_MetaData), NewProp_BlendWeights_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventBlendTexturesGPU_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_InputTextures_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_InputTextures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_BlendWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_BlendWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "BlendTexturesGPU", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::AuracronMetaHumanTextureSystem_eventBlendTexturesGPU_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::AuracronMetaHumanTextureSystem_eventBlendTexturesGPU_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execBlendTexturesGPU)
{
	P_GET_TARRAY_REF(UTexture2D*,Z_Param_Out_InputTextures);
	P_GET_TARRAY_REF(float,Z_Param_Out_BlendWeights);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->BlendTexturesGPU(Z_Param_Out_InputTextures,Z_Param_Out_BlendWeights);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function BlendTexturesGPU ******************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function ConvertTextureFormat ************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics
{
	struct AuracronMetaHumanTextureSystem_eventConvertTextureFormat_Parms
	{
		UTexture2D* SourceTexture;
		FString TargetFormat;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Convert texture format\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Convert texture format" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetFormat_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceTexture;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetFormat;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::NewProp_SourceTexture = { "SourceTexture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventConvertTextureFormat_Parms, SourceTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::NewProp_TargetFormat = { "TargetFormat", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventConvertTextureFormat_Parms, TargetFormat), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetFormat_MetaData), NewProp_TargetFormat_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventConvertTextureFormat_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::NewProp_SourceTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::NewProp_TargetFormat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "ConvertTextureFormat", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::AuracronMetaHumanTextureSystem_eventConvertTextureFormat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::AuracronMetaHumanTextureSystem_eventConvertTextureFormat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execConvertTextureFormat)
{
	P_GET_OBJECT(UTexture2D,Z_Param_SourceTexture);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetFormat);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->ConvertTextureFormat(Z_Param_SourceTexture,Z_Param_TargetFormat);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function ConvertTextureFormat **************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function CreateRenderTargetTexture *******
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics
{
	struct AuracronMetaHumanTextureSystem_eventCreateRenderTargetTexture_Parms
	{
		int32 Width;
		int32 Height;
		TEnumAsByte<EPixelFormat> Format;
		UTextureRenderTarget2D* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|RealTime" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create render target texture\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create render target texture" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Height;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Format;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventCreateRenderTargetTexture_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventCreateRenderTargetTexture_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_Format = { "Format", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventCreateRenderTargetTexture_Parms, Format), Z_Construct_UEnum_CoreUObject_EPixelFormat, METADATA_PARAMS(0, nullptr) }; // 2051073252
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventCreateRenderTargetTexture_Parms, ReturnValue), Z_Construct_UClass_UTextureRenderTarget2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_Format,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "CreateRenderTargetTexture", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::AuracronMetaHumanTextureSystem_eventCreateRenderTargetTexture_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::AuracronMetaHumanTextureSystem_eventCreateRenderTargetTexture_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execCreateRenderTargetTexture)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Width);
	P_GET_PROPERTY(FIntProperty,Z_Param_Height);
	P_GET_PROPERTY(FByteProperty,Z_Param_Format);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UTextureRenderTarget2D**)Z_Param__Result=P_THIS->CreateRenderTargetTexture(Z_Param_Width,Z_Param_Height,EPixelFormat(Z_Param_Format));
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function CreateRenderTargetTexture *********

// ********** Begin Class UAuracronMetaHumanTextureSystem Function GenerateSkinTexture *************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics
{
	struct AuracronMetaHumanTextureSystem_eventGenerateSkinTexture_Parms
	{
		FAuracronSkinTextureConfig Config;
		FAuracronTextureParams Params;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate skin texture\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate skin texture" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Params_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Params;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateSkinTexture_Parms, Config), Z_Construct_UScriptStruct_FAuracronSkinTextureConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1066872387
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::NewProp_Params = { "Params", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateSkinTexture_Parms, Params), Z_Construct_UScriptStruct_FAuracronTextureParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Params_MetaData), NewProp_Params_MetaData) }; // 608788009
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateSkinTexture_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::NewProp_Params,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "GenerateSkinTexture", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::AuracronMetaHumanTextureSystem_eventGenerateSkinTexture_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::AuracronMetaHumanTextureSystem_eventGenerateSkinTexture_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execGenerateSkinTexture)
{
	P_GET_STRUCT_REF(FAuracronSkinTextureConfig,Z_Param_Out_Config);
	P_GET_STRUCT_REF(FAuracronTextureParams,Z_Param_Out_Params);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->GenerateSkinTexture(Z_Param_Out_Config,Z_Param_Out_Params);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function GenerateSkinTexture ***************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function GenerateSkinTextureAsync ********
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics
{
	struct AuracronMetaHumanTextureSystem_eventGenerateSkinTextureAsync_Parms
	{
		FAuracronSkinTextureConfig Config;
		FAuracronTextureParams Params;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate skin texture asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate skin texture asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Params_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Params;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateSkinTextureAsync_Parms, Config), Z_Construct_UScriptStruct_FAuracronSkinTextureConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1066872387
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::NewProp_Params = { "Params", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateSkinTextureAsync_Parms, Params), Z_Construct_UScriptStruct_FAuracronTextureParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Params_MetaData), NewProp_Params_MetaData) }; // 608788009
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateSkinTextureAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::NewProp_Params,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "GenerateSkinTextureAsync", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::AuracronMetaHumanTextureSystem_eventGenerateSkinTextureAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::AuracronMetaHumanTextureSystem_eventGenerateSkinTextureAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execGenerateSkinTextureAsync)
{
	P_GET_STRUCT_REF(FAuracronSkinTextureConfig,Z_Param_Out_Config);
	P_GET_STRUCT_REF(FAuracronTextureParams,Z_Param_Out_Params);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateSkinTextureAsync(Z_Param_Out_Config,Z_Param_Out_Params);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function GenerateSkinTextureAsync **********

// ********** Begin Class UAuracronMetaHumanTextureSystem Function GenerateTexture *****************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics
{
	struct AuracronMetaHumanTextureSystem_eventGenerateTexture_Parms
	{
		FAuracronTextureParams Params;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate texture from parameters\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate texture from parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Params_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Params;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::NewProp_Params = { "Params", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateTexture_Parms, Params), Z_Construct_UScriptStruct_FAuracronTextureParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Params_MetaData), NewProp_Params_MetaData) }; // 608788009
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateTexture_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::NewProp_Params,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "GenerateTexture", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::AuracronMetaHumanTextureSystem_eventGenerateTexture_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::AuracronMetaHumanTextureSystem_eventGenerateTexture_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execGenerateTexture)
{
	P_GET_STRUCT_REF(FAuracronTextureParams,Z_Param_Out_Params);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->GenerateTexture(Z_Param_Out_Params);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function GenerateTexture *******************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function GenerateTextureAsync ************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics
{
	struct AuracronMetaHumanTextureSystem_eventGenerateTextureAsync_Parms
	{
		FAuracronTextureParams Params;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate texture asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate texture asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Params_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Params;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::NewProp_Params = { "Params", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateTextureAsync_Parms, Params), Z_Construct_UScriptStruct_FAuracronTextureParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Params_MetaData), NewProp_Params_MetaData) }; // 608788009
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGenerateTextureAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::NewProp_Params,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "GenerateTextureAsync", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::AuracronMetaHumanTextureSystem_eventGenerateTextureAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::AuracronMetaHumanTextureSystem_eventGenerateTextureAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execGenerateTextureAsync)
{
	P_GET_STRUCT_REF(FAuracronTextureParams,Z_Param_Out_Params);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateTextureAsync(Z_Param_Out_Params);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function GenerateTextureAsync **************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function GetAsyncOperationResult *********
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics
{
	struct AuracronMetaHumanTextureSystem_eventGetAsyncOperationResult_Parms
	{
		FString OperationID;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get async operation result\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get async operation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGetAsyncOperationResult_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGetAsyncOperationResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "GetAsyncOperationResult", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::AuracronMetaHumanTextureSystem_eventGetAsyncOperationResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::AuracronMetaHumanTextureSystem_eventGetAsyncOperationResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execGetAsyncOperationResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->GetAsyncOperationResult(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function GetAsyncOperationResult ***********

// ********** Begin Class UAuracronMetaHumanTextureSystem Function GetSupportedTextureFormats ******
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics
{
	struct AuracronMetaHumanTextureSystem_eventGetSupportedTextureFormats_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get supported texture formats\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get supported texture formats" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGetSupportedTextureFormats_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "GetSupportedTextureFormats", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::AuracronMetaHumanTextureSystem_eventGetSupportedTextureFormats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::AuracronMetaHumanTextureSystem_eventGetSupportedTextureFormats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execGetSupportedTextureFormats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetSupportedTextureFormats();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function GetSupportedTextureFormats ********

// ********** Begin Class UAuracronMetaHumanTextureSystem Function GetTextureStatistics ************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics
{
	struct AuracronMetaHumanTextureSystem_eventGetTextureStatistics_Parms
	{
		UTexture2D* Texture;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get texture statistics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get texture statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Texture;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGetTextureStatistics_Parms, Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventGetTextureStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::NewProp_Texture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "GetTextureStatistics", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::AuracronMetaHumanTextureSystem_eventGetTextureStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::AuracronMetaHumanTextureSystem_eventGetTextureStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execGetTextureStatistics)
{
	P_GET_OBJECT(UTexture2D,Z_Param_Texture);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetTextureStatistics(Z_Param_Texture);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function GetTextureStatistics **************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function Initialize **********************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics
{
	struct AuracronMetaHumanTextureSystem_eventInitialize_Parms
	{
		UAuracronMetaHumanFramework* InFramework;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize texture system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize texture system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InFramework;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::NewProp_InFramework = { "InFramework", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventInitialize_Parms, InFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanTextureSystem_eventInitialize_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanTextureSystem_eventInitialize_Parms), &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::NewProp_InFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "Initialize", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::AuracronMetaHumanTextureSystem_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::AuracronMetaHumanTextureSystem_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execInitialize)
{
	P_GET_OBJECT(UAuracronMetaHumanFramework,Z_Param_InFramework);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Initialize(Z_Param_InFramework);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function Initialize ************************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function IsAsyncOperationComplete ********
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics
{
	struct AuracronMetaHumanTextureSystem_eventIsAsyncOperationComplete_Parms
	{
		FString OperationID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if async operation is complete\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if async operation is complete" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventIsAsyncOperationComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanTextureSystem_eventIsAsyncOperationComplete_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanTextureSystem_eventIsAsyncOperationComplete_Parms), &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "IsAsyncOperationComplete", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanTextureSystem_eventIsAsyncOperationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanTextureSystem_eventIsAsyncOperationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execIsAsyncOperationComplete)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAsyncOperationComplete(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function IsAsyncOperationComplete **********

// ********** Begin Class UAuracronMetaHumanTextureSystem Function IsInitialized *******************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics
{
	struct AuracronMetaHumanTextureSystem_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if texture system is initialized\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if texture system is initialized" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanTextureSystem_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanTextureSystem_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::AuracronMetaHumanTextureSystem_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::AuracronMetaHumanTextureSystem_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function IsInitialized *********************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function OptimizeTexture *****************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics
{
	struct AuracronMetaHumanTextureSystem_eventOptimizeTexture_Parms
	{
		UTexture2D* Texture;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize texture\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize texture" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Texture;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventOptimizeTexture_Parms, Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventOptimizeTexture_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::NewProp_Texture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "OptimizeTexture", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::AuracronMetaHumanTextureSystem_eventOptimizeTexture_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::AuracronMetaHumanTextureSystem_eventOptimizeTexture_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execOptimizeTexture)
{
	P_GET_OBJECT(UTexture2D,Z_Param_Texture);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->OptimizeTexture(Z_Param_Texture);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function OptimizeTexture *******************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function ProcessTextureWithGPU ***********
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics
{
	struct AuracronMetaHumanTextureSystem_eventProcessTextureWithGPU_Parms
	{
		UTexture2D* InputTexture;
		FString ShaderName;
		TMap<FString,float> Parameters;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Process texture with GPU compute shader\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Process texture with GPU compute shader" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShaderName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputTexture;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ShaderName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_InputTexture = { "InputTexture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventProcessTextureWithGPU_Parms, InputTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_ShaderName = { "ShaderName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventProcessTextureWithGPU_Parms, ShaderName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShaderName_MetaData), NewProp_ShaderName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventProcessTextureWithGPU_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventProcessTextureWithGPU_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_InputTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_ShaderName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "ProcessTextureWithGPU", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::AuracronMetaHumanTextureSystem_eventProcessTextureWithGPU_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::AuracronMetaHumanTextureSystem_eventProcessTextureWithGPU_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execProcessTextureWithGPU)
{
	P_GET_OBJECT(UTexture2D,Z_Param_InputTexture);
	P_GET_PROPERTY(FStrProperty,Z_Param_ShaderName);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->ProcessTextureWithGPU(Z_Param_InputTexture,Z_Param_ShaderName,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function ProcessTextureWithGPU *************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function Shutdown ************************
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown texture system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown texture system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function Shutdown **************************

// ********** Begin Class UAuracronMetaHumanTextureSystem Function UpdateDynamicMaterialParameter **
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics
{
	struct AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms
	{
		UMaterialInstanceDynamic* MaterialInstance;
		FString ParameterName;
		float Value;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|RealTime" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Update dynamic material parameter\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update dynamic material parameter" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MaterialInstance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_MaterialInstance = { "MaterialInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms, MaterialInstance), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms, Value), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms), &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_MaterialInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "UpdateDynamicMaterialParameter", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::AuracronMetaHumanTextureSystem_eventUpdateDynamicMaterialParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execUpdateDynamicMaterialParameter)
{
	P_GET_OBJECT(UMaterialInstanceDynamic,Z_Param_MaterialInstance);
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateDynamicMaterialParameter(Z_Param_MaterialInstance,Z_Param_ParameterName,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function UpdateDynamicMaterialParameter ****

// ********** Begin Class UAuracronMetaHumanTextureSystem Function UpdateDynamicTextureParameter ***
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics
{
	struct AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms
	{
		UMaterialInstanceDynamic* MaterialInstance;
		FString ParameterName;
		UTexture* Texture;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|RealTime" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Update dynamic texture parameter\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update dynamic texture parameter" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MaterialInstance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Texture;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_MaterialInstance = { "MaterialInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms, MaterialInstance), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms, Texture), Z_Construct_UClass_UTexture_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms), &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_MaterialInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_Texture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "UpdateDynamicTextureParameter", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::AuracronMetaHumanTextureSystem_eventUpdateDynamicTextureParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execUpdateDynamicTextureParameter)
{
	P_GET_OBJECT(UMaterialInstanceDynamic,Z_Param_MaterialInstance);
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_OBJECT(UTexture,Z_Param_Texture);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateDynamicTextureParameter(Z_Param_MaterialInstance,Z_Param_ParameterName,Z_Param_Texture);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function UpdateDynamicTextureParameter *****

// ********** Begin Class UAuracronMetaHumanTextureSystem Function ValidateMetaHumanTexture ********
struct Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics
{
	struct AuracronMetaHumanTextureSystem_eventValidateMetaHumanTexture_Parms
	{
		UTexture2D* Texture;
		EAuracronMetaHumanTextureType ExpectedType;
		FAuracronTextureResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Texture|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate texture for MetaHuman\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate texture for MetaHuman" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Texture;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExpectedType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExpectedType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventValidateMetaHumanTexture_Parms, Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_ExpectedType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_ExpectedType = { "ExpectedType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventValidateMetaHumanTexture_Parms, ExpectedType), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanTextureType, METADATA_PARAMS(0, nullptr) }; // 959042091
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanTextureSystem_eventValidateMetaHumanTexture_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTextureResult, METADATA_PARAMS(0, nullptr) }; // 1415548636
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_Texture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_ExpectedType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_ExpectedType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanTextureSystem, nullptr, "ValidateMetaHumanTexture", Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::AuracronMetaHumanTextureSystem_eventValidateMetaHumanTexture_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::AuracronMetaHumanTextureSystem_eventValidateMetaHumanTexture_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanTextureSystem::execValidateMetaHumanTexture)
{
	P_GET_OBJECT(UTexture2D,Z_Param_Texture);
	P_GET_ENUM(EAuracronMetaHumanTextureType,Z_Param_ExpectedType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTextureResult*)Z_Param__Result=P_THIS->ValidateMetaHumanTexture(Z_Param_Texture,EAuracronMetaHumanTextureType(Z_Param_ExpectedType));
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanTextureSystem Function ValidateMetaHumanTexture **********

// ********** Begin Class UAuracronMetaHumanTextureSystem ******************************************
void UAuracronMetaHumanTextureSystem::StaticRegisterNativesUAuracronMetaHumanTextureSystem()
{
	UClass* Class = UAuracronMetaHumanTextureSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AnalyzeTextureQuality", &UAuracronMetaHumanTextureSystem::execAnalyzeTextureQuality },
		{ "ApplyTextureFilterGPU", &UAuracronMetaHumanTextureSystem::execApplyTextureFilterGPU },
		{ "BlendTexturesGPU", &UAuracronMetaHumanTextureSystem::execBlendTexturesGPU },
		{ "ConvertTextureFormat", &UAuracronMetaHumanTextureSystem::execConvertTextureFormat },
		{ "CreateRenderTargetTexture", &UAuracronMetaHumanTextureSystem::execCreateRenderTargetTexture },
		{ "GenerateSkinTexture", &UAuracronMetaHumanTextureSystem::execGenerateSkinTexture },
		{ "GenerateSkinTextureAsync", &UAuracronMetaHumanTextureSystem::execGenerateSkinTextureAsync },
		{ "GenerateTexture", &UAuracronMetaHumanTextureSystem::execGenerateTexture },
		{ "GenerateTextureAsync", &UAuracronMetaHumanTextureSystem::execGenerateTextureAsync },
		{ "GetAsyncOperationResult", &UAuracronMetaHumanTextureSystem::execGetAsyncOperationResult },
		{ "GetSupportedTextureFormats", &UAuracronMetaHumanTextureSystem::execGetSupportedTextureFormats },
		{ "GetTextureStatistics", &UAuracronMetaHumanTextureSystem::execGetTextureStatistics },
		{ "Initialize", &UAuracronMetaHumanTextureSystem::execInitialize },
		{ "IsAsyncOperationComplete", &UAuracronMetaHumanTextureSystem::execIsAsyncOperationComplete },
		{ "IsInitialized", &UAuracronMetaHumanTextureSystem::execIsInitialized },
		{ "OptimizeTexture", &UAuracronMetaHumanTextureSystem::execOptimizeTexture },
		{ "ProcessTextureWithGPU", &UAuracronMetaHumanTextureSystem::execProcessTextureWithGPU },
		{ "Shutdown", &UAuracronMetaHumanTextureSystem::execShutdown },
		{ "UpdateDynamicMaterialParameter", &UAuracronMetaHumanTextureSystem::execUpdateDynamicMaterialParameter },
		{ "UpdateDynamicTextureParameter", &UAuracronMetaHumanTextureSystem::execUpdateDynamicTextureParameter },
		{ "ValidateMetaHumanTexture", &UAuracronMetaHumanTextureSystem::execValidateMetaHumanTexture },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem;
UClass* UAuracronMetaHumanTextureSystem::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanTextureSystem;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanTextureSystem"),
			Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanTextureSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister()
{
	return UAuracronMetaHumanTextureSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|MetaHuman|Texture" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AURACRON MetaHuman Texture System\n * Advanced texture processing and generation system for UE 5.6\n */" },
#endif
		{ "IncludePath", "Systems/AuracronMetaHumanTextureSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AURACRON MetaHuman Texture System\nAdvanced texture processing and generation system for UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTextureComplete_MetaData[] = {
		{ "Category", "AURACRON MetaHuman|Texture|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when an async texture operation completes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when an async texture operation completes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerFramework_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Internal State ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Internal State ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastErrorMessage_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanTextureSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTextureComplete;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerFramework;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_AnalyzeTextureQuality, "AnalyzeTextureQuality" }, // 2042961051
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ApplyTextureFilterGPU, "ApplyTextureFilterGPU" }, // 848694049
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_BlendTexturesGPU, "BlendTexturesGPU" }, // 2330888969
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ConvertTextureFormat, "ConvertTextureFormat" }, // 3278199438
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_CreateRenderTargetTexture, "CreateRenderTargetTexture" }, // 3296213884
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTexture, "GenerateSkinTexture" }, // 2137465634
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateSkinTextureAsync, "GenerateSkinTextureAsync" }, // 2160573239
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTexture, "GenerateTexture" }, // 4083986273
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GenerateTextureAsync, "GenerateTextureAsync" }, // 1865096724
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetAsyncOperationResult, "GetAsyncOperationResult" }, // 3332938651
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetSupportedTextureFormats, "GetSupportedTextureFormats" }, // 557673984
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_GetTextureStatistics, "GetTextureStatistics" }, // 2778924075
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Initialize, "Initialize" }, // 617202896
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsAsyncOperationComplete, "IsAsyncOperationComplete" }, // 3643935411
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_IsInitialized, "IsInitialized" }, // 1621824975
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_OptimizeTexture, "OptimizeTexture" }, // 2884236396
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ProcessTextureWithGPU, "ProcessTextureWithGPU" }, // 3186866069
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_Shutdown, "Shutdown" }, // 2029639422
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicMaterialParameter, "UpdateDynamicMaterialParameter" }, // 3591864267
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_UpdateDynamicTextureParameter, "UpdateDynamicTextureParameter" }, // 1863237898
		{ &Z_Construct_UFunction_UAuracronMetaHumanTextureSystem_ValidateMetaHumanTexture, "ValidateMetaHumanTexture" }, // 3821966691
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanTextureSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_OnTextureComplete = { "OnTextureComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanTextureSystem, OnTextureComplete), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronTextureComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTextureComplete_MetaData), NewProp_OnTextureComplete_MetaData) }; // 2396510623
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_OwnerFramework = { "OwnerFramework", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanTextureSystem, OwnerFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerFramework_MetaData), NewProp_OwnerFramework_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanTextureSystem*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanTextureSystem), &Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_LastErrorMessage = { "LastErrorMessage", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanTextureSystem, LastErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastErrorMessage_MetaData), NewProp_LastErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_OnTextureComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_OwnerFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::NewProp_LastErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::ClassParams = {
	&UAuracronMetaHumanTextureSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanTextureSystem()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanTextureSystem);
// ********** End Class UAuracronMetaHumanTextureSystem ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronMetaHumanTextureType_StaticEnum, TEXT("EAuracronMetaHumanTextureType"), &Z_Registration_Info_UEnum_EAuracronMetaHumanTextureType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 959042091U) },
		{ EAuracronTextureQuality_StaticEnum, TEXT("EAuracronTextureQuality"), &Z_Registration_Info_UEnum_EAuracronTextureQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4189780467U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronTextureParams::StaticStruct, Z_Construct_UScriptStruct_FAuracronTextureParams_Statics::NewStructOps, TEXT("AuracronTextureParams"), &Z_Registration_Info_UScriptStruct_FAuracronTextureParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTextureParams), 608788009U) },
		{ FAuracronTextureResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronTextureResult_Statics::NewStructOps, TEXT("AuracronTextureResult"), &Z_Registration_Info_UScriptStruct_FAuracronTextureResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTextureResult), 1415548636U) },
		{ FAuracronSkinTextureConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics::NewStructOps, TEXT("AuracronSkinTextureConfig"), &Z_Registration_Info_UScriptStruct_FAuracronSkinTextureConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSkinTextureConfig), 1066872387U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanTextureSystem, UAuracronMetaHumanTextureSystem::StaticClass, TEXT("UAuracronMetaHumanTextureSystem"), &Z_Registration_Info_UClass_UAuracronMetaHumanTextureSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanTextureSystem), 4136289674U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_2410839096(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
