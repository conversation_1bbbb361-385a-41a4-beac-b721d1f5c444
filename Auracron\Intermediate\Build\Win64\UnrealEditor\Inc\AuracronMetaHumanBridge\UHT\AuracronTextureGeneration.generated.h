// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronTextureGeneration.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronTextureGeneration_generated_h
#error "AuracronTextureGeneration.generated.h already included, missing '#pragma once' in AuracronTextureGeneration.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronTextureGeneration_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FNoiseParameters **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h_85_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FNoiseParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FNoiseParameters;
// ********** End ScriptStruct FNoiseParameters ****************************************************

// ********** Begin ScriptStruct FSkinVariationData ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h_115_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSkinVariationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSkinVariationData;
// ********** End ScriptStruct FSkinVariationData **************************************************

// ********** Begin ScriptStruct FTattooData *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h_142_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTattooData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTattooData;
// ********** End ScriptStruct FTattooData *********************************************************

// ********** Begin ScriptStruct FScarData *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h_166_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FScarData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FScarData;
// ********** End ScriptStruct FScarData ***********************************************************

// ********** Begin ScriptStruct FMakeupData *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h_190_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMakeupData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMakeupData;
// ********** End ScriptStruct FMakeupData *********************************************************

// ********** Begin ScriptStruct FAgingEffectData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h_217_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAgingEffectData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAgingEffectData;
// ********** End ScriptStruct FAgingEffectData ****************************************************

// ********** Begin ScriptStruct FTextureGenerationParameters **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h_238_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureGenerationParameters;
// ********** End ScriptStruct FTextureGenerationParameters ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h

// ********** Begin Enum ETextureQuality ***********************************************************
#define FOREACH_ENUM_ETEXTUREQUALITY(op) \
	op(ETextureQuality::Low) \
	op(ETextureQuality::Medium) \
	op(ETextureQuality::High) \
	op(ETextureQuality::Ultra) \
	op(ETextureQuality::Custom) 

enum class ETextureQuality : uint8;
template<> struct TIsUEnumClass<ETextureQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ETextureQuality>();
// ********** End Enum ETextureQuality *************************************************************

// ********** Begin Enum EAuracronTextureType ******************************************************
#define FOREACH_ENUM_EAURACRONTEXTURETYPE(op) \
	op(EAuracronTextureType::Diffuse) \
	op(EAuracronTextureType::Normal) \
	op(EAuracronTextureType::Roughness) \
	op(EAuracronTextureType::Metallic) \
	op(EAuracronTextureType::Specular) \
	op(EAuracronTextureType::Emissive) \
	op(EAuracronTextureType::Opacity) \
	op(EAuracronTextureType::Height) \
	op(EAuracronTextureType::AmbientOcclusion) \
	op(EAuracronTextureType::Subsurface) 

enum class EAuracronTextureType : uint8;
template<> struct TIsUEnumClass<EAuracronTextureType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAuracronTextureType>();
// ********** End Enum EAuracronTextureType ********************************************************

// ********** Begin Enum ENoiseType ****************************************************************
#define FOREACH_ENUM_ENOISETYPE(op) \
	op(ENoiseType::Perlin) \
	op(ENoiseType::Simplex) \
	op(ENoiseType::Worley) \
	op(ENoiseType::FBM) \
	op(ENoiseType::Ridged) \
	op(ENoiseType::Billow) 

enum class ENoiseType : uint8;
template<> struct TIsUEnumClass<ENoiseType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ENoiseType>();
// ********** End Enum ENoiseType ******************************************************************

// ********** Begin Enum ETextureBlendMode *********************************************************
#define FOREACH_ENUM_ETEXTUREBLENDMODE(op) \
	op(ETextureBlendMode::Normal) \
	op(ETextureBlendMode::Multiply) \
	op(ETextureBlendMode::Screen) \
	op(ETextureBlendMode::Overlay) \
	op(ETextureBlendMode::SoftLight) \
	op(ETextureBlendMode::HardLight) \
	op(ETextureBlendMode::ColorDodge) \
	op(ETextureBlendMode::ColorBurn) \
	op(ETextureBlendMode::Darken) \
	op(ETextureBlendMode::Lighten) \
	op(ETextureBlendMode::Difference) \
	op(ETextureBlendMode::Exclusion) 

enum class ETextureBlendMode : uint8;
template<> struct TIsUEnumClass<ETextureBlendMode> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ETextureBlendMode>();
// ********** End Enum ETextureBlendMode ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
