// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Física Chaos Bridge Implementation

#include "AuracronPhysicsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Chaos/ChaosSolverActor.h"
#include "FieldSystem/FieldSystemComponent.h"
#include "FieldSystem/FieldSystemActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"

UAuracronPhysicsBridge::UAuracronPhysicsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.033f; // 30 FPS para física responsiva
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão de física Chaos
    ChaosPhysicsConfiguration.bUsePhysicsSimulation = true;
    ChaosPhysicsConfiguration.CustomGravity = FVector(0.0f, 0.0f, -980.0f);
    ChaosPhysicsConfiguration.bUseCustomGravity = false;
    ChaosPhysicsConfiguration.AirDensity = 1.225f;
    ChaosPhysicsConfiguration.AirResistance = 0.1f;
    ChaosPhysicsConfiguration.bUseSubStepping = true;
    ChaosPhysicsConfiguration.SubSteps = 4;
    ChaosPhysicsConfiguration.MaxDeltaTime = 0.033f;
    ChaosPhysicsConfiguration.bUseCCD = true;
    ChaosPhysicsConfiguration.CCDThreshold = 1.0f;
    ChaosPhysicsConfiguration.bUseAsyncPhysics = true;
    ChaosPhysicsConfiguration.PhysicsThreads = 4;
    ChaosPhysicsConfiguration.bUseDeterministicPhysics = true;
    ChaosPhysicsConfiguration.SolverIterations = 8;
    ChaosPhysicsConfiguration.CollisionIterations = 4;
    
    // Configurações padrão de destruição
    DefaultDestructionConfiguration.DestructionType = EAuracronDestructionType::Fracture;
    DefaultDestructionConfiguration.DestructionForce = 1000.0f;
    DefaultDestructionConfiguration.DestructionRadius = 500.0f;
    DefaultDestructionConfiguration.DamageThreshold = 100.0f;
    DefaultDestructionConfiguration.MaxFragments = 100;
    DefaultDestructionConfiguration.MinFragmentSize = 10.0f;
    DefaultDestructionConfiguration.bUseDebris = true;
    DefaultDestructionConfiguration.FragmentLifetime = 10.0f;
    DefaultDestructionConfiguration.bUseFadeOut = true;
    DefaultDestructionConfiguration.FadeOutTime = 3.0f;
    DefaultDestructionConfiguration.bUseDestructionSound = true;
    DefaultDestructionConfiguration.bUseParticleEffects = true;
    
    // Configurações padrão de Field System
    DefaultFieldSystemConfiguration.FieldType = TEXT("RadialForce");
    DefaultFieldSystemConfiguration.FieldForce = 1000.0f;
    DefaultFieldSystemConfiguration.FieldRadius = 500.0f;
    DefaultFieldSystemConfiguration.FieldDuration = 2.0f;
    DefaultFieldSystemConfiguration.bUseFalloff = true;
    DefaultFieldSystemConfiguration.FalloffType = TEXT("Linear");
    DefaultFieldSystemConfiguration.bAffectDestructibleOnly = false;
    DefaultFieldSystemConfiguration.bAffectCharacterPhysics = true;
    DefaultFieldSystemConfiguration.CharacterForceMultiplier = 0.5f;
    DefaultFieldSystemConfiguration.bUseCustomDirection = false;
    DefaultFieldSystemConfiguration.CustomDirection = FVector::UpVector;
    DefaultFieldSystemConfiguration.bUseNoise = false;
    DefaultFieldSystemConfiguration.NoiseIntensity = 0.1f;
    DefaultFieldSystemConfiguration.NoiseFrequency = 1.0f;
}

void UAuracronPhysicsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Física Chaos"));

    // Inicializar sistema
    bSystemInitialized = InitializePhysicsSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers
        GetWorld()->GetTimerManager().SetTimer(
            CleanupTimer,
            [this]()
            {
                CleanupInactivePhysicsObjects();
            },
            10.0f, // A cada 10 segundos
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimer,
            [this]()
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    if (APawn* Pawn = PC->GetPawn())
                    {
                        OptimizePhysicsByDistance(Pawn->GetActorLocation());
                    }
                }
            },
            2.0f, // A cada 2 segundos
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Física Chaos inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Física Chaos"));
    }
}

void UAuracronPhysicsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar objetos físicos ativos
    for (AActor* Actor : ActivePhysicsObjects)
    {
        if (IsValid(Actor))
        {
            // Desabilitar física
            if (UPrimitiveComponent* PrimComp = Actor->FindComponentByClass<UPrimitiveComponent>())
            {
                PrimComp->SetSimulatePhysics(false);
            }
        }
    }
    ActivePhysicsObjects.Empty();
    
    // Limpar Field Components
    for (UFieldSystemComponent* Component : ActiveFieldComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveFieldComponents.Empty();

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(CleanupTimer);
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronPhysicsBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronPhysicsBridge, ChaosPhysicsConfiguration);
}

void UAuracronPhysicsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar física ativa
    ProcessActivePhysics(DeltaTime);
}

// === Core Physics Management ===

bool UAuracronPhysicsBridge::ApplyForceToObject(AActor* TargetActor, const FVector& Force, const FVector& Location)
{
    if (!bSystemInitialized || !TargetActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou ator inválido"));
        return false;
    }

    UPrimitiveComponent* PrimComp = TargetActor->FindComponentByClass<UPrimitiveComponent>();
    if (!PrimComp)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ator não possui PrimitiveComponent"));
        return false;
    }

    // Aplicar força
    if (Location.IsZero())
    {
        PrimComp->AddForce(Force);
    }
    else
    {
        PrimComp->AddForceAtLocation(Force, Location);
    }

    // Adicionar à lista de objetos ativos se não estiver
    if (!ActivePhysicsObjects.Contains(TargetActor))
    {
        ActivePhysicsObjects.Add(TargetActor);
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Força aplicada a %s: %s"), *TargetActor->GetName(), *Force.ToString());

    return true;
}

bool UAuracronPhysicsBridge::ApplyImpulseToObject(AActor* TargetActor, const FVector& Impulse, const FVector& Location)
{
    if (!bSystemInitialized || !TargetActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou ator inválido"));
        return false;
    }

    UPrimitiveComponent* PrimComp = TargetActor->FindComponentByClass<UPrimitiveComponent>();
    if (!PrimComp)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ator não possui PrimitiveComponent"));
        return false;
    }

    // Aplicar impulso
    if (Location.IsZero())
    {
        PrimComp->AddImpulse(Impulse);
    }
    else
    {
        PrimComp->AddImpulseAtLocation(Impulse, Location);
    }

    // Adicionar à lista de objetos ativos
    if (!ActivePhysicsObjects.Contains(TargetActor))
    {
        ActivePhysicsObjects.Add(TargetActor);
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Impulso aplicado a %s: %s"), *TargetActor->GetName(), *Impulse.ToString());

    return true;
}

bool UAuracronPhysicsBridge::SetCustomGravity(const FVector& NewGravity)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    ChaosPhysicsConfiguration.CustomGravity = NewGravity;
    ChaosPhysicsConfiguration.bUseCustomGravity = true;

    // Aplicar gravidade ao mundo
    if (UWorld* World = GetWorld())
    {
        if (AWorldSettings* WorldSettings = World->GetWorldSettings())
        {
            WorldSettings->GlobalGravityZ = NewGravity.Z;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gravidade customizada definida: %s"), *NewGravity.ToString());

    return true;
}

bool UAuracronPhysicsBridge::ApplyCustomGravityToObject(AActor* TargetActor, const FVector& Gravity)
{
    if (!bSystemInitialized || !TargetActor)
    {
        return false;
    }

    UPrimitiveComponent* PrimComp = TargetActor->FindComponentByClass<UPrimitiveComponent>();
    if (!PrimComp)
    {
        return false;
    }

    // Aplicar gravidade customizada como força contínua
    FVector GravityForce = Gravity * PrimComp->GetMass();
    PrimComp->AddForce(GravityForce);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Gravidade customizada aplicada a %s: %s"), *TargetActor->GetName(), *Gravity.ToString());

    return true;
}

// === Destruction System ===

bool UAuracronPhysicsBridge::DestroyObject(AActor* TargetActor, const FAuracronDestructionConfiguration& DestructionConfig)
{
    if (!bSystemInitialized || !TargetActor)
    {
        return false;
    }

    if (!ValidateDestructionConfiguration(DestructionConfig))
    {
        return false;
    }

    // Converter para Geometry Collection se necessário
    AGeometryCollectionActor* GeomCollectionActor = ConvertToGeometryCollection(TargetActor);
    if (!GeomCollectionActor)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao converter para Geometry Collection"));
        return false;
    }

    // Aplicar destruição
    UGeometryCollectionComponent* GeomComponent = GeomCollectionActor->GetGeometryCollectionComponent();
    if (GeomComponent)
    {
        // Aplicar força de destruição
        FVector DestructionLocation = TargetActor->GetActorLocation();
        GeomComponent->ApplyPhysicsField(true, EFieldPhysicsType::Field_LinearForce, nullptr, nullptr,
            DestructionConfig.DestructionForce, DestructionLocation);

        // Configurar fragmentos
        GeomComponent->SetNotifyBreaks(true);

        // Reproduzir som se configurado
        if (DestructionConfig.bUseDestructionSound && DestructionConfig.DestructionSound.IsValid())
        {
            USoundBase* Sound = DestructionConfig.DestructionSound.LoadSynchronous();
            if (Sound)
            {
                UGameplayStatics::PlaySoundAtLocation(GetWorld(), Sound, DestructionLocation);
            }
        }

        // Spawnar partículas se configurado
        if (DestructionConfig.bUseParticleEffects && DestructionConfig.DestructionParticles.IsValid())
        {
            UNiagaraSystem* ParticleSystem = DestructionConfig.DestructionParticles.LoadSynchronous();
            if (ParticleSystem)
            {
                UNiagaraFunctionLibrary::SpawnSystemAtLocation(GetWorld(), ParticleSystem, DestructionLocation);
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Objeto destruído: %s"), *TargetActor->GetName());

        // Broadcast evento
        OnObjectDestroyed.Broadcast(TargetActor, DestructionConfig);

        return true;
    }

    return false;
}

bool UAuracronPhysicsBridge::CreateExplosion(const FVector& Location, const FAuracronDestructionConfiguration& DestructionConfig)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Criar Field System para explosão
    if (AFieldSystemActor* FieldActor = GetWorld()->SpawnActor<AFieldSystemActor>())
    {
        UFieldSystemComponent* FieldComponent = FieldActor->GetFieldSystemComponent();
        if (FieldComponent)
        {
            // Configurar campo de força radial
            // Implementação específica do Field System seria aqui

            ActiveFieldComponents.Add(FieldComponent);

            // Configurar timer para destruir o field após a duração
            GetWorld()->GetTimerManager().SetTimer(
                FTimerHandle(),
                [FieldActor]()
                {
                    if (IsValid(FieldActor))
                    {
                        FieldActor->Destroy();
                    }
                },
                5.0f, // Duração da explosão
                false
            );

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Explosão criada em: %s"), *Location.ToString());

            return true;
        }
    }

    return false;
}

// === Internal Methods ===

bool UAuracronPhysicsBridge::InitializePhysicsSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Configurar Chaos Solver
    if (!SetupChaosSolver())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar Chaos Solver"));
        return false;
    }

    // Configurar Field System
    if (!SetupFieldSystem())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar Field System"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de física inicializado"));

    return true;
}

bool UAuracronPhysicsBridge::SetupChaosSolver()
{
    // Spawnar Chaos Solver se não existir
    if (!ChaosSolver)
    {
        ChaosSolver = GetWorld()->SpawnActor<AChaosSolverActor>();
        if (!ChaosSolver)
        {
            return false;
        }
    }

    // Configurar solver
    if (UChaosSolverComponent* SolverComponent = ChaosSolver->GetChaosSolverComponent())
    {
        // Aplicar configurações
        SolverComponent->SetSolverIterations(ChaosPhysicsConfiguration.SolverIterations);
        SolverComponent->SetCollisionIterations(ChaosPhysicsConfiguration.CollisionIterations);
    }

    return true;
}

bool UAuracronPhysicsBridge::SetupFieldSystem()
{
    // Field System será configurado conforme necessário
    return true;
}

void UAuracronPhysicsBridge::ProcessActivePhysics(float DeltaTime)
{
    FScopeLock Lock(&PhysicsMutex);

    // Remover objetos inválidos da lista
    ActivePhysicsObjects.RemoveAll([](const TObjectPtr<AActor>& Actor)
    {
        return !IsValid(Actor);
    });

    // Processar objetos físicos ativos
    for (AActor* Actor : ActivePhysicsObjects)
    {
        if (!IsValid(Actor))
            continue;

        UPrimitiveComponent* PrimComp = Actor->FindComponentByClass<UPrimitiveComponent>();
        if (!PrimComp || !PrimComp->IsSimulatingPhysics())
            continue;

        // Aplicar gravidade customizada se configurada
        if (ChaosPhysicsConfiguration.bUseCustomGravity)
        {
            ApplyCustomGravityToObject(Actor, ChaosPhysicsConfiguration.CustomGravity);
        }

        // Aplicar resistência do ar
        if (ChaosPhysicsConfiguration.AirResistance > 0.0f)
        {
            FVector Velocity = PrimComp->GetPhysicsLinearVelocity();
            FVector AirResistanceForce = -Velocity * ChaosPhysicsConfiguration.AirResistance * ChaosPhysicsConfiguration.AirDensity;
            PrimComp->AddForce(AirResistanceForce);
        }
    }
}

bool UAuracronPhysicsBridge::ValidateDestructionConfiguration(const FAuracronDestructionConfiguration& Config) const
{
    if (Config.DestructionForce <= 0.0f || Config.DestructionRadius <= 0.0f)
    {
        return false;
    }

    if (Config.MaxFragments <= 0 || Config.MinFragmentSize <= 0.0f)
    {
        return false;
    }

    return true;
}

bool UAuracronPhysicsBridge::ApplyTorqueToObject(AActor* TargetActor, const FVector& Torque)
{
    if (!bSystemInitialized || !TargetActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou ator inválido"));
        return false;
    }

    UPrimitiveComponent* PrimComp = TargetActor->FindComponentByClass<UPrimitiveComponent>();
    if (!PrimComp)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ator não possui PrimitiveComponent"));
        return false;
    }

    // Aplicar torque
    PrimComp->AddTorqueInRadians(Torque);

    // Adicionar à lista de objetos ativos
    if (!ActivePhysicsObjects.Contains(TargetActor))
    {
        ActivePhysicsObjects.Add(TargetActor);
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Torque aplicado a %s: %s"), *TargetActor->GetName(), *Torque.ToString());

    return true;
}
