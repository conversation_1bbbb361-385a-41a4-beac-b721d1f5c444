// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionLandscape.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionLandscape() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronHeightmapData();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLandscapeStatistics();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
LANDSCAPE_API UClass* Z_Construct_UClass_ULandscapeSubsystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronLandscapeStreamingState ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState;
static UEnum* EAuracronLandscapeStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLandscapeStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLandscapeStreamingState>()
{
	return EAuracronLandscapeStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape streaming states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronLandscapeStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronLandscapeStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronLandscapeStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape streaming states" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronLandscapeStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronLandscapeStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLandscapeStreamingState::Unloaded", (int64)EAuracronLandscapeStreamingState::Unloaded },
		{ "EAuracronLandscapeStreamingState::Loading", (int64)EAuracronLandscapeStreamingState::Loading },
		{ "EAuracronLandscapeStreamingState::Loaded", (int64)EAuracronLandscapeStreamingState::Loaded },
		{ "EAuracronLandscapeStreamingState::Unloading", (int64)EAuracronLandscapeStreamingState::Unloading },
		{ "EAuracronLandscapeStreamingState::Failed", (int64)EAuracronLandscapeStreamingState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLandscapeStreamingState",
	"EAuracronLandscapeStreamingState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronLandscapeStreamingState ********************************************

// ********** Begin Enum EAuracronLandscapeLODState ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLandscapeLODState;
static UEnum* EAuracronLandscapeLODState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLandscapeLODState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLandscapeLODState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLandscapeLODState"));
	}
	return Z_Registration_Info_UEnum_EAuracronLandscapeLODState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLandscapeLODState>()
{
	return EAuracronLandscapeLODState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape LOD states\n" },
#endif
		{ "LOD0.DisplayName", "LOD 0 (Highest)" },
		{ "LOD0.Name", "EAuracronLandscapeLODState::LOD0" },
		{ "LOD1.DisplayName", "LOD 1" },
		{ "LOD1.Name", "EAuracronLandscapeLODState::LOD1" },
		{ "LOD2.DisplayName", "LOD 2" },
		{ "LOD2.Name", "EAuracronLandscapeLODState::LOD2" },
		{ "LOD3.DisplayName", "LOD 3" },
		{ "LOD3.Name", "EAuracronLandscapeLODState::LOD3" },
		{ "LOD4.DisplayName", "LOD 4" },
		{ "LOD4.Name", "EAuracronLandscapeLODState::LOD4" },
		{ "LOD5.DisplayName", "LOD 5" },
		{ "LOD5.Name", "EAuracronLandscapeLODState::LOD5" },
		{ "LOD6.DisplayName", "LOD 6" },
		{ "LOD6.Name", "EAuracronLandscapeLODState::LOD6" },
		{ "LOD7.DisplayName", "LOD 7 (Lowest)" },
		{ "LOD7.Name", "EAuracronLandscapeLODState::LOD7" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape LOD states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLandscapeLODState::LOD0", (int64)EAuracronLandscapeLODState::LOD0 },
		{ "EAuracronLandscapeLODState::LOD1", (int64)EAuracronLandscapeLODState::LOD1 },
		{ "EAuracronLandscapeLODState::LOD2", (int64)EAuracronLandscapeLODState::LOD2 },
		{ "EAuracronLandscapeLODState::LOD3", (int64)EAuracronLandscapeLODState::LOD3 },
		{ "EAuracronLandscapeLODState::LOD4", (int64)EAuracronLandscapeLODState::LOD4 },
		{ "EAuracronLandscapeLODState::LOD5", (int64)EAuracronLandscapeLODState::LOD5 },
		{ "EAuracronLandscapeLODState::LOD6", (int64)EAuracronLandscapeLODState::LOD6 },
		{ "EAuracronLandscapeLODState::LOD7", (int64)EAuracronLandscapeLODState::LOD7 },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLandscapeLODState",
	"EAuracronLandscapeLODState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState()
{
	if (!Z_Registration_Info_UEnum_EAuracronLandscapeLODState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLandscapeLODState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLandscapeLODState.InnerSingleton;
}
// ********** End Enum EAuracronLandscapeLODState **************************************************

// ********** Begin Enum EAuracronHeightmapQuality *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronHeightmapQuality;
static UEnum* EAuracronHeightmapQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronHeightmapQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronHeightmapQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronHeightmapQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronHeightmapQuality.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronHeightmapQuality>()
{
	return EAuracronHeightmapQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Heightmap quality levels\n" },
#endif
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronHeightmapQuality::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronHeightmapQuality::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronHeightmapQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Heightmap quality levels" },
#endif
		{ "Ultra.DisplayName", "Ultra" },
		{ "Ultra.Name", "EAuracronHeightmapQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronHeightmapQuality::Low", (int64)EAuracronHeightmapQuality::Low },
		{ "EAuracronHeightmapQuality::Medium", (int64)EAuracronHeightmapQuality::Medium },
		{ "EAuracronHeightmapQuality::High", (int64)EAuracronHeightmapQuality::High },
		{ "EAuracronHeightmapQuality::Ultra", (int64)EAuracronHeightmapQuality::Ultra },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronHeightmapQuality",
	"EAuracronHeightmapQuality",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronHeightmapQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronHeightmapQuality.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronHeightmapQuality.InnerSingleton;
}
// ********** End Enum EAuracronHeightmapQuality ***************************************************

// ********** Begin Enum EAuracronMaterialStreamingType ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronMaterialStreamingType;
static UEnum* EAuracronMaterialStreamingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronMaterialStreamingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronMaterialStreamingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronMaterialStreamingType"));
	}
	return Z_Registration_Info_UEnum_EAuracronMaterialStreamingType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronMaterialStreamingType>()
{
	return EAuracronMaterialStreamingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronMaterialStreamingType::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material streaming types\n" },
#endif
		{ "Dynamic.DisplayName", "Dynamic" },
		{ "Dynamic.Name", "EAuracronMaterialStreamingType::Dynamic" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
		{ "OnDemand.DisplayName", "On Demand" },
		{ "OnDemand.Name", "EAuracronMaterialStreamingType::OnDemand" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronMaterialStreamingType::Static" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material streaming types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronMaterialStreamingType::Static", (int64)EAuracronMaterialStreamingType::Static },
		{ "EAuracronMaterialStreamingType::Dynamic", (int64)EAuracronMaterialStreamingType::Dynamic },
		{ "EAuracronMaterialStreamingType::Adaptive", (int64)EAuracronMaterialStreamingType::Adaptive },
		{ "EAuracronMaterialStreamingType::OnDemand", (int64)EAuracronMaterialStreamingType::OnDemand },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronMaterialStreamingType",
	"EAuracronMaterialStreamingType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType()
{
	if (!Z_Registration_Info_UEnum_EAuracronMaterialStreamingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronMaterialStreamingType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronMaterialStreamingType.InnerSingleton;
}
// ********** End Enum EAuracronMaterialStreamingType **********************************************

// ********** Begin ScriptStruct FAuracronLandscapeConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration;
class UScriptStruct* FAuracronLandscapeConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLandscapeConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Landscape Configuration\n * Configuration settings for landscape integration with world partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape Configuration\nConfiguration settings for landscape integration with world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLandscapeStreaming_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHeightmapStreaming_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMaterialStreaming_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLandscapeLOD_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeStreamingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeUnloadingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentLandscapeOperations_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultHeightmapQuality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightmapResolution_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentSize_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistanceMultiplier_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLODDistance_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevel_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialStreamingType_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialStreamingDistance_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLandscapeMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLandscapeCaching_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLandscapeDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogLandscapeOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableLandscapeStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLandscapeStreaming;
	static void NewProp_bEnableHeightmapStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHeightmapStreaming;
	static void NewProp_bEnableMaterialStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMaterialStreaming;
	static void NewProp_bEnableLandscapeLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLandscapeLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LandscapeStreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LandscapeUnloadingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentLandscapeOperations;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultHeightmapQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultHeightmapQuality;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HeightmapResolution;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComponentSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistanceMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseLODDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MaterialStreamingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MaterialStreamingType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaterialStreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxLandscapeMemoryUsageMB;
	static void NewProp_bEnableLandscapeCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLandscapeCaching;
	static void NewProp_bEnableLandscapeDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLandscapeDebug;
	static void NewProp_bLogLandscapeOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogLandscapeOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLandscapeConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeStreaming_SetBit(void* Obj)
{
	((FAuracronLandscapeConfiguration*)Obj)->bEnableLandscapeStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeStreaming = { "bEnableLandscapeStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeConfiguration), &Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLandscapeStreaming_MetaData), NewProp_bEnableLandscapeStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableHeightmapStreaming_SetBit(void* Obj)
{
	((FAuracronLandscapeConfiguration*)Obj)->bEnableHeightmapStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableHeightmapStreaming = { "bEnableHeightmapStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeConfiguration), &Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableHeightmapStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHeightmapStreaming_MetaData), NewProp_bEnableHeightmapStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableMaterialStreaming_SetBit(void* Obj)
{
	((FAuracronLandscapeConfiguration*)Obj)->bEnableMaterialStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableMaterialStreaming = { "bEnableMaterialStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeConfiguration), &Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableMaterialStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMaterialStreaming_MetaData), NewProp_bEnableMaterialStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeLOD_SetBit(void* Obj)
{
	((FAuracronLandscapeConfiguration*)Obj)->bEnableLandscapeLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeLOD = { "bEnableLandscapeLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeConfiguration), &Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLandscapeLOD_MetaData), NewProp_bEnableLandscapeLOD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_LandscapeStreamingDistance = { "LandscapeStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, LandscapeStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeStreamingDistance_MetaData), NewProp_LandscapeStreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_LandscapeUnloadingDistance = { "LandscapeUnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, LandscapeUnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeUnloadingDistance_MetaData), NewProp_LandscapeUnloadingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaxConcurrentLandscapeOperations = { "MaxConcurrentLandscapeOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, MaxConcurrentLandscapeOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentLandscapeOperations_MetaData), NewProp_MaxConcurrentLandscapeOperations_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_DefaultHeightmapQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_DefaultHeightmapQuality = { "DefaultHeightmapQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, DefaultHeightmapQuality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultHeightmapQuality_MetaData), NewProp_DefaultHeightmapQuality_MetaData) }; // 2474363911
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_HeightmapResolution = { "HeightmapResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, HeightmapResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightmapResolution_MetaData), NewProp_HeightmapResolution_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_ComponentSize = { "ComponentSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, ComponentSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentSize_MetaData), NewProp_ComponentSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_LODDistanceMultiplier = { "LODDistanceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, LODDistanceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistanceMultiplier_MetaData), NewProp_LODDistanceMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_BaseLODDistance = { "BaseLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, BaseLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLODDistance_MetaData), NewProp_BaseLODDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaxLODLevel = { "MaxLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, MaxLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevel_MetaData), NewProp_MaxLODLevel_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaterialStreamingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaterialStreamingType = { "MaterialStreamingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, MaterialStreamingType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMaterialStreamingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialStreamingType_MetaData), NewProp_MaterialStreamingType_MetaData) }; // 3084897209
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaterialStreamingDistance = { "MaterialStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, MaterialStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialStreamingDistance_MetaData), NewProp_MaterialStreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaxLandscapeMemoryUsageMB = { "MaxLandscapeMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeConfiguration, MaxLandscapeMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLandscapeMemoryUsageMB_MetaData), NewProp_MaxLandscapeMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeCaching_SetBit(void* Obj)
{
	((FAuracronLandscapeConfiguration*)Obj)->bEnableLandscapeCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeCaching = { "bEnableLandscapeCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeConfiguration), &Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLandscapeCaching_MetaData), NewProp_bEnableLandscapeCaching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeDebug_SetBit(void* Obj)
{
	((FAuracronLandscapeConfiguration*)Obj)->bEnableLandscapeDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeDebug = { "bEnableLandscapeDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeConfiguration), &Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLandscapeDebug_MetaData), NewProp_bEnableLandscapeDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bLogLandscapeOperations_SetBit(void* Obj)
{
	((FAuracronLandscapeConfiguration*)Obj)->bLogLandscapeOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bLogLandscapeOperations = { "bLogLandscapeOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeConfiguration), &Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bLogLandscapeOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogLandscapeOperations_MetaData), NewProp_bLogLandscapeOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableHeightmapStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableMaterialStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_LandscapeStreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_LandscapeUnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaxConcurrentLandscapeOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_DefaultHeightmapQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_DefaultHeightmapQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_HeightmapResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_ComponentSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_LODDistanceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_BaseLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaxLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaterialStreamingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaterialStreamingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaterialStreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_MaxLandscapeMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bEnableLandscapeDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewProp_bLogLandscapeOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLandscapeConfiguration",
	Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::PropPointers),
	sizeof(FAuracronLandscapeConfiguration),
	alignof(FAuracronLandscapeConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLandscapeConfiguration *************************************

// ********** Begin ScriptStruct FAuracronLandscapeDescriptor **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor;
class UScriptStruct* FAuracronLandscapeDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLandscapeDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Landscape Descriptor\n * Descriptor for landscape components and their properties\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape Descriptor\nDescriptor for landscape components and their properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeName_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Bounds_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLODState_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentCountX_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentCountY_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightmapResolution_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightmapQuality_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialLayers_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsStreaming_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCollision_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Landscape Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Bounds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentLODState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentLODState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComponentCountX;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComponentCountY;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HeightmapResolution;
	static const UECodeGen_Private::FBytePropertyParams NewProp_HeightmapQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_HeightmapQuality;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialLayers;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_bIsStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsStreaming;
	static void NewProp_bHasCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCollision;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLandscapeDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_LandscapeName = { "LandscapeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, LandscapeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeName_MetaData), NewProp_LandscapeName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Bounds = { "Bounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, Bounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Bounds_MetaData), NewProp_Bounds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 786100275
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CurrentLODState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CurrentLODState = { "CurrentLODState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, CurrentLODState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLODState_MetaData), NewProp_CurrentLODState_MetaData) }; // 460746793
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_ComponentCountX = { "ComponentCountX", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, ComponentCountX), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentCountX_MetaData), NewProp_ComponentCountX_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_ComponentCountY = { "ComponentCountY", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, ComponentCountY), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentCountY_MetaData), NewProp_ComponentCountY_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_HeightmapResolution = { "HeightmapResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, HeightmapResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightmapResolution_MetaData), NewProp_HeightmapResolution_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_HeightmapQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_HeightmapQuality = { "HeightmapQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, HeightmapQuality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightmapQuality_MetaData), NewProp_HeightmapQuality_MetaData) }; // 2474363911
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_MaterialLayers_Inner = { "MaterialLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_MaterialLayers = { "MaterialLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, MaterialLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialLayers_MetaData), NewProp_MaterialLayers_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bIsStreaming_SetBit(void* Obj)
{
	((FAuracronLandscapeDescriptor*)Obj)->bIsStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bIsStreaming = { "bIsStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeDescriptor), &Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bIsStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsStreaming_MetaData), NewProp_bIsStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bHasCollision_SetBit(void* Obj)
{
	((FAuracronLandscapeDescriptor*)Obj)->bHasCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bHasCollision = { "bHasCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLandscapeDescriptor), &Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bHasCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCollision_MetaData), NewProp_bHasCollision_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeDescriptor, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_LandscapeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_Bounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CurrentLODState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CurrentLODState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_ComponentCountX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_ComponentCountY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_HeightmapResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_HeightmapQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_HeightmapQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_MaterialLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_MaterialLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bIsStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_bHasCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewProp_LastAccessTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLandscapeDescriptor",
	Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::PropPointers),
	sizeof(FAuracronLandscapeDescriptor),
	alignof(FAuracronLandscapeDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLandscapeDescriptor ****************************************

// ********** Begin ScriptStruct FAuracronHeightmapData ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronHeightmapData;
class UScriptStruct* FAuracronHeightmapData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronHeightmapData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronHeightmapData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronHeightmapData, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronHeightmapData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronHeightmapData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Heightmap Data\n * Data structure for heightmap information and streaming\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Heightmap Data\nData structure for heightmap information and streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightmapId_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinHeight_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHeight_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompressed_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionRatio_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastModified_MetaData[] = {
		{ "Category", "Heightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_HeightmapId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Height;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHeight;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static void NewProp_bIsCompressed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompressed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompressionRatio;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastModified;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronHeightmapData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_HeightmapId = { "HeightmapId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, HeightmapId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightmapId_MetaData), NewProp_HeightmapId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_MinHeight = { "MinHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, MinHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinHeight_MetaData), NewProp_MinHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_MaxHeight = { "MaxHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, MaxHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHeight_MetaData), NewProp_MaxHeight_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, Quality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronHeightmapQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 2474363911
void Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_bIsCompressed_SetBit(void* Obj)
{
	((FAuracronHeightmapData*)Obj)->bIsCompressed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_bIsCompressed = { "bIsCompressed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHeightmapData), &Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_bIsCompressed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompressed_MetaData), NewProp_bIsCompressed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_CompressionRatio = { "CompressionRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, CompressionRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionRatio_MetaData), NewProp_CompressionRatio_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_LastModified = { "LastModified", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightmapData, LastModified), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastModified_MetaData), NewProp_LastModified_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_HeightmapId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_MinHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_MaxHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_bIsCompressed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_CompressionRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewProp_LastModified,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronHeightmapData",
	Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::PropPointers),
	sizeof(FAuracronHeightmapData),
	alignof(FAuracronHeightmapData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronHeightmapData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronHeightmapData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronHeightmapData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronHeightmapData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronHeightmapData **********************************************

// ********** Begin ScriptStruct FAuracronLandscapeStatistics **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics;
class UScriptStruct* FAuracronLandscapeStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLandscapeStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLandscapeStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Landscape Statistics\n * Performance and usage statistics for landscape system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape Statistics\nPerformance and usage statistics for landscape system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLandscapes_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedLandscapes_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingLandscapes_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightmapOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODTransitions_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalLandscapes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedLandscapes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingLandscapes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HeightmapOperations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaterialOperations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODTransitions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LandscapeEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLandscapeStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_TotalLandscapes = { "TotalLandscapes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, TotalLandscapes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLandscapes_MetaData), NewProp_TotalLandscapes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LoadedLandscapes = { "LoadedLandscapes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, LoadedLandscapes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedLandscapes_MetaData), NewProp_LoadedLandscapes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_StreamingLandscapes = { "StreamingLandscapes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, StreamingLandscapes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingLandscapes_MetaData), NewProp_StreamingLandscapes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_AverageLoadingTime = { "AverageLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, AverageLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadingTime_MetaData), NewProp_AverageLoadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_HeightmapOperations = { "HeightmapOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, HeightmapOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightmapOperations_MetaData), NewProp_HeightmapOperations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_MaterialOperations = { "MaterialOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, MaterialOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialOperations_MetaData), NewProp_MaterialOperations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LODTransitions = { "LODTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, LODTransitions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODTransitions_MetaData), NewProp_LODTransitions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_FailedOperations = { "FailedOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, FailedOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedOperations_MetaData), NewProp_FailedOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LandscapeEfficiency = { "LandscapeEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, LandscapeEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeEfficiency_MetaData), NewProp_LandscapeEfficiency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLandscapeStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_TotalLandscapes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LoadedLandscapes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_StreamingLandscapes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_AverageLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_HeightmapOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_MaterialOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LODTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_FailedOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LandscapeEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLandscapeStatistics",
	Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::PropPointers),
	sizeof(FAuracronLandscapeStatistics),
	alignof(FAuracronLandscapeStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLandscapeStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLandscapeStatistics ****************************************

// ********** Begin Delegate FOnLandscapeLoaded ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms
	{
		FString LandscapeId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms, LandscapeId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "OnLandscapeLoaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLandscapeManager::FOnLandscapeLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnLandscapeLoaded, const FString& LandscapeId, bool bSuccess)
{
	struct AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms
	{
		FString LandscapeId;
		bool bSuccess;
	};
	AuracronWorldPartitionLandscapeManager_eventOnLandscapeLoaded_Parms Parms;
	Parms.LandscapeId=LandscapeId;
	Parms.bSuccess=bSuccess ? true : false;
	OnLandscapeLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLandscapeLoaded ******************************************************

// ********** Begin Delegate FOnLandscapeUnloaded **************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventOnLandscapeUnloaded_Parms
	{
		FString LandscapeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventOnLandscapeUnloaded_Parms, LandscapeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::NewProp_LandscapeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "OnLandscapeUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnLandscapeUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnLandscapeUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLandscapeManager::FOnLandscapeUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnLandscapeUnloaded, const FString& LandscapeId)
{
	struct AuracronWorldPartitionLandscapeManager_eventOnLandscapeUnloaded_Parms
	{
		FString LandscapeId;
	};
	AuracronWorldPartitionLandscapeManager_eventOnLandscapeUnloaded_Parms Parms;
	Parms.LandscapeId=LandscapeId;
	OnLandscapeUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLandscapeUnloaded ****************************************************

// ********** Begin Delegate FOnLandscapeLODChanged ************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventOnLandscapeLODChanged_Parms
	{
		FString LandscapeId;
		EAuracronLandscapeLODState NewLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLOD_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventOnLandscapeLODChanged_Parms, LandscapeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::NewProp_NewLOD = { "NewLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventOnLandscapeLODChanged_Parms, NewLOD), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState, METADATA_PARAMS(0, nullptr) }; // 460746793
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::NewProp_NewLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "OnLandscapeLODChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnLandscapeLODChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnLandscapeLODChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLandscapeManager::FOnLandscapeLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLandscapeLODChanged, const FString& LandscapeId, EAuracronLandscapeLODState NewLOD)
{
	struct AuracronWorldPartitionLandscapeManager_eventOnLandscapeLODChanged_Parms
	{
		FString LandscapeId;
		EAuracronLandscapeLODState NewLOD;
	};
	AuracronWorldPartitionLandscapeManager_eventOnLandscapeLODChanged_Parms Parms;
	Parms.LandscapeId=LandscapeId;
	Parms.NewLOD=NewLOD;
	OnLandscapeLODChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLandscapeLODChanged **************************************************

// ********** Begin Delegate FOnHeightmapUpdated ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms
	{
		FString LandscapeId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms, LandscapeId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "OnHeightmapUpdated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLandscapeManager::FOnHeightmapUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnHeightmapUpdated, const FString& LandscapeId, bool bSuccess)
{
	struct AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms
	{
		FString LandscapeId;
		bool bSuccess;
	};
	AuracronWorldPartitionLandscapeManager_eventOnHeightmapUpdated_Parms Parms;
	Parms.LandscapeId=LandscapeId;
	Parms.bSuccess=bSuccess ? true : false;
	OnHeightmapUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnHeightmapUpdated *****************************************************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function CalculateLODForDistance *
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventCalculateLODForDistance_Parms
	{
		float Distance;
		EAuracronLandscapeLODState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventCalculateLODForDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventCalculateLODForDistance_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState, METADATA_PARAMS(0, nullptr) }; // 460746793
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "CalculateLODForDistance", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::AuracronWorldPartitionLandscapeManager_eventCalculateLODForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::AuracronWorldPartitionLandscapeManager_eventCalculateLODForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execCalculateLODForDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLandscapeLODState*)Z_Param__Result=P_THIS->CalculateLODForDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function CalculateLODForDistance ***

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function CreateLandscape *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms
	{
		FVector Location;
		int32 ComponentCountX;
		int32 ComponentCountY;
		int32 HeightmapResolution;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape creation and management\n" },
#endif
		{ "CPP_Default_HeightmapResolution", "1024" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComponentCountX;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComponentCountY;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HeightmapResolution;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_ComponentCountX = { "ComponentCountX", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms, ComponentCountX), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_ComponentCountY = { "ComponentCountY", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms, ComponentCountY), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_HeightmapResolution = { "HeightmapResolution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms, HeightmapResolution), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_ComponentCountX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_ComponentCountY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_HeightmapResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "CreateLandscape", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventCreateLandscape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execCreateLandscape)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FIntProperty,Z_Param_ComponentCountX);
	P_GET_PROPERTY(FIntProperty,Z_Param_ComponentCountY);
	P_GET_PROPERTY(FIntProperty,Z_Param_HeightmapResolution);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateLandscape(Z_Param_Out_Location,Z_Param_ComponentCountX,Z_Param_ComponentCountY,Z_Param_HeightmapResolution);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function CreateLandscape ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function DoesLandscapeExist ******
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventDoesLandscapeExist_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventDoesLandscapeExist_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventDoesLandscapeExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventDoesLandscapeExist_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "DoesLandscapeExist", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::AuracronWorldPartitionLandscapeManager_eventDoesLandscapeExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::AuracronWorldPartitionLandscapeManager_eventDoesLandscapeExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execDoesLandscapeExist)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesLandscapeExist(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function DoesLandscapeExist ********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function DrawDebugLandscapeInfo **
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventDrawDebugLandscapeInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventDrawDebugLandscapeInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "DrawDebugLandscapeInfo", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::AuracronWorldPartitionLandscapeManager_eventDrawDebugLandscapeInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::AuracronWorldPartitionLandscapeManager_eventDrawDebugLandscapeInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execDrawDebugLandscapeInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugLandscapeInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function DrawDebugLandscapeInfo ****

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function EnableLandscapeCollision 
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms
	{
		FString LandscapeId;
		bool bEnabled;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "EnableLandscapeCollision", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::AuracronWorldPartitionLandscapeManager_eventEnableLandscapeCollision_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execEnableLandscapeCollision)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EnableLandscapeCollision(Z_Param_LandscapeId,Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function EnableLandscapeCollision **

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function EnableLandscapeDebug ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventEnableLandscapeDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventEnableLandscapeDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventEnableLandscapeDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "EnableLandscapeDebug", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::AuracronWorldPartitionLandscapeManager_eventEnableLandscapeDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::AuracronWorldPartitionLandscapeManager_eventEnableLandscapeDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execEnableLandscapeDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableLandscapeDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function EnableLandscapeDebug ******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetAllLandscapes ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetAllLandscapes_Parms
	{
		TArray<FAuracronLandscapeDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor, METADATA_PARAMS(0, nullptr) }; // 106307670
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetAllLandscapes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 106307670
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetAllLandscapes", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::AuracronWorldPartitionLandscapeManager_eventGetAllLandscapes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::AuracronWorldPartitionLandscapeManager_eventGetAllLandscapes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetAllLandscapes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLandscapeDescriptor>*)Z_Param__Result=P_THIS->GetAllLandscapes();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetAllLandscapes **********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetConfiguration ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetConfiguration_Parms
	{
		FAuracronLandscapeConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration, METADATA_PARAMS(0, nullptr) }; // 2797402148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::AuracronWorldPartitionLandscapeManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::AuracronWorldPartitionLandscapeManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLandscapeConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetConfiguration **********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetHeightAtLocation *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetHeightAtLocation_Parms
	{
		FString LandscapeId;
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetHeightAtLocation_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetHeightAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetHeightAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetHeightAtLocation", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::AuracronWorldPartitionLandscapeManager_eventGetHeightAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::AuracronWorldPartitionLandscapeManager_eventGetHeightAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetHeightAtLocation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHeightAtLocation(Z_Param_LandscapeId,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetHeightAtLocation *******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetHeightmapData ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetHeightmapData_Parms
	{
		FString LandscapeId;
		FAuracronHeightmapData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetHeightmapData_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetHeightmapData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronHeightmapData, METADATA_PARAMS(0, nullptr) }; // 995768442
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetHeightmapData", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::AuracronWorldPartitionLandscapeManager_eventGetHeightmapData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::AuracronWorldPartitionLandscapeManager_eventGetHeightmapData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetHeightmapData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronHeightmapData*)Z_Param__Result=P_THIS->GetHeightmapData(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetHeightmapData **********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetInstance *************
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionLandscapeManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::AuracronWorldPartitionLandscapeManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::AuracronWorldPartitionLandscapeManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionLandscapeManager**)Z_Param__Result=UAuracronWorldPartitionLandscapeManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetInstance ***************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeCell ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapeCell_Parms
	{
		FString LandscapeId;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeCell_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeCell_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapeCell", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapeCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetLandscapeCell(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeCell **********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeDescriptor **
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapeDescriptor_Parms
	{
		FString LandscapeId;
		FAuracronLandscapeDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeDescriptor_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor, METADATA_PARAMS(0, nullptr) }; // 106307670
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapeDescriptor", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapeDescriptor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLandscapeDescriptor*)Z_Param__Result=P_THIS->GetLandscapeDescriptor(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeDescriptor ****

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeIds *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapeIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapeIds", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapeIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLandscapeIds();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeIds ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeLOD *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapeLOD_Parms
	{
		FString LandscapeId;
		EAuracronLandscapeLODState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeLOD_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeLOD_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState, METADATA_PARAMS(0, nullptr) }; // 460746793
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapeLOD", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapeLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLandscapeLODState*)Z_Param__Result=P_THIS->GetLandscapeLOD(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeLOD ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeMaterials ***
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapeMaterials_Parms
	{
		FString LandscapeId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeMaterials_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeMaterials_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapeMaterials", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeMaterials_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeMaterials_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapeMaterials)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLandscapeMaterials(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeMaterials *****

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapesInCell *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapesInCell_Parms
	{
		FString CellId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapesInCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapesInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapesInCell", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapesInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapesInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapesInCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLandscapesInCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapesInCell *******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeStatistics **
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapeStatistics_Parms
	{
		FAuracronLandscapeStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLandscapeStatistics, METADATA_PARAMS(0, nullptr) }; // 3967063662
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapeStatistics", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapeStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLandscapeStatistics*)Z_Param__Result=P_THIS->GetLandscapeStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeStatistics ****

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeStreamingState 
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLandscapeStreamingState_Parms
	{
		FString LandscapeId;
		EAuracronLandscapeStreamingState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeStreamingState_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLandscapeStreamingState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeStreamingState, METADATA_PARAMS(0, nullptr) }; // 786100275
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLandscapeStreamingState", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeStreamingState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::AuracronWorldPartitionLandscapeManager_eventGetLandscapeStreamingState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLandscapeStreamingState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLandscapeStreamingState*)Z_Param__Result=P_THIS->GetLandscapeStreamingState(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLandscapeStreamingState 

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLoadedLandscapeCount *
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapeCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapeCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLoadedLandscapeCount", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapeCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapeCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLoadedLandscapeCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedLandscapeCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLoadedLandscapeCount ***

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetLoadedLandscapes *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapes_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetLoadedLandscapes", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::AuracronWorldPartitionLandscapeManager_eventGetLoadedLandscapes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetLoadedLandscapes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedLandscapes();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetLoadedLandscapes *******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetStreamingLandscapes **
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetStreamingLandscapes_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetStreamingLandscapes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetStreamingLandscapes", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::AuracronWorldPartitionLandscapeManager_eventGetStreamingLandscapes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::AuracronWorldPartitionLandscapeManager_eventGetStreamingLandscapes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetStreamingLandscapes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetStreamingLandscapes();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetStreamingLandscapes ****

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetTotalLandscapeCount **
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetTotalLandscapeCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetTotalLandscapeCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetTotalLandscapeCount", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::AuracronWorldPartitionLandscapeManager_eventGetTotalLandscapeCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::AuracronWorldPartitionLandscapeManager_eventGetTotalLandscapeCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetTotalLandscapeCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalLandscapeCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetTotalLandscapeCount ****

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function GetTotalMemoryUsage *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventGetTotalMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventGetTotalMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "GetTotalMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionLandscapeManager_eventGetTotalMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionLandscapeManager_eventGetTotalMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execGetTotalMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function GetTotalMemoryUsage *******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function Initialize **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventInitialize_Parms
	{
		FAuracronLandscapeConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2797402148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::AuracronWorldPartitionLandscapeManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::AuracronWorldPartitionLandscapeManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronLandscapeConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function Initialize ****************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function IsInitialized ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::AuracronWorldPartitionLandscapeManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::AuracronWorldPartitionLandscapeManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function IsInitialized *************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function IsLandscapeCollisionEnabled 
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventIsLandscapeCollisionEnabled_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventIsLandscapeCollisionEnabled_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventIsLandscapeCollisionEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventIsLandscapeCollisionEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "IsLandscapeCollisionEnabled", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::AuracronWorldPartitionLandscapeManager_eventIsLandscapeCollisionEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::AuracronWorldPartitionLandscapeManager_eventIsLandscapeCollisionEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execIsLandscapeCollisionEnabled)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLandscapeCollisionEnabled(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function IsLandscapeCollisionEnabled 

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function IsLandscapeDebugEnabled *
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventIsLandscapeDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventIsLandscapeDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventIsLandscapeDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "IsLandscapeDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::AuracronWorldPartitionLandscapeManager_eventIsLandscapeDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::AuracronWorldPartitionLandscapeManager_eventIsLandscapeDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execIsLandscapeDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLandscapeDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function IsLandscapeDebugEnabled ***

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function LoadHeightmap ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventLoadHeightmap_Parms
	{
		FString LandscapeId;
		FAuracronHeightmapData HeightmapData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Heightmap management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Heightmap management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightmapData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightmapData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventLoadHeightmap_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_HeightmapData = { "HeightmapData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventLoadHeightmap_Parms, HeightmapData), Z_Construct_UScriptStruct_FAuracronHeightmapData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightmapData_MetaData), NewProp_HeightmapData_MetaData) }; // 995768442
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventLoadHeightmap_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventLoadHeightmap_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_HeightmapData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "LoadHeightmap", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::AuracronWorldPartitionLandscapeManager_eventLoadHeightmap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::AuracronWorldPartitionLandscapeManager_eventLoadHeightmap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execLoadHeightmap)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_GET_STRUCT_REF(FAuracronHeightmapData,Z_Param_Out_HeightmapData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadHeightmap(Z_Param_LandscapeId,Z_Param_Out_HeightmapData);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function LoadHeightmap *************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function LoadLandscape ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventLoadLandscape_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventLoadLandscape_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventLoadLandscape_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventLoadLandscape_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "LoadLandscape", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventLoadLandscape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventLoadLandscape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execLoadLandscape)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadLandscape(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function LoadLandscape *************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function LoadLandscapeMaterials **
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventLoadLandscapeMaterials_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventLoadLandscapeMaterials_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventLoadLandscapeMaterials_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventLoadLandscapeMaterials_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "LoadLandscapeMaterials", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::AuracronWorldPartitionLandscapeManager_eventLoadLandscapeMaterials_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::AuracronWorldPartitionLandscapeManager_eventLoadLandscapeMaterials_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execLoadLandscapeMaterials)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadLandscapeMaterials(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function LoadLandscapeMaterials ****

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function LogLandscapeState *******
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LogLandscapeState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LogLandscapeState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "LogLandscapeState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LogLandscapeState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LogLandscapeState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LogLandscapeState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LogLandscapeState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execLogLandscapeState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogLandscapeState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function LogLandscapeState *********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function MoveLandscapeToCell *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventMoveLandscapeToCell_Parms
	{
		FString LandscapeId;
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventMoveLandscapeToCell_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventMoveLandscapeToCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventMoveLandscapeToCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventMoveLandscapeToCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "MoveLandscapeToCell", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::AuracronWorldPartitionLandscapeManager_eventMoveLandscapeToCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::AuracronWorldPartitionLandscapeManager_eventMoveLandscapeToCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execMoveLandscapeToCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveLandscapeToCell(Z_Param_LandscapeId,Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function MoveLandscapeToCell *******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function RemoveLandscape *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventRemoveLandscape_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventRemoveLandscape_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventRemoveLandscape_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventRemoveLandscape_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "RemoveLandscape", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventRemoveLandscape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventRemoveLandscape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execRemoveLandscape)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveLandscape(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function RemoveLandscape ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function ResetStatistics *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function ResetStatistics ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function SetConfiguration ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventSetConfiguration_Parms
	{
		FAuracronLandscapeConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2797402148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::AuracronWorldPartitionLandscapeManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::AuracronWorldPartitionLandscapeManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronLandscapeConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function SetConfiguration **********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function SetLandscapeLOD *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventSetLandscapeLOD_Parms
	{
		FString LandscapeId;
		EAuracronLandscapeLODState LODState;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODState;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventSetLandscapeLOD_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_LODState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_LODState = { "LODState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventSetLandscapeLOD_Parms, LODState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLandscapeLODState, METADATA_PARAMS(0, nullptr) }; // 460746793
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventSetLandscapeLOD_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventSetLandscapeLOD_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_LODState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_LODState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "SetLandscapeLOD", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::AuracronWorldPartitionLandscapeManager_eventSetLandscapeLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::AuracronWorldPartitionLandscapeManager_eventSetLandscapeLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execSetLandscapeLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_GET_ENUM(EAuracronLandscapeLODState,Z_Param_LODState);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLandscapeLOD(Z_Param_LandscapeId,EAuracronLandscapeLODState(Z_Param_LODState));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function SetLandscapeLOD ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function SetLandscapeMaterial ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventSetLandscapeMaterial_Parms
	{
		FString LandscapeId;
		FString MaterialPath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialPath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventSetLandscapeMaterial_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_MaterialPath = { "MaterialPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventSetLandscapeMaterial_Parms, MaterialPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialPath_MetaData), NewProp_MaterialPath_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventSetLandscapeMaterial_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventSetLandscapeMaterial_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_MaterialPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "SetLandscapeMaterial", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::AuracronWorldPartitionLandscapeManager_eventSetLandscapeMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::AuracronWorldPartitionLandscapeManager_eventSetLandscapeMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execSetLandscapeMaterial)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_MaterialPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLandscapeMaterial(Z_Param_LandscapeId,Z_Param_MaterialPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function SetLandscapeMaterial ******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function Shutdown ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function Shutdown ******************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function Tick ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::AuracronWorldPartitionLandscapeManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::AuracronWorldPartitionLandscapeManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function Tick **********************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function UnloadHeightmap *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventUnloadHeightmap_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventUnloadHeightmap_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventUnloadHeightmap_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventUnloadHeightmap_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "UnloadHeightmap", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::AuracronWorldPartitionLandscapeManager_eventUnloadHeightmap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::AuracronWorldPartitionLandscapeManager_eventUnloadHeightmap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execUnloadHeightmap)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadHeightmap(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function UnloadHeightmap ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function UnloadLandscape *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventUnloadLandscape_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventUnloadLandscape_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventUnloadLandscape_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventUnloadLandscape_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "UnloadLandscape", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventUnloadLandscape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::AuracronWorldPartitionLandscapeManager_eventUnloadLandscape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execUnloadLandscape)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadLandscape(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function UnloadLandscape ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function UnloadLandscapeMaterials 
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventUnloadLandscapeMaterials_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventUnloadLandscapeMaterials_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventUnloadLandscapeMaterials_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventUnloadLandscapeMaterials_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "UnloadLandscapeMaterials", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::AuracronWorldPartitionLandscapeManager_eventUnloadLandscapeMaterials_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::AuracronWorldPartitionLandscapeManager_eventUnloadLandscapeMaterials_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execUnloadLandscapeMaterials)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadLandscapeMaterials(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function UnloadLandscapeMaterials **

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function UpdateCollisionMesh *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventUpdateCollisionMesh_Parms
	{
		FString LandscapeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventUpdateCollisionMesh_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventUpdateCollisionMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventUpdateCollisionMesh_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "UpdateCollisionMesh", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::AuracronWorldPartitionLandscapeManager_eventUpdateCollisionMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::AuracronWorldPartitionLandscapeManager_eventUpdateCollisionMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execUpdateCollisionMesh)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateCollisionMesh(Z_Param_LandscapeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function UpdateCollisionMesh *******

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function UpdateDistanceBasedLODs *
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventUpdateDistanceBasedLODs_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventUpdateDistanceBasedLODs_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "UpdateDistanceBasedLODs", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionLandscapeManager_eventUpdateDistanceBasedLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionLandscapeManager_eventUpdateDistanceBasedLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execUpdateDistanceBasedLODs)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedLODs(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function UpdateDistanceBasedLODs ***

// ********** Begin Class UAuracronWorldPartitionLandscapeManager Function UpdateHeightmap *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics
{
	struct AuracronWorldPartitionLandscapeManager_eventUpdateHeightmap_Parms
	{
		FString LandscapeId;
		TArray<float> HeightData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HeightData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_LandscapeId = { "LandscapeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventUpdateHeightmap_Parms, LandscapeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeId_MetaData), NewProp_LandscapeId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_HeightData_Inner = { "HeightData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_HeightData = { "HeightData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLandscapeManager_eventUpdateHeightmap_Parms, HeightData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightData_MetaData), NewProp_HeightData_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLandscapeManager_eventUpdateHeightmap_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLandscapeManager_eventUpdateHeightmap_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_LandscapeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_HeightData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_HeightData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, nullptr, "UpdateHeightmap", Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::AuracronWorldPartitionLandscapeManager_eventUpdateHeightmap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::AuracronWorldPartitionLandscapeManager_eventUpdateHeightmap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLandscapeManager::execUpdateHeightmap)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LandscapeId);
	P_GET_TARRAY_REF(float,Z_Param_Out_HeightData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateHeightmap(Z_Param_LandscapeId,Z_Param_Out_HeightData);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLandscapeManager Function UpdateHeightmap ***********

// ********** Begin Class UAuracronWorldPartitionLandscapeManager **********************************
void UAuracronWorldPartitionLandscapeManager::StaticRegisterNativesUAuracronWorldPartitionLandscapeManager()
{
	UClass* Class = UAuracronWorldPartitionLandscapeManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateLODForDistance", &UAuracronWorldPartitionLandscapeManager::execCalculateLODForDistance },
		{ "CreateLandscape", &UAuracronWorldPartitionLandscapeManager::execCreateLandscape },
		{ "DoesLandscapeExist", &UAuracronWorldPartitionLandscapeManager::execDoesLandscapeExist },
		{ "DrawDebugLandscapeInfo", &UAuracronWorldPartitionLandscapeManager::execDrawDebugLandscapeInfo },
		{ "EnableLandscapeCollision", &UAuracronWorldPartitionLandscapeManager::execEnableLandscapeCollision },
		{ "EnableLandscapeDebug", &UAuracronWorldPartitionLandscapeManager::execEnableLandscapeDebug },
		{ "GetAllLandscapes", &UAuracronWorldPartitionLandscapeManager::execGetAllLandscapes },
		{ "GetConfiguration", &UAuracronWorldPartitionLandscapeManager::execGetConfiguration },
		{ "GetHeightAtLocation", &UAuracronWorldPartitionLandscapeManager::execGetHeightAtLocation },
		{ "GetHeightmapData", &UAuracronWorldPartitionLandscapeManager::execGetHeightmapData },
		{ "GetInstance", &UAuracronWorldPartitionLandscapeManager::execGetInstance },
		{ "GetLandscapeCell", &UAuracronWorldPartitionLandscapeManager::execGetLandscapeCell },
		{ "GetLandscapeDescriptor", &UAuracronWorldPartitionLandscapeManager::execGetLandscapeDescriptor },
		{ "GetLandscapeIds", &UAuracronWorldPartitionLandscapeManager::execGetLandscapeIds },
		{ "GetLandscapeLOD", &UAuracronWorldPartitionLandscapeManager::execGetLandscapeLOD },
		{ "GetLandscapeMaterials", &UAuracronWorldPartitionLandscapeManager::execGetLandscapeMaterials },
		{ "GetLandscapesInCell", &UAuracronWorldPartitionLandscapeManager::execGetLandscapesInCell },
		{ "GetLandscapeStatistics", &UAuracronWorldPartitionLandscapeManager::execGetLandscapeStatistics },
		{ "GetLandscapeStreamingState", &UAuracronWorldPartitionLandscapeManager::execGetLandscapeStreamingState },
		{ "GetLoadedLandscapeCount", &UAuracronWorldPartitionLandscapeManager::execGetLoadedLandscapeCount },
		{ "GetLoadedLandscapes", &UAuracronWorldPartitionLandscapeManager::execGetLoadedLandscapes },
		{ "GetStreamingLandscapes", &UAuracronWorldPartitionLandscapeManager::execGetStreamingLandscapes },
		{ "GetTotalLandscapeCount", &UAuracronWorldPartitionLandscapeManager::execGetTotalLandscapeCount },
		{ "GetTotalMemoryUsage", &UAuracronWorldPartitionLandscapeManager::execGetTotalMemoryUsage },
		{ "Initialize", &UAuracronWorldPartitionLandscapeManager::execInitialize },
		{ "IsInitialized", &UAuracronWorldPartitionLandscapeManager::execIsInitialized },
		{ "IsLandscapeCollisionEnabled", &UAuracronWorldPartitionLandscapeManager::execIsLandscapeCollisionEnabled },
		{ "IsLandscapeDebugEnabled", &UAuracronWorldPartitionLandscapeManager::execIsLandscapeDebugEnabled },
		{ "LoadHeightmap", &UAuracronWorldPartitionLandscapeManager::execLoadHeightmap },
		{ "LoadLandscape", &UAuracronWorldPartitionLandscapeManager::execLoadLandscape },
		{ "LoadLandscapeMaterials", &UAuracronWorldPartitionLandscapeManager::execLoadLandscapeMaterials },
		{ "LogLandscapeState", &UAuracronWorldPartitionLandscapeManager::execLogLandscapeState },
		{ "MoveLandscapeToCell", &UAuracronWorldPartitionLandscapeManager::execMoveLandscapeToCell },
		{ "RemoveLandscape", &UAuracronWorldPartitionLandscapeManager::execRemoveLandscape },
		{ "ResetStatistics", &UAuracronWorldPartitionLandscapeManager::execResetStatistics },
		{ "SetConfiguration", &UAuracronWorldPartitionLandscapeManager::execSetConfiguration },
		{ "SetLandscapeLOD", &UAuracronWorldPartitionLandscapeManager::execSetLandscapeLOD },
		{ "SetLandscapeMaterial", &UAuracronWorldPartitionLandscapeManager::execSetLandscapeMaterial },
		{ "Shutdown", &UAuracronWorldPartitionLandscapeManager::execShutdown },
		{ "Tick", &UAuracronWorldPartitionLandscapeManager::execTick },
		{ "UnloadHeightmap", &UAuracronWorldPartitionLandscapeManager::execUnloadHeightmap },
		{ "UnloadLandscape", &UAuracronWorldPartitionLandscapeManager::execUnloadLandscape },
		{ "UnloadLandscapeMaterials", &UAuracronWorldPartitionLandscapeManager::execUnloadLandscapeMaterials },
		{ "UpdateCollisionMesh", &UAuracronWorldPartitionLandscapeManager::execUpdateCollisionMesh },
		{ "UpdateDistanceBasedLODs", &UAuracronWorldPartitionLandscapeManager::execUpdateDistanceBasedLODs },
		{ "UpdateHeightmap", &UAuracronWorldPartitionLandscapeManager::execUpdateHeightmap },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager;
UClass* UAuracronWorldPartitionLandscapeManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionLandscapeManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionLandscapeManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionLandscapeManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_NoRegister()
{
	return UAuracronWorldPartitionLandscapeManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Landscape Manager\n * Central manager for landscape integration with world partition\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionLandscape.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Landscape Manager\nCentral manager for landscape integration with world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLandscapeLoaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLandscapeUnloaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLandscapeLODChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnHeightmapUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeSubsystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLandscape.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLandscapeLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLandscapeUnloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLandscapeLODChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnHeightmapUpdated;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_LandscapeSubsystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CalculateLODForDistance, "CalculateLODForDistance" }, // 2954580033
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_CreateLandscape, "CreateLandscape" }, // 3666833933
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DoesLandscapeExist, "DoesLandscapeExist" }, // 540500264
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_DrawDebugLandscapeInfo, "DrawDebugLandscapeInfo" }, // 3687915847
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeCollision, "EnableLandscapeCollision" }, // 2203390389
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_EnableLandscapeDebug, "EnableLandscapeDebug" }, // 1173190366
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetAllLandscapes, "GetAllLandscapes" }, // 1549051388
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetConfiguration, "GetConfiguration" }, // 2682112963
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightAtLocation, "GetHeightAtLocation" }, // 1219970608
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetHeightmapData, "GetHeightmapData" }, // 2878634379
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetInstance, "GetInstance" }, // 1562205213
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeCell, "GetLandscapeCell" }, // 2452730241
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeDescriptor, "GetLandscapeDescriptor" }, // 4218854398
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeIds, "GetLandscapeIds" }, // 2313536507
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeLOD, "GetLandscapeLOD" }, // 130701737
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeMaterials, "GetLandscapeMaterials" }, // 1325066843
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapesInCell, "GetLandscapesInCell" }, // 4224360428
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStatistics, "GetLandscapeStatistics" }, // 2512304346
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLandscapeStreamingState, "GetLandscapeStreamingState" }, // 4165377242
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapeCount, "GetLoadedLandscapeCount" }, // 842425828
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetLoadedLandscapes, "GetLoadedLandscapes" }, // 3588925715
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetStreamingLandscapes, "GetStreamingLandscapes" }, // 1846418766
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalLandscapeCount, "GetTotalLandscapeCount" }, // 1012839433
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_GetTotalMemoryUsage, "GetTotalMemoryUsage" }, // 1849352297
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Initialize, "Initialize" }, // 785720175
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsInitialized, "IsInitialized" }, // 2424967612
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeCollisionEnabled, "IsLandscapeCollisionEnabled" }, // 581213725
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_IsLandscapeDebugEnabled, "IsLandscapeDebugEnabled" }, // 72875335
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadHeightmap, "LoadHeightmap" }, // 2454276862
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscape, "LoadLandscape" }, // 384576028
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LoadLandscapeMaterials, "LoadLandscapeMaterials" }, // 792967346
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_LogLandscapeState, "LogLandscapeState" }, // 1168596765
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_MoveLandscapeToCell, "MoveLandscapeToCell" }, // 1285604322
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature, "OnHeightmapUpdated__DelegateSignature" }, // 1968612595
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature, "OnLandscapeLoaded__DelegateSignature" }, // 2542111038
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature, "OnLandscapeLODChanged__DelegateSignature" }, // 4139944406
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature, "OnLandscapeUnloaded__DelegateSignature" }, // 3924331130
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_RemoveLandscape, "RemoveLandscape" }, // 2342545887
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_ResetStatistics, "ResetStatistics" }, // 3476232356
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetConfiguration, "SetConfiguration" }, // 1490639254
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeLOD, "SetLandscapeLOD" }, // 3663668474
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_SetLandscapeMaterial, "SetLandscapeMaterial" }, // 1240163668
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Shutdown, "Shutdown" }, // 196254732
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_Tick, "Tick" }, // 1911709531
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadHeightmap, "UnloadHeightmap" }, // 2960457535
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscape, "UnloadLandscape" }, // 4030108461
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UnloadLandscapeMaterials, "UnloadLandscapeMaterials" }, // 2553041972
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateCollisionMesh, "UpdateCollisionMesh" }, // 2469841461
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateDistanceBasedLODs, "UpdateDistanceBasedLODs" }, // 3897376285
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLandscapeManager_UpdateHeightmap, "UpdateHeightmap" }, // 4034783136
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionLandscapeManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnLandscapeLoaded = { "OnLandscapeLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLandscapeManager, OnLandscapeLoaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLandscapeLoaded_MetaData), NewProp_OnLandscapeLoaded_MetaData) }; // 2542111038
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnLandscapeUnloaded = { "OnLandscapeUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLandscapeManager, OnLandscapeUnloaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLandscapeUnloaded_MetaData), NewProp_OnLandscapeUnloaded_MetaData) }; // 3924331130
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnLandscapeLODChanged = { "OnLandscapeLODChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLandscapeManager, OnLandscapeLODChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLandscapeLODChanged_MetaData), NewProp_OnLandscapeLODChanged_MetaData) }; // 4139944406
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnHeightmapUpdated = { "OnHeightmapUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLandscapeManager, OnHeightmapUpdated), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnHeightmapUpdated_MetaData), NewProp_OnHeightmapUpdated_MetaData) }; // 1968612595
void Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionLandscapeManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionLandscapeManager), &Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLandscapeManager, Configuration), Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2797402148
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLandscapeManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_LandscapeSubsystem = { "LandscapeSubsystem", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLandscapeManager, LandscapeSubsystem), Z_Construct_UClass_ULandscapeSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeSubsystem_MetaData), NewProp_LandscapeSubsystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnLandscapeLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnLandscapeUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnLandscapeLODChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_OnHeightmapUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::NewProp_LandscapeSubsystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::ClassParams = {
	&UAuracronWorldPartitionLandscapeManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager.OuterSingleton;
}
UAuracronWorldPartitionLandscapeManager::UAuracronWorldPartitionLandscapeManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionLandscapeManager);
UAuracronWorldPartitionLandscapeManager::~UAuracronWorldPartitionLandscapeManager() {}
// ********** End Class UAuracronWorldPartitionLandscapeManager ************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronLandscapeStreamingState_StaticEnum, TEXT("EAuracronLandscapeStreamingState"), &Z_Registration_Info_UEnum_EAuracronLandscapeStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 786100275U) },
		{ EAuracronLandscapeLODState_StaticEnum, TEXT("EAuracronLandscapeLODState"), &Z_Registration_Info_UEnum_EAuracronLandscapeLODState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 460746793U) },
		{ EAuracronHeightmapQuality_StaticEnum, TEXT("EAuracronHeightmapQuality"), &Z_Registration_Info_UEnum_EAuracronHeightmapQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2474363911U) },
		{ EAuracronMaterialStreamingType_StaticEnum, TEXT("EAuracronMaterialStreamingType"), &Z_Registration_Info_UEnum_EAuracronMaterialStreamingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3084897209U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLandscapeConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics::NewStructOps, TEXT("AuracronLandscapeConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronLandscapeConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLandscapeConfiguration), 2797402148U) },
		{ FAuracronLandscapeDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics::NewStructOps, TEXT("AuracronLandscapeDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronLandscapeDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLandscapeDescriptor), 106307670U) },
		{ FAuracronHeightmapData::StaticStruct, Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics::NewStructOps, TEXT("AuracronHeightmapData"), &Z_Registration_Info_UScriptStruct_FAuracronHeightmapData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronHeightmapData), 995768442U) },
		{ FAuracronLandscapeStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics::NewStructOps, TEXT("AuracronLandscapeStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronLandscapeStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLandscapeStatistics), 3967063662U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager, UAuracronWorldPartitionLandscapeManager::StaticClass, TEXT("UAuracronWorldPartitionLandscapeManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionLandscapeManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionLandscapeManager), 1517017738U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_1113786646(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
