#include "AuracronHairGeneration.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "GroomAsset.h"
#include "GroomBindingAsset.h"
#include "GroomComponent.h"
#include "HairStrandsCore.h"
#include "HairCardsBuilder.h"
#include "GroomBuilder.h"
#include "GroomImportOptions.h"
#include "GroomCreateBindingOptions.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Rendering/SkeletalMeshRenderData.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"

DEFINE_LOG_CATEGORY(LogAuracronHairGeneration);

// ========================================
// FAuracronHairGeneration Implementation
// ========================================

FAuracronHairGeneration::FAuracronHairGeneration()
    : HairAssetCacheMemoryUsage(0)
    , TotalHairGenerationTime(0.0f)
{
}

FAuracronHairGeneration::~FAuracronHairGeneration()
{
    ClearHairAssetCache();
}

UGroomAsset* FAuracronHairGeneration::GenerateProceduralHair(const FHairGenerationParameters& Parameters)
{
    FScopeLock Lock(&HairGenerationMutex);

    FString ValidationError;
    if (!ValidateHairGenerationParameters(Parameters, ValidationError))
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Invalid hair generation parameters: %s"), *ValidationError);
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Generate cache key for hair asset using UE5.6 hashing
        FString CacheKey = CalculateHairGenerationHash(Parameters);
        
        // Check cache first using UE5.6 caching system
        if (HairAssetCache.Contains(CacheKey))
        {
            TWeakObjectPtr<UGroomAsset> CachedAsset = HairAssetCache[CacheKey];
            if (CachedAsset.IsValid())
            {
                UE_LOG(LogAuracronHairGeneration, Log, TEXT("Returning cached hair asset for key: %s"), *CacheKey);
                return CachedAsset.Get();
            }
            else
            {
                // Remove invalid cache entry
                HairAssetCache.Remove(CacheKey);
            }
        }

        // Create new groom asset using UE5.6 Groom system
        UGroomAsset* NewGroomAsset = NewObject<UGroomAsset>(GetTransientPackage(), UGroomAsset::StaticClass());
        if (!NewGroomAsset)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to create groom asset"));
            return nullptr;
        }

        // Initialize groom asset with UE5.6 groom builder
        FGroomBuilder GroomBuilder;
        if (!InitializeGroomBuilder(GroomBuilder, Parameters))
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to initialize groom builder"));
            return nullptr;
        }

        // Generate hair strands using UE5.6 hair strand system
        if (Parameters.StrandType == EHairStrandType::Strands || Parameters.StrandType == EHairStrandType::Hybrid)
        {
            if (!GenerateHairStrands(NewGroomAsset, Parameters.StrandData, Parameters.StylingData))
            {
                UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to generate hair strands"));
                return nullptr;
            }
        }

        // Generate hair cards if requested using UE5.6 hair card system
        if (Parameters.bGenerateHairCards && (Parameters.StrandType == EHairStrandType::Cards || Parameters.StrandType == EHairStrandType::Hybrid))
        {
            if (!CreateHairCards(NewGroomAsset, Parameters.CardData))
            {
                UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to generate hair cards"));
                return nullptr;
            }
        }

        // Setup physics if requested using UE5.6 Niagara system
        if (Parameters.bGeneratePhysics && Parameters.PhysicsData.PhysicsType != EHairPhysicsType::None)
        {
            SetupHairPhysics(NewGroomAsset, Parameters.PhysicsData);
        }

        // Apply color variation using UE5.6 material system
        GenerateHairColorVariation(NewGroomAsset, Parameters.ColorData);

        // Generate LODs if requested using UE5.6 LOD system
        if (Parameters.bGenerateLODs)
        {
            OptimizeHairLOD(NewGroomAsset, Parameters.LODData);
        }

        // Optimize for performance using UE5.6 optimization
        OptimizeHairPerformance(NewGroomAsset);

        // Build the groom asset using UE5.6 groom builder
        GroomBuilder.BuildGroom(NewGroomAsset);

        // Cache the result using UE5.6 caching system
        HairAssetCache.Add(CacheKey, NewGroomAsset);
        UpdateHairAssetCacheStats();

        // Update generation statistics
        double GenerationTime = FPlatformTime::Seconds() - StartTime;
        TotalHairGenerationTime += GenerationTime;
        UpdateHairGenerationStats(TEXT("GenerateProceduralHair"), GenerationTime, true);

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully generated procedural hair asset in %.3f seconds"), GenerationTime);
        return NewGroomAsset;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception generating procedural hair: %s"), UTF8_TO_TCHAR(e.what()));
        UpdateHairGenerationStats(TEXT("GenerateProceduralHair"), FPlatformTime::Seconds() - StartTime, false);
        return nullptr;
    }
}

bool FAuracronHairGeneration::GenerateHairStrands(UGroomAsset* GroomAsset, const FHairStrandData& StrandData, const FHairStylingData& StylingData)
{
    if (!GroomAsset)
    {
        return false;
    }

    try
    {
        // Create hair strand geometry using UE5.6 Hair Strands Core
        FHairStrandsDatas HairStrandsData;
        
        // Generate strand points using UE5.6 procedural generation
        TArray<FHairStrandsPoints> StrandPoints;
        if (!GenerateStrandPoints(StrandData, StrandPoints))
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to generate strand points"));
            return false;
        }

        // Convert strand points to hair strands data using UE5.6 conversion
        for (int32 StrandIndex = 0; StrandIndex < StrandPoints.Num(); ++StrandIndex)
        {
            const FHairStrandsPoints& Points = StrandPoints[StrandIndex];
            
            // Create strand data using UE5.6 hair strand APIs
            FHairStrandsData StrandData_Internal;
            StrandData_Internal.StrandsPoints = Points;
            
            // Set strand properties using UE5.6 strand system
            StrandData_Internal.StrandsRadius.SetNum(Points.PointsPosition.Num());
            StrandData_Internal.StrandsCoordU.SetNum(Points.PointsPosition.Num());
            
            for (int32 PointIndex = 0; PointIndex < Points.PointsPosition.Num(); ++PointIndex)
            {
                // Calculate radius based on strand data using UE5.6 math
                float NormalizedU = static_cast<float>(PointIndex) / FMath::Max(1, Points.PointsPosition.Num() - 1);
                StrandData_Internal.StrandsRadius[PointIndex] = FMath::Lerp(StrandData.RootRadius, StrandData.TipRadius, NormalizedU);
                StrandData_Internal.StrandsCoordU[PointIndex] = NormalizedU;
            }
            
            HairStrandsData.StrandsData.Add(StrandData_Internal);
        }

        // Apply styling if procedural styling is enabled using UE5.6 styling system
        if (StylingData.bUseProceduralStyling)
        {
            ApplyProceduralStyling(HairStrandsData, StylingData);
        }

        // Set the hair strands data to the groom asset using UE5.6 groom APIs
        GroomAsset->SetHairStrandsData(HairStrandsData);

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully generated %d hair strands"), StrandData.StrandCount);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception generating hair strands: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronHairGeneration::CreateHairCards(UGroomAsset* GroomAsset, const FHairCardData& CardData)
{
    if (!GroomAsset)
    {
        return false;
    }

    try
    {
        // Use UE5.6 Hair Cards Builder for card generation
        FHairCardsBuilder CardsBuilder;
        
        // Configure cards builder using UE5.6 configuration system
        FHairCardsBuilderSettings BuilderSettings;
        BuilderSettings.CardCount = CardData.CardCount;
        BuilderSettings.CardWidth = CardData.CardWidth;
        BuilderSettings.CardLength = CardData.CardLength;
        BuilderSettings.TextureResolution = CardData.TextureResolution;
        BuilderSettings.Quality = static_cast<EHairCardsQuality>(CardData.Quality);
        
        // Generate hair cards from existing strands using UE5.6 card generation
        FHairCardsData CardsData;
        if (!CardsBuilder.BuildCards(GroomAsset->GetHairStrandsData(), BuilderSettings, CardsData))
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to build hair cards"));
            return false;
        }

        // Create hair card textures using UE5.6 texture generation
        UTexture2D* HairTexture = GenerateHairTextureAtlas(GroomAsset, CardData.TextureResolution, CardData.Quality);
        if (!HairTexture)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to generate hair texture atlas"));
            return false;
        }

        // Set the hair cards data to the groom asset using UE5.6 groom APIs
        GroomAsset->SetHairCardsData(CardsData);
        GroomAsset->SetHairCardsTexture(HairTexture);

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully created %d hair cards"), CardData.CardCount);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception creating hair cards: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronHairGeneration::SetupHairPhysics(UGroomAsset* GroomAsset, const FHairPhysicsData& PhysicsData)
{
    if (!GroomAsset)
    {
        return false;
    }

    try
    {
        // Setup Niagara physics simulation using UE5.6 Niagara system
        switch (PhysicsData.PhysicsType)
        {
            case EHairPhysicsType::Niagara:
            {
                // Create Niagara system for hair physics using UE5.6 Niagara APIs
                UNiagaraSystem* HairPhysicsSystem = CreateHairPhysicsNiagaraSystem(PhysicsData);
                if (HairPhysicsSystem)
                {
                    GroomAsset->SetPhysicsSystem(HairPhysicsSystem);
                    UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully setup Niagara hair physics"));
                }
                break;
            }
            
            case EHairPhysicsType::Simulation:
            {
                // Setup hair simulation using UE5.6 hair simulation system
                FHairSimulationSettings SimSettings;
                SimSettings.Damping = PhysicsData.Damping;
                SimSettings.Stiffness = PhysicsData.Stiffness;
                SimSettings.Gravity = PhysicsData.Gravity;
                SimSettings.AirResistance = PhysicsData.AirResistance;
                
                GroomAsset->SetSimulationSettings(SimSettings);
                UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully setup hair simulation physics"));
                break;
            }
            
            default:
                UE_LOG(LogAuracronHairGeneration, Warning, TEXT("Unsupported hair physics type"));
                return false;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception setting up hair physics: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronHairGeneration::GenerateHairColorVariation(UGroomAsset* GroomAsset, const FHairColorData& ColorData)
{
    if (!GroomAsset)
    {
        return false;
    }

    try
    {
        // Generate hair color texture using UE5.6 texture generation
        UTexture2D* ColorTexture = GenerateHairColorTexture(ColorData, FIntPoint(1024, 1024));
        if (!ColorTexture)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to generate hair color texture"));
            return false;
        }

        // Create material instance with color variation using UE5.6 material system
        UMaterialInstanceDynamic* HairMaterial = CreateHairMaterialInstance(GroomAsset, ColorData);
        if (!HairMaterial)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to create hair material instance"));
            return false;
        }

        // Apply color texture to material using UE5.6 material parameters
        HairMaterial->SetTextureParameterValue(TEXT("ColorTexture"), ColorTexture);
        HairMaterial->SetVectorParameterValue(TEXT("BaseColor"), ColorData.BaseColor);
        HairMaterial->SetVectorParameterValue(TEXT("TipColor"), ColorData.TipColor);
        HairMaterial->SetScalarParameterValue(TEXT("Roughness"), ColorData.Roughness);
        HairMaterial->SetScalarParameterValue(TEXT("SpecularIntensity"), ColorData.SpecularIntensity);
        HairMaterial->SetScalarParameterValue(TEXT("VariationIntensity"), ColorData.VariationIntensity);

        // Set the material to the groom asset using UE5.6 groom APIs
        GroomAsset->SetHairMaterial(HairMaterial);

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully generated hair color variation"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception generating hair color variation: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

UGroomBindingAsset* FAuracronHairGeneration::CreateGroomBinding(UGroomAsset* GroomAsset, USkeletalMesh* SkeletalMesh, const FString& AttachmentSocket)
{
    if (!GroomAsset || !SkeletalMesh)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Invalid groom asset or skeletal mesh"));
        return nullptr;
    }

    try
    {
        // Create new groom binding asset using UE5.6 groom binding system
        UGroomBindingAsset* BindingAsset = NewObject<UGroomBindingAsset>(GetTransientPackage(), UGroomBindingAsset::StaticClass());
        if (!BindingAsset)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to create groom binding asset"));
            return nullptr;
        }

        // Configure binding options using UE5.6 binding configuration
        FGroomCreateBindingOptions BindingOptions;
        BindingOptions.GroomAsset = GroomAsset;
        BindingOptions.SourceSkeletalMesh = SkeletalMesh;
        BindingOptions.TargetSkeletalMesh = SkeletalMesh;
        BindingOptions.NumInterpolationPoints = 100;
        BindingOptions.MatchingSection = 0;

        // Create binding data using UE5.6 groom binding APIs
        if (!CreateHairBindingData(BindingAsset, GroomAsset, SkeletalMesh, AttachmentSocket))
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to create hair binding data"));
            return nullptr;
        }

        // Build the binding using UE5.6 binding builder
        FGroomBindingBuilder BindingBuilder;
        if (!BindingBuilder.BuildBinding(BindingAsset, BindingOptions))
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to build groom binding"));
            return nullptr;
        }

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully created groom binding for socket: %s"), *AttachmentSocket);
        return BindingAsset;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception creating groom binding: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

bool FAuracronHairGeneration::OptimizeHairLOD(UGroomAsset* GroomAsset, const FHairLODData& LODData)
{
    if (!GroomAsset)
    {
        return false;
    }

    try
    {
        // Create hair LOD levels using UE5.6 LOD system
        for (int32 LODIndex = 0; LODIndex < LODData.LODLevels.Num(); ++LODIndex)
        {
            const FHairLODLevel& LODLevel = LODData.LODLevels[LODIndex];

            // Create LOD data using UE5.6 LOD generation
            FHairLODSettings LODSettings;
            LODSettings.CurveDecimation = LODLevel.CurveDecimation;
            LODSettings.VertexDecimation = LODLevel.VertexDecimation;
            LODSettings.AngularThreshold = LODLevel.AngularThreshold;
            LODSettings.ScreenSize = LODLevel.ScreenSize;
            LODSettings.ThicknessScale = LODLevel.ThicknessScale;

            // Generate LOD using UE5.6 hair LOD generator
            if (!GenerateHairLODLevel(GroomAsset, LODIndex, LODSettings))
            {
                UE_LOG(LogAuracronHairGeneration, Warning, TEXT("Failed to generate LOD level %d"), LODIndex);
                continue;
            }
        }

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully optimized hair LOD with %d levels"), LODData.LODLevels.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception optimizing hair LOD: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

UTexture2D* FAuracronHairGeneration::GenerateHairTextureAtlas(UGroomAsset* GroomAsset, const FIntPoint& TextureResolution, EHairCardQuality Quality)
{
    if (!GroomAsset)
    {
        return nullptr;
    }

    try
    {
        // Create transient texture for hair atlas using UE5.6 texture creation
        EPixelFormat PixelFormat = GetPixelFormatForQuality(Quality);
        UTexture2D* HairTexture = UTexture2D::CreateTransient(TextureResolution.X, TextureResolution.Y, PixelFormat);
        if (!HairTexture)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to create hair texture"));
            return nullptr;
        }

        // Generate hair texture data using UE5.6 procedural generation
        TArray<FColor> TextureData;
        if (!GenerateHairTextureData(GroomAsset, TextureResolution, Quality, TextureData))
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to generate hair texture data"));
            return nullptr;
        }

        // Upload texture data using UE5.6 texture upload system
        void* TextureDataPtr = HairTexture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_WRITE_ONLY);
        FMemory::Memcpy(TextureDataPtr, TextureData.GetData(), TextureData.Num() * sizeof(FColor));
        HairTexture->GetPlatformData()->Mips[0].BulkData.Unlock();

        // Configure texture properties using UE5.6 texture settings
        HairTexture->SRGB = true;
        HairTexture->Filter = TF_Bilinear;
        HairTexture->AddressX = TA_Clamp;
        HairTexture->AddressY = TA_Clamp;
        HairTexture->CompressionSettings = TC_Default;
        HairTexture->UpdateResource();

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully generated hair texture atlas %dx%d"), TextureResolution.X, TextureResolution.Y);
        return HairTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception generating hair texture atlas: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

UMaterialInstanceDynamic* FAuracronHairGeneration::CreateHairMaterialInstance(UGroomAsset* GroomAsset, const FHairColorData& ColorData, UMaterialInterface* BaseMaterial)
{
    if (!GroomAsset)
    {
        return nullptr;
    }

    try
    {
        // Get base material or use default using UE5.6 material system
        UMaterialInterface* MaterialToUse = BaseMaterial ? BaseMaterial : GetDefaultHairMaterial();
        if (!MaterialToUse)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("No valid base material available"));
            return nullptr;
        }

        // Create dynamic material instance using UE5.6 material system
        UMaterialInstanceDynamic* HairMaterial = UMaterialInstanceDynamic::Create(MaterialToUse, GetTransientPackage());
        if (!HairMaterial)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to create dynamic material instance"));
            return nullptr;
        }

        // Set hair color parameters using UE5.6 material parameter system
        HairMaterial->SetVectorParameterValue(TEXT("BaseColor"), ColorData.BaseColor);
        HairMaterial->SetVectorParameterValue(TEXT("TipColor"), ColorData.TipColor);
        HairMaterial->SetVectorParameterValue(TEXT("RootColor"), ColorData.RootColor);
        HairMaterial->SetScalarParameterValue(TEXT("Metallic"), ColorData.Metallic);
        HairMaterial->SetScalarParameterValue(TEXT("Roughness"), ColorData.Roughness);
        HairMaterial->SetScalarParameterValue(TEXT("SpecularIntensity"), ColorData.SpecularIntensity);
        HairMaterial->SetScalarParameterValue(TEXT("VariationIntensity"), ColorData.VariationIntensity);
        HairMaterial->SetScalarParameterValue(TEXT("Opacity"), ColorData.Opacity);

        // Cache the material instance using UE5.6 caching system
        FString CacheKey = FString::Printf(TEXT("HairMaterial_%s_%u"), *GroomAsset->GetName(), GetTypeHash(ColorData));
        HairMaterialCache.Add(CacheKey, HairMaterial);

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully created hair material instance"));
        return HairMaterial;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception creating hair material instance: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

bool FAuracronHairGeneration::ApplyHairStyling(UGroomAsset* GroomAsset, const FHairStylingData& StylingData)
{
    if (!GroomAsset)
    {
        return false;
    }

    try
    {
        // Apply procedural styling using UE5.6 hair styling system
        FHairStrandsDatas& HairData = GroomAsset->GetHairStrandsData();

        // Apply curl transformation using UE5.6 math utilities
        if (StylingData.CurlIntensity > 0.0f)
        {
            ApplyCurlTransformation(HairData, StylingData);
        }

        // Apply wave transformation using UE5.6 wave generation
        if (StylingData.WaveIntensity > 0.0f)
        {
            ApplyWaveTransformation(HairData, StylingData);
        }

        // Apply length variation using UE5.6 procedural variation
        if (StylingData.LengthVariation > 0.0f)
        {
            ApplyLengthVariation(HairData, StylingData);
        }

        // Apply directional styling using UE5.6 vector math
        if (!StylingData.DirectionVector.IsZero())
        {
            ApplyDirectionalStyling(HairData, StylingData);
        }

        // Update the groom asset with modified data using UE5.6 groom APIs
        GroomAsset->SetHairStrandsData(HairData);
        GroomAsset->MarkPackageDirty();

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Successfully applied hair styling"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception applying hair styling: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronHairGeneration::ValidateHairGenerationParameters(const FHairGenerationParameters& Parameters, FString& OutError)
{
    // Validate strand data
    if (Parameters.StrandData.StrandCount <= 0)
    {
        OutError = TEXT("Strand count must be greater than 0");
        return false;
    }

    if (Parameters.StrandData.StrandCount > 1000000)
    {
        OutError = TEXT("Strand count exceeds maximum limit of 1,000,000");
        return false;
    }

    if (Parameters.StrandData.SegmentsPerStrand <= 0)
    {
        OutError = TEXT("Segments per strand must be greater than 0");
        return false;
    }

    if (Parameters.StrandData.RootRadius <= 0.0f || Parameters.StrandData.TipRadius < 0.0f)
    {
        OutError = TEXT("Invalid strand radius values");
        return false;
    }

    if (Parameters.StrandData.StrandLength <= 0.0f)
    {
        OutError = TEXT("Strand length must be greater than 0");
        return false;
    }

    // Validate card data if cards are requested
    if (Parameters.bGenerateHairCards)
    {
        if (Parameters.CardData.CardCount <= 0)
        {
            OutError = TEXT("Card count must be greater than 0");
            return false;
        }

        if (Parameters.CardData.CardWidth <= 0.0f || Parameters.CardData.CardLength <= 0.0f)
        {
            OutError = TEXT("Invalid card dimensions");
            return false;
        }

        if (Parameters.CardData.TextureResolution.X <= 0 || Parameters.CardData.TextureResolution.Y <= 0)
        {
            OutError = TEXT("Invalid texture resolution");
            return false;
        }
    }

    // Validate physics data if physics are requested
    if (Parameters.bGeneratePhysics)
    {
        if (Parameters.PhysicsData.Damping < 0.0f || Parameters.PhysicsData.Damping > 1.0f)
        {
            OutError = TEXT("Damping must be between 0 and 1");
            return false;
        }

        if (Parameters.PhysicsData.Stiffness < 0.0f || Parameters.PhysicsData.Stiffness > 1.0f)
        {
            OutError = TEXT("Stiffness must be between 0 and 1");
            return false;
        }
    }

    // Validate color data
    if (!Parameters.ColorData.BaseColor.IsFinite())
    {
        OutError = TEXT("Invalid base color values");
        return false;
    }

    return true;
}

FString FAuracronHairGeneration::CalculateHairGenerationHash(const FHairGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE5.6 hashing
    FString HashString = FString::Printf(TEXT("%d_%d_%d_%s_%s_%d_%d"),
        static_cast<int32>(Parameters.StrandType),
        static_cast<int32>(Parameters.DensityLevel),
        Parameters.StrandData.StrandCount,
        *Parameters.ColorData.BaseColor.ToString(),
        *Parameters.StylingData.DirectionVector.ToString(),
        Parameters.bGenerateHairCards ? 1 : 0,
        Parameters.RandomSeed
    );

    return FString::Printf(TEXT("%u"), GetTypeHash(HashString));
}

bool FAuracronHairGeneration::InitializeGroomBuilder(FGroomBuilder& GroomBuilder, const FHairGenerationParameters& Parameters)
{
    try
    {
        // Configure groom builder settings using UE5.6 groom builder APIs
        FGroomBuildSettings BuildSettings;
        BuildSettings.bOverrideGuides = true;
        BuildSettings.HairToGuideDensity = CalculateGuideDensity(Parameters.DensityLevel);
        BuildSettings.InterpolationDistance = 1.0f;
        BuildSettings.bRandomizeGuide = true;
        BuildSettings.bUseUniqueGuide = false;

        // Set build settings to groom builder
        GroomBuilder.SetBuildSettings(BuildSettings);

        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception initializing groom builder: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronHairGeneration::GenerateStrandPoints(const FHairStrandData& StrandData, TArray<FHairStrandsPoints>& OutStrandPoints)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronHairGeneration::GenerateStrandPoints);
    
    try
    {
        OutStrandPoints.Empty();
        OutStrandPoints.Reserve(StrandData.StrandCount);

        // Generate strand points using UE5.6 procedural generation
        FRandomStream RandomStream(StrandData.RandomSeed);

        for (int32 StrandIndex = 0; StrandIndex < StrandData.StrandCount; ++StrandIndex)
        {
            FHairStrandsPoints StrandPoints;

            // Generate root position using UE5.6 distribution algorithms
            FVector RootPosition = GenerateRootPosition(StrandIndex, StrandData, RandomStream);

            // Generate strand direction using UE5.6 vector math
            FVector StrandDirection = GenerateStrandDirection(RootPosition, StrandData, RandomStream);

            // Generate points along the strand using UE5.6 curve generation
            StrandPoints.PointsPosition.Reserve(StrandData.SegmentsPerStrand + 1);
            StrandPoints.PointsCoordU.Reserve(StrandData.SegmentsPerStrand + 1);

            for (int32 SegmentIndex = 0; SegmentIndex <= StrandData.SegmentsPerStrand; ++SegmentIndex)
            {
                float T = static_cast<float>(SegmentIndex) / StrandData.SegmentsPerStrand;

                // Calculate position along strand using UE5.6 interpolation
                FVector SegmentPosition = RootPosition + (StrandDirection * StrandData.StrandLength * T);

                // Apply noise for natural variation using UE5.6 noise functions
                FVector NoiseOffset = GenerateStrandNoise(SegmentPosition, T, StrandData, RandomStream);
                SegmentPosition += NoiseOffset;

                StrandPoints.PointsPosition.Add(SegmentPosition);
                StrandPoints.PointsCoordU.Add(T);
            }

            // Set strand properties
            StrandPoints.StrandID = StrandIndex;
            StrandPoints.NumPoints = StrandData.SegmentsPerStrand + 1;
            
            OutStrandPoints.Add(StrandPoints);
        }

        UE_LOG(LogAuracronHairGeneration, Log, TEXT("Generated %d hair strands with %d total points"), 
               StrandData.StrandCount, OutStrandPoints.Num());
        
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception generating strand points: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

void FAuracronHairGeneration::ApplyProceduralStyling(FHairStrandsDatas& HairData, const FHairStylingData& StylingData)
{
    try
    {
        // Apply styling to each strand group using UE5.6 hair styling APIs
        for (FHairStrandsData& StrandData : HairData.StrandsData)
        {
            // Apply curl transformation using UE5.6 math utilities
            if (StylingData.CurlIntensity > 0.0f)
            {
                ApplyCurlToStrandData(StrandData, StylingData);
            }

            // Apply wave transformation using UE5.6 wave generation
            if (StylingData.WaveIntensity > 0.0f)
            {
                ApplyWaveToStrandData(StrandData, StylingData);
            }

            // Apply directional styling using UE5.6 vector operations
            if (!StylingData.DirectionVector.IsZero())
            {
                ApplyDirectionToStrandData(StrandData, StylingData);
            }
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception applying procedural styling: %s"), UTF8_TO_TCHAR(e.what()));
    }
}

UTexture2D* FAuracronHairGeneration::GenerateHairColorTexture(const FHairColorData& ColorData, const FIntPoint& Resolution)
{
    try
    {
        // Create transient texture for hair color using UE5.6 texture creation
        UTexture2D* ColorTexture = UTexture2D::CreateTransient(Resolution.X, Resolution.Y, PF_B8G8R8A8);
        if (!ColorTexture)
        {
            UE_LOG(LogAuracronHairGeneration, Error, TEXT("Failed to create color texture"));
            return nullptr;
        }

        // Generate color variation texture using UE5.6 procedural generation
        TArray<FColor> ColorData_Array;
        ColorData_Array.Reserve(Resolution.X * Resolution.Y);

        FRandomStream ColorRandom(GetTypeHash(ColorData.BaseColor.ToString()));

        for (int32 Y = 0; Y < Resolution.Y; ++Y)
        {
            for (int32 X = 0; X < Resolution.X; ++X)
            {
                // Calculate normalized coordinates
                float U = static_cast<float>(X) / Resolution.X;
                float V = static_cast<float>(Y) / Resolution.Y;

                // Generate color variation using UE5.6 color interpolation
                FLinearColor PixelColor = GenerateHairPixelColor(ColorData, U, V, ColorRandom);
                ColorData_Array.Add(PixelColor.ToFColor(true));
            }
        }

        // Upload texture data using UE5.6 texture upload system
        void* TextureDataPtr = ColorTexture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_WRITE_ONLY);
        FMemory::Memcpy(TextureDataPtr, ColorData_Array.GetData(), ColorData_Array.Num() * sizeof(FColor));
        ColorTexture->GetPlatformData()->Mips[0].BulkData.Unlock();

        // Configure texture properties using UE5.6 texture settings
        ColorTexture->SRGB = true;
        ColorTexture->Filter = TF_Bilinear;
        ColorTexture->AddressX = TA_Wrap;
        ColorTexture->AddressY = TA_Wrap;
        ColorTexture->UpdateResource();

        return ColorTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception generating hair color texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

float FAuracronHairGeneration::CalculateGuideDensity(EHairDensityLevel DensityLevel)
{
    switch (DensityLevel)
    {
        case EHairDensityLevel::Low:
            return 0.1f;
        case EHairDensityLevel::Medium:
            return 0.3f;
        case EHairDensityLevel::High:
            return 0.6f;
        case EHairDensityLevel::Ultra:
            return 1.0f;
        default:
            return 0.3f;
    }
}

FVector FAuracronHairGeneration::GenerateRootPosition(int32 StrandIndex, const FHairStrandData& StrandData, FRandomStream& RandomStream)
{
    // Generate root position using UE5.6 surface sampling
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronHairGeneration::GenerateRootPosition);
    
    // Use scalp bounds to generate positions
    FVector ScalpCenter = StrandData.ScalpBounds.GetCenter();
    FVector ScalpExtent = StrandData.ScalpBounds.GetExtent();
    
    // Generate position using different distribution patterns
    FVector RootPosition;
    
    switch (StrandData.DistributionPattern)
    {
        case EHairDistributionPattern::Uniform:
        {
            // Uniform distribution across scalp surface
            float U = RandomStream.FRand();
            float V = RandomStream.FRand();
            
            RootPosition = ScalpCenter + FVector(
                (U - 0.5f) * ScalpExtent.X * 2.0f,
                (V - 0.5f) * ScalpExtent.Y * 2.0f,
                0.0f
            );
            break;
        }
        
        case EHairDistributionPattern::Radial:
        {
            // Radial distribution from crown
            float Angle = RandomStream.FRand() * 2.0f * PI;
            float Radius = FMath::Sqrt(RandomStream.FRand()) * ScalpExtent.Size2D();
            
            RootPosition = ScalpCenter + FVector(
                FMath::Cos(Angle) * Radius,
                FMath::Sin(Angle) * Radius,
                0.0f
            );
            break;
        }
        
        case EHairDistributionPattern::Clustered:
        {
            // Clustered distribution with density variations
            int32 ClusterIndex = StrandIndex % 5; // 5 clusters
            float ClusterAngle = (ClusterIndex * 2.0f * PI) / 5.0f;
            float ClusterRadius = ScalpExtent.Size2D() * 0.3f;
            
            FVector ClusterCenter = ScalpCenter + FVector(
                FMath::Cos(ClusterAngle) * ClusterRadius,
                FMath::Sin(ClusterAngle) * ClusterRadius,
                0.0f
            );
            
            // Add random offset within cluster
            float LocalAngle = RandomStream.FRand() * 2.0f * PI;
            float LocalRadius = RandomStream.FRand() * ScalpExtent.Size2D() * 0.2f;
            
            RootPosition = ClusterCenter + FVector(
                FMath::Cos(LocalAngle) * LocalRadius,
                FMath::Sin(LocalAngle) * LocalRadius,
                0.0f
            );
            break;
        }
        
        default:
        {
            // Default to uniform distribution
            float U = RandomStream.FRand();
            float V = RandomStream.FRand();
            
            RootPosition = ScalpCenter + FVector(
                (U - 0.5f) * ScalpExtent.X * 2.0f,
                (V - 0.5f) * ScalpExtent.Y * 2.0f,
                0.0f
            );
            break;
        }
    }
    
    // Project to scalp surface (assuming ellipsoid scalp shape)
    FVector ToCenter = RootPosition - ScalpCenter;
    ToCenter.Z = 0.0f; // Keep on XY plane initially
    
    // Calculate scalp surface height using ellipsoid equation
    float NormalizedX = ToCenter.X / ScalpExtent.X;
    float NormalizedY = ToCenter.Y / ScalpExtent.Y;
    float DistanceFromCenter = FMath::Sqrt(NormalizedX * NormalizedX + NormalizedY * NormalizedY);
    
    if (DistanceFromCenter <= 1.0f)
    {
        // Point is within scalp bounds, calculate surface height
        float SurfaceHeight = ScalpExtent.Z * FMath::Sqrt(1.0f - DistanceFromCenter * DistanceFromCenter);
        RootPosition.Z = ScalpCenter.Z + SurfaceHeight;
    }
    else
    {
        // Point is outside scalp, clamp to edge
        ToCenter = ToCenter.GetSafeNormal() * ScalpExtent.Size2D();
        RootPosition = ScalpCenter + ToCenter;
        RootPosition.Z = ScalpCenter.Z;
    }
    
    // Add small random variation to avoid perfect regularity
    FVector RandomOffset = FVector(
        RandomStream.FRandRange(-StrandData.PositionVariation, StrandData.PositionVariation),
        RandomStream.FRandRange(-StrandData.PositionVariation, StrandData.PositionVariation),
        RandomStream.FRandRange(-StrandData.PositionVariation * 0.1f, StrandData.PositionVariation * 0.1f)
    );
    
    RootPosition += RandomOffset;
    
    return RootPosition;
}

FVector FAuracronHairGeneration::GenerateStrandDirection(const FVector& RootPosition, const FHairStrandData& StrandData, FRandomStream& RandomStream)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronHairGeneration::GenerateStrandDirection);
    
    // Generate strand direction using UE5.6 vector field algorithms
    FVector ScalpCenter = StrandData.ScalpBounds.GetCenter();
    FVector ToCenter = RootPosition - ScalpCenter;
    ToCenter.Z = 0.0f; // Project to XY plane
    
    // Calculate base direction based on hair flow pattern
    FVector BaseDirection;
    
    switch (StrandData.FlowPattern)
    {
        case EHairFlowPattern::Radial:
        {
            // Hair flows radially outward from crown
            FVector RadialDirection = ToCenter.GetSafeNormal();
            BaseDirection = FVector(RadialDirection.X, RadialDirection.Y, -0.3f).GetSafeNormal();
            break;
        }
        
        case EHairFlowPattern::Spiral:
        {
            // Hair flows in spiral pattern (like a cowlick)
            float Angle = FMath::Atan2(ToCenter.Y, ToCenter.X);
            float SpiralAngle = Angle + StrandData.SpiralIntensity;
            
            FVector SpiralDirection = FVector(
                FMath::Cos(SpiralAngle),
                FMath::Sin(SpiralAngle),
                -0.4f
            ).GetSafeNormal();
            
            BaseDirection = SpiralDirection;
            break;
        }
        
        case EHairFlowPattern::Directional:
        {
            // Hair flows in a specific direction (like combed hair)
            FVector FlowDirection = StrandData.PrimaryFlowDirection.GetSafeNormal();
            BaseDirection = FVector(FlowDirection.X, FlowDirection.Y, -0.2f).GetSafeNormal();
            break;
        }
        
        case EHairFlowPattern::Natural:
        {
            // Natural hair growth pattern with multiple flow directions
            FVector RadialComponent = ToCenter.GetSafeNormal() * 0.6f;
            FVector GravityComponent = FVector(0.0f, 0.0f, -1.0f) * 0.4f;
            
            // Add some forward bias for natural hair fall
            FVector ForwardBias = FVector(0.0f, -0.2f, 0.0f);
            
            BaseDirection = (RadialComponent + GravityComponent + ForwardBias).GetSafeNormal();
            break;
        }
        
        default:
        {
            // Default downward direction
            BaseDirection = FVector(0.0f, 0.0f, -1.0f);
            break;
        }
    }
    
    // Apply curvature based on distance from scalp center
    float DistanceFromCenter = ToCenter.Size();
    float MaxDistance = StrandData.ScalpBounds.GetExtent().Size2D();
    float NormalizedDistance = FMath::Clamp(DistanceFromCenter / MaxDistance, 0.0f, 1.0f);
    
    // Hair tends to curve more at the edges of the scalp
    float CurvatureAmount = StrandData.CurvatureIntensity * NormalizedDistance;
    FVector CurvatureDirection = ToCenter.GetSafeNormal();
    
    BaseDirection = FMath::Lerp(BaseDirection, CurvatureDirection, CurvatureAmount).GetSafeNormal();
    
    // Add random variation for natural look
    FVector RandomVariation = FVector(
        RandomStream.FRandRange(-StrandData.DirectionVariation, StrandData.DirectionVariation),
        RandomStream.FRandRange(-StrandData.DirectionVariation, StrandData.DirectionVariation),
        RandomStream.FRandRange(-StrandData.DirectionVariation * 0.5f, StrandData.DirectionVariation * 0.5f)
    );
    
    FVector FinalDirection = (BaseDirection + RandomVariation).GetSafeNormal();
    
    // Ensure direction has some downward component for realistic hair fall
    if (FinalDirection.Z > -0.1f)
    {
        FinalDirection.Z = FMath::Min(FinalDirection.Z, -0.1f);
        FinalDirection = FinalDirection.GetSafeNormal();
    }
    
    return FinalDirection;
}

FVector FAuracronHairGeneration::GenerateStrandNoise(const FVector& Position, float T, const FHairStrandData& StrandData, FRandomStream& RandomStream)
{
    // Generate noise for natural strand variation using UE5.6 noise functions
    float NoiseScale = StrandData.NoiseScale * (1.0f - T * 0.5f); // Reduce noise towards tip

    FVector NoiseOffset;
    NoiseOffset.X = FMath::PerlinNoise1D(Position.X * 0.1f + T * 2.0f) * NoiseScale;
    NoiseOffset.Y = FMath::PerlinNoise1D(Position.Y * 0.1f + T * 2.0f) * NoiseScale;
    NoiseOffset.Z = FMath::PerlinNoise1D(Position.Z * 0.1f + T * 2.0f) * NoiseScale * 0.5f;

    return NoiseOffset;
}

void FAuracronHairGeneration::ApplyCurlToStrandData(FHairStrandsData& StrandData, const FHairStylingData& StylingData)
{
    // Apply curl transformation to strand points using UE5.6 math utilities
    for (int32 PointIndex = 0; PointIndex < StrandData.StrandsPoints.PointsPosition.Num(); ++PointIndex)
    {
        float T = StrandData.StrandsPoints.PointsCoordU[PointIndex];
        FVector& Position = StrandData.StrandsPoints.PointsPosition[PointIndex];

        // Calculate curl transformation using UE5.6 rotation math
        float CurlAngle = T * StylingData.CurlIntensity * 2.0f * PI * StylingData.CurlFrequency;
        FVector CurlAxis = StylingData.CurlAxis.GetSafeNormal();

        FQuat CurlRotation = FQuat(CurlAxis, CurlAngle);
        FVector RelativePosition = Position - (Position.ProjectOnTo(CurlAxis) * CurlAxis);
        FVector CurledPosition = CurlRotation.RotateVector(RelativePosition);

        Position = (Position.ProjectOnTo(CurlAxis) * CurlAxis) + CurledPosition;
    }
}

void FAuracronHairGeneration::ApplyWaveToStrandData(FHairStrandsData& StrandData, const FHairStylingData& StylingData)
{
    // Apply wave transformation to strand points using UE5.6 wave generation
    for (int32 PointIndex = 0; PointIndex < StrandData.StrandsPoints.PointsPosition.Num(); ++PointIndex)
    {
        float T = StrandData.StrandsPoints.PointsCoordU[PointIndex];
        FVector& Position = StrandData.StrandsPoints.PointsPosition[PointIndex];

        // Calculate wave displacement using UE5.6 trigonometric functions
        float WaveOffset = FMath::Sin(T * StylingData.WaveFrequency * 2.0f * PI) * StylingData.WaveIntensity;
        FVector WaveDirection = StylingData.WaveDirection.GetSafeNormal();

        Position += WaveDirection * WaveOffset;
    }
}

void FAuracronHairGeneration::ApplyDirectionToStrandData(FHairStrandsData& StrandData, const FHairStylingData& StylingData)
{
    // Apply directional styling to strand points using UE5.6 vector operations
    FVector TargetDirection = StylingData.DirectionVector.GetSafeNormal();

    for (int32 PointIndex = 0; PointIndex < StrandData.StrandsPoints.PointsPosition.Num(); ++PointIndex)
    {
        float T = StrandData.StrandsPoints.PointsCoordU[PointIndex];
        FVector& Position = StrandData.StrandsPoints.PointsPosition[PointIndex];

        // Apply directional influence using UE5.6 interpolation
        float DirectionInfluence = T * StylingData.DirectionStrength;
        FVector CurrentDirection = Position.GetSafeNormal();
        FVector BlendedDirection = FMath::Lerp(CurrentDirection, TargetDirection, DirectionInfluence);

        Position = BlendedDirection * Position.Size();
    }
}

FLinearColor FAuracronHairGeneration::GenerateHairPixelColor(const FHairColorData& ColorData, float U, float V, FRandomStream& RandomStream)
{
    // Generate pixel color with variation using UE5.6 color interpolation
    FLinearColor BaseColor = ColorData.BaseColor;
    FLinearColor TipColor = ColorData.TipColor;
    FLinearColor RootColor = ColorData.RootColor;

    // Interpolate between root and tip color based on V coordinate
    FLinearColor InterpolatedColor = FMath::Lerp(RootColor, TipColor, V);

    // Blend with base color using UE5.6 color blending
    FLinearColor FinalColor = FMath::Lerp(BaseColor, InterpolatedColor, 0.7f);

    // Add variation using UE5.6 random color variation
    if (ColorData.VariationIntensity > 0.0f)
    {
        float VariationAmount = RandomStream.FRandRange(-ColorData.VariationIntensity, ColorData.VariationIntensity);
        FinalColor = FinalColor * (1.0f + VariationAmount);
    }

    // Clamp color values using UE5.6 color utilities
    FinalColor = FinalColor.GetClamped(0.0f, 1.0f);

    return FinalColor;
}

EPixelFormat FAuracronHairGeneration::GetPixelFormatForQuality(EHairCardQuality Quality)
{
    switch (Quality)
    {
        case EHairCardQuality::Low:
            return PF_DXT1;
        case EHairCardQuality::Medium:
            return PF_DXT5;
        case EHairCardQuality::High:
            return PF_BC7;
        case EHairCardQuality::Ultra:
            return PF_B8G8R8A8;
        default:
            return PF_B8G8R8A8;
    }
}

bool FAuracronHairGeneration::GenerateHairTextureData(UGroomAsset* GroomAsset, const FIntPoint& Resolution, EHairCardQuality Quality, TArray<FColor>& OutTextureData)
{
    try
    {
        OutTextureData.Empty();
        OutTextureData.Reserve(Resolution.X * Resolution.Y);

        // Generate hair texture data using UE5.6 procedural generation
        FRandomStream TextureRandom(GetTypeHash(GroomAsset->GetName()));

        for (int32 Y = 0; Y < Resolution.Y; ++Y)
        {
            for (int32 X = 0; X < Resolution.X; ++X)
            {
                // Calculate normalized coordinates
                float U = static_cast<float>(X) / Resolution.X;
                float V = static_cast<float>(Y) / Resolution.Y;

                // Generate hair strand pattern using UE5.6 pattern generation
                FColor PixelColor = GenerateHairStrandPixel(U, V, Quality, TextureRandom);
                OutTextureData.Add(PixelColor);
            }
        }

        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronHairGeneration, Error, TEXT("Exception generating hair texture data: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

FColor FAuracronHairGeneration::GenerateHairStrandPixel(float U, float V, EHairCardQuality Quality, FRandomStream& RandomStream)
{
    // Generate hair strand pixel using UE5.6 procedural algorithms
    float StrandWidth = GetStrandWidthForQuality(Quality);
    float DistanceFromCenter = FMath::Abs(U - 0.5f) * 2.0f;

    // Calculate alpha based on distance from strand center using UE5.6 math
    float Alpha = 1.0f - FMath::Smoothstep(0.0f, StrandWidth, DistanceFromCenter);

    // Add noise for natural variation using UE5.6 noise functions
    float NoiseValue = FMath::PerlinNoise2D(FVector2D(U * 10.0f, V * 50.0f));
    Alpha *= (0.8f + NoiseValue * 0.2f);

    // Generate base color using UE5.6 color generation
    uint8 BaseValue = static_cast<uint8>(FMath::Clamp(RandomStream.FRandRange(180, 220), 0, 255));
    uint8 AlphaValue = static_cast<uint8>(FMath::Clamp(Alpha * 255.0f, 0.0f, 255.0f));

    return FColor(BaseValue, BaseValue, BaseValue, AlphaValue);
}

float FAuracronHairGeneration::GetStrandWidthForQuality(EHairCardQuality Quality)
{
    switch (Quality)
    {
        case EHairCardQuality::Low:
            return 0.8f;
        case EHairCardQuality::Medium:
            return 0.6f;
        case EHairCardQuality::High:
            return 0.4f;
        case EHairCardQuality::Ultra:
            return 0.2f;
        default:
            return 0.6f;
    }
}

void FAuracronHairGeneration::UpdateHairAssetCacheStats()
{
    FScopeLock Lock(&HairGenerationMutex);

    HairAssetCacheMemoryUsage = 0;

    // Calculate total memory usage of cached hair assets using UE5.6 memory tracking
    for (const auto& CachePair : HairAssetCache)
    {
        if (CachePair.Value.IsValid())
        {
            UGroomAsset* GroomAsset = CachePair.Value.Get();
            // Estimate memory usage based on groom asset complexity
            HairAssetCacheMemoryUsage += EstimateGroomAssetMemoryUsage(GroomAsset);
        }
    }
}

int32 FAuracronHairGeneration::EstimateGroomAssetMemoryUsage(UGroomAsset* GroomAsset)
{
    if (!GroomAsset)
    {
        return 0;
    }

    // Estimate memory usage based on groom asset data using UE5.6 memory estimation
    int32 EstimatedMemory = 0;

    // Base memory for groom asset structure
    EstimatedMemory += sizeof(UGroomAsset);

    // Memory for hair strands data
    const FHairStrandsDatas& HairData = GroomAsset->GetHairStrandsData();
    for (const FHairStrandsData& StrandData : HairData.StrandsData)
    {
        EstimatedMemory += StrandData.StrandsPoints.PointsPosition.Num() * sizeof(FVector);
        EstimatedMemory += StrandData.StrandsRadius.Num() * sizeof(float);
        EstimatedMemory += StrandData.StrandsCoordU.Num() * sizeof(float);
    }

    return EstimatedMemory;
}

void FAuracronHairGeneration::ClearHairAssetCache()
{
    FScopeLock Lock(&HairGenerationMutex);

    HairAssetCache.Empty();
    HairMaterialCache.Empty();
    HairAssetCacheMemoryUsage = 0;

    UE_LOG(LogAuracronHairGeneration, Log, TEXT("Hair asset cache cleared"));
}

void FAuracronHairGeneration::UpdateHairGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess)
{
    FScopeLock Lock(&HairGenerationMutex);

    FString StatsValue = FString::Printf(TEXT("Time: %.3fs, Success: %s"), ExecutionTime, bSuccess ? TEXT("Yes") : TEXT("No"));
    HairGenerationStats.Add(OperationName, StatsValue);
}
