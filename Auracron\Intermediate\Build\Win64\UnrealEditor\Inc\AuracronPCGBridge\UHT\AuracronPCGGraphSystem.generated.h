// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGGraphSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGGraphSystem_generated_h
#error "AuracronPCGGraphSystem.generated.h already included, missing '#pragma once' in AuracronPCGGraphSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGGraphSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class AActor;
class UAuracronPCGGraphWrapper;
class UPCGGraph;
class UPCGNode;
class UPCGSettings;
enum class EAuracronPCGGraphState : uint8;
struct FAuracronPCGGraphExecutionStats;
struct FAuracronPCGGraphSerializationData;
struct FAuracronPCGGraphValidationResult;
struct FAuracronPCGPinConnection;

// ********** Begin ScriptStruct FAuracronPCGGraphValidationResult *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_62_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGGraphValidationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGGraphValidationResult;
// ********** End ScriptStruct FAuracronPCGGraphValidationResult ***********************************

// ********** Begin ScriptStruct FAuracronPCGGraphExecutionStats ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_98_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGGraphExecutionStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGGraphExecutionStats;
// ********** End ScriptStruct FAuracronPCGGraphExecutionStats *************************************

// ********** Begin ScriptStruct FAuracronPCGPinConnection *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_137_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGPinConnection_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGPinConnection;
// ********** End ScriptStruct FAuracronPCGPinConnection *******************************************

// ********** Begin ScriptStruct FAuracronPCGGraphSerializationData ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_172_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGGraphSerializationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGGraphSerializationData;
// ********** End ScriptStruct FAuracronPCGGraphSerializationData **********************************

// ********** Begin Class UAuracronPCGGraphWrapper *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_211_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetNodesByType); \
	DECLARE_FUNCTION(execMergeGraph); \
	DECLARE_FUNCTION(execDuplicateGraph); \
	DECLARE_FUNCTION(execClearGraph); \
	DECLARE_FUNCTION(execRemoveGraphParameter); \
	DECLARE_FUNCTION(execGetAllGraphParameters); \
	DECLARE_FUNCTION(execGetGraphParameter); \
	DECLARE_FUNCTION(execSetGraphParameter); \
	DECLARE_FUNCTION(execMarkAsModified); \
	DECLARE_FUNCTION(execIsGraphModified); \
	DECLARE_FUNCTION(execGetConnectionCount); \
	DECLARE_FUNCTION(execGetNodeCount); \
	DECLARE_FUNCTION(execSetGraphName); \
	DECLARE_FUNCTION(execGetGraphName); \
	DECLARE_FUNCTION(execGetSerializationData); \
	DECLARE_FUNCTION(execDeserializeGraphFromString); \
	DECLARE_FUNCTION(execSerializeGraphToString); \
	DECLARE_FUNCTION(execLoadGraphFromFile); \
	DECLARE_FUNCTION(execSaveGraphToFile); \
	DECLARE_FUNCTION(execGetExecutionStats); \
	DECLARE_FUNCTION(execGetExecutionProgress); \
	DECLARE_FUNCTION(execGetExecutionState); \
	DECLARE_FUNCTION(execCancelExecution); \
	DECLARE_FUNCTION(execExecuteGraphAsync); \
	DECLARE_FUNCTION(execExecuteGraph); \
	DECLARE_FUNCTION(execValidateNodeConnections); \
	DECLARE_FUNCTION(execFindDisconnectedNodes); \
	DECLARE_FUNCTION(execHasCycles); \
	DECLARE_FUNCTION(execValidateGraph); \
	DECLARE_FUNCTION(execIsValidConnection); \
	DECLARE_FUNCTION(execGetAllConnections); \
	DECLARE_FUNCTION(execDisconnectPins); \
	DECLARE_FUNCTION(execConnectPins); \
	DECLARE_FUNCTION(execGetNodeById); \
	DECLARE_FUNCTION(execGetAllNodeIds); \
	DECLARE_FUNCTION(execMoveNode); \
	DECLARE_FUNCTION(execRemoveNode); \
	DECLARE_FUNCTION(execAddNode); \
	DECLARE_FUNCTION(execCloneGraph); \
	DECLARE_FUNCTION(execCreateNewGraph); \
	DECLARE_FUNCTION(execGetPCGGraph); \
	DECLARE_FUNCTION(execInitializeFromPCGGraph);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_211_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGGraphWrapper(); \
	friend struct Z_Construct_UClass_UAuracronPCGGraphWrapper_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGGraphWrapper, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGGraphWrapper_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGGraphWrapper)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_211_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGGraphWrapper(UAuracronPCGGraphWrapper&&) = delete; \
	UAuracronPCGGraphWrapper(const UAuracronPCGGraphWrapper&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGGraphWrapper); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGGraphWrapper); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGGraphWrapper) \
	NO_API virtual ~UAuracronPCGGraphWrapper();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_208_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_211_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_211_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_211_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h_211_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGGraphWrapper;

// ********** End Class UAuracronPCGGraphWrapper ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGGraphSystem_h

// ********** Begin Enum EAuracronPCGGraphState ****************************************************
#define FOREACH_ENUM_EAURACRONPCGGRAPHSTATE(op) \
	op(EAuracronPCGGraphState::Idle) \
	op(EAuracronPCGGraphState::Compiling) \
	op(EAuracronPCGGraphState::Executing) \
	op(EAuracronPCGGraphState::Completed) \
	op(EAuracronPCGGraphState::Error) \
	op(EAuracronPCGGraphState::Cancelled) 

enum class EAuracronPCGGraphState : uint8;
template<> struct TIsUEnumClass<EAuracronPCGGraphState> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGGraphState>();
// ********** End Enum EAuracronPCGGraphState ******************************************************

// ********** Begin Enum EAuracronPCGPinType *******************************************************
#define FOREACH_ENUM_EAURACRONPCGPINTYPE(op) \
	op(EAuracronPCGPinType::Input) \
	op(EAuracronPCGPinType::Output) \
	op(EAuracronPCGPinType::Parameter) \
	op(EAuracronPCGPinType::Execution) 

enum class EAuracronPCGPinType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPinType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPinType>();
// ********** End Enum EAuracronPCGPinType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
