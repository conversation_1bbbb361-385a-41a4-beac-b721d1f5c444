// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronAnalyticsBridge_init() {}
	AURACRONANALYTICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature();
	AURACRONANALYTICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronAnalyticsBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronAnalyticsBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronAnalyticsBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronAnalyticsBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xFDB42698,
				0x6674A1AD,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronAnalyticsBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronAnalyticsBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronAnalyticsBridge(Z_Construct_UPackage__Script_AuracronAnalyticsBridge, TEXT("/Script/AuracronAnalyticsBridge"), Z_Registration_Info_UPackage__Script_AuracronAnalyticsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xFDB42698, 0x6674A1AD));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
