#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGVolumeData.h"
#include "PCGSurfaceData.h"
#include "PCGLandscapeData.h"
#include "PCGSplineData.h"
#include "PCGMeshData.h"
#include "PCGTextureData.h"
#include "PCGRenderTargetData.h"
#include "PCGWorldActor.h"
#include "PCGSubsystem.h"
#include "Elements/PCGSurfaceSampler.h"
#include "Elements/PCGVolumeSampler.h"
#include "Elements/PCGSplineSampler.h"
#include "Elements/PCGStaticMeshSpawner.h"
#include "Elements/PCGTransformPoints.h"
#include "Elements/PCGDensityFilter.h"
#include "Elements/PCGAttributeFilter.h"
#include "Elements/PCGBoundsModifier.h"
#include "Elements/PCGCopyPoints.h"
#include "Elements/PCGCreateSpline.h"
#include "Elements/PCGDataFromActor.h"
#include "Elements/PCGDebugElement.h"
#include "Elements/PCGDifferenceElement.h"
#include "Elements/PCGExecuteBlueprint.h"
#include "Elements/PCGGather.h"
#include "Elements/PCGIntersectionElement.h"
#include "Elements/PCGLoopElement.h"
#include "Elements/PCGMergeElement.h"
#include "Elements/PCGNormalToDensity.h"
#include "Elements/PCGPointExtents.h"
#include "Elements/PCGPointFilter.h"
#include "Elements/PCGPointMatchAndSet.h"
#include "Elements/PCGPointNeighborhood.h"
#include "Elements/PCGPointProcessingElementBase.h"
#include "Elements/PCGProjectionElement.h"
#include "Elements/PCGPropertyToParamData.h"
#include "Elements/PCGSelfPruning.h"
#include "Elements/PCGSpawnActor.h"
#include "Elements/PCGStaticMeshSpawnerContext.h"
#include "Elements/PCGUnionElement.h"
#include "PCGContext.h"
#include "PCGExecutionContext.h"
#include "PCGSettings.h"
#include "PCGCustomVersion.h"
#include "PCGBlueprintElement.h"
#include "PCGParamData.h"
#include "PCGMetadata.h"
#include "PCGMetadataAccessor.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Queue.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "Stats/Stats.h"
#include "Elements/PCGAttributeFilter.h"
#include "Elements/PCGBoundsModifier.h"
#include "Elements/PCGCopyPoints.h"
#include "Elements/PCGCreateSpline.h"
#include "Elements/PCGDataFromActor.h"
#include "Elements/PCGDataTableRowToParamData.h"
#include "Elements/PCGDebugElement.h"
#include "Elements/PCGDifferenceElement.h"
#include "Elements/PCGExecuteBlueprint.h"
#include "Elements/PCGGather.h"
#include "Elements/PCGGetLandscapeData.h"
#include "Elements/PCGIntersectionElement.h"
#include "Elements/PCGLoopElement.h"
#include "Elements/PCGMakeConcreteElement.h"
#include "Elements/PCGMergeElement.h"
#include "Elements/PCGNormalToDensity.h"
#include "Elements/PCGNumberOfEntries.h"
#include "Elements/PCGPointExtents.h"
#include "Elements/PCGPointFilter.h"
#include "Elements/PCGPointMatchAndSet.h"
#include "Elements/PCGPointNeighborhood.h"
#include "Elements/PCGPointProcessingElementBase.h"
#include "Elements/PCGPrimitiveData.h"
#include "Elements/PCGProjectionElement.h"
#include "Elements/PCGPropertyToParamData.h"
#include "Elements/PCGReroute.h"
#include "Elements/PCGSelfPruning.h"
#include "Elements/PCGSpawnActor.h"
#include "Elements/PCGSplitAttributes.h"
#include "Elements/PCGSubgraph.h"
#include "Elements/PCGUnionElement.h"
#include "Elements/PCGUserParameterGet.h"
#include "Elements/Metadata/PCGMetadataAccessorHelpers.h"
#include "Elements/Metadata/PCGMetadataBreakVector.h"
#include "Elements/Metadata/PCGMetadataCompare.h"
#include "Elements/Metadata/PCGMetadataMakeRotator.h"
#include "Elements/Metadata/PCGMetadataMakeTransform.h"
#include "Elements/Metadata/PCGMetadataMakeVector.h"
#include "Elements/Metadata/PCGMetadataMathsOpElement.h"
#include "Elements/Metadata/PCGMetadataPartition.h"
#include "Elements/Metadata/PCGMetadataRenameElement.h"
#include "Elements/Metadata/PCGMetadataRotatorOp.h"
#include "Elements/Metadata/PCGMetadataStringOpElement.h"
#include "Elements/Metadata/PCGMetadataTransformOp.h"
#include "Elements/Metadata/PCGMetadataTrigOp.h"
#include "Elements/Metadata/PCGMetadataVectorOp.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPCGBridge, Log, All);

DECLARE_STATS_GROUP(TEXT("Auracron PCG Bridge"), STATGROUP_AuracronPCGBridge, STATCAT_Advanced);
DECLARE_CYCLE_STAT(TEXT("PCG Generation Time"), STAT_AuracronPCGGenerationTime, STATGROUP_AuracronPCGBridge);
DECLARE_CYCLE_STAT(TEXT("PCG Node Creation Time"), STAT_AuracronPCGNodeCreationTime, STATGROUP_AuracronPCGBridge);
DECLARE_CYCLE_STAT(TEXT("PCG Data Processing Time"), STAT_AuracronPCGDataProcessingTime, STATGROUP_AuracronPCGBridge);
DECLARE_CYCLE_STAT(TEXT("PCG Async Processing Time"), STAT_AuracronPCGAsyncProcessingTime, STATGROUP_AuracronPCGBridge);
DECLARE_CYCLE_STAT(TEXT("PCG Memory Optimization Time"), STAT_AuracronPCGMemoryOptimizationTime, STATGROUP_AuracronPCGBridge);
DECLARE_DWORD_COUNTER_STAT(TEXT("PCG Points Generated"), STAT_AuracronPCGPointsGenerated, STATGROUP_AuracronPCGBridge);
DECLARE_DWORD_COUNTER_STAT(TEXT("PCG Async Tasks"), STAT_AuracronPCGAsyncTasks, STATGROUP_AuracronPCGBridge);
DECLARE_MEMORY_STAT(TEXT("PCG Memory Usage"), STAT_AuracronPCGMemoryUsage, STATGROUP_AuracronPCGBridge);
DECLARE_MEMORY_STAT(TEXT("PCG Cache Memory Usage"), STAT_AuracronPCGCacheMemoryUsage, STATGROUP_AuracronPCGBridge);

/**
 * Performance monitoring data for PCG operations
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FPCGPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float GenerationTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 PointsGenerated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 MemoryUsage = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 AsyncTasksExecuted = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float AverageProcessingTime = 0.0f;

    FPCGPerformanceData()
    {
        GenerationTime = 0.0f;
        PointsGenerated = 0;
        MemoryUsage = 0;
        AsyncTasksExecuted = 0;
        AverageProcessingTime = 0.0f;
    }
};

/**
 * Async PCG generation parameters
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FPCGAsyncGenerationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    bool bUseAsyncGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    int32 MaxConcurrentTasks = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    float TimeSlicePerFrame = 0.016f; // 16ms

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    bool bEnableProgressCallback = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    bool bEnableMemoryOptimization = true;

    FPCGAsyncGenerationParams()
    {
        bUseAsyncGeneration = true;
        MaxConcurrentTasks = 4;
        TimeSlicePerFrame = 0.016f;
        bEnableProgressCallback = true;
        bEnableMemoryOptimization = true;
    }
};

/**
 * PCG Memory optimization settings
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FPCGMemoryOptimizationSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableMemoryPooling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MaxCacheSize = 100 * 1024 * 1024; // 100MB

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableDataStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float GCThreshold = 0.8f; // 80% memory usage

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableCompression = false;

    FPCGMemoryOptimizationSettings()
    {
        bEnableMemoryPooling = true;
        MaxCacheSize = 100 * 1024 * 1024;
        bEnableDataStreaming = true;
        GCThreshold = 0.8f;
        bEnableCompression = false;
    }
};

/**
 * Auracron PCG Bridge Module
 * Provides C++ bridge to expose PCG Framework APIs to Python for procedural content generation
 */
class AURACRONPCGBRIDGE_API FAuracronPCGBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;
    
    /** Check if module is available */
    static inline bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("AuracronPCGBridge");
    }
    
    /** Get module instance */
    static inline FAuracronPCGBridgeModule& Get()
    {
        return FModuleManager::LoadModuleChecked<FAuracronPCGBridgeModule>("AuracronPCGBridge");
    }

private:
    /** Initialize Python bindings */
    void InitializePythonBindings();
    
    /** Cleanup Python bindings */
    void CleanupPythonBindings();
    
    /** Register PCG element types */
    void RegisterPCGElements();
    
    /** Unregister PCG element types */
    void UnregisterPCGElements();
};

/**
 * Auracron PCG Bridge API
 * Main interface for Python to interact with PCG Framework
 */
class AURACRONPCGBRIDGE_API UAuracronPCGBridgeAPI : public UObject
{
    // TEMPORARILY COMMENTED: GENERATED_BODY()

public:
    UAuracronPCGBridgeAPI();

    // PCG Bridge Initialization
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool InitializePCGBridge();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static void ShutdownPCGBridge();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool IsPCGBridgeReady();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static FString GetPCGBridgeVersion();

    // PCG Graph Management
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGGraph* CreatePCGGraph(const FString& GraphName);
    
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SavePCGGraph(class UPCGGraph* Graph, const FString& PackagePath);
    
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGGraph* LoadPCGGraph(const FString& PackagePath);
    
    // PCG Graph Execution
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool ExecutePCGGraph(UPCGGraph* Graph);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool IsPCGGraphExecuting(UPCGGraph* Graph);

    // PCG Data Management
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static UPCGPointData* CreatePointData();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SetPointDataPoints(UPCGPointData* PointData, const TArray<FPCGPoint>& Points);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static TArray<FPCGPoint> GetPointDataPoints(UPCGPointData* PointData);

    // PCG Component Management
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGComponent* CreatePCGComponent(class AActor* Owner);
    
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SetPCGGraph(class UPCGComponent* Component, class UPCGGraph* Graph);
    
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool GeneratePCG(class UPCGComponent* Component, bool bForce = false);
    
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool CleanupPCG(class UPCGComponent* Component);
    
    // PCG Node Management
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGNode* CreatePCGNode(class UPCGGraph* Graph, const FString& NodeType, const FString& NodeName = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGNode* AddSurfaceSamplerNode(class UPCGGraph* Graph, const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGNode* AddTransformPointsNode(class UPCGGraph* Graph, const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGNode* AddStaticMeshSpawnerNode(class UPCGGraph* Graph, const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static class UPCGNode* AddLandscapeDataNode(class UPCGGraph* Graph, const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool ConnectPCGNodes(class UPCGNode* OutputNode, class UPCGNode* InputNode,
                               const FString& OutputPinName = TEXT(""), const FString& InputPinName = TEXT(""));

    // PCG Node Configuration
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SetSurfaceSamplerSettings(class UPCGNode* Node, float PointsPerSquaredMeter,
                                         const FVector& PointExtents, float Looseness);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SetTransformPointsSettings(class UPCGNode* Node, const FVector& OffsetMin, const FVector& OffsetMax,
                                          const FVector& RotationMin, const FVector& RotationMax,
                                          const FVector& ScaleMin, const FVector& ScaleMax,
                                          bool bAbsoluteRotation = true);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool AddStaticMeshToSpawner(class UPCGNode* Node, class UStaticMesh* StaticMesh, float Weight = 1.0f);
    
    // PCG Data Access
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static TArray<FVector> GetGeneratedPoints(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static TArray<FTransform> GetGeneratedTransforms(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static int32 GetGeneratedPointCount(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static TArray<class AActor*> GetGeneratedActors(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool IsPCGGenerationComplete(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static FString GetPCGComponentStatus(class UPCGComponent* Component);
    
    // PCG Graph Parameters
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SetPCGGraphParameter(class UPCGGraph* Graph, const FString& ParameterName, float Value);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SetPCGGraphParameterVector(class UPCGGraph* Graph, const FString& ParameterName, const FVector& Value);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static TArray<FString> GetPCGGraphParameters(class UPCGGraph* Graph);

    // PCG Metadata and Attributes
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool CreatePCGAttribute(class UPCGPointData* PointData, const FString& AttributeName, const FString& AttributeType);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge")
    static bool SetPointAttribute(class UPCGPointData* PointData, int32 PointIndex, const FString& AttributeName, float Value);

    // ========================================
    // UE 5.6 Advanced PCG Features
    // ========================================

    // Async PCG Generation
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Async")
    static bool GeneratePCGAsync(class UPCGComponent* Component, const FPCGAsyncGenerationParams& Params);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Async")
    static bool IsAsyncGenerationComplete(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Async")
    static float GetAsyncGenerationProgress(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Async")
    static bool CancelAsyncGeneration(class UPCGComponent* Component);

    // Performance Monitoring
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Performance")
    static FPCGPerformanceData GetPCGPerformanceData(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Performance")
    static bool EnablePCGProfiling(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Performance")
    static TArray<FString> GetPCGPerformanceReport();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Performance")
    static bool OptimizePCGMemoryUsage(class UPCGComponent* Component);

    // Custom PCG Elements
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Custom")
    static class UPCGNode* AddCustomBlueprintElement(class UPCGGraph* Graph, const FString& NodeName, class UClass* BlueprintClass);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Custom")
    static bool RegisterCustomPCGElement(const FString& ElementName, class UClass* ElementClass);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Custom")
    static TArray<FString> GetRegisteredCustomElements();

    // Runtime Parameter Modification
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Runtime")
    static bool SetRuntimeParameter(class UPCGComponent* Component, const FString& ParameterName, float Value);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Runtime")
    static bool SetRuntimeParameterVector(class UPCGComponent* Component, const FString& ParameterName, const FVector& Value);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Runtime")
    static bool RefreshPCGWithNewParameters(class UPCGComponent* Component);

    // Advanced Data Processing
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Advanced")
    static bool ProcessPCGDataInBatches(class UPCGComponent* Component, int32 BatchSize);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Advanced")
    static bool StreamPCGData(class UPCGComponent* Component, const FString& StreamingMode);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Advanced")
    static bool CompressPCGData(class UPCGComponent* Component, float CompressionRatio);

    // Memory Management
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Memory")
    static int32 GetPCGMemoryUsage(class UPCGComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Memory")
    static bool ClearPCGCache();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Bridge | Memory")
    static bool SetMemoryOptimizationSettings(const FPCGMemoryOptimizationSettings& Settings);

private:
    // Internal async processing
    static TMap<UPCGComponent*, TSharedPtr<class FPCGAsyncTask>> AsyncTasks;
    static FCriticalSection AsyncTasksMutex;
    static TMap<UPCGComponent*, FPCGPerformanceData> PerformanceDataCache;
    static bool bProfilingEnabled;
};
