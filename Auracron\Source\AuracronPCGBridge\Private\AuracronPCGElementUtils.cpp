// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Element Utilities Implementation
// Bridge 2.3: PCG Framework - Element Library

#include "AuracronPCGElementLibrary.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeInfo.h"
#include "LandscapeComponent.h"
#include "LandscapeDataAccess.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// Noise generation includes
#include "Math/RandomStream.h"

namespace AuracronPCGElementUtils
{
    // =============================================================================
    // NOISE GENERATION FUNCTIONS
    // =============================================================================

    float GenerateNoise(EAuracronPCGNoiseType NoiseType, const FVector& Position, float Scale, int32 Octaves)
    {
        FVector ScaledPosition = Position * Scale;
        float NoiseValue = 0.0f;

        switch (NoiseType)
        {
            case EAuracronPCGNoiseType::Perlin:
            {
                NoiseValue = FMath::PerlinNoise3D(ScaledPosition);
                break;
            }
            case EAuracronPCGNoiseType::Simplex:
            {
                // Robust 3D simplex noise implementation using UE5.6 math utilities
                const float F3 = 1.0f / 3.0f;
                const float G3 = 1.0f / 6.0f;
                
                float s = (ScaledPosition.X + ScaledPosition.Y + ScaledPosition.Z) * F3;
                int32 i = FMath::FloorToInt(ScaledPosition.X + s);
                int32 j = FMath::FloorToInt(ScaledPosition.Y + s);
                int32 k = FMath::FloorToInt(ScaledPosition.Z + s);
                
                float t = (i + j + k) * G3;
                float X0 = i - t;
                float Y0 = j - t;
                float Z0 = k - t;
                float x0 = ScaledPosition.X - X0;
                float y0 = ScaledPosition.Y - Y0;
                float z0 = ScaledPosition.Z - Z0;
                
                // Determine which simplex we are in
                int32 i1, j1, k1, i2, j2, k2;
                if (x0 >= y0)
                {
                    if (y0 >= z0) { i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 1; k2 = 0; }
                    else if (x0 >= z0) { i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 0; k2 = 1; }
                    else { i1 = 0; j1 = 0; k1 = 1; i2 = 1; j2 = 0; k2 = 1; }
                }
                else
                {
                    if (y0 < z0) { i1 = 0; j1 = 0; k1 = 1; i2 = 0; j2 = 1; k2 = 1; }
                    else if (x0 < z0) { i1 = 0; j1 = 1; k1 = 0; i2 = 0; j2 = 1; k2 = 1; }
                    else { i1 = 0; j1 = 1; k1 = 0; i2 = 1; j2 = 1; k2 = 0; }
                }
                
                float x1 = x0 - i1 + G3;
                float y1 = y0 - j1 + G3;
                float z1 = z0 - k1 + G3;
                float x2 = x0 - i2 + 2.0f * G3;
                float y2 = y0 - j2 + 2.0f * G3;
                float z2 = z0 - k2 + 2.0f * G3;
                float x3 = x0 - 1.0f + 3.0f * G3;
                float y3 = y0 - 1.0f + 3.0f * G3;
                float z3 = z0 - 1.0f + 3.0f * G3;
                
                // Calculate contributions from each corner
                float n0 = 0.0f, n1 = 0.0f, n2 = 0.0f, n3 = 0.0f;
                
                float t0 = 0.6f - x0 * x0 - y0 * y0 - z0 * z0;
                if (t0 >= 0.0f)
                {
                    t0 *= t0;
                    uint32 hash0 = FMath::Abs(FMath::RandHelper(i + j * 57 + k * 997));
                    FVector grad0 = FVector((hash0 & 0xFF) / 128.0f - 1.0f, ((hash0 >> 8) & 0xFF) / 128.0f - 1.0f, ((hash0 >> 16) & 0xFF) / 128.0f - 1.0f);
                    n0 = t0 * t0 * FVector::DotProduct(grad0, FVector(x0, y0, z0));
                }
                
                float t1 = 0.6f - x1 * x1 - y1 * y1 - z1 * z1;
                if (t1 >= 0.0f)
                {
                    t1 *= t1;
                    uint32 hash1 = FMath::Abs(FMath::RandHelper((i + i1) + (j + j1) * 57 + (k + k1) * 997));
                    FVector grad1 = FVector((hash1 & 0xFF) / 128.0f - 1.0f, ((hash1 >> 8) & 0xFF) / 128.0f - 1.0f, ((hash1 >> 16) & 0xFF) / 128.0f - 1.0f);
                    n1 = t1 * t1 * FVector::DotProduct(grad1, FVector(x1, y1, z1));
                }
                
                float t2 = 0.6f - x2 * x2 - y2 * y2 - z2 * z2;
                if (t2 >= 0.0f)
                {
                    t2 *= t2;
                    uint32 hash2 = FMath::Abs(FMath::RandHelper((i + i2) + (j + j2) * 57 + (k + k2) * 997));
                    FVector grad2 = FVector((hash2 & 0xFF) / 128.0f - 1.0f, ((hash2 >> 8) & 0xFF) / 128.0f - 1.0f, ((hash2 >> 16) & 0xFF) / 128.0f - 1.0f);
                    n2 = t2 * t2 * FVector::DotProduct(grad2, FVector(x2, y2, z2));
                }
                
                float t3 = 0.6f - x3 * x3 - y3 * y3 - z3 * z3;
                if (t3 >= 0.0f)
                {
                    t3 *= t3;
                    uint32 hash3 = FMath::Abs(FMath::RandHelper((i + 1) + (j + 1) * 57 + (k + 1) * 997));
                    FVector grad3 = FVector((hash3 & 0xFF) / 128.0f - 1.0f, ((hash3 >> 8) & 0xFF) / 128.0f - 1.0f, ((hash3 >> 16) & 0xFF) / 128.0f - 1.0f);
                    n3 = t3 * t3 * FVector::DotProduct(grad3, FVector(x3, y3, z3));
                }
                
                NoiseValue = 32.0f * (n0 + n1 + n2 + n3);
                break;
            }
            case EAuracronPCGNoiseType::Worley:
            {
                // Worley noise implementation
                FVector CellPosition = FVector(FMath::Floor(ScaledPosition.X), FMath::Floor(ScaledPosition.Y), FMath::Floor(ScaledPosition.Z));
                float MinDistance = FLT_MAX;

                for (int32 X = -1; X <= 1; X++)
                {
                    for (int32 Y = -1; Y <= 1; Y++)
                    {
                        for (int32 Z = -1; Z <= 1; Z++)
                        {
                            FVector NeighborCell = CellPosition + FVector(X, Y, Z);
                            FRandomStream RandomStream(FMath::FloorToInt(NeighborCell.X * 73 + NeighborCell.Y * 311 + NeighborCell.Z * 179));
                            FVector FeaturePoint = NeighborCell + FVector(RandomStream.FRand(), RandomStream.FRand(), RandomStream.FRand());
                            float Distance = FVector::Dist(ScaledPosition, FeaturePoint);
                            MinDistance = FMath::Min(MinDistance, Distance);
                        }
                    }
                }
                NoiseValue = MinDistance;
                break;
            }
            case EAuracronPCGNoiseType::Ridge:
            {
                NoiseValue = 1.0f - FMath::Abs(FMath::PerlinNoise3D(ScaledPosition));
                break;
            }
            case EAuracronPCGNoiseType::Fractal:
            {
                float Amplitude = 1.0f;
                float Frequency = 1.0f;
                float MaxValue = 0.0f;

                for (int32 i = 0; i < Octaves; i++)
                {
                    NoiseValue += FMath::PerlinNoise3D(ScaledPosition * Frequency) * Amplitude;
                    MaxValue += Amplitude;
                    Amplitude *= 0.5f;
                    Frequency *= 2.0f;
                }
                NoiseValue /= MaxValue;
                break;
            }
            case EAuracronPCGNoiseType::Turbulence:
            {
                NoiseValue = FMath::Abs(FMath::PerlinNoise3D(ScaledPosition));
                for (int32 i = 1; i < Octaves; i++)
                {
                    float Frequency = FMath::Pow(2.0f, i);
                    float Amplitude = FMath::Pow(0.5f, i);
                    NoiseValue += FMath::Abs(FMath::PerlinNoise3D(ScaledPosition * Frequency)) * Amplitude;
                }
                break;
            }
            default:
                NoiseValue = FMath::PerlinNoise3D(ScaledPosition);
                break;
        }

        return FMath::Clamp(NoiseValue, -1.0f, 1.0f);
    }

    // =============================================================================
    // DISTRIBUTION PATTERN FUNCTIONS
    // =============================================================================

    TArray<FVector> GenerateDistributionPattern(EAuracronPCGDistributionPattern Pattern, const FBox& Bounds, float Spacing, float Jitter)
    {
        TArray<FVector> Positions;
        FRandomStream RandomStream(FMath::Rand());

        switch (Pattern)
        {
            case EAuracronPCGDistributionPattern::Random:
            {
                int32 NumPoints = FMath::FloorToInt((Bounds.GetSize().X * Bounds.GetSize().Y) / (Spacing * Spacing));
                for (int32 i = 0; i < NumPoints; i++)
                {
                    FVector Position = FVector(
                        RandomStream.FRandRange(Bounds.Min.X, Bounds.Max.X),
                        RandomStream.FRandRange(Bounds.Min.Y, Bounds.Max.Y),
                        RandomStream.FRandRange(Bounds.Min.Z, Bounds.Max.Z)
                    );
                    Positions.Add(Position);
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Grid:
            {
                for (float X = Bounds.Min.X; X <= Bounds.Max.X; X += Spacing)
                {
                    for (float Y = Bounds.Min.Y; Y <= Bounds.Max.Y; Y += Spacing)
                    {
                        FVector Position(X, Y, (Bounds.Min.Z + Bounds.Max.Z) * 0.5f);
                        
                        // Apply jitter
                        if (Jitter > 0.0f)
                        {
                            Position.X += RandomStream.FRandRange(-Spacing * Jitter * 0.5f, Spacing * Jitter * 0.5f);
                            Position.Y += RandomStream.FRandRange(-Spacing * Jitter * 0.5f, Spacing * Jitter * 0.5f);
                        }
                        
                        Positions.Add(Position);
                    }
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Hexagonal:
            {
                float HexSpacing = Spacing * 0.866f; // Hexagonal spacing factor
                bool bOffsetRow = false;
                
                for (float Y = Bounds.Min.Y; Y <= Bounds.Max.Y; Y += HexSpacing)
                {
                    float XOffset = bOffsetRow ? Spacing * 0.5f : 0.0f;
                    for (float X = Bounds.Min.X + XOffset; X <= Bounds.Max.X; X += Spacing)
                    {
                        FVector Position(X, Y, (Bounds.Min.Z + Bounds.Max.Z) * 0.5f);
                        
                        // Apply jitter
                        if (Jitter > 0.0f)
                        {
                            Position.X += RandomStream.FRandRange(-Spacing * Jitter * 0.25f, Spacing * Jitter * 0.25f);
                            Position.Y += RandomStream.FRandRange(-Spacing * Jitter * 0.25f, Spacing * Jitter * 0.25f);
                        }
                        
                        Positions.Add(Position);
                    }
                    bOffsetRow = !bOffsetRow;
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Poisson:
            {
                // Robust Poisson disk sampling using spatial grid optimization for UE5.6
                TArray<FVector> ActiveList;
                TArray<TArray<int32>> SpatialGrid;
                
                // Calculate optimal cell size for O(1) neighbor lookup
                float CellSize = Spacing / FMath::Sqrt(2.0f);
                int32 GridWidth = FMath::CeilToInt(Bounds.GetSize().X / CellSize) + 1;
                int32 GridHeight = FMath::CeilToInt(Bounds.GetSize().Y / CellSize) + 1;
                int32 GridDepth = FMath::CeilToInt(Bounds.GetSize().Z / CellSize) + 1;
                
                // Initialize 3D spatial grid for fast neighbor queries
                SpatialGrid.SetNum(GridWidth * GridHeight * GridDepth);
                
                auto GetGridIndex = [&](const FVector& Point) -> int32
                {
                    FVector RelativePos = Point - Bounds.Min;
                    int32 X = FMath::Clamp(FMath::FloorToInt(RelativePos.X / CellSize), 0, GridWidth - 1);
                    int32 Y = FMath::Clamp(FMath::FloorToInt(RelativePos.Y / CellSize), 0, GridHeight - 1);
                    int32 Z = FMath::Clamp(FMath::FloorToInt(RelativePos.Z / CellSize), 0, GridDepth - 1);
                    return X + Y * GridWidth + Z * GridWidth * GridHeight;
                };
                
                auto IsValidPosition = [&](const FVector& NewPoint) -> bool
                {
                    if (!Bounds.IsInside(NewPoint)) return false;
                    
                    FVector RelativePos = NewPoint - Bounds.Min;
                    int32 CenterX = FMath::FloorToInt(RelativePos.X / CellSize);
                    int32 CenterY = FMath::FloorToInt(RelativePos.Y / CellSize);
                    int32 CenterZ = FMath::FloorToInt(RelativePos.Z / CellSize);
                    
                    // Check neighboring cells in 3D (27 cells total)
                    for (int32 X = FMath::Max(0, CenterX - 2); X <= FMath::Min(GridWidth - 1, CenterX + 2); X++)
                    {
                        for (int32 Y = FMath::Max(0, CenterY - 2); Y <= FMath::Min(GridHeight - 1, CenterY + 2); Y++)
                        {
                            for (int32 Z = FMath::Max(0, CenterZ - 2); Z <= FMath::Min(GridDepth - 1, CenterZ + 2); Z++)
                            {
                                int32 GridIndex = X + Y * GridWidth + Z * GridWidth * GridHeight;
                                for (int32 PointIndex : SpatialGrid[GridIndex])
                                {
                                    if (FVector::Dist(NewPoint, Positions[PointIndex]) < Spacing)
                                    {
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                    return true;
                };
                
                // Generate initial point with proper height sampling
                FVector InitialPoint = Bounds.GetCenter();
                float SampledHeight;
                if (SampleLandscapeHeight(InitialPoint, SampledHeight))
                {
                    InitialPoint.Z = SampledHeight;
                }
                
                Positions.Add(InitialPoint);
                ActiveList.Add(InitialPoint);
                SpatialGrid[GetGridIndex(InitialPoint)].Add(0);
                
                // Main Poisson disk sampling loop with adaptive sampling
                const int32 MaxAttempts = 30;
                while (ActiveList.Num() > 0)
                {
                    int32 RandomIndex = RandomStream.RandRange(0, ActiveList.Num() - 1);
                    FVector CurrentPoint = ActiveList[RandomIndex];
                    bool bFoundValidPoint = false;
                    
                    for (int32 Attempt = 0; Attempt < MaxAttempts; Attempt++)
                    {
                        // Generate candidate point in annulus around current point
                        float Angle = RandomStream.FRand() * 2.0f * PI;
                        float Phi = RandomStream.FRand() * PI; // For 3D distribution
                        float Distance = RandomStream.FRandRange(Spacing, 2.0f * Spacing);
                        
                        FVector NewPoint = CurrentPoint + FVector(
                            FMath::Sin(Phi) * FMath::Cos(Angle) * Distance,
                            FMath::Sin(Phi) * FMath::Sin(Angle) * Distance,
                            FMath::Cos(Phi) * Distance * 0.1f // Reduced Z variation for terrain following
                        );
                        
                        // Sample landscape height for terrain conformance
                        float TerrainHeight;
                        if (SampleLandscapeHeight(NewPoint, TerrainHeight))
                        {
                            NewPoint.Z = TerrainHeight + RandomStream.FRandRange(-10.0f, 50.0f); // Small height variation
                        }
                        
                        if (IsValidPosition(NewPoint))
                        {
                            int32 NewPointIndex = Positions.Add(NewPoint);
                            ActiveList.Add(NewPoint);
                            SpatialGrid[GetGridIndex(NewPoint)].Add(NewPointIndex);
                            bFoundValidPoint = true;
                            break;
                        }
                    }
                    
                    if (!bFoundValidPoint)
                    {
                        ActiveList.RemoveAt(RandomIndex);
                    }
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Spiral:
            {
                FVector Center = Bounds.GetCenter();
                float MaxRadius = FMath::Min(Bounds.GetSize().X, Bounds.GetSize().Y) * 0.5f;
                float AngleStep = 2.0f * PI / 8.0f; // 8 points per revolution
                
                for (float Radius = Spacing; Radius <= MaxRadius; Radius += Spacing)
                {
                    float Angle = (Radius / Spacing) * AngleStep;
                    FVector Position = Center + FVector(FMath::Cos(Angle) * Radius, FMath::Sin(Angle) * Radius, 0.0f);
                    
                    if (Bounds.IsInside(Position))
                    {
                        Positions.Add(Position);
                    }
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Radial:
            {
                FVector Center = Bounds.GetCenter();
                float MaxRadius = FMath::Min(Bounds.GetSize().X, Bounds.GetSize().Y) * 0.5f;
                int32 NumRings = FMath::FloorToInt(MaxRadius / Spacing);
                
                for (int32 Ring = 1; Ring <= NumRings; Ring++)
                {
                    float Radius = Ring * Spacing;
                    int32 NumPointsInRing = FMath::Max(1, FMath::FloorToInt(2.0f * PI * Radius / Spacing));
                    
                    for (int32 Point = 0; Point < NumPointsInRing; Point++)
                    {
                        float Angle = (2.0f * PI * Point) / NumPointsInRing;
                        FVector Position = Center + FVector(FMath::Cos(Angle) * Radius, FMath::Sin(Angle) * Radius, 0.0f);
                        
                        if (Bounds.IsInside(Position))
                        {
                            Positions.Add(Position);
                        }
                    }
                }
                break;
            }
            default:
                // Fallback to grid pattern
                return GenerateDistributionPattern(EAuracronPCGDistributionPattern::Grid, Bounds, Spacing, Jitter);
        }

        return Positions;
    }

    // =============================================================================
    // BIOME VALIDATION FUNCTIONS
    // =============================================================================

    bool IsPointValidForBiome(const FVector& Position, EAuracronPCGBiomeType BiomeType, float Intensity)
    {
        // Comprehensive biome validation using multiple environmental factors
        
        // Sample landscape height and calculate slope
        float LandscapeHeight = 0.0f;
        bool bHasLandscape = SampleLandscapeHeight(Position, LandscapeHeight);
        
        FVector SurfaceNormal = CalculateSurfaceNormal(Position, 100.0f);
        float Slope = CalculateSlope(SurfaceNormal);
        
        // Generate multiple noise layers for environmental factors
        float TemperatureNoise = GenerateNoise(EAuracronPCGNoiseType::Perlin, Position * 0.0005f, 1.0f, 4);
        float HumidityNoise = GenerateNoise(EAuracronPCGNoiseType::Simplex, Position * 0.0008f, 1.0f, 3);
        float ElevationFactor = bHasLandscape ? FMath::Clamp((LandscapeHeight - 0.0f) / 2000.0f, -1.0f, 1.0f) : Position.Z / 1000.0f;
        float SlopeFactor = FMath::Clamp(Slope / 45.0f, 0.0f, 1.0f); // Normalize slope to 0-1
        
        // Sample landscape material layers if available
        float GrassWeight = 0.0f;
        float RockWeight = 0.0f;
        float SandWeight = 0.0f;
        float SnowWeight = 0.0f;
        
        SampleLandscapeMaterial(Position, TEXT("Grass"), GrassWeight);
        SampleLandscapeMaterial(Position, TEXT("Rock"), RockWeight);
        SampleLandscapeMaterial(Position, TEXT("Sand"), SandWeight);
        SampleLandscapeMaterial(Position, TEXT("Snow"), SnowWeight);
        
        // Calculate biome suitability based on environmental factors
        float BiomeSuitability = 0.0f;
        float IntensityModifier = FMath::Clamp(Intensity, 0.1f, 1.0f);
        
        switch (BiomeType)
        {
            case EAuracronPCGBiomeType::Forest:
            {
                // Forests prefer moderate temperature, high humidity, low-medium elevation, gentle slopes
                float TemperatureSuit = 1.0f - FMath::Abs(TemperatureNoise); // Prefer moderate temps
                float HumiditySuit = FMath::Clamp(HumidityNoise + 0.3f, 0.0f, 1.0f); // Prefer high humidity
                float ElevationSuit = 1.0f - FMath::Clamp(FMath::Abs(ElevationFactor), 0.0f, 1.0f); // Prefer low-medium elevation
                float SlopeSuit = 1.0f - SlopeFactor; // Prefer gentle slopes
                float MaterialSuit = GrassWeight * 0.8f + (1.0f - RockWeight) * 0.2f;
                
                BiomeSuitability = (TemperatureSuit * 0.25f + HumiditySuit * 0.3f + ElevationSuit * 0.2f + 
                                  SlopeSuit * 0.15f + MaterialSuit * 0.1f) * IntensityModifier;
                return BiomeSuitability > 0.6f;
            }
            case EAuracronPCGBiomeType::Desert:
            {
                // Deserts prefer high temperature, low humidity, low-medium elevation, any slope
                float TemperatureSuit = FMath::Clamp(TemperatureNoise + 0.4f, 0.0f, 1.0f); // Prefer hot
                float HumiditySuit = FMath::Clamp(-HumidityNoise + 0.2f, 0.0f, 1.0f); // Prefer dry
                float ElevationSuit = ElevationFactor > -0.5f ? 1.0f : 0.0f; // Above sea level
                float MaterialSuit = SandWeight * 0.7f + RockWeight * 0.3f;
                
                BiomeSuitability = (TemperatureSuit * 0.35f + HumiditySuit * 0.35f + ElevationSuit * 0.2f + 
                                   MaterialSuit * 0.1f) * IntensityModifier;
                 return BiomeSuitability > 0.65f;
             }
             case EAuracronPCGBiomeType::Mountain:
             {
                 // Mountains prefer high elevation, steep slopes, cold temperature, low humidity
                 float TemperatureSuit = FMath::Clamp(-TemperatureNoise + 0.2f, 0.0f, 1.0f); // Prefer cold
                 float HumiditySuit = FMath::Clamp(-HumidityNoise + 0.3f, 0.0f, 1.0f); // Prefer dry
                 float ElevationSuit = FMath::Clamp(ElevationFactor, 0.0f, 1.0f); // Prefer high elevation
                 float SlopeSuit = SlopeFactor; // Prefer steep slopes
                 float MaterialSuit = RockWeight * 0.8f + SnowWeight * 0.2f;
                 
                 BiomeSuitability = (TemperatureSuit * 0.2f + HumiditySuit * 0.15f + ElevationSuit * 0.4f + 
                                   SlopeSuit * 0.15f + MaterialSuit * 0.1f) * IntensityModifier;
                 return BiomeSuitability > 0.7f && ElevationFactor > 0.3f;
             }
             case EAuracronPCGBiomeType::Ocean:
             {
                 // Ocean requires below sea level or very low elevation
                 float ElevationSuit = ElevationFactor < -0.1f ? 1.0f : FMath::Clamp(-ElevationFactor * 2.0f, 0.0f, 1.0f);
                 float HumiditySuit = FMath::Clamp(HumidityNoise + 0.5f, 0.0f, 1.0f); // High humidity near water
                 float SlopeSuit = 1.0f - SlopeFactor; // Prefer flat areas
                 
                 BiomeSuitability = (ElevationSuit * 0.6f + HumiditySuit * 0.25f + SlopeSuit * 0.15f) * IntensityModifier;
                 return BiomeSuitability > 0.8f;
             }
            default:
                return true;
        }
    }

    // =============================================================================
    // SURFACE ANALYSIS FUNCTIONS
    // =============================================================================

    FVector CalculateSurfaceNormal(const FVector& Position, float SampleDistance)
    {
        // Sample height at nearby points to calculate normal
        float HeightCenter, HeightX, HeightY;
        
        if (!SampleLandscapeHeight(Position, HeightCenter) ||
            !SampleLandscapeHeight(Position + FVector(SampleDistance, 0, 0), HeightX) ||
            !SampleLandscapeHeight(Position + FVector(0, SampleDistance, 0), HeightY))
        {
            return FVector::UpVector; // Default to up if sampling fails
        }
        
        FVector TangentX(SampleDistance, 0, HeightX - HeightCenter);
        FVector TangentY(0, SampleDistance, HeightY - HeightCenter);
        
        return FVector::CrossProduct(TangentY, TangentX).GetSafeNormal();
    }

    float CalculateSlope(const FVector& Normal)
    {
        return FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(Normal, FVector::UpVector)));
    }

    bool SampleLandscapeHeight(const FVector& Position, float& OutHeight)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(AuracronPCGElementUtils::SampleLandscapeHeight);

        // Real landscape height sampling using UE5.6 APIs
        if (UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull))
        {
            // Find landscape actor in the world
            for (TActorIterator<ALandscape> LandscapeItr(World); LandscapeItr; ++LandscapeItr)
            {
                ALandscape* Landscape = *LandscapeItr;
                if (Landscape && Landscape->GetLandscapeInfo())
                {
                    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();

                    // Convert world position to landscape coordinates
                    FVector LandscapePosition = Landscape->GetTransform().InverseTransformPosition(Position);

                    // Sample height using landscape info
                    float SampledHeight;
                    if (LandscapeInfo->GetHeightAtLocation(Position, SampledHeight))
                    {
                        OutHeight = SampledHeight;
                        return true;
                    }

                    // Fallback: Use landscape component sampling
                    if (ULandscapeComponent* Component = LandscapeInfo->GetLandscapeComponentAtLocation(Position))
                    {
                        FVector LocalPosition = Component->GetComponentTransform().InverseTransformPosition(Position);

                        // Sample from heightmap data
                        if (Component->GetHeightmapTexture())
                        {
                            int32 ComponentSizeQuads = Component->ComponentSizeQuads;
                            float ScaleX = LocalPosition.X / Component->GetComponentTransform().GetScale3D().X;
                            float ScaleY = LocalPosition.Y / Component->GetComponentTransform().GetScale3D().Y;

                            // Clamp to valid range
                            ScaleX = FMath::Clamp(ScaleX, 0.0f, 1.0f);
                            ScaleY = FMath::Clamp(ScaleY, 0.0f, 1.0f);

                            // Calculate heightmap coordinates
                            int32 HeightmapX = FMath::FloorToInt(ScaleX * ComponentSizeQuads);
                            int32 HeightmapY = FMath::FloorToInt(ScaleY * ComponentSizeQuads);

                            // Sample height from component data
                            OutHeight = Component->GetHeightAtLocation(Position);
                            return true;
                        }
                    }
                }
            }
        }

        // Fallback: Use line trace
        if (World)
        {
            FHitResult HitResult;
            FVector TraceStart = Position + FVector(0, 0, 10000);
            FVector TraceEnd = Position - FVector(0, 0, 10000);

            FCollisionQueryParams QueryParams;
            QueryParams.bTraceComplex = false;

            if (World->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
            {
                OutHeight = HitResult.Location.Z;
                return true;
            }
        }

        OutHeight = 0.0f;
        return false;
    }

    bool SampleLandscapeMaterial(const FVector& Position, const FName& LayerName, float& OutWeight)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(AuracronPCGElementUtils::SampleLandscapeMaterial);

        // Real landscape material sampling using UE5.6 APIs
        if (UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull))
        {
            // Find landscape actor in the world
            for (TActorIterator<ALandscape> LandscapeItr(World); LandscapeItr; ++LandscapeItr)
            {
                ALandscape* Landscape = *LandscapeItr;
                if (Landscape && Landscape->GetLandscapeInfo())
                {
                    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();

                    // Get landscape component at position
                    if (ULandscapeComponent* Component = LandscapeInfo->GetLandscapeComponentAtLocation(Position))
                    {
                        // Find the layer info for the specified layer name
                        ULandscapeLayerInfoObject* LayerInfo = nullptr;
                        for (const FLandscapeInfoLayerSettings& LayerSettings : LandscapeInfo->Layers)
                        {
                            if (LayerSettings.LayerInfoObj && LayerSettings.LayerName == LayerName)
                            {
                                LayerInfo = LayerSettings.LayerInfoObj;
                                break;
                            }
                        }

                        if (LayerInfo)
                        {
                            // Sample weight from layer
                            TArray<uint8> WeightData;
                            TArray<UTexture2D*> WeightTextures;
                            Component->GetLayerWeightData(LayerInfo, WeightData, WeightTextures);

                            if (WeightData.Num() > 0)
                            {
                                // Convert world position to component local coordinates
                                FVector LocalPosition = Component->GetComponentTransform().InverseTransformPosition(Position);

                                // Calculate texture coordinates
                                int32 ComponentSizeQuads = Component->ComponentSizeQuads;
                                float ScaleX = LocalPosition.X / Component->GetComponentTransform().GetScale3D().X;
                                float ScaleY = LocalPosition.Y / Component->GetComponentTransform().GetScale3D().Y;

                                // Clamp to valid range
                                ScaleX = FMath::Clamp(ScaleX, 0.0f, 1.0f);
                                ScaleY = FMath::Clamp(ScaleY, 0.0f, 1.0f);

                                // Calculate weight map coordinates
                                int32 WeightX = FMath::FloorToInt(ScaleX * ComponentSizeQuads);
                                int32 WeightY = FMath::FloorToInt(ScaleY * ComponentSizeQuads);
                                int32 WeightIndex = WeightY * (ComponentSizeQuads + 1) + WeightX;

                                if (WeightIndex >= 0 && WeightIndex < WeightData.Num())
                                {
                                    // Convert byte weight to float (0-255 -> 0.0-1.0)
                                    OutWeight = static_cast<float>(WeightData[WeightIndex]) / 255.0f;
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }

        OutWeight = 0.0f;
        return false;
    }
}
