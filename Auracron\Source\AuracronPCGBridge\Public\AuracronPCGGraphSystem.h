// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Graph System Core Header
// Bridge 2.2: PCG Framework - Graph System Core

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/World.h"
#include "AuracronPCGFramework.h"

// PCG Framework includes for UE5.6
#include "PCGGraph.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "PCGEdge.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"

// Serialization includes
#include "Serialization/Archive.h"
#include "Serialization/ObjectAndNameAsStringProxyArchive.h"
#include "HAL/PlatformFilemanager.h"

#include "AuracronPCGGraphSystem.generated.h"

// Forward declarations
class UAuracronPCGGraphManager;
class UAuracronPCGNodeBase;
class FAuracronPCGGraphValidator;

// Graph execution state
UENUM(BlueprintType)
enum class EAuracronPCGGraphState : uint8
{
    Idle            UMETA(DisplayName = "Idle"),
    Compiling       UMETA(DisplayName = "Compiling"),
    Executing       UMETA(DisplayName = "Executing"),
    Completed       UMETA(DisplayName = "Completed"),
    Error           UMETA(DisplayName = "Error"),
    Cancelled       UMETA(DisplayName = "Cancelled")
};

// Pin connection types
UENUM(BlueprintType)
enum class EAuracronPCGPinType : uint8
{
    Input           UMETA(DisplayName = "Input"),
    Output          UMETA(DisplayName = "Output"),
    Parameter       UMETA(DisplayName = "Parameter"),
    Execution       UMETA(DisplayName = "Execution")
};

// Graph validation result
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGGraphValidationResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    bool bIsValid = false;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> Errors;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> Warnings;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    int32 NodeCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    int32 EdgeCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    bool bHasCycles = false;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> DisconnectedNodes;

    FAuracronPCGGraphValidationResult()
    {
        bIsValid = false;
        NodeCount = 0;
        EdgeCount = 0;
        bHasCycles = false;
    }
};

// Graph execution statistics
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGGraphExecutionStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float TotalExecutionTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float CompilationTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 NodesExecuted = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 PointsGenerated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float MemoryUsedMB = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    FDateTime StartTime;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    FDateTime EndTime;

    FAuracronPCGGraphExecutionStats()
    {
        TotalExecutionTime = 0.0f;
        CompilationTime = 0.0f;
        NodesExecuted = 0;
        PointsGenerated = 0;
        MemoryUsedMB = 0.0f;
        StartTime = FDateTime::Now();
        EndTime = StartTime;
    }
};

// Pin connection information
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGPinConnection
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connection")
    FString SourceNodeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connection")
    FString SourcePinName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connection")
    FString TargetNodeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connection")
    FString TargetPinName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connection")
    EAuracronPCGPinType PinType = EAuracronPCGPinType::Input;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connection")
    bool bIsValid = true;

    FAuracronPCGPinConnection()
    {
        SourceNodeId = TEXT("");
        SourcePinName = TEXT("");
        TargetNodeId = TEXT("");
        TargetPinName = TEXT("");
        PinType = EAuracronPCGPinType::Input;
        bIsValid = true;
    }
};

// Graph serialization data
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGGraphSerializationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Serialization")
    FString GraphName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Serialization")
    FString GraphVersion;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Serialization")
    TArray<FString> NodeData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Serialization")
    TArray<FAuracronPCGPinConnection> Connections;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Serialization")
    TMap<FString, FString> GraphParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Serialization")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Serialization")
    FDateTime LastModified;

    FAuracronPCGGraphSerializationData()
    {
        GraphName = TEXT("Untitled Graph");
        GraphVersion = TEXT("1.0");
        CreationTime = FDateTime::Now();
        LastModified = CreationTime;
    }
};

/**
 * Enhanced PCG Graph wrapper for AURACRON framework
 * Provides advanced graph management, validation, and execution capabilities
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGGraphWrapper : public UObject
{
    GENERATED_BODY()

public:
    UAuracronPCGGraphWrapper();

    // Graph management
    UFUNCTION(BlueprintCallable, Category = "PCG Graph")
    bool InitializeFromPCGGraph(UPCGGraph* SourceGraph);

    UFUNCTION(BlueprintCallable, Category = "PCG Graph")
    UPCGGraph* GetPCGGraph() const { return PCGGraph; }

    UFUNCTION(BlueprintCallable, Category = "PCG Graph")
    bool CreateNewGraph(const FString& GraphName);

    UFUNCTION(BlueprintCallable, Category = "PCG Graph")
    bool CloneGraph(UAuracronPCGGraphWrapper* SourceGraph);

    // Node management
    UFUNCTION(BlueprintCallable, Category = "PCG Nodes")
    FString AddNode(TSubclassOf<UPCGSettings> NodeClass, const FVector2D& Position = FVector2D::ZeroVector);

    UFUNCTION(BlueprintCallable, Category = "PCG Nodes")
    bool RemoveNode(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "PCG Nodes")
    bool MoveNode(const FString& NodeId, const FVector2D& NewPosition);

    UFUNCTION(BlueprintCallable, Category = "PCG Nodes")
    TArray<FString> GetAllNodeIds() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Nodes")
    UPCGNode* GetNodeById(const FString& NodeId) const;

    // Connection management
    UFUNCTION(BlueprintCallable, Category = "PCG Connections")
    bool ConnectPins(const FString& SourceNodeId, const FString& SourcePinName, 
                     const FString& TargetNodeId, const FString& TargetPinName);

    UFUNCTION(BlueprintCallable, Category = "PCG Connections")
    bool DisconnectPins(const FString& SourceNodeId, const FString& SourcePinName, 
                        const FString& TargetNodeId, const FString& TargetPinName);

    UFUNCTION(BlueprintCallable, Category = "PCG Connections")
    TArray<FAuracronPCGPinConnection> GetAllConnections() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Connections")
    bool IsValidConnection(const FString& SourceNodeId, const FString& SourcePinName, 
                          const FString& TargetNodeId, const FString& TargetPinName) const;

    // Graph validation
    UFUNCTION(BlueprintCallable, Category = "PCG Validation")
    FAuracronPCGGraphValidationResult ValidateGraph() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Validation")
    bool HasCycles() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Validation")
    TArray<FString> FindDisconnectedNodes() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Validation")
    bool ValidateNodeConnections(const FString& NodeId, TArray<FString>& ValidationErrors) const;

    // Graph execution
    UFUNCTION(BlueprintCallable, Category = "PCG Execution")
    bool ExecuteGraph(AActor* TargetActor = nullptr);

    UFUNCTION(BlueprintCallable, Category = "PCG Execution")
    bool ExecuteGraphAsync(AActor* TargetActor = nullptr);

    UFUNCTION(BlueprintCallable, Category = "PCG Execution")
    bool CancelExecution();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Execution")
    EAuracronPCGGraphState GetExecutionState() const { return ExecutionState; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Execution")
    float GetExecutionProgress() const { return ExecutionProgress; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Execution")
    FAuracronPCGGraphExecutionStats GetExecutionStats() const { return ExecutionStats; }

    // Serialization
    UFUNCTION(BlueprintCallable, Category = "PCG Serialization")
    bool SaveGraphToFile(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "PCG Serialization")
    bool LoadGraphFromFile(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "PCG Serialization")
    FString SerializeGraphToString() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Serialization")
    bool DeserializeGraphFromString(const FString& SerializedData);

    UFUNCTION(BlueprintCallable, Category = "PCG Serialization")
    FAuracronPCGGraphSerializationData GetSerializationData() const;

    // Graph properties
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Properties")
    FString GetGraphName() const { return GraphName; }

    UFUNCTION(BlueprintCallable, Category = "PCG Properties")
    void SetGraphName(const FString& NewName) { GraphName = NewName; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Properties")
    int32 GetNodeCount() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Properties")
    int32 GetConnectionCount() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Properties")
    bool IsGraphModified() const { return bIsModified; }

    UFUNCTION(BlueprintCallable, Category = "PCG Properties")
    void MarkAsModified() { bIsModified = true; LastModified = FDateTime::Now(); }

    // Graph parameters
    UFUNCTION(BlueprintCallable, Category = "PCG Parameters")
    bool SetGraphParameter(const FString& ParameterName, const FString& ParameterValue);

    UFUNCTION(BlueprintCallable, Category = "PCG Parameters")
    FString GetGraphParameter(const FString& ParameterName, const FString& DefaultValue = TEXT("")) const;

    UFUNCTION(BlueprintCallable, Category = "PCG Parameters")
    TMap<FString, FString> GetAllGraphParameters() const { return GraphParameters; }

    UFUNCTION(BlueprintCallable, Category = "PCG Parameters")
    bool RemoveGraphParameter(const FString& ParameterName);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "PCG Utilities")
    void ClearGraph();

    UFUNCTION(BlueprintCallable, Category = "PCG Utilities")
    UAuracronPCGGraphWrapper* DuplicateGraph() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Utilities")
    bool MergeGraph(UAuracronPCGGraphWrapper* OtherGraph);

    UFUNCTION(BlueprintCallable, Category = "PCG Utilities")
    TArray<FString> GetNodesByType(TSubclassOf<UPCGSettings> NodeType) const;

protected:
    // Core graph data
    UPROPERTY()
    UPCGGraph* PCGGraph;

    UPROPERTY()
    FString GraphName;

    UPROPERTY()
    TMap<FString, FString> GraphParameters;

    UPROPERTY()
    TMap<FString, UPCGNode*> NodeIdMap;

    // Execution state
    UPROPERTY()
    EAuracronPCGGraphState ExecutionState;

    UPROPERTY()
    float ExecutionProgress;

    UPROPERTY()
    FAuracronPCGGraphExecutionStats ExecutionStats;

    // Modification tracking
    UPROPERTY()
    bool bIsModified;

    UPROPERTY()
    FDateTime CreationTime;

    UPROPERTY()
    FDateTime LastModified;

    // Internal methods
    virtual void InitializeGraph();
    virtual void UpdateNodeIdMap();
    virtual FString GenerateUniqueNodeId() const;
    virtual bool ValidateNodeType(TSubclassOf<UPCGSettings> NodeClass) const;
    virtual void OnGraphModified();
    virtual void UpdateExecutionProgress();

private:
    // Internal state
    mutable FCriticalSection GraphLock;
    static int32 GraphIdCounter;
};

/**
 * Graph validator for AURACRON PCG graphs
 * Provides comprehensive validation and analysis capabilities
 */
class AURACRONPCGFRAMEWORK_API FAuracronPCGGraphValidator
{
public:
    FAuracronPCGGraphValidator();
    ~FAuracronPCGGraphValidator() = default;

    // Validation methods
    static FAuracronPCGGraphValidationResult ValidateGraph(const UAuracronPCGGraphWrapper* GraphWrapper);
    static bool DetectCycles(const UAuracronPCGGraphWrapper* GraphWrapper);
    static TArray<FString> FindDisconnectedNodes(const UAuracronPCGGraphWrapper* GraphWrapper);
    static bool ValidateNodeConnections(const UAuracronPCGGraphWrapper* GraphWrapper, const FString& NodeId, TArray<FString>& ValidationErrors);
    static bool ValidatePinConnection(const UPCGNode* SourceNode, const FString& SourcePinName, 
                                     const UPCGNode* TargetNode, const FString& TargetPinName);

    // Analysis methods
    static TArray<UPCGNode*> GetExecutionOrder(const UAuracronPCGGraphWrapper* GraphWrapper);
    static TMap<FString, int32> AnalyzeNodeDependencies(const UAuracronPCGGraphWrapper* GraphWrapper);
    static float EstimateExecutionComplexity(const UAuracronPCGGraphWrapper* GraphWrapper);

private:
    // Internal validation helpers
    static void PerformDepthFirstSearch(const UPCGNode* Node, TSet<const UPCGNode*>& Visited, 
                                       TSet<const UPCGNode*>& RecursionStack, bool& bHasCycle);
    static bool IsValidPinType(const FPCGPinProperties& SourcePin, const FPCGPinProperties& TargetPin);
    static int32 CalculateNodeComplexity(const UPCGNode* Node);
};

// Inline implementations for performance-critical functions
FORCEINLINE int32 UAuracronPCGGraphWrapper::GetNodeCount() const
{
    return PCGGraph ? PCGGraph->GetNodes().Num() : 0;
}

FORCEINLINE int32 UAuracronPCGGraphWrapper::GetConnectionCount() const
{
    if (!PCGGraph)
    {
        return 0;
    }

    int32 ConnectionCount = 0;
    for (const UPCGNode* Node : PCGGraph->GetNodes())
    {
        if (Node)
        {
            ConnectionCount += Node->GetInputPins().Num() + Node->GetOutputPins().Num();
        }
    }
    return ConnectionCount;
}
