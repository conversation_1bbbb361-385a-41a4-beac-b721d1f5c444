// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronVoiceBridge_init() {}
	AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature();
	AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature();
	AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature();
	AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronVoiceBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronVoiceBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronVoiceBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronVoiceBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x63DECF16,
				0x299CAF23,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronVoiceBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronVoiceBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronVoiceBridge(Z_Construct_UPackage__Script_AuracronVoiceBridge, TEXT("/Script/AuracronVoiceBridge"), Z_Registration_Info_UPackage__Script_AuracronVoiceBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x63DECF16, 0x299CAF23));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
