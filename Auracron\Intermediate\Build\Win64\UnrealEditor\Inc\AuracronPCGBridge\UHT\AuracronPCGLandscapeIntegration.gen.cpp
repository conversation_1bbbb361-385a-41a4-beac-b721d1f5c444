// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGLandscapeIntegration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGLandscapeIntegration() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
LANDSCAPE_API UClass* Z_Construct_UClass_ALandscape_NoRegister();
LANDSCAPE_API UClass* Z_Construct_UClass_ULandscapeLayerInfoObject_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGLandscapeSamplingMode *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode;
static UEnum* EAuracronPCGLandscapeSamplingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGLandscapeSamplingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLandscapeSamplingMode>()
{
	return EAuracronPCGLandscapeSamplingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape sampling modes\n" },
#endif
		{ "Complete.DisplayName", "Complete Sampling" },
		{ "Complete.Name", "EAuracronPCGLandscapeSamplingMode::Complete" },
		{ "CurvatureOnly.DisplayName", "Curvature Only" },
		{ "CurvatureOnly.Name", "EAuracronPCGLandscapeSamplingMode::CurvatureOnly" },
		{ "Height.DisplayName", "Height Only" },
		{ "Height.Name", "EAuracronPCGLandscapeSamplingMode::Height" },
		{ "HeightAndLayers.DisplayName", "Height and Layers" },
		{ "HeightAndLayers.Name", "EAuracronPCGLandscapeSamplingMode::HeightAndLayers" },
		{ "HeightAndNormal.DisplayName", "Height and Normal" },
		{ "HeightAndNormal.Name", "EAuracronPCGLandscapeSamplingMode::HeightAndNormal" },
		{ "LayersOnly.DisplayName", "Layers Only" },
		{ "LayersOnly.Name", "EAuracronPCGLandscapeSamplingMode::LayersOnly" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
		{ "NormalOnly.DisplayName", "Normal Only" },
		{ "NormalOnly.Name", "EAuracronPCGLandscapeSamplingMode::NormalOnly" },
		{ "SlopeOnly.DisplayName", "Slope Only" },
		{ "SlopeOnly.Name", "EAuracronPCGLandscapeSamplingMode::SlopeOnly" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape sampling modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGLandscapeSamplingMode::Height", (int64)EAuracronPCGLandscapeSamplingMode::Height },
		{ "EAuracronPCGLandscapeSamplingMode::HeightAndNormal", (int64)EAuracronPCGLandscapeSamplingMode::HeightAndNormal },
		{ "EAuracronPCGLandscapeSamplingMode::HeightAndLayers", (int64)EAuracronPCGLandscapeSamplingMode::HeightAndLayers },
		{ "EAuracronPCGLandscapeSamplingMode::Complete", (int64)EAuracronPCGLandscapeSamplingMode::Complete },
		{ "EAuracronPCGLandscapeSamplingMode::LayersOnly", (int64)EAuracronPCGLandscapeSamplingMode::LayersOnly },
		{ "EAuracronPCGLandscapeSamplingMode::NormalOnly", (int64)EAuracronPCGLandscapeSamplingMode::NormalOnly },
		{ "EAuracronPCGLandscapeSamplingMode::SlopeOnly", (int64)EAuracronPCGLandscapeSamplingMode::SlopeOnly },
		{ "EAuracronPCGLandscapeSamplingMode::CurvatureOnly", (int64)EAuracronPCGLandscapeSamplingMode::CurvatureOnly },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGLandscapeSamplingMode",
	"EAuracronPCGLandscapeSamplingMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGLandscapeSamplingMode *******************************************

// ********** Begin Enum EAuracronPCGLandscapeBlendMode ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode;
static UEnum* EAuracronPCGLandscapeBlendMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGLandscapeBlendMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLandscapeBlendMode>()
{
	return EAuracronPCGLandscapeBlendMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Add.DisplayName", "Add" },
		{ "Add.Name", "EAuracronPCGLandscapeBlendMode::Add" },
		{ "BlueprintType", "true" },
		{ "ColorBurn.DisplayName", "Color Burn" },
		{ "ColorBurn.Name", "EAuracronPCGLandscapeBlendMode::ColorBurn" },
		{ "ColorDodge.DisplayName", "Color Dodge" },
		{ "ColorDodge.Name", "EAuracronPCGLandscapeBlendMode::ColorDodge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape blending modes\n" },
#endif
		{ "Darken.DisplayName", "Darken" },
		{ "Darken.Name", "EAuracronPCGLandscapeBlendMode::Darken" },
		{ "Difference.DisplayName", "Difference" },
		{ "Difference.Name", "EAuracronPCGLandscapeBlendMode::Difference" },
		{ "Exclusion.DisplayName", "Exclusion" },
		{ "Exclusion.Name", "EAuracronPCGLandscapeBlendMode::Exclusion" },
		{ "HardLight.DisplayName", "Hard Light" },
		{ "HardLight.Name", "EAuracronPCGLandscapeBlendMode::HardLight" },
		{ "Lighten.DisplayName", "Lighten" },
		{ "Lighten.Name", "EAuracronPCGLandscapeBlendMode::Lighten" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "EAuracronPCGLandscapeBlendMode::Multiply" },
		{ "Overlay.DisplayName", "Overlay" },
		{ "Overlay.Name", "EAuracronPCGLandscapeBlendMode::Overlay" },
		{ "Replace.DisplayName", "Replace" },
		{ "Replace.Name", "EAuracronPCGLandscapeBlendMode::Replace" },
		{ "Screen.DisplayName", "Screen" },
		{ "Screen.Name", "EAuracronPCGLandscapeBlendMode::Screen" },
		{ "SoftLight.DisplayName", "Soft Light" },
		{ "SoftLight.Name", "EAuracronPCGLandscapeBlendMode::SoftLight" },
		{ "Subtract.DisplayName", "Subtract" },
		{ "Subtract.Name", "EAuracronPCGLandscapeBlendMode::Subtract" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape blending modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGLandscapeBlendMode::Replace", (int64)EAuracronPCGLandscapeBlendMode::Replace },
		{ "EAuracronPCGLandscapeBlendMode::Add", (int64)EAuracronPCGLandscapeBlendMode::Add },
		{ "EAuracronPCGLandscapeBlendMode::Subtract", (int64)EAuracronPCGLandscapeBlendMode::Subtract },
		{ "EAuracronPCGLandscapeBlendMode::Multiply", (int64)EAuracronPCGLandscapeBlendMode::Multiply },
		{ "EAuracronPCGLandscapeBlendMode::Screen", (int64)EAuracronPCGLandscapeBlendMode::Screen },
		{ "EAuracronPCGLandscapeBlendMode::Overlay", (int64)EAuracronPCGLandscapeBlendMode::Overlay },
		{ "EAuracronPCGLandscapeBlendMode::SoftLight", (int64)EAuracronPCGLandscapeBlendMode::SoftLight },
		{ "EAuracronPCGLandscapeBlendMode::HardLight", (int64)EAuracronPCGLandscapeBlendMode::HardLight },
		{ "EAuracronPCGLandscapeBlendMode::ColorDodge", (int64)EAuracronPCGLandscapeBlendMode::ColorDodge },
		{ "EAuracronPCGLandscapeBlendMode::ColorBurn", (int64)EAuracronPCGLandscapeBlendMode::ColorBurn },
		{ "EAuracronPCGLandscapeBlendMode::Darken", (int64)EAuracronPCGLandscapeBlendMode::Darken },
		{ "EAuracronPCGLandscapeBlendMode::Lighten", (int64)EAuracronPCGLandscapeBlendMode::Lighten },
		{ "EAuracronPCGLandscapeBlendMode::Difference", (int64)EAuracronPCGLandscapeBlendMode::Difference },
		{ "EAuracronPCGLandscapeBlendMode::Exclusion", (int64)EAuracronPCGLandscapeBlendMode::Exclusion },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGLandscapeBlendMode",
	"EAuracronPCGLandscapeBlendMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGLandscapeBlendMode **********************************************

// ********** Begin Enum EAuracronPCGErosionType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGErosionType;
static UEnum* EAuracronPCGErosionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGErosionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGErosionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGErosionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGErosionType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGErosionType>()
{
	return EAuracronPCGErosionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chemical.DisplayName", "Chemical Erosion" },
		{ "Chemical.Name", "EAuracronPCGErosionType::Chemical" },
		{ "Combined.DisplayName", "Combined Erosion" },
		{ "Combined.Name", "EAuracronPCGErosionType::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Erosion simulation types\n" },
#endif
		{ "Custom.DisplayName", "Custom Erosion" },
		{ "Custom.Name", "EAuracronPCGErosionType::Custom" },
		{ "Glacial.DisplayName", "Glacial Erosion" },
		{ "Glacial.Name", "EAuracronPCGErosionType::Glacial" },
		{ "Hydraulic.DisplayName", "Hydraulic Erosion" },
		{ "Hydraulic.Name", "EAuracronPCGErosionType::Hydraulic" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
		{ "Thermal.DisplayName", "Thermal Erosion" },
		{ "Thermal.Name", "EAuracronPCGErosionType::Thermal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Erosion simulation types" },
#endif
		{ "Wind.DisplayName", "Wind Erosion" },
		{ "Wind.Name", "EAuracronPCGErosionType::Wind" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGErosionType::Thermal", (int64)EAuracronPCGErosionType::Thermal },
		{ "EAuracronPCGErosionType::Hydraulic", (int64)EAuracronPCGErosionType::Hydraulic },
		{ "EAuracronPCGErosionType::Wind", (int64)EAuracronPCGErosionType::Wind },
		{ "EAuracronPCGErosionType::Chemical", (int64)EAuracronPCGErosionType::Chemical },
		{ "EAuracronPCGErosionType::Glacial", (int64)EAuracronPCGErosionType::Glacial },
		{ "EAuracronPCGErosionType::Combined", (int64)EAuracronPCGErosionType::Combined },
		{ "EAuracronPCGErosionType::Custom", (int64)EAuracronPCGErosionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGErosionType",
	"EAuracronPCGErosionType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGErosionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGErosionType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGErosionType.InnerSingleton;
}
// ********** End Enum EAuracronPCGErosionType *****************************************************

// ********** Begin Enum EAuracronPCGLayerPaintMode ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode;
static UEnum* EAuracronPCGLayerPaintMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGLayerPaintMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLayerPaintMode>()
{
	return EAuracronPCGLayerPaintMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer painting modes\n" },
#endif
		{ "Erase.DisplayName", "Erase" },
		{ "Erase.Name", "EAuracronPCGLayerPaintMode::Erase" },
		{ "Flatten.DisplayName", "Flatten" },
		{ "Flatten.Name", "EAuracronPCGLayerPaintMode::Flatten" },
		{ "Gradient.DisplayName", "Gradient" },
		{ "Gradient.Name", "EAuracronPCGLayerPaintMode::Gradient" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
		{ "Noise.DisplayName", "Noise" },
		{ "Noise.Name", "EAuracronPCGLayerPaintMode::Noise" },
		{ "Paint.DisplayName", "Paint" },
		{ "Paint.Name", "EAuracronPCGLayerPaintMode::Paint" },
		{ "Pattern.DisplayName", "Pattern" },
		{ "Pattern.Name", "EAuracronPCGLayerPaintMode::Pattern" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "EAuracronPCGLayerPaintMode::Procedural" },
		{ "Smooth.DisplayName", "Smooth" },
		{ "Smooth.Name", "EAuracronPCGLayerPaintMode::Smooth" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer painting modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGLayerPaintMode::Paint", (int64)EAuracronPCGLayerPaintMode::Paint },
		{ "EAuracronPCGLayerPaintMode::Erase", (int64)EAuracronPCGLayerPaintMode::Erase },
		{ "EAuracronPCGLayerPaintMode::Smooth", (int64)EAuracronPCGLayerPaintMode::Smooth },
		{ "EAuracronPCGLayerPaintMode::Flatten", (int64)EAuracronPCGLayerPaintMode::Flatten },
		{ "EAuracronPCGLayerPaintMode::Noise", (int64)EAuracronPCGLayerPaintMode::Noise },
		{ "EAuracronPCGLayerPaintMode::Gradient", (int64)EAuracronPCGLayerPaintMode::Gradient },
		{ "EAuracronPCGLayerPaintMode::Pattern", (int64)EAuracronPCGLayerPaintMode::Pattern },
		{ "EAuracronPCGLayerPaintMode::Procedural", (int64)EAuracronPCGLayerPaintMode::Procedural },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGLayerPaintMode",
	"EAuracronPCGLayerPaintMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGLayerPaintMode **************************************************

// ********** Begin Enum EAuracronPCGHeightModificationMode ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode;
static UEnum* EAuracronPCGHeightModificationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGHeightModificationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGHeightModificationMode>()
{
	return EAuracronPCGHeightModificationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Absolute.DisplayName", "Absolute" },
		{ "Absolute.Name", "EAuracronPCGHeightModificationMode::Absolute" },
		{ "Additive.DisplayName", "Additive" },
		{ "Additive.Name", "EAuracronPCGHeightModificationMode::Additive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Height modification modes\n" },
#endif
		{ "Flatten.DisplayName", "Flatten" },
		{ "Flatten.Name", "EAuracronPCGHeightModificationMode::Flatten" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
		{ "Noise.DisplayName", "Noise" },
		{ "Noise.Name", "EAuracronPCGHeightModificationMode::Noise" },
		{ "Relative.DisplayName", "Relative" },
		{ "Relative.Name", "EAuracronPCGHeightModificationMode::Relative" },
		{ "Smooth.DisplayName", "Smooth" },
		{ "Smooth.Name", "EAuracronPCGHeightModificationMode::Smooth" },
		{ "Subtractive.DisplayName", "Subtractive" },
		{ "Subtractive.Name", "EAuracronPCGHeightModificationMode::Subtractive" },
		{ "Terrace.DisplayName", "Terrace" },
		{ "Terrace.Name", "EAuracronPCGHeightModificationMode::Terrace" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height modification modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGHeightModificationMode::Absolute", (int64)EAuracronPCGHeightModificationMode::Absolute },
		{ "EAuracronPCGHeightModificationMode::Relative", (int64)EAuracronPCGHeightModificationMode::Relative },
		{ "EAuracronPCGHeightModificationMode::Additive", (int64)EAuracronPCGHeightModificationMode::Additive },
		{ "EAuracronPCGHeightModificationMode::Subtractive", (int64)EAuracronPCGHeightModificationMode::Subtractive },
		{ "EAuracronPCGHeightModificationMode::Smooth", (int64)EAuracronPCGHeightModificationMode::Smooth },
		{ "EAuracronPCGHeightModificationMode::Flatten", (int64)EAuracronPCGHeightModificationMode::Flatten },
		{ "EAuracronPCGHeightModificationMode::Noise", (int64)EAuracronPCGHeightModificationMode::Noise },
		{ "EAuracronPCGHeightModificationMode::Terrace", (int64)EAuracronPCGHeightModificationMode::Terrace },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGHeightModificationMode",
	"EAuracronPCGHeightModificationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGHeightModificationMode ******************************************

// ********** Begin ScriptStruct FAuracronPCGLandscapeSamplingDescriptor ***************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor;
class UScriptStruct* FAuracronPCGLandscapeSamplingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGLandscapeSamplingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Landscape Sampling Descriptor\n * Describes parameters for landscape sampling operations\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape Sampling Descriptor\nDescribes parameters for landscape sampling operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingMode_MetaData[] = {
		{ "Category", "Sampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleHeight_MetaData[] = {
		{ "Category", "Sampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleNormal_MetaData[] = {
		{ "Category", "Sampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleSlope_MetaData[] = {
		{ "Category", "Sampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleCurvature_MetaData[] = {
		{ "Category", "Sampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleLayers_MetaData[] = {
		{ "Category", "Layers" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerNames_MetaData[] = {
		{ "Category", "Layers" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleAllLayers_MetaData[] = {
		{ "Category", "Layers" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHighQualitySampling_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingRadius_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SampleCount_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByHeight_MetaData[] = {
		{ "Category", "Filtering" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightRange_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByHeight" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterBySlope_MetaData[] = {
		{ "Category", "Filtering" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeRange_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterBySlope" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SamplingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SamplingMode;
	static void NewProp_bSampleHeight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleHeight;
	static void NewProp_bSampleNormal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleNormal;
	static void NewProp_bSampleSlope_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleSlope;
	static void NewProp_bSampleCurvature_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleCurvature;
	static void NewProp_bSampleLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleLayers;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LayerNames;
	static void NewProp_bSampleAllLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleAllLayers;
	static void NewProp_bUseHighQualitySampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHighQualitySampling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SamplingRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SampleCount;
	static void NewProp_bFilterByHeight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByHeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightRange;
	static void NewProp_bFilterBySlope_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterBySlope;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlopeRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGLandscapeSamplingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SamplingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SamplingMode = { "SamplingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLandscapeSamplingDescriptor, SamplingMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeSamplingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingMode_MetaData), NewProp_SamplingMode_MetaData) }; // 977814554
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleHeight_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bSampleHeight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleHeight = { "bSampleHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleHeight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleHeight_MetaData), NewProp_bSampleHeight_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleNormal_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bSampleNormal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleNormal = { "bSampleNormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleNormal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleNormal_MetaData), NewProp_bSampleNormal_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleSlope_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bSampleSlope = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleSlope = { "bSampleSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleSlope_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleSlope_MetaData), NewProp_bSampleSlope_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleCurvature_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bSampleCurvature = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleCurvature = { "bSampleCurvature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleCurvature_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleCurvature_MetaData), NewProp_bSampleCurvature_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleLayers_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bSampleLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleLayers = { "bSampleLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleLayers_MetaData), NewProp_bSampleLayers_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_LayerNames_Inner = { "LayerNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_LayerNames = { "LayerNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLandscapeSamplingDescriptor, LayerNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerNames_MetaData), NewProp_LayerNames_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleAllLayers_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bSampleAllLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleAllLayers = { "bSampleAllLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleAllLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleAllLayers_MetaData), NewProp_bSampleAllLayers_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bUseHighQualitySampling_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bUseHighQualitySampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bUseHighQualitySampling = { "bUseHighQualitySampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bUseHighQualitySampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHighQualitySampling_MetaData), NewProp_bUseHighQualitySampling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SamplingRadius = { "SamplingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLandscapeSamplingDescriptor, SamplingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingRadius_MetaData), NewProp_SamplingRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SampleCount = { "SampleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLandscapeSamplingDescriptor, SampleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SampleCount_MetaData), NewProp_SampleCount_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterByHeight_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bFilterByHeight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterByHeight = { "bFilterByHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterByHeight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByHeight_MetaData), NewProp_bFilterByHeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_HeightRange = { "HeightRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLandscapeSamplingDescriptor, HeightRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightRange_MetaData), NewProp_HeightRange_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterBySlope_SetBit(void* Obj)
{
	((FAuracronPCGLandscapeSamplingDescriptor*)Obj)->bFilterBySlope = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterBySlope = { "bFilterBySlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLandscapeSamplingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterBySlope_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterBySlope_MetaData), NewProp_bFilterBySlope_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SlopeRange = { "SlopeRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLandscapeSamplingDescriptor, SlopeRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeRange_MetaData), NewProp_SlopeRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SamplingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SamplingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleNormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleCurvature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_LayerNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_LayerNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bSampleAllLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bUseHighQualitySampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SamplingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SampleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterByHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_HeightRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_bFilterBySlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewProp_SlopeRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGLandscapeSamplingDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGLandscapeSamplingDescriptor),
	alignof(FAuracronPCGLandscapeSamplingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGLandscapeSamplingDescriptor *****************************

// ********** Begin ScriptStruct FAuracronPCGErosionDescriptor *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor;
class UScriptStruct* FAuracronPCGErosionDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGErosionDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Erosion Simulation Descriptor\n * Describes parameters for erosion simulation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Erosion Simulation Descriptor\nDescribes parameters for erosion simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErosionType_MetaData[] = {
		{ "Category", "Erosion" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Iterations_MetaData[] = {
		{ "Category", "Erosion" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Strength_MetaData[] = {
		{ "Category", "Erosion" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThermalStrength_MetaData[] = {
		{ "Category", "Thermal" },
		{ "EditCondition", "ErosionType == EAuracronPCGErosionType::Thermal || ErosionType == EAuracronPCGErosionType::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TalusAngle_MetaData[] = {
		{ "Category", "Thermal" },
		{ "EditCondition", "ErosionType == EAuracronPCGErosionType::Thermal || ErosionType == EAuracronPCGErosionType::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RainAmount_MetaData[] = {
		{ "Category", "Hydraulic" },
		{ "EditCondition", "ErosionType == EAuracronPCGErosionType::Hydraulic || ErosionType == EAuracronPCGErosionType::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Evaporation_MetaData[] = {
		{ "Category", "Hydraulic" },
		{ "EditCondition", "ErosionType == EAuracronPCGErosionType::Hydraulic || ErosionType == EAuracronPCGErosionType::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SedimentCapacity_MetaData[] = {
		{ "Category", "Hydraulic" },
		{ "EditCondition", "ErosionType == EAuracronPCGErosionType::Hydraulic || ErosionType == EAuracronPCGErosionType::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirection_MetaData[] = {
		{ "Category", "Wind" },
		{ "EditCondition", "ErosionType == EAuracronPCGErosionType::Wind || ErosionType == EAuracronPCGErosionType::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindStrength_MetaData[] = {
		{ "Category", "Wind" },
		{ "EditCondition", "ErosionType == EAuracronPCGErosionType::Wind || ErosionType == EAuracronPCGErosionType::Combined" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveBoundaries_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateDebugData_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomSeed_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ErosionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ErosionType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Iterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Strength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ThermalStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TalusAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RainAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Evaporation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SedimentCapacity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindStrength;
	static void NewProp_bPreserveBoundaries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveBoundaries;
	static void NewProp_bGenerateDebugData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateDebugData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RandomSeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGErosionDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_ErosionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_ErosionType = { "ErosionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, ErosionType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGErosionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErosionType_MetaData), NewProp_ErosionType_MetaData) }; // 3061212850
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_Iterations = { "Iterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, Iterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Iterations_MetaData), NewProp_Iterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_Strength = { "Strength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, Strength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Strength_MetaData), NewProp_Strength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_ThermalStrength = { "ThermalStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, ThermalStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThermalStrength_MetaData), NewProp_ThermalStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_TalusAngle = { "TalusAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, TalusAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TalusAngle_MetaData), NewProp_TalusAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_RainAmount = { "RainAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, RainAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RainAmount_MetaData), NewProp_RainAmount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_Evaporation = { "Evaporation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, Evaporation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Evaporation_MetaData), NewProp_Evaporation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_SedimentCapacity = { "SedimentCapacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, SedimentCapacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SedimentCapacity_MetaData), NewProp_SedimentCapacity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_WindDirection = { "WindDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, WindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirection_MetaData), NewProp_WindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_WindStrength = { "WindStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, WindStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindStrength_MetaData), NewProp_WindStrength_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bPreserveBoundaries_SetBit(void* Obj)
{
	((FAuracronPCGErosionDescriptor*)Obj)->bPreserveBoundaries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bPreserveBoundaries = { "bPreserveBoundaries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGErosionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bPreserveBoundaries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveBoundaries_MetaData), NewProp_bPreserveBoundaries_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bGenerateDebugData_SetBit(void* Obj)
{
	((FAuracronPCGErosionDescriptor*)Obj)->bGenerateDebugData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bGenerateDebugData = { "bGenerateDebugData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGErosionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bGenerateDebugData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateDebugData_MetaData), NewProp_bGenerateDebugData_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_RandomSeed = { "RandomSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGErosionDescriptor, RandomSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomSeed_MetaData), NewProp_RandomSeed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_ErosionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_ErosionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_Iterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_Strength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_ThermalStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_TalusAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_RainAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_Evaporation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_SedimentCapacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_WindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_WindStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bPreserveBoundaries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_bGenerateDebugData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewProp_RandomSeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGErosionDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGErosionDescriptor),
	alignof(FAuracronPCGErosionDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGErosionDescriptor ***************************************

// ********** Begin Class UAuracronPCGAdvancedLandscapeSamplerSettings *****************************
void UAuracronPCGAdvancedLandscapeSamplerSettings::StaticRegisterNativesUAuracronPCGAdvancedLandscapeSamplerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings;
UClass* UAuracronPCGAdvancedLandscapeSamplerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedLandscapeSamplerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedLandscapeSamplerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedLandscapeSamplerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_NoRegister()
{
	return UAuracronPCGAdvancedLandscapeSamplerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLandscapeIntegration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingDescriptor_MetaData[] = {
		{ "Category", "Sampling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sampling configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sampling configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLandscape_MetaData[] = {
		{ "Category", "Landscape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape selection\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape selection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoDetectLandscape_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleAllLandscapes_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultiSampling_MetaData[] = {
		{ "Category", "Advanced Sampling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced sampling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced sampling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MultiSampleCount_MetaData[] = {
		{ "Category", "Advanced Sampling" },
		{ "EditCondition", "bUseMultiSampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MultiSampleRadius_MetaData[] = {
		{ "Category", "Advanced Sampling" },
		{ "EditCondition", "bUseMultiSampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAdaptiveSampling_MetaData[] = {
		{ "Category", "Advanced Sampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveThreshold_MetaData[] = {
		{ "Category", "Advanced Sampling" },
		{ "EditCondition", "bUseAdaptiveSampling" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncSampling_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCacheSamplingResults_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentSamples_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "16" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputDebugInfo_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputSamplingStatistics_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SamplingDescriptor;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TargetLandscape;
	static void NewProp_bAutoDetectLandscape_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoDetectLandscape;
	static void NewProp_bSampleAllLandscapes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleAllLandscapes;
	static void NewProp_bUseMultiSampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultiSampling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MultiSampleCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MultiSampleRadius;
	static void NewProp_bUseAdaptiveSampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAdaptiveSampling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptiveThreshold;
	static void NewProp_bUseAsyncSampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncSampling;
	static void NewProp_bCacheSamplingResults_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCacheSamplingResults;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentSamples;
	static void NewProp_bOutputDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputDebugInfo;
	static void NewProp_bOutputSamplingStatistics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputSamplingStatistics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedLandscapeSamplerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_SamplingDescriptor = { "SamplingDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedLandscapeSamplerSettings, SamplingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingDescriptor_MetaData), NewProp_SamplingDescriptor_MetaData) }; // 3790163207
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_TargetLandscape = { "TargetLandscape", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedLandscapeSamplerSettings, TargetLandscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLandscape_MetaData), NewProp_TargetLandscape_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bAutoDetectLandscape_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bAutoDetectLandscape = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bAutoDetectLandscape = { "bAutoDetectLandscape", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bAutoDetectLandscape_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoDetectLandscape_MetaData), NewProp_bAutoDetectLandscape_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bSampleAllLandscapes_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bSampleAllLandscapes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bSampleAllLandscapes = { "bSampleAllLandscapes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bSampleAllLandscapes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleAllLandscapes_MetaData), NewProp_bSampleAllLandscapes_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseMultiSampling_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bUseMultiSampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseMultiSampling = { "bUseMultiSampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseMultiSampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultiSampling_MetaData), NewProp_bUseMultiSampling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_MultiSampleCount = { "MultiSampleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedLandscapeSamplerSettings, MultiSampleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MultiSampleCount_MetaData), NewProp_MultiSampleCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_MultiSampleRadius = { "MultiSampleRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedLandscapeSamplerSettings, MultiSampleRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MultiSampleRadius_MetaData), NewProp_MultiSampleRadius_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAdaptiveSampling_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bUseAdaptiveSampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAdaptiveSampling = { "bUseAdaptiveSampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAdaptiveSampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAdaptiveSampling_MetaData), NewProp_bUseAdaptiveSampling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_AdaptiveThreshold = { "AdaptiveThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedLandscapeSamplerSettings, AdaptiveThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveThreshold_MetaData), NewProp_AdaptiveThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAsyncSampling_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bUseAsyncSampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAsyncSampling = { "bUseAsyncSampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAsyncSampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncSampling_MetaData), NewProp_bUseAsyncSampling_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bCacheSamplingResults_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bCacheSamplingResults = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bCacheSamplingResults = { "bCacheSamplingResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bCacheSamplingResults_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCacheSamplingResults_MetaData), NewProp_bCacheSamplingResults_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_MaxConcurrentSamples = { "MaxConcurrentSamples", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedLandscapeSamplerSettings, MaxConcurrentSamples), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentSamples_MetaData), NewProp_MaxConcurrentSamples_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputDebugInfo_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bOutputDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputDebugInfo = { "bOutputDebugInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputDebugInfo_MetaData), NewProp_bOutputDebugInfo_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputSamplingStatistics_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedLandscapeSamplerSettings*)Obj)->bOutputSamplingStatistics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputSamplingStatistics = { "bOutputSamplingStatistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputSamplingStatistics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputSamplingStatistics_MetaData), NewProp_bOutputSamplingStatistics_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_SamplingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_TargetLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bAutoDetectLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bSampleAllLandscapes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseMultiSampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_MultiSampleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_MultiSampleRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAdaptiveSampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_AdaptiveThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bUseAsyncSampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bCacheSamplingResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_MaxConcurrentSamples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputDebugInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::NewProp_bOutputSamplingStatistics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedLandscapeSamplerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedLandscapeSamplerSettings);
UAuracronPCGAdvancedLandscapeSamplerSettings::~UAuracronPCGAdvancedLandscapeSamplerSettings() {}
// ********** End Class UAuracronPCGAdvancedLandscapeSamplerSettings *******************************

// ********** Begin Class UAuracronPCGLandscapeHeightModifierSettings ******************************
void UAuracronPCGLandscapeHeightModifierSettings::StaticRegisterNativesUAuracronPCGLandscapeHeightModifierSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings;
UClass* UAuracronPCGLandscapeHeightModifierSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGLandscapeHeightModifierSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGLandscapeHeightModifierSettings"),
			Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGLandscapeHeightModifierSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_NoRegister()
{
	return UAuracronPCGLandscapeHeightModifierSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLandscapeIntegration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLandscape_MetaData[] = {
		{ "Category", "Landscape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target landscape\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target landscape" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoDetectLandscape_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModificationMode_MetaData[] = {
		{ "Category", "Height Modification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Height modification\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height modification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightValue_MetaData[] = {
		{ "Category", "Height Modification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightAttribute_MetaData[] = {
		{ "Category", "Height Modification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAttributeForHeight_MetaData[] = {
		{ "Category", "Height Modification" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushRadius_MetaData[] = {
		{ "Category", "Brush" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Brush settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Brush settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushStrength_MetaData[] = {
		{ "Category", "Brush" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushFalloff_MetaData[] = {
		{ "Category", "Brush" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushFalloffCurve_MetaData[] = {
		{ "Category", "Brush" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise settings\n" },
#endif
		{ "EditCondition", "ModificationMode == EAuracronPCGHeightModificationMode::Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseStrength_MetaData[] = {
		{ "Category", "Noise" },
		{ "EditCondition", "ModificationMode == EAuracronPCGHeightModificationMode::Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "Noise" },
		{ "EditCondition", "ModificationMode == EAuracronPCGHeightModificationMode::Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerraceHeight_MetaData[] = {
		{ "Category", "Terrace" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Terrace settings\n" },
#endif
		{ "EditCondition", "ModificationMode == EAuracronPCGHeightModificationMode::Terrace" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Terrace settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerraceCount_MetaData[] = {
		{ "Category", "Terrace" },
		{ "EditCondition", "ModificationMode == EAuracronPCGHeightModificationMode::Terrace" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerraceSmoothing_MetaData[] = {
		{ "Category", "Terrace" },
		{ "EditCondition", "ModificationMode == EAuracronPCGHeightModificationMode::Terrace" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveExistingData_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced options\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced options" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUpdateCollision_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUpdateNavigation_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TargetLandscape;
	static void NewProp_bAutoDetectLandscape_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoDetectLandscape;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ModificationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ModificationMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightValue;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HeightAttribute;
	static void NewProp_bUseAttributeForHeight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAttributeForHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BrushRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BrushStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BrushFalloff;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BrushFalloffCurve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseStrength;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerraceHeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TerraceCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerraceSmoothing;
	static void NewProp_bPreserveExistingData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveExistingData;
	static void NewProp_bUpdateCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUpdateCollision;
	static void NewProp_bUpdateNavigation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUpdateNavigation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGLandscapeHeightModifierSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TargetLandscape = { "TargetLandscape", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, TargetLandscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLandscape_MetaData), NewProp_TargetLandscape_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bAutoDetectLandscape_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeHeightModifierSettings*)Obj)->bAutoDetectLandscape = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bAutoDetectLandscape = { "bAutoDetectLandscape", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeHeightModifierSettings), &Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bAutoDetectLandscape_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoDetectLandscape_MetaData), NewProp_bAutoDetectLandscape_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_ModificationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_ModificationMode = { "ModificationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, ModificationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModificationMode_MetaData), NewProp_ModificationMode_MetaData) }; // 3579358337
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_HeightValue = { "HeightValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, HeightValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightValue_MetaData), NewProp_HeightValue_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_HeightAttribute = { "HeightAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, HeightAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightAttribute_MetaData), NewProp_HeightAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUseAttributeForHeight_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeHeightModifierSettings*)Obj)->bUseAttributeForHeight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUseAttributeForHeight = { "bUseAttributeForHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeHeightModifierSettings), &Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUseAttributeForHeight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAttributeForHeight_MetaData), NewProp_bUseAttributeForHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushRadius = { "BrushRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, BrushRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushRadius_MetaData), NewProp_BrushRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushStrength = { "BrushStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, BrushStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushStrength_MetaData), NewProp_BrushStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushFalloff = { "BrushFalloff", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, BrushFalloff), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushFalloff_MetaData), NewProp_BrushFalloff_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushFalloffCurve = { "BrushFalloffCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, BrushFalloffCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushFalloffCurve_MetaData), NewProp_BrushFalloffCurve_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_NoiseStrength = { "NoiseStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, NoiseStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseStrength_MetaData), NewProp_NoiseStrength_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TerraceHeight = { "TerraceHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, TerraceHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerraceHeight_MetaData), NewProp_TerraceHeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TerraceCount = { "TerraceCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, TerraceCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerraceCount_MetaData), NewProp_TerraceCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TerraceSmoothing = { "TerraceSmoothing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeHeightModifierSettings, TerraceSmoothing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerraceSmoothing_MetaData), NewProp_TerraceSmoothing_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bPreserveExistingData_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeHeightModifierSettings*)Obj)->bPreserveExistingData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bPreserveExistingData = { "bPreserveExistingData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeHeightModifierSettings), &Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bPreserveExistingData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveExistingData_MetaData), NewProp_bPreserveExistingData_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateCollision_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeHeightModifierSettings*)Obj)->bUpdateCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateCollision = { "bUpdateCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeHeightModifierSettings), &Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUpdateCollision_MetaData), NewProp_bUpdateCollision_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateNavigation_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeHeightModifierSettings*)Obj)->bUpdateNavigation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateNavigation = { "bUpdateNavigation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeHeightModifierSettings), &Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateNavigation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUpdateNavigation_MetaData), NewProp_bUpdateNavigation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TargetLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bAutoDetectLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_ModificationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_ModificationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_HeightValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_HeightAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUseAttributeForHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushFalloff,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_BrushFalloffCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_NoiseStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TerraceHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TerraceCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_TerraceSmoothing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bPreserveExistingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::NewProp_bUpdateNavigation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::ClassParams = {
	&UAuracronPCGLandscapeHeightModifierSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGLandscapeHeightModifierSettings);
UAuracronPCGLandscapeHeightModifierSettings::~UAuracronPCGLandscapeHeightModifierSettings() {}
// ********** End Class UAuracronPCGLandscapeHeightModifierSettings ********************************

// ********** Begin Class UAuracronPCGLandscapeLayerPainterSettings ********************************
void UAuracronPCGLandscapeLayerPainterSettings::StaticRegisterNativesUAuracronPCGLandscapeLayerPainterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings;
UClass* UAuracronPCGLandscapeLayerPainterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGLandscapeLayerPainterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGLandscapeLayerPainterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGLandscapeLayerPainterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_NoRegister()
{
	return UAuracronPCGLandscapeLayerPainterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLandscapeIntegration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLandscape_MetaData[] = {
		{ "Category", "Landscape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target landscape\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target landscape" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoDetectLandscape_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "Category", "Layer" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerInfo_MetaData[] = {
		{ "Category", "Layer" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PaintMode_MetaData[] = {
		{ "Category", "Layer" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PaintStrength_MetaData[] = {
		{ "Category", "Paint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Paint settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Paint settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrengthAttribute_MetaData[] = {
		{ "Category", "Paint" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAttributeForStrength_MetaData[] = {
		{ "Category", "Paint" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushRadius_MetaData[] = {
		{ "Category", "Brush" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Brush settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Brush settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushFalloff_MetaData[] = {
		{ "Category", "Brush" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushFalloffCurve_MetaData[] = {
		{ "Category", "Brush" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendMode_MetaData[] = {
		{ "Category", "Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blending settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blending settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendStrength_MetaData[] = {
		{ "Category", "Blending" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseConditionalPainting_MetaData[] = {
		{ "Category", "Conditional" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Conditional painting\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conditional painting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionAttribute_MetaData[] = {
		{ "Category", "Conditional" },
		{ "EditCondition", "bUseConditionalPainting" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionRange_MetaData[] = {
		{ "Category", "Conditional" },
		{ "EditCondition", "bUseConditionalPainting" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizeLayers_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced options\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced options" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveOtherLayers_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUpdateMaterials_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TargetLandscape;
	static void NewProp_bAutoDetectLandscape_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoDetectLandscape;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LayerInfo;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PaintMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PaintMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PaintStrength;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StrengthAttribute;
	static void NewProp_bUseAttributeForStrength_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAttributeForStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BrushRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BrushFalloff;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BrushFalloffCurve;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BlendMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BlendMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendStrength;
	static void NewProp_bUseConditionalPainting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseConditionalPainting;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConditionAttribute;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConditionRange;
	static void NewProp_bNormalizeLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizeLayers;
	static void NewProp_bPreserveOtherLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveOtherLayers;
	static void NewProp_bUpdateMaterials_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUpdateMaterials;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGLandscapeLayerPainterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_TargetLandscape = { "TargetLandscape", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, TargetLandscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLandscape_MetaData), NewProp_TargetLandscape_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bAutoDetectLandscape_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeLayerPainterSettings*)Obj)->bAutoDetectLandscape = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bAutoDetectLandscape = { "bAutoDetectLandscape", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeLayerPainterSettings), &Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bAutoDetectLandscape_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoDetectLandscape_MetaData), NewProp_bAutoDetectLandscape_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_LayerInfo = { "LayerInfo", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, LayerInfo), Z_Construct_UClass_ULandscapeLayerInfoObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerInfo_MetaData), NewProp_LayerInfo_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_PaintMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_PaintMode = { "PaintMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, PaintMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLayerPaintMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PaintMode_MetaData), NewProp_PaintMode_MetaData) }; // 588648483
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_PaintStrength = { "PaintStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, PaintStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PaintStrength_MetaData), NewProp_PaintStrength_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_StrengthAttribute = { "StrengthAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, StrengthAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrengthAttribute_MetaData), NewProp_StrengthAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseAttributeForStrength_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeLayerPainterSettings*)Obj)->bUseAttributeForStrength = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseAttributeForStrength = { "bUseAttributeForStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeLayerPainterSettings), &Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseAttributeForStrength_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAttributeForStrength_MetaData), NewProp_bUseAttributeForStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BrushRadius = { "BrushRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, BrushRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushRadius_MetaData), NewProp_BrushRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BrushFalloff = { "BrushFalloff", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, BrushFalloff), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushFalloff_MetaData), NewProp_BrushFalloff_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BrushFalloffCurve = { "BrushFalloffCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, BrushFalloffCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushFalloffCurve_MetaData), NewProp_BrushFalloffCurve_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BlendMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BlendMode = { "BlendMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, BlendMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGLandscapeBlendMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendMode_MetaData), NewProp_BlendMode_MetaData) }; // 3778115934
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BlendStrength = { "BlendStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, BlendStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendStrength_MetaData), NewProp_BlendStrength_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseConditionalPainting_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeLayerPainterSettings*)Obj)->bUseConditionalPainting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseConditionalPainting = { "bUseConditionalPainting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeLayerPainterSettings), &Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseConditionalPainting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseConditionalPainting_MetaData), NewProp_bUseConditionalPainting_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_ConditionAttribute = { "ConditionAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, ConditionAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionAttribute_MetaData), NewProp_ConditionAttribute_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_ConditionRange = { "ConditionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeLayerPainterSettings, ConditionRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionRange_MetaData), NewProp_ConditionRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bNormalizeLayers_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeLayerPainterSettings*)Obj)->bNormalizeLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bNormalizeLayers = { "bNormalizeLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeLayerPainterSettings), &Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bNormalizeLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizeLayers_MetaData), NewProp_bNormalizeLayers_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bPreserveOtherLayers_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeLayerPainterSettings*)Obj)->bPreserveOtherLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bPreserveOtherLayers = { "bPreserveOtherLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeLayerPainterSettings), &Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bPreserveOtherLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveOtherLayers_MetaData), NewProp_bPreserveOtherLayers_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUpdateMaterials_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeLayerPainterSettings*)Obj)->bUpdateMaterials = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUpdateMaterials = { "bUpdateMaterials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeLayerPainterSettings), &Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUpdateMaterials_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUpdateMaterials_MetaData), NewProp_bUpdateMaterials_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_TargetLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bAutoDetectLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_LayerInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_PaintMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_PaintMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_PaintStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_StrengthAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseAttributeForStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BrushRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BrushFalloff,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BrushFalloffCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BlendMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BlendMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_BlendStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUseConditionalPainting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_ConditionAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_ConditionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bNormalizeLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bPreserveOtherLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::NewProp_bUpdateMaterials,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::ClassParams = {
	&UAuracronPCGLandscapeLayerPainterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGLandscapeLayerPainterSettings);
UAuracronPCGLandscapeLayerPainterSettings::~UAuracronPCGLandscapeLayerPainterSettings() {}
// ********** End Class UAuracronPCGLandscapeLayerPainterSettings **********************************

// ********** Begin Class UAuracronPCGLandscapeErosionSimulatorSettings ****************************
void UAuracronPCGLandscapeErosionSimulatorSettings::StaticRegisterNativesUAuracronPCGLandscapeErosionSimulatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings;
UClass* UAuracronPCGLandscapeErosionSimulatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGLandscapeErosionSimulatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGLandscapeErosionSimulatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGLandscapeErosionSimulatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_NoRegister()
{
	return UAuracronPCGLandscapeErosionSimulatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGLandscapeIntegration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLandscape_MetaData[] = {
		{ "Category", "Landscape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target landscape\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target landscape" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoDetectLandscape_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErosionDescriptor_MetaData[] = {
		{ "Category", "Erosion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Erosion configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Erosion configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomArea_MetaData[] = {
		{ "Category", "Simulation Area" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simulation area\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simulation area" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationBounds_MetaData[] = {
		{ "Category", "Simulation Area" },
		{ "EditCondition", "bUseCustomArea" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGPUAcceleration_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultithreading_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadCount_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "16" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputErosionMask_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputSedimentMap_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputWaterMap_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputVelocityMap_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TargetLandscape;
	static void NewProp_bAutoDetectLandscape_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoDetectLandscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ErosionDescriptor;
	static void NewProp_bUseCustomArea_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomArea;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SimulationBounds;
	static void NewProp_bUseGPUAcceleration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGPUAcceleration;
	static void NewProp_bUseMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static void NewProp_bOutputErosionMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputErosionMask;
	static void NewProp_bOutputSedimentMap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputSedimentMap;
	static void NewProp_bOutputWaterMap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputWaterMap;
	static void NewProp_bOutputVelocityMap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputVelocityMap;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGLandscapeErosionSimulatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_TargetLandscape = { "TargetLandscape", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeErosionSimulatorSettings, TargetLandscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLandscape_MetaData), NewProp_TargetLandscape_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bAutoDetectLandscape_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bAutoDetectLandscape = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bAutoDetectLandscape = { "bAutoDetectLandscape", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bAutoDetectLandscape_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoDetectLandscape_MetaData), NewProp_bAutoDetectLandscape_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_ErosionDescriptor = { "ErosionDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeErosionSimulatorSettings, ErosionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErosionDescriptor_MetaData), NewProp_ErosionDescriptor_MetaData) }; // 1631345082
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseCustomArea_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bUseCustomArea = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseCustomArea = { "bUseCustomArea", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseCustomArea_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomArea_MetaData), NewProp_bUseCustomArea_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_SimulationBounds = { "SimulationBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeErosionSimulatorSettings, SimulationBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationBounds_MetaData), NewProp_SimulationBounds_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseGPUAcceleration_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bUseGPUAcceleration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseGPUAcceleration = { "bUseGPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseGPUAcceleration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGPUAcceleration_MetaData), NewProp_bUseGPUAcceleration_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseMultithreading_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bUseMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseMultithreading = { "bUseMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultithreading_MetaData), NewProp_bUseMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeErosionSimulatorSettings, ThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadCount_MetaData), NewProp_ThreadCount_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputErosionMask_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bOutputErosionMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputErosionMask = { "bOutputErosionMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputErosionMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputErosionMask_MetaData), NewProp_bOutputErosionMask_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputSedimentMap_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bOutputSedimentMap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputSedimentMap = { "bOutputSedimentMap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputSedimentMap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputSedimentMap_MetaData), NewProp_bOutputSedimentMap_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputWaterMap_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bOutputWaterMap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputWaterMap = { "bOutputWaterMap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputWaterMap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputWaterMap_MetaData), NewProp_bOutputWaterMap_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputVelocityMap_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeErosionSimulatorSettings*)Obj)->bOutputVelocityMap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputVelocityMap = { "bOutputVelocityMap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), &Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputVelocityMap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputVelocityMap_MetaData), NewProp_bOutputVelocityMap_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_TargetLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bAutoDetectLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_ErosionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseCustomArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_SimulationBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseGPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bUseMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_ThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputErosionMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputSedimentMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputWaterMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::NewProp_bOutputVelocityMap,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::ClassParams = {
	&UAuracronPCGLandscapeErosionSimulatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGLandscapeErosionSimulatorSettings);
UAuracronPCGLandscapeErosionSimulatorSettings::~UAuracronPCGLandscapeErosionSimulatorSettings() {}
// ********** End Class UAuracronPCGLandscapeErosionSimulatorSettings ******************************

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function FindLandscapeInWorld ******
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventFindLandscapeInWorld_Parms
	{
		UWorld* World;
		FVector Location;
		ALandscape* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape detection and access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape detection and access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventFindLandscapeInWorld_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventFindLandscapeInWorld_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventFindLandscapeInWorld_Parms, ReturnValue), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "FindLandscapeInWorld", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::AuracronPCGLandscapeIntegrationUtils_eventFindLandscapeInWorld_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::AuracronPCGLandscapeIntegrationUtils_eventFindLandscapeInWorld_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execFindLandscapeInWorld)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ALandscape**)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::FindLandscapeInWorld(Z_Param_World,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function FindLandscapeInWorld ********

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function GetAllLandscapesInWorld ***
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventGetAllLandscapesInWorld_Parms
	{
		UWorld* World;
		TArray<ALandscape*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventGetAllLandscapesInWorld_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventGetAllLandscapesInWorld_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "GetAllLandscapesInWorld", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::AuracronPCGLandscapeIntegrationUtils_eventGetAllLandscapesInWorld_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::AuracronPCGLandscapeIntegrationUtils_eventGetAllLandscapesInWorld_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execGetAllLandscapesInWorld)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ALandscape*>*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::GetAllLandscapesInWorld(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function GetAllLandscapesInWorld *****

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function GetLandscapeBounds ********
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeBounds_Parms
	{
		ALandscape* Landscape;
		FBox ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeBounds_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeBounds_Parms, ReturnValue), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "GetLandscapeBounds", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeBounds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeBounds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execGetLandscapeBounds)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FBox*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::GetLandscapeBounds(Z_Param_Landscape);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function GetLandscapeBounds **********

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function GetLandscapeLayerNames ****
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeLayerNames_Parms
	{
		ALandscape* Landscape;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeLayerNames_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeLayerNames_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "GetLandscapeLayerNames", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeLayerNames_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::AuracronPCGLandscapeIntegrationUtils_eventGetLandscapeLayerNames_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execGetLandscapeLayerNames)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::GetLandscapeLayerNames(Z_Param_Landscape);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function GetLandscapeLayerNames ******

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function IsPointOnLandscape ********
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventIsPointOnLandscape_Parms
	{
		ALandscape* Landscape;
		FVector WorldLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventIsPointOnLandscape_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventIsPointOnLandscape_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
void Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLandscapeIntegrationUtils_eventIsPointOnLandscape_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLandscapeIntegrationUtils_eventIsPointOnLandscape_Parms), &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "IsPointOnLandscape", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::AuracronPCGLandscapeIntegrationUtils_eventIsPointOnLandscape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::AuracronPCGLandscapeIntegrationUtils_eventIsPointOnLandscape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execIsPointOnLandscape)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::IsPointOnLandscape(Z_Param_Landscape,Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function IsPointOnLandscape **********

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function LandscapeCoordinateToWorldLocation 
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventLandscapeCoordinateToWorldLocation_Parms
	{
		ALandscape* Landscape;
		FIntPoint LandscapeCoordinate;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeCoordinate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LandscapeCoordinate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventLandscapeCoordinateToWorldLocation_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::NewProp_LandscapeCoordinate = { "LandscapeCoordinate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventLandscapeCoordinateToWorldLocation_Parms, LandscapeCoordinate), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeCoordinate_MetaData), NewProp_LandscapeCoordinate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventLandscapeCoordinateToWorldLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::NewProp_LandscapeCoordinate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "LandscapeCoordinateToWorldLocation", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::AuracronPCGLandscapeIntegrationUtils_eventLandscapeCoordinateToWorldLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::AuracronPCGLandscapeIntegrationUtils_eventLandscapeCoordinateToWorldLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execLandscapeCoordinateToWorldLocation)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_LandscapeCoordinate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::LandscapeCoordinateToWorldLocation(Z_Param_Landscape,Z_Param_Out_LandscapeCoordinate);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function LandscapeCoordinateToWorldLocation 

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function ModifyLandscapeHeight *****
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms
	{
		ALandscape* Landscape;
		FVector WorldLocation;
		float Height;
		float Radius;
		EAuracronPCGHeightModificationMode Mode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Height modification\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height modification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms, Mode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode, METADATA_PARAMS(0, nullptr) }; // 3579358337
void Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms), &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_Mode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "ModifyLandscapeHeight", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execModifyLandscapeHeight)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_ENUM(EAuracronPCGHeightModificationMode,Z_Param_Mode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeight(Z_Param_Landscape,Z_Param_Out_WorldLocation,Z_Param_Height,Z_Param_Radius,EAuracronPCGHeightModificationMode(Z_Param_Mode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function ModifyLandscapeHeight *******

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function ModifyLandscapeHeightBatch 
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms
	{
		ALandscape* Landscape;
		TArray<FVector> Locations;
		TArray<float> Heights;
		float Radius;
		EAuracronPCGHeightModificationMode Mode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Locations_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Heights_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Locations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Locations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Heights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Heights;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Locations_Inner = { "Locations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Locations = { "Locations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms, Locations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Locations_MetaData), NewProp_Locations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Heights_Inner = { "Heights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Heights = { "Heights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms, Heights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Heights_MetaData), NewProp_Heights_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms, Mode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGHeightModificationMode, METADATA_PARAMS(0, nullptr) }; // 3579358337
void Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms), &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Locations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Locations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Heights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Heights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_Mode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "ModifyLandscapeHeightBatch", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::AuracronPCGLandscapeIntegrationUtils_eventModifyLandscapeHeightBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execModifyLandscapeHeightBatch)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Locations);
	P_GET_TARRAY_REF(float,Z_Param_Out_Heights);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_ENUM(EAuracronPCGHeightModificationMode,Z_Param_Mode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeightBatch(Z_Param_Landscape,Z_Param_Out_Locations,Z_Param_Out_Heights,Z_Param_Radius,EAuracronPCGHeightModificationMode(Z_Param_Mode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function ModifyLandscapeHeightBatch **

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function PaintLandscapeLayer *******
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms
	{
		ALandscape* Landscape;
		FString LayerName;
		FVector WorldLocation;
		float Strength;
		float Radius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer painting\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer painting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Strength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_Strength = { "Strength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms, Strength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms), &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_Strength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "PaintLandscapeLayer", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execPaintLandscapeLayer)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerName);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Strength);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::PaintLandscapeLayer(Z_Param_Landscape,Z_Param_LayerName,Z_Param_Out_WorldLocation,Z_Param_Strength,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function PaintLandscapeLayer *********

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function PaintLandscapeLayerBatch **
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms
	{
		ALandscape* Landscape;
		FString LayerName;
		TArray<FVector> Locations;
		TArray<float> Strengths;
		float Radius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Locations_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Strengths_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Locations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Locations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Strengths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Strengths;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Locations_Inner = { "Locations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Locations = { "Locations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms, Locations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Locations_MetaData), NewProp_Locations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Strengths_Inner = { "Strengths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Strengths = { "Strengths", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms, Strengths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Strengths_MetaData), NewProp_Strengths_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms), &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Locations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Locations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Strengths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Strengths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "PaintLandscapeLayerBatch", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::AuracronPCGLandscapeIntegrationUtils_eventPaintLandscapeLayerBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execPaintLandscapeLayerBatch)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerName);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Locations);
	P_GET_TARRAY_REF(float,Z_Param_Out_Strengths);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::PaintLandscapeLayerBatch(Z_Param_Landscape,Z_Param_LayerName,Z_Param_Out_Locations,Z_Param_Out_Strengths,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function PaintLandscapeLayerBatch ****

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function SampleAllLandscapeLayers **
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventSampleAllLandscapeLayers_Parms
	{
		ALandscape* Landscape;
		FVector WorldLocation;
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleAllLandscapeLayers_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleAllLandscapeLayers_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleAllLandscapeLayers_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "SampleAllLandscapeLayers", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleAllLandscapeLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleAllLandscapeLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execSampleAllLandscapeLayers)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::SampleAllLandscapeLayers(Z_Param_Landscape,Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function SampleAllLandscapeLayers ****

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeHeight *****
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms
	{
		ALandscape* Landscape;
		FVector WorldLocation;
		bool bUseInterpolation;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Height sampling\n" },
#endif
		{ "CPP_Default_bUseInterpolation", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height sampling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static void NewProp_bUseInterpolation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseInterpolation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
void Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_bUseInterpolation_SetBit(void* Obj)
{
	((AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms*)Obj)->bUseInterpolation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_bUseInterpolation = { "bUseInterpolation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms), &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_bUseInterpolation_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_bUseInterpolation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "SampleLandscapeHeight", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeHeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeHeight)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_GET_UBOOL(Z_Param_bUseInterpolation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Z_Param_Landscape,Z_Param_Out_WorldLocation,Z_Param_bUseInterpolation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeHeight *******

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeLayer ******
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeLayer_Parms
	{
		ALandscape* Landscape;
		FString LayerName;
		FVector WorldLocation;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer sampling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer sampling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeLayer_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeLayer_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeLayer_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeLayer_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "SampleLandscapeLayer", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeLayer)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerName);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeLayer(Z_Param_Landscape,Z_Param_LayerName,Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeLayer ********

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeNormal *****
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeNormal_Parms
	{
		ALandscape* Landscape;
		FVector WorldLocation;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeNormal_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeNormal_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeNormal_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "SampleLandscapeNormal", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeNormal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeNormal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeNormal)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeNormal(Z_Param_Landscape,Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeNormal *******

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeSlope ******
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeSlope_Parms
	{
		ALandscape* Landscape;
		FVector WorldLocation;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeSlope_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeSlope_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeSlope_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "SampleLandscapeSlope", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeSlope_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::AuracronPCGLandscapeIntegrationUtils_eventSampleLandscapeSlope_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeSlope)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeSlope(Z_Param_Landscape,Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function SampleLandscapeSlope ********

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function SimulateErosion ***********
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms
	{
		ALandscape* Landscape;
		FAuracronPCGErosionDescriptor ErosionDescriptor;
		FBox SimulationArea;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Erosion simulation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Erosion simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErosionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationArea_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ErosionDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SimulationArea;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_ErosionDescriptor = { "ErosionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms, ErosionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErosionDescriptor_MetaData), NewProp_ErosionDescriptor_MetaData) }; // 1631345082
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_SimulationArea = { "SimulationArea", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms, SimulationArea), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationArea_MetaData), NewProp_SimulationArea_MetaData) };
void Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms), &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_ErosionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_SimulationArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "SimulateErosion", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::AuracronPCGLandscapeIntegrationUtils_eventSimulateErosion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execSimulateErosion)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FAuracronPCGErosionDescriptor,Z_Param_Out_ErosionDescriptor);
	P_GET_STRUCT_REF(FBox,Z_Param_Out_SimulationArea);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::SimulateErosion(Z_Param_Landscape,Z_Param_Out_ErosionDescriptor,Z_Param_Out_SimulationArea);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function SimulateErosion *************

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils Function WorldLocationToLandscapeCoordinate 
struct Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics
{
	struct AuracronPCGLandscapeIntegrationUtils_eventWorldLocationToLandscapeCoordinate_Parms
	{
		ALandscape* Landscape;
		FVector WorldLocation;
		FIntPoint ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Landscape Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Landscape;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::NewProp_Landscape = { "Landscape", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventWorldLocationToLandscapeCoordinate_Parms, Landscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventWorldLocationToLandscapeCoordinate_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGLandscapeIntegrationUtils_eventWorldLocationToLandscapeCoordinate_Parms, ReturnValue), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::NewProp_Landscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, nullptr, "WorldLocationToLandscapeCoordinate", Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::AuracronPCGLandscapeIntegrationUtils_eventWorldLocationToLandscapeCoordinate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::AuracronPCGLandscapeIntegrationUtils_eventWorldLocationToLandscapeCoordinate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGLandscapeIntegrationUtils::execWorldLocationToLandscapeCoordinate)
{
	P_GET_OBJECT(ALandscape,Z_Param_Landscape);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FIntPoint*)Z_Param__Result=UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Z_Param_Landscape,Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils Function WorldLocationToLandscapeCoordinate 

// ********** Begin Class UAuracronPCGLandscapeIntegrationUtils ************************************
void UAuracronPCGLandscapeIntegrationUtils::StaticRegisterNativesUAuracronPCGLandscapeIntegrationUtils()
{
	UClass* Class = UAuracronPCGLandscapeIntegrationUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "FindLandscapeInWorld", &UAuracronPCGLandscapeIntegrationUtils::execFindLandscapeInWorld },
		{ "GetAllLandscapesInWorld", &UAuracronPCGLandscapeIntegrationUtils::execGetAllLandscapesInWorld },
		{ "GetLandscapeBounds", &UAuracronPCGLandscapeIntegrationUtils::execGetLandscapeBounds },
		{ "GetLandscapeLayerNames", &UAuracronPCGLandscapeIntegrationUtils::execGetLandscapeLayerNames },
		{ "IsPointOnLandscape", &UAuracronPCGLandscapeIntegrationUtils::execIsPointOnLandscape },
		{ "LandscapeCoordinateToWorldLocation", &UAuracronPCGLandscapeIntegrationUtils::execLandscapeCoordinateToWorldLocation },
		{ "ModifyLandscapeHeight", &UAuracronPCGLandscapeIntegrationUtils::execModifyLandscapeHeight },
		{ "ModifyLandscapeHeightBatch", &UAuracronPCGLandscapeIntegrationUtils::execModifyLandscapeHeightBatch },
		{ "PaintLandscapeLayer", &UAuracronPCGLandscapeIntegrationUtils::execPaintLandscapeLayer },
		{ "PaintLandscapeLayerBatch", &UAuracronPCGLandscapeIntegrationUtils::execPaintLandscapeLayerBatch },
		{ "SampleAllLandscapeLayers", &UAuracronPCGLandscapeIntegrationUtils::execSampleAllLandscapeLayers },
		{ "SampleLandscapeHeight", &UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeHeight },
		{ "SampleLandscapeLayer", &UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeLayer },
		{ "SampleLandscapeNormal", &UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeNormal },
		{ "SampleLandscapeSlope", &UAuracronPCGLandscapeIntegrationUtils::execSampleLandscapeSlope },
		{ "SimulateErosion", &UAuracronPCGLandscapeIntegrationUtils::execSimulateErosion },
		{ "WorldLocationToLandscapeCoordinate", &UAuracronPCGLandscapeIntegrationUtils::execWorldLocationToLandscapeCoordinate },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils;
UClass* UAuracronPCGLandscapeIntegrationUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGLandscapeIntegrationUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGLandscapeIntegrationUtils"),
			Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGLandscapeIntegrationUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_NoRegister()
{
	return UAuracronPCGLandscapeIntegrationUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Landscape Integration Utilities\n * Utility functions for advanced landscape integration operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGLandscapeIntegration.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGLandscapeIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape Integration Utilities\nUtility functions for advanced landscape integration operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_FindLandscapeInWorld, "FindLandscapeInWorld" }, // 1562457121
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetAllLandscapesInWorld, "GetAllLandscapesInWorld" }, // 2288335721
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeBounds, "GetLandscapeBounds" }, // 418648668
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_GetLandscapeLayerNames, "GetLandscapeLayerNames" }, // 1762009905
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_IsPointOnLandscape, "IsPointOnLandscape" }, // 3012765163
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_LandscapeCoordinateToWorldLocation, "LandscapeCoordinateToWorldLocation" }, // 229179812
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeight, "ModifyLandscapeHeight" }, // 3997862523
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_ModifyLandscapeHeightBatch, "ModifyLandscapeHeightBatch" }, // 105155873
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayer, "PaintLandscapeLayer" }, // 2288411060
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_PaintLandscapeLayerBatch, "PaintLandscapeLayerBatch" }, // 3834283875
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleAllLandscapeLayers, "SampleAllLandscapeLayers" }, // 1833046368
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeHeight, "SampleLandscapeHeight" }, // 699263184
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeLayer, "SampleLandscapeLayer" }, // 1135596455
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeNormal, "SampleLandscapeNormal" }, // 509667003
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SampleLandscapeSlope, "SampleLandscapeSlope" }, // 3060556957
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_SimulateErosion, "SimulateErosion" }, // 2918397673
		{ &Z_Construct_UFunction_UAuracronPCGLandscapeIntegrationUtils_WorldLocationToLandscapeCoordinate, "WorldLocationToLandscapeCoordinate" }, // 1338168857
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGLandscapeIntegrationUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics::ClassParams = {
	&UAuracronPCGLandscapeIntegrationUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils.OuterSingleton;
}
UAuracronPCGLandscapeIntegrationUtils::UAuracronPCGLandscapeIntegrationUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGLandscapeIntegrationUtils);
UAuracronPCGLandscapeIntegrationUtils::~UAuracronPCGLandscapeIntegrationUtils() {}
// ********** End Class UAuracronPCGLandscapeIntegrationUtils **************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGLandscapeSamplingMode_StaticEnum, TEXT("EAuracronPCGLandscapeSamplingMode"), &Z_Registration_Info_UEnum_EAuracronPCGLandscapeSamplingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 977814554U) },
		{ EAuracronPCGLandscapeBlendMode_StaticEnum, TEXT("EAuracronPCGLandscapeBlendMode"), &Z_Registration_Info_UEnum_EAuracronPCGLandscapeBlendMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3778115934U) },
		{ EAuracronPCGErosionType_StaticEnum, TEXT("EAuracronPCGErosionType"), &Z_Registration_Info_UEnum_EAuracronPCGErosionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3061212850U) },
		{ EAuracronPCGLayerPaintMode_StaticEnum, TEXT("EAuracronPCGLayerPaintMode"), &Z_Registration_Info_UEnum_EAuracronPCGLayerPaintMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 588648483U) },
		{ EAuracronPCGHeightModificationMode_StaticEnum, TEXT("EAuracronPCGHeightModificationMode"), &Z_Registration_Info_UEnum_EAuracronPCGHeightModificationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3579358337U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGLandscapeSamplingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor_Statics::NewStructOps, TEXT("AuracronPCGLandscapeSamplingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGLandscapeSamplingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGLandscapeSamplingDescriptor), 3790163207U) },
		{ FAuracronPCGErosionDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGErosionDescriptor_Statics::NewStructOps, TEXT("AuracronPCGErosionDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGErosionDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGErosionDescriptor), 1631345082U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings, UAuracronPCGAdvancedLandscapeSamplerSettings::StaticClass, TEXT("UAuracronPCGAdvancedLandscapeSamplerSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedLandscapeSamplerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedLandscapeSamplerSettings), 2235233542U) },
		{ Z_Construct_UClass_UAuracronPCGLandscapeHeightModifierSettings, UAuracronPCGLandscapeHeightModifierSettings::StaticClass, TEXT("UAuracronPCGLandscapeHeightModifierSettings"), &Z_Registration_Info_UClass_UAuracronPCGLandscapeHeightModifierSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGLandscapeHeightModifierSettings), 3523571103U) },
		{ Z_Construct_UClass_UAuracronPCGLandscapeLayerPainterSettings, UAuracronPCGLandscapeLayerPainterSettings::StaticClass, TEXT("UAuracronPCGLandscapeLayerPainterSettings"), &Z_Registration_Info_UClass_UAuracronPCGLandscapeLayerPainterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGLandscapeLayerPainterSettings), 1067624201U) },
		{ Z_Construct_UClass_UAuracronPCGLandscapeErosionSimulatorSettings, UAuracronPCGLandscapeErosionSimulatorSettings::StaticClass, TEXT("UAuracronPCGLandscapeErosionSimulatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGLandscapeErosionSimulatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGLandscapeErosionSimulatorSettings), 2307424918U) },
		{ Z_Construct_UClass_UAuracronPCGLandscapeIntegrationUtils, UAuracronPCGLandscapeIntegrationUtils::StaticClass, TEXT("UAuracronPCGLandscapeIntegrationUtils"), &Z_Registration_Info_UClass_UAuracronPCGLandscapeIntegrationUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGLandscapeIntegrationUtils), 2299455693U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_627030476(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLandscapeIntegration_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
