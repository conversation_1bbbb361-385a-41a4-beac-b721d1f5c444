// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Element Library Implementation
// Bridge 2.3: PCG Framework - Element Library

#include "AuracronPCGElementLibrary.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGNodeSystem.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeInfo.h"
#include "LandscapeComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"

// Initialize static member
bool UAuracronPCGElementLibrary::bElementsRegistered = false;

// =============================================================================
// POINT GRID GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGPointGridSettings::UAuracronPCGPointGridSettings()
{
    NodeMetadata.NodeName = TEXT("Point Grid Generator");
    NodeMetadata.NodeDescription = TEXT("Generates points in various grid patterns with noise and variation");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Generator;
    NodeMetadata.Tags.Add(TEXT("Generator"));
    NodeMetadata.Tags.Add(TEXT("Grid"));
    NodeMetadata.Tags.Add(TEXT("Points"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.2f);
}

void UAuracronPCGPointGridSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    // Optional bounds input
    FPCGPinProperties& BoundsPin = InputPins.Emplace_GetRef();
    BoundsPin.Label = TEXT("Bounds");
    BoundsPin.AllowedTypes = EPCGDataType::Spatial;
    BoundsPin.bAllowMultipleConnections = false;
    BoundsPin.bAdvancedPin = true;
}

void UAuracronPCGPointGridSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Points");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGPointGridElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                   FPCGDataCollection& OutputData, 
                                                                   const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGPointGridSettings* Settings = GetTypedSettings<UAuracronPCGPointGridSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Point Grid Generator");
            return Result;
        }

        // Determine bounds
        FBox GenerationBounds;
        if (InputData.GetInputs().Num() > 0 && InputData.GetInputs()[0].Data)
        {
            // Use input bounds
            if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(InputData.GetInputs()[0].Data))
            {
                GenerationBounds = SpatialData->GetBounds();
            }
            else
            {
                GenerationBounds = FBox(FVector(-Settings->GridSize.X * 0.5f, -Settings->GridSize.Y * 0.5f, -100.0f),
                                       FVector(Settings->GridSize.X * 0.5f, Settings->GridSize.Y * 0.5f, 100.0f));
            }
        }
        else
        {
            // Use default bounds from settings
            GenerationBounds = FBox(FVector(-Settings->GridSize.X * 0.5f, -Settings->GridSize.Y * 0.5f, -100.0f),
                                   FVector(Settings->GridSize.X * 0.5f, Settings->GridSize.Y * 0.5f, 100.0f));
        }

        // Create output point data
        UPCGPointData* PointData = NewObject<UPCGPointData>();
        PointData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& Points = PointData->GetMutablePoints();

        // Generate points based on distribution pattern
        TArray<FVector> Positions = AuracronPCGElementUtils::GenerateDistributionPattern(
            Settings->DistributionPattern, GenerationBounds, Settings->GridSpacing, Settings->Jitter);

        // Create PCG points
        for (const FVector& Position : Positions)
        {
            // Apply noise filtering if enabled
            if (Settings->bUseNoise)
            {
                float NoiseValue = AuracronPCGElementUtils::GenerateNoise(
                    Settings->NoiseType, Position, Settings->NoiseScale, Settings->NoiseOctaves);
                
                if (NoiseValue < Settings->NoiseThreshold)
                {
                    continue; // Skip this point
                }
            }

            FPCGPoint& Point = Points.Emplace_GetRef();
            Point.Transform.SetLocation(Position);
            Point.Transform.SetRotation(FQuat::Identity);
            Point.Transform.SetScale3D(FVector::OneVector);

            // Set density
            float Density = Settings->BaseDensity;
            if (Settings->DensityVariation > 0.0f)
            {
                float DensityNoise = AuracronPCGElementUtils::GenerateNoise(
                    EAuracronPCGNoiseType::Perlin, Position, 0.001f, 1);
                Density *= FMath::Lerp(1.0f - Settings->DensityVariation, 1.0f + Settings->DensityVariation, 
                                      (DensityNoise + 1.0f) * 0.5f);
            }
            Point.Density = FMath::Clamp(Density, 0.0f, 1.0f);

            // Set bounds
            Point.SetLocalBounds(FBox(FVector(-Settings->GridSpacing * 0.5f), FVector(Settings->GridSpacing * 0.5f)));
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = PointData;
        TaggedData.Pin = TEXT("Points");

        Result.bSuccess = true;
        Result.PointsProcessed = Points.Num();
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Point Grid Generator created %d points"), Points.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Point Grid Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// BIOME GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGBiomeGeneratorSettings::UAuracronPCGBiomeGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("Biome Generator");
    NodeMetadata.NodeDescription = TEXT("Generates biome-specific content with ecosystem rules");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Generator;
    NodeMetadata.Tags.Add(TEXT("Generator"));
    NodeMetadata.Tags.Add(TEXT("Biome"));
    NodeMetadata.Tags.Add(TEXT("Ecosystem"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.6f, 0.8f);
}

void UAuracronPCGBiomeGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Surface");
    InputPin.AllowedTypes = EPCGDataType::Surface;
    InputPin.bAllowMultipleConnections = false;
}

void UAuracronPCGBiomeGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& VegetationPin = OutputPins.Emplace_GetRef();
    VegetationPin.Label = TEXT("Vegetation");
    VegetationPin.AllowedTypes = EPCGDataType::Point;

    FPCGPinProperties& EnvironmentPin = OutputPins.Emplace_GetRef();
    EnvironmentPin.Label = TEXT("Environment");
    EnvironmentPin.AllowedTypes = EPCGDataType::Point;
    EnvironmentPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGBiomeGeneratorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                        FPCGDataCollection& OutputData, 
                                                                        const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGBiomeGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGBiomeGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Biome Generator");
            return Result;
        }

        // Validate input
        if (InputData.GetInputs().Num() == 0 || !InputData.GetInputs()[0].Data)
        {
            Result.ErrorMessage = TEXT("No surface input provided");
            return Result;
        }

        const UPCGSpatialData* SurfaceData = Cast<UPCGSpatialData>(InputData.GetInputs()[0].Data);
        if (!SurfaceData)
        {
            Result.ErrorMessage = TEXT("Invalid surface data");
            return Result;
        }

        // Sample surface for points
        UPCGPointData* SampledPoints = SurfaceData->ToPointData();
        if (!SampledPoints)
        {
            Result.ErrorMessage = TEXT("Failed to sample surface");
            return Result;
        }

        // Create vegetation output
        UPCGPointData* VegetationData = NewObject<UPCGPointData>();
        VegetationData->InitializeFromData(SampledPoints);
        TArray<FPCGPoint>& VegetationPoints = VegetationData->GetMutablePoints();

        // Create environment output
        UPCGPointData* EnvironmentData = NewObject<UPCGPointData>();
        EnvironmentData->InitializeFromData(SampledPoints);
        TArray<FPCGPoint>& EnvironmentPoints = EnvironmentData->GetMutablePoints();

        // Process each sampled point
        const TArray<FPCGPoint>& SourcePoints = SampledPoints->GetPoints();
        for (const FPCGPoint& SourcePoint : SourcePoints)
        {
            FVector Position = SourcePoint.Transform.GetLocation();

            // Check biome validity
            if (!AuracronPCGElementUtils::IsPointValidForBiome(Position, Settings->BiomeType, Settings->BiomeIntensity))
            {
                continue;
            }

            // Check height constraints
            if (Position.Z < Settings->HeightRange.X || Position.Z > Settings->HeightRange.Y)
            {
                continue;
            }

            // Check slope constraints
            FVector Normal = AuracronPCGElementUtils::CalculateSurfaceNormal(Position);
            float Slope = AuracronPCGElementUtils::CalculateSlope(Normal);
            if (Slope > Settings->MaxSlope)
            {
                continue;
            }

            // Generate vegetation points
            if (Settings->VegetationMeshes.Num() > 0 && FMath::RandRange(0.0f, 1.0f) < Settings->VegetationDensity)
            {
                FPCGPoint& VegPoint = VegetationPoints.Emplace_GetRef(SourcePoint);
                
                // Add biome-specific attributes
                if (VegetationData->Metadata)
                {
                    VegetationData->Metadata->CreateFloatAttribute(TEXT("BiomeIntensity"), Settings->BiomeIntensity, false);
                    VegetationData->Metadata->CreateFloatAttribute(TEXT("MoistureLevel"), Settings->MoistureLevel, false);
                    VegetationData->Metadata->CreateFloatAttribute(TEXT("TemperatureLevel"), Settings->TemperatureLevel, false);
                }
            }

            // Generate environment points (rocks, debris, etc.)
            if (FMath::RandRange(0.0f, 1.0f) < 0.1f) // 10% chance for environment objects
            {
                FPCGPoint& EnvPoint = EnvironmentPoints.Emplace_GetRef(SourcePoint);
                EnvPoint.Density *= 0.5f; // Lower density for environment objects
            }
        }

        // Add vegetation output
        FPCGTaggedData& VegetationTaggedData = OutputData.TaggedData.Emplace_GetRef();
        VegetationTaggedData.Data = VegetationData;
        VegetationTaggedData.Pin = TEXT("Vegetation");

        // Add environment output
        FPCGTaggedData& EnvironmentTaggedData = OutputData.TaggedData.Emplace_GetRef();
        EnvironmentTaggedData.Data = EnvironmentData;
        EnvironmentTaggedData.Pin = TEXT("Environment");

        Result.bSuccess = true;
        Result.PointsProcessed = SourcePoints.Num();
        Result.OutputDataCount = 2;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Biome Generator created %d vegetation points and %d environment points"), 
                                  VegetationPoints.Num(), EnvironmentPoints.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Biome Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// ADVANCED SURFACE SAMPLER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedSurfaceSamplerSettings::UAuracronPCGAdvancedSurfaceSamplerSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Surface Sampler");
    NodeMetadata.NodeDescription = TEXT("Enhanced surface sampling with multiple sampling strategies");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Sampler;
    NodeMetadata.Tags.Add(TEXT("Sampler"));
    NodeMetadata.Tags.Add(TEXT("Surface"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.4f, 0.2f);
}

void UAuracronPCGAdvancedSurfaceSamplerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& SurfacePin = InputPins.Emplace_GetRef();
    SurfacePin.Label = TEXT("Surface");
    SurfacePin.AllowedTypes = EPCGDataType::Surface;
    SurfacePin.bAllowMultipleConnections = false;
}

void UAuracronPCGAdvancedSurfaceSamplerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Points");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGAdvancedSurfaceSamplerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                FPCGDataCollection& OutputData, 
                                                                                const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedSurfaceSamplerSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedSurfaceSamplerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Surface Sampler");
            return Result;
        }

        // Validate input
        if (InputData.GetInputs().Num() == 0 || !InputData.GetInputs()[0].Data)
        {
            Result.ErrorMessage = TEXT("No surface input provided");
            return Result;
        }

        const UPCGSpatialData* SurfaceData = Cast<UPCGSpatialData>(InputData.GetInputs()[0].Data);
        if (!SurfaceData)
        {
            Result.ErrorMessage = TEXT("Invalid surface data");
            return Result;
        }

        // Create output point data
        UPCGPointData* PointData = NewObject<UPCGPointData>();
        PointData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& Points = PointData->GetMutablePoints();

        // Get surface bounds
        FBox SurfaceBounds = SurfaceData->GetBounds();
        
        // Generate sampling positions based on pattern
        TArray<FVector> SamplingPositions = AuracronPCGElementUtils::GenerateDistributionPattern(
            Settings->SamplingPattern, SurfaceBounds, 
            1.0f / FMath::Sqrt(Settings->PointsPerSquareMeter), Settings->SamplingJitter);

        // Sample surface at each position
        for (const FVector& Position : SamplingPositions)
        {
            // Project to surface if enabled
            FVector SampledPosition = Position;
            if (Settings->bProjectToSurface)
            {
                FVector ProjectedPosition;
                if (SurfaceData->ProjectPoint(Position, FBox(Position - FVector(Settings->ProjectionDistance), 
                                                           Position + FVector(Settings->ProjectionDistance)), 
                                            FPCGProjectionParams(), ProjectedPosition, nullptr))
                {
                    SampledPosition = ProjectedPosition;
                }
                else
                {
                    continue; // Skip if projection failed
                }
            }

            // Check surface angle constraint
            FVector Normal = AuracronPCGElementUtils::CalculateSurfaceNormal(SampledPosition);
            float SurfaceAngle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(Normal, FVector::UpVector)));
            
            if (SurfaceAngle > Settings->MaxSurfaceAngle)
            {
                continue; // Skip steep surfaces
            }

            // Create point
            FPCGPoint& Point = Points.Emplace_GetRef();
            Point.Transform.SetLocation(SampledPosition);
            Point.Transform.SetRotation(FQuat::FindBetweenNormals(FVector::UpVector, Normal));
            Point.Transform.SetScale3D(FVector::OneVector);
            Point.Density = 1.0f;

            // Set bounds based on sampling density
            float PointSize = 1.0f / FMath::Sqrt(Settings->PointsPerSquareMeter);
            Point.SetLocalBounds(FBox(FVector(-PointSize * 0.5f), FVector(PointSize * 0.5f)));
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = PointData;
        TaggedData.Pin = TEXT("Points");

        Result.bSuccess = true;
        Result.PointsProcessed = Points.Num();
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Surface Sampler created %d points"), Points.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Surface Sampler error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// LANDSCAPE SAMPLER IMPLEMENTATION
// =============================================================================

UAuracronPCGLandscapeSamplerSettings::UAuracronPCGLandscapeSamplerSettings()
{
    NodeMetadata.NodeName = TEXT("Landscape Sampler");
    NodeMetadata.NodeDescription = TEXT("Specialized sampler for landscape data with height and material sampling");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Landscape;
    NodeMetadata.Tags.Add(TEXT("Sampler"));
    NodeMetadata.Tags.Add(TEXT("Landscape"));
    NodeMetadata.Tags.Add(TEXT("Terrain"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.8f, 0.2f);
}

void UAuracronPCGLandscapeSamplerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& LandscapePin = InputPins.Emplace_GetRef();
    LandscapePin.Label = TEXT("Landscape");
    LandscapePin.AllowedTypes = EPCGDataType::Landscape;
    LandscapePin.bAllowMultipleConnections = false;
}

void UAuracronPCGLandscapeSamplerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Points");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGLandscapeSamplerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                          FPCGDataCollection& OutputData,
                                                                          const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGLandscapeSamplerSettings* Settings = GetTypedSettings<UAuracronPCGLandscapeSamplerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Landscape Sampler");
            return Result;
        }

        // Validate input
        if (InputData.GetInputs().Num() == 0 || !InputData.GetInputs()[0].Data)
        {
            Result.ErrorMessage = TEXT("No landscape input provided");
            return Result;
        }

        const UPCGSpatialData* LandscapeData = Cast<UPCGSpatialData>(InputData.GetInputs()[0].Data);
        if (!LandscapeData)
        {
            Result.ErrorMessage = TEXT("Invalid landscape data");
            return Result;
        }

        // Create output point data
        UPCGPointData* PointData = NewObject<UPCGPointData>();
        PointData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& Points = PointData->GetMutablePoints();

        // Get landscape bounds
        FBox LandscapeBounds = LandscapeData->GetBounds();

        // Calculate sampling resolution
        float SamplingStep = 100.0f / Settings->SamplingResolution; // Base 100cm step

        // Sample landscape in grid pattern
        for (float X = LandscapeBounds.Min.X; X <= LandscapeBounds.Max.X; X += SamplingStep)
        {
            for (float Y = LandscapeBounds.Min.Y; Y <= LandscapeBounds.Max.Y; Y += SamplingStep)
            {
                FVector SamplePosition(X, Y, 0.0f);

                // Sample height if enabled
                float Height = 0.0f;
                if (Settings->bSampleHeight)
                {
                    if (!AuracronPCGElementUtils::SampleLandscapeHeight(SamplePosition, Height))
                    {
                        continue; // Skip if height sampling failed
                    }
                    SamplePosition.Z = Height;
                }

                // Check height constraints
                if (Height < Settings->HeightRange.X || Height > Settings->HeightRange.Y)
                {
                    continue;
                }

                // Sample slope if enabled
                float Slope = 0.0f;
                if (Settings->bSampleSlope)
                {
                    FVector Normal = AuracronPCGElementUtils::CalculateSurfaceNormal(SamplePosition);
                    Slope = AuracronPCGElementUtils::CalculateSlope(Normal);

                    if (Slope > Settings->MaxSlope)
                    {
                        continue;
                    }
                }

                // Create point
                FPCGPoint& Point = Points.Emplace_GetRef();
                Point.Transform.SetLocation(SamplePosition);
                Point.Transform.SetRotation(FQuat::Identity);
                Point.Transform.SetScale3D(FVector::OneVector);
                Point.Density = 1.0f;
                Point.SetLocalBounds(FBox(FVector(-SamplingStep * 0.5f), FVector(SamplingStep * 0.5f)));

                // Add landscape-specific attributes
                if (PointData->Metadata)
                {
                    if (Settings->bSampleHeight)
                    {
                        PointData->Metadata->CreateFloatAttribute(TEXT("Height"), Height, false);
                    }

                    if (Settings->bSampleSlope)
                    {
                        PointData->Metadata->CreateFloatAttribute(TEXT("Slope"), Slope, false);
                    }

                    // Sample material layers if enabled
                    if (Settings->bSampleMaterials)
                    {
                        for (const FName& LayerName : Settings->MaterialLayerNames)
                        {
                            float LayerWeight = 0.0f;
                            if (AuracronPCGElementUtils::SampleLandscapeMaterial(SamplePosition, LayerName, LayerWeight))
                            {
                                FString AttributeName = FString::Printf(TEXT("Material_%s"), *LayerName.ToString());
                                PointData->Metadata->CreateFloatAttribute(FName(*AttributeName), LayerWeight, false);
                            }
                        }
                    }
                }
            }
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = PointData;
        TaggedData.Pin = TEXT("Points");

        Result.bSuccess = true;
        Result.PointsProcessed = Points.Num();
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Landscape Sampler created %d points"), Points.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Landscape Sampler error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// ADVANCED FILTER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedFilterSettings::UAuracronPCGAdvancedFilterSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Filter");
    NodeMetadata.NodeDescription = TEXT("Multi-criteria point filtering with complex conditions");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Filter;
    NodeMetadata.Tags.Add(TEXT("Filter"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Multi-criteria"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.6f);
}

void UAuracronPCGAdvancedFilterSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedFilterSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& PassedPin = OutputPins.Emplace_GetRef();
    PassedPin.Label = TEXT("Passed");
    PassedPin.AllowedTypes = EPCGDataType::Point;

    FPCGPinProperties& FilteredPin = OutputPins.Emplace_GetRef();
    FilteredPin.Label = TEXT("Filtered");
    FilteredPin.AllowedTypes = EPCGDataType::Point;
    FilteredPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGAdvancedFilterElement::ProcessData(const FPCGDataCollection& InputData,
                                                                        FPCGDataCollection& OutputData,
                                                                        const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedFilterSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedFilterSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Filter");
            return Result;
        }

        // Create output point data
        UPCGPointData* PassedData = NewObject<UPCGPointData>();
        PassedData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& PassedPoints = PassedData->GetMutablePoints();

        UPCGPointData* FilteredData = NewObject<UPCGPointData>();
        FilteredData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& FilteredPoints = FilteredData->GetMutablePoints();

        int32 TotalProcessed = 0;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();

            for (const FPCGPoint& Point : InputPoints)
            {
                TotalProcessed++;
                bool bPassesFilter = true;
                FVector Position = Point.Transform.GetLocation();

                // Density filter
                if (Settings->bFilterByDensity)
                {
                    if (Point.Density < Settings->DensityRange.X || Point.Density > Settings->DensityRange.Y)
                    {
                        bPassesFilter = false;
                    }
                }

                // Height filter
                if (bPassesFilter && Settings->bFilterByHeight)
                {
                    if (Position.Z < Settings->HeightRange.X || Position.Z > Settings->HeightRange.Y)
                    {
                        bPassesFilter = false;
                    }
                }

                // Slope filter
                if (bPassesFilter && Settings->bFilterBySlope)
                {
                    FVector Normal = AuracronPCGElementUtils::CalculateSurfaceNormal(Position);
                    float Slope = AuracronPCGElementUtils::CalculateSlope(Normal);

                    if (Slope > Settings->MaxSlope)
                    {
                        bPassesFilter = false;
                    }
                }

                // Noise filter
                if (bPassesFilter && Settings->bUseNoiseFilter)
                {
                    float NoiseValue = AuracronPCGElementUtils::GenerateNoise(
                        Settings->NoiseType, Position, Settings->NoiseScale, 1);

                    if (NoiseValue < Settings->NoiseThreshold)
                    {
                        bPassesFilter = false;
                    }
                }

                // Add to appropriate output
                if (bPassesFilter)
                {
                    PassedPoints.Add(Point);
                }
                else
                {
                    FilteredPoints.Add(Point);
                }
            }

            // Copy metadata if available
            if (InputPointData->Metadata)
            {
                if (!PassedData->Metadata)
                {
                    PassedData->Metadata = InputPointData->Metadata->Copy();
                }
                if (!FilteredData->Metadata)
                {
                    FilteredData->Metadata = InputPointData->Metadata->Copy();
                }
            }
        }

        // Add passed points to output
        FPCGTaggedData& PassedTaggedData = OutputData.TaggedData.Emplace_GetRef();
        PassedTaggedData.Data = PassedData;
        PassedTaggedData.Pin = TEXT("Passed");

        // Add filtered points to output
        FPCGTaggedData& FilteredTaggedData = OutputData.TaggedData.Emplace_GetRef();
        FilteredTaggedData.Data = FilteredData;
        FilteredTaggedData.Pin = TEXT("Filtered");

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = 2;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Filter processed %d points (%d passed, %d filtered)"),
                                  TotalProcessed, PassedPoints.Num(), FilteredPoints.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Filter error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// ELEMENT LIBRARY MANAGER IMPLEMENTATION
// =============================================================================

UAuracronPCGElementLibrary::UAuracronPCGElementLibrary()
{
}

void UAuracronPCGElementLibrary::RegisterAllElements()
{
    if (bElementsRegistered)
    {
        return;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registering all PCG elements in library"));

    RegisterGeneratorElements();
    RegisterSamplerElements();
    RegisterFilterElements();
    RegisterTransformerElements();
    RegisterSpawnerElements();
    RegisterUtilityElements();

    bElementsRegistered = true;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Element Library registration completed"));
}

TArray<TSubclassOf<UAuracronPCGNodeSettings>> UAuracronPCGElementLibrary::GetElementsByCategory(EAuracronPCGElementCategory Category)
{
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> Elements;

    // Get global registry
    UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
    if (!Registry)
    {
        return Elements;
    }

    // Convert category to node category
    EAuracronPCGNodeCategory NodeCategory = EAuracronPCGNodeCategory::Custom;
    switch (Category)
    {
        case EAuracronPCGElementCategory::Generators:
            NodeCategory = EAuracronPCGNodeCategory::Generator;
            break;
        case EAuracronPCGElementCategory::Samplers:
            NodeCategory = EAuracronPCGNodeCategory::Sampler;
            break;
        case EAuracronPCGElementCategory::Filters:
            NodeCategory = EAuracronPCGNodeCategory::Filter;
            break;
        case EAuracronPCGElementCategory::Transformers:
            NodeCategory = EAuracronPCGNodeCategory::Transform;
            break;
        case EAuracronPCGElementCategory::Utilities:
            NodeCategory = EAuracronPCGNodeCategory::Utility;
            break;
        default:
            NodeCategory = EAuracronPCGNodeCategory::Custom;
            break;
    }

    return Registry->GetNodesByCategory(NodeCategory);
}

TArray<FString> UAuracronPCGElementLibrary::GetAvailableCategories()
{
    TArray<FString> Categories;

    Categories.Add(TEXT("Generators"));
    Categories.Add(TEXT("Samplers"));
    Categories.Add(TEXT("Filters"));
    Categories.Add(TEXT("Transformers"));
    Categories.Add(TEXT("Spawners"));
    Categories.Add(TEXT("Utilities"));
    Categories.Add(TEXT("Noise"));
    Categories.Add(TEXT("Spatial"));
    Categories.Add(TEXT("Landscape"));
    Categories.Add(TEXT("Spline"));
    Categories.Add(TEXT("Custom"));

    return Categories;
}

int32 UAuracronPCGElementLibrary::GetElementCount()
{
    UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
    return Registry ? Registry->GetRegisteredNodeCount() : 0;
}

UAuracronPCGNodeSettings* UAuracronPCGElementLibrary::CreateElementInstance(TSubclassOf<UAuracronPCGNodeSettings> ElementClass)
{
    UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
    return Registry ? Registry->CreateNodeInstance(ElementClass) : nullptr;
}

bool UAuracronPCGElementLibrary::ValidateElementConfiguration(UAuracronPCGNodeSettings* ElementSettings, TArray<FString>& ValidationErrors)
{
    if (!ElementSettings)
    {
        ValidationErrors.Add(TEXT("Element settings is null"));
        return false;
    }

    return ElementSettings->ValidateNodeSettings(ValidationErrors);
}

void UAuracronPCGElementLibrary::RegisterGeneratorElements()
{
    UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
    if (!Registry)
    {
        return;
    }

    // Register generator elements
    Registry->RegisterNode(UAuracronPCGPointGridSettings::StaticClass());
    Registry->RegisterNode(UAuracronPCGBiomeGeneratorSettings::StaticClass());

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered generator elements"));
}

void UAuracronPCGElementLibrary::RegisterSamplerElements()
{
    UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
    if (!Registry)
    {
        return;
    }

    // Register sampler elements
    Registry->RegisterNode(UAuracronPCGAdvancedSurfaceSamplerSettings::StaticClass());
    Registry->RegisterNode(UAuracronPCGLandscapeSamplerSettings::StaticClass());

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered sampler elements"));
}

void UAuracronPCGElementLibrary::RegisterFilterElements()
{
    UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
    if (!Registry)
    {
        return;
    }

    // Register filter elements
    Registry->RegisterNode(UAuracronPCGAdvancedFilterSettings::StaticClass());

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered filter elements"));
}

void UAuracronPCGElementLibrary::RegisterTransformerElements()
{
    // Register transform elements using UE5.6 PCG API
    RegisteredElements.Add(UAuracronPCGTransformSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGRotateSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGScaleSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGProjectionSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGNormalizeSettings::StaticClass());
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered %d transformer elements"), 5);
}

void UAuracronPCGElementLibrary::RegisterSpawnerElements()
{
    // Register spawner elements using UE5.6 PCG API
    RegisteredElements.Add(UAuracronPCGStaticMeshSpawnerSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGInstancedMeshSpawnerSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGActorSpawnerSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGBlueprintSpawnerSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGFoliageSpawnerSettings::StaticClass());
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered %d spawner elements"), 5);
}

void UAuracronPCGElementLibrary::RegisterUtilityElements()
{
    // Register utility elements using UE5.6 PCG API
    RegisteredElements.Add(UAuracronPCGDebugSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGDataValidatorSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGPerformanceProfilerSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGCacheManagerSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGMetadataExtractorSettings::StaticClass());
    RegisteredElements.Add(UAuracronPCGBoundsCalculatorSettings::StaticClass());
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered %d utility elements"), 6);
}
