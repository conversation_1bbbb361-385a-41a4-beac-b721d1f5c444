// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Mesh Generation Advanced Utilities Implementation
// Bridge 2.6: PCG Framework - Mesh Generation Nodes

#include "AuracronPCGMeshGeneration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "PhysicsEngine/BodySetup.h"

// GeometryScript includes for UE5.6 boolean operations
#include "DynamicMesh/DynamicMesh3.h"
#include "GeometryScript/MeshBooleanFunctions.h"
#include "UDynamicMesh.h"
#include "GeometryScript/GeometryScriptTypes.h"

namespace AuracronPCGMeshGenerationUtils
{
    // =============================================================================
    // STATIC MESH CREATION
    // =============================================================================

    UStaticMesh* CreateStaticMeshFromProcMesh(const FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, const FString& MeshName)
    {
        // Create new static mesh
        UStaticMesh* StaticMesh = NewObject<UStaticMesh>();
        if (!StaticMesh)
        {
            return nullptr;
        }

        // Set up static mesh data
        StaticMesh->SetNumSourceModels(1);
        FStaticMeshSourceModel& SourceModel = StaticMesh->GetSourceModel(0);
        
        // Create mesh description
        FMeshDescription* MeshDescription = StaticMesh->CreateMeshDescription(0);
        if (!MeshDescription)
        {
            return nullptr;
        }

        // Initialize mesh description
        FStaticMeshAttributes Attributes(*MeshDescription);
        Attributes.Register();

        // Add vertices
        TArray<FVertexID> VertexIDs;
        VertexIDs.Reserve(MeshDescriptor.Vertices.Num());
        
        for (const FVector& Vertex : MeshDescriptor.Vertices)
        {
            FVertexID VertexID = MeshDescription->CreateVertex();
            MeshDescription->SetVertexPosition(VertexID, Vertex);
            VertexIDs.Add(VertexID);
        }

        // Add triangles
        TArray<FVertexInstanceID> VertexInstanceIDs;
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            if (i + 2 < MeshDescriptor.Triangles.Num())
            {
                TArray<FVertexID> TriangleVertices;
                TriangleVertices.Add(VertexIDs[MeshDescriptor.Triangles[i]]);
                TriangleVertices.Add(VertexIDs[MeshDescriptor.Triangles[i + 1]]);
                TriangleVertices.Add(VertexIDs[MeshDescriptor.Triangles[i + 2]]);

                // Create polygon
                FPolygonID PolygonID = MeshDescription->CreatePolygon(FPolygonGroupID(0), TriangleVertices);
            }
        }

        // Set material if available
        if (MeshDescriptor.Material.IsValid())
        {
            UMaterialInterface* Material = MeshDescriptor.Material.LoadSynchronous();
            if (Material)
            {
                StaticMesh->SetMaterial(0, Material);
            }
        }

        // Build static mesh
        StaticMesh->CommitMeshDescription(0);
        StaticMesh->Build();

        return StaticMesh;
    }

    // =============================================================================
    // INSTANCED MESH COMPONENT CREATION
    // =============================================================================

    UInstancedStaticMeshComponent* CreateInstancedMeshComponent(AActor* Owner, UStaticMesh* Mesh, bool bUseHierarchical)
    {
        if (!Mesh)
        {
            return nullptr;
        }

        UInstancedStaticMeshComponent* Component = nullptr;
        
        if (bUseHierarchical)
        {
            Component = NewObject<UHierarchicalInstancedStaticMeshComponent>(Owner ? Owner : GetTransientPackage());
        }
        else
        {
            Component = NewObject<UInstancedStaticMeshComponent>(Owner ? Owner : GetTransientPackage());
        }

        if (Component)
        {
            Component->SetStaticMesh(Mesh);
            
            // Configure default settings
            Component->SetCastShadow(true);
            Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            Component->SetCollisionResponseToAllChannels(ECR_Block);
            
            // Attach to owner if provided
            if (Owner && Owner->GetRootComponent())
            {
                Component->AttachToComponent(Owner->GetRootComponent(), 
                    FAttachmentTransformRules::KeepWorldTransform);
            }
        }

        return Component;
    }

    // =============================================================================
    // MATERIAL SETUP
    // =============================================================================

    bool SetupMeshMaterials(UMeshComponent* MeshComponent, const TArray<TSoftObjectPtr<UMaterialInterface>>& Materials)
    {
        if (!MeshComponent)
        {
            return false;
        }

        // Apply material overrides
        for (int32 i = 0; i < Materials.Num(); i++)
        {
            if (Materials[i].IsValid())
            {
                UMaterialInterface* Material = Materials[i].LoadSynchronous();
                if (Material)
                {
                    MeshComponent->SetMaterial(i, Material);
                }
            }
        }

        return true;
    }

    // =============================================================================
    // TRANSFORM CALCULATION
    // =============================================================================

    FTransform CalculateInstanceTransform(const FPCGPoint& Point, const FAuracronPCGMeshEntry& MeshEntry)
    {
        // Start with point transform
        FTransform InstanceTransform = Point.Transform;

        // Apply local offset
        FVector LocalOffset = MeshEntry.LocalOffset;
        InstanceTransform.AddToTranslation(InstanceTransform.TransformVector(LocalOffset));

        // Apply local rotation
        FQuat LocalRotation = MeshEntry.LocalRotation.Quaternion();
        InstanceTransform.SetRotation(InstanceTransform.GetRotation() * LocalRotation);

        // Apply local scale
        FVector CurrentScale = InstanceTransform.GetScale3D();
        FVector NewScale = CurrentScale * MeshEntry.LocalScale;
        InstanceTransform.SetScale3D(NewScale);

        return InstanceTransform;
    }

    // =============================================================================
    // MESH VALIDATION
    // =============================================================================

    bool ValidateMeshForInstancing(UStaticMesh* Mesh)
    {
        if (!Mesh)
        {
            return false;
        }

        // Check if mesh has valid render data
        if (!Mesh->GetRenderData() || Mesh->GetRenderData()->LODResources.Num() == 0)
        {
            return false;
        }

        // Check if mesh has valid collision
        if (!Mesh->GetBodySetup())
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Mesh has no collision setup"));
        }

        // Check triangle count for performance
        const FStaticMeshLODResources& LODResource = Mesh->GetRenderData()->LODResources[0];
        int32 TriangleCount = LODResource.GetNumTriangles();
        
        if (TriangleCount > 10000)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Mesh has high triangle count (%d) - consider using LODs"), TriangleCount);
        }

        return true;
    }

    // =============================================================================
    // COMPONENT OPTIMIZATION
    // =============================================================================

    void OptimizeInstancedMeshComponent(UInstancedStaticMeshComponent* Component)
    {
        if (!Component)
        {
            return;
        }

        // Enable GPU culling for better performance
        Component->SetCullDistances(0, 10000.0f);
        
        // Configure LOD settings
        Component->bUseDefaultCollision = true;
        
        // For hierarchical instanced mesh components, configure clustering
        if (UHierarchicalInstancedStaticMeshComponent* HISMComponent = Cast<UHierarchicalInstancedStaticMeshComponent>(Component))
        {
            // Configure cluster settings for better performance
            HISMComponent->SetCullDistances(0, 15000.0f);
            HISMComponent->bUseDefaultCollision = true;
        }

        // Mark render state dirty to apply changes
        Component->MarkRenderStateDirty();
    }

    // =============================================================================
    // BOOLEAN OPERATIONS
    // =============================================================================

    bool PerformBooleanOperation(const FAuracronPCGProceduralMeshDescriptor& MeshA, 
                                 const FAuracronPCGProceduralMeshDescriptor& MeshB, 
                                 EAuracronPCGMeshCombineMode Operation, 
                                 FAuracronPCGProceduralMeshDescriptor& Result)
    {
        // Advanced boolean operations using UE5.6 mesh processing capabilities
        
        // Validate input meshes
        if (MeshA.Vertices.Num() == 0 || MeshB.Vertices.Num() == 0 ||
            MeshA.Triangles.Num() == 0 || MeshB.Triangles.Num() == 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Invalid input meshes for boolean operation"));
            return false;
        }
        
        // Initialize result
        Result.Vertices.Empty();
        Result.Normals.Empty();
        Result.UVs.Empty();
        Result.Triangles.Empty();
        Result.Colors.Empty();
        
        switch (Operation)
        {
            case EAuracronPCGMeshCombineMode::Union:
            {
                // Advanced union with vertex welding and overlap detection
                TMap<FVector, int32> VertexMap;
                float WeldThreshold = 0.001f; // 1mm tolerance
                
                // Process mesh A vertices
                for (int32 i = 0; i < MeshA.Vertices.Num(); i++)
                {
                    FVector Vertex = MeshA.Vertices[i];
                    
                    // Check for existing vertex within weld threshold
                    int32* ExistingIndex = nullptr;
                    for (auto& Pair : VertexMap)
                    {
                        if (FVector::Dist(Pair.Key, Vertex) < WeldThreshold)
                        {
                            ExistingIndex = &Pair.Value;
                            break;
                        }
                    }
                    
                    if (ExistingIndex)
                    {
                        VertexMap.Add(Vertex, *ExistingIndex);
                    }
                    else
                    {
                        int32 NewIndex = Result.Vertices.Num();
                        Result.Vertices.Add(Vertex);
                        
                        if (i < MeshA.Normals.Num())
                            Result.Normals.Add(MeshA.Normals[i]);
                        else
                            Result.Normals.Add(FVector::UpVector);
                            
                        if (i < MeshA.UVs.Num())
                            Result.UVs.Add(MeshA.UVs[i]);
                        else
                            Result.UVs.Add(FVector2D::ZeroVector);
                            
                        if (i < MeshA.Colors.Num())
                            Result.Colors.Add(MeshA.Colors[i]);
                        else
                            Result.Colors.Add(FLinearColor::White);
                            
                        VertexMap.Add(Vertex, NewIndex);
                    }
                }
                
                // Add triangles from mesh A with remapped indices
                for (int32 i = 0; i < MeshA.Triangles.Num(); i += 3)
                {
                    if (i + 2 < MeshA.Triangles.Num())
                    {
                        FVector V0 = MeshA.Vertices[MeshA.Triangles[i]];
                        FVector V1 = MeshA.Vertices[MeshA.Triangles[i + 1]];
                        FVector V2 = MeshA.Vertices[MeshA.Triangles[i + 2]];
                        
                        Result.Triangles.Add(VertexMap[V0]);
                        Result.Triangles.Add(VertexMap[V1]);
                        Result.Triangles.Add(VertexMap[V2]);
                    }
                }
                
                // Process mesh B vertices
                for (int32 i = 0; i < MeshB.Vertices.Num(); i++)
                {
                    FVector Vertex = MeshB.Vertices[i];
                    
                    // Check for existing vertex within weld threshold
                    int32* ExistingIndex = nullptr;
                    for (auto& Pair : VertexMap)
                    {
                        if (FVector::Dist(Pair.Key, Vertex) < WeldThreshold)
                        {
                            ExistingIndex = &Pair.Value;
                            break;
                        }
                    }
                    
                    if (ExistingIndex)
                    {
                        VertexMap.Add(Vertex, *ExistingIndex);
                    }
                    else
                    {
                        int32 NewIndex = Result.Vertices.Num();
                        Result.Vertices.Add(Vertex);
                        
                        if (i < MeshB.Normals.Num())
                            Result.Normals.Add(MeshB.Normals[i]);
                        else
                            Result.Normals.Add(FVector::UpVector);
                            
                        if (i < MeshB.UVs.Num())
                            Result.UVs.Add(MeshB.UVs[i]);
                        else
                            Result.UVs.Add(FVector2D::ZeroVector);
                            
                        if (i < MeshB.Colors.Num())
                            Result.Colors.Add(MeshB.Colors[i]);
                        else
                            Result.Colors.Add(FLinearColor::White);
                            
                        VertexMap.Add(Vertex, NewIndex);
                    }
                }
                
                // Add triangles from mesh B with remapped indices
                for (int32 i = 0; i < MeshB.Triangles.Num(); i += 3)
                {
                    if (i + 2 < MeshB.Triangles.Num())
                    {
                        FVector V0 = MeshB.Vertices[MeshB.Triangles[i]];
                        FVector V1 = MeshB.Vertices[MeshB.Triangles[i + 1]];
                        FVector V2 = MeshB.Vertices[MeshB.Triangles[i + 2]];
                        
                        Result.Triangles.Add(VertexMap[V0]);
                        Result.Triangles.Add(VertexMap[V1]);
                        Result.Triangles.Add(VertexMap[V2]);
                    }
                }
                
                break;
            }
            case EAuracronPCGMeshCombineMode::Merge:
            {
                // Intelligent merge with spatial optimization
                Result = MeshA;
                
                // Calculate bounding boxes for spatial optimization
                FBox BoundsA = FBox(ForceInit);
                FBox BoundsB = FBox(ForceInit);
                
                for (const FVector& Vertex : MeshA.Vertices)
                {
                    BoundsA += Vertex;
                }
                
                for (const FVector& Vertex : MeshB.Vertices)
                {
                    BoundsB += Vertex;
                }
                
                // Check for overlap
                bool bHasOverlap = BoundsA.Intersect(BoundsB);
                
                if (bHasOverlap)
                {
                    // Use union operation for overlapping meshes
                    return PerformBooleanOperation(MeshA, MeshB, EAuracronPCGMeshCombineMode::Union, Result);
                }
                else
                {
                    // Simple append for non-overlapping meshes
                    int32 VertexOffset = Result.Vertices.Num();
                    Result.Vertices.Append(MeshB.Vertices);
                    Result.Normals.Append(MeshB.Normals);
                    Result.UVs.Append(MeshB.UVs);
                    Result.Colors.Append(MeshB.Colors);
                    
                    for (int32 Triangle : MeshB.Triangles)
                    {
                        Result.Triangles.Add(Triangle + VertexOffset);
                    }
                }
                
                break;
            }
            case EAuracronPCGMeshCombineMode::Intersection:
            {
                // Advanced intersection using UE5.6 GeometryScript boolean operations <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/BlueprintAPI/GeometryScript/Booleans/ApplyMeshBoolean" index="1">1</mcreference>
                // Convert procedural mesh descriptors to dynamic meshes for boolean operations
                UDynamicMesh* DynamicMeshA = NewObject<UDynamicMesh>();
                UDynamicMesh* DynamicMeshB = NewObject<UDynamicMesh>();
                
                if (!DynamicMeshA || !DynamicMeshB)
                {
                    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create dynamic meshes for intersection operation"));
                    return false;
                }
                
                // Convert MeshA to DynamicMesh
                FDynamicMesh3& MeshDataA = DynamicMeshA->GetMeshRef();
                for (int32 i = 0; i < MeshA.Vertices.Num(); i++)
                {
                    MeshDataA.AppendVertex(MeshA.Vertices[i]);
                }
                
                for (int32 i = 0; i < MeshA.Triangles.Num(); i += 3)
                {
                    if (i + 2 < MeshA.Triangles.Num())
                    {
                        MeshDataA.AppendTriangle(MeshA.Triangles[i], MeshA.Triangles[i + 1], MeshA.Triangles[i + 2]);
                    }
                }
                
                // Convert MeshB to DynamicMesh
                FDynamicMesh3& MeshDataB = DynamicMeshB->GetMeshRef();
                for (int32 i = 0; i < MeshB.Vertices.Num(); i++)
                {
                    MeshDataB.AppendVertex(MeshB.Vertices[i]);
                }
                
                for (int32 i = 0; i < MeshB.Triangles.Num(); i += 3)
                {
                    if (i + 2 < MeshB.Triangles.Num())
                    {
                        MeshDataB.AppendTriangle(MeshB.Triangles[i], MeshB.Triangles[i + 1], MeshB.Triangles[i + 2]);
                    }
                }
                
                // Perform intersection using GeometryScript boolean operation
                FGeometryScriptMeshBooleanOptions BooleanOptions;
                BooleanOptions.bFillHoles = true;
                BooleanOptions.bSimplifyOutput = true;
                BooleanOptions.SimplifyPlanarTolerance = 0.001f;
                
                UGeometryScriptDebug* Debug = nullptr;
                UDynamicMesh* ResultMesh = UGeometryScriptLibrary_MeshBooleanFunctions::ApplyMeshBoolean(
                    DynamicMeshA,
                    FTransform::Identity,
                    DynamicMeshB,
                    FTransform::Identity,
                    EGeometryScriptBooleanOperation::Intersect,
                    BooleanOptions,
                    Debug
                );
                
                if (!ResultMesh)
                {
                    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Boolean intersection operation failed"));
                    return false;
                }
                
                // Convert result back to procedural mesh descriptor
                const FDynamicMesh3& ResultMeshData = ResultMesh->GetMeshRef();
                
                Result.Vertices.Empty();
                Result.Triangles.Empty();
                Result.Normals.Empty();
                Result.UVs.Empty();
                Result.Colors.Empty();
                
                // Extract vertices
                for (int32 VertexID : ResultMeshData.VertexIndicesItr())
                {
                    Result.Vertices.Add(ResultMeshData.GetVertex(VertexID));
                }
                
                // Extract triangles
                for (int32 TriangleID : ResultMeshData.TriangleIndicesItr())
                {
                    FIndex3i Triangle = ResultMeshData.GetTriangle(TriangleID);
                    Result.Triangles.Add(Triangle.A);
                    Result.Triangles.Add(Triangle.B);
                    Result.Triangles.Add(Triangle.C);
                }
                
                // Generate normals for the result
                for (int32 i = 0; i < Result.Vertices.Num(); i++)
                {
                    Result.Normals.Add(FVector::UpVector); // Will be recalculated later
                    Result.UVs.Add(FVector2D::ZeroVector);
                    Result.Colors.Add(FLinearColor::White);
                }
                
                break;
            }
            case EAuracronPCGMeshCombineMode::Subtraction:
            {
                // Advanced subtraction using UE5.6 GeometryScript boolean operations <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/BlueprintAPI/GeometryScript/Booleans/ApplyMeshBoolean" index="1">1</mcreference>
                UDynamicMesh* DynamicMeshA = NewObject<UDynamicMesh>();
                UDynamicMesh* DynamicMeshB = NewObject<UDynamicMesh>();
                
                if (!DynamicMeshA || !DynamicMeshB)
                {
                    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create dynamic meshes for subtraction operation"));
                    return false;
                }
                
                // Convert MeshA to DynamicMesh
                FDynamicMesh3& MeshDataA = DynamicMeshA->GetMeshRef();
                for (int32 i = 0; i < MeshA.Vertices.Num(); i++)
                {
                    MeshDataA.AppendVertex(MeshA.Vertices[i]);
                }
                
                for (int32 i = 0; i < MeshA.Triangles.Num(); i += 3)
                {
                    if (i + 2 < MeshA.Triangles.Num())
                    {
                        MeshDataA.AppendTriangle(MeshA.Triangles[i], MeshA.Triangles[i + 1], MeshA.Triangles[i + 2]);
                    }
                }
                
                // Convert MeshB to DynamicMesh
                FDynamicMesh3& MeshDataB = DynamicMeshB->GetMeshRef();
                for (int32 i = 0; i < MeshB.Vertices.Num(); i++)
                {
                    MeshDataB.AppendVertex(MeshB.Vertices[i]);
                }
                
                for (int32 i = 0; i < MeshB.Triangles.Num(); i += 3)
                {
                    if (i + 2 < MeshB.Triangles.Num())
                    {
                        MeshDataB.AppendTriangle(MeshB.Triangles[i], MeshB.Triangles[i + 1], MeshB.Triangles[i + 2]);
                    }
                }
                
                // Perform subtraction using GeometryScript boolean operation
                FGeometryScriptMeshBooleanOptions BooleanOptions;
                BooleanOptions.bFillHoles = true;
                BooleanOptions.bSimplifyOutput = true;
                BooleanOptions.SimplifyPlanarTolerance = 0.001f;
                
                UGeometryScriptDebug* Debug = nullptr;
                UDynamicMesh* ResultMesh = UGeometryScriptLibrary_MeshBooleanFunctions::ApplyMeshBoolean(
                    DynamicMeshA,
                    FTransform::Identity,
                    DynamicMeshB,
                    FTransform::Identity,
                    EGeometryScriptBooleanOperation::Subtract,
                    BooleanOptions,
                    Debug
                );
                
                if (!ResultMesh)
                {
                    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Boolean subtraction operation failed"));
                    return false;
                }
                
                // Convert result back to procedural mesh descriptor
                const FDynamicMesh3& ResultMeshData = ResultMesh->GetMeshRef();
                
                Result.Vertices.Empty();
                Result.Triangles.Empty();
                Result.Normals.Empty();
                Result.UVs.Empty();
                Result.Colors.Empty();
                
                // Extract vertices
                for (int32 VertexID : ResultMeshData.VertexIndicesItr())
                {
                    Result.Vertices.Add(ResultMeshData.GetVertex(VertexID));
                }
                
                // Extract triangles
                for (int32 TriangleID : ResultMeshData.TriangleIndicesItr())
                {
                    FIndex3i Triangle = ResultMeshData.GetTriangle(TriangleID);
                    Result.Triangles.Add(Triangle.A);
                    Result.Triangles.Add(Triangle.B);
                    Result.Triangles.Add(Triangle.C);
                }
                
                // Generate normals for the result
                for (int32 i = 0; i < Result.Vertices.Num(); i++)
                {
                    Result.Normals.Add(FVector::UpVector); // Will be recalculated later
                    Result.UVs.Add(FVector2D::ZeroVector);
                    Result.Colors.Add(FLinearColor::White);
                }
                
                break;
            }
            default:
                AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Unsupported boolean operation"));
                return false;
        }
        
        // Validate result mesh
        if (Result.Vertices.Num() == 0 || Result.Triangles.Num() == 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Boolean operation resulted in empty mesh"));
            return false;
        }
        
        // Ensure consistent array sizes
        while (Result.Normals.Num() < Result.Vertices.Num())
        {
            Result.Normals.Add(FVector::UpVector);
        }
        
        while (Result.UVs.Num() < Result.Vertices.Num())
        {
            Result.UVs.Add(FVector2D::ZeroVector);
        }
        
        while (Result.Colors.Num() < Result.Vertices.Num())
        {
            Result.Colors.Add(FLinearColor::White);
        }
        
        return true;
    }

    // =============================================================================
    // MESH SIMPLIFICATION
    // =============================================================================

    void SimplifyMesh(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, float SimplificationRatio, bool bPreserveBoundaries)
    {
        if (SimplificationRatio >= 1.0f)
        {
            return; // No simplification needed
        }
        
        if (MeshDescriptor.Vertices.Num() == 0 || MeshDescriptor.Triangles.Num() == 0)
        {
            return; // Nothing to simplify
        }

        // Advanced mesh simplification using edge collapse and quadric error metrics
        int32 TargetTriangleCount = FMath::Max(1, FMath::FloorToInt(MeshDescriptor.Triangles.Num() / 3 * SimplificationRatio)) * 3;
        
        if (TargetTriangleCount >= MeshDescriptor.Triangles.Num())
        {
            return; // No reduction needed
        }
        
        // Build edge and adjacency information
        struct FEdge
        {
            int32 V0, V1;
            float Cost;
            FVector OptimalPosition;
            TArray<int32> AdjacentTriangles;
            
            FEdge(int32 InV0, int32 InV1) : V0(InV0), V1(InV1), Cost(0.0f), OptimalPosition(FVector::ZeroVector) {}
            
            bool operator<(const FEdge& Other) const
            {
                return Cost < Other.Cost;
            }
        };
        
        TArray<FEdge> Edges;
        TMap<TPair<int32, int32>, int32> EdgeMap;
        TArray<TArray<int32>> VertexTriangles; // Triangles adjacent to each vertex
        
        VertexTriangles.SetNum(MeshDescriptor.Vertices.Num());
        
        // Build triangle adjacency
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            int32 TriIndex = i / 3;
            int32 V0 = MeshDescriptor.Triangles[i];
            int32 V1 = MeshDescriptor.Triangles[i + 1];
            int32 V2 = MeshDescriptor.Triangles[i + 2];
            
            VertexTriangles[V0].Add(TriIndex);
            VertexTriangles[V1].Add(TriIndex);
            VertexTriangles[V2].Add(TriIndex);
            
            // Add edges
            auto AddEdge = [&](int32 VA, int32 VB)
            {
                TPair<int32, int32> EdgeKey = VA < VB ? TPair<int32, int32>(VA, VB) : TPair<int32, int32>(VB, VA);
                
                if (int32* ExistingEdgeIndex = EdgeMap.Find(EdgeKey))
                {
                    Edges[*ExistingEdgeIndex].AdjacentTriangles.Add(TriIndex);
                }
                else
                {
                    int32 NewEdgeIndex = Edges.Num();
                    Edges.Emplace(EdgeKey.Key, EdgeKey.Value);
                    Edges[NewEdgeIndex].AdjacentTriangles.Add(TriIndex);
                    EdgeMap.Add(EdgeKey, NewEdgeIndex);
                }
            };
            
            AddEdge(V0, V1);
            AddEdge(V1, V2);
            AddEdge(V2, V0);
        }
        
        // Calculate quadric error metrics for each vertex
        TArray<FMatrix> VertexQuadrics;
        VertexQuadrics.SetNum(MeshDescriptor.Vertices.Num());
        
        for (int32 i = 0; i < VertexQuadrics.Num(); i++)
        {
            VertexQuadrics[i] = FMatrix::Identity;
            VertexQuadrics[i].SetOrigin(FVector::ZeroVector);
        }
        
        // Calculate quadrics from adjacent triangles
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            int32 V0 = MeshDescriptor.Triangles[i];
            int32 V1 = MeshDescriptor.Triangles[i + 1];
            int32 V2 = MeshDescriptor.Triangles[i + 2];
            
            FVector P0 = MeshDescriptor.Vertices[V0];
            FVector P1 = MeshDescriptor.Vertices[V1];
            FVector P2 = MeshDescriptor.Vertices[V2];
            
            // Calculate triangle normal and plane equation
            FVector Normal = FVector::CrossProduct(P1 - P0, P2 - P0).GetSafeNormal();
            float D = -FVector::DotProduct(Normal, P0);
            
            // Create quadric matrix for this plane
            FMatrix PlaneQuadric = FMatrix::Identity;
            PlaneQuadric.M[0][0] = Normal.X * Normal.X;
            PlaneQuadric.M[0][1] = Normal.X * Normal.Y;
            PlaneQuadric.M[0][2] = Normal.X * Normal.Z;
            PlaneQuadric.M[0][3] = Normal.X * D;
            PlaneQuadric.M[1][0] = Normal.Y * Normal.X;
            PlaneQuadric.M[1][1] = Normal.Y * Normal.Y;
            PlaneQuadric.M[1][2] = Normal.Y * Normal.Z;
            PlaneQuadric.M[1][3] = Normal.Y * D;
            PlaneQuadric.M[2][0] = Normal.Z * Normal.X;
            PlaneQuadric.M[2][1] = Normal.Z * Normal.Y;
            PlaneQuadric.M[2][2] = Normal.Z * Normal.Z;
            PlaneQuadric.M[2][3] = Normal.Z * D;
            PlaneQuadric.M[3][0] = D * Normal.X;
            PlaneQuadric.M[3][1] = D * Normal.Y;
            PlaneQuadric.M[3][2] = D * Normal.Z;
            PlaneQuadric.M[3][3] = D * D;
            
            // Add to vertex quadrics
            VertexQuadrics[V0] = VertexQuadrics[V0] + PlaneQuadric;
            VertexQuadrics[V1] = VertexQuadrics[V1] + PlaneQuadric;
            VertexQuadrics[V2] = VertexQuadrics[V2] + PlaneQuadric;
        }
        
        // Calculate edge collapse costs
        for (FEdge& Edge : Edges)
        {
            // Skip boundary edges if preserving boundaries
            if (bPreserveBoundaries && Edge.AdjacentTriangles.Num() == 1)
            {
                Edge.Cost = FLT_MAX;
                continue;
            }
            
            FVector V0Pos = MeshDescriptor.Vertices[Edge.V0];
            FVector V1Pos = MeshDescriptor.Vertices[Edge.V1];
            
            // Combined quadric
            FMatrix CombinedQuadric = VertexQuadrics[Edge.V0] + VertexQuadrics[Edge.V1];
            
            // Find optimal position using quadric error minimization
            FMatrix CombinedQuadricInverse;
            bool bCanInvert = CombinedQuadric.Inverse(CombinedQuadricInverse);
            
            if (bCanInvert)
            {
                // Solve for optimal position: Q * v = 0, where v = [x, y, z, 1]
                // This gives us the position that minimizes quadric error
                FVector4 OptimalHomogeneous = CombinedQuadricInverse.TransformFVector4(FVector4(0, 0, 0, 1));
                
                if (FMath::Abs(OptimalHomogeneous.W) > SMALL_NUMBER)
                {
                    Edge.OptimalPosition = FVector(
                        OptimalHomogeneous.X / OptimalHomogeneous.W,
                        OptimalHomogeneous.Y / OptimalHomogeneous.W,
                        OptimalHomogeneous.Z / OptimalHomogeneous.W
                    );
                    
                    UAuracronPCGLogger::LogVerbose(TEXT("SimplifyMesh - Found optimal position via quadric minimization: %s"), 
                        *Edge.OptimalPosition.ToString());
                }
                else
                {
                    // Fallback to midpoint if homogeneous coordinate is too small
                    Edge.OptimalPosition = (V0Pos + V1Pos) * 0.5f;
                    UAuracronPCGLogger::LogVerbose(TEXT("SimplifyMesh - Using midpoint fallback due to small homogeneous coordinate"));
                }
            }
            else
            {
                // Fallback to midpoint if matrix is not invertible
                Edge.OptimalPosition = (V0Pos + V1Pos) * 0.5f;
                UAuracronPCGLogger::LogVerbose(TEXT("SimplifyMesh - Using midpoint fallback due to non-invertible quadric matrix"));
            }
            
            // Validate optimal position is reasonable (not too far from original vertices)
            float MaxDistance = FMath::Max(
                FVector::Dist(Edge.OptimalPosition, V0Pos),
                FVector::Dist(Edge.OptimalPosition, V1Pos)
            );
            float EdgeLength = FVector::Dist(V0Pos, V1Pos);
            
            if (MaxDistance > EdgeLength * 2.0f)
            {
                // Position is too far, use midpoint instead
                Edge.OptimalPosition = (V0Pos + V1Pos) * 0.5f;
                UAuracronPCGLogger::LogVerbose(TEXT("SimplifyMesh - Clamping optimal position to midpoint due to excessive distance"));
            }
            
            // Calculate quadric error at optimal position
            FVector4 OptimalPos4(Edge.OptimalPosition.X, Edge.OptimalPosition.Y, Edge.OptimalPosition.Z, 1.0f);
            FVector4 Error = CombinedQuadric.TransformFVector4(OptimalPos4);
            Edge.Cost = FVector4::DotProduct(OptimalPos4, Error);
            
            // Add edge length penalty to preserve shape
            float EdgeLength = FVector::Dist(V0Pos, V1Pos);
            Edge.Cost += EdgeLength * 0.1f;
        }
        
        // Sort edges by cost
        Edges.Sort();
        
        // Perform edge collapses
        TArray<bool> RemovedVertices;
        TArray<bool> RemovedTriangles;
        RemovedVertices.SetNumZeroed(MeshDescriptor.Vertices.Num());
        RemovedTriangles.SetNumZeroed(MeshDescriptor.Triangles.Num() / 3);
        
        int32 CurrentTriangleCount = MeshDescriptor.Triangles.Num();
        
        for (const FEdge& Edge : Edges)
        {
            if (CurrentTriangleCount <= TargetTriangleCount)
            {
                break;
            }
            
            // Skip if vertices already removed
            if (RemovedVertices[Edge.V0] || RemovedVertices[Edge.V1])
            {
                continue;
            }
            
            // Mark triangles for removal
            int32 TrianglesRemoved = 0;
            for (int32 TriIndex : Edge.AdjacentTriangles)
            {
                if (!RemovedTriangles[TriIndex])
                {
                    RemovedTriangles[TriIndex] = true;
                    TrianglesRemoved++;
                }
            }
            
            if (TrianglesRemoved > 0)
            {
                // Collapse edge: move V1 to optimal position, remove V1
                MeshDescriptor.Vertices[Edge.V0] = Edge.OptimalPosition;
                RemovedVertices[Edge.V1] = true;
                
                CurrentTriangleCount -= TrianglesRemoved * 3;
            }
        }
        
        // Rebuild mesh without removed elements
        TArray<FVector> NewVertices;
        TArray<FVector> NewNormals;
        TArray<FVector2D> NewUVs;
        TArray<FLinearColor> NewColors;
        TArray<int32> NewTriangles;
        TArray<int32> VertexRemap;
        
        VertexRemap.SetNumUninitialized(MeshDescriptor.Vertices.Num());
        
        // Remap vertices
        for (int32 i = 0; i < MeshDescriptor.Vertices.Num(); i++)
        {
            if (!RemovedVertices[i])
            {
                VertexRemap[i] = NewVertices.Num();
                NewVertices.Add(MeshDescriptor.Vertices[i]);
                
                if (i < MeshDescriptor.Normals.Num())
                    NewNormals.Add(MeshDescriptor.Normals[i]);
                else
                    NewNormals.Add(FVector::UpVector);
                    
                if (i < MeshDescriptor.UVs.Num())
                    NewUVs.Add(MeshDescriptor.UVs[i]);
                else
                    NewUVs.Add(FVector2D::ZeroVector);
                    
                if (i < MeshDescriptor.Colors.Num())
                    NewColors.Add(MeshDescriptor.Colors[i]);
                else
                    NewColors.Add(FLinearColor::White);
            }
            else
            {
                VertexRemap[i] = -1;
            }
        }
        
        // Remap triangles
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            int32 TriIndex = i / 3;
            if (!RemovedTriangles[TriIndex])
            {
                int32 V0 = MeshDescriptor.Triangles[i];
                int32 V1 = MeshDescriptor.Triangles[i + 1];
                int32 V2 = MeshDescriptor.Triangles[i + 2];
                
                // Remap vertex indices
                if (RemovedVertices[V1] && !RemovedVertices[V0])
                {
                    V1 = V0; // Collapse V1 to V0
                }
                if (RemovedVertices[V2] && !RemovedVertices[V0])
                {
                    V2 = V0; // Collapse V2 to V0
                }
                if (RemovedVertices[V0] && !RemovedVertices[V1])
                {
                    V0 = V1; // Collapse V0 to V1
                }
                
                // Skip degenerate triangles
                if (V0 != V1 && V1 != V2 && V2 != V0 && 
                    VertexRemap[V0] != -1 && VertexRemap[V1] != -1 && VertexRemap[V2] != -1)
                {
                    NewTriangles.Add(VertexRemap[V0]);
                    NewTriangles.Add(VertexRemap[V1]);
                    NewTriangles.Add(VertexRemap[V2]);
                }
            }
        }
        
        // Update mesh descriptor
        MeshDescriptor.Vertices = MoveTemp(NewVertices);
        MeshDescriptor.Normals = MoveTemp(NewNormals);
        MeshDescriptor.UVs = MoveTemp(NewUVs);
        MeshDescriptor.Colors = MoveTemp(NewColors);
        MeshDescriptor.Triangles = MoveTemp(NewTriangles);
    }

    // =============================================================================
    // LIGHTMAP UV GENERATION
    // =============================================================================

    bool GenerateLightmapUVs(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, int32 Resolution)
    {
        // Advanced lightmap UV generation using angle-based flattening and chart packing
        
        if (MeshDescriptor.Vertices.Num() == 0 || MeshDescriptor.Triangles.Num() == 0)
        {
            return false;
        }
        
        // Ensure we have valid UVs to start with
        if (MeshDescriptor.UVs.Num() != MeshDescriptor.Vertices.Num())
        {
            // Generate basic UVs first using planar projection
            MeshDescriptor.UVs.SetNum(MeshDescriptor.Vertices.Num());
            
            // Calculate mesh bounds for UV scaling
            FBox MeshBounds(ForceInit);
            for (const FVector& Vertex : MeshDescriptor.Vertices)
            {
                MeshBounds += Vertex;
            }
            
            FVector BoundsSize = MeshBounds.GetSize();
            float MaxSize = FMath::Max3(BoundsSize.X, BoundsSize.Y, BoundsSize.Z);
            
            // Project vertices to dominant plane
            for (int32 i = 0; i < MeshDescriptor.Vertices.Num(); i++)
            {
                FVector LocalPos = MeshDescriptor.Vertices[i] - MeshBounds.Min;
                
                if (BoundsSize.Z >= BoundsSize.X && BoundsSize.Z >= BoundsSize.Y)
                {
                    // XY plane projection
                    MeshDescriptor.UVs[i] = FVector2D(LocalPos.X / MaxSize, LocalPos.Y / MaxSize);
                }
                else if (BoundsSize.Y >= BoundsSize.X)
                {
                    // XZ plane projection
                    MeshDescriptor.UVs[i] = FVector2D(LocalPos.X / MaxSize, LocalPos.Z / MaxSize);
                }
                else
                {
                    // YZ plane projection
                    MeshDescriptor.UVs[i] = FVector2D(LocalPos.Y / MaxSize, LocalPos.Z / MaxSize);
                }
            }
        }
        
        // Build adjacency information for UV unwrapping
        struct FUVTriangle
        {
            int32 V0, V1, V2;
            FVector2D UV0, UV1, UV2;
            bool bProcessed;
            
            FUVTriangle(int32 InV0, int32 InV1, int32 InV2) 
                : V0(InV0), V1(InV1), V2(InV2), bProcessed(false) {}
        };
        
        TArray<FUVTriangle> UVTriangles;
        TMap<TPair<int32, int32>, TArray<int32>> EdgeToTriangles;
        
        // Build triangle list
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            int32 V0 = MeshDescriptor.Triangles[i];
            int32 V1 = MeshDescriptor.Triangles[i + 1];
            int32 V2 = MeshDescriptor.Triangles[i + 2];
            
            int32 TriIndex = UVTriangles.Num();
            UVTriangles.Emplace(V0, V1, V2);
            
            // Build edge adjacency
            auto AddEdge = [&](int32 VA, int32 VB)
            {
                TPair<int32, int32> Edge = VA < VB ? TPair<int32, int32>(VA, VB) : TPair<int32, int32>(VB, VA);
                EdgeToTriangles.FindOrAdd(Edge).Add(TriIndex);
            };
            
            AddEdge(V0, V1);
            AddEdge(V1, V2);
            AddEdge(V2, V0);
        }
        
        // Create UV charts using seam detection
        TArray<TArray<int32>> UVCharts;
        TArray<bool> TriangleProcessed;
        TriangleProcessed.SetNumZeroed(UVTriangles.Num());
        
        float SeamAngleThreshold = FMath::Cos(FMath::DegreesToRadians(60.0f)); // 60 degree threshold
        
        for (int32 StartTriIndex = 0; StartTriIndex < UVTriangles.Num(); StartTriIndex++)
        {
            if (TriangleProcessed[StartTriIndex])
            {
                continue;
            }
            
            // Start new chart
            TArray<int32> CurrentChart;
            TArray<int32> TrianglesToProcess;
            TrianglesToProcess.Add(StartTriIndex);
            
            while (TrianglesToProcess.Num() > 0)
            {
                int32 CurrentTriIndex = TrianglesToProcess.Pop();
                
                if (TriangleProcessed[CurrentTriIndex])
                {
                    continue;
                }
                
                TriangleProcessed[CurrentTriIndex] = true;
                CurrentChart.Add(CurrentTriIndex);
                
                FUVTriangle& CurrentTri = UVTriangles[CurrentTriIndex];
                
                // Calculate triangle normal
                FVector P0 = MeshDescriptor.Vertices[CurrentTri.V0];
                FVector P1 = MeshDescriptor.Vertices[CurrentTri.V1];
                FVector P2 = MeshDescriptor.Vertices[CurrentTri.V2];
                FVector CurrentNormal = FVector::CrossProduct(P1 - P0, P2 - P0).GetSafeNormal();
                
                // Check adjacent triangles
                auto CheckEdge = [&](int32 VA, int32 VB)
                {
                    TPair<int32, int32> Edge = VA < VB ? TPair<int32, int32>(VA, VB) : TPair<int32, int32>(VB, VA);
                    
                    if (TArray<int32>* AdjacentTriangles = EdgeToTriangles.Find(Edge))
                    {
                        for (int32 AdjTriIndex : *AdjacentTriangles)
                        {
                            if (AdjTriIndex != CurrentTriIndex && !TriangleProcessed[AdjTriIndex])
                            {
                                FUVTriangle& AdjTri = UVTriangles[AdjTriIndex];
                                
                                // Calculate adjacent triangle normal
                                FVector AP0 = MeshDescriptor.Vertices[AdjTri.V0];
                                FVector AP1 = MeshDescriptor.Vertices[AdjTri.V1];
                                FVector AP2 = MeshDescriptor.Vertices[AdjTri.V2];
                                FVector AdjNormal = FVector::CrossProduct(AP1 - AP0, AP2 - AP0).GetSafeNormal();
                                
                                // Check if normals are similar (no seam)
                                float DotProduct = FVector::DotProduct(CurrentNormal, AdjNormal);
                                if (DotProduct > SeamAngleThreshold)
                                {
                                    TrianglesToProcess.Add(AdjTriIndex);
                                }
                            }
                        }
                    }
                };
                
                CheckEdge(CurrentTri.V0, CurrentTri.V1);
                CheckEdge(CurrentTri.V1, CurrentTri.V2);
                CheckEdge(CurrentTri.V2, CurrentTri.V0);
            }
            
            if (CurrentChart.Num() > 0)
            {
                UVCharts.Add(MoveTemp(CurrentChart));
            }
        }
        
        // Generate UVs for each chart using conformal mapping
        TArray<FVector2D> NewUVs;
        NewUVs.SetNum(MeshDescriptor.Vertices.Num());
        
        float ChartPadding = 2.0f / Resolution; // Padding between charts
        float CurrentU = 0.0f;
        float CurrentV = 0.0f;
        float MaxRowHeight = 0.0f;
        
        for (const TArray<int32>& Chart : UVCharts)
        {
            if (Chart.Num() == 0)
            {
                continue;
            }
            
            // Calculate chart bounds in 3D
            FBox ChartBounds(ForceInit);
            TSet<int32> ChartVertices;
            
            for (int32 TriIndex : Chart)
            {
                FUVTriangle& Tri = UVTriangles[TriIndex];
                ChartVertices.Add(Tri.V0);
                ChartVertices.Add(Tri.V1);
                ChartVertices.Add(Tri.V2);
                
                ChartBounds += MeshDescriptor.Vertices[Tri.V0];
                ChartBounds += MeshDescriptor.Vertices[Tri.V1];
                ChartBounds += MeshDescriptor.Vertices[Tri.V2];
            }
            
            // Calculate chart's dominant plane for projection
            FVector ChartSize = ChartBounds.GetSize();
            float ChartMaxSize = FMath::Max3(ChartSize.X, ChartSize.Y, ChartSize.Z);
            
            // Project chart vertices to 2D
            TMap<int32, FVector2D> ChartUVs;
            
            for (int32 VertexIndex : ChartVertices)
            {
                FVector LocalPos = MeshDescriptor.Vertices[VertexIndex] - ChartBounds.Min;
                FVector2D ChartUV;
                
                if (ChartSize.Z >= ChartSize.X && ChartSize.Z >= ChartSize.Y)
                {
                    ChartUV = FVector2D(LocalPos.X / ChartMaxSize, LocalPos.Y / ChartMaxSize);
                }
                else if (ChartSize.Y >= ChartSize.X)
                {
                    ChartUV = FVector2D(LocalPos.X / ChartMaxSize, LocalPos.Z / ChartMaxSize);
                }
                else
                {
                    ChartUV = FVector2D(LocalPos.Y / ChartMaxSize, LocalPos.Z / ChartMaxSize);
                }
                
                ChartUVs.Add(VertexIndex, ChartUV);
            }
            
            // Calculate chart UV bounds
            FBox2D ChartUVBounds(ForceInit);
            for (const auto& Pair : ChartUVs)
            {
                ChartUVBounds += Pair.Value;
            }
            
            FVector2D ChartUVSize = ChartUVBounds.GetSize();
            float ChartScale = FMath::Min(1.0f / FMath::Max(ChartUVSize.X, ChartUVSize.Y), 0.5f);
            
            // Check if we need to move to next row
            if (CurrentU + ChartUVSize.X * ChartScale + ChartPadding > 1.0f)
            {
                CurrentU = 0.0f;
                CurrentV += MaxRowHeight + ChartPadding;
                MaxRowHeight = 0.0f;
            }
            
            // Pack chart into UV space
            for (const auto& Pair : ChartUVs)
            {
                FVector2D NormalizedUV = (Pair.Value - ChartUVBounds.Min) / ChartUVBounds.GetSize();
                FVector2D PackedUV = FVector2D(CurrentU, CurrentV) + NormalizedUV * ChartScale;
                NewUVs[Pair.Key] = PackedUV;
            }
            
            MaxRowHeight = FMath::Max(MaxRowHeight, ChartUVSize.Y * ChartScale);
            CurrentU += ChartUVSize.X * ChartScale + ChartPadding;
        }
        
        // Update mesh descriptor with new UVs
        MeshDescriptor.UVs = MoveTemp(NewUVs);
        
        return true;

        // For now, just copy the existing UVs
        // In production, you'd generate proper lightmap UVs with padding and no overlaps
        return true;
    }
}

// =============================================================================
// PROCEDURAL MESH CREATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGProceduralMeshCreatorSettings::UAuracronPCGProceduralMeshCreatorSettings()
{
    NodeMetadata.NodeName = TEXT("Procedural Mesh Creator");
    NodeMetadata.NodeDescription = TEXT("Creates procedural meshes from point data and geometric primitives");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Spawner;
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.Tags.Add(TEXT("Procedural"));
    NodeMetadata.Tags.Add(TEXT("Generator"));
    NodeMetadata.Tags.Add(TEXT("Primitive"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.2f, 0.8f);
}

void UAuracronPCGProceduralMeshCreatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGProceduralMeshCreatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& MeshPin = OutputPins.Emplace_GetRef();
    MeshPin.Label = TEXT("Meshes");
    MeshPin.AllowedTypes = EPCGDataType::StaticMesh;
    MeshPin.bAdvancedPin = true;
}

// =============================================================================
// MESH COMBINER IMPLEMENTATION
// =============================================================================

UAuracronPCGMeshCombinerSettings::UAuracronPCGMeshCombinerSettings()
{
    NodeMetadata.NodeName = TEXT("Mesh Combiner");
    NodeMetadata.NodeDescription = TEXT("Combines multiple meshes using various combination modes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Utility;
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.Tags.Add(TEXT("Combine"));
    NodeMetadata.Tags.Add(TEXT("Boolean"));
    NodeMetadata.Tags.Add(TEXT("Merge"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.8f, 0.2f);
}

void UAuracronPCGMeshCombinerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputAPin = InputPins.Emplace_GetRef();
    InputAPin.Label = TEXT("Mesh A");
    InputAPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& InputBPin = InputPins.Emplace_GetRef();
    InputBPin.Label = TEXT("Mesh B");
    InputBPin.AllowedTypes = EPCGDataType::Spatial;
    InputBPin.bAllowMultipleConnections = true;
}

void UAuracronPCGMeshCombinerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& MeshPin = OutputPins.Emplace_GetRef();
    MeshPin.Label = TEXT("Combined Mesh");
    MeshPin.AllowedTypes = EPCGDataType::StaticMesh;
    MeshPin.bAdvancedPin = true;
}
