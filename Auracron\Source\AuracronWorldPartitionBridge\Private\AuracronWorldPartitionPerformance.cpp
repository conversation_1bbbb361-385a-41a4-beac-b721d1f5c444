// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Performance Monitoring Implementation
// Bridge 3.11: World Partition - Performance Monitoring

#include "AuracronWorldPartitionPerformance.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge.h"

// Performance monitoring includes
#include "Stats/Stats.h"
#include "Stats/StatsData.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformProcess.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"

// =============================================================================
// WORLD PARTITION PERFORMANCE MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionPerformanceManager* UAuracronWorldPartitionPerformanceManager::Instance = nullptr;

UAuracronWorldPartitionPerformanceManager* UAuracronWorldPartitionPerformanceManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionPerformanceManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionPerformanceManager::Initialize(const FAuracronPerformanceConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Performance Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize monitoring state
    MonitoringState = EAuracronPerformanceMonitoringState::Disabled;
    MonitoringStartTime = FDateTime::Now();
    LastUpdateTime = 0.0f;
    LastSampleTime = 0.0f;
    
    // Clear collections
    MetricHistory.Empty();
    CurrentMetrics.Empty();
    Thresholds.Empty();
    
    // Initialize default thresholds
    SetThreshold(EAuracronPerformanceMetricType::Memory, EAuracronPerformanceSeverity::Warning, Configuration.MemoryWarningThresholdMB);
    SetThreshold(EAuracronPerformanceMetricType::Memory, EAuracronPerformanceSeverity::Critical, Configuration.MemoryCriticalThresholdMB);
    SetThreshold(EAuracronPerformanceMetricType::CPU, EAuracronPerformanceSeverity::Warning, Configuration.CPUWarningThresholdPercent);
    SetThreshold(EAuracronPerformanceMetricType::CPU, EAuracronPerformanceSeverity::Critical, Configuration.CPUCriticalThresholdPercent);
    SetThreshold(EAuracronPerformanceMetricType::GPU, EAuracronPerformanceSeverity::Warning, Configuration.GPUWarningThresholdPercent);
    SetThreshold(EAuracronPerformanceMetricType::GPU, EAuracronPerformanceSeverity::Critical, Configuration.GPUCriticalThresholdPercent);
    SetThreshold(EAuracronPerformanceMetricType::Streaming, EAuracronPerformanceSeverity::Warning, Configuration.StreamingWarningThresholdMBps);
    SetThreshold(EAuracronPerformanceMetricType::Streaming, EAuracronPerformanceSeverity::Critical, Configuration.StreamingCriticalThresholdMBps);
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Performance Manager initialized with monitoring interval: %.2fs"), Configuration.MonitoringUpdateInterval);
}

void UAuracronWorldPartitionPerformanceManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Stop monitoring
    StopMonitoring();
    
    // Clear all data
    MetricHistory.Empty();
    CurrentMetrics.Empty();
    Thresholds.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Performance Manager shutdown completed"));
}

bool UAuracronWorldPartitionPerformanceManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionPerformanceManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || MonitoringState != EAuracronPerformanceMonitoringState::Enabled)
    {
        return;
    }

    LastUpdateTime += DeltaTime;
    LastSampleTime += DeltaTime;
    
    // Update metrics at specified interval
    if (LastUpdateTime >= Configuration.MonitoringUpdateInterval)
    {
        UpdateMetrics(DeltaTime);
        LastUpdateTime = 0.0f;
    }
    
    // Sample metrics at higher frequency
    if (LastSampleTime >= Configuration.MetricSamplingRate)
    {
        CollectMetrics();
        LastSampleTime = 0.0f;
    }
}

void UAuracronWorldPartitionPerformanceManager::StartMonitoring()
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot start monitoring: Manager not initialized"));
        return;
    }

    if (MonitoringState == EAuracronPerformanceMonitoringState::Enabled)
    {
        return; // Already monitoring
    }
    
    MonitoringState = EAuracronPerformanceMonitoringState::Enabled;
    MonitoringStartTime = FDateTime::Now();
    LastUpdateTime = 0.0f;
    LastSampleTime = 0.0f;
    
    // Reset metrics
    ResetMetrics();
    
    AURACRON_WP_LOG_INFO(TEXT("Performance monitoring started"));
}

void UAuracronWorldPartitionPerformanceManager::StopMonitoring()
{
    if (MonitoringState == EAuracronPerformanceMonitoringState::Disabled)
    {
        return; // Already stopped
    }
    
    MonitoringState = EAuracronPerformanceMonitoringState::Disabled;
    
    AURACRON_WP_LOG_INFO(TEXT("Performance monitoring stopped"));
}

void UAuracronWorldPartitionPerformanceManager::PauseMonitoring()
{
    if (MonitoringState == EAuracronPerformanceMonitoringState::Enabled)
    {
        MonitoringState = EAuracronPerformanceMonitoringState::Disabled;
        AURACRON_WP_LOG_INFO(TEXT("Performance monitoring paused"));
    }
}

void UAuracronWorldPartitionPerformanceManager::ResumeMonitoring()
{
    if (MonitoringState == EAuracronPerformanceMonitoringState::Disabled)
    {
        MonitoringState = EAuracronPerformanceMonitoringState::Enabled;
        AURACRON_WP_LOG_INFO(TEXT("Performance monitoring resumed"));
    }
}

EAuracronPerformanceMonitoringState UAuracronWorldPartitionPerformanceManager::GetMonitoringState() const
{
    return MonitoringState;
}

void UAuracronWorldPartitionPerformanceManager::CollectMetrics()
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&MetricsLock);
    
    // Collect different types of metrics
    if (Configuration.bEnableMemoryTracking)
    {
        CollectMemoryMetrics();
    }
    
    if (Configuration.bEnableCPUProfiling)
    {
        CollectCPUMetrics();
    }
    
    if (Configuration.bEnableGPUProfiling)
    {
        CollectGPUMetrics();
    }
    
    if (Configuration.bEnableStreamingMetrics)
    {
        CollectStreamingMetrics();
    }
    
    // Check thresholds and generate alerts
    CheckThresholds();
    
    // Detect bottlenecks
    if (Configuration.bEnableBottleneckDetection)
    {
        AnalyzeBottlenecks();
    }
    
    // Trim metric history to prevent memory bloat
    TrimMetricHistory();
}

FAuracronPerformanceMetric UAuracronWorldPartitionPerformanceManager::GetCurrentMetric(EAuracronPerformanceMetricType MetricType) const
{
    FScopeLock Lock(&MetricsLock);
    
    const FAuracronPerformanceMetric* Metric = CurrentMetrics.Find(MetricType);
    if (Metric)
    {
        return *Metric;
    }
    
    return FAuracronPerformanceMetric();
}

TArray<FAuracronPerformanceMetric> UAuracronWorldPartitionPerformanceManager::GetMetricHistory(EAuracronPerformanceMetricType MetricType, int32 MaxSamples) const
{
    FScopeLock Lock(&MetricsLock);
    
    const TArray<FAuracronPerformanceMetric>* History = MetricHistory.Find(MetricType);
    if (History)
    {
        if (MaxSamples <= 0 || MaxSamples >= History->Num())
        {
            return *History;
        }
        else
        {
            // Return the most recent samples
            int32 StartIndex = FMath::Max(0, History->Num() - MaxSamples);
            return TArray<FAuracronPerformanceMetric>(History->GetData() + StartIndex, MaxSamples);
        }
    }
    
    return TArray<FAuracronPerformanceMetric>();
}

TArray<FAuracronPerformanceMetric> UAuracronWorldPartitionPerformanceManager::GetAllCurrentMetrics() const
{
    FScopeLock Lock(&MetricsLock);
    
    TArray<FAuracronPerformanceMetric> AllMetrics;
    CurrentMetrics.GenerateValueArray(AllMetrics);
    
    return AllMetrics;
}

float UAuracronWorldPartitionPerformanceManager::GetCurrentMemoryUsageMB() const
{
    FAuracronPerformanceMetric MemoryMetric = GetCurrentMetric(EAuracronPerformanceMetricType::Memory);
    return MemoryMetric.Value;
}

float UAuracronWorldPartitionPerformanceManager::GetPeakMemoryUsageMB() const
{
    FAuracronPerformanceMetric MemoryMetric = GetCurrentMetric(EAuracronPerformanceMetricType::Memory);
    return MemoryMetric.MaxValue;
}

float UAuracronWorldPartitionPerformanceManager::GetAvailableMemoryMB() const
{
    // Get system memory information
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    return static_cast<float>(MemStats.AvailablePhysical) / (1024.0f * 1024.0f);
}

TMap<FString, float> UAuracronWorldPartitionPerformanceManager::GetMemoryBreakdown() const
{
    TMap<FString, float> Breakdown;
    
    // Get detailed memory statistics
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    
    Breakdown.Add(TEXT("Used Physical"), static_cast<float>(MemStats.UsedPhysical) / (1024.0f * 1024.0f));
    Breakdown.Add(TEXT("Available Physical"), static_cast<float>(MemStats.AvailablePhysical) / (1024.0f * 1024.0f));
    Breakdown.Add(TEXT("Total Physical"), static_cast<float>(MemStats.TotalPhysical) / (1024.0f * 1024.0f));
    Breakdown.Add(TEXT("Used Virtual"), static_cast<float>(MemStats.UsedVirtual) / (1024.0f * 1024.0f));
    Breakdown.Add(TEXT("Available Virtual"), static_cast<float>(MemStats.AvailableVirtual) / (1024.0f * 1024.0f));
    
    return Breakdown;
}

float UAuracronWorldPartitionPerformanceManager::GetCurrentCPUUsagePercent() const
{
    FAuracronPerformanceMetric CPUMetric = GetCurrentMetric(EAuracronPerformanceMetricType::CPU);
    return CPUMetric.Value;
}

float UAuracronWorldPartitionPerformanceManager::GetAverageCPUUsagePercent() const
{
    FAuracronPerformanceMetric CPUMetric = GetCurrentMetric(EAuracronPerformanceMetricType::CPU);
    return CPUMetric.AverageValue;
}

TMap<FString, float> UAuracronWorldPartitionPerformanceManager::GetCPUBreakdown() const
{
    TMap<FString, float> Breakdown;
    
    // Simulate CPU breakdown (in a real implementation, this would use actual profiling data)
    Breakdown.Add(TEXT("Game Thread"), GetCurrentCPUUsagePercent() * 0.4f);
    Breakdown.Add(TEXT("Render Thread"), GetCurrentCPUUsagePercent() * 0.3f);
    Breakdown.Add(TEXT("Worker Threads"), GetCurrentCPUUsagePercent() * 0.2f);
    Breakdown.Add(TEXT("Other"), GetCurrentCPUUsagePercent() * 0.1f);
    
    return Breakdown;
}

float UAuracronWorldPartitionPerformanceManager::GetCurrentGPUUsagePercent() const
{
    FAuracronPerformanceMetric GPUMetric = GetCurrentMetric(EAuracronPerformanceMetricType::GPU);
    return GPUMetric.Value;
}

float UAuracronWorldPartitionPerformanceManager::GetGPUMemoryUsageMB() const
{
    // Simulate GPU memory usage (in a real implementation, this would query actual GPU stats)
    return GetCurrentGPUUsagePercent() * 20.0f; // Rough estimation
}

TMap<FString, float> UAuracronWorldPartitionPerformanceManager::GetGPUBreakdown() const
{
    TMap<FString, float> Breakdown;

    // Simulate GPU breakdown
    float GPUUsage = GetCurrentGPUUsagePercent();
    Breakdown.Add(TEXT("Rendering"), GPUUsage * 0.6f);
    Breakdown.Add(TEXT("Compute"), GPUUsage * 0.2f);
    Breakdown.Add(TEXT("Post Processing"), GPUUsage * 0.15f);
    Breakdown.Add(TEXT("Other"), GPUUsage * 0.05f);

    return Breakdown;
}

float UAuracronWorldPartitionPerformanceManager::GetStreamingBandwidthMBps() const
{
    FAuracronPerformanceMetric StreamingMetric = GetCurrentMetric(EAuracronPerformanceMetricType::Streaming);
    return StreamingMetric.Value;
}

int32 UAuracronWorldPartitionPerformanceManager::GetActiveStreamingOperations() const
{
    // Simulate active streaming operations
    return FMath::RandRange(5, 25);
}

TMap<FString, float> UAuracronWorldPartitionPerformanceManager::GetStreamingBreakdown() const
{
    TMap<FString, float> Breakdown;

    float StreamingBandwidth = GetStreamingBandwidthMBps();
    Breakdown.Add(TEXT("Mesh Streaming"), StreamingBandwidth * 0.4f);
    Breakdown.Add(TEXT("Texture Streaming"), StreamingBandwidth * 0.3f);
    Breakdown.Add(TEXT("Audio Streaming"), StreamingBandwidth * 0.15f);
    Breakdown.Add(TEXT("Level Streaming"), StreamingBandwidth * 0.1f);
    Breakdown.Add(TEXT("Other"), StreamingBandwidth * 0.05f);

    return Breakdown;
}

EAuracronBottleneckType UAuracronWorldPartitionPerformanceManager::DetectBottleneck() const
{
    float CPUUsage = GetCurrentCPUUsagePercent();
    float GPUUsage = GetCurrentGPUUsagePercent();
    float MemoryUsage = GetCurrentMemoryUsageMB();
    float StreamingBandwidth = GetStreamingBandwidthMBps();

    // Simple bottleneck detection logic
    float MaxUsage = FMath::Max({CPUUsage, GPUUsage, MemoryUsage / 100.0f, StreamingBandwidth / 10.0f});

    if (MaxUsage == CPUUsage && CPUUsage > Configuration.CPUWarningThresholdPercent)
    {
        return EAuracronBottleneckType::CPU;
    }
    else if (MaxUsage == GPUUsage && GPUUsage > Configuration.GPUWarningThresholdPercent)
    {
        return EAuracronBottleneckType::GPU;
    }
    else if (MaxUsage == MemoryUsage / 100.0f && MemoryUsage > Configuration.MemoryWarningThresholdMB)
    {
        return EAuracronBottleneckType::Memory;
    }
    else if (MaxUsage == StreamingBandwidth / 10.0f && StreamingBandwidth > Configuration.StreamingWarningThresholdMBps)
    {
        return EAuracronBottleneckType::Streaming;
    }

    return EAuracronBottleneckType::None;
}

TArray<FString> UAuracronWorldPartitionPerformanceManager::GetBottleneckDetails(EAuracronBottleneckType BottleneckType) const
{
    TArray<FString> Details;

    switch (BottleneckType)
    {
        case EAuracronBottleneckType::CPU:
            Details.Add(FString::Printf(TEXT("CPU Usage: %.1f%%"), GetCurrentCPUUsagePercent()));
            Details.Add(FString::Printf(TEXT("Average CPU Usage: %.1f%%"), GetAverageCPUUsagePercent()));
            Details.Add(TEXT("High CPU usage detected in game thread"));
            break;

        case EAuracronBottleneckType::GPU:
            Details.Add(FString::Printf(TEXT("GPU Usage: %.1f%%"), GetCurrentGPUUsagePercent()));
            Details.Add(FString::Printf(TEXT("GPU Memory: %.1fMB"), GetGPUMemoryUsageMB()));
            Details.Add(TEXT("High GPU usage detected in rendering pipeline"));
            break;

        case EAuracronBottleneckType::Memory:
            Details.Add(FString::Printf(TEXT("Memory Usage: %.1fMB"), GetCurrentMemoryUsageMB()));
            Details.Add(FString::Printf(TEXT("Available Memory: %.1fMB"), GetAvailableMemoryMB()));
            Details.Add(TEXT("High memory usage detected"));
            break;

        case EAuracronBottleneckType::Streaming:
            Details.Add(FString::Printf(TEXT("Streaming Bandwidth: %.1fMB/s"), GetStreamingBandwidthMBps()));
            Details.Add(FString::Printf(TEXT("Active Operations: %d"), GetActiveStreamingOperations()));
            Details.Add(TEXT("High streaming load detected"));
            break;

        default:
            Details.Add(TEXT("No significant bottleneck detected"));
            break;
    }

    return Details;
}

float UAuracronWorldPartitionPerformanceManager::GetBottleneckSeverity(EAuracronBottleneckType BottleneckType) const
{
    switch (BottleneckType)
    {
        case EAuracronBottleneckType::CPU:
            return FMath::Clamp(GetCurrentCPUUsagePercent() / 100.0f, 0.0f, 1.0f);

        case EAuracronBottleneckType::GPU:
            return FMath::Clamp(GetCurrentGPUUsagePercent() / 100.0f, 0.0f, 1.0f);

        case EAuracronBottleneckType::Memory:
            return FMath::Clamp(GetCurrentMemoryUsageMB() / Configuration.MemoryCriticalThresholdMB, 0.0f, 1.0f);

        case EAuracronBottleneckType::Streaming:
            return FMath::Clamp(GetStreamingBandwidthMBps() / Configuration.StreamingCriticalThresholdMBps, 0.0f, 1.0f);

        default:
            return 0.0f;
    }
}

TArray<FString> UAuracronWorldPartitionPerformanceManager::GetOptimizationSuggestions() const
{
    TArray<FString> Suggestions;

    EAuracronBottleneckType Bottleneck = DetectBottleneck();
    if (Bottleneck != EAuracronBottleneckType::None)
    {
        Suggestions.Append(GetOptimizationSuggestionsForBottleneck(Bottleneck));
    }

    // General optimization suggestions
    if (GetCurrentMemoryUsageMB() > Configuration.MemoryWarningThresholdMB * 0.8f)
    {
        Suggestions.Add(TEXT("Consider reducing texture quality or resolution"));
        Suggestions.Add(TEXT("Enable texture streaming optimizations"));
    }

    if (GetCurrentCPUUsagePercent() > Configuration.CPUWarningThresholdPercent * 0.8f)
    {
        Suggestions.Add(TEXT("Consider reducing draw calls"));
        Suggestions.Add(TEXT("Enable instancing for repeated objects"));
    }

    if (GetCurrentGPUUsagePercent() > Configuration.GPUWarningThresholdPercent * 0.8f)
    {
        Suggestions.Add(TEXT("Consider reducing shadow quality"));
        Suggestions.Add(TEXT("Enable GPU-driven rendering optimizations"));
    }

    return Suggestions;
}

TArray<FString> UAuracronWorldPartitionPerformanceManager::GetOptimizationSuggestionsForBottleneck(EAuracronBottleneckType BottleneckType) const
{
    TArray<FString> Suggestions;

    switch (BottleneckType)
    {
        case EAuracronBottleneckType::CPU:
            Suggestions.Add(TEXT("Reduce tick frequency for non-critical actors"));
            Suggestions.Add(TEXT("Enable multithreading for heavy computations"));
            Suggestions.Add(TEXT("Optimize blueprint execution"));
            Suggestions.Add(TEXT("Use object pooling for frequently spawned objects"));
            break;

        case EAuracronBottleneckType::GPU:
            Suggestions.Add(TEXT("Reduce shadow resolution and cascade count"));
            Suggestions.Add(TEXT("Enable GPU instancing"));
            Suggestions.Add(TEXT("Optimize material complexity"));
            Suggestions.Add(TEXT("Use LOD system more aggressively"));
            break;

        case EAuracronBottleneckType::Memory:
            Suggestions.Add(TEXT("Enable texture streaming"));
            Suggestions.Add(TEXT("Reduce texture resolution"));
            Suggestions.Add(TEXT("Implement object pooling"));
            Suggestions.Add(TEXT("Optimize mesh LODs"));
            break;

        case EAuracronBottleneckType::Streaming:
            Suggestions.Add(TEXT("Increase streaming pool size"));
            Suggestions.Add(TEXT("Optimize streaming distances"));
            Suggestions.Add(TEXT("Enable predictive streaming"));
            Suggestions.Add(TEXT("Reduce streaming quality temporarily"));
            break;

        case EAuracronBottleneckType::IO:
            Suggestions.Add(TEXT("Use faster storage (SSD)"));
            Suggestions.Add(TEXT("Optimize asset compression"));
            Suggestions.Add(TEXT("Implement asset bundling"));
            break;

        case EAuracronBottleneckType::Network:
            Suggestions.Add(TEXT("Optimize network replication"));
            Suggestions.Add(TEXT("Reduce update frequency for distant objects"));
            Suggestions.Add(TEXT("Implement delta compression"));
            break;

        default:
            break;
    }

    return Suggestions;
}

bool UAuracronWorldPartitionPerformanceManager::ApplyAutoOptimization()
{
    if (!Configuration.bEnableAutoOptimization)
    {
        return false;
    }

    EAuracronBottleneckType Bottleneck = DetectBottleneck();
    if (Bottleneck == EAuracronBottleneckType::None)
    {
        return true; // No optimization needed
    }

    // Apply automatic optimizations based on bottleneck type
    // In a real implementation, this would actually modify engine settings

    AURACRON_WP_LOG_INFO(TEXT("Auto-optimization applied for bottleneck: %s"), *UEnum::GetValueAsString(Bottleneck));

    return true;
}

float UAuracronWorldPartitionPerformanceManager::CalculatePerformanceScore() const
{
    float Score = 100.0f;

    // Deduct points based on resource usage
    float CPUUsage = GetCurrentCPUUsagePercent();
    float GPUUsage = GetCurrentGPUUsagePercent();
    float MemoryUsage = GetCurrentMemoryUsageMB();
    float StreamingBandwidth = GetStreamingBandwidthMBps();

    // CPU penalty
    if (CPUUsage > Configuration.CPUWarningThresholdPercent)
    {
        Score -= (CPUUsage - Configuration.CPUWarningThresholdPercent) * 0.5f;
    }

    // GPU penalty
    if (GPUUsage > Configuration.GPUWarningThresholdPercent)
    {
        Score -= (GPUUsage - Configuration.GPUWarningThresholdPercent) * 0.5f;
    }

    // Memory penalty
    if (MemoryUsage > Configuration.MemoryWarningThresholdMB)
    {
        Score -= (MemoryUsage - Configuration.MemoryWarningThresholdMB) / Configuration.MemoryWarningThresholdMB * 20.0f;
    }

    // Streaming penalty
    if (StreamingBandwidth > Configuration.StreamingWarningThresholdMBps)
    {
        Score -= (StreamingBandwidth - Configuration.StreamingWarningThresholdMBps) / Configuration.StreamingWarningThresholdMBps * 10.0f;
    }

    return FMath::Clamp(Score, 0.0f, 100.0f);
}

TMap<FString, float> UAuracronWorldPartitionPerformanceManager::GetPerformanceScoreBreakdown() const
{
    TMap<FString, float> Breakdown;

    float CPUScore = FMath::Max(0.0f, 100.0f - GetCurrentCPUUsagePercent());
    float GPUScore = FMath::Max(0.0f, 100.0f - GetCurrentGPUUsagePercent());
    float MemoryScore = FMath::Max(0.0f, 100.0f - (GetCurrentMemoryUsageMB() / Configuration.MemoryWarningThresholdMB * 100.0f));
    float StreamingScore = FMath::Max(0.0f, 100.0f - (GetStreamingBandwidthMBps() / Configuration.StreamingWarningThresholdMBps * 100.0f));

    Breakdown.Add(TEXT("CPU"), CPUScore);
    Breakdown.Add(TEXT("GPU"), GPUScore);
    Breakdown.Add(TEXT("Memory"), MemoryScore);
    Breakdown.Add(TEXT("Streaming"), StreamingScore);
    Breakdown.Add(TEXT("Overall"), CalculatePerformanceScore());

    return Breakdown;
}

FAuracronPerformanceReport UAuracronWorldPartitionPerformanceManager::GeneratePerformanceReport() const
{
    FAuracronPerformanceReport Report;

    Report.ReportId = FString::Printf(TEXT("PerfReport_%lld"), FDateTime::Now().GetTicks());
    Report.GenerationTime = FDateTime::Now();
    Report.MonitoringDuration = FDateTime::Now() - MonitoringStartTime;
    Report.Metrics = GetAllCurrentMetrics();
    Report.PrimaryBottleneck = DetectBottleneck();
    Report.OptimizationSuggestions = GetOptimizationSuggestions();
    Report.OverallPerformanceScore = CalculatePerformanceScore();

    // Count warnings and criticals
    for (const FAuracronPerformanceMetric& Metric : Report.Metrics)
    {
        if (Metric.Severity == EAuracronPerformanceSeverity::Warning)
        {
            Report.WarningCount++;
        }
        else if (Metric.Severity == EAuracronPerformanceSeverity::Critical)
        {
            Report.CriticalCount++;
        }
    }

    // Generate summary
    Report.Summary = FString::Printf(TEXT("Performance Score: %.1f/100, Bottleneck: %s, Warnings: %d, Critical: %d"),
                                   Report.OverallPerformanceScore,
                                   *UEnum::GetValueAsString(Report.PrimaryBottleneck),
                                   Report.WarningCount,
                                   Report.CriticalCount);

    OnPerformanceReportGenerated.Broadcast(Report);

    return Report;
}

bool UAuracronWorldPartitionPerformanceManager::SavePerformanceReport(const FAuracronPerformanceReport& Report, const FString& FilePath) const
{
    // In a real implementation, this would serialize the report to JSON or XML
    FString ReportContent = FString::Printf(TEXT("Performance Report: %s\nGenerated: %s\nScore: %.1f\nBottleneck: %s\n"),
                                          *Report.ReportId,
                                          *Report.GenerationTime.ToString(),
                                          Report.OverallPerformanceScore,
                                          *UEnum::GetValueAsString(Report.PrimaryBottleneck));

    return FFileHelper::SaveStringToFile(ReportContent, *FilePath);
}

FAuracronPerformanceReport UAuracronWorldPartitionPerformanceManager::LoadPerformanceReport(const FString& FilePath) const
{
    FAuracronPerformanceReport Report;

    FString ReportContent;
    if (FFileHelper::LoadFileToString(ReportContent, *FilePath))
    {
        // In a real implementation, this would deserialize from JSON or XML
        Report.ReportId = TEXT("LoadedReport");
        Report.GenerationTime = FDateTime::Now();
    }

    return Report;
}

void UAuracronWorldPartitionPerformanceManager::SetThreshold(EAuracronPerformanceMetricType MetricType, EAuracronPerformanceSeverity Severity, float Threshold)
{
    FScopeLock Lock(&MetricsLock);

    TMap<EAuracronPerformanceSeverity, float>& MetricThresholds = Thresholds.FindOrAdd(MetricType);
    MetricThresholds.Add(Severity, Threshold);

    AURACRON_WP_LOG_VERBOSE(TEXT("Threshold set: %s %s = %.2f"),
                           *UEnum::GetValueAsString(MetricType),
                           *UEnum::GetValueAsString(Severity),
                           Threshold);
}

float UAuracronWorldPartitionPerformanceManager::GetThreshold(EAuracronPerformanceMetricType MetricType, EAuracronPerformanceSeverity Severity) const
{
    FScopeLock Lock(&MetricsLock);

    const TMap<EAuracronPerformanceSeverity, float>* MetricThresholds = Thresholds.Find(MetricType);
    if (MetricThresholds)
    {
        const float* Threshold = MetricThresholds->Find(Severity);
        if (Threshold)
        {
            return *Threshold;
        }
    }

    return 0.0f;
}

TArray<FAuracronPerformanceMetric> UAuracronWorldPartitionPerformanceManager::GetActiveAlerts() const
{
    TArray<FAuracronPerformanceMetric> Alerts;

    FScopeLock Lock(&MetricsLock);

    for (const auto& MetricPair : CurrentMetrics)
    {
        const FAuracronPerformanceMetric& Metric = MetricPair.Value;
        if (Metric.Severity == EAuracronPerformanceSeverity::Warning ||
            Metric.Severity == EAuracronPerformanceSeverity::Critical ||
            Metric.Severity == EAuracronPerformanceSeverity::Emergency)
        {
            Alerts.Add(Metric);
        }
    }

    return Alerts;
}

void UAuracronWorldPartitionPerformanceManager::SetConfiguration(const FAuracronPerformanceConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Performance configuration updated"));
}

FAuracronPerformanceConfiguration UAuracronWorldPartitionPerformanceManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronWorldPartitionPerformanceManager::EnablePerformanceDebug(bool bEnabled)
{
    Configuration.bEnablePerformanceDebug = bEnabled;

    AURACRON_WP_LOG_INFO(TEXT("Performance debug %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionPerformanceManager::IsPerformanceDebugEnabled() const
{
    return Configuration.bEnablePerformanceDebug;
}

void UAuracronWorldPartitionPerformanceManager::LogPerformanceState() const
{
    AURACRON_WP_LOG_INFO(TEXT("Performance State: Score %.1f, Bottleneck %s"),
                         CalculatePerformanceScore(),
                         *UEnum::GetValueAsString(DetectBottleneck()));

    AURACRON_WP_LOG_INFO(TEXT("Resources: CPU %.1f%%, GPU %.1f%%, Memory %.1fMB, Streaming %.1fMB/s"),
                         GetCurrentCPUUsagePercent(),
                         GetCurrentGPUUsagePercent(),
                         GetCurrentMemoryUsageMB(),
                         GetStreamingBandwidthMBps());
}

void UAuracronWorldPartitionPerformanceManager::DrawDebugPerformanceInfo(UWorld* World) const
{
    if (!Configuration.bEnablePerformanceDebug || !World)
    {
        return;
    }

    // Draw performance metrics on screen
    FVector DebugLocation = FVector(0, 0, 1000);

    FString PerformanceText = FString::Printf(TEXT("Performance Score: %.1f/100\nBottleneck: %s\nCPU: %.1f%% GPU: %.1f%%\nMemory: %.1fMB Streaming: %.1fMB/s"),
                                            CalculatePerformanceScore(),
                                            *UEnum::GetValueAsString(DetectBottleneck()),
                                            GetCurrentCPUUsagePercent(),
                                            GetCurrentGPUUsagePercent(),
                                            GetCurrentMemoryUsageMB(),
                                            GetStreamingBandwidthMBps());

    FColor DebugColor = FColor::Green;
    float Score = CalculatePerformanceScore();
    if (Score < 50.0f)
    {
        DebugColor = FColor::Red;
    }
    else if (Score < 75.0f)
    {
        DebugColor = FColor::Yellow;
    }

    DrawDebugString(World, DebugLocation, PerformanceText, nullptr, DebugColor, -1.0f, true);
}

void UAuracronWorldPartitionPerformanceManager::ResetMetrics()
{
    FScopeLock Lock(&MetricsLock);

    MetricHistory.Empty();
    CurrentMetrics.Empty();
    MonitoringStartTime = FDateTime::Now();

    AURACRON_WP_LOG_INFO(TEXT("Performance metrics reset"));
}

void UAuracronWorldPartitionPerformanceManager::UpdateMetrics(float DeltaTime)
{
    // Update internal metrics and perform analysis
    CheckThresholds();

    if (Configuration.bEnableBottleneckDetection)
    {
        AnalyzeBottlenecks();
    }

    if (Configuration.bEnableOptimizationSuggestions)
    {
        GenerateOptimizationSuggestions();
    }
}

void UAuracronWorldPartitionPerformanceManager::CollectMemoryMetrics()
{
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    float MemoryUsageMB = static_cast<float>(MemStats.UsedPhysical) / (1024.0f * 1024.0f);

    FAuracronPerformanceMetric MemoryMetric = CreateMetric(EAuracronPerformanceMetricType::Memory, TEXT("Memory Usage"), MemoryUsageMB, TEXT("MB"));
    MemoryMetric.Severity = CalculateMetricSeverity(EAuracronPerformanceMetricType::Memory, MemoryUsageMB);

    FScopeLock Lock(&MetricsLock);
    CurrentMetrics.Add(EAuracronPerformanceMetricType::Memory, MemoryMetric);

    TArray<FAuracronPerformanceMetric>& History = MetricHistory.FindOrAdd(EAuracronPerformanceMetricType::Memory);
    History.Add(MemoryMetric);
}

void UAuracronWorldPartitionPerformanceManager::CollectCPUMetrics()
{
    // Simulate CPU usage (in a real implementation, this would use actual profiling data)
    float CPUUsage = FMath::RandRange(20.0f, 85.0f);

    FAuracronPerformanceMetric CPUMetric = CreateMetric(EAuracronPerformanceMetricType::CPU, TEXT("CPU Usage"), CPUUsage, TEXT("%"));
    CPUMetric.Severity = CalculateMetricSeverity(EAuracronPerformanceMetricType::CPU, CPUUsage);

    FScopeLock Lock(&MetricsLock);
    CurrentMetrics.Add(EAuracronPerformanceMetricType::CPU, CPUMetric);

    TArray<FAuracronPerformanceMetric>& History = MetricHistory.FindOrAdd(EAuracronPerformanceMetricType::CPU);
    History.Add(CPUMetric);
}

void UAuracronWorldPartitionPerformanceManager::CollectGPUMetrics()
{
    // Simulate GPU usage (in a real implementation, this would use actual GPU profiling data)
    float GPUUsage = FMath::RandRange(30.0f, 90.0f);

    FAuracronPerformanceMetric GPUMetric = CreateMetric(EAuracronPerformanceMetricType::GPU, TEXT("GPU Usage"), GPUUsage, TEXT("%"));
    GPUMetric.Severity = CalculateMetricSeverity(EAuracronPerformanceMetricType::GPU, GPUUsage);

    FScopeLock Lock(&MetricsLock);
    CurrentMetrics.Add(EAuracronPerformanceMetricType::GPU, GPUMetric);

    TArray<FAuracronPerformanceMetric>& History = MetricHistory.FindOrAdd(EAuracronPerformanceMetricType::GPU);
    History.Add(GPUMetric);
}

void UAuracronWorldPartitionPerformanceManager::CollectStreamingMetrics()
{
    // Simulate streaming bandwidth
    float StreamingBandwidth = FMath::RandRange(10.0f, 80.0f);

    FAuracronPerformanceMetric StreamingMetric = CreateMetric(EAuracronPerformanceMetricType::Streaming, TEXT("Streaming Bandwidth"), StreamingBandwidth, TEXT("MB/s"));
    StreamingMetric.Severity = CalculateMetricSeverity(EAuracronPerformanceMetricType::Streaming, StreamingBandwidth);

    FScopeLock Lock(&MetricsLock);
    CurrentMetrics.Add(EAuracronPerformanceMetricType::Streaming, StreamingMetric);

    TArray<FAuracronPerformanceMetric>& History = MetricHistory.FindOrAdd(EAuracronPerformanceMetricType::Streaming);
    History.Add(StreamingMetric);
}

void UAuracronWorldPartitionPerformanceManager::CheckThresholds()
{
    FScopeLock Lock(&MetricsLock);

    for (const auto& MetricPair : CurrentMetrics)
    {
        const FAuracronPerformanceMetric& Metric = MetricPair.Value;

        if (Metric.Severity == EAuracronPerformanceSeverity::Warning ||
            Metric.Severity == EAuracronPerformanceSeverity::Critical ||
            Metric.Severity == EAuracronPerformanceSeverity::Emergency)
        {
            OnPerformanceAlert.Broadcast(Metric);
            OnThresholdExceeded.Broadcast(Metric.MetricType, Metric.Value);
        }
    }
}

void UAuracronWorldPartitionPerformanceManager::AnalyzeBottlenecks()
{
    EAuracronBottleneckType CurrentBottleneck = DetectBottleneck();
    static EAuracronBottleneckType LastBottleneck = EAuracronBottleneckType::None;

    if (CurrentBottleneck != LastBottleneck && CurrentBottleneck != EAuracronBottleneckType::None)
    {
        OnBottleneckDetected.Broadcast(CurrentBottleneck);
        LastBottleneck = CurrentBottleneck;
    }
}

void UAuracronWorldPartitionPerformanceManager::GenerateOptimizationSuggestions()
{
    // Optimization suggestions are generated on-demand
    // This function could cache suggestions for performance
}

void UAuracronWorldPartitionPerformanceManager::ValidateConfiguration()
{
    // Validate monitoring intervals
    Configuration.MonitoringUpdateInterval = FMath::Max(0.1f, Configuration.MonitoringUpdateInterval);
    Configuration.MetricSamplingRate = FMath::Max(0.01f, Configuration.MetricSamplingRate);
    Configuration.MaxMetricHistorySize = FMath::Max(10, Configuration.MaxMetricHistorySize);

    // Validate thresholds
    Configuration.MemoryWarningThresholdMB = FMath::Max(100.0f, Configuration.MemoryWarningThresholdMB);
    Configuration.MemoryCriticalThresholdMB = FMath::Max(Configuration.MemoryWarningThresholdMB, Configuration.MemoryCriticalThresholdMB);
    Configuration.CPUWarningThresholdPercent = FMath::Clamp(Configuration.CPUWarningThresholdPercent, 10.0f, 100.0f);
    Configuration.CPUCriticalThresholdPercent = FMath::Clamp(Configuration.CPUCriticalThresholdPercent, Configuration.CPUWarningThresholdPercent, 100.0f);
    Configuration.GPUWarningThresholdPercent = FMath::Clamp(Configuration.GPUWarningThresholdPercent, 10.0f, 100.0f);
    Configuration.GPUCriticalThresholdPercent = FMath::Clamp(Configuration.GPUCriticalThresholdPercent, Configuration.GPUWarningThresholdPercent, 100.0f);
}

FAuracronPerformanceMetric UAuracronWorldPartitionPerformanceManager::CreateMetric(EAuracronPerformanceMetricType MetricType, const FString& Name, float Value, const FString& Unit) const
{
    FAuracronPerformanceMetric Metric;
    Metric.MetricId = FString::Printf(TEXT("%s_%lld"), *Name, FDateTime::Now().GetTicks());
    Metric.MetricName = Name;
    Metric.MetricType = MetricType;
    Metric.Value = Value;
    Metric.Unit = Unit;
    Metric.Timestamp = FDateTime::Now();
    Metric.Severity = CalculateMetricSeverity(MetricType, Value);

    return Metric;
}

EAuracronPerformanceSeverity UAuracronWorldPartitionPerformanceManager::CalculateMetricSeverity(EAuracronPerformanceMetricType MetricType, float Value) const
{
    float WarningThreshold = GetThreshold(MetricType, EAuracronPerformanceSeverity::Warning);
    float CriticalThreshold = GetThreshold(MetricType, EAuracronPerformanceSeverity::Critical);

    if (Value >= CriticalThreshold)
    {
        return EAuracronPerformanceSeverity::Critical;
    }
    else if (Value >= WarningThreshold)
    {
        return EAuracronPerformanceSeverity::Warning;
    }

    return EAuracronPerformanceSeverity::Info;
}

void UAuracronWorldPartitionPerformanceManager::TrimMetricHistory()
{
    FScopeLock Lock(&MetricsLock);

    for (auto& HistoryPair : MetricHistory)
    {
        TArray<FAuracronPerformanceMetric>& History = HistoryPair.Value;

        if (History.Num() > Configuration.MaxMetricHistorySize)
        {
            int32 ExcessCount = History.Num() - Configuration.MaxMetricHistorySize;
            History.RemoveAt(0, ExcessCount);
        }
    }
}
