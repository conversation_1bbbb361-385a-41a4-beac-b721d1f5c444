// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronTextureGeneration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronTextureGeneration() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAgingEffectData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FMakeupData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FNoiseParameters();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FScarData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FSkinVariationData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FTattooData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FTextureGenerationParameters();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ETextureQuality ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ETextureQuality;
static UEnum* ETextureQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ETextureQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ETextureQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ETextureQuality"));
	}
	return Z_Registration_Info_UEnum_ETextureQuality.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ETextureQuality>()
{
	return ETextureQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for texture generation\n" },
#endif
		{ "Custom.DisplayName", "Custom Resolution" },
		{ "Custom.Name", "ETextureQuality::Custom" },
		{ "High.DisplayName", "High (2048x2048)" },
		{ "High.Name", "ETextureQuality::High" },
		{ "Low.DisplayName", "Low (512x512)" },
		{ "Low.Name", "ETextureQuality::Low" },
		{ "Medium.DisplayName", "Medium (1024x1024)" },
		{ "Medium.Name", "ETextureQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for texture generation" },
#endif
		{ "Ultra.DisplayName", "Ultra (4096x4096)" },
		{ "Ultra.Name", "ETextureQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ETextureQuality::Low", (int64)ETextureQuality::Low },
		{ "ETextureQuality::Medium", (int64)ETextureQuality::Medium },
		{ "ETextureQuality::High", (int64)ETextureQuality::High },
		{ "ETextureQuality::Ultra", (int64)ETextureQuality::Ultra },
		{ "ETextureQuality::Custom", (int64)ETextureQuality::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ETextureQuality",
	"ETextureQuality",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality()
{
	if (!Z_Registration_Info_UEnum_ETextureQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ETextureQuality.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ETextureQuality.InnerSingleton;
}
// ********** End Enum ETextureQuality *************************************************************

// ********** Begin Enum EAuracronTextureType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTextureType;
static UEnum* EAuracronTextureType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTextureType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTextureType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EAuracronTextureType"));
	}
	return Z_Registration_Info_UEnum_EAuracronTextureType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAuracronTextureType>()
{
	return EAuracronTextureType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AmbientOcclusion.DisplayName", "Ambient Occlusion" },
		{ "AmbientOcclusion.Name", "EAuracronTextureType::AmbientOcclusion" },
		{ "BlueprintType", "true" },
		{ "Diffuse.DisplayName", "Diffuse" },
		{ "Diffuse.Name", "EAuracronTextureType::Diffuse" },
		{ "Emissive.DisplayName", "Emissive" },
		{ "Emissive.Name", "EAuracronTextureType::Emissive" },
		{ "Height.DisplayName", "Height" },
		{ "Height.Name", "EAuracronTextureType::Height" },
		{ "Metallic.DisplayName", "Metallic" },
		{ "Metallic.Name", "EAuracronTextureType::Metallic" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronTextureType::Normal" },
		{ "Opacity.DisplayName", "Opacity" },
		{ "Opacity.Name", "EAuracronTextureType::Opacity" },
		{ "Roughness.DisplayName", "Roughness" },
		{ "Roughness.Name", "EAuracronTextureType::Roughness" },
		{ "Specular.DisplayName", "Specular" },
		{ "Specular.Name", "EAuracronTextureType::Specular" },
		{ "Subsurface.DisplayName", "Subsurface" },
		{ "Subsurface.Name", "EAuracronTextureType::Subsurface" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTextureType::Diffuse", (int64)EAuracronTextureType::Diffuse },
		{ "EAuracronTextureType::Normal", (int64)EAuracronTextureType::Normal },
		{ "EAuracronTextureType::Roughness", (int64)EAuracronTextureType::Roughness },
		{ "EAuracronTextureType::Metallic", (int64)EAuracronTextureType::Metallic },
		{ "EAuracronTextureType::Specular", (int64)EAuracronTextureType::Specular },
		{ "EAuracronTextureType::Emissive", (int64)EAuracronTextureType::Emissive },
		{ "EAuracronTextureType::Opacity", (int64)EAuracronTextureType::Opacity },
		{ "EAuracronTextureType::Height", (int64)EAuracronTextureType::Height },
		{ "EAuracronTextureType::AmbientOcclusion", (int64)EAuracronTextureType::AmbientOcclusion },
		{ "EAuracronTextureType::Subsurface", (int64)EAuracronTextureType::Subsurface },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EAuracronTextureType",
	"EAuracronTextureType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType()
{
	if (!Z_Registration_Info_UEnum_EAuracronTextureType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTextureType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTextureType.InnerSingleton;
}
// ********** End Enum EAuracronTextureType ********************************************************

// ********** Begin Enum ENoiseType ****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ENoiseType;
static UEnum* ENoiseType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ENoiseType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ENoiseType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ENoiseType"));
	}
	return Z_Registration_Info_UEnum_ENoiseType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ENoiseType>()
{
	return ENoiseType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Billow.DisplayName", "Billow" },
		{ "Billow.Name", "ENoiseType::Billow" },
		{ "BlueprintType", "true" },
		{ "FBM.DisplayName", "Fractal Brownian Motion" },
		{ "FBM.Name", "ENoiseType::FBM" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
		{ "Perlin.DisplayName", "Perlin" },
		{ "Perlin.Name", "ENoiseType::Perlin" },
		{ "Ridged.DisplayName", "Ridged Multifractal" },
		{ "Ridged.Name", "ENoiseType::Ridged" },
		{ "Simplex.DisplayName", "Simplex" },
		{ "Simplex.Name", "ENoiseType::Simplex" },
		{ "Worley.DisplayName", "Worley" },
		{ "Worley.Name", "ENoiseType::Worley" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ENoiseType::Perlin", (int64)ENoiseType::Perlin },
		{ "ENoiseType::Simplex", (int64)ENoiseType::Simplex },
		{ "ENoiseType::Worley", (int64)ENoiseType::Worley },
		{ "ENoiseType::FBM", (int64)ENoiseType::FBM },
		{ "ENoiseType::Ridged", (int64)ENoiseType::Ridged },
		{ "ENoiseType::Billow", (int64)ENoiseType::Billow },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ENoiseType",
	"ENoiseType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType()
{
	if (!Z_Registration_Info_UEnum_ENoiseType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ENoiseType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ENoiseType.InnerSingleton;
}
// ********** End Enum ENoiseType ******************************************************************

// ********** Begin Enum ETextureBlendMode *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ETextureBlendMode;
static UEnum* ETextureBlendMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ETextureBlendMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ETextureBlendMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ETextureBlendMode"));
	}
	return Z_Registration_Info_UEnum_ETextureBlendMode.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ETextureBlendMode>()
{
	return ETextureBlendMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ColorBurn.DisplayName", "Color Burn" },
		{ "ColorBurn.Name", "ETextureBlendMode::ColorBurn" },
		{ "ColorDodge.DisplayName", "Color Dodge" },
		{ "ColorDodge.Name", "ETextureBlendMode::ColorDodge" },
		{ "Darken.DisplayName", "Darken" },
		{ "Darken.Name", "ETextureBlendMode::Darken" },
		{ "Difference.DisplayName", "Difference" },
		{ "Difference.Name", "ETextureBlendMode::Difference" },
		{ "Exclusion.DisplayName", "Exclusion" },
		{ "Exclusion.Name", "ETextureBlendMode::Exclusion" },
		{ "HardLight.DisplayName", "Hard Light" },
		{ "HardLight.Name", "ETextureBlendMode::HardLight" },
		{ "Lighten.DisplayName", "Lighten" },
		{ "Lighten.Name", "ETextureBlendMode::Lighten" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "ETextureBlendMode::Multiply" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "ETextureBlendMode::Normal" },
		{ "Overlay.DisplayName", "Overlay" },
		{ "Overlay.Name", "ETextureBlendMode::Overlay" },
		{ "Screen.DisplayName", "Screen" },
		{ "Screen.Name", "ETextureBlendMode::Screen" },
		{ "SoftLight.DisplayName", "Soft Light" },
		{ "SoftLight.Name", "ETextureBlendMode::SoftLight" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ETextureBlendMode::Normal", (int64)ETextureBlendMode::Normal },
		{ "ETextureBlendMode::Multiply", (int64)ETextureBlendMode::Multiply },
		{ "ETextureBlendMode::Screen", (int64)ETextureBlendMode::Screen },
		{ "ETextureBlendMode::Overlay", (int64)ETextureBlendMode::Overlay },
		{ "ETextureBlendMode::SoftLight", (int64)ETextureBlendMode::SoftLight },
		{ "ETextureBlendMode::HardLight", (int64)ETextureBlendMode::HardLight },
		{ "ETextureBlendMode::ColorDodge", (int64)ETextureBlendMode::ColorDodge },
		{ "ETextureBlendMode::ColorBurn", (int64)ETextureBlendMode::ColorBurn },
		{ "ETextureBlendMode::Darken", (int64)ETextureBlendMode::Darken },
		{ "ETextureBlendMode::Lighten", (int64)ETextureBlendMode::Lighten },
		{ "ETextureBlendMode::Difference", (int64)ETextureBlendMode::Difference },
		{ "ETextureBlendMode::Exclusion", (int64)ETextureBlendMode::Exclusion },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ETextureBlendMode",
	"ETextureBlendMode",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode()
{
	if (!Z_Registration_Info_UEnum_ETextureBlendMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ETextureBlendMode.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureBlendMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ETextureBlendMode.InnerSingleton;
}
// ********** End Enum ETextureBlendMode ***********************************************************

// ********** Begin ScriptStruct FNoiseParameters **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FNoiseParameters;
class UScriptStruct* FNoiseParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FNoiseParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FNoiseParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FNoiseParameters, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("NoiseParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FNoiseParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FNoiseParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for texture generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for texture generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Frequency_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Amplitude_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Octaves_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Lacunarity_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Persistence_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Seed_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amplitude;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Octaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Lacunarity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Persistence;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FNoiseParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, NoiseType), Z_Construct_UEnum_AuracronMetaHumanBridge_ENoiseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 141586324
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, Frequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Frequency_MetaData), NewProp_Frequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Amplitude = { "Amplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, Amplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Amplitude_MetaData), NewProp_Amplitude_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Octaves = { "Octaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, Octaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Octaves_MetaData), NewProp_Octaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Lacunarity = { "Lacunarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, Lacunarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Lacunarity_MetaData), NewProp_Lacunarity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Persistence = { "Persistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, Persistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Persistence_MetaData), NewProp_Persistence_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, Offset), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNoiseParameters, Seed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Seed_MetaData), NewProp_Seed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FNoiseParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Amplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Octaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Lacunarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Persistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewProp_Seed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FNoiseParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FNoiseParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"NoiseParameters",
	Z_Construct_UScriptStruct_FNoiseParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FNoiseParameters_Statics::PropPointers),
	sizeof(FNoiseParameters),
	alignof(FNoiseParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FNoiseParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FNoiseParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FNoiseParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FNoiseParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FNoiseParameters.InnerSingleton, Z_Construct_UScriptStruct_FNoiseParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FNoiseParameters.InnerSingleton;
}
// ********** End ScriptStruct FNoiseParameters ****************************************************

// ********** Begin ScriptStruct FSkinVariationData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSkinVariationData;
class UScriptStruct* FSkinVariationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSkinVariationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSkinVariationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSkinVariationData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("SkinVariationData"));
	}
	return Z_Registration_Info_UScriptStruct_FSkinVariationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSkinVariationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoreSize_MetaData[] = {
		{ "Category", "Skin Variation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoreIntensity_MetaData[] = {
		{ "Category", "Skin Variation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WrinkleIntensity_MetaData[] = {
		{ "Category", "Skin Variation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FreckleIntensity_MetaData[] = {
		{ "Category", "Skin Variation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlemishIntensity_MetaData[] = {
		{ "Category", "Skin Variation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseColor_MetaData[] = {
		{ "Category", "Skin Variation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseParams_MetaData[] = {
		{ "Category", "Skin Variation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PoreSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PoreIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WrinkleIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FreckleIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlemishIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseParams;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSkinVariationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_PoreSize = { "PoreSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkinVariationData, PoreSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoreSize_MetaData), NewProp_PoreSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_PoreIntensity = { "PoreIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkinVariationData, PoreIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoreIntensity_MetaData), NewProp_PoreIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_WrinkleIntensity = { "WrinkleIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkinVariationData, WrinkleIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WrinkleIntensity_MetaData), NewProp_WrinkleIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_FreckleIntensity = { "FreckleIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkinVariationData, FreckleIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FreckleIntensity_MetaData), NewProp_FreckleIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_BlemishIntensity = { "BlemishIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkinVariationData, BlemishIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlemishIntensity_MetaData), NewProp_BlemishIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_BaseColor = { "BaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkinVariationData, BaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseColor_MetaData), NewProp_BaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_NoiseParams = { "NoiseParams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkinVariationData, NoiseParams), Z_Construct_UScriptStruct_FNoiseParameters, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseParams_MetaData), NewProp_NoiseParams_MetaData) }; // 3449342282
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSkinVariationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_PoreSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_PoreIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_WrinkleIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_FreckleIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_BlemishIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_BaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewProp_NoiseParams,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSkinVariationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSkinVariationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"SkinVariationData",
	Z_Construct_UScriptStruct_FSkinVariationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSkinVariationData_Statics::PropPointers),
	sizeof(FSkinVariationData),
	alignof(FSkinVariationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSkinVariationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSkinVariationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSkinVariationData()
{
	if (!Z_Registration_Info_UScriptStruct_FSkinVariationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSkinVariationData.InnerSingleton, Z_Construct_UScriptStruct_FSkinVariationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSkinVariationData.InnerSingleton;
}
// ********** End ScriptStruct FSkinVariationData **************************************************

// ********** Begin ScriptStruct FTattooData *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTattooData;
class UScriptStruct* FTattooData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTattooData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTattooData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTattooData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("TattooData"));
	}
	return Z_Registration_Info_UScriptStruct_FTattooData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTattooData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TattooName_MetaData[] = {
		{ "Category", "Tattoo" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TattooPath_MetaData[] = {
		{ "Category", "Tattoo" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineWidth_MetaData[] = {
		{ "Category", "Tattoo" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TattooColor_MetaData[] = {
		{ "Category", "Tattoo" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Opacity_MetaData[] = {
		{ "Category", "Tattoo" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlurRadius_MetaData[] = {
		{ "Category", "Tattoo" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TattooName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TattooPath_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TattooPath;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LineWidth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TattooColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Opacity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlurRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTattooData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooName = { "TattooName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTattooData, TattooName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TattooName_MetaData), NewProp_TattooName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooPath_Inner = { "TattooPath", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooPath = { "TattooPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTattooData, TattooPath), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TattooPath_MetaData), NewProp_TattooPath_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_LineWidth = { "LineWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTattooData, LineWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineWidth_MetaData), NewProp_LineWidth_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooColor = { "TattooColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTattooData, TattooColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TattooColor_MetaData), NewProp_TattooColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_Opacity = { "Opacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTattooData, Opacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Opacity_MetaData), NewProp_Opacity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_BlurRadius = { "BlurRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTattooData, BlurRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlurRadius_MetaData), NewProp_BlurRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTattooData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooPath_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_LineWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_TattooColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_Opacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTattooData_Statics::NewProp_BlurRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTattooData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTattooData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"TattooData",
	Z_Construct_UScriptStruct_FTattooData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTattooData_Statics::PropPointers),
	sizeof(FTattooData),
	alignof(FTattooData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTattooData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTattooData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTattooData()
{
	if (!Z_Registration_Info_UScriptStruct_FTattooData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTattooData.InnerSingleton, Z_Construct_UScriptStruct_FTattooData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTattooData.InnerSingleton;
}
// ********** End ScriptStruct FTattooData *********************************************************

// ********** Begin ScriptStruct FScarData *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FScarData;
class UScriptStruct* FScarData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FScarData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FScarData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FScarData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ScarData"));
	}
	return Z_Registration_Info_UScriptStruct_FScarData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FScarData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScarPath_MetaData[] = {
		{ "Category", "Scar" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScarWidth_MetaData[] = {
		{ "Category", "Scar" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScarDepth_MetaData[] = {
		{ "Category", "Scar" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScarColor_MetaData[] = {
		{ "Category", "Scar" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Roughness_MetaData[] = {
		{ "Category", "Scar" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EdgeSoftness_MetaData[] = {
		{ "Category", "Scar" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScarPath_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ScarPath;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScarWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScarDepth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScarColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Roughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EdgeSoftness;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FScarData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarPath_Inner = { "ScarPath", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarPath = { "ScarPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FScarData, ScarPath), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScarPath_MetaData), NewProp_ScarPath_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarWidth = { "ScarWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FScarData, ScarWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScarWidth_MetaData), NewProp_ScarWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarDepth = { "ScarDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FScarData, ScarDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScarDepth_MetaData), NewProp_ScarDepth_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarColor = { "ScarColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FScarData, ScarColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScarColor_MetaData), NewProp_ScarColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FScarData_Statics::NewProp_Roughness = { "Roughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FScarData, Roughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Roughness_MetaData), NewProp_Roughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FScarData_Statics::NewProp_EdgeSoftness = { "EdgeSoftness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FScarData, EdgeSoftness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EdgeSoftness_MetaData), NewProp_EdgeSoftness_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FScarData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarPath_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FScarData_Statics::NewProp_ScarColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FScarData_Statics::NewProp_Roughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FScarData_Statics::NewProp_EdgeSoftness,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FScarData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FScarData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"ScarData",
	Z_Construct_UScriptStruct_FScarData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FScarData_Statics::PropPointers),
	sizeof(FScarData),
	alignof(FScarData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FScarData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FScarData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FScarData()
{
	if (!Z_Registration_Info_UScriptStruct_FScarData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FScarData.InnerSingleton, Z_Construct_UScriptStruct_FScarData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FScarData.InnerSingleton;
}
// ********** End ScriptStruct FScarData ***********************************************************

// ********** Begin ScriptStruct FMakeupData *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMakeupData;
class UScriptStruct* FMakeupData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMakeupData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMakeupData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMakeupData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("MakeupData"));
	}
	return Z_Registration_Info_UScriptStruct_FMakeupData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMakeupData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LipstickColor_MetaData[] = {
		{ "Category", "Makeup" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LipstickIntensity_MetaData[] = {
		{ "Category", "Makeup" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeshadowColor_MetaData[] = {
		{ "Category", "Makeup" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeshadowIntensity_MetaData[] = {
		{ "Category", "Makeup" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlushColor_MetaData[] = {
		{ "Category", "Makeup" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlushIntensity_MetaData[] = {
		{ "Category", "Makeup" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoundationCoverage_MetaData[] = {
		{ "Category", "Makeup" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LipstickColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LipstickIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EyeshadowColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EyeshadowIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlushColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlushIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FoundationCoverage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMakeupData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_LipstickColor = { "LipstickColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMakeupData, LipstickColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LipstickColor_MetaData), NewProp_LipstickColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_LipstickIntensity = { "LipstickIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMakeupData, LipstickIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LipstickIntensity_MetaData), NewProp_LipstickIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_EyeshadowColor = { "EyeshadowColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMakeupData, EyeshadowColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeshadowColor_MetaData), NewProp_EyeshadowColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_EyeshadowIntensity = { "EyeshadowIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMakeupData, EyeshadowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeshadowIntensity_MetaData), NewProp_EyeshadowIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_BlushColor = { "BlushColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMakeupData, BlushColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlushColor_MetaData), NewProp_BlushColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_BlushIntensity = { "BlushIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMakeupData, BlushIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlushIntensity_MetaData), NewProp_BlushIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_FoundationCoverage = { "FoundationCoverage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMakeupData, FoundationCoverage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoundationCoverage_MetaData), NewProp_FoundationCoverage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMakeupData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_LipstickColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_LipstickIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_EyeshadowColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_EyeshadowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_BlushColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_BlushIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMakeupData_Statics::NewProp_FoundationCoverage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMakeupData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMakeupData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"MakeupData",
	Z_Construct_UScriptStruct_FMakeupData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMakeupData_Statics::PropPointers),
	sizeof(FMakeupData),
	alignof(FMakeupData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMakeupData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMakeupData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMakeupData()
{
	if (!Z_Registration_Info_UScriptStruct_FMakeupData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMakeupData.InnerSingleton, Z_Construct_UScriptStruct_FMakeupData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMakeupData.InnerSingleton;
}
// ********** End ScriptStruct FMakeupData *********************************************************

// ********** Begin ScriptStruct FAgingEffectData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAgingEffectData;
class UScriptStruct* FAgingEffectData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAgingEffectData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAgingEffectData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAgingEffectData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("AgingEffectData"));
	}
	return Z_Registration_Info_UScriptStruct_FAgingEffectData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAgingEffectData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WrinkleIntensity_MetaData[] = {
		{ "Category", "Aging" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgeSpotIntensity_MetaData[] = {
		{ "Category", "Aging" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkinSaggingIntensity_MetaData[] = {
		{ "Category", "Aging" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariationIntensity_MetaData[] = {
		{ "Category", "Aging" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgeYears_MetaData[] = {
		{ "Category", "Aging" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WrinkleIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AgeSpotIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SkinSaggingIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorVariationIntensity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AgeYears;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAgingEffectData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_WrinkleIntensity = { "WrinkleIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAgingEffectData, WrinkleIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WrinkleIntensity_MetaData), NewProp_WrinkleIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_AgeSpotIntensity = { "AgeSpotIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAgingEffectData, AgeSpotIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgeSpotIntensity_MetaData), NewProp_AgeSpotIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_SkinSaggingIntensity = { "SkinSaggingIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAgingEffectData, SkinSaggingIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkinSaggingIntensity_MetaData), NewProp_SkinSaggingIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_ColorVariationIntensity = { "ColorVariationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAgingEffectData, ColorVariationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariationIntensity_MetaData), NewProp_ColorVariationIntensity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_AgeYears = { "AgeYears", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAgingEffectData, AgeYears), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgeYears_MetaData), NewProp_AgeYears_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAgingEffectData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_WrinkleIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_AgeSpotIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_SkinSaggingIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_ColorVariationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewProp_AgeYears,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAgingEffectData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAgingEffectData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"AgingEffectData",
	Z_Construct_UScriptStruct_FAgingEffectData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAgingEffectData_Statics::PropPointers),
	sizeof(FAgingEffectData),
	alignof(FAgingEffectData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAgingEffectData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAgingEffectData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAgingEffectData()
{
	if (!Z_Registration_Info_UScriptStruct_FAgingEffectData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAgingEffectData.InnerSingleton, Z_Construct_UScriptStruct_FAgingEffectData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAgingEffectData.InnerSingleton;
}
// ********** End ScriptStruct FAgingEffectData ****************************************************

// ********** Begin ScriptStruct FTextureGenerationParameters **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTextureGenerationParameters;
class UScriptStruct* FTextureGenerationParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTextureGenerationParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTextureGenerationParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTextureGenerationParameters, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("TextureGenerationParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FTextureGenerationParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureType_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomResolution_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkinVariation_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tattoos_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scars_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Makeup_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgingEffects_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateMipmaps_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSRGB_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Seed_MetaData[] = {
		{ "Category", "Texture Generation" },
		{ "ModuleRelativePath", "Public/AuracronTextureGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TextureType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TextureType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomResolution;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SkinVariation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tattoos_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tattoos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scars_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Scars;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Makeup;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AgingEffects;
	static void NewProp_bGenerateMipmaps_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateMipmaps;
	static void NewProp_bSRGB_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSRGB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTextureGenerationParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_TextureType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_TextureType = { "TextureType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, TextureType), Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronTextureType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureType_MetaData), NewProp_TextureType_MetaData) }; // 3058260838
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, Quality), Z_Construct_UEnum_AuracronMetaHumanBridge_ETextureQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 3122628518
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_CustomResolution = { "CustomResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, CustomResolution), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomResolution_MetaData), NewProp_CustomResolution_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_SkinVariation = { "SkinVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, SkinVariation), Z_Construct_UScriptStruct_FSkinVariationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkinVariation_MetaData), NewProp_SkinVariation_MetaData) }; // 853883337
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Tattoos_Inner = { "Tattoos", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTattooData, METADATA_PARAMS(0, nullptr) }; // 4223074943
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Tattoos = { "Tattoos", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, Tattoos), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tattoos_MetaData), NewProp_Tattoos_MetaData) }; // 4223074943
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Scars_Inner = { "Scars", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FScarData, METADATA_PARAMS(0, nullptr) }; // 3581142439
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Scars = { "Scars", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, Scars), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scars_MetaData), NewProp_Scars_MetaData) }; // 3581142439
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Makeup = { "Makeup", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, Makeup), Z_Construct_UScriptStruct_FMakeupData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Makeup_MetaData), NewProp_Makeup_MetaData) }; // 3351202104
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_AgingEffects = { "AgingEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, AgingEffects), Z_Construct_UScriptStruct_FAgingEffectData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgingEffects_MetaData), NewProp_AgingEffects_MetaData) }; // 2598147273
void Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bGenerateMipmaps_SetBit(void* Obj)
{
	((FTextureGenerationParameters*)Obj)->bGenerateMipmaps = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bGenerateMipmaps = { "bGenerateMipmaps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTextureGenerationParameters), &Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bGenerateMipmaps_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateMipmaps_MetaData), NewProp_bGenerateMipmaps_MetaData) };
void Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bSRGB_SetBit(void* Obj)
{
	((FTextureGenerationParameters*)Obj)->bSRGB = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bSRGB = { "bSRGB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTextureGenerationParameters), &Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bSRGB_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSRGB_MetaData), NewProp_bSRGB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTextureGenerationParameters, Seed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Seed_MetaData), NewProp_Seed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_TextureType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_TextureType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_CustomResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_SkinVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Tattoos_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Tattoos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Scars_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Scars,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Makeup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_AgingEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bGenerateMipmaps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_bSRGB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewProp_Seed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"TextureGenerationParameters",
	Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::PropPointers),
	sizeof(FTextureGenerationParameters),
	alignof(FTextureGenerationParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTextureGenerationParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FTextureGenerationParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTextureGenerationParameters.InnerSingleton, Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTextureGenerationParameters.InnerSingleton;
}
// ********** End ScriptStruct FTextureGenerationParameters ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ETextureQuality_StaticEnum, TEXT("ETextureQuality"), &Z_Registration_Info_UEnum_ETextureQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3122628518U) },
		{ EAuracronTextureType_StaticEnum, TEXT("EAuracronTextureType"), &Z_Registration_Info_UEnum_EAuracronTextureType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3058260838U) },
		{ ENoiseType_StaticEnum, TEXT("ENoiseType"), &Z_Registration_Info_UEnum_ENoiseType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 141586324U) },
		{ ETextureBlendMode_StaticEnum, TEXT("ETextureBlendMode"), &Z_Registration_Info_UEnum_ETextureBlendMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3524559248U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FNoiseParameters::StaticStruct, Z_Construct_UScriptStruct_FNoiseParameters_Statics::NewStructOps, TEXT("NoiseParameters"), &Z_Registration_Info_UScriptStruct_FNoiseParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FNoiseParameters), 3449342282U) },
		{ FSkinVariationData::StaticStruct, Z_Construct_UScriptStruct_FSkinVariationData_Statics::NewStructOps, TEXT("SkinVariationData"), &Z_Registration_Info_UScriptStruct_FSkinVariationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSkinVariationData), 853883337U) },
		{ FTattooData::StaticStruct, Z_Construct_UScriptStruct_FTattooData_Statics::NewStructOps, TEXT("TattooData"), &Z_Registration_Info_UScriptStruct_FTattooData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTattooData), 4223074943U) },
		{ FScarData::StaticStruct, Z_Construct_UScriptStruct_FScarData_Statics::NewStructOps, TEXT("ScarData"), &Z_Registration_Info_UScriptStruct_FScarData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FScarData), 3581142439U) },
		{ FMakeupData::StaticStruct, Z_Construct_UScriptStruct_FMakeupData_Statics::NewStructOps, TEXT("MakeupData"), &Z_Registration_Info_UScriptStruct_FMakeupData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMakeupData), 3351202104U) },
		{ FAgingEffectData::StaticStruct, Z_Construct_UScriptStruct_FAgingEffectData_Statics::NewStructOps, TEXT("AgingEffectData"), &Z_Registration_Info_UScriptStruct_FAgingEffectData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAgingEffectData), 2598147273U) },
		{ FTextureGenerationParameters::StaticStruct, Z_Construct_UScriptStruct_FTextureGenerationParameters_Statics::NewStructOps, TEXT("TextureGenerationParameters"), &Z_Registration_Info_UScriptStruct_FTextureGenerationParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTextureGenerationParameters), 3069624430U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h__Script_AuracronMetaHumanBridge_1632904495(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronTextureGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
