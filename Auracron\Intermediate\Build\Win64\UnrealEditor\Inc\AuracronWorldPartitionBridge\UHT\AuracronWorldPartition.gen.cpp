// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartition.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartition() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLogger();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLogger_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCellInfo();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingStatistics();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UDataLayerSubsystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UEngineSubsystem();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldPartition_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldPartitionSubsystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronWorldPartitionState **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronWorldPartitionState;
static UEnum* EAuracronWorldPartitionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronWorldPartitionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronWorldPartitionState"));
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionState>()
{
	return EAuracronWorldPartitionState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World partition states\n" },
#endif
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronWorldPartitionState::Error" },
		{ "Initializing.DisplayName", "Initializing" },
		{ "Initializing.Name", "EAuracronWorldPartitionState::Initializing" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
		{ "Ready.DisplayName", "Ready" },
		{ "Ready.Name", "EAuracronWorldPartitionState::Ready" },
		{ "Shutdown.DisplayName", "Shutdown" },
		{ "Shutdown.Name", "EAuracronWorldPartitionState::Shutdown" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EAuracronWorldPartitionState::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World partition states" },
#endif
		{ "Uninitialized.DisplayName", "Uninitialized" },
		{ "Uninitialized.Name", "EAuracronWorldPartitionState::Uninitialized" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronWorldPartitionState::Uninitialized", (int64)EAuracronWorldPartitionState::Uninitialized },
		{ "EAuracronWorldPartitionState::Initializing", (int64)EAuracronWorldPartitionState::Initializing },
		{ "EAuracronWorldPartitionState::Ready", (int64)EAuracronWorldPartitionState::Ready },
		{ "EAuracronWorldPartitionState::Streaming", (int64)EAuracronWorldPartitionState::Streaming },
		{ "EAuracronWorldPartitionState::Error", (int64)EAuracronWorldPartitionState::Error },
		{ "EAuracronWorldPartitionState::Shutdown", (int64)EAuracronWorldPartitionState::Shutdown },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronWorldPartitionState",
	"EAuracronWorldPartitionState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronWorldPartitionState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionState.InnerSingleton;
}
// ********** End Enum EAuracronWorldPartitionState ************************************************

// ********** Begin Enum EAuracronStreamingState ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronStreamingState;
static UEnum* EAuracronStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingState>()
{
	return EAuracronStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming states" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronStreamingState::Unloaded", (int64)EAuracronStreamingState::Unloaded },
		{ "EAuracronStreamingState::Loading", (int64)EAuracronStreamingState::Loading },
		{ "EAuracronStreamingState::Loaded", (int64)EAuracronStreamingState::Loaded },
		{ "EAuracronStreamingState::Unloading", (int64)EAuracronStreamingState::Unloading },
		{ "EAuracronStreamingState::Failed", (int64)EAuracronStreamingState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronStreamingState",
	"EAuracronStreamingState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronStreamingState *****************************************************

// ********** Begin Enum EAuracronCellType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCellType;
static UEnum* EAuracronCellType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCellType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCellType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronCellType"));
	}
	return Z_Registration_Info_UEnum_EAuracronCellType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCellType>()
{
	return EAuracronCellType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AlwaysLoaded.DisplayName", "Always Loaded" },
		{ "AlwaysLoaded.Name", "EAuracronCellType::AlwaysLoaded" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell types\n" },
#endif
		{ "Dynamic.DisplayName", "Dynamic" },
		{ "Dynamic.Name", "EAuracronCellType::Dynamic" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
		{ "RuntimeGenerated.DisplayName", "Runtime Generated" },
		{ "RuntimeGenerated.Name", "EAuracronCellType::RuntimeGenerated" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronCellType::Static" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EAuracronCellType::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCellType::Static", (int64)EAuracronCellType::Static },
		{ "EAuracronCellType::Dynamic", (int64)EAuracronCellType::Dynamic },
		{ "EAuracronCellType::Streaming", (int64)EAuracronCellType::Streaming },
		{ "EAuracronCellType::AlwaysLoaded", (int64)EAuracronCellType::AlwaysLoaded },
		{ "EAuracronCellType::RuntimeGenerated", (int64)EAuracronCellType::RuntimeGenerated },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronCellType",
	"EAuracronCellType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType()
{
	if (!Z_Registration_Info_UEnum_EAuracronCellType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCellType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCellType.InnerSingleton;
}
// ********** End Enum EAuracronCellType ***********************************************************

// ********** Begin Enum EAuracronWorldPartitionLogLevel *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel;
static UEnum* EAuracronWorldPartitionLogLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronWorldPartitionLogLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionLogLevel>()
{
	return EAuracronWorldPartitionLogLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Log levels for World Partition\n" },
#endif
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronWorldPartitionLogLevel::Error" },
		{ "Log.DisplayName", "Log" },
		{ "Log.Name", "EAuracronWorldPartitionLogLevel::Log" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronWorldPartitionLogLevel::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Log levels for World Partition" },
#endif
		{ "Verbose.DisplayName", "Verbose" },
		{ "Verbose.Name", "EAuracronWorldPartitionLogLevel::Verbose" },
		{ "VeryVerbose.DisplayName", "Very Verbose" },
		{ "VeryVerbose.Name", "EAuracronWorldPartitionLogLevel::VeryVerbose" },
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EAuracronWorldPartitionLogLevel::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronWorldPartitionLogLevel::None", (int64)EAuracronWorldPartitionLogLevel::None },
		{ "EAuracronWorldPartitionLogLevel::Error", (int64)EAuracronWorldPartitionLogLevel::Error },
		{ "EAuracronWorldPartitionLogLevel::Warning", (int64)EAuracronWorldPartitionLogLevel::Warning },
		{ "EAuracronWorldPartitionLogLevel::Log", (int64)EAuracronWorldPartitionLogLevel::Log },
		{ "EAuracronWorldPartitionLogLevel::Verbose", (int64)EAuracronWorldPartitionLogLevel::Verbose },
		{ "EAuracronWorldPartitionLogLevel::VeryVerbose", (int64)EAuracronWorldPartitionLogLevel::VeryVerbose },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronWorldPartitionLogLevel",
	"EAuracronWorldPartitionLogLevel",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel.InnerSingleton;
}
// ********** End Enum EAuracronWorldPartitionLogLevel *********************************************

// ********** Begin ScriptStruct FAuracronWorldPartitionConfiguration ******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration;
class UScriptStruct* FAuracronWorldPartitionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronWorldPartitionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Configuration\n * Configuration settings for large world management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Configuration\nConfiguration settings for large world management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWorldPartition_MetaData[] = {
		{ "Category", "World Partition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreaming_MetaData[] = {
		{ "Category", "World Partition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDataLayers_MetaData[] = {
		{ "Category", "World Partition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentStreamingRequests_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultGridSize_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinGridSize_MetaData[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 256m cells\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "256m cells" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGridSize_MetaData[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 64m minimum\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "64m minimum" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncLoading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 1024m maximum\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "1024m maximum" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLevelStreaming_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPoolSize_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// MB\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogStreamingOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LogLevel_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableWorldPartition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWorldPartition;
	static void NewProp_bEnableStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreaming;
	static void NewProp_bEnableDataLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDataLayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentStreamingRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DefaultGridSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinGridSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxGridSize;
	static void NewProp_bEnableAsyncLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncLoading;
	static void NewProp_bEnableLevelStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLevelStreaming;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingPoolSize;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bLogStreamingOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogStreamingOperations;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LogLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LogLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronWorldPartitionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableWorldPartition_SetBit(void* Obj)
{
	((FAuracronWorldPartitionConfiguration*)Obj)->bEnableWorldPartition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableWorldPartition = { "bEnableWorldPartition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableWorldPartition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWorldPartition_MetaData), NewProp_bEnableWorldPartition_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableStreaming_SetBit(void* Obj)
{
	((FAuracronWorldPartitionConfiguration*)Obj)->bEnableStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableStreaming = { "bEnableStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreaming_MetaData), NewProp_bEnableStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDataLayers_SetBit(void* Obj)
{
	((FAuracronWorldPartitionConfiguration*)Obj)->bEnableDataLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDataLayers = { "bEnableDataLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDataLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDataLayers_MetaData), NewProp_bEnableDataLayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_StreamingDistance = { "StreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, StreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingDistance_MetaData), NewProp_StreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_UnloadingDistance = { "UnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, UnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadingDistance_MetaData), NewProp_UnloadingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_MaxConcurrentStreamingRequests = { "MaxConcurrentStreamingRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, MaxConcurrentStreamingRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentStreamingRequests_MetaData), NewProp_MaxConcurrentStreamingRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_DefaultGridSize = { "DefaultGridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, DefaultGridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultGridSize_MetaData), NewProp_DefaultGridSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_MinGridSize = { "MinGridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, MinGridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinGridSize_MetaData), NewProp_MinGridSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_MaxGridSize = { "MaxGridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, MaxGridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGridSize_MetaData), NewProp_MaxGridSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableAsyncLoading_SetBit(void* Obj)
{
	((FAuracronWorldPartitionConfiguration*)Obj)->bEnableAsyncLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableAsyncLoading = { "bEnableAsyncLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableAsyncLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncLoading_MetaData), NewProp_bEnableAsyncLoading_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableLevelStreaming_SetBit(void* Obj)
{
	((FAuracronWorldPartitionConfiguration*)Obj)->bEnableLevelStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableLevelStreaming = { "bEnableLevelStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableLevelStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLevelStreaming_MetaData), NewProp_bEnableLevelStreaming_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_StreamingPoolSize = { "StreamingPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, StreamingPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPoolSize_MetaData), NewProp_StreamingPoolSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FAuracronWorldPartitionConfiguration*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bLogStreamingOperations_SetBit(void* Obj)
{
	((FAuracronWorldPartitionConfiguration*)Obj)->bLogStreamingOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bLogStreamingOperations = { "bLogStreamingOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bLogStreamingOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogStreamingOperations_MetaData), NewProp_bLogStreamingOperations_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_LogLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_LogLevel = { "LogLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionConfiguration, LogLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LogLevel_MetaData), NewProp_LogLevel_MetaData) }; // 175707838
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableWorldPartition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDataLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_StreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_UnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_MaxConcurrentStreamingRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_DefaultGridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_MinGridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_MaxGridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableAsyncLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableLevelStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_StreamingPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_bLogStreamingOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_LogLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewProp_LogLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronWorldPartitionConfiguration",
	Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::PropPointers),
	sizeof(FAuracronWorldPartitionConfiguration),
	alignof(FAuracronWorldPartitionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronWorldPartitionConfiguration ********************************

// ********** Begin ScriptStruct FAuracronCellInfo *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCellInfo;
class UScriptStruct* FAuracronCellInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCellInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCellInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCellInfo, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronCellInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCellInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCellInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Cell Information\n * Information about a world partition cell\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell Information\nInformation about a world partition cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellBounds_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellType_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorCount_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingTime_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayers_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellBounds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CellType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CellType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActorCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DataLayers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCellInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, CellCoordinates), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellBounds = { "CellBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, CellBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellBounds_MetaData), NewProp_CellBounds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellType = { "CellType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, CellType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellType_MetaData), NewProp_CellType_MetaData) }; // 2616550595
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 1699613073
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_ActorCount = { "ActorCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, ActorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorCount_MetaData), NewProp_ActorCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_LoadingTime = { "LoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, LoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingTime_MetaData), NewProp_LoadingTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_DataLayers_Inner = { "DataLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_DataLayers = { "DataLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, DataLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayers_MetaData), NewProp_DataLayers_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCellInfo, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_CellType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_ActorCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_LoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_DataLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_DataLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewProp_LastAccessTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronCellInfo",
	Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::PropPointers),
	sizeof(FAuracronCellInfo),
	alignof(FAuracronCellInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCellInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCellInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCellInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCellInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCellInfo ***************************************************

// ********** Begin ScriptStruct FAuracronStreamingStatistics **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics;
class UScriptStruct* FAuracronStreamingStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStreamingStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronStreamingStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Streaming Statistics\n * Performance and usage statistics for world partition streaming\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming Statistics\nPerformance and usage statistics for world partition streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalCells_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedCells_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingCells_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingRequests_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedRequests_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalCells;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedCells;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingCells;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedRequests;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStreamingStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_TotalCells = { "TotalCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, TotalCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalCells_MetaData), NewProp_TotalCells_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_LoadedCells = { "LoadedCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, LoadedCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedCells_MetaData), NewProp_LoadedCells_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_StreamingCells = { "StreamingCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, StreamingCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingCells_MetaData), NewProp_StreamingCells_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_AverageLoadingTime = { "AverageLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, AverageLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadingTime_MetaData), NewProp_AverageLoadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_StreamingRequests = { "StreamingRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, StreamingRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingRequests_MetaData), NewProp_StreamingRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_FailedRequests = { "FailedRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, FailedRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedRequests_MetaData), NewProp_FailedRequests_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_StreamingEfficiency = { "StreamingEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, StreamingEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingEfficiency_MetaData), NewProp_StreamingEfficiency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_TotalCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_LoadedCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_StreamingCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_AverageLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_StreamingRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_FailedRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_StreamingEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronStreamingStatistics",
	Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::PropPointers),
	sizeof(FAuracronStreamingStatistics),
	alignof(FAuracronStreamingStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStreamingStatistics ****************************************

// ********** Begin Class UAuracronWorldPartitionLogger Function ClearLogs *************************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_ClearLogs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Log management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Log management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_ClearLogs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "ClearLogs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_ClearLogs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_ClearLogs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_ClearLogs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_ClearLogs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execClearLogs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearLogs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function ClearLogs ***************************

// ********** Begin Class UAuracronWorldPartitionLogger Function EnableCategoryLogging *************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics
{
	struct AuracronWorldPartitionLogger_eventEnableCategoryLogging_Parms
	{
		FString Category;
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventEnableCategoryLogging_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionLogger_eventEnableCategoryLogging_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLogger_eventEnableCategoryLogging_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "EnableCategoryLogging", Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::AuracronWorldPartitionLogger_eventEnableCategoryLogging_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::AuracronWorldPartitionLogger_eventEnableCategoryLogging_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execEnableCategoryLogging)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableCategoryLogging(Z_Param_Category,Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function EnableCategoryLogging ***************

// ********** Begin Class UAuracronWorldPartitionLogger Function GetInstance ***********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics
{
	struct AuracronWorldPartitionLogger_eventGetInstance_Parms
	{
		UAuracronWorldPartitionLogger* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionLogger_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::AuracronWorldPartitionLogger_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::AuracronWorldPartitionLogger_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionLogger**)Z_Param__Result=UAuracronWorldPartitionLogger::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function GetInstance *************************

// ********** Begin Class UAuracronWorldPartitionLogger Function GetLogLevel ***********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics
{
	struct AuracronWorldPartitionLogger_eventGetLogLevel_Parms
	{
		EAuracronWorldPartitionLogLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventGetLogLevel_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel, METADATA_PARAMS(0, nullptr) }; // 175707838
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "GetLogLevel", Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::AuracronWorldPartitionLogger_eventGetLogLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::AuracronWorldPartitionLogger_eventGetLogLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execGetLogLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronWorldPartitionLogLevel*)Z_Param__Result=P_THIS->GetLogLevel();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function GetLogLevel *************************

// ********** Begin Class UAuracronWorldPartitionLogger Function GetRecentLogs *********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics
{
	struct AuracronWorldPartitionLogger_eventGetRecentLogs_Parms
	{
		int32 MaxCount;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "CPP_Default_MaxCount", "100" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::NewProp_MaxCount = { "MaxCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventGetRecentLogs_Parms, MaxCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventGetRecentLogs_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::NewProp_MaxCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "GetRecentLogs", Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::AuracronWorldPartitionLogger_eventGetRecentLogs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::AuracronWorldPartitionLogger_eventGetRecentLogs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execGetRecentLogs)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetRecentLogs(Z_Param_MaxCount);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function GetRecentLogs ***********************

// ********** Begin Class UAuracronWorldPartitionLogger Function IsCategoryEnabled *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics
{
	struct AuracronWorldPartitionLogger_eventIsCategoryEnabled_Parms
	{
		FString Category;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventIsCategoryEnabled_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLogger_eventIsCategoryEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLogger_eventIsCategoryEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "IsCategoryEnabled", Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::AuracronWorldPartitionLogger_eventIsCategoryEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::AuracronWorldPartitionLogger_eventIsCategoryEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execIsCategoryEnabled)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCategoryEnabled(Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function IsCategoryEnabled *******************

// ********** Begin Class UAuracronWorldPartitionLogger Function LogError **************************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics
{
	struct AuracronWorldPartitionLogger_eventLogError_Parms
	{
		FString Message;
		FString Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "CPP_Default_Category", "WorldPartition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogError_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogError_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "LogError", Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::AuracronWorldPartitionLogger_eventLogError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::AuracronWorldPartitionLogger_eventLogError_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execLogError)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogError(Z_Param_Message,Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function LogError ****************************

// ********** Begin Class UAuracronWorldPartitionLogger Function LogInfo ***************************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics
{
	struct AuracronWorldPartitionLogger_eventLogInfo_Parms
	{
		FString Message;
		FString Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "CPP_Default_Category", "WorldPartition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogInfo_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogInfo_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "LogInfo", Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::AuracronWorldPartitionLogger_eventLogInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::AuracronWorldPartitionLogger_eventLogInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execLogInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogInfo(Z_Param_Message,Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function LogInfo *****************************

// ********** Begin Class UAuracronWorldPartitionLogger Function LogMessage ************************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics
{
	struct AuracronWorldPartitionLogger_eventLogMessage_Parms
	{
		EAuracronWorldPartitionLogLevel Level;
		FString Message;
		FString Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Logging functions\n" },
#endif
		{ "CPP_Default_Category", "WorldPartition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Logging functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Level_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Level;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Level_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogMessage_Parms, Level), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel, METADATA_PARAMS(0, nullptr) }; // 175707838
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogMessage_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogMessage_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Level_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "LogMessage", Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::AuracronWorldPartitionLogger_eventLogMessage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::AuracronWorldPartitionLogger_eventLogMessage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execLogMessage)
{
	P_GET_ENUM(EAuracronWorldPartitionLogLevel,Z_Param_Level);
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogMessage(EAuracronWorldPartitionLogLevel(Z_Param_Level),Z_Param_Message,Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function LogMessage **************************

// ********** Begin Class UAuracronWorldPartitionLogger Function LogVerbose ************************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics
{
	struct AuracronWorldPartitionLogger_eventLogVerbose_Parms
	{
		FString Message;
		FString Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "CPP_Default_Category", "WorldPartition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogVerbose_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogVerbose_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "LogVerbose", Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::AuracronWorldPartitionLogger_eventLogVerbose_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::AuracronWorldPartitionLogger_eventLogVerbose_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execLogVerbose)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogVerbose(Z_Param_Message,Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function LogVerbose **************************

// ********** Begin Class UAuracronWorldPartitionLogger Function LogWarning ************************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics
{
	struct AuracronWorldPartitionLogger_eventLogWarning_Parms
	{
		FString Message;
		FString Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "CPP_Default_Category", "WorldPartition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogWarning_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventLogWarning_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "LogWarning", Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::AuracronWorldPartitionLogger_eventLogWarning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::AuracronWorldPartitionLogger_eventLogWarning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execLogWarning)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogWarning(Z_Param_Message,Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function LogWarning **************************

// ********** Begin Class UAuracronWorldPartitionLogger Function SaveLogsToFile ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics
{
	struct AuracronWorldPartitionLogger_eventSaveLogsToFile_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventSaveLogsToFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "SaveLogsToFile", Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::AuracronWorldPartitionLogger_eventSaveLogsToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::AuracronWorldPartitionLogger_eventSaveLogsToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execSaveLogsToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SaveLogsToFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function SaveLogsToFile **********************

// ********** Begin Class UAuracronWorldPartitionLogger Function SetLogLevel ***********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics
{
	struct AuracronWorldPartitionLogger_eventSetLogLevel_Parms
	{
		EAuracronWorldPartitionLogLevel Level;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Logger" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Level_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Level;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::NewProp_Level_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLogger_eventSetLogLevel_Parms, Level), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel, METADATA_PARAMS(0, nullptr) }; // 175707838
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::NewProp_Level_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::NewProp_Level,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLogger, nullptr, "SetLogLevel", Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::AuracronWorldPartitionLogger_eventSetLogLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::AuracronWorldPartitionLogger_eventSetLogLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLogger::execSetLogLevel)
{
	P_GET_ENUM(EAuracronWorldPartitionLogLevel,Z_Param_Level);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLogLevel(EAuracronWorldPartitionLogLevel(Z_Param_Level));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLogger Function SetLogLevel *************************

// ********** Begin Class UAuracronWorldPartitionLogger ********************************************
void UAuracronWorldPartitionLogger::StaticRegisterNativesUAuracronWorldPartitionLogger()
{
	UClass* Class = UAuracronWorldPartitionLogger::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearLogs", &UAuracronWorldPartitionLogger::execClearLogs },
		{ "EnableCategoryLogging", &UAuracronWorldPartitionLogger::execEnableCategoryLogging },
		{ "GetInstance", &UAuracronWorldPartitionLogger::execGetInstance },
		{ "GetLogLevel", &UAuracronWorldPartitionLogger::execGetLogLevel },
		{ "GetRecentLogs", &UAuracronWorldPartitionLogger::execGetRecentLogs },
		{ "IsCategoryEnabled", &UAuracronWorldPartitionLogger::execIsCategoryEnabled },
		{ "LogError", &UAuracronWorldPartitionLogger::execLogError },
		{ "LogInfo", &UAuracronWorldPartitionLogger::execLogInfo },
		{ "LogMessage", &UAuracronWorldPartitionLogger::execLogMessage },
		{ "LogVerbose", &UAuracronWorldPartitionLogger::execLogVerbose },
		{ "LogWarning", &UAuracronWorldPartitionLogger::execLogWarning },
		{ "SaveLogsToFile", &UAuracronWorldPartitionLogger::execSaveLogsToFile },
		{ "SetLogLevel", &UAuracronWorldPartitionLogger::execSetLogLevel },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionLogger;
UClass* UAuracronWorldPartitionLogger::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionLogger;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLogger.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionLogger"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionLogger.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionLogger,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLogger.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionLogger_NoRegister()
{
	return UAuracronWorldPartitionLogger::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Logger\n * Specialized logging system for world partition operations\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartition.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Logger\nSpecialized logging system for world partition operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLogLevel_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnabledCategories_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LogHistory_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentLogLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentLogLevel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnabledCategories_ElementProp;
	static const UECodeGen_Private::FSetPropertyParams NewProp_EnabledCategories;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LogHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LogHistory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_ClearLogs, "ClearLogs" }, // 2476552111
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_EnableCategoryLogging, "EnableCategoryLogging" }, // 2246501645
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetInstance, "GetInstance" }, // 3464775059
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetLogLevel, "GetLogLevel" }, // 2926806804
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_GetRecentLogs, "GetRecentLogs" }, // 1637169822
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_IsCategoryEnabled, "IsCategoryEnabled" }, // 868614079
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogError, "LogError" }, // 2598169726
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogInfo, "LogInfo" }, // 3733393220
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogMessage, "LogMessage" }, // 3464497983
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogVerbose, "LogVerbose" }, // 1211441812
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_LogWarning, "LogWarning" }, // 4034588805
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_SaveLogsToFile, "SaveLogsToFile" }, // 685704773
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLogger_SetLogLevel, "SetLogLevel" }, // 2385078001
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionLogger>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_CurrentLogLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_CurrentLogLevel = { "CurrentLogLevel", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLogger, CurrentLogLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLogLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLogLevel_MetaData), NewProp_CurrentLogLevel_MetaData) }; // 175707838
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_EnabledCategories_ElementProp = { "EnabledCategories", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FSetPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_EnabledCategories = { "EnabledCategories", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Set, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLogger, EnabledCategories), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnabledCategories_MetaData), NewProp_EnabledCategories_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_LogHistory_Inner = { "LogHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_LogHistory = { "LogHistory", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLogger, LogHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LogHistory_MetaData), NewProp_LogHistory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_CurrentLogLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_CurrentLogLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_EnabledCategories_ElementProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_EnabledCategories,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_LogHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::NewProp_LogHistory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::ClassParams = {
	&UAuracronWorldPartitionLogger::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionLogger()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLogger.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionLogger.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLogger.OuterSingleton;
}
UAuracronWorldPartitionLogger::UAuracronWorldPartitionLogger(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionLogger);
UAuracronWorldPartitionLogger::~UAuracronWorldPartitionLogger() {}
// ********** End Class UAuracronWorldPartitionLogger **********************************************

// ********** Begin Delegate FOnCellLoaded *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionManager_eventOnCellLoaded_Parms
	{
		FString CellId;
		float LoadingTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventOnCellLoaded_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::NewProp_LoadingTime = { "LoadingTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventOnCellLoaded_Parms, LoadingTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::NewProp_LoadingTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "OnCellLoaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::AuracronWorldPartitionManager_eventOnCellLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::AuracronWorldPartitionManager_eventOnCellLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionManager::FOnCellLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnCellLoaded, const FString& CellId, float LoadingTime)
{
	struct AuracronWorldPartitionManager_eventOnCellLoaded_Parms
	{
		FString CellId;
		float LoadingTime;
	};
	AuracronWorldPartitionManager_eventOnCellLoaded_Parms Parms;
	Parms.CellId=CellId;
	Parms.LoadingTime=LoadingTime;
	OnCellLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellLoaded ***********************************************************

// ********** Begin Delegate FOnCellUnloaded *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionManager_eventOnCellUnloaded_Parms
	{
		FString CellId;
		float UnloadingTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadingTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventOnCellUnloaded_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::NewProp_UnloadingTime = { "UnloadingTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventOnCellUnloaded_Parms, UnloadingTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::NewProp_UnloadingTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "OnCellUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::AuracronWorldPartitionManager_eventOnCellUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::AuracronWorldPartitionManager_eventOnCellUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionManager::FOnCellUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnCellUnloaded, const FString& CellId, float UnloadingTime)
{
	struct AuracronWorldPartitionManager_eventOnCellUnloaded_Parms
	{
		FString CellId;
		float UnloadingTime;
	};
	AuracronWorldPartitionManager_eventOnCellUnloaded_Parms Parms;
	Parms.CellId=CellId;
	Parms.UnloadingTime=UnloadingTime;
	OnCellUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellUnloaded *********************************************************

// ********** Begin Delegate FOnStreamingStateChanged **********************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionManager_eventOnStreamingStateChanged_Parms
	{
		EAuracronStreamingState OldState;
		EAuracronStreamingState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventOnStreamingStateChanged_Parms, OldState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState, METADATA_PARAMS(0, nullptr) }; // 1699613073
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventOnStreamingStateChanged_Parms, NewState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingState, METADATA_PARAMS(0, nullptr) }; // 1699613073
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "OnStreamingStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::AuracronWorldPartitionManager_eventOnStreamingStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::AuracronWorldPartitionManager_eventOnStreamingStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionManager::FOnStreamingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnStreamingStateChanged, EAuracronStreamingState OldState, EAuracronStreamingState NewState)
{
	struct AuracronWorldPartitionManager_eventOnStreamingStateChanged_Parms
	{
		EAuracronStreamingState OldState;
		EAuracronStreamingState NewState;
	};
	AuracronWorldPartitionManager_eventOnStreamingStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	OnStreamingStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnStreamingStateChanged ************************************************

// ********** Begin Class UAuracronWorldPartitionManager Function DrawDebugCells *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics
{
	struct AuracronWorldPartitionManager_eventDrawDebugCells_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventDrawDebugCells_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "DrawDebugCells", Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::AuracronWorldPartitionManager_eventDrawDebugCells_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::AuracronWorldPartitionManager_eventDrawDebugCells_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execDrawDebugCells)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugCells(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function DrawDebugCells *********************

// ********** Begin Class UAuracronWorldPartitionManager Function EnableDebugVisualization *********
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics
{
	struct AuracronWorldPartitionManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::AuracronWorldPartitionManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::AuracronWorldPartitionManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function EnableDebugVisualization ***********

// ********** Begin Class UAuracronWorldPartitionManager Function GetAllCells **********************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics
{
	struct AuracronWorldPartitionManager_eventGetAllCells_Parms
	{
		TArray<FAuracronCellInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCellInfo, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetAllCells_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetAllCells", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::AuracronWorldPartitionManager_eventGetAllCells_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::AuracronWorldPartitionManager_eventGetAllCells_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetAllCells)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCellInfo>*)Z_Param__Result=P_THIS->GetAllCells();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetAllCells ************************

// ********** Begin Class UAuracronWorldPartitionManager Function GetAvailableDataLayers ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics
{
	struct AuracronWorldPartitionManager_eventGetAvailableDataLayers_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetAvailableDataLayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetAvailableDataLayers", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::AuracronWorldPartitionManager_eventGetAvailableDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::AuracronWorldPartitionManager_eventGetAvailableDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetAvailableDataLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAvailableDataLayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetAvailableDataLayers *************

// ********** Begin Class UAuracronWorldPartitionManager Function GetCellInfo **********************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics
{
	struct AuracronWorldPartitionManager_eventGetCellInfo_Parms
	{
		FString CellId;
		FAuracronCellInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetCellInfo_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetCellInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCellInfo, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetCellInfo", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::AuracronWorldPartitionManager_eventGetCellInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::AuracronWorldPartitionManager_eventGetCellInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetCellInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCellInfo*)Z_Param__Result=P_THIS->GetCellInfo(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetCellInfo ************************

// ********** Begin Class UAuracronWorldPartitionManager Function GetCellsInRadius *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics
{
	struct AuracronWorldPartitionManager_eventGetCellsInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FAuracronCellInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetCellsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetCellsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCellInfo, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetCellsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetCellsInRadius", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::AuracronWorldPartitionManager_eventGetCellsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::AuracronWorldPartitionManager_eventGetCellsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetCellsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCellInfo>*)Z_Param__Result=P_THIS->GetCellsInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetCellsInRadius *******************

// ********** Begin Class UAuracronWorldPartitionManager Function GetConfiguration *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionManager_eventGetConfiguration_Parms
	{
		FAuracronWorldPartitionConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration, METADATA_PARAMS(0, nullptr) }; // 595702961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::AuracronWorldPartitionManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::AuracronWorldPartitionManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWorldPartitionConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetConfiguration *******************

// ********** Begin Class UAuracronWorldPartitionManager Function GetDataLayerState ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics
{
	struct AuracronWorldPartitionManager_eventGetDataLayerState_Parms
	{
		FString DataLayerName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayerName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::NewProp_DataLayerName = { "DataLayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetDataLayerState_Parms, DataLayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerName_MetaData), NewProp_DataLayerName_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionManager_eventGetDataLayerState_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionManager_eventGetDataLayerState_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::NewProp_DataLayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetDataLayerState", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::AuracronWorldPartitionManager_eventGetDataLayerState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::AuracronWorldPartitionManager_eventGetDataLayerState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetDataLayerState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DataLayerName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetDataLayerState(Z_Param_DataLayerName);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetDataLayerState ******************

// ********** Begin Class UAuracronWorldPartitionManager Function GetDataLayerSubsystem ************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics
{
	struct AuracronWorldPartitionManager_eventGetDataLayerSubsystem_Parms
	{
		UDataLayerSubsystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetDataLayerSubsystem_Parms, ReturnValue), Z_Construct_UClass_UDataLayerSubsystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetDataLayerSubsystem", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::AuracronWorldPartitionManager_eventGetDataLayerSubsystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::AuracronWorldPartitionManager_eventGetDataLayerSubsystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetDataLayerSubsystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UDataLayerSubsystem**)Z_Param__Result=P_THIS->GetDataLayerSubsystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetDataLayerSubsystem **************

// ********** Begin Class UAuracronWorldPartitionManager Function GetInstance **********************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics
{
	struct AuracronWorldPartitionManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::AuracronWorldPartitionManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::AuracronWorldPartitionManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionManager**)Z_Param__Result=UAuracronWorldPartitionManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetInstance ************************

// ********** Begin Class UAuracronWorldPartitionManager Function GetLoadedCellCount ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics
{
	struct AuracronWorldPartitionManager_eventGetLoadedCellCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetLoadedCellCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetLoadedCellCount", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::AuracronWorldPartitionManager_eventGetLoadedCellCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::AuracronWorldPartitionManager_eventGetLoadedCellCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetLoadedCellCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedCellCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetLoadedCellCount *****************

// ********** Begin Class UAuracronWorldPartitionManager Function GetLoadedCells *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics
{
	struct AuracronWorldPartitionManager_eventGetLoadedCells_Parms
	{
		TArray<FAuracronCellInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCellInfo, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetLoadedCells_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetLoadedCells", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::AuracronWorldPartitionManager_eventGetLoadedCells_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::AuracronWorldPartitionManager_eventGetLoadedCells_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetLoadedCells)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCellInfo>*)Z_Param__Result=P_THIS->GetLoadedCells();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetLoadedCells *********************

// ********** Begin Class UAuracronWorldPartitionManager Function GetLoadedDataLayers **************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics
{
	struct AuracronWorldPartitionManager_eventGetLoadedDataLayers_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetLoadedDataLayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetLoadedDataLayers", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::AuracronWorldPartitionManager_eventGetLoadedDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::AuracronWorldPartitionManager_eventGetLoadedDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetLoadedDataLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedDataLayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetLoadedDataLayers ****************

// ********** Begin Class UAuracronWorldPartitionManager Function GetMemoryUsage *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics
{
	struct AuracronWorldPartitionManager_eventGetMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::AuracronWorldPartitionManager_eventGetMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::AuracronWorldPartitionManager_eventGetMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetMemoryUsage *********************

// ********** Begin Class UAuracronWorldPartitionManager Function GetState *************************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics
{
	struct AuracronWorldPartitionManager_eventGetState_Parms
	{
		EAuracronWorldPartitionState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState, METADATA_PARAMS(0, nullptr) }; // 1246104703
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetState", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::AuracronWorldPartitionManager_eventGetState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::AuracronWorldPartitionManager_eventGetState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronWorldPartitionState*)Z_Param__Result=P_THIS->GetState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetState ***************************

// ********** Begin Class UAuracronWorldPartitionManager Function GetStreamingCells ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics
{
	struct AuracronWorldPartitionManager_eventGetStreamingCells_Parms
	{
		TArray<FAuracronCellInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCellInfo, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetStreamingCells_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 917828667
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetStreamingCells", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::AuracronWorldPartitionManager_eventGetStreamingCells_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::AuracronWorldPartitionManager_eventGetStreamingCells_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetStreamingCells)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCellInfo>*)Z_Param__Result=P_THIS->GetStreamingCells();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetStreamingCells ******************

// ********** Begin Class UAuracronWorldPartitionManager Function GetStreamingDistance *************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics
{
	struct AuracronWorldPartitionManager_eventGetStreamingDistance_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetStreamingDistance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetStreamingDistance", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::AuracronWorldPartitionManager_eventGetStreamingDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::AuracronWorldPartitionManager_eventGetStreamingDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetStreamingDistance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStreamingDistance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetStreamingDistance ***************

// ********** Begin Class UAuracronWorldPartitionManager Function GetStreamingStatistics ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics
{
	struct AuracronWorldPartitionManager_eventGetStreamingStatistics_Parms
	{
		FAuracronStreamingStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetStreamingStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronStreamingStatistics, METADATA_PARAMS(0, nullptr) }; // 4248696152
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetStreamingStatistics", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::AuracronWorldPartitionManager_eventGetStreamingStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::AuracronWorldPartitionManager_eventGetStreamingStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetStreamingStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronStreamingStatistics*)Z_Param__Result=P_THIS->GetStreamingStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetStreamingStatistics *************

// ********** Begin Class UAuracronWorldPartitionManager Function GetTotalCellCount ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics
{
	struct AuracronWorldPartitionManager_eventGetTotalCellCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetTotalCellCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetTotalCellCount", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::AuracronWorldPartitionManager_eventGetTotalCellCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::AuracronWorldPartitionManager_eventGetTotalCellCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetTotalCellCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalCellCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetTotalCellCount ******************

// ********** Begin Class UAuracronWorldPartitionManager Function GetWorldPartition ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics
{
	struct AuracronWorldPartitionManager_eventGetWorldPartition_Parms
	{
		UWorldPartition* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetWorldPartition_Parms, ReturnValue), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetWorldPartition", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::AuracronWorldPartitionManager_eventGetWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::AuracronWorldPartitionManager_eventGetWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetWorldPartition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UWorldPartition**)Z_Param__Result=P_THIS->GetWorldPartition();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetWorldPartition ******************

// ********** Begin Class UAuracronWorldPartitionManager Function GetWorldPartitionSubsystem *******
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics
{
	struct AuracronWorldPartitionManager_eventGetWorldPartitionSubsystem_Parms
	{
		UWorldPartitionSubsystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventGetWorldPartitionSubsystem_Parms, ReturnValue), Z_Construct_UClass_UWorldPartitionSubsystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "GetWorldPartitionSubsystem", Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::AuracronWorldPartitionManager_eventGetWorldPartitionSubsystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::AuracronWorldPartitionManager_eventGetWorldPartitionSubsystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execGetWorldPartitionSubsystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UWorldPartitionSubsystem**)Z_Param__Result=P_THIS->GetWorldPartitionSubsystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function GetWorldPartitionSubsystem *********

// ********** Begin Class UAuracronWorldPartitionManager Function InitializeWorldPartition *********
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics
{
	struct AuracronWorldPartitionManager_eventInitializeWorldPartition_Parms
	{
		UWorld* World;
		FAuracronWorldPartitionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventInitializeWorldPartition_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventInitializeWorldPartition_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 595702961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "InitializeWorldPartition", Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::AuracronWorldPartitionManager_eventInitializeWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::AuracronWorldPartitionManager_eventInitializeWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execInitializeWorldPartition)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FAuracronWorldPartitionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWorldPartition(Z_Param_World,Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function InitializeWorldPartition ***********

// ********** Begin Class UAuracronWorldPartitionManager Function IsDebugVisualizationEnabled ******
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronWorldPartitionManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::AuracronWorldPartitionManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::AuracronWorldPartitionManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function IsDebugVisualizationEnabled ********

// ********** Begin Class UAuracronWorldPartitionManager Function IsInitialized ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::AuracronWorldPartitionManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::AuracronWorldPartitionManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function IsInitialized **********************

// ********** Begin Class UAuracronWorldPartitionManager Function LogCurrentState ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_LogCurrentState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_LogCurrentState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "LogCurrentState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_LogCurrentState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_LogCurrentState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_LogCurrentState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_LogCurrentState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execLogCurrentState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogCurrentState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function LogCurrentState ********************

// ********** Begin Class UAuracronWorldPartitionManager Function RequestCellLoading ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics
{
	struct AuracronWorldPartitionManager_eventRequestCellLoading_Parms
	{
		FString CellId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventRequestCellLoading_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::NewProp_CellId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "RequestCellLoading", Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::AuracronWorldPartitionManager_eventRequestCellLoading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::AuracronWorldPartitionManager_eventRequestCellLoading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execRequestCellLoading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RequestCellLoading(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function RequestCellLoading *****************

// ********** Begin Class UAuracronWorldPartitionManager Function RequestCellsInRadius *************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics
{
	struct AuracronWorldPartitionManager_eventRequestCellsInRadius_Parms
	{
		FVector Location;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventRequestCellsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventRequestCellsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "RequestCellsInRadius", Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::AuracronWorldPartitionManager_eventRequestCellsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::AuracronWorldPartitionManager_eventRequestCellsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execRequestCellsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RequestCellsInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function RequestCellsInRadius ***************

// ********** Begin Class UAuracronWorldPartitionManager Function RequestCellUnloading *************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics
{
	struct AuracronWorldPartitionManager_eventRequestCellUnloading_Parms
	{
		FString CellId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventRequestCellUnloading_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::NewProp_CellId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "RequestCellUnloading", Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::AuracronWorldPartitionManager_eventRequestCellUnloading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::AuracronWorldPartitionManager_eventRequestCellUnloading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execRequestCellUnloading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RequestCellUnloading(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function RequestCellUnloading ***************

// ********** Begin Class UAuracronWorldPartitionManager Function ResetStatistics ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function ResetStatistics ********************

// ********** Begin Class UAuracronWorldPartitionManager Function SetConfiguration *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionManager_eventSetConfiguration_Parms
	{
		FAuracronWorldPartitionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 595702961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::AuracronWorldPartitionManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::AuracronWorldPartitionManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronWorldPartitionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function SetConfiguration *******************

// ********** Begin Class UAuracronWorldPartitionManager Function SetDataLayerState ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics
{
	struct AuracronWorldPartitionManager_eventSetDataLayerState_Parms
	{
		FString DataLayerName;
		bool bIsLoaded;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayerName;
	static void NewProp_bIsLoaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoaded;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::NewProp_DataLayerName = { "DataLayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventSetDataLayerState_Parms, DataLayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerName_MetaData), NewProp_DataLayerName_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::NewProp_bIsLoaded_SetBit(void* Obj)
{
	((AuracronWorldPartitionManager_eventSetDataLayerState_Parms*)Obj)->bIsLoaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::NewProp_bIsLoaded = { "bIsLoaded", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionManager_eventSetDataLayerState_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::NewProp_bIsLoaded_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::NewProp_DataLayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::NewProp_bIsLoaded,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "SetDataLayerState", Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::AuracronWorldPartitionManager_eventSetDataLayerState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::AuracronWorldPartitionManager_eventSetDataLayerState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execSetDataLayerState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DataLayerName);
	P_GET_UBOOL(Z_Param_bIsLoaded);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDataLayerState(Z_Param_DataLayerName,Z_Param_bIsLoaded);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function SetDataLayerState ******************

// ********** Begin Class UAuracronWorldPartitionManager Function SetStreamingDistance *************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics
{
	struct AuracronWorldPartitionManager_eventSetStreamingDistance_Parms
	{
		float Distance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventSetStreamingDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::NewProp_Distance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "SetStreamingDistance", Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::AuracronWorldPartitionManager_eventSetStreamingDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::AuracronWorldPartitionManager_eventSetStreamingDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execSetStreamingDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStreamingDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function SetStreamingDistance ***************

// ********** Begin Class UAuracronWorldPartitionManager Function SetStreamingSource ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics
{
	struct AuracronWorldPartitionManager_eventSetStreamingSource_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventSetStreamingSource_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "SetStreamingSource", Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::AuracronWorldPartitionManager_eventSetStreamingSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::AuracronWorldPartitionManager_eventSetStreamingSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execSetStreamingSource)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStreamingSource(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function SetStreamingSource *****************

// ********** Begin Class UAuracronWorldPartitionManager Function SetupWorldPartition **************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics
{
	struct AuracronWorldPartitionManager_eventSetupWorldPartition_Parms
	{
		UWorld* World;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionManager_eventSetupWorldPartition_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionManager_eventSetupWorldPartition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionManager_eventSetupWorldPartition_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "SetupWorldPartition", Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::AuracronWorldPartitionManager_eventSetupWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::AuracronWorldPartitionManager_eventSetupWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execSetupWorldPartition)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupWorldPartition(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function SetupWorldPartition ****************

// ********** Begin Class UAuracronWorldPartitionManager Function ShutdownWorldPartition ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_ShutdownWorldPartition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_ShutdownWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "ShutdownWorldPartition", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_ShutdownWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_ShutdownWorldPartition_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_ShutdownWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_ShutdownWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execShutdownWorldPartition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownWorldPartition();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function ShutdownWorldPartition *************

// ********** Begin Class UAuracronWorldPartitionManager Function UpdateStreamingState *************
struct Z_Construct_UFunction_UAuracronWorldPartitionManager_UpdateStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionManager_UpdateStreamingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionManager, nullptr, "UpdateStreamingState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionManager_UpdateStreamingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionManager_UpdateStreamingState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionManager_UpdateStreamingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionManager_UpdateStreamingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionManager::execUpdateStreamingState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateStreamingState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionManager Function UpdateStreamingState ***************

// ********** Begin Class UAuracronWorldPartitionManager *******************************************
void UAuracronWorldPartitionManager::StaticRegisterNativesUAuracronWorldPartitionManager()
{
	UClass* Class = UAuracronWorldPartitionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "DrawDebugCells", &UAuracronWorldPartitionManager::execDrawDebugCells },
		{ "EnableDebugVisualization", &UAuracronWorldPartitionManager::execEnableDebugVisualization },
		{ "GetAllCells", &UAuracronWorldPartitionManager::execGetAllCells },
		{ "GetAvailableDataLayers", &UAuracronWorldPartitionManager::execGetAvailableDataLayers },
		{ "GetCellInfo", &UAuracronWorldPartitionManager::execGetCellInfo },
		{ "GetCellsInRadius", &UAuracronWorldPartitionManager::execGetCellsInRadius },
		{ "GetConfiguration", &UAuracronWorldPartitionManager::execGetConfiguration },
		{ "GetDataLayerState", &UAuracronWorldPartitionManager::execGetDataLayerState },
		{ "GetDataLayerSubsystem", &UAuracronWorldPartitionManager::execGetDataLayerSubsystem },
		{ "GetInstance", &UAuracronWorldPartitionManager::execGetInstance },
		{ "GetLoadedCellCount", &UAuracronWorldPartitionManager::execGetLoadedCellCount },
		{ "GetLoadedCells", &UAuracronWorldPartitionManager::execGetLoadedCells },
		{ "GetLoadedDataLayers", &UAuracronWorldPartitionManager::execGetLoadedDataLayers },
		{ "GetMemoryUsage", &UAuracronWorldPartitionManager::execGetMemoryUsage },
		{ "GetState", &UAuracronWorldPartitionManager::execGetState },
		{ "GetStreamingCells", &UAuracronWorldPartitionManager::execGetStreamingCells },
		{ "GetStreamingDistance", &UAuracronWorldPartitionManager::execGetStreamingDistance },
		{ "GetStreamingStatistics", &UAuracronWorldPartitionManager::execGetStreamingStatistics },
		{ "GetTotalCellCount", &UAuracronWorldPartitionManager::execGetTotalCellCount },
		{ "GetWorldPartition", &UAuracronWorldPartitionManager::execGetWorldPartition },
		{ "GetWorldPartitionSubsystem", &UAuracronWorldPartitionManager::execGetWorldPartitionSubsystem },
		{ "InitializeWorldPartition", &UAuracronWorldPartitionManager::execInitializeWorldPartition },
		{ "IsDebugVisualizationEnabled", &UAuracronWorldPartitionManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronWorldPartitionManager::execIsInitialized },
		{ "LogCurrentState", &UAuracronWorldPartitionManager::execLogCurrentState },
		{ "RequestCellLoading", &UAuracronWorldPartitionManager::execRequestCellLoading },
		{ "RequestCellsInRadius", &UAuracronWorldPartitionManager::execRequestCellsInRadius },
		{ "RequestCellUnloading", &UAuracronWorldPartitionManager::execRequestCellUnloading },
		{ "ResetStatistics", &UAuracronWorldPartitionManager::execResetStatistics },
		{ "SetConfiguration", &UAuracronWorldPartitionManager::execSetConfiguration },
		{ "SetDataLayerState", &UAuracronWorldPartitionManager::execSetDataLayerState },
		{ "SetStreamingDistance", &UAuracronWorldPartitionManager::execSetStreamingDistance },
		{ "SetStreamingSource", &UAuracronWorldPartitionManager::execSetStreamingSource },
		{ "SetupWorldPartition", &UAuracronWorldPartitionManager::execSetupWorldPartition },
		{ "ShutdownWorldPartition", &UAuracronWorldPartitionManager::execShutdownWorldPartition },
		{ "UpdateStreamingState", &UAuracronWorldPartitionManager::execUpdateStreamingState },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionManager;
UClass* UAuracronWorldPartitionManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionManager_NoRegister()
{
	return UAuracronWorldPartitionManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Manager\n * Central manager for large world streaming and partitioning\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartition.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Manager\nCentral manager for large world streaming and partitioning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellLoaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellUnloaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnStreamingStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartition_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Logger_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartition.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellUnloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnStreamingStateChanged;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WorldPartition;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Logger;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_DrawDebugCells, "DrawDebugCells" }, // 1693250948
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 4254615068
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAllCells, "GetAllCells" }, // 1639196128
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetAvailableDataLayers, "GetAvailableDataLayers" }, // 2346537337
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellInfo, "GetCellInfo" }, // 2484693775
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetCellsInRadius, "GetCellsInRadius" }, // 4160013143
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetConfiguration, "GetConfiguration" }, // 3686886674
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerState, "GetDataLayerState" }, // 3702230132
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetDataLayerSubsystem, "GetDataLayerSubsystem" }, // 3903657698
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetInstance, "GetInstance" }, // 2579767710
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCellCount, "GetLoadedCellCount" }, // 4240351154
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedCells, "GetLoadedCells" }, // 1357912444
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetLoadedDataLayers, "GetLoadedDataLayers" }, // 2451931465
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetMemoryUsage, "GetMemoryUsage" }, // 1630110444
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetState, "GetState" }, // 2190003305
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingCells, "GetStreamingCells" }, // 1386325126
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingDistance, "GetStreamingDistance" }, // 3847347826
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetStreamingStatistics, "GetStreamingStatistics" }, // 3659710092
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetTotalCellCount, "GetTotalCellCount" }, // 2039701136
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartition, "GetWorldPartition" }, // 1512870547
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_GetWorldPartitionSubsystem, "GetWorldPartitionSubsystem" }, // 1390872518
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_InitializeWorldPartition, "InitializeWorldPartition" }, // 1943543545
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 2838402970
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_IsInitialized, "IsInitialized" }, // 971828802
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_LogCurrentState, "LogCurrentState" }, // 31341771
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature, "OnCellLoaded__DelegateSignature" }, // 896687308
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature, "OnCellUnloaded__DelegateSignature" }, // 2412075688
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature, "OnStreamingStateChanged__DelegateSignature" }, // 3064342065
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellLoading, "RequestCellLoading" }, // 1145013418
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellsInRadius, "RequestCellsInRadius" }, // 2646096080
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_RequestCellUnloading, "RequestCellUnloading" }, // 3443850250
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_ResetStatistics, "ResetStatistics" }, // 3979692530
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_SetConfiguration, "SetConfiguration" }, // 1343883917
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_SetDataLayerState, "SetDataLayerState" }, // 4184619929
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingDistance, "SetStreamingDistance" }, // 32488524
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_SetStreamingSource, "SetStreamingSource" }, // 4125994669
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_SetupWorldPartition, "SetupWorldPartition" }, // 201963823
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_ShutdownWorldPartition, "ShutdownWorldPartition" }, // 339025521
		{ &Z_Construct_UFunction_UAuracronWorldPartitionManager_UpdateStreamingState, "UpdateStreamingState" }, // 3730650093
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_OnCellLoaded = { "OnCellLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, OnCellLoaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellLoaded_MetaData), NewProp_OnCellLoaded_MetaData) }; // 896687308
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_OnCellUnloaded = { "OnCellUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, OnCellUnloaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellUnloaded_MetaData), NewProp_OnCellUnloaded_MetaData) }; // 2412075688
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_OnStreamingStateChanged = { "OnStreamingStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, OnStreamingStateChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnStreamingStateChanged_MetaData), NewProp_OnStreamingStateChanged_MetaData) }; // 3064342065
void Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionManager), &Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, CurrentState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 1246104703
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, Configuration), Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 595702961
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_WorldPartition = { "WorldPartition", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, WorldPartition), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartition_MetaData), NewProp_WorldPartition_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_Logger = { "Logger", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionManager, Logger), Z_Construct_UClass_UAuracronWorldPartitionLogger_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Logger_MetaData), NewProp_Logger_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_OnCellLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_OnCellUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_OnStreamingStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_WorldPartition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::NewProp_Logger,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UEngineSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::ClassParams = {
	&UAuracronWorldPartitionManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionManager.OuterSingleton;
}
UAuracronWorldPartitionManager::UAuracronWorldPartitionManager() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionManager);
UAuracronWorldPartitionManager::~UAuracronWorldPartitionManager() {}
// ********** End Class UAuracronWorldPartitionManager *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronWorldPartitionState_StaticEnum, TEXT("EAuracronWorldPartitionState"), &Z_Registration_Info_UEnum_EAuracronWorldPartitionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1246104703U) },
		{ EAuracronStreamingState_StaticEnum, TEXT("EAuracronStreamingState"), &Z_Registration_Info_UEnum_EAuracronStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1699613073U) },
		{ EAuracronCellType_StaticEnum, TEXT("EAuracronCellType"), &Z_Registration_Info_UEnum_EAuracronCellType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2616550595U) },
		{ EAuracronWorldPartitionLogLevel_StaticEnum, TEXT("EAuracronWorldPartitionLogLevel"), &Z_Registration_Info_UEnum_EAuracronWorldPartitionLogLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 175707838U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronWorldPartitionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics::NewStructOps, TEXT("AuracronWorldPartitionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronWorldPartitionConfiguration), 595702961U) },
		{ FAuracronCellInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronCellInfo_Statics::NewStructOps, TEXT("AuracronCellInfo"), &Z_Registration_Info_UScriptStruct_FAuracronCellInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCellInfo), 917828667U) },
		{ FAuracronStreamingStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics::NewStructOps, TEXT("AuracronStreamingStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronStreamingStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStreamingStatistics), 4248696152U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionLogger, UAuracronWorldPartitionLogger::StaticClass, TEXT("UAuracronWorldPartitionLogger"), &Z_Registration_Info_UClass_UAuracronWorldPartitionLogger, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionLogger), 2944109712U) },
		{ Z_Construct_UClass_UAuracronWorldPartitionManager, UAuracronWorldPartitionManager::StaticClass, TEXT("UAuracronWorldPartitionManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionManager), 2908489606U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_3250440624(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
