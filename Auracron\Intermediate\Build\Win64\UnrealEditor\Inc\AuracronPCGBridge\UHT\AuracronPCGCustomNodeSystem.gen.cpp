// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGCustomNodeSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGCustomNodeSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeBuilder();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeFactory();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeFactory_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeRegistry();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeUtils_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UBlueprint_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSettings_NoRegister();
PCG_API UEnum* Z_Construct_UEnum_PCG_EPCGDataType();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGCustomParameterType *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType;
static UEnum* EAuracronPCGCustomParameterType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCustomParameterType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomParameterType>()
{
	return EAuracronPCGCustomParameterType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Array.DisplayName", "Array" },
		{ "Array.Name", "EAuracronPCGCustomParameterType::Array" },
		{ "BlueprintType", "true" },
		{ "Boolean.DisplayName", "Boolean" },
		{ "Boolean.Name", "EAuracronPCGCustomParameterType::Boolean" },
		{ "Class.DisplayName", "Class" },
		{ "Class.Name", "EAuracronPCGCustomParameterType::Class" },
		{ "Color.DisplayName", "Color" },
		{ "Color.Name", "EAuracronPCGCustomParameterType::Color" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom node parameter types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGCustomParameterType::Custom" },
		{ "Delegate.DisplayName", "Delegate" },
		{ "Delegate.Name", "EAuracronPCGCustomParameterType::Delegate" },
		{ "Enum.DisplayName", "Enum" },
		{ "Enum.Name", "EAuracronPCGCustomParameterType::Enum" },
		{ "Float.DisplayName", "Float" },
		{ "Float.Name", "EAuracronPCGCustomParameterType::Float" },
		{ "Integer.DisplayName", "Integer" },
		{ "Integer.Name", "EAuracronPCGCustomParameterType::Integer" },
		{ "Map.DisplayName", "Map" },
		{ "Map.Name", "EAuracronPCGCustomParameterType::Map" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
		{ "Object.DisplayName", "Object" },
		{ "Object.Name", "EAuracronPCGCustomParameterType::Object" },
		{ "Rotator.DisplayName", "Rotator" },
		{ "Rotator.Name", "EAuracronPCGCustomParameterType::Rotator" },
		{ "Set.DisplayName", "Set" },
		{ "Set.Name", "EAuracronPCGCustomParameterType::Set" },
		{ "String.DisplayName", "String" },
		{ "String.Name", "EAuracronPCGCustomParameterType::String" },
		{ "Struct.DisplayName", "Struct" },
		{ "Struct.Name", "EAuracronPCGCustomParameterType::Struct" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom node parameter types" },
#endif
		{ "Transform.DisplayName", "Transform" },
		{ "Transform.Name", "EAuracronPCGCustomParameterType::Transform" },
		{ "Vector.DisplayName", "Vector" },
		{ "Vector.Name", "EAuracronPCGCustomParameterType::Vector" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCustomParameterType::Boolean", (int64)EAuracronPCGCustomParameterType::Boolean },
		{ "EAuracronPCGCustomParameterType::Integer", (int64)EAuracronPCGCustomParameterType::Integer },
		{ "EAuracronPCGCustomParameterType::Float", (int64)EAuracronPCGCustomParameterType::Float },
		{ "EAuracronPCGCustomParameterType::String", (int64)EAuracronPCGCustomParameterType::String },
		{ "EAuracronPCGCustomParameterType::Vector", (int64)EAuracronPCGCustomParameterType::Vector },
		{ "EAuracronPCGCustomParameterType::Rotator", (int64)EAuracronPCGCustomParameterType::Rotator },
		{ "EAuracronPCGCustomParameterType::Transform", (int64)EAuracronPCGCustomParameterType::Transform },
		{ "EAuracronPCGCustomParameterType::Color", (int64)EAuracronPCGCustomParameterType::Color },
		{ "EAuracronPCGCustomParameterType::Object", (int64)EAuracronPCGCustomParameterType::Object },
		{ "EAuracronPCGCustomParameterType::Class", (int64)EAuracronPCGCustomParameterType::Class },
		{ "EAuracronPCGCustomParameterType::Enum", (int64)EAuracronPCGCustomParameterType::Enum },
		{ "EAuracronPCGCustomParameterType::Struct", (int64)EAuracronPCGCustomParameterType::Struct },
		{ "EAuracronPCGCustomParameterType::Array", (int64)EAuracronPCGCustomParameterType::Array },
		{ "EAuracronPCGCustomParameterType::Map", (int64)EAuracronPCGCustomParameterType::Map },
		{ "EAuracronPCGCustomParameterType::Set", (int64)EAuracronPCGCustomParameterType::Set },
		{ "EAuracronPCGCustomParameterType::Delegate", (int64)EAuracronPCGCustomParameterType::Delegate },
		{ "EAuracronPCGCustomParameterType::Custom", (int64)EAuracronPCGCustomParameterType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCustomParameterType",
	"EAuracronPCGCustomParameterType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType.InnerSingleton;
}
// ********** End Enum EAuracronPCGCustomParameterType *********************************************

// ********** Begin Enum EAuracronPCGCustomNodeExecutionMode ***************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode;
static UEnum* EAuracronPCGCustomNodeExecutionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCustomNodeExecutionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomNodeExecutionMode>()
{
	return EAuracronPCGCustomNodeExecutionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Asynchronous.DisplayName", "Asynchronous" },
		{ "Asynchronous.Name", "EAuracronPCGCustomNodeExecutionMode::Asynchronous" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom node execution modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGCustomNodeExecutionMode::Custom" },
		{ "Distributed.DisplayName", "Distributed" },
		{ "Distributed.Name", "EAuracronPCGCustomNodeExecutionMode::Distributed" },
		{ "GPU.DisplayName", "GPU" },
		{ "GPU.Name", "EAuracronPCGCustomNodeExecutionMode::GPU" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
		{ "Synchronous.DisplayName", "Synchronous" },
		{ "Synchronous.Name", "EAuracronPCGCustomNodeExecutionMode::Synchronous" },
		{ "Threaded.DisplayName", "Threaded" },
		{ "Threaded.Name", "EAuracronPCGCustomNodeExecutionMode::Threaded" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom node execution modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCustomNodeExecutionMode::Synchronous", (int64)EAuracronPCGCustomNodeExecutionMode::Synchronous },
		{ "EAuracronPCGCustomNodeExecutionMode::Asynchronous", (int64)EAuracronPCGCustomNodeExecutionMode::Asynchronous },
		{ "EAuracronPCGCustomNodeExecutionMode::Threaded", (int64)EAuracronPCGCustomNodeExecutionMode::Threaded },
		{ "EAuracronPCGCustomNodeExecutionMode::GPU", (int64)EAuracronPCGCustomNodeExecutionMode::GPU },
		{ "EAuracronPCGCustomNodeExecutionMode::Distributed", (int64)EAuracronPCGCustomNodeExecutionMode::Distributed },
		{ "EAuracronPCGCustomNodeExecutionMode::Custom", (int64)EAuracronPCGCustomNodeExecutionMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCustomNodeExecutionMode",
	"EAuracronPCGCustomNodeExecutionMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGCustomNodeExecutionMode *****************************************

// ********** Begin Enum EAuracronPCGCustomNodeTemplateType ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType;
static UEnum* EAuracronPCGCustomNodeTemplateType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCustomNodeTemplateType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomNodeTemplateType>()
{
	return EAuracronPCGCustomNodeTemplateType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Analyzer.DisplayName", "Analyzer" },
		{ "Analyzer.Name", "EAuracronPCGCustomNodeTemplateType::Analyzer" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom node template types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGCustomNodeTemplateType::Custom" },
		{ "Debug.DisplayName", "Debug" },
		{ "Debug.Name", "EAuracronPCGCustomNodeTemplateType::Debug" },
		{ "Filter.DisplayName", "Filter" },
		{ "Filter.Name", "EAuracronPCGCustomNodeTemplateType::Filter" },
		{ "Generator.DisplayName", "Generator" },
		{ "Generator.Name", "EAuracronPCGCustomNodeTemplateType::Generator" },
		{ "Modifier.DisplayName", "Modifier" },
		{ "Modifier.Name", "EAuracronPCGCustomNodeTemplateType::Modifier" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
		{ "Sampler.DisplayName", "Sampler" },
		{ "Sampler.Name", "EAuracronPCGCustomNodeTemplateType::Sampler" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom node template types" },
#endif
		{ "Transformer.DisplayName", "Transformer" },
		{ "Transformer.Name", "EAuracronPCGCustomNodeTemplateType::Transformer" },
		{ "Utility.DisplayName", "Utility" },
		{ "Utility.Name", "EAuracronPCGCustomNodeTemplateType::Utility" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCustomNodeTemplateType::Generator", (int64)EAuracronPCGCustomNodeTemplateType::Generator },
		{ "EAuracronPCGCustomNodeTemplateType::Modifier", (int64)EAuracronPCGCustomNodeTemplateType::Modifier },
		{ "EAuracronPCGCustomNodeTemplateType::Filter", (int64)EAuracronPCGCustomNodeTemplateType::Filter },
		{ "EAuracronPCGCustomNodeTemplateType::Sampler", (int64)EAuracronPCGCustomNodeTemplateType::Sampler },
		{ "EAuracronPCGCustomNodeTemplateType::Transformer", (int64)EAuracronPCGCustomNodeTemplateType::Transformer },
		{ "EAuracronPCGCustomNodeTemplateType::Analyzer", (int64)EAuracronPCGCustomNodeTemplateType::Analyzer },
		{ "EAuracronPCGCustomNodeTemplateType::Utility", (int64)EAuracronPCGCustomNodeTemplateType::Utility },
		{ "EAuracronPCGCustomNodeTemplateType::Debug", (int64)EAuracronPCGCustomNodeTemplateType::Debug },
		{ "EAuracronPCGCustomNodeTemplateType::Custom", (int64)EAuracronPCGCustomNodeTemplateType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCustomNodeTemplateType",
	"EAuracronPCGCustomNodeTemplateType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType.InnerSingleton;
}
// ********** End Enum EAuracronPCGCustomNodeTemplateType ******************************************

// ********** Begin Enum EAuracronPCGCustomNodeValidationLevel *************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel;
static UEnum* EAuracronPCGCustomNodeValidationLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGCustomNodeValidationLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomNodeValidationLevel>()
{
	return EAuracronPCGCustomNodeValidationLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EAuracronPCGCustomNodeValidationLevel::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom node validation levels\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGCustomNodeValidationLevel::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGCustomNodeValidationLevel::None" },
		{ "Standard.DisplayName", "Standard" },
		{ "Standard.Name", "EAuracronPCGCustomNodeValidationLevel::Standard" },
		{ "Strict.DisplayName", "Strict" },
		{ "Strict.Name", "EAuracronPCGCustomNodeValidationLevel::Strict" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom node validation levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGCustomNodeValidationLevel::None", (int64)EAuracronPCGCustomNodeValidationLevel::None },
		{ "EAuracronPCGCustomNodeValidationLevel::Basic", (int64)EAuracronPCGCustomNodeValidationLevel::Basic },
		{ "EAuracronPCGCustomNodeValidationLevel::Standard", (int64)EAuracronPCGCustomNodeValidationLevel::Standard },
		{ "EAuracronPCGCustomNodeValidationLevel::Strict", (int64)EAuracronPCGCustomNodeValidationLevel::Strict },
		{ "EAuracronPCGCustomNodeValidationLevel::Custom", (int64)EAuracronPCGCustomNodeValidationLevel::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGCustomNodeValidationLevel",
	"EAuracronPCGCustomNodeValidationLevel",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel.InnerSingleton;
}
// ********** End Enum EAuracronPCGCustomNodeValidationLevel ***************************************

// ********** Begin ScriptStruct FAuracronPCGCustomParameterDescriptor *****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor;
class UScriptStruct* FAuracronPCGCustomParameterDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGCustomParameterDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Parameter Descriptor\n * Describes a custom parameter for a custom node\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Parameter Descriptor\nDescribes a custom parameter for a custom node" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "Category", "Parameter" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "Parameter" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Parameter" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterType_MetaData[] = {
		{ "Category", "Parameter" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "Category", "Default Value" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRequired_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasMinValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "EditCondition", "bHasMinValue" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasMaxValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "EditCondition", "bHasMaxValue" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAdvanced_MetaData[] = {
		{ "Category", "UI" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsHidden_MetaData[] = {
		{ "Category", "UI" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "UI" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tooltip_MetaData[] = {
		{ "Category", "UI" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnumValues_MetaData[] = {
		{ "Category", "Enum" },
		{ "EditCondition", "ParameterType == EAuracronPCGCustomParameterType::Enum" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectClass_MetaData[] = {
		{ "Category", "Object" },
		{ "EditCondition", "ParameterType == EAuracronPCGCustomParameterType::Object || ParameterType == EAuracronPCGCustomParameterType::Class" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ParameterType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ParameterType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultValue;
	static void NewProp_bIsRequired_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRequired;
	static void NewProp_bHasMinValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasMinValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinValue;
	static void NewProp_bHasMaxValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasMaxValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static void NewProp_bIsAdvanced_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAdvanced;
	static void NewProp_bIsHidden_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsHidden;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tooltip;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnumValues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnumValues;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_ObjectClass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGCustomParameterDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ParameterType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ParameterType = { "ParameterType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, ParameterType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterType_MetaData), NewProp_ParameterType_MetaData) }; // 1387726243
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, DefaultValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsRequired_SetBit(void* Obj)
{
	((FAuracronPCGCustomParameterDescriptor*)Obj)->bIsRequired = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsRequired = { "bIsRequired", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsRequired_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRequired_MetaData), NewProp_bIsRequired_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMinValue_SetBit(void* Obj)
{
	((FAuracronPCGCustomParameterDescriptor*)Obj)->bHasMinValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMinValue = { "bHasMinValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMinValue_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasMinValue_MetaData), NewProp_bHasMinValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_MinValue = { "MinValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, MinValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinValue_MetaData), NewProp_MinValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMaxValue_SetBit(void* Obj)
{
	((FAuracronPCGCustomParameterDescriptor*)Obj)->bHasMaxValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMaxValue = { "bHasMaxValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMaxValue_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasMaxValue_MetaData), NewProp_bHasMaxValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, MaxValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValue_MetaData), NewProp_MaxValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsAdvanced_SetBit(void* Obj)
{
	((FAuracronPCGCustomParameterDescriptor*)Obj)->bIsAdvanced = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsAdvanced = { "bIsAdvanced", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsAdvanced_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAdvanced_MetaData), NewProp_bIsAdvanced_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsHidden_SetBit(void* Obj)
{
	((FAuracronPCGCustomParameterDescriptor*)Obj)->bIsHidden = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsHidden = { "bIsHidden", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsHidden_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsHidden_MetaData), NewProp_bIsHidden_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_Tooltip = { "Tooltip", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, Tooltip), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tooltip_MetaData), NewProp_Tooltip_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_EnumValues_Inner = { "EnumValues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_EnumValues = { "EnumValues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, EnumValues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnumValues_MetaData), NewProp_EnumValues_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ObjectClass = { "ObjectClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomParameterDescriptor, ObjectClass), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectClass_MetaData), NewProp_ObjectClass_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ParameterType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ParameterType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_MinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bHasMaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsAdvanced,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_bIsHidden,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_Tooltip,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_EnumValues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_EnumValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewProp_ObjectClass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGCustomParameterDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGCustomParameterDescriptor),
	alignof(FAuracronPCGCustomParameterDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGCustomParameterDescriptor *******************************

// ********** Begin ScriptStruct FAuracronPCGCustomPinDescriptor ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor;
class UScriptStruct* FAuracronPCGCustomPinDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGCustomPinDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Pin Descriptor\n * Describes input/output pins for a custom node\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Pin Descriptor\nDescribes input/output pins for a custom node" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinName_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedTypes_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInput_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRequired_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowMultipleConnections_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAdvanced_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsHidden_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinColor_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tooltip_MetaData[] = {
		{ "Category", "Pin" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PinName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FUInt32PropertyParams NewProp_AllowedTypes_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AllowedTypes;
	static void NewProp_bIsInput_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInput;
	static void NewProp_bIsRequired_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRequired;
	static void NewProp_bAllowMultipleConnections_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowMultipleConnections;
	static void NewProp_bIsAdvanced_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAdvanced;
	static void NewProp_bIsHidden_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsHidden;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PinColor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tooltip;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGCustomPinDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_PinName = { "PinName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomPinDescriptor, PinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinName_MetaData), NewProp_PinName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomPinDescriptor, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomPinDescriptor, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FUInt32PropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_AllowedTypes_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::UInt32, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_AllowedTypes = { "AllowedTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomPinDescriptor, AllowedTypes), Z_Construct_UEnum_PCG_EPCGDataType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedTypes_MetaData), NewProp_AllowedTypes_MetaData) }; // 2498721046
void Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsInput_SetBit(void* Obj)
{
	((FAuracronPCGCustomPinDescriptor*)Obj)->bIsInput = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsInput = { "bIsInput", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomPinDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsInput_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInput_MetaData), NewProp_bIsInput_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsRequired_SetBit(void* Obj)
{
	((FAuracronPCGCustomPinDescriptor*)Obj)->bIsRequired = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsRequired = { "bIsRequired", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomPinDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsRequired_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRequired_MetaData), NewProp_bIsRequired_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bAllowMultipleConnections_SetBit(void* Obj)
{
	((FAuracronPCGCustomPinDescriptor*)Obj)->bAllowMultipleConnections = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bAllowMultipleConnections = { "bAllowMultipleConnections", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomPinDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bAllowMultipleConnections_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowMultipleConnections_MetaData), NewProp_bAllowMultipleConnections_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsAdvanced_SetBit(void* Obj)
{
	((FAuracronPCGCustomPinDescriptor*)Obj)->bIsAdvanced = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsAdvanced = { "bIsAdvanced", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomPinDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsAdvanced_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAdvanced_MetaData), NewProp_bIsAdvanced_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsHidden_SetBit(void* Obj)
{
	((FAuracronPCGCustomPinDescriptor*)Obj)->bIsHidden = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsHidden = { "bIsHidden", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomPinDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsHidden_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsHidden_MetaData), NewProp_bIsHidden_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_PinColor = { "PinColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomPinDescriptor, PinColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinColor_MetaData), NewProp_PinColor_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_Tooltip = { "Tooltip", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomPinDescriptor, Tooltip), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tooltip_MetaData), NewProp_Tooltip_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_PinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_AllowedTypes_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_AllowedTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsInput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bAllowMultipleConnections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsAdvanced,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_bIsHidden,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_PinColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewProp_Tooltip,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGCustomPinDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGCustomPinDescriptor),
	alignof(FAuracronPCGCustomPinDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGCustomPinDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGCustomNodeTemplate ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate;
class UScriptStruct* FAuracronPCGCustomNodeTemplate::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGCustomNodeTemplate"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Node Template\n * Template for creating custom PCG nodes\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Node Template\nTemplate for creating custom PCG nodes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateType_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeColor_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputPins_MetaData[] = {
		{ "Category", "Pins" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputPins_MetaData[] = {
		{ "Category", "Pins" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionMode_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionFunction_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlueprintImplementation_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NativeClassName_MetaData[] = {
		{ "Category", "Execution" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationLevel_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationFunction_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsExperimental_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDeprecated_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Version_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Author_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TemplateName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TemplateType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TemplateType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NodeColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Parameters_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InputPins_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputPins;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutputPins_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutputPins;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExecutionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExecutionMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExecutionFunction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BlueprintImplementation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NativeClassName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationLevel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationFunction;
	static void NewProp_bIsExperimental_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsExperimental;
	static void NewProp_bIsDeprecated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDeprecated;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Version;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Author;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGCustomNodeTemplate>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_TemplateType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_TemplateType = { "TemplateType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, TemplateType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateType_MetaData), NewProp_TemplateType_MetaData) }; // 141862105
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, Category), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 3952805449
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Tags_Inner = { "Tags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, Tags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_NodeColor = { "NodeColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, NodeColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeColor_MetaData), NewProp_NodeColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Parameters_Inner = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, Parameters), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) }; // 3935982077
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_InputPins_Inner = { "InputPins", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor, METADATA_PARAMS(0, nullptr) }; // 3804071926
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_InputPins = { "InputPins", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, InputPins), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputPins_MetaData), NewProp_InputPins_MetaData) }; // 3804071926
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_OutputPins_Inner = { "OutputPins", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor, METADATA_PARAMS(0, nullptr) }; // 3804071926
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_OutputPins = { "OutputPins", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, OutputPins), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputPins_MetaData), NewProp_OutputPins_MetaData) }; // 3804071926
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ExecutionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ExecutionMode = { "ExecutionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, ExecutionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionMode_MetaData), NewProp_ExecutionMode_MetaData) }; // 1270598183
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ExecutionFunction = { "ExecutionFunction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, ExecutionFunction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionFunction_MetaData), NewProp_ExecutionFunction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_BlueprintImplementation = { "BlueprintImplementation", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, BlueprintImplementation), Z_Construct_UClass_UBlueprint_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlueprintImplementation_MetaData), NewProp_BlueprintImplementation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_NativeClassName = { "NativeClassName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, NativeClassName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NativeClassName_MetaData), NewProp_NativeClassName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ValidationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ValidationLevel = { "ValidationLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, ValidationLevel), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationLevel_MetaData), NewProp_ValidationLevel_MetaData) }; // 64341457
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ValidationFunction = { "ValidationFunction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, ValidationFunction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationFunction_MetaData), NewProp_ValidationFunction_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsExperimental_SetBit(void* Obj)
{
	((FAuracronPCGCustomNodeTemplate*)Obj)->bIsExperimental = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsExperimental = { "bIsExperimental", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomNodeTemplate), &Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsExperimental_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsExperimental_MetaData), NewProp_bIsExperimental_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsDeprecated_SetBit(void* Obj)
{
	((FAuracronPCGCustomNodeTemplate*)Obj)->bIsDeprecated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsDeprecated = { "bIsDeprecated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGCustomNodeTemplate), &Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsDeprecated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDeprecated_MetaData), NewProp_bIsDeprecated_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Version = { "Version", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, Version), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Version_MetaData), NewProp_Version_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Author = { "Author", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGCustomNodeTemplate, Author), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Author_MetaData), NewProp_Author_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_TemplateType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_TemplateType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Tags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Tags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_NodeColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Parameters_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_InputPins_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_InputPins,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_OutputPins_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_OutputPins,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ExecutionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ExecutionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ExecutionFunction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_BlueprintImplementation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_NativeClassName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ValidationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ValidationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_ValidationFunction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsExperimental,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_bIsDeprecated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Version,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewProp_Author,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGCustomNodeTemplate",
	Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::PropPointers),
	sizeof(FAuracronPCGCustomNodeTemplate),
	alignof(FAuracronPCGCustomNodeTemplate),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGCustomNodeTemplate **************************************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function CompileBlueprintNode **************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics
{
	struct AuracronPCGCustomNodeFactory_eventCompileBlueprintNode_Parms
	{
		UBlueprint* Blueprint;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Blueprint;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::NewProp_Blueprint = { "Blueprint", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventCompileBlueprintNode_Parms, Blueprint), Z_Construct_UClass_UBlueprint_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeFactory_eventCompileBlueprintNode_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeFactory_eventCompileBlueprintNode_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::NewProp_Blueprint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "CompileBlueprintNode", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::AuracronPCGCustomNodeFactory_eventCompileBlueprintNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::AuracronPCGCustomNodeFactory_eventCompileBlueprintNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execCompileBlueprintNode)
{
	P_GET_OBJECT(UBlueprint,Z_Param_Blueprint);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeFactory::CompileBlueprintNode(Z_Param_Blueprint);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function CompileBlueprintNode ****************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function CreateBlueprintFromTemplate *******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics
{
	struct AuracronPCGCustomNodeFactory_eventCreateBlueprintFromTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		UBlueprint* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventCreateBlueprintFromTemplate_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventCreateBlueprintFromTemplate_Parms, ReturnValue), Z_Construct_UClass_UBlueprint_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "CreateBlueprintFromTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::AuracronPCGCustomNodeFactory_eventCreateBlueprintFromTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::AuracronPCGCustomNodeFactory_eventCreateBlueprintFromTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execCreateBlueprintFromTemplate)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UBlueprint**)Z_Param__Result=UAuracronPCGCustomNodeFactory::CreateBlueprintFromTemplate(Z_Param_Out_Template);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function CreateBlueprintFromTemplate *********

// ********** Begin Class UAuracronPCGCustomNodeFactory Function CreateCustomNodeClass *************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics
{
	struct AuracronPCGCustomNodeFactory_eventCreateCustomNodeClass_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		UClass* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node creation functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node creation functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventCreateCustomNodeClass_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventCreateCustomNodeClass_Parms, ReturnValue), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "CreateCustomNodeClass", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::AuracronPCGCustomNodeFactory_eventCreateCustomNodeClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::AuracronPCGCustomNodeFactory_eventCreateCustomNodeClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execCreateCustomNodeClass)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UClass**)Z_Param__Result=UAuracronPCGCustomNodeFactory::CreateCustomNodeClass(Z_Param_Out_Template);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function CreateCustomNodeClass ***************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function CreateCustomNodeInstance **********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics
{
	struct AuracronPCGCustomNodeFactory_eventCreateCustomNodeInstance_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		UPCGSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventCreateCustomNodeInstance_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventCreateCustomNodeInstance_Parms, ReturnValue), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "CreateCustomNodeInstance", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::AuracronPCGCustomNodeFactory_eventCreateCustomNodeInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::AuracronPCGCustomNodeFactory_eventCreateCustomNodeInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execCreateCustomNodeInstance)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGSettings**)Z_Param__Result=UAuracronPCGCustomNodeFactory::CreateCustomNodeInstance(Z_Param_Out_Template);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function CreateCustomNodeInstance ************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function GetAvailableTemplateFiles *********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics
{
	struct AuracronPCGCustomNodeFactory_eventGetAvailableTemplateFiles_Parms
	{
		FString Directory;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Directory_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Directory;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::NewProp_Directory = { "Directory", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventGetAvailableTemplateFiles_Parms, Directory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Directory_MetaData), NewProp_Directory_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventGetAvailableTemplateFiles_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::NewProp_Directory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "GetAvailableTemplateFiles", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::AuracronPCGCustomNodeFactory_eventGetAvailableTemplateFiles_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::AuracronPCGCustomNodeFactory_eventGetAvailableTemplateFiles_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execGetAvailableTemplateFiles)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Directory);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGCustomNodeFactory::GetAvailableTemplateFiles(Z_Param_Directory);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function GetAvailableTemplateFiles ***********

// ********** Begin Class UAuracronPCGCustomNodeFactory Function GetRegisteredTemplates ************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics
{
	struct AuracronPCGCustomNodeFactory_eventGetRegisteredTemplates_Parms
	{
		TArray<FAuracronPCGCustomNodeTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventGetRegisteredTemplates_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "GetRegisteredTemplates", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::AuracronPCGCustomNodeFactory_eventGetRegisteredTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::AuracronPCGCustomNodeFactory_eventGetRegisteredTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execGetRegisteredTemplates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGCustomNodeTemplate>*)Z_Param__Result=UAuracronPCGCustomNodeFactory::GetRegisteredTemplates();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function GetRegisteredTemplates **************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function GetTemplate ***********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics
{
	struct AuracronPCGCustomNodeFactory_eventGetTemplate_Parms
	{
		FString TemplateName;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TemplateName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventGetTemplate_Parms, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventGetTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "GetTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::AuracronPCGCustomNodeFactory_eventGetTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::AuracronPCGCustomNodeFactory_eventGetTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execGetTemplate)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TemplateName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeFactory::GetTemplate(Z_Param_TemplateName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function GetTemplate *************************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function GetTemplateValidationErrors *******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics
{
	struct AuracronPCGCustomNodeFactory_eventGetTemplateValidationErrors_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventGetTemplateValidationErrors_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventGetTemplateValidationErrors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "GetTemplateValidationErrors", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::AuracronPCGCustomNodeFactory_eventGetTemplateValidationErrors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::AuracronPCGCustomNodeFactory_eventGetTemplateValidationErrors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execGetTemplateValidationErrors)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGCustomNodeFactory::GetTemplateValidationErrors(Z_Param_Out_Template);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function GetTemplateValidationErrors *********

// ********** Begin Class UAuracronPCGCustomNodeFactory Function LoadTemplateFromFile **************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics
{
	struct AuracronPCGCustomNodeFactory_eventLoadTemplateFromFile_Parms
	{
		FString FilePath;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventLoadTemplateFromFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventLoadTemplateFromFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "LoadTemplateFromFile", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::AuracronPCGCustomNodeFactory_eventLoadTemplateFromFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::AuracronPCGCustomNodeFactory_eventLoadTemplateFromFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execLoadTemplateFromFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeFactory::LoadTemplateFromFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function LoadTemplateFromFile ****************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function RegisterCustomNodeTemplate ********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics
{
	struct AuracronPCGCustomNodeFactory_eventRegisterCustomNodeTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventRegisterCustomNodeTemplate_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
void Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeFactory_eventRegisterCustomNodeTemplate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeFactory_eventRegisterCustomNodeTemplate_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "RegisterCustomNodeTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::AuracronPCGCustomNodeFactory_eventRegisterCustomNodeTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::AuracronPCGCustomNodeFactory_eventRegisterCustomNodeTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execRegisterCustomNodeTemplate)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeFactory::RegisterCustomNodeTemplate(Z_Param_Out_Template);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function RegisterCustomNodeTemplate **********

// ********** Begin Class UAuracronPCGCustomNodeFactory Function SaveTemplateToFile ****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics
{
	struct AuracronPCGCustomNodeFactory_eventSaveTemplateToFile_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventSaveTemplateToFile_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventSaveTemplateToFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeFactory_eventSaveTemplateToFile_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeFactory_eventSaveTemplateToFile_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "SaveTemplateToFile", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::AuracronPCGCustomNodeFactory_eventSaveTemplateToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::AuracronPCGCustomNodeFactory_eventSaveTemplateToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execSaveTemplateToFile)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeFactory::SaveTemplateToFile(Z_Param_Out_Template,Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function SaveTemplateToFile ******************

// ********** Begin Class UAuracronPCGCustomNodeFactory Function UnregisterCustomNodeTemplate ******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics
{
	struct AuracronPCGCustomNodeFactory_eventUnregisterCustomNodeTemplate_Parms
	{
		FString TemplateName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TemplateName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventUnregisterCustomNodeTemplate_Parms, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeFactory_eventUnregisterCustomNodeTemplate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeFactory_eventUnregisterCustomNodeTemplate_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "UnregisterCustomNodeTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::AuracronPCGCustomNodeFactory_eventUnregisterCustomNodeTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::AuracronPCGCustomNodeFactory_eventUnregisterCustomNodeTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execUnregisterCustomNodeTemplate)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TemplateName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeFactory::UnregisterCustomNodeTemplate(Z_Param_TemplateName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function UnregisterCustomNodeTemplate ********

// ********** Begin Class UAuracronPCGCustomNodeFactory Function ValidateTemplate ******************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics
{
	struct AuracronPCGCustomNodeFactory_eventValidateTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		FString OutErrorMessage;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Factory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrorMessage;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventValidateTemplate_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_OutErrorMessage = { "OutErrorMessage", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeFactory_eventValidateTemplate_Parms, OutErrorMessage), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeFactory_eventValidateTemplate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeFactory_eventValidateTemplate_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_OutErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeFactory, nullptr, "ValidateTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::AuracronPCGCustomNodeFactory_eventValidateTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::AuracronPCGCustomNodeFactory_eventValidateTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeFactory::execValidateTemplate)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_OutErrorMessage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeFactory::ValidateTemplate(Z_Param_Out_Template,Z_Param_Out_OutErrorMessage);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeFactory Function ValidateTemplate ********************

// ********** Begin Class UAuracronPCGCustomNodeFactory ********************************************
void UAuracronPCGCustomNodeFactory::StaticRegisterNativesUAuracronPCGCustomNodeFactory()
{
	UClass* Class = UAuracronPCGCustomNodeFactory::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CompileBlueprintNode", &UAuracronPCGCustomNodeFactory::execCompileBlueprintNode },
		{ "CreateBlueprintFromTemplate", &UAuracronPCGCustomNodeFactory::execCreateBlueprintFromTemplate },
		{ "CreateCustomNodeClass", &UAuracronPCGCustomNodeFactory::execCreateCustomNodeClass },
		{ "CreateCustomNodeInstance", &UAuracronPCGCustomNodeFactory::execCreateCustomNodeInstance },
		{ "GetAvailableTemplateFiles", &UAuracronPCGCustomNodeFactory::execGetAvailableTemplateFiles },
		{ "GetRegisteredTemplates", &UAuracronPCGCustomNodeFactory::execGetRegisteredTemplates },
		{ "GetTemplate", &UAuracronPCGCustomNodeFactory::execGetTemplate },
		{ "GetTemplateValidationErrors", &UAuracronPCGCustomNodeFactory::execGetTemplateValidationErrors },
		{ "LoadTemplateFromFile", &UAuracronPCGCustomNodeFactory::execLoadTemplateFromFile },
		{ "RegisterCustomNodeTemplate", &UAuracronPCGCustomNodeFactory::execRegisterCustomNodeTemplate },
		{ "SaveTemplateToFile", &UAuracronPCGCustomNodeFactory::execSaveTemplateToFile },
		{ "UnregisterCustomNodeTemplate", &UAuracronPCGCustomNodeFactory::execUnregisterCustomNodeTemplate },
		{ "ValidateTemplate", &UAuracronPCGCustomNodeFactory::execValidateTemplate },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory;
UClass* UAuracronPCGCustomNodeFactory::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCustomNodeFactory;
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCustomNodeFactory"),
			Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCustomNodeFactory,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeFactory_NoRegister()
{
	return UAuracronPCGCustomNodeFactory::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Node Factory\n * Factory for creating custom PCG nodes from templates\n */" },
#endif
		{ "IncludePath", "AuracronPCGCustomNodeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Node Factory\nFactory for creating custom PCG nodes from templates" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CompileBlueprintNode, "CompileBlueprintNode" }, // 65009824
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateBlueprintFromTemplate, "CreateBlueprintFromTemplate" }, // 931445548
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeClass, "CreateCustomNodeClass" }, // 2385704824
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_CreateCustomNodeInstance, "CreateCustomNodeInstance" }, // 2844606830
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetAvailableTemplateFiles, "GetAvailableTemplateFiles" }, // 2257048105
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetRegisteredTemplates, "GetRegisteredTemplates" }, // 3619027360
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplate, "GetTemplate" }, // 665049557
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_GetTemplateValidationErrors, "GetTemplateValidationErrors" }, // 3389725190
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_LoadTemplateFromFile, "LoadTemplateFromFile" }, // 458785798
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_RegisterCustomNodeTemplate, "RegisterCustomNodeTemplate" }, // 4126198159
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_SaveTemplateToFile, "SaveTemplateToFile" }, // 562453402
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_UnregisterCustomNodeTemplate, "UnregisterCustomNodeTemplate" }, // 433339901
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeFactory_ValidateTemplate, "ValidateTemplate" }, // 2574761553
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCustomNodeFactory>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics::ClassParams = {
	&UAuracronPCGCustomNodeFactory::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeFactory()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory.OuterSingleton, Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory.OuterSingleton;
}
UAuracronPCGCustomNodeFactory::UAuracronPCGCustomNodeFactory(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCustomNodeFactory);
UAuracronPCGCustomNodeFactory::~UAuracronPCGCustomNodeFactory() {}
// ********** End Class UAuracronPCGCustomNodeFactory **********************************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function AddInputPin ***********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventAddInputPin_Parms
	{
		FAuracronPCGCustomPinDescriptor Pin;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pin_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Pin;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::NewProp_Pin = { "Pin", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddInputPin_Parms, Pin), Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pin_MetaData), NewProp_Pin_MetaData) }; // 3804071926
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddInputPin_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::NewProp_Pin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "AddInputPin", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::AuracronPCGCustomNodeBuilder_eventAddInputPin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::AuracronPCGCustomNodeBuilder_eventAddInputPin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execAddInputPin)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomPinDescriptor,Z_Param_Out_Pin);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->AddInputPin(Z_Param_Out_Pin);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function AddInputPin *************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function AddOutputPin **********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventAddOutputPin_Parms
	{
		FAuracronPCGCustomPinDescriptor Pin;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pin_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Pin;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::NewProp_Pin = { "Pin", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddOutputPin_Parms, Pin), Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pin_MetaData), NewProp_Pin_MetaData) }; // 3804071926
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddOutputPin_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::NewProp_Pin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "AddOutputPin", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::AuracronPCGCustomNodeBuilder_eventAddOutputPin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::AuracronPCGCustomNodeBuilder_eventAddOutputPin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execAddOutputPin)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomPinDescriptor,Z_Param_Out_Pin);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->AddOutputPin(Z_Param_Out_Pin);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function AddOutputPin ************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function AddParameter **********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventAddParameter_Parms
	{
		FAuracronPCGCustomParameterDescriptor Parameter;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Parameter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::NewProp_Parameter = { "Parameter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddParameter_Parms, Parameter), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameter_MetaData), NewProp_Parameter_MetaData) }; // 3935982077
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddParameter_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::NewProp_Parameter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "AddParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::AuracronPCGCustomNodeBuilder_eventAddParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::AuracronPCGCustomNodeBuilder_eventAddParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execAddParameter)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomParameterDescriptor,Z_Param_Out_Parameter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->AddParameter(Z_Param_Out_Parameter);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function AddParameter ************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function AddTag ****************************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventAddTag_Parms
	{
		FString Tag;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddTag_Parms, Tag), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventAddTag_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "AddTag", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::AuracronPCGCustomNodeBuilder_eventAddTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::AuracronPCGCustomNodeBuilder_eventAddTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execAddTag)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->AddTag(Z_Param_Tag);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function AddTag ******************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function BuildNodeClass ********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventBuildNodeClass_Parms
	{
		UClass* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventBuildNodeClass_Parms, ReturnValue), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "BuildNodeClass", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::AuracronPCGCustomNodeBuilder_eventBuildNodeClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::AuracronPCGCustomNodeBuilder_eventBuildNodeClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execBuildNodeClass)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UClass**)Z_Param__Result=P_THIS->BuildNodeClass();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function BuildNodeClass **********************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function BuildNodeInstance *****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventBuildNodeInstance_Parms
	{
		UPCGSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventBuildNodeInstance_Parms, ReturnValue), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "BuildNodeInstance", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::AuracronPCGCustomNodeBuilder_eventBuildNodeInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::AuracronPCGCustomNodeBuilder_eventBuildNodeInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execBuildNodeInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGSettings**)Z_Param__Result=P_THIS->BuildNodeInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function BuildNodeInstance *******************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function BuildTemplate *********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventBuildTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Build methods\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Build methods" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventBuildTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "BuildTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::AuracronPCGCustomNodeBuilder_eventBuildTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::AuracronPCGCustomNodeBuilder_eventBuildTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execBuildTemplate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=P_THIS->BuildTemplate();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function BuildTemplate ***********************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function Reset *****************************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_Reset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_Reset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "Reset", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_Reset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_Reset_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_Reset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_Reset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execReset)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Reset();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function Reset *******************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetAuthor *************************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetAuthor_Parms
	{
		FString Author;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Author_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Author;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::NewProp_Author = { "Author", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetAuthor_Parms, Author), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Author_MetaData), NewProp_Author_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetAuthor_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::NewProp_Author,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetAuthor", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::AuracronPCGCustomNodeBuilder_eventSetAuthor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::AuracronPCGCustomNodeBuilder_eventSetAuthor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetAuthor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Author);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetAuthor(Z_Param_Author);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetAuthor ***************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetBlueprintImplementation ********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetBlueprintImplementation_Parms
	{
		UBlueprint* Blueprint;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Blueprint;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::NewProp_Blueprint = { "Blueprint", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetBlueprintImplementation_Parms, Blueprint), Z_Construct_UClass_UBlueprint_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetBlueprintImplementation_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::NewProp_Blueprint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetBlueprintImplementation", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::AuracronPCGCustomNodeBuilder_eventSetBlueprintImplementation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::AuracronPCGCustomNodeBuilder_eventSetBlueprintImplementation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetBlueprintImplementation)
{
	P_GET_OBJECT(UBlueprint,Z_Param_Blueprint);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetBlueprintImplementation(Z_Param_Blueprint);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetBlueprintImplementation **********

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetCategory ***********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetCategory_Parms
	{
		EAuracronPCGNodeCategory Category;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetCategory_Parms, Category), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, METADATA_PARAMS(0, nullptr) }; // 3952805449
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetCategory_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetCategory", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::AuracronPCGCustomNodeBuilder_eventSetCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::AuracronPCGCustomNodeBuilder_eventSetCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetCategory)
{
	P_GET_ENUM(EAuracronPCGNodeCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetCategory(EAuracronPCGNodeCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetCategory *************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetDescription ********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetDescription_Parms
	{
		FString Description;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetDescription_Parms, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetDescription_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetDescription", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::AuracronPCGCustomNodeBuilder_eventSetDescription_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::AuracronPCGCustomNodeBuilder_eventSetDescription_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetDescription)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Description);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetDescription(Z_Param_Description);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetDescription **********************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetDisplayName ********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetDisplayName_Parms
	{
		FString DisplayName;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetDisplayName_Parms, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetDisplayName_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetDisplayName", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::AuracronPCGCustomNodeBuilder_eventSetDisplayName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::AuracronPCGCustomNodeBuilder_eventSetDisplayName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetDisplayName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DisplayName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetDisplayName(Z_Param_DisplayName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetDisplayName **********************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetExecutionFunction **************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetExecutionFunction_Parms
	{
		FString FunctionName;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetExecutionFunction_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetExecutionFunction_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetExecutionFunction", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::AuracronPCGCustomNodeBuilder_eventSetExecutionFunction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::AuracronPCGCustomNodeBuilder_eventSetExecutionFunction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetExecutionFunction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetExecutionFunction(Z_Param_FunctionName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetExecutionFunction ****************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetExecutionMode ******************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetExecutionMode_Parms
	{
		EAuracronPCGCustomNodeExecutionMode ExecutionMode;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExecutionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExecutionMode;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::NewProp_ExecutionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::NewProp_ExecutionMode = { "ExecutionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetExecutionMode_Parms, ExecutionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode, METADATA_PARAMS(0, nullptr) }; // 1270598183
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetExecutionMode_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::NewProp_ExecutionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::NewProp_ExecutionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetExecutionMode", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::AuracronPCGCustomNodeBuilder_eventSetExecutionMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::AuracronPCGCustomNodeBuilder_eventSetExecutionMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetExecutionMode)
{
	P_GET_ENUM(EAuracronPCGCustomNodeExecutionMode,Z_Param_ExecutionMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetExecutionMode(EAuracronPCGCustomNodeExecutionMode(Z_Param_ExecutionMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetExecutionMode ********************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetNativeClassName ****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetNativeClassName_Parms
	{
		FString ClassName;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClassName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClassName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::NewProp_ClassName = { "ClassName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetNativeClassName_Parms, ClassName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClassName_MetaData), NewProp_ClassName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetNativeClassName_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::NewProp_ClassName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetNativeClassName", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::AuracronPCGCustomNodeBuilder_eventSetNativeClassName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::AuracronPCGCustomNodeBuilder_eventSetNativeClassName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetNativeClassName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ClassName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetNativeClassName(Z_Param_ClassName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetNativeClassName ******************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetNodeColor **********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetNodeColor_Parms
	{
		FLinearColor Color;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetNodeColor_Parms, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetNodeColor_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetNodeColor", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::AuracronPCGCustomNodeBuilder_eventSetNodeColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::AuracronPCGCustomNodeBuilder_eventSetNodeColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetNodeColor)
{
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Color);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetNodeColor(Z_Param_Out_Color);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetNodeColor ************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetNodeName ***********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetNodeName_Parms
	{
		FString Name;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Builder pattern methods\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Builder pattern methods" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetNodeName_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetNodeName_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetNodeName", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::AuracronPCGCustomNodeBuilder_eventSetNodeName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::AuracronPCGCustomNodeBuilder_eventSetNodeName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetNodeName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetNodeName(Z_Param_Name);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetNodeName *************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetTemplateType *******************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetTemplateType_Parms
	{
		EAuracronPCGCustomNodeTemplateType TemplateType;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TemplateType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TemplateType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::NewProp_TemplateType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::NewProp_TemplateType = { "TemplateType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetTemplateType_Parms, TemplateType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType, METADATA_PARAMS(0, nullptr) }; // 141862105
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetTemplateType_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::NewProp_TemplateType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::NewProp_TemplateType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetTemplateType", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::AuracronPCGCustomNodeBuilder_eventSetTemplateType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::AuracronPCGCustomNodeBuilder_eventSetTemplateType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetTemplateType)
{
	P_GET_ENUM(EAuracronPCGCustomNodeTemplateType,Z_Param_TemplateType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetTemplateType(EAuracronPCGCustomNodeTemplateType(Z_Param_TemplateType));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetTemplateType *********************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetValidationLevel ****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetValidationLevel_Parms
	{
		EAuracronPCGCustomNodeValidationLevel ValidationLevel;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationLevel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::NewProp_ValidationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::NewProp_ValidationLevel = { "ValidationLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetValidationLevel_Parms, ValidationLevel), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeValidationLevel, METADATA_PARAMS(0, nullptr) }; // 64341457
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetValidationLevel_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::NewProp_ValidationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::NewProp_ValidationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetValidationLevel", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::AuracronPCGCustomNodeBuilder_eventSetValidationLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::AuracronPCGCustomNodeBuilder_eventSetValidationLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetValidationLevel)
{
	P_GET_ENUM(EAuracronPCGCustomNodeValidationLevel,Z_Param_ValidationLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetValidationLevel(EAuracronPCGCustomNodeValidationLevel(Z_Param_ValidationLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetValidationLevel ******************

// ********** Begin Class UAuracronPCGCustomNodeBuilder Function SetVersion ************************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics
{
	struct AuracronPCGCustomNodeBuilder_eventSetVersion_Parms
	{
		FString Version;
		UAuracronPCGCustomNodeBuilder* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Builder" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Version_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Version;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::NewProp_Version = { "Version", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetVersion_Parms, Version), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Version_MetaData), NewProp_Version_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeBuilder_eventSetVersion_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::NewProp_Version,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, nullptr, "SetVersion", Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::AuracronPCGCustomNodeBuilder_eventSetVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::AuracronPCGCustomNodeBuilder_eventSetVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeBuilder::execSetVersion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Version);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeBuilder**)Z_Param__Result=P_THIS->SetVersion(Z_Param_Version);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeBuilder Function SetVersion **************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder ********************************************
void UAuracronPCGCustomNodeBuilder::StaticRegisterNativesUAuracronPCGCustomNodeBuilder()
{
	UClass* Class = UAuracronPCGCustomNodeBuilder::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddInputPin", &UAuracronPCGCustomNodeBuilder::execAddInputPin },
		{ "AddOutputPin", &UAuracronPCGCustomNodeBuilder::execAddOutputPin },
		{ "AddParameter", &UAuracronPCGCustomNodeBuilder::execAddParameter },
		{ "AddTag", &UAuracronPCGCustomNodeBuilder::execAddTag },
		{ "BuildNodeClass", &UAuracronPCGCustomNodeBuilder::execBuildNodeClass },
		{ "BuildNodeInstance", &UAuracronPCGCustomNodeBuilder::execBuildNodeInstance },
		{ "BuildTemplate", &UAuracronPCGCustomNodeBuilder::execBuildTemplate },
		{ "Reset", &UAuracronPCGCustomNodeBuilder::execReset },
		{ "SetAuthor", &UAuracronPCGCustomNodeBuilder::execSetAuthor },
		{ "SetBlueprintImplementation", &UAuracronPCGCustomNodeBuilder::execSetBlueprintImplementation },
		{ "SetCategory", &UAuracronPCGCustomNodeBuilder::execSetCategory },
		{ "SetDescription", &UAuracronPCGCustomNodeBuilder::execSetDescription },
		{ "SetDisplayName", &UAuracronPCGCustomNodeBuilder::execSetDisplayName },
		{ "SetExecutionFunction", &UAuracronPCGCustomNodeBuilder::execSetExecutionFunction },
		{ "SetExecutionMode", &UAuracronPCGCustomNodeBuilder::execSetExecutionMode },
		{ "SetNativeClassName", &UAuracronPCGCustomNodeBuilder::execSetNativeClassName },
		{ "SetNodeColor", &UAuracronPCGCustomNodeBuilder::execSetNodeColor },
		{ "SetNodeName", &UAuracronPCGCustomNodeBuilder::execSetNodeName },
		{ "SetTemplateType", &UAuracronPCGCustomNodeBuilder::execSetTemplateType },
		{ "SetValidationLevel", &UAuracronPCGCustomNodeBuilder::execSetValidationLevel },
		{ "SetVersion", &UAuracronPCGCustomNodeBuilder::execSetVersion },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder;
UClass* UAuracronPCGCustomNodeBuilder::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCustomNodeBuilder;
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCustomNodeBuilder"),
			Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCustomNodeBuilder,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister()
{
	return UAuracronPCGCustomNodeBuilder::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Node Builder\n * Builder pattern for creating custom nodes step by step\n */" },
#endif
		{ "IncludePath", "AuracronPCGCustomNodeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Node Builder\nBuilder pattern for creating custom nodes step by step" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTemplate_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentTemplate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddInputPin, "AddInputPin" }, // 211297596
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddOutputPin, "AddOutputPin" }, // 2121043217
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddParameter, "AddParameter" }, // 2225448802
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_AddTag, "AddTag" }, // 1878621010
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeClass, "BuildNodeClass" }, // 2473183208
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildNodeInstance, "BuildNodeInstance" }, // 3609268587
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_BuildTemplate, "BuildTemplate" }, // 4170953442
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_Reset, "Reset" }, // 376494948
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetAuthor, "SetAuthor" }, // 2821704167
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetBlueprintImplementation, "SetBlueprintImplementation" }, // 2660529836
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetCategory, "SetCategory" }, // 4071389853
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDescription, "SetDescription" }, // 2874447363
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetDisplayName, "SetDisplayName" }, // 3668044511
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionFunction, "SetExecutionFunction" }, // 3024084264
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetExecutionMode, "SetExecutionMode" }, // 1337651075
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNativeClassName, "SetNativeClassName" }, // 1695283981
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeColor, "SetNodeColor" }, // 3684188820
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetNodeName, "SetNodeName" }, // 1177011509
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetTemplateType, "SetTemplateType" }, // 3159659195
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetValidationLevel, "SetValidationLevel" }, // 1263834431
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeBuilder_SetVersion, "SetVersion" }, // 3773055723
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCustomNodeBuilder>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::NewProp_CurrentTemplate = { "CurrentTemplate", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCustomNodeBuilder, CurrentTemplate), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTemplate_MetaData), NewProp_CurrentTemplate_MetaData) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::NewProp_CurrentTemplate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::ClassParams = {
	&UAuracronPCGCustomNodeBuilder::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeBuilder()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder.OuterSingleton, Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCustomNodeBuilder);
UAuracronPCGCustomNodeBuilder::~UAuracronPCGCustomNodeBuilder() {}
// ********** End Class UAuracronPCGCustomNodeBuilder **********************************************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function ClearRegistry ********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ClearRegistry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ClearRegistry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "ClearRegistry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ClearRegistry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ClearRegistry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ClearRegistry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ClearRegistry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execClearRegistry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearRegistry();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function ClearRegistry **********************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function CreateNodeInstance ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventCreateNodeInstance_Parms
	{
		FString TemplateName;
		UPCGSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node instance management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node instance management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TemplateName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventCreateNodeInstance_Parms, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventCreateNodeInstance_Parms, ReturnValue), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "CreateNodeInstance", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::AuracronPCGCustomNodeRegistry_eventCreateNodeInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::AuracronPCGCustomNodeRegistry_eventCreateNodeInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execCreateNodeInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TemplateName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGSettings**)Z_Param__Result=P_THIS->CreateNodeInstance(Z_Param_TemplateName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function CreateNodeInstance *****************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function GetAllTemplates ******************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventGetAllTemplates_Parms
	{
		TArray<FAuracronPCGCustomNodeTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetAllTemplates_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "GetAllTemplates", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::AuracronPCGCustomNodeRegistry_eventGetAllTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::AuracronPCGCustomNodeRegistry_eventGetAllTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execGetAllTemplates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGCustomNodeTemplate>*)Z_Param__Result=P_THIS->GetAllTemplates();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function GetAllTemplates ********************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function GetInstance **********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventGetInstance_Parms
	{
		UAuracronPCGCustomNodeRegistry* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Registry management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registry management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::AuracronPCGCustomNodeRegistry_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::AuracronPCGCustomNodeRegistry_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGCustomNodeRegistry**)Z_Param__Result=UAuracronPCGCustomNodeRegistry::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function GetInstance ************************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function GetNodeInstance ******************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventGetNodeInstance_Parms
	{
		FString InstanceName;
		UPCGSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::NewProp_InstanceName = { "InstanceName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetNodeInstance_Parms, InstanceName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceName_MetaData), NewProp_InstanceName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetNodeInstance_Parms, ReturnValue), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::NewProp_InstanceName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "GetNodeInstance", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::AuracronPCGCustomNodeRegistry_eventGetNodeInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::AuracronPCGCustomNodeRegistry_eventGetNodeInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execGetNodeInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGSettings**)Z_Param__Result=P_THIS->GetNodeInstance(Z_Param_InstanceName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function GetNodeInstance ********************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function GetTemplate **********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventGetTemplate_Parms
	{
		FString TemplateName;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TemplateName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetTemplate_Parms, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "GetTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execGetTemplate)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TemplateName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=P_THIS->GetTemplate(Z_Param_TemplateName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function GetTemplate ************************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function GetTemplateNames *****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventGetTemplateNames_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetTemplateNames_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "GetTemplateNames", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplateNames_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplateNames_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execGetTemplateNames)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetTemplateNames();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function GetTemplateNames *******************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function GetTemplatesByCategory ***********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventGetTemplatesByCategory_Parms
	{
		EAuracronPCGNodeCategory Category;
		TArray<FAuracronPCGCustomNodeTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetTemplatesByCategory_Parms, Category), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, METADATA_PARAMS(0, nullptr) }; // 3952805449
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetTemplatesByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "GetTemplatesByCategory", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplatesByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplatesByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execGetTemplatesByCategory)
{
	P_GET_ENUM(EAuracronPCGNodeCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGCustomNodeTemplate>*)Z_Param__Result=P_THIS->GetTemplatesByCategory(EAuracronPCGNodeCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function GetTemplatesByCategory *************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function GetTemplatesByType ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventGetTemplatesByType_Parms
	{
		EAuracronPCGCustomNodeTemplateType TemplateType;
		TArray<FAuracronPCGCustomNodeTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TemplateType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TemplateType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_TemplateType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_TemplateType = { "TemplateType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetTemplatesByType_Parms, TemplateType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType, METADATA_PARAMS(0, nullptr) }; // 141862105
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventGetTemplatesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_TemplateType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_TemplateType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "GetTemplatesByType", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplatesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::AuracronPCGCustomNodeRegistry_eventGetTemplatesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execGetTemplatesByType)
{
	P_GET_ENUM(EAuracronPCGCustomNodeTemplateType,Z_Param_TemplateType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGCustomNodeTemplate>*)Z_Param__Result=P_THIS->GetTemplatesByType(EAuracronPCGCustomNodeTemplateType(Z_Param_TemplateType));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function GetTemplatesByType *****************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function IsTemplateRegistered *************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventIsTemplateRegistered_Parms
	{
		FString TemplateName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TemplateName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventIsTemplateRegistered_Parms, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeRegistry_eventIsTemplateRegistered_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeRegistry_eventIsTemplateRegistered_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "IsTemplateRegistered", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::AuracronPCGCustomNodeRegistry_eventIsTemplateRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::AuracronPCGCustomNodeRegistry_eventIsTemplateRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execIsTemplateRegistered)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TemplateName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTemplateRegistered(Z_Param_TemplateName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function IsTemplateRegistered ***************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function RefreshRegistry ******************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RefreshRegistry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RefreshRegistry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "RefreshRegistry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RefreshRegistry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RefreshRegistry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RefreshRegistry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RefreshRegistry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execRefreshRegistry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RefreshRegistry();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function RefreshRegistry ********************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function RegisterNodeInstance *************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventRegisterNodeInstance_Parms
	{
		FString InstanceName;
		UPCGSettings* NodeInstance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NodeInstance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_InstanceName = { "InstanceName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventRegisterNodeInstance_Parms, InstanceName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceName_MetaData), NewProp_InstanceName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_NodeInstance = { "NodeInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventRegisterNodeInstance_Parms, NodeInstance), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeRegistry_eventRegisterNodeInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeRegistry_eventRegisterNodeInstance_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_InstanceName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_NodeInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "RegisterNodeInstance", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::AuracronPCGCustomNodeRegistry_eventRegisterNodeInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::AuracronPCGCustomNodeRegistry_eventRegisterNodeInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execRegisterNodeInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceName);
	P_GET_OBJECT(UPCGSettings,Z_Param_NodeInstance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterNodeInstance(Z_Param_InstanceName,Z_Param_NodeInstance);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function RegisterNodeInstance ***************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function RegisterTemplate *****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventRegisterTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventRegisterTemplate_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
void Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeRegistry_eventRegisterTemplate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeRegistry_eventRegisterTemplate_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "RegisterTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::AuracronPCGCustomNodeRegistry_eventRegisterTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::AuracronPCGCustomNodeRegistry_eventRegisterTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execRegisterTemplate)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterTemplate(Z_Param_Out_Template);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function RegisterTemplate *******************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function UnregisterTemplate ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventUnregisterTemplate_Parms
	{
		FString TemplateName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TemplateName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventUnregisterTemplate_Parms, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeRegistry_eventUnregisterTemplate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeRegistry_eventUnregisterTemplate_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "UnregisterTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::AuracronPCGCustomNodeRegistry_eventUnregisterTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::AuracronPCGCustomNodeRegistry_eventUnregisterTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execUnregisterTemplate)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TemplateName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterTemplate(Z_Param_TemplateName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function UnregisterTemplate *****************

// ********** Begin Class UAuracronPCGCustomNodeRegistry Function ValidateAllTemplates *************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics
{
	struct AuracronPCGCustomNodeRegistry_eventValidateAllTemplates_Parms
	{
		TArray<FString> OutErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation and utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_OutErrors_Inner = { "OutErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_OutErrors = { "OutErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeRegistry_eventValidateAllTemplates_Parms, OutErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeRegistry_eventValidateAllTemplates_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeRegistry_eventValidateAllTemplates_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_OutErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_OutErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, nullptr, "ValidateAllTemplates", Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::AuracronPCGCustomNodeRegistry_eventValidateAllTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::AuracronPCGCustomNodeRegistry_eventValidateAllTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeRegistry::execValidateAllTemplates)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_OutErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateAllTemplates(Z_Param_Out_OutErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeRegistry Function ValidateAllTemplates ***************

// ********** Begin Class UAuracronPCGCustomNodeRegistry *******************************************
void UAuracronPCGCustomNodeRegistry::StaticRegisterNativesUAuracronPCGCustomNodeRegistry()
{
	UClass* Class = UAuracronPCGCustomNodeRegistry::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearRegistry", &UAuracronPCGCustomNodeRegistry::execClearRegistry },
		{ "CreateNodeInstance", &UAuracronPCGCustomNodeRegistry::execCreateNodeInstance },
		{ "GetAllTemplates", &UAuracronPCGCustomNodeRegistry::execGetAllTemplates },
		{ "GetInstance", &UAuracronPCGCustomNodeRegistry::execGetInstance },
		{ "GetNodeInstance", &UAuracronPCGCustomNodeRegistry::execGetNodeInstance },
		{ "GetTemplate", &UAuracronPCGCustomNodeRegistry::execGetTemplate },
		{ "GetTemplateNames", &UAuracronPCGCustomNodeRegistry::execGetTemplateNames },
		{ "GetTemplatesByCategory", &UAuracronPCGCustomNodeRegistry::execGetTemplatesByCategory },
		{ "GetTemplatesByType", &UAuracronPCGCustomNodeRegistry::execGetTemplatesByType },
		{ "IsTemplateRegistered", &UAuracronPCGCustomNodeRegistry::execIsTemplateRegistered },
		{ "RefreshRegistry", &UAuracronPCGCustomNodeRegistry::execRefreshRegistry },
		{ "RegisterNodeInstance", &UAuracronPCGCustomNodeRegistry::execRegisterNodeInstance },
		{ "RegisterTemplate", &UAuracronPCGCustomNodeRegistry::execRegisterTemplate },
		{ "UnregisterTemplate", &UAuracronPCGCustomNodeRegistry::execUnregisterTemplate },
		{ "ValidateAllTemplates", &UAuracronPCGCustomNodeRegistry::execValidateAllTemplates },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry;
UClass* UAuracronPCGCustomNodeRegistry::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCustomNodeRegistry;
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCustomNodeRegistry"),
			Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCustomNodeRegistry,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_NoRegister()
{
	return UAuracronPCGCustomNodeRegistry::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Node Registry\n * Registry for managing custom node templates and instances\n */" },
#endif
		{ "IncludePath", "AuracronPCGCustomNodeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Node Registry\nRegistry for managing custom node templates and instances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Templates_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeInstances_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Templates_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Templates_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Templates;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NodeInstances_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeInstances_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NodeInstances;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ClearRegistry, "ClearRegistry" }, // 3920640497
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_CreateNodeInstance, "CreateNodeInstance" }, // 12689217
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetAllTemplates, "GetAllTemplates" }, // 2939292260
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetInstance, "GetInstance" }, // 2510019258
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetNodeInstance, "GetNodeInstance" }, // 3937345138
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplate, "GetTemplate" }, // 2774806998
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplateNames, "GetTemplateNames" }, // 1611961470
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByCategory, "GetTemplatesByCategory" }, // 4174626756
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_GetTemplatesByType, "GetTemplatesByType" }, // 2087292264
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_IsTemplateRegistered, "IsTemplateRegistered" }, // 2238050412
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RefreshRegistry, "RefreshRegistry" }, // 377755889
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterNodeInstance, "RegisterNodeInstance" }, // 3846750906
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_RegisterTemplate, "RegisterTemplate" }, // 3118967786
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_UnregisterTemplate, "UnregisterTemplate" }, // 986030257
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeRegistry_ValidateAllTemplates, "ValidateAllTemplates" }, // 3803983146
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCustomNodeRegistry>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_Templates_ValueProp = { "Templates", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_Templates_Key_KeyProp = { "Templates_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_Templates = { "Templates", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCustomNodeRegistry, Templates), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Templates_MetaData), NewProp_Templates_MetaData) }; // 3551892101
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_NodeInstances_ValueProp = { "NodeInstances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_NodeInstances_Key_KeyProp = { "NodeInstances_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_NodeInstances = { "NodeInstances", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGCustomNodeRegistry, NodeInstances), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeInstances_MetaData), NewProp_NodeInstances_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_Templates_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_Templates_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_Templates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_NodeInstances_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_NodeInstances_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::NewProp_NodeInstances,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::ClassParams = {
	&UAuracronPCGCustomNodeRegistry::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeRegistry()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry.OuterSingleton, Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry.OuterSingleton;
}
UAuracronPCGCustomNodeRegistry::UAuracronPCGCustomNodeRegistry(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCustomNodeRegistry);
UAuracronPCGCustomNodeRegistry::~UAuracronPCGCustomNodeRegistry() {}
// ********** End Class UAuracronPCGCustomNodeRegistry *********************************************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateBasicTemplate *****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateBasicTemplate_Parms
	{
		FString NodeName;
		EAuracronPCGCustomNodeTemplateType TemplateType;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TemplateType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TemplateType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_NodeName = { "NodeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateBasicTemplate_Parms, NodeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeName_MetaData), NewProp_NodeName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_TemplateType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_TemplateType = { "TemplateType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateBasicTemplate_Parms, TemplateType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeTemplateType, METADATA_PARAMS(0, nullptr) }; // 141862105
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateBasicTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_NodeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_TemplateType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_TemplateType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateBasicTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::AuracronPCGCustomNodeUtils_eventCreateBasicTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::AuracronPCGCustomNodeUtils_eventCreateBasicTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateBasicTemplate)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeName);
	P_GET_ENUM(EAuracronPCGCustomNodeTemplateType,Z_Param_TemplateType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateBasicTemplate(Z_Param_NodeName,EAuracronPCGCustomNodeTemplateType(Z_Param_TemplateType));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateBasicTemplate *******************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateBoolParameter *****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateBoolParameter_Parms
	{
		FString Name;
		bool DefaultValue;
		FAuracronPCGCustomParameterDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static void NewProp_DefaultValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateBoolParameter_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_DefaultValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeUtils_eventCreateBoolParameter_Parms*)Obj)->DefaultValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeUtils_eventCreateBoolParameter_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_DefaultValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateBoolParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateBoolParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateBoolParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateBoolParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateBoolParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_UBOOL(Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomParameterDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateBoolParameter(Z_Param_Name,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateBoolParameter *******************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateColorParameter ****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateColorParameter_Parms
	{
		FString Name;
		FLinearColor DefaultValue;
		FAuracronPCGCustomParameterDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateColorParameter_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateColorParameter_Parms, DefaultValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateColorParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateColorParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateColorParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateColorParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateColorParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomParameterDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateColorParameter(Z_Param_Name,Z_Param_Out_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateColorParameter ******************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateEnumParameter *****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateEnumParameter_Parms
	{
		FString Name;
		TArray<FString> EnumValues;
		FString DefaultValue;
		FAuracronPCGCustomParameterDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnumValues_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnumValues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnumValues;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateEnumParameter_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_EnumValues_Inner = { "EnumValues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_EnumValues = { "EnumValues", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateEnumParameter_Parms, EnumValues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnumValues_MetaData), NewProp_EnumValues_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateEnumParameter_Parms, DefaultValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateEnumParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_EnumValues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_EnumValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateEnumParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateEnumParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateEnumParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateEnumParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_TARRAY_REF(FString,Z_Param_Out_EnumValues);
	P_GET_PROPERTY(FStrProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomParameterDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateEnumParameter(Z_Param_Name,Z_Param_Out_EnumValues,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateEnumParameter *******************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateFloatParameter ****************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms
	{
		FString Name;
		float DefaultValue;
		float MinValue;
		float MaxValue;
		FAuracronPCGCustomParameterDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "CPP_Default_MaxValue", "1.000000" },
		{ "CPP_Default_MinValue", "0.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms, DefaultValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_MinValue = { "MinValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms, MinValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms, MaxValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_MinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateFloatParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateFloatParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateFloatParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DefaultValue);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinValue);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomParameterDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateFloatParameter(Z_Param_Name,Z_Param_DefaultValue,Z_Param_MinValue,Z_Param_MaxValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateFloatParameter ******************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateInputPin **********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms
	{
		FString Name;
		EPCGDataType DataType;
		bool bRequired;
		FAuracronPCGCustomPinDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "CPP_Default_bRequired", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FUInt32PropertyParams NewProp_DataType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DataType;
	static void NewProp_bRequired_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequired;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FUInt32PropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_DataType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::UInt32, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_DataType = { "DataType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms, DataType), Z_Construct_UEnum_PCG_EPCGDataType, METADATA_PARAMS(0, nullptr) }; // 2498721046
void Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_bRequired_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms*)Obj)->bRequired = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_bRequired = { "bRequired", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_bRequired_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor, METADATA_PARAMS(0, nullptr) }; // 3804071926
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_DataType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_DataType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_bRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateInputPin", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::AuracronPCGCustomNodeUtils_eventCreateInputPin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateInputPin)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_ENUM(EPCGDataType,Z_Param_DataType);
	P_GET_UBOOL(Z_Param_bRequired);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomPinDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateInputPin(Z_Param_Name,EPCGDataType(Z_Param_DataType),Z_Param_bRequired);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateInputPin ************************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateIntParameter ******************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms
	{
		FString Name;
		int32 DefaultValue;
		int32 MinValue;
		int32 MaxValue;
		FAuracronPCGCustomParameterDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "CPP_Default_MaxValue", "100" },
		{ "CPP_Default_MinValue", "0" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinValue;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms, DefaultValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_MinValue = { "MinValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms, MinValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms, MaxValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_MinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateIntParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateIntParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateIntParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_PROPERTY(FIntProperty,Z_Param_DefaultValue);
	P_GET_PROPERTY(FIntProperty,Z_Param_MinValue);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomParameterDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateIntParameter(Z_Param_Name,Z_Param_DefaultValue,Z_Param_MinValue,Z_Param_MaxValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateIntParameter ********************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateOutputPin *********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateOutputPin_Parms
	{
		FString Name;
		EPCGDataType DataType;
		FAuracronPCGCustomPinDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FUInt32PropertyParams NewProp_DataType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DataType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateOutputPin_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FUInt32PropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_DataType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::UInt32, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_DataType = { "DataType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateOutputPin_Parms, DataType), Z_Construct_UEnum_PCG_EPCGDataType, METADATA_PARAMS(0, nullptr) }; // 2498721046
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateOutputPin_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor, METADATA_PARAMS(0, nullptr) }; // 3804071926
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_DataType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_DataType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateOutputPin", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::AuracronPCGCustomNodeUtils_eventCreateOutputPin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::AuracronPCGCustomNodeUtils_eventCreateOutputPin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateOutputPin)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_ENUM(EPCGDataType,Z_Param_DataType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomPinDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateOutputPin(Z_Param_Name,EPCGDataType(Z_Param_DataType));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateOutputPin ***********************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateStringParameter ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateStringParameter_Parms
	{
		FString Name;
		FString DefaultValue;
		FAuracronPCGCustomParameterDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateStringParameter_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateStringParameter_Parms, DefaultValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateStringParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateStringParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateStringParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateStringParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateStringParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_PROPERTY(FStrProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomParameterDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateStringParameter(Z_Param_Name,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateStringParameter *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function CreateVectorParameter ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics
{
	struct AuracronPCGCustomNodeUtils_eventCreateVectorParameter_Parms
	{
		FString Name;
		FVector DefaultValue;
		FAuracronPCGCustomParameterDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateVectorParameter_Parms, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateVectorParameter_Parms, DefaultValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventCreateVectorParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 3935982077
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "CreateVectorParameter", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateVectorParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::AuracronPCGCustomNodeUtils_eventCreateVectorParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execCreateVectorParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Name);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomParameterDescriptor*)Z_Param__Result=UAuracronPCGCustomNodeUtils::CreateVectorParameter(Z_Param_Name,Z_Param_Out_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function CreateVectorParameter *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function ExecutionModeToString ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics
{
	struct AuracronPCGCustomNodeUtils_eventExecutionModeToString_Parms
	{
		EAuracronPCGCustomNodeExecutionMode ExecutionMode;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExecutionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExecutionMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::NewProp_ExecutionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::NewProp_ExecutionMode = { "ExecutionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventExecutionModeToString_Parms, ExecutionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode, METADATA_PARAMS(0, nullptr) }; // 1270598183
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventExecutionModeToString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::NewProp_ExecutionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::NewProp_ExecutionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "ExecutionModeToString", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::AuracronPCGCustomNodeUtils_eventExecutionModeToString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::AuracronPCGCustomNodeUtils_eventExecutionModeToString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execExecutionModeToString)
{
	P_GET_ENUM(EAuracronPCGCustomNodeExecutionMode,Z_Param_ExecutionMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGCustomNodeUtils::ExecutionModeToString(EAuracronPCGCustomNodeExecutionMode(Z_Param_ExecutionMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function ExecutionModeToString *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function GenerateTemplateFromBlueprint *******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics
{
	struct AuracronPCGCustomNodeUtils_eventGenerateTemplateFromBlueprint_Parms
	{
		UBlueprint* Blueprint;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template generation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template generation utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Blueprint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::NewProp_Blueprint = { "Blueprint", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventGenerateTemplateFromBlueprint_Parms, Blueprint), Z_Construct_UClass_UBlueprint_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventGenerateTemplateFromBlueprint_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::NewProp_Blueprint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "GenerateTemplateFromBlueprint", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::AuracronPCGCustomNodeUtils_eventGenerateTemplateFromBlueprint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::AuracronPCGCustomNodeUtils_eventGenerateTemplateFromBlueprint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execGenerateTemplateFromBlueprint)
{
	P_GET_OBJECT(UBlueprint,Z_Param_Blueprint);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeUtils::GenerateTemplateFromBlueprint(Z_Param_Blueprint);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function GenerateTemplateFromBlueprint *********

// ********** Begin Class UAuracronPCGCustomNodeUtils Function GenerateTemplateFromClass ***********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics
{
	struct AuracronPCGCustomNodeUtils_eventGenerateTemplateFromClass_Parms
	{
		UClass* NodeClass;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventGenerateTemplateFromClass_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventGenerateTemplateFromClass_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "GenerateTemplateFromClass", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::AuracronPCGCustomNodeUtils_eventGenerateTemplateFromClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::AuracronPCGCustomNodeUtils_eventGenerateTemplateFromClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execGenerateTemplateFromClass)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeUtils::GenerateTemplateFromClass(Z_Param_NodeClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function GenerateTemplateFromClass *************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function GenerateTemplatesFromDirectory ******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics
{
	struct AuracronPCGCustomNodeUtils_eventGenerateTemplatesFromDirectory_Parms
	{
		FString Directory;
		TArray<FAuracronPCGCustomNodeTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Directory_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Directory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::NewProp_Directory = { "Directory", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventGenerateTemplatesFromDirectory_Parms, Directory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Directory_MetaData), NewProp_Directory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventGenerateTemplatesFromDirectory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::NewProp_Directory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "GenerateTemplatesFromDirectory", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::AuracronPCGCustomNodeUtils_eventGenerateTemplatesFromDirectory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::AuracronPCGCustomNodeUtils_eventGenerateTemplatesFromDirectory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execGenerateTemplatesFromDirectory)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Directory);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGCustomNodeTemplate>*)Z_Param__Result=UAuracronPCGCustomNodeUtils::GenerateTemplatesFromDirectory(Z_Param_Directory);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function GenerateTemplatesFromDirectory ********

// ********** Begin Class UAuracronPCGCustomNodeUtils Function ParameterTypeToString ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics
{
	struct AuracronPCGCustomNodeUtils_eventParameterTypeToString_Parms
	{
		EAuracronPCGCustomParameterType ParameterType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Conversion utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conversion utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ParameterType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ParameterType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::NewProp_ParameterType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::NewProp_ParameterType = { "ParameterType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventParameterTypeToString_Parms, ParameterType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType, METADATA_PARAMS(0, nullptr) }; // 1387726243
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventParameterTypeToString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::NewProp_ParameterType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::NewProp_ParameterType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "ParameterTypeToString", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::AuracronPCGCustomNodeUtils_eventParameterTypeToString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::AuracronPCGCustomNodeUtils_eventParameterTypeToString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execParameterTypeToString)
{
	P_GET_ENUM(EAuracronPCGCustomParameterType,Z_Param_ParameterType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGCustomNodeUtils::ParameterTypeToString(EAuracronPCGCustomParameterType(Z_Param_ParameterType));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function ParameterTypeToString *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function SanitizeNodeName ********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics
{
	struct AuracronPCGCustomNodeUtils_eventSanitizeNodeName_Parms
	{
		FString NodeName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::NewProp_NodeName = { "NodeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventSanitizeNodeName_Parms, NodeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeName_MetaData), NewProp_NodeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventSanitizeNodeName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::NewProp_NodeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "SanitizeNodeName", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::AuracronPCGCustomNodeUtils_eventSanitizeNodeName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::AuracronPCGCustomNodeUtils_eventSanitizeNodeName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execSanitizeNodeName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGCustomNodeUtils::SanitizeNodeName(Z_Param_NodeName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function SanitizeNodeName **********************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function SanitizeParameterName ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics
{
	struct AuracronPCGCustomNodeUtils_eventSanitizeParameterName_Parms
	{
		FString ParameterName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventSanitizeParameterName_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventSanitizeParameterName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "SanitizeParameterName", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::AuracronPCGCustomNodeUtils_eventSanitizeParameterName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::AuracronPCGCustomNodeUtils_eventSanitizeParameterName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execSanitizeParameterName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGCustomNodeUtils::SanitizeParameterName(Z_Param_ParameterName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function SanitizeParameterName *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function SanitizePinName *********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics
{
	struct AuracronPCGCustomNodeUtils_eventSanitizePinName_Parms
	{
		FString PinName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PinName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::NewProp_PinName = { "PinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventSanitizePinName_Parms, PinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinName_MetaData), NewProp_PinName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventSanitizePinName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::NewProp_PinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "SanitizePinName", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::AuracronPCGCustomNodeUtils_eventSanitizePinName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::AuracronPCGCustomNodeUtils_eventSanitizePinName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execSanitizePinName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PinName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGCustomNodeUtils::SanitizePinName(Z_Param_PinName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function SanitizePinName ***********************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function StringToExecutionMode ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics
{
	struct AuracronPCGCustomNodeUtils_eventStringToExecutionMode_Parms
	{
		FString ModeString;
		EAuracronPCGCustomNodeExecutionMode ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModeString_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModeString;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::NewProp_ModeString = { "ModeString", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventStringToExecutionMode_Parms, ModeString), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModeString_MetaData), NewProp_ModeString_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventStringToExecutionMode_Parms, ReturnValue), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomNodeExecutionMode, METADATA_PARAMS(0, nullptr) }; // 1270598183
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::NewProp_ModeString,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "StringToExecutionMode", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::AuracronPCGCustomNodeUtils_eventStringToExecutionMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::AuracronPCGCustomNodeUtils_eventStringToExecutionMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execStringToExecutionMode)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModeString);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPCGCustomNodeExecutionMode*)Z_Param__Result=UAuracronPCGCustomNodeUtils::StringToExecutionMode(Z_Param_ModeString);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function StringToExecutionMode *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function StringToParameterType ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics
{
	struct AuracronPCGCustomNodeUtils_eventStringToParameterType_Parms
	{
		FString TypeString;
		EAuracronPCGCustomParameterType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeString_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeString;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::NewProp_TypeString = { "TypeString", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventStringToParameterType_Parms, TypeString), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeString_MetaData), NewProp_TypeString_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventStringToParameterType_Parms, ReturnValue), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGCustomParameterType, METADATA_PARAMS(0, nullptr) }; // 1387726243
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::NewProp_TypeString,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "StringToParameterType", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::AuracronPCGCustomNodeUtils_eventStringToParameterType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::AuracronPCGCustomNodeUtils_eventStringToParameterType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execStringToParameterType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeString);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPCGCustomParameterType*)Z_Param__Result=UAuracronPCGCustomNodeUtils::StringToParameterType(Z_Param_TypeString);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function StringToParameterType *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function ValidateNodeName ********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics
{
	struct AuracronPCGCustomNodeUtils_eventValidateNodeName_Parms
	{
		FString NodeName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::NewProp_NodeName = { "NodeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventValidateNodeName_Parms, NodeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeName_MetaData), NewProp_NodeName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeUtils_eventValidateNodeName_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeUtils_eventValidateNodeName_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::NewProp_NodeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "ValidateNodeName", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::AuracronPCGCustomNodeUtils_eventValidateNodeName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::AuracronPCGCustomNodeUtils_eventValidateNodeName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execValidateNodeName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeUtils::ValidateNodeName(Z_Param_NodeName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function ValidateNodeName **********************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function ValidateParameterName ***************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics
{
	struct AuracronPCGCustomNodeUtils_eventValidateParameterName_Parms
	{
		FString ParameterName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventValidateParameterName_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeUtils_eventValidateParameterName_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeUtils_eventValidateParameterName_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "ValidateParameterName", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::AuracronPCGCustomNodeUtils_eventValidateParameterName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::AuracronPCGCustomNodeUtils_eventValidateParameterName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execValidateParameterName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeUtils::ValidateParameterName(Z_Param_ParameterName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function ValidateParameterName *****************

// ********** Begin Class UAuracronPCGCustomNodeUtils Function ValidatePinName *********************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics
{
	struct AuracronPCGCustomNodeUtils_eventValidatePinName_Parms
	{
		FString PinName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PinName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::NewProp_PinName = { "PinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeUtils_eventValidatePinName_Parms, PinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinName_MetaData), NewProp_PinName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeUtils_eventValidatePinName_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeUtils_eventValidatePinName_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::NewProp_PinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeUtils, nullptr, "ValidatePinName", Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::AuracronPCGCustomNodeUtils_eventValidatePinName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::AuracronPCGCustomNodeUtils_eventValidatePinName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeUtils::execValidatePinName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PinName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeUtils::ValidatePinName(Z_Param_PinName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeUtils Function ValidatePinName ***********************

// ********** Begin Class UAuracronPCGCustomNodeUtils **********************************************
void UAuracronPCGCustomNodeUtils::StaticRegisterNativesUAuracronPCGCustomNodeUtils()
{
	UClass* Class = UAuracronPCGCustomNodeUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateBasicTemplate", &UAuracronPCGCustomNodeUtils::execCreateBasicTemplate },
		{ "CreateBoolParameter", &UAuracronPCGCustomNodeUtils::execCreateBoolParameter },
		{ "CreateColorParameter", &UAuracronPCGCustomNodeUtils::execCreateColorParameter },
		{ "CreateEnumParameter", &UAuracronPCGCustomNodeUtils::execCreateEnumParameter },
		{ "CreateFloatParameter", &UAuracronPCGCustomNodeUtils::execCreateFloatParameter },
		{ "CreateInputPin", &UAuracronPCGCustomNodeUtils::execCreateInputPin },
		{ "CreateIntParameter", &UAuracronPCGCustomNodeUtils::execCreateIntParameter },
		{ "CreateOutputPin", &UAuracronPCGCustomNodeUtils::execCreateOutputPin },
		{ "CreateStringParameter", &UAuracronPCGCustomNodeUtils::execCreateStringParameter },
		{ "CreateVectorParameter", &UAuracronPCGCustomNodeUtils::execCreateVectorParameter },
		{ "ExecutionModeToString", &UAuracronPCGCustomNodeUtils::execExecutionModeToString },
		{ "GenerateTemplateFromBlueprint", &UAuracronPCGCustomNodeUtils::execGenerateTemplateFromBlueprint },
		{ "GenerateTemplateFromClass", &UAuracronPCGCustomNodeUtils::execGenerateTemplateFromClass },
		{ "GenerateTemplatesFromDirectory", &UAuracronPCGCustomNodeUtils::execGenerateTemplatesFromDirectory },
		{ "ParameterTypeToString", &UAuracronPCGCustomNodeUtils::execParameterTypeToString },
		{ "SanitizeNodeName", &UAuracronPCGCustomNodeUtils::execSanitizeNodeName },
		{ "SanitizeParameterName", &UAuracronPCGCustomNodeUtils::execSanitizeParameterName },
		{ "SanitizePinName", &UAuracronPCGCustomNodeUtils::execSanitizePinName },
		{ "StringToExecutionMode", &UAuracronPCGCustomNodeUtils::execStringToExecutionMode },
		{ "StringToParameterType", &UAuracronPCGCustomNodeUtils::execStringToParameterType },
		{ "ValidateNodeName", &UAuracronPCGCustomNodeUtils::execValidateNodeName },
		{ "ValidateParameterName", &UAuracronPCGCustomNodeUtils::execValidateParameterName },
		{ "ValidatePinName", &UAuracronPCGCustomNodeUtils::execValidatePinName },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils;
UClass* UAuracronPCGCustomNodeUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCustomNodeUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCustomNodeUtils"),
			Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCustomNodeUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeUtils_NoRegister()
{
	return UAuracronPCGCustomNodeUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Node Utilities\n * Utility functions for custom node operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGCustomNodeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Node Utilities\nUtility functions for custom node operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBasicTemplate, "CreateBasicTemplate" }, // 1811597132
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateBoolParameter, "CreateBoolParameter" }, // 4185129064
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateColorParameter, "CreateColorParameter" }, // 2645879213
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateEnumParameter, "CreateEnumParameter" }, // 2089070482
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateFloatParameter, "CreateFloatParameter" }, // 3491103520
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateInputPin, "CreateInputPin" }, // 3498890793
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateIntParameter, "CreateIntParameter" }, // 4280435540
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateOutputPin, "CreateOutputPin" }, // 2959487598
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateStringParameter, "CreateStringParameter" }, // 633635814
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_CreateVectorParameter, "CreateVectorParameter" }, // 3810500847
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ExecutionModeToString, "ExecutionModeToString" }, // 872105651
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromBlueprint, "GenerateTemplateFromBlueprint" }, // 2199685263
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplateFromClass, "GenerateTemplateFromClass" }, // 3119697724
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_GenerateTemplatesFromDirectory, "GenerateTemplatesFromDirectory" }, // 3250186579
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ParameterTypeToString, "ParameterTypeToString" }, // 2540150596
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeNodeName, "SanitizeNodeName" }, // 4164131716
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizeParameterName, "SanitizeParameterName" }, // 1320423026
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_SanitizePinName, "SanitizePinName" }, // 541373515
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToExecutionMode, "StringToExecutionMode" }, // 1434724875
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_StringToParameterType, "StringToParameterType" }, // 439443950
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateNodeName, "ValidateNodeName" }, // 2520967585
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidateParameterName, "ValidateParameterName" }, // 1840705546
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeUtils_ValidatePinName, "ValidatePinName" }, // 2203517565
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCustomNodeUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics::ClassParams = {
	&UAuracronPCGCustomNodeUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils.OuterSingleton;
}
UAuracronPCGCustomNodeUtils::UAuracronPCGCustomNodeUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCustomNodeUtils);
UAuracronPCGCustomNodeUtils::~UAuracronPCGCustomNodeUtils() {}
// ********** End Class UAuracronPCGCustomNodeUtils ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGCustomParameterType_StaticEnum, TEXT("EAuracronPCGCustomParameterType"), &Z_Registration_Info_UEnum_EAuracronPCGCustomParameterType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1387726243U) },
		{ EAuracronPCGCustomNodeExecutionMode_StaticEnum, TEXT("EAuracronPCGCustomNodeExecutionMode"), &Z_Registration_Info_UEnum_EAuracronPCGCustomNodeExecutionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1270598183U) },
		{ EAuracronPCGCustomNodeTemplateType_StaticEnum, TEXT("EAuracronPCGCustomNodeTemplateType"), &Z_Registration_Info_UEnum_EAuracronPCGCustomNodeTemplateType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 141862105U) },
		{ EAuracronPCGCustomNodeValidationLevel_StaticEnum, TEXT("EAuracronPCGCustomNodeValidationLevel"), &Z_Registration_Info_UEnum_EAuracronPCGCustomNodeValidationLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 64341457U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGCustomParameterDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics::NewStructOps, TEXT("AuracronPCGCustomParameterDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGCustomParameterDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGCustomParameterDescriptor), 3935982077U) },
		{ FAuracronPCGCustomPinDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics::NewStructOps, TEXT("AuracronPCGCustomPinDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGCustomPinDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGCustomPinDescriptor), 3804071926U) },
		{ FAuracronPCGCustomNodeTemplate::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics::NewStructOps, TEXT("AuracronPCGCustomNodeTemplate"), &Z_Registration_Info_UScriptStruct_FAuracronPCGCustomNodeTemplate, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGCustomNodeTemplate), 3551892101U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGCustomNodeFactory, UAuracronPCGCustomNodeFactory::StaticClass, TEXT("UAuracronPCGCustomNodeFactory"), &Z_Registration_Info_UClass_UAuracronPCGCustomNodeFactory, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCustomNodeFactory), 2688445169U) },
		{ Z_Construct_UClass_UAuracronPCGCustomNodeBuilder, UAuracronPCGCustomNodeBuilder::StaticClass, TEXT("UAuracronPCGCustomNodeBuilder"), &Z_Registration_Info_UClass_UAuracronPCGCustomNodeBuilder, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCustomNodeBuilder), 1616792580U) },
		{ Z_Construct_UClass_UAuracronPCGCustomNodeRegistry, UAuracronPCGCustomNodeRegistry::StaticClass, TEXT("UAuracronPCGCustomNodeRegistry"), &Z_Registration_Info_UClass_UAuracronPCGCustomNodeRegistry, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCustomNodeRegistry), 2715046286U) },
		{ Z_Construct_UClass_UAuracronPCGCustomNodeUtils, UAuracronPCGCustomNodeUtils::StaticClass, TEXT("UAuracronPCGCustomNodeUtils"), &Z_Registration_Info_UClass_UAuracronPCGCustomNodeUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCustomNodeUtils), 2017260339U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_2375518303(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
