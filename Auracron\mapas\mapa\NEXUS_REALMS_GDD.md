# 🌟 AURACRON - GAME DESIGN DOCUMENT _(formerly Nexus Realms)_
**Versão**: 1.0  
**Data**: 27 de Junho de 2025  
**Plataforma**: Mobile (Android/iOS) + PC  
**Engine**: Unity 6.2  

---

## 📋 **ÍNDICE**
1. [Visão Geral](#visão-geral)
2. [Análise Competitiva](#análise-competitiva)
3. [Mecânicas Inovadoras](#mecânicas-inovadoras)
4. [Sistemas Técnicos](#sistemas-técnicos)
5. [Progressão e Monetização](#progressão-e-monetização)
6. [Roadmap de Desenvolvimento](#roadmap-de-desenvolvimento)
7. [Análise de Riscos](#análise-de-riscos)

---

## 🎯 **VISÃO GERAL**

### **Conceito Central**
**AURACRON** é um MOBA 5v5 revolucionário que combina elementos tradicionais com **mapas dinâmicos multidimensionais** e **IA adaptativa**. O diferencial está na capacidade do mapa evoluir durante a partida, criando layers verticais de combate e objetivos procedurais únicos.

### **Público-Alvo**
- **Primário**: Players de MOBA mobile (18-35 anos)
- **Secundário**: Gamers PC buscando inovação no gênero
- **Terciário**: Streamers/criadores de conteúdo

### **Pillars de Design**
1. **📐 EVOLUÇÃO CONSTANTE**: Mapas que mudam, estratégias que adaptam
2. **🎮 ACESSIBILIDADE INTELIGENTE**: Complexo para mestres, simples para iniciantes
3. **🤝 COOPERAÇÃO AMPLIADA**: Mecânicas que recompensam teamwork criativo
4. **⚡ INOVAÇÃO TECNOLÓGICA**: IA, procedural generation, physics avançada

---

## ⚔️ **ANÁLISE COMPETITIVA**

### **Wild Rift vs AURACRON**

| **ASPECTO** | **WILD RIFT** | **AURACRON** | **VANTAGEM** |
|-------------|---------------|---------------|--------------|
| **Mapa** | Estático, 3 lanes fixas | Dinâmico, 3 realms evolutivos | 🟢 **NOSSA** |
| **Combate** | 2D horizontal | 3D vertical (3 layers) | 🟢 **NOSSA** |
| **Objectives** | Fixos e previsíveis | Procedurais + IA adaptativa | 🟢 **NOSSA** |
| **Champions** | 164 fixos | Sistema de fusão temporária | 🟢 **NOSSA** |
| **Jungle** | Spawn patterns fixos | IA que aprende e adapta | 🟢 **NOSSA** |
| **Reconhecimento** | ✅ Brand estabelecido | ❌ Brand novo | 🔴 **DELES** |
| **Player Base** | ✅ 50M+ players | ❌ Zero players | 🔴 **DELES** |
| **Recursos** | ✅ Budget Riot Games | ❌ Indie/Startup | 🔴 **DELES** |

---

## 🚀 **MECÂNICAS INOVADORAS**

### **1. DYNAMIC REALM SYSTEM** 🌍

#### **Como Funciona**
```
TIMELINE DA PARTIDA:
00:00-10:00 → REALM TERRESTRE
├── Mapa tradicional 3-lane
├── Jungle padrão com adaptação IA
└── Foco em laning phase clássico

10:00-15:00 → PORTAL PHASE
├── Portais dimensionais aparecem
├── Escolhas estratégicas de realm
└── Split decisions entre equipes

15:00-20:00 → REALM CELESTIAL
├── Plataformas flutuantes ativam
├── Air champions ganham vantagem
├── Nova layer de combate vertical
└── Objetivos aéreos únicos

20:00-25:00 → REALM ABYSSAL
├── Túneis subterrâneos abrem
├── Stealth champions dominam
├── Underground objectives
└── Rotas de flank secretas

25:00+ → NEXUS FUSION
├── Todos os realms simultâneos
├── Combate 3D completo
├── Máxima complexidade estratégica
└── Endgame épico e único
```

### **2. CHAMPION FUSION SYSTEM** 👥

#### **Mecânica Central**
- **2 jogadores** podem temporariamente fundir seus champions
- **Duração**: 45 segundos
- **Cooldown**: 5 minutos (compartilhado)
- **Limitação**: Máximo 1 fusão ativa por equipe

#### **Tipos de Fusão**
```
TANK + MAGE = BATTLE-MAGE
├── HP Pool combinado
├── AP scaling em defensive abilities
├── Área de controle massiva
└── Exemplo: Malphite + Orianna = Graviton Colossus

ASSASSIN + SUPPORT = PHANTOM GUARDIAN
├── Stealth compartilhado para ally
├── Execution abilities com heal/shield
├── Alta mobilidade + utility
└── Exemplo: Zed + Lulu = Shadow Sprite

ADC + JUNGLE = APEX PREDATOR
├── DPS sustentado + burst windows
├── Objective control supremo
├── Map mobility + team fight presence
└── Exemplo: Jinx + Graves = Arsenal Beast
```

### **3. ADAPTIVE AI JUNGLE** 🤖

#### **Adaptive Elements**
- **Camp Spawns**: Baseado em clear patterns
- **Objective Timing**: Adaptado ao ritmo da partida
- **Creature Behavior**: "Lembram" de encontros anteriores
- **Reward Scaling**: Balanceamento dinâmico baseado em performance

### **4. VERTICAL COMBAT LAYERS** ⬆️

#### **Três Camadas de Combate**
- **Surface Layer**: Combat tradicional
- **Sky Layer**: Voo limitado, air champions
- **Underground**: Stealth routes, ambush points

---

## 🔧 **SISTEMAS TÉCNICOS**

### **Engine & Frameworks**
```
🛠️ TECH STACK:
├── Unity 6.2 (Core Engine)
│   ├── HDRP (Visual fidelity)
│   ├── Addressables (Content streaming)
│   ├── Unity NetCode (Multiplayer)
│   └── ML-Agents (AI Jungle)
├── Backend Services
│   ├── Mirror Networking (Authoritative server)
│   ├── Firebase (User data, analytics)
│   ├── Unity Cloud Build (CI/CD)
│   └── Vivox (Voice communication)
└── Analytics & Balancing
    ├── Unity Analytics (Player behavior)
    ├── Custom telemetry (Balance data)
    └── A/B Testing framework
```

### **Performance Targets**
| **Platform** | **FPS** | **Resolution** | **Memory** | **Storage** |
|--------------|---------|----------------|------------|-------------|
| **Flagship Mobile** | 60 FPS | 1080p+ | <4GB RAM | <8GB |
| **Mid-range Mobile** | 30 FPS | 720p | <3GB RAM | <6GB |
| **PC** | 120 FPS | 1440p+ | <8GB RAM | <15GB |

---

## 💰 **PROGRESSÃO E MONETIZAÇÃO**

### **Modelo de Monetização Ética**

#### **Battle Pass Evoluído**
```
🎁 ADAPTIVE BATTLE PASS:
├── Traditional Track (Linear progression)
├── Role-Specific Tracks (Tank, DPS, Support, Jungle, Mid)
├── Playstyle Tracks (Aggressive, Defensive, Strategic)
└── Community Tracks (Unlocked via community goals)
```

#### **Champion Acquisition**
- **Free Rotation**: 15 champions/week (vs 10 do Wild Rift)
- **Earn Rate**: 1 novo champion/semana jogando casual
- **Currency**: Blue Essence (earned) + Realm Crystals (premium)
- **No P2W**: Champions purchasable apenas com earned currency

---

## 📈 **ROADMAP DE DESENVOLVIMENTO**

### **FASE 1: FOUNDATION (Meses 1-6)**
```
🏗️ CORE SYSTEMS:
├── ✅ Unity 6.2 setup
├── ✅ Basic MOBA mechanics
├── ✅ Networking foundation
├── ⏳ Champion system architecture
├── ⏳ Basic UI/UX
└── ⏳ Single realm prototype

DELIVERABLES:
- Playable vertical slice (1 realm, 5v5)
- Core champion abilities working
- Basic progression system
- Alpha build for internal testing
```

### **FASE 2: INNOVATION CORE (Meses 7-14)**
```
🚀 UNIQUE FEATURES:
├── ⏳ Dynamic realm transitions
├── ⏳ Vertical combat layers
├── ⏳ Champion fusion system
├── ⏳ AI adaptive jungle
├── ⏳ Procedural objectives
└── ⏳ Advanced networking

DELIVERABLES:
- All core innovations implemented
- 20 champions with fusion compatibility
- Closed beta with 1000 players
- Balancing data collection system
```

### **FASE 3: POLISH & LAUNCH (Meses 15-18)**
```
⚡ FINALIZATION:
├── ⏳ Performance optimization
├── ⏳ UI/UX refinement
├── ⏳ Mobile platform optimization
├── ⏳ Monetization integration
├── ⏳ Anti-cheat systems
└── ⏳ Launch marketing campaign

DELIVERABLES:
- Production-ready build
- 50+ champions at launch
- Comprehensive tutorial system
- Global release (soft launch → worldwide)
```

---

## ⚠️ **ANÁLISE DE RISCOS**

### **RISCOS TÉCNICOS**

#### **🔴 ALTO RISCO**
1. **Complexidade 3D em Mobile**
   - **Problema**: Performance em devices low-end
   - **Solução**: Scalable quality settings, 2D fallback mode
   - **Mitigation**: Extensive device testing, cloud gaming option

2. **Networking Complexity**
   - **Problema**: 3D positioning sync, realm transitions
   - **Solução**: Predictive networking, authoritative server
   - **Mitigation**: Dedicated server infrastructure, regional servers

#### **🟡 MÉDIO RISCO**
1. **AI Jungle Balance**
   - **Problema**: IA muito forte/fraca, exploits
   - **Solução**: Extensive playtesting, gradual learning
   - **Mitigation**: Manual override system, community feedback

### **RISCOS DE MERCADO**

#### **🔴 ALTO RISCO**
1. **Competição com Wild Rift**
   - **Problema**: Brand recognition, established playerbase
   - **Solução**: Focus em inovação, influencer partnerships
   - **Mitigation**: Unique value proposition, superior tech

2. **Monetização Sustentável**
   - **Problema**: F2P market saturation, whale dependency
   - **Solução**: Ethical monetization, broad appeal cosmetics
   - **Mitigation**: Multiple revenue streams, community support

---

## 🎯 **MÉTRICAS DE SUCESSO**

### **KPIs Pré-Launch**
- **Alpha Retention**: >40% D7, >20% D30
- **Beta Feedback Score**: >4.2/5.0
- **Performance**: 60fps em 80% dos devices testados
- **Bug Reports**: <5 critical bugs per build

### **KPIs Post-Launch**
- **DAU**: 100K+ em 6 meses
- **Player Retention**: >30% D30, >10% D90
- **Revenue**: $1M+ revenue em ano 1
- **Community**: 50K+ Discord members, 100K+ Reddit

---

## 📞 **PRÓXIMOS PASSOS**

### **IMEDIATOS (1-2 semanas)**
1. **📋 Refinar este documento** baseado em feedback
2. **👥 Formar core team** (Lead Designer, Tech Lead, Artist Lead)
3. **💰 Preparar pitch deck** para investidores/publishers
4. **🔬 Research aprofundado** de mercado e competição

### **CURTO PRAZO (1-3 meses)**
1. **🎮 Protótipo vertical** de realm transition
2. **🤖 PoC do sistema de IA** adaptativa
3. **📊 Validar interesse** via surveys/focus groups
4. **🏢 Buscar funding** inicial para desenvolvimento

---

## 📝 **CONCLUSÃO**

**AURACRON** representa uma evolução natural do gênero MOBA, mantendo a acessibilidade que tornou Wild Rift popular enquanto introduz inovações significativas que podem redefinir o competitive gaming mobile.

**Diferenciais Únicos:**
- ✅ Mapas dinâmicos que evoluem durante a partida
- ✅ Combate 3D vertical em plataforma mobile
- ✅ IA adaptativa que aprende e desafia players
- ✅ Sistema de fusão que recompensa teamwork
- ✅ Objetivos procedurais únicos a cada match

**Desafios Principais:**
- ⚠️ Complexidade técnica vs performance mobile
- ⚠️ Balanceamento de mecânicas inovadoras
- ⚠️ Competição com IPs estabelecidos
- ⚠️ Sustainable monetization em mercado saturado

**Potencial de Sucesso:**
Com execução adequada e recursos suficientes, **AURACRON** tem potencial para se tornar o "next-gen MOBA" que a comunidade busca, oferecendo profundidade estratégica sem sacrificar acessibilidade.

---

*Este documento é um living document e será atualizado conforme o desenvolvimento do conceito e feedback da equipe.*

### 🔄 **Atualização 29/06/2025 – Refinamentos de Design**

> Resultado de pesquisa interna + benchmarking (Inworld AI, Riot UX, BBC GEL) e discussões de equipe.

#### 1. Essência do Produto
- **Mapa Dinâmico ✔️** agora usa transições por "sub-zonas" antes da troca global de realm.
- **Fusão de Campeões 🔧** reduzida a 3 arquétipos por temporada para facilitar balance.
- **Objetivos Procedurais 🎲** categorizados em Core e Catch-up.

#### 2. Onboarding & Acessibilidade
- Tutorial progressivo + AI Mentor.
- Chat/voice opt-in desativado até nível 10.

#### 3. Comunidade Saudável
- Bônus de honra coletiva (+5 % XP se ninguém for mutado).
- Silenciamento automático 15 min por linguagem tóxica.

#### 4. Roadmap 2.0
| Fase | Janela | Entregas-chave |
|------|--------|----------------|
| 0 – POC | M1-M3 | Grey-box vertical lane, 6 champs |
| 1 – Vertical Slice | M4-M8 | Transição Terra→Céu, fusão Tank+Mage |
| 2 – Closed Beta | M9-M15 | Realm Abyssal, honor system |
| 3 – Launch | M16-M18 | Monetização ética, spectator, 50+ champs |

### 🔄 **Atualização 30/06/2025 – Identidade de Mapas & Narrativa (Resumo)**

- **Planície Radiante** → trilhos Solar / Axis / Lunar, vegetação *Canopy*, balizas de visão.
- **Firmamento Zephyr** → Núcleo de Tempestade, Santuários dos Ventos.
- **Abismo Umbrio** → Leviatã Umbrático, Altares da Sombra.
- Terminologia padronizada: Trilho, Canopy, Baliza, Fluxo, Guardião Prismal.
- Sistema de **Fragmentos de Crônica** + **Vínculos Ocultos** + **Eco de Fusão** integrado ao Codex.

### 🔄 **Atualização 05/07/2025 – Rebrand & Fusion 2.0 (Sigil System)**

#### 1. Rebrand Global
- Codename/brand oficial passa a ser **AURACRON**; referências a "Nexus Realms" ficam como nome de engine/lore interno.
- Tagline de marketing: _"Domine as três camadas. Desperte o Auracron."_
- Domínios reservados: auracron.com / auracron.gg / auracron.game.

#### 2. Fusion 2.0 → Sistema de **SÍGILOS**
- A fusão deixa de exigir dois jogadores.
- Durante a **tela de seleção de campeões**, cada player escolhe **1 de 3 "Sigilos Auracron"** (Tank, Damage, Utility).
- O Sigilo funde-se ao campeão aos 6 min, desbloqueando árvore de habilidades alternativa.
- Pode ser re-forjado no Nexus uma vez por partida (cooldown global de 2 min).
- Cria combinatória de 50 campeões × 3 Sigilos = 150 arquétipos sem depender de cooperação específica.

| Sigilo | Bônus Passivo | Habilidade Exclusiva | Arquétipo-chave |
|--------|--------------|----------------------|-----------------|
| **Aegis** (Tank) | +15% HP, Armadura adaptativa | "Murallion" – cria barreira circular 3s | Frontliner / Iniciado |
| **Ruin** (Damage) | +12% ATK / AP adaptativo | "Fracasso Prismal" – reset parcial de CD | Burst / Skirmisher |
| **Vesper** (Utility) | +10% Vel. Move + 8% Cooldown | "Sopro de Fluxo" – dash aliado + shield | Roamer / Suporte |

#### 3. Impacto em Balanceamento
- Remove gargalo de _matchmaking_ de fusão 2-jogadores.
- Incentiva expressão individual (paralelo às Runas de LoL).
- Mantém identidade "fusão" como power-spike temático.

#### 4. Roadmap Ajustado
- **Fase 2**: Implementar Sigilos em todos os 20 champions alvo.
- **Fase 3**: Ferramenta de telemetria para taxa de escolha / win-rate de Sigilos.

> *Nota: documentação de gameplay, UX e tutoriais deverá refletir a nomenclatura "Sigilo Auracron" ao invés de "Champion Fusion" em futuras revisões.*