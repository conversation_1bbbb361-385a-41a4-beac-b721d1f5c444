// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronTutorialBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronTutorialBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialBridge();
AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialBridge_NoRegister();
AURACRONTUTORIALBRIDGE_API UEnum* Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState();
AURACRONTUTORIALBRIDGE_API UEnum* Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType();
AURACRONTUTORIALBRIDGE_API UEnum* Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType();
AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature();
AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature();
AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature();
AURACRONTUTORIALBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTutorialConfiguration();
AURACRONTUTORIALBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTutorialStep();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
LEVELSEQUENCE_API UClass* Z_Construct_UClass_ULevelSequence_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UMG_API UClass* Z_Construct_UClass_UUserWidget_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronTutorialBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronTutorialType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTutorialType;
static UEnum* EAuracronTutorialType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTutorialType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTutorialType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType, (UObject*)Z_Construct_UPackage__Script_AuracronTutorialBridge(), TEXT("EAuracronTutorialType"));
	}
	return Z_Registration_Info_UEnum_EAuracronTutorialType.OuterSingleton;
}
template<> AURACRONTUTORIALBRIDGE_API UEnum* StaticEnum<EAuracronTutorialType>()
{
	return EAuracronTutorialType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AbilityUsage.DisplayName", "Ability Usage" },
		{ "AbilityUsage.Name", "EAuracronTutorialType::AbilityUsage" },
		{ "Advanced.DisplayName", "Advanced" },
		{ "Advanced.Name", "EAuracronTutorialType::Advanced" },
		{ "BasicMovement.DisplayName", "Basic Movement" },
		{ "BasicMovement.Name", "EAuracronTutorialType::BasicMovement" },
		{ "BlueprintType", "true" },
		{ "CombatBasics.DisplayName", "Combat Basics" },
		{ "CombatBasics.Name", "EAuracronTutorialType::CombatBasics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de tutorial\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronTutorialType::None" },
		{ "Objectives.DisplayName", "Objectives" },
		{ "Objectives.Name", "EAuracronTutorialType::Objectives" },
		{ "Onboarding.DisplayName", "Onboarding" },
		{ "Onboarding.Name", "EAuracronTutorialType::Onboarding" },
		{ "RealmNavigation.DisplayName", "Realm Navigation" },
		{ "RealmNavigation.Name", "EAuracronTutorialType::RealmNavigation" },
		{ "Reboarding.DisplayName", "Reboarding" },
		{ "Reboarding.Name", "EAuracronTutorialType::Reboarding" },
		{ "SigiloSystem.DisplayName", "Sigilo System" },
		{ "SigiloSystem.Name", "EAuracronTutorialType::SigiloSystem" },
		{ "TeamPlay.DisplayName", "Team Play" },
		{ "TeamPlay.Name", "EAuracronTutorialType::TeamPlay" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de tutorial" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTutorialType::None", (int64)EAuracronTutorialType::None },
		{ "EAuracronTutorialType::Onboarding", (int64)EAuracronTutorialType::Onboarding },
		{ "EAuracronTutorialType::BasicMovement", (int64)EAuracronTutorialType::BasicMovement },
		{ "EAuracronTutorialType::CombatBasics", (int64)EAuracronTutorialType::CombatBasics },
		{ "EAuracronTutorialType::AbilityUsage", (int64)EAuracronTutorialType::AbilityUsage },
		{ "EAuracronTutorialType::SigiloSystem", (int64)EAuracronTutorialType::SigiloSystem },
		{ "EAuracronTutorialType::RealmNavigation", (int64)EAuracronTutorialType::RealmNavigation },
		{ "EAuracronTutorialType::TeamPlay", (int64)EAuracronTutorialType::TeamPlay },
		{ "EAuracronTutorialType::Objectives", (int64)EAuracronTutorialType::Objectives },
		{ "EAuracronTutorialType::Advanced", (int64)EAuracronTutorialType::Advanced },
		{ "EAuracronTutorialType::Reboarding", (int64)EAuracronTutorialType::Reboarding },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
	nullptr,
	"EAuracronTutorialType",
	"EAuracronTutorialType",
	Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType()
{
	if (!Z_Registration_Info_UEnum_EAuracronTutorialType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTutorialType.InnerSingleton, Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTutorialType.InnerSingleton;
}
// ********** End Enum EAuracronTutorialType *******************************************************

// ********** Begin Enum EAuracronTutorialState ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTutorialState;
static UEnum* EAuracronTutorialState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTutorialState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTutorialState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState, (UObject*)Z_Construct_UPackage__Script_AuracronTutorialBridge(), TEXT("EAuracronTutorialState"));
	}
	return Z_Registration_Info_UEnum_EAuracronTutorialState.OuterSingleton;
}
template<> AURACRONTUTORIALBRIDGE_API UEnum* StaticEnum<EAuracronTutorialState>()
{
	return EAuracronTutorialState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estado do tutorial\n */" },
#endif
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "EAuracronTutorialState::Completed" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronTutorialState::Failed" },
		{ "InProgress.DisplayName", "In Progress" },
		{ "InProgress.Name", "EAuracronTutorialState::InProgress" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
		{ "NotStarted.DisplayName", "Not Started" },
		{ "NotStarted.Name", "EAuracronTutorialState::NotStarted" },
		{ "Paused.DisplayName", "Paused" },
		{ "Paused.Name", "EAuracronTutorialState::Paused" },
		{ "Skipped.DisplayName", "Skipped" },
		{ "Skipped.Name", "EAuracronTutorialState::Skipped" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estado do tutorial" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTutorialState::NotStarted", (int64)EAuracronTutorialState::NotStarted },
		{ "EAuracronTutorialState::InProgress", (int64)EAuracronTutorialState::InProgress },
		{ "EAuracronTutorialState::Completed", (int64)EAuracronTutorialState::Completed },
		{ "EAuracronTutorialState::Skipped", (int64)EAuracronTutorialState::Skipped },
		{ "EAuracronTutorialState::Failed", (int64)EAuracronTutorialState::Failed },
		{ "EAuracronTutorialState::Paused", (int64)EAuracronTutorialState::Paused },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
	nullptr,
	"EAuracronTutorialState",
	"EAuracronTutorialState",
	Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState()
{
	if (!Z_Registration_Info_UEnum_EAuracronTutorialState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTutorialState.InnerSingleton, Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTutorialState.InnerSingleton;
}
// ********** End Enum EAuracronTutorialState ******************************************************

// ********** Begin Enum EAuracronTutorialStepType *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTutorialStepType;
static UEnum* EAuracronTutorialStepType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTutorialStepType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTutorialStepType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType, (UObject*)Z_Construct_UPackage__Script_AuracronTutorialBridge(), TEXT("EAuracronTutorialStepType"));
	}
	return Z_Registration_Info_UEnum_EAuracronTutorialStepType.OuterSingleton;
}
template<> AURACRONTUTORIALBRIDGE_API UEnum* StaticEnum<EAuracronTutorialStepType>()
{
	return EAuracronTutorialStepType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Action.DisplayName", "Action Required" },
		{ "Action.Name", "EAuracronTutorialStepType::Action" },
		{ "BlueprintType", "true" },
		{ "Checkpoint.DisplayName", "Checkpoint" },
		{ "Checkpoint.Name", "EAuracronTutorialStepType::Checkpoint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de passo do tutorial\n */" },
#endif
		{ "Demonstration.DisplayName", "Demonstration" },
		{ "Demonstration.Name", "EAuracronTutorialStepType::Demonstration" },
		{ "Information.DisplayName", "Information" },
		{ "Information.Name", "EAuracronTutorialStepType::Information" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
		{ "Practice.DisplayName", "Practice" },
		{ "Practice.Name", "EAuracronTutorialStepType::Practice" },
		{ "Quiz.DisplayName", "Quiz" },
		{ "Quiz.Name", "EAuracronTutorialStepType::Quiz" },
		{ "Reward.DisplayName", "Reward" },
		{ "Reward.Name", "EAuracronTutorialStepType::Reward" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de passo do tutorial" },
#endif
		{ "Transition.DisplayName", "Transition" },
		{ "Transition.Name", "EAuracronTutorialStepType::Transition" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTutorialStepType::Information", (int64)EAuracronTutorialStepType::Information },
		{ "EAuracronTutorialStepType::Action", (int64)EAuracronTutorialStepType::Action },
		{ "EAuracronTutorialStepType::Demonstration", (int64)EAuracronTutorialStepType::Demonstration },
		{ "EAuracronTutorialStepType::Practice", (int64)EAuracronTutorialStepType::Practice },
		{ "EAuracronTutorialStepType::Quiz", (int64)EAuracronTutorialStepType::Quiz },
		{ "EAuracronTutorialStepType::Checkpoint", (int64)EAuracronTutorialStepType::Checkpoint },
		{ "EAuracronTutorialStepType::Reward", (int64)EAuracronTutorialStepType::Reward },
		{ "EAuracronTutorialStepType::Transition", (int64)EAuracronTutorialStepType::Transition },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
	nullptr,
	"EAuracronTutorialStepType",
	"EAuracronTutorialStepType",
	Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType()
{
	if (!Z_Registration_Info_UEnum_EAuracronTutorialStepType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTutorialStepType.InnerSingleton, Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTutorialStepType.InnerSingleton;
}
// ********** End Enum EAuracronTutorialStepType ***************************************************

// ********** Begin ScriptStruct FAuracronTutorialStep *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTutorialStep;
class UScriptStruct* FAuracronTutorialStep::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTutorialStep.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTutorialStep.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTutorialStep, (UObject*)Z_Construct_UPackage__Script_AuracronTutorialBridge(), TEXT("AuracronTutorialStep"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTutorialStep.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para passo do tutorial\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para passo do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepID_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico do passo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico do passo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepName_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do passo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do passo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepDescription_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do passo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do passo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepType_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do passo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do passo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDuration_MetaData[] = {
		{ "Category", "Tutorial Step" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o m\xc3\x83\xc2\xa1xima do passo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o m\xc3\x83\xc2\xa1xima do passo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeSkipped_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode ser pulado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode ser pulado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMandatory_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\xb0 obrigat\xc3\x83\xc2\xb3rio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\xb0 obrigat\xc3\x83\xc2\xb3rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredAction_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o requerida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o requerida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionParameters_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Par\xc3\x83\xc2\xa2metros da a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Par\xc3\x83\xc2\xa2metros da a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepWidget_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Widget de UI para o passo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widget de UI para o passo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o no mundo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FocusActor_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ator de foco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator de foco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHighlight_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar highlight */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar highlight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighlightColor_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do highlight */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do highlight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCameraAnimation_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de c\xc3\x83\xc2\xa2mera */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar anima\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de c\xc3\x83\xc2\xa2mera" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraSequence_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sequ\xc3\x83\xc2\xaancia de c\xc3\x83\xc2\xa2mera */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sequ\xc3\x83\xc2\xaancia de c\xc3\x83\xc2\xa2mera" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNarration_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar narra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar narra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NarrationAudio_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x81udio de narra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x81udio de narra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NarrationText_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Texto de narra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texto de narra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionConditions_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de conclus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de conclus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Prerequisites_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pr\xc3\x83\xc2\xa9-requisitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pr\xc3\x83\xc2\xa9-requisitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepTags_MetaData[] = {
		{ "Category", "Tutorial Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags do passo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags do passo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StepID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_StepName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_StepDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StepType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StepType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDuration;
	static void NewProp_bCanBeSkipped_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeSkipped;
	static void NewProp_bIsMandatory_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMandatory;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredAction;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActionParameters;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_StepWidget;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FocusActor;
	static void NewProp_bUseHighlight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHighlight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HighlightColor;
	static void NewProp_bUseCameraAnimation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCameraAnimation;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CameraSequence;
	static void NewProp_bUseNarration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNarration;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_NarrationAudio;
	static const UECodeGen_Private::FTextPropertyParams NewProp_NarrationText;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletionConditions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletionConditions;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Prerequisites_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Prerequisites;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StepTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTutorialStep>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepID = { "StepID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, StepID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepID_MetaData), NewProp_StepID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepName = { "StepName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, StepName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepName_MetaData), NewProp_StepName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepDescription = { "StepDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, StepDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepDescription_MetaData), NewProp_StepDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepType = { "StepType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, StepType), Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialStepType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepType_MetaData), NewProp_StepType_MetaData) }; // 2010187351
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_MaxDuration = { "MaxDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, MaxDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDuration_MetaData), NewProp_MaxDuration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bCanBeSkipped_SetBit(void* Obj)
{
	((FAuracronTutorialStep*)Obj)->bCanBeSkipped = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bCanBeSkipped = { "bCanBeSkipped", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialStep), &Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bCanBeSkipped_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeSkipped_MetaData), NewProp_bCanBeSkipped_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bIsMandatory_SetBit(void* Obj)
{
	((FAuracronTutorialStep*)Obj)->bIsMandatory = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bIsMandatory = { "bIsMandatory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialStep), &Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bIsMandatory_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMandatory_MetaData), NewProp_bIsMandatory_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_RequiredAction = { "RequiredAction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, RequiredAction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredAction_MetaData), NewProp_RequiredAction_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_ActionParameters_ValueProp = { "ActionParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_ActionParameters_Key_KeyProp = { "ActionParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_ActionParameters = { "ActionParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, ActionParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionParameters_MetaData), NewProp_ActionParameters_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepWidget = { "StepWidget", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, StepWidget), Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepWidget_MetaData), NewProp_StepWidget_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_FocusActor = { "FocusActor", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, FocusActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FocusActor_MetaData), NewProp_FocusActor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseHighlight_SetBit(void* Obj)
{
	((FAuracronTutorialStep*)Obj)->bUseHighlight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseHighlight = { "bUseHighlight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialStep), &Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseHighlight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHighlight_MetaData), NewProp_bUseHighlight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_HighlightColor = { "HighlightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, HighlightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighlightColor_MetaData), NewProp_HighlightColor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseCameraAnimation_SetBit(void* Obj)
{
	((FAuracronTutorialStep*)Obj)->bUseCameraAnimation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseCameraAnimation = { "bUseCameraAnimation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialStep), &Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseCameraAnimation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCameraAnimation_MetaData), NewProp_bUseCameraAnimation_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_CameraSequence = { "CameraSequence", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, CameraSequence), Z_Construct_UClass_ULevelSequence_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraSequence_MetaData), NewProp_CameraSequence_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseNarration_SetBit(void* Obj)
{
	((FAuracronTutorialStep*)Obj)->bUseNarration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseNarration = { "bUseNarration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialStep), &Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseNarration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNarration_MetaData), NewProp_bUseNarration_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_NarrationAudio = { "NarrationAudio", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, NarrationAudio), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NarrationAudio_MetaData), NewProp_NarrationAudio_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_NarrationText = { "NarrationText", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, NarrationText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NarrationText_MetaData), NewProp_NarrationText_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_CompletionConditions_Inner = { "CompletionConditions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_CompletionConditions = { "CompletionConditions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, CompletionConditions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionConditions_MetaData), NewProp_CompletionConditions_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_Prerequisites_Inner = { "Prerequisites", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_Prerequisites = { "Prerequisites", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, Prerequisites), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Prerequisites_MetaData), NewProp_Prerequisites_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepTags = { "StepTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialStep, StepTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepTags_MetaData), NewProp_StepTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_MaxDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bCanBeSkipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bIsMandatory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_RequiredAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_ActionParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_ActionParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_ActionParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_FocusActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseHighlight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_HighlightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseCameraAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_CameraSequence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_bUseNarration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_NarrationAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_NarrationText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_CompletionConditions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_CompletionConditions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_Prerequisites_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_Prerequisites,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewProp_StepTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
	nullptr,
	&NewStructOps,
	"AuracronTutorialStep",
	Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::PropPointers),
	sizeof(FAuracronTutorialStep),
	alignof(FAuracronTutorialStep),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTutorialStep()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTutorialStep.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTutorialStep.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTutorialStep.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTutorialStep ***********************************************

// ********** Begin ScriptStruct FAuracronTutorialConfiguration ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration;
class UScriptStruct* FAuracronTutorialConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTutorialConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronTutorialBridge(), TEXT("AuracronTutorialConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de tutorial\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialName_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialDescription_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialType_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialSteps_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Passos do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Passos do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedDuration_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
		{ "ClampMax", "1800.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o estimada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o estimada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyLevel_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de dificuldade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de dificuldade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBePaused_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode ser pausado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode ser pausado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeRestarted_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode ser reiniciado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode ser reiniciado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoSaveProgress_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Salvar progresso automaticamente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Salvar progresso automaticamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAIMentor_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar AI Mentor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar AI Mentor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIMentorPersonality_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Personalidade do AI Mentor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Personalidade do AI Mentor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDynamicAdaptation_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar adapta\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o din\xc3\x83\xc2\xa2mica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar adapta\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseRealTimeFeedback_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar feedback em tempo real */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar feedback em tempo real" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGamification_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar gamifica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar gamifica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsPerStep_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos por passo completado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos por passo completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionReward_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensa de conclus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensa de conclus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAnalytics_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar analytics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar analytics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SupportedLanguages_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Idiomas suportados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Idiomas suportados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialVersion_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vers\xc3\x83\xc2\xa3o do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vers\xc3\x83\xc2\xa3o do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialTags_MetaData[] = {
		{ "Category", "Tutorial Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags do tutorial" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TutorialName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TutorialDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TutorialType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TutorialType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TutorialSteps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TutorialSteps;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedDuration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DifficultyLevel;
	static void NewProp_bCanBePaused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBePaused;
	static void NewProp_bCanBeRestarted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeRestarted;
	static void NewProp_bAutoSaveProgress_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoSaveProgress;
	static void NewProp_bUseAIMentor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAIMentor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AIMentorPersonality;
	static void NewProp_bUseDynamicAdaptation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDynamicAdaptation;
	static void NewProp_bUseRealTimeFeedback_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseRealTimeFeedback;
	static void NewProp_bUseGamification_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGamification;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsPerStep;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletionReward;
	static void NewProp_bUseAnalytics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAnalytics;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SupportedLanguages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SupportedLanguages;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialVersion;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TutorialTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTutorialConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialName = { "TutorialName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, TutorialName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialName_MetaData), NewProp_TutorialName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialDescription = { "TutorialDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, TutorialDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialDescription_MetaData), NewProp_TutorialDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialType = { "TutorialType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, TutorialType), Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialType_MetaData), NewProp_TutorialType_MetaData) }; // 2589027139
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialSteps_Inner = { "TutorialSteps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronTutorialStep, METADATA_PARAMS(0, nullptr) }; // 4142743485
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialSteps = { "TutorialSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, TutorialSteps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialSteps_MetaData), NewProp_TutorialSteps_MetaData) }; // 4142743485
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_EstimatedDuration = { "EstimatedDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, EstimatedDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedDuration_MetaData), NewProp_EstimatedDuration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_DifficultyLevel = { "DifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, DifficultyLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyLevel_MetaData), NewProp_DifficultyLevel_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBePaused_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bCanBePaused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBePaused = { "bCanBePaused", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBePaused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBePaused_MetaData), NewProp_bCanBePaused_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBeRestarted_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bCanBeRestarted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBeRestarted = { "bCanBeRestarted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBeRestarted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeRestarted_MetaData), NewProp_bCanBeRestarted_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bAutoSaveProgress_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bAutoSaveProgress = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bAutoSaveProgress = { "bAutoSaveProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bAutoSaveProgress_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoSaveProgress_MetaData), NewProp_bAutoSaveProgress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAIMentor_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bUseAIMentor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAIMentor = { "bUseAIMentor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAIMentor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAIMentor_MetaData), NewProp_bUseAIMentor_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_AIMentorPersonality = { "AIMentorPersonality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, AIMentorPersonality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIMentorPersonality_MetaData), NewProp_AIMentorPersonality_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseDynamicAdaptation_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bUseDynamicAdaptation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseDynamicAdaptation = { "bUseDynamicAdaptation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseDynamicAdaptation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDynamicAdaptation_MetaData), NewProp_bUseDynamicAdaptation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseRealTimeFeedback_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bUseRealTimeFeedback = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseRealTimeFeedback = { "bUseRealTimeFeedback", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseRealTimeFeedback_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseRealTimeFeedback_MetaData), NewProp_bUseRealTimeFeedback_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseGamification_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bUseGamification = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseGamification = { "bUseGamification", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseGamification_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGamification_MetaData), NewProp_bUseGamification_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_PointsPerStep = { "PointsPerStep", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, PointsPerStep), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsPerStep_MetaData), NewProp_PointsPerStep_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_CompletionReward = { "CompletionReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, CompletionReward), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionReward_MetaData), NewProp_CompletionReward_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAnalytics_SetBit(void* Obj)
{
	((FAuracronTutorialConfiguration*)Obj)->bUseAnalytics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAnalytics = { "bUseAnalytics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTutorialConfiguration), &Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAnalytics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAnalytics_MetaData), NewProp_bUseAnalytics_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_SupportedLanguages_Inner = { "SupportedLanguages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_SupportedLanguages = { "SupportedLanguages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, SupportedLanguages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SupportedLanguages_MetaData), NewProp_SupportedLanguages_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialVersion = { "TutorialVersion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, TutorialVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialVersion_MetaData), NewProp_TutorialVersion_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialTags = { "TutorialTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialConfiguration, TutorialTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialTags_MetaData), NewProp_TutorialTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialSteps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_EstimatedDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_DifficultyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBePaused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bCanBeRestarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bAutoSaveProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAIMentor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_AIMentorPersonality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseDynamicAdaptation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseRealTimeFeedback,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseGamification,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_PointsPerStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_CompletionReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_bUseAnalytics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_SupportedLanguages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_SupportedLanguages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewProp_TutorialTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
	nullptr,
	&NewStructOps,
	"AuracronTutorialConfiguration",
	Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::PropPointers),
	sizeof(FAuracronTutorialConfiguration),
	alignof(FAuracronTutorialConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTutorialConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTutorialConfiguration **************************************

// ********** Begin Delegate FOnTutorialStarted ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics
{
	struct AuracronTutorialBridge_eventOnTutorialStarted_Parms
	{
		FAuracronTutorialConfiguration Tutorial;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando tutorial inicia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando tutorial inicia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tutorial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::NewProp_Tutorial = { "Tutorial", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventOnTutorialStarted_Parms, Tutorial), Z_Construct_UScriptStruct_FAuracronTutorialConfiguration, METADATA_PARAMS(0, nullptr) }; // 157954862
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::NewProp_Tutorial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "OnTutorialStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::AuracronTutorialBridge_eventOnTutorialStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::AuracronTutorialBridge_eventOnTutorialStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronTutorialBridge::FOnTutorialStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTutorialStarted, FAuracronTutorialConfiguration Tutorial)
{
	struct AuracronTutorialBridge_eventOnTutorialStarted_Parms
	{
		FAuracronTutorialConfiguration Tutorial;
	};
	AuracronTutorialBridge_eventOnTutorialStarted_Parms Parms;
	Parms.Tutorial=Tutorial;
	OnTutorialStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTutorialStarted ******************************************************

// ********** Begin Delegate FOnTutorialStepCompleted **********************************************
struct Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics
{
	struct AuracronTutorialBridge_eventOnTutorialStepCompleted_Parms
	{
		int32 StepIndex;
		FAuracronTutorialStep Step;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando passo \xc3\x83\xc2\xa9 completado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando passo \xc3\x83\xc2\xa9 completado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Step;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventOnTutorialStepCompleted_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::NewProp_Step = { "Step", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventOnTutorialStepCompleted_Parms, Step), Z_Construct_UScriptStruct_FAuracronTutorialStep, METADATA_PARAMS(0, nullptr) }; // 4142743485
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::NewProp_Step,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "OnTutorialStepCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::AuracronTutorialBridge_eventOnTutorialStepCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::AuracronTutorialBridge_eventOnTutorialStepCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronTutorialBridge::FOnTutorialStepCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTutorialStepCompleted, int32 StepIndex, FAuracronTutorialStep Step)
{
	struct AuracronTutorialBridge_eventOnTutorialStepCompleted_Parms
	{
		int32 StepIndex;
		FAuracronTutorialStep Step;
	};
	AuracronTutorialBridge_eventOnTutorialStepCompleted_Parms Parms;
	Parms.StepIndex=StepIndex;
	Parms.Step=Step;
	OnTutorialStepCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTutorialStepCompleted ************************************************

// ********** Begin Delegate FOnTutorialCompleted **************************************************
struct Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics
{
	struct AuracronTutorialBridge_eventOnTutorialCompleted_Parms
	{
		FString TutorialID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando tutorial \xc3\x83\xc2\xa9 completado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando tutorial \xc3\x83\xc2\xa9 completado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventOnTutorialCompleted_Parms, TutorialID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::NewProp_TutorialID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "OnTutorialCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::AuracronTutorialBridge_eventOnTutorialCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::AuracronTutorialBridge_eventOnTutorialCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronTutorialBridge::FOnTutorialCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTutorialCompleted, const FString& TutorialID)
{
	struct AuracronTutorialBridge_eventOnTutorialCompleted_Parms
	{
		FString TutorialID;
	};
	AuracronTutorialBridge_eventOnTutorialCompleted_Parms Parms;
	Parms.TutorialID=TutorialID;
	OnTutorialCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTutorialCompleted ****************************************************

// ********** Begin Class UAuracronTutorialBridge Function ActivateAIMentor ************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics
{
	struct AuracronTutorialBridge_eventActivateAIMentor_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|AIMentor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar AI Mentor\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar AI Mentor" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventActivateAIMentor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventActivateAIMentor_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "ActivateAIMentor", Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::AuracronTutorialBridge_eventActivateAIMentor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::AuracronTutorialBridge_eventActivateAIMentor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execActivateAIMentor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateAIMentor();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function ActivateAIMentor **************************

// ********** Begin Class UAuracronTutorialBridge Function AdaptTutorialToPlayer *******************
struct Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics
{
	struct AuracronTutorialBridge_eventAdaptTutorialToPlayer_Parms
	{
		TMap<FString,float> PlayerMetrics;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adaptar tutorial ao jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptar tutorial ao jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerMetrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerMetrics;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_PlayerMetrics_ValueProp = { "PlayerMetrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_PlayerMetrics_Key_KeyProp = { "PlayerMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_PlayerMetrics = { "PlayerMetrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventAdaptTutorialToPlayer_Parms, PlayerMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerMetrics_MetaData), NewProp_PlayerMetrics_MetaData) };
void Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventAdaptTutorialToPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventAdaptTutorialToPlayer_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_PlayerMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_PlayerMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_PlayerMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "AdaptTutorialToPlayer", Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::AuracronTutorialBridge_eventAdaptTutorialToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::AuracronTutorialBridge_eventAdaptTutorialToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execAdaptTutorialToPlayer)
{
	P_GET_TMAP_REF(FString,float,Z_Param_Out_PlayerMetrics);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AdaptTutorialToPlayer(Z_Param_Out_PlayerMetrics);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function AdaptTutorialToPlayer *********************

// ********** Begin Class UAuracronTutorialBridge Function AdjustTutorialDifficulty ****************
struct Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics
{
	struct AuracronTutorialBridge_eventAdjustTutorialDifficulty_Parms
	{
		int32 NewDifficultyLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ajustar dificuldade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ajustar dificuldade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewDifficultyLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::NewProp_NewDifficultyLevel = { "NewDifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventAdjustTutorialDifficulty_Parms, NewDifficultyLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventAdjustTutorialDifficulty_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventAdjustTutorialDifficulty_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::NewProp_NewDifficultyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "AdjustTutorialDifficulty", Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::AuracronTutorialBridge_eventAdjustTutorialDifficulty_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::AuracronTutorialBridge_eventAdjustTutorialDifficulty_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execAdjustTutorialDifficulty)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewDifficultyLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AdjustTutorialDifficulty(Z_Param_NewDifficultyLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function AdjustTutorialDifficulty ******************

// ********** Begin Class UAuracronTutorialBridge Function AIMentorDemonstrate *********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics
{
	struct AuracronTutorialBridge_eventAIMentorDemonstrate_Parms
	{
		FString ActionType;
		TMap<FString,FString> Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|AIMentor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * AI Mentor demonstrar a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Mentor demonstrar a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventAIMentorDemonstrate_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventAIMentorDemonstrate_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventAIMentorDemonstrate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventAIMentorDemonstrate_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "AIMentorDemonstrate", Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::AuracronTutorialBridge_eventAIMentorDemonstrate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::AuracronTutorialBridge_eventAIMentorDemonstrate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execAIMentorDemonstrate)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AIMentorDemonstrate(Z_Param_ActionType,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function AIMentorDemonstrate ***********************

// ********** Begin Class UAuracronTutorialBridge Function AIMentorSpeak ***************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics
{
	struct AuracronTutorialBridge_eventAIMentorSpeak_Parms
	{
		FText Message;
		bool bUseVoice;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|AIMentor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * AI Mentor falar\n     */" },
#endif
		{ "CPP_Default_bUseVoice", "true" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Mentor falar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Message;
	static void NewProp_bUseVoice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVoice;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventAIMentorSpeak_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
void Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_bUseVoice_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventAIMentorSpeak_Parms*)Obj)->bUseVoice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_bUseVoice = { "bUseVoice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventAIMentorSpeak_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_bUseVoice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventAIMentorSpeak_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventAIMentorSpeak_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_bUseVoice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "AIMentorSpeak", Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::AuracronTutorialBridge_eventAIMentorSpeak_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::AuracronTutorialBridge_eventAIMentorSpeak_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execAIMentorSpeak)
{
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Message);
	P_GET_UBOOL(Z_Param_bUseVoice);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AIMentorSpeak(Z_Param_Out_Message,Z_Param_bUseVoice);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function AIMentorSpeak *****************************

// ********** Begin Class UAuracronTutorialBridge Function CompleteCurrentStep *********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics
{
	struct AuracronTutorialBridge_eventCompleteCurrentStep_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Steps" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Completar passo atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completar passo atual" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventCompleteCurrentStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventCompleteCurrentStep_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "CompleteCurrentStep", Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::AuracronTutorialBridge_eventCompleteCurrentStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::AuracronTutorialBridge_eventCompleteCurrentStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execCompleteCurrentStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CompleteCurrentStep();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function CompleteCurrentStep ***********************

// ********** Begin Class UAuracronTutorialBridge Function DeactivateAIMentor **********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics
{
	struct AuracronTutorialBridge_eventDeactivateAIMentor_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|AIMentor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Desativar AI Mentor\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar AI Mentor" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventDeactivateAIMentor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventDeactivateAIMentor_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "DeactivateAIMentor", Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::AuracronTutorialBridge_eventDeactivateAIMentor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::AuracronTutorialBridge_eventDeactivateAIMentor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execDeactivateAIMentor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeactivateAIMentor();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function DeactivateAIMentor ************************

// ********** Begin Class UAuracronTutorialBridge Function GetTutorialProgress *********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics
{
	struct AuracronTutorialBridge_eventGetTutorialProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter progresso atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progresso atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventGetTutorialProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "GetTutorialProgress", Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::AuracronTutorialBridge_eventGetTutorialProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::AuracronTutorialBridge_eventGetTutorialProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execGetTutorialProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTutorialProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function GetTutorialProgress ***********************

// ********** Begin Class UAuracronTutorialBridge Function GoToTutorialStep ************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics
{
	struct AuracronTutorialBridge_eventGoToTutorialStep_Parms
	{
		int32 StepIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Steps" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ir para passo espec\xc3\x83\xc2\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ir para passo espec\xc3\x83\xc2\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventGoToTutorialStep_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventGoToTutorialStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventGoToTutorialStep_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "GoToTutorialStep", Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::AuracronTutorialBridge_eventGoToTutorialStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::AuracronTutorialBridge_eventGoToTutorialStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execGoToTutorialStep)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_StepIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GoToTutorialStep(Z_Param_StepIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function GoToTutorialStep **************************

// ********** Begin Class UAuracronTutorialBridge Function IsTutorialCompleted *********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics
{
	struct AuracronTutorialBridge_eventIsTutorialCompleted_Parms
	{
		FString TutorialID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se tutorial foi completado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se tutorial foi completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventIsTutorialCompleted_Parms, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
void Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventIsTutorialCompleted_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventIsTutorialCompleted_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::NewProp_TutorialID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "IsTutorialCompleted", Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::AuracronTutorialBridge_eventIsTutorialCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::AuracronTutorialBridge_eventIsTutorialCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execIsTutorialCompleted)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TutorialID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTutorialCompleted(Z_Param_TutorialID);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function IsTutorialCompleted ***********************

// ********** Begin Class UAuracronTutorialBridge Function LoadTutorialProgress ********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics
{
	struct AuracronTutorialBridge_eventLoadTutorialProgress_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar progresso do tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar progresso do tutorial" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventLoadTutorialProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventLoadTutorialProgress_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "LoadTutorialProgress", Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::AuracronTutorialBridge_eventLoadTutorialProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::AuracronTutorialBridge_eventLoadTutorialProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execLoadTutorialProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadTutorialProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function LoadTutorialProgress **********************

// ********** Begin Class UAuracronTutorialBridge Function NextTutorialStep ************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics
{
	struct AuracronTutorialBridge_eventNextTutorialStep_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Steps" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Avan\xc3\x83\xc2\xa7""ar para pr\xc3\x83\xc2\xb3ximo passo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Avan\xc3\x83\xc2\xa7""ar para pr\xc3\x83\xc2\xb3ximo passo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventNextTutorialStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventNextTutorialStep_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "NextTutorialStep", Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::AuracronTutorialBridge_eventNextTutorialStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::AuracronTutorialBridge_eventNextTutorialStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execNextTutorialStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->NextTutorialStep();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function NextTutorialStep **************************

// ********** Begin Class UAuracronTutorialBridge Function OnRep_CurrentStep ***********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_CurrentStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_CurrentStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "OnRep_CurrentStep", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_CurrentStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_CurrentStep_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_CurrentStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_CurrentStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execOnRep_CurrentStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CurrentStep();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function OnRep_CurrentStep *************************

// ********** Begin Class UAuracronTutorialBridge Function OnRep_TutorialState *********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_TutorialState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_TutorialState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "OnRep_TutorialState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_TutorialState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_TutorialState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_TutorialState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_TutorialState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execOnRep_TutorialState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_TutorialState();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function OnRep_TutorialState ***********************

// ********** Begin Class UAuracronTutorialBridge Function PauseTutorial ***************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics
{
	struct AuracronTutorialBridge_eventPauseTutorial_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Pausar tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pausar tutorial" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventPauseTutorial_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventPauseTutorial_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "PauseTutorial", Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::AuracronTutorialBridge_eventPauseTutorial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::AuracronTutorialBridge_eventPauseTutorial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execPauseTutorial)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PauseTutorial();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function PauseTutorial *****************************

// ********** Begin Class UAuracronTutorialBridge Function PersonalizeTutorialContent **************
struct Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics
{
	struct AuracronTutorialBridge_eventPersonalizeTutorialContent_Parms
	{
		FString PlayerProfile;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Adaptive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Personalizar conte\xc3\x83\xc2\xba""do\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Personalizar conte\xc3\x83\xc2\xba""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerProfile_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerProfile;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::NewProp_PlayerProfile = { "PlayerProfile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventPersonalizeTutorialContent_Parms, PlayerProfile), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerProfile_MetaData), NewProp_PlayerProfile_MetaData) };
void Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventPersonalizeTutorialContent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventPersonalizeTutorialContent_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::NewProp_PlayerProfile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "PersonalizeTutorialContent", Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::AuracronTutorialBridge_eventPersonalizeTutorialContent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::AuracronTutorialBridge_eventPersonalizeTutorialContent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execPersonalizeTutorialContent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerProfile);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PersonalizeTutorialContent(Z_Param_PlayerProfile);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function PersonalizeTutorialContent ****************

// ********** Begin Class UAuracronTutorialBridge Function PreviousTutorialStep ********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics
{
	struct AuracronTutorialBridge_eventPreviousTutorialStep_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Steps" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Voltar para passo anterior\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Voltar para passo anterior" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventPreviousTutorialStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventPreviousTutorialStep_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "PreviousTutorialStep", Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::AuracronTutorialBridge_eventPreviousTutorialStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::AuracronTutorialBridge_eventPreviousTutorialStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execPreviousTutorialStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PreviousTutorialStep();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function PreviousTutorialStep **********************

// ********** Begin Class UAuracronTutorialBridge Function RestartTutorial *************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics
{
	struct AuracronTutorialBridge_eventRestartTutorial_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reiniciar tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reiniciar tutorial" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventRestartTutorial_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventRestartTutorial_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "RestartTutorial", Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::AuracronTutorialBridge_eventRestartTutorial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::AuracronTutorialBridge_eventRestartTutorial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execRestartTutorial)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RestartTutorial();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function RestartTutorial ***************************

// ********** Begin Class UAuracronTutorialBridge Function ResumeTutorial **************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics
{
	struct AuracronTutorialBridge_eventResumeTutorial_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Retomar tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retomar tutorial" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventResumeTutorial_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventResumeTutorial_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "ResumeTutorial", Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::AuracronTutorialBridge_eventResumeTutorial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::AuracronTutorialBridge_eventResumeTutorial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execResumeTutorial)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ResumeTutorial();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function ResumeTutorial ****************************

// ********** Begin Class UAuracronTutorialBridge Function SaveTutorialProgress ********************
struct Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics
{
	struct AuracronTutorialBridge_eventSaveTutorialProgress_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Salvar progresso do tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Salvar progresso do tutorial" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventSaveTutorialProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventSaveTutorialProgress_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "SaveTutorialProgress", Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::AuracronTutorialBridge_eventSaveTutorialProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::AuracronTutorialBridge_eventSaveTutorialProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execSaveTutorialProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveTutorialProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function SaveTutorialProgress **********************

// ********** Begin Class UAuracronTutorialBridge Function SkipCurrentStep *************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics
{
	struct AuracronTutorialBridge_eventSkipCurrentStep_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Steps" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Pular passo atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pular passo atual" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventSkipCurrentStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventSkipCurrentStep_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "SkipCurrentStep", Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::AuracronTutorialBridge_eventSkipCurrentStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::AuracronTutorialBridge_eventSkipCurrentStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execSkipCurrentStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SkipCurrentStep();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function SkipCurrentStep ***************************

// ********** Begin Class UAuracronTutorialBridge Function SkipTutorial ****************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics
{
	struct AuracronTutorialBridge_eventSkipTutorial_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Pular tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pular tutorial" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventSkipTutorial_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventSkipTutorial_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "SkipTutorial", Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::AuracronTutorialBridge_eventSkipTutorial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::AuracronTutorialBridge_eventSkipTutorial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execSkipTutorial)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SkipTutorial();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function SkipTutorial ******************************

// ********** Begin Class UAuracronTutorialBridge Function StartTutorial ***************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics
{
	struct AuracronTutorialBridge_eventStartTutorial_Parms
	{
		FAuracronTutorialConfiguration TutorialConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TutorialConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::NewProp_TutorialConfig = { "TutorialConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialBridge_eventStartTutorial_Parms, TutorialConfig), Z_Construct_UScriptStruct_FAuracronTutorialConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialConfig_MetaData), NewProp_TutorialConfig_MetaData) }; // 157954862
void Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventStartTutorial_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventStartTutorial_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::NewProp_TutorialConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "StartTutorial", Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::AuracronTutorialBridge_eventStartTutorial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::AuracronTutorialBridge_eventStartTutorial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execStartTutorial)
{
	P_GET_STRUCT_REF(FAuracronTutorialConfiguration,Z_Param_Out_TutorialConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartTutorial(Z_Param_Out_TutorialConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function StartTutorial *****************************

// ********** Begin Class UAuracronTutorialBridge Function StopTutorial ****************************
struct Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics
{
	struct AuracronTutorialBridge_eventStopTutorial_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Tutorial|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar tutorial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar tutorial" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialBridge_eventStopTutorial_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialBridge_eventStopTutorial_Parms), &Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialBridge, nullptr, "StopTutorial", Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::AuracronTutorialBridge_eventStopTutorial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::AuracronTutorialBridge_eventStopTutorial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialBridge::execStopTutorial)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopTutorial();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialBridge Function StopTutorial ******************************

// ********** Begin Class UAuracronTutorialBridge **************************************************
void UAuracronTutorialBridge::StaticRegisterNativesUAuracronTutorialBridge()
{
	UClass* Class = UAuracronTutorialBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateAIMentor", &UAuracronTutorialBridge::execActivateAIMentor },
		{ "AdaptTutorialToPlayer", &UAuracronTutorialBridge::execAdaptTutorialToPlayer },
		{ "AdjustTutorialDifficulty", &UAuracronTutorialBridge::execAdjustTutorialDifficulty },
		{ "AIMentorDemonstrate", &UAuracronTutorialBridge::execAIMentorDemonstrate },
		{ "AIMentorSpeak", &UAuracronTutorialBridge::execAIMentorSpeak },
		{ "CompleteCurrentStep", &UAuracronTutorialBridge::execCompleteCurrentStep },
		{ "DeactivateAIMentor", &UAuracronTutorialBridge::execDeactivateAIMentor },
		{ "GetTutorialProgress", &UAuracronTutorialBridge::execGetTutorialProgress },
		{ "GoToTutorialStep", &UAuracronTutorialBridge::execGoToTutorialStep },
		{ "IsTutorialCompleted", &UAuracronTutorialBridge::execIsTutorialCompleted },
		{ "LoadTutorialProgress", &UAuracronTutorialBridge::execLoadTutorialProgress },
		{ "NextTutorialStep", &UAuracronTutorialBridge::execNextTutorialStep },
		{ "OnRep_CurrentStep", &UAuracronTutorialBridge::execOnRep_CurrentStep },
		{ "OnRep_TutorialState", &UAuracronTutorialBridge::execOnRep_TutorialState },
		{ "PauseTutorial", &UAuracronTutorialBridge::execPauseTutorial },
		{ "PersonalizeTutorialContent", &UAuracronTutorialBridge::execPersonalizeTutorialContent },
		{ "PreviousTutorialStep", &UAuracronTutorialBridge::execPreviousTutorialStep },
		{ "RestartTutorial", &UAuracronTutorialBridge::execRestartTutorial },
		{ "ResumeTutorial", &UAuracronTutorialBridge::execResumeTutorial },
		{ "SaveTutorialProgress", &UAuracronTutorialBridge::execSaveTutorialProgress },
		{ "SkipCurrentStep", &UAuracronTutorialBridge::execSkipCurrentStep },
		{ "SkipTutorial", &UAuracronTutorialBridge::execSkipTutorial },
		{ "StartTutorial", &UAuracronTutorialBridge::execStartTutorial },
		{ "StopTutorial", &UAuracronTutorialBridge::execStopTutorial },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronTutorialBridge;
UClass* UAuracronTutorialBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronTutorialBridge;
	if (!Z_Registration_Info_UClass_UAuracronTutorialBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronTutorialBridge"),
			Z_Registration_Info_UClass_UAuracronTutorialBridge.InnerSingleton,
			StaticRegisterNativesUAuracronTutorialBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronTutorialBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronTutorialBridge_NoRegister()
{
	return UAuracronTutorialBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronTutorialBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Tutorial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Tutorial\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de tutoriais progressivos\n */" },
#endif
		{ "DisplayName", "AURACRON Tutorial Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronTutorialBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Tutorial\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de tutoriais progressivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTutorial_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tutorial atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tutorial atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTutorialState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentStepIndex_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8dndice do passo atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8dndice do passo atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialProgress_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progresso do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progresso do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedTutorials_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tutoriais completados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tutoriais completados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAIMentorActive_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** AI Mentor est\xc3\x83\xc2\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Mentor est\xc3\x83\xc2\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTutorialWidget_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Widget de tutorial atual */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widget de tutorial atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTutorialStarted_MetaData[] = {
		{ "Category", "AURACRON Tutorial|Events" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTutorialStepCompleted_MetaData[] = {
		{ "Category", "AURACRON Tutorial|Events" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTutorialCompleted_MetaData[] = {
		{ "Category", "AURACRON Tutorial|Events" },
		{ "ModuleRelativePath", "Public/AuracronTutorialBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentTutorial;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTutorialState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTutorialState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentStepIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TutorialProgress;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletedTutorials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedTutorials;
	static void NewProp_bAIMentorActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAIMentorActive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentTutorialWidget;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTutorialStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTutorialStepCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTutorialCompleted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_ActivateAIMentor, "ActivateAIMentor" }, // 742340266
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_AdaptTutorialToPlayer, "AdaptTutorialToPlayer" }, // 2859275565
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_AdjustTutorialDifficulty, "AdjustTutorialDifficulty" }, // 3278091316
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorDemonstrate, "AIMentorDemonstrate" }, // 174669212
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_AIMentorSpeak, "AIMentorSpeak" }, // 470199394
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_CompleteCurrentStep, "CompleteCurrentStep" }, // 3799305782
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_DeactivateAIMentor, "DeactivateAIMentor" }, // 3443287817
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_GetTutorialProgress, "GetTutorialProgress" }, // 1820695695
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_GoToTutorialStep, "GoToTutorialStep" }, // 3687850180
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_IsTutorialCompleted, "IsTutorialCompleted" }, // 336623373
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_LoadTutorialProgress, "LoadTutorialProgress" }, // 2693303065
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_NextTutorialStep, "NextTutorialStep" }, // 4158645982
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_CurrentStep, "OnRep_CurrentStep" }, // 2399474041
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_OnRep_TutorialState, "OnRep_TutorialState" }, // 4077817184
		{ &Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature, "OnTutorialCompleted__DelegateSignature" }, // 881621711
		{ &Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature, "OnTutorialStarted__DelegateSignature" }, // 2534103607
		{ &Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature, "OnTutorialStepCompleted__DelegateSignature" }, // 939584789
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_PauseTutorial, "PauseTutorial" }, // 1012693234
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_PersonalizeTutorialContent, "PersonalizeTutorialContent" }, // 3311574478
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_PreviousTutorialStep, "PreviousTutorialStep" }, // 2739870437
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_RestartTutorial, "RestartTutorial" }, // 898702949
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_ResumeTutorial, "ResumeTutorial" }, // 98013863
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_SaveTutorialProgress, "SaveTutorialProgress" }, // 2342817407
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_SkipCurrentStep, "SkipCurrentStep" }, // 996441539
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_SkipTutorial, "SkipTutorial" }, // 1012733743
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_StartTutorial, "StartTutorial" }, // 2080150137
		{ &Z_Construct_UFunction_UAuracronTutorialBridge_StopTutorial, "StopTutorial" }, // 570040858
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronTutorialBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorial = { "CurrentTutorial", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, CurrentTutorial), Z_Construct_UScriptStruct_FAuracronTutorialConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTutorial_MetaData), NewProp_CurrentTutorial_MetaData) }; // 157954862
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorialState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorialState = { "CurrentTutorialState", "OnRep_TutorialState", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, CurrentTutorialState), Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTutorialState_MetaData), NewProp_CurrentTutorialState_MetaData) }; // 102943273
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentStepIndex = { "CurrentStepIndex", "OnRep_CurrentStep", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, CurrentStepIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentStepIndex_MetaData), NewProp_CurrentStepIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_TutorialProgress = { "TutorialProgress", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, TutorialProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialProgress_MetaData), NewProp_TutorialProgress_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CompletedTutorials_Inner = { "CompletedTutorials", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CompletedTutorials = { "CompletedTutorials", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, CompletedTutorials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedTutorials_MetaData), NewProp_CompletedTutorials_MetaData) };
void Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_bAIMentorActive_SetBit(void* Obj)
{
	((UAuracronTutorialBridge*)Obj)->bAIMentorActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_bAIMentorActive = { "bAIMentorActive", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronTutorialBridge), &Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_bAIMentorActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAIMentorActive_MetaData), NewProp_bAIMentorActive_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorialWidget = { "CurrentTutorialWidget", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, CurrentTutorialWidget), Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTutorialWidget_MetaData), NewProp_CurrentTutorialWidget_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_OnTutorialStarted = { "OnTutorialStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, OnTutorialStarted), Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTutorialStarted_MetaData), NewProp_OnTutorialStarted_MetaData) }; // 2534103607
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_OnTutorialStepCompleted = { "OnTutorialStepCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, OnTutorialStepCompleted), Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTutorialStepCompleted_MetaData), NewProp_OnTutorialStepCompleted_MetaData) }; // 939584789
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_OnTutorialCompleted = { "OnTutorialCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialBridge, OnTutorialCompleted), Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTutorialCompleted_MetaData), NewProp_OnTutorialCompleted_MetaData) }; // 881621711
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronTutorialBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorialState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorialState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentStepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_TutorialProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CompletedTutorials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CompletedTutorials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_bAIMentorActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_CurrentTutorialWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_OnTutorialStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_OnTutorialStepCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialBridge_Statics::NewProp_OnTutorialCompleted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronTutorialBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronTutorialBridge_Statics::ClassParams = {
	&UAuracronTutorialBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronTutorialBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronTutorialBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronTutorialBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronTutorialBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronTutorialBridge.OuterSingleton, Z_Construct_UClass_UAuracronTutorialBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronTutorialBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronTutorialBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentTutorial(TEXT("CurrentTutorial"));
	static FName Name_CurrentTutorialState(TEXT("CurrentTutorialState"));
	static FName Name_CurrentStepIndex(TEXT("CurrentStepIndex"));
	static FName Name_TutorialProgress(TEXT("TutorialProgress"));
	static FName Name_bAIMentorActive(TEXT("bAIMentorActive"));
	const bool bIsValid = true
		&& Name_CurrentTutorial == ClassReps[(int32)ENetFields_Private::CurrentTutorial].Property->GetFName()
		&& Name_CurrentTutorialState == ClassReps[(int32)ENetFields_Private::CurrentTutorialState].Property->GetFName()
		&& Name_CurrentStepIndex == ClassReps[(int32)ENetFields_Private::CurrentStepIndex].Property->GetFName()
		&& Name_TutorialProgress == ClassReps[(int32)ENetFields_Private::TutorialProgress].Property->GetFName()
		&& Name_bAIMentorActive == ClassReps[(int32)ENetFields_Private::bAIMentorActive].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronTutorialBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronTutorialBridge);
UAuracronTutorialBridge::~UAuracronTutorialBridge() {}
// ********** End Class UAuracronTutorialBridge ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronTutorialType_StaticEnum, TEXT("EAuracronTutorialType"), &Z_Registration_Info_UEnum_EAuracronTutorialType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2589027139U) },
		{ EAuracronTutorialState_StaticEnum, TEXT("EAuracronTutorialState"), &Z_Registration_Info_UEnum_EAuracronTutorialState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 102943273U) },
		{ EAuracronTutorialStepType_StaticEnum, TEXT("EAuracronTutorialStepType"), &Z_Registration_Info_UEnum_EAuracronTutorialStepType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2010187351U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronTutorialStep::StaticStruct, Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics::NewStructOps, TEXT("AuracronTutorialStep"), &Z_Registration_Info_UScriptStruct_FAuracronTutorialStep, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTutorialStep), 4142743485U) },
		{ FAuracronTutorialConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics::NewStructOps, TEXT("AuracronTutorialConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronTutorialConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTutorialConfiguration), 157954862U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronTutorialBridge, UAuracronTutorialBridge::StaticClass, TEXT("UAuracronTutorialBridge"), &Z_Registration_Info_UClass_UAuracronTutorialBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronTutorialBridge), 3872176378U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_1154234346(TEXT("/Script/AuracronTutorialBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h__Script_AuracronTutorialBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
