# Script para adicionar plugins necessários ao arquivo .uproject

$uprojectPath = "Auracron\Auracron.uproject"

# Lista de plugins necessários
$plugins = @(
    "GeometryScripting",
    "GeometryProcessing", 
    "MeshModelingToolset",
    "MassGameplay",
    "MassAI",
    "StateTree",
    "GameplayStateTree",
    "StructUtils",
    "SmartObjects",
    "ZoneGraph",
    "ZoneGraphAnnotations",
    "AnalyticsMulticast",
    "ModularGameplay",
    "ReplicationGraph",
    "NetcodeUnitTest",
    "RawInput",
    "GPULightmass",
    "HairStrands",
    "LiveLink",
    "AudioSynesthesia",
    "RigLogic",
    "Takes",
    "RemoteControl",
    "VirtualCamera",
    "ProxyLODPlugin",
    "ChaosVehiclesPlugin",
    "Water",
    "SignificanceManager",
    "StylusInput"
)

Write-Host "Adicionando plugins ao arquivo .uproject..."

# Ler o arquivo .uproject
$content = Get-Content $uprojectPath -Raw | ConvertFrom-Json

# Verificar se já existe a seção Plugins
if (-not $content.Plugins) {
    $content | Add-Member -Type NoteProperty -Name "Plugins" -Value @()
}

# Converter para array se necessário
if ($content.Plugins -isnot [array]) {
    $content.Plugins = @($content.Plugins)
}

# Adicionar plugins que não existem
foreach ($pluginName in $plugins) {
    $exists = $content.Plugins | Where-Object { $_.Name -eq $pluginName }
    if (-not $exists) {
        $newPlugin = @{
            Name = $pluginName
            Enabled = $true
        }
        $content.Plugins += $newPlugin
        Write-Host "Adicionado plugin: $pluginName"
    } else {
        Write-Host "Plugin já existe: $pluginName"
    }
}

# Salvar o arquivo
$content | ConvertTo-Json -Depth 10 | Set-Content $uprojectPath -Encoding UTF8

Write-Host "Plugins adicionados com sucesso!"
