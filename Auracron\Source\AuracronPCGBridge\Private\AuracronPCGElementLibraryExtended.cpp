// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Extended Element Library Implementation
// Bridge 2.3: PCG Framework - Element Library (Extended)

#include "AuracronPCGElementLibraryExtended.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"

// Engine includes
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "DrawDebugHelpers.h"

// =============================================================================
// POINT TRANSFORMER IMPLEMENTATION
// =============================================================================

UAuracronPCGPointTransformerSettings::UAuracronPCGPointTransformerSettings()
{
    NodeMetadata.NodeName = TEXT("Point Transformer");
    NodeMetadata.NodeDescription = TEXT("Advanced point transformation with multiple transformation modes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Transform;
    NodeMetadata.Tags.Add(TEXT("Transform"));
    NodeMetadata.Tags.Add(TEXT("Points"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.2f, 0.8f);
}

void UAuracronPCGPointTransformerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGPointTransformerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGPointTransformerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                          FPCGDataCollection& OutputData, 
                                                                          const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGPointTransformerSettings* Settings = GetTypedSettings<UAuracronPCGPointTransformerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Point Transformer");
            return Result;
        }

        // Create output point data
        UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
        OutputPointData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

        int32 TotalProcessed = 0;
        FRandomStream RandomStream(FMath::Rand());

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                TotalProcessed++;
                FPCGPoint OutputPoint = InputPoint;
                FTransform& Transform = OutputPoint.Transform;

                // Transform position
                if (Settings->bTransformPosition)
                {
                    FVector Position = Transform.GetLocation();
                    
                    // Apply offset
                    Position += Settings->PositionOffset;
                    
                    // Apply random variation
                    if (!Settings->PositionMin.IsZero() || !Settings->PositionMax.IsZero())
                    {
                        Position.X += RandomStream.FRandRange(Settings->PositionMin.X, Settings->PositionMax.X);
                        Position.Y += RandomStream.FRandRange(Settings->PositionMin.Y, Settings->PositionMax.Y);
                        Position.Z += RandomStream.FRandRange(Settings->PositionMin.Z, Settings->PositionMax.Z);
                    }
                    
                    // Apply noise transformation
                    if (Settings->bUseNoiseTransform)
                    {
                        float NoiseValue = AuracronPCGElementUtils::GenerateNoise(
                            Settings->NoiseType, Position, Settings->NoiseScale, 1);
                        Position += FVector(NoiseValue) * Settings->NoiseIntensity * 100.0f;
                    }
                    
                    Transform.SetLocation(Position);
                }

                // Transform rotation
                if (Settings->bTransformRotation)
                {
                    FRotator Rotation = Transform.GetRotation().Rotator();
                    
                    // Apply offset
                    Rotation += Settings->RotationOffset;
                    
                    // Apply random variation
                    Rotation.Pitch += RandomStream.FRandRange(Settings->RotationMin.Pitch, Settings->RotationMax.Pitch);
                    Rotation.Yaw += RandomStream.FRandRange(Settings->RotationMin.Yaw, Settings->RotationMax.Yaw);
                    Rotation.Roll += RandomStream.FRandRange(Settings->RotationMin.Roll, Settings->RotationMax.Roll);
                    
                    if (Settings->bAbsoluteRotation)
                    {
                        Transform.SetRotation(Rotation.Quaternion());
                    }
                    else
                    {
                        Transform.SetRotation(Transform.GetRotation() * Rotation.Quaternion());
                    }
                }

                // Transform scale
                if (Settings->bTransformScale)
                {
                    FVector Scale = Transform.GetScale3D();
                    
                    // Apply offset
                    Scale += Settings->ScaleOffset;
                    
                    // Apply random variation
                    if (Settings->bUniformScale)
                    {
                        float UniformScale = RandomStream.FRandRange(Settings->ScaleMin.X, Settings->ScaleMax.X);
                        Scale *= UniformScale;
                    }
                    else
                    {
                        Scale.X *= RandomStream.FRandRange(Settings->ScaleMin.X, Settings->ScaleMax.X);
                        Scale.Y *= RandomStream.FRandRange(Settings->ScaleMin.Y, Settings->ScaleMax.Y);
                        Scale.Z *= RandomStream.FRandRange(Settings->ScaleMin.Z, Settings->ScaleMax.Z);
                    }
                    
                    Transform.SetScale3D(Scale);
                }

                OutputPoints.Add(OutputPoint);
            }

            // Copy metadata if available
            if (InputPointData->Metadata && !OutputPointData->Metadata)
            {
                OutputPointData->Metadata = InputPointData->Metadata->Copy();
            }
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = OutputPointData;
        TaggedData.Pin = TEXT("Output");

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Point Transformer processed %d points"), TotalProcessed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Point Transformer error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// ADVANCED MESH SPAWNER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedMeshSpawnerSettings::UAuracronPCGAdvancedMeshSpawnerSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Mesh Spawner");
    NodeMetadata.NodeDescription = TEXT("Enhanced mesh spawning with weighted selection and LOD support");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Spawner;
    NodeMetadata.Tags.Add(TEXT("Spawner"));
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("LOD"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.6f);
}

void UAuracronPCGAdvancedMeshSpawnerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedMeshSpawnerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGAdvancedMeshSpawnerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                             FPCGDataCollection& OutputData, 
                                                                             const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedMeshSpawnerSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedMeshSpawnerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Mesh Spawner");
            return Result;
        }

        if (Settings->MeshEntries.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No mesh entries configured");
            return Result;
        }

        // Calculate total weight
        float TotalWeight = 0.0f;
        for (const FAuracronPCGMeshEntry& Entry : Settings->MeshEntries)
        {
            TotalWeight += Entry.Weight;
        }

        if (TotalWeight <= 0.0f)
        {
            Result.ErrorMessage = TEXT("Total mesh weight is zero or negative");
            return Result;
        }

        // Create output point data
        UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
        OutputPointData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

        int32 TotalProcessed = 0;
        int32 SpawnedMeshes = 0;
        FRandomStream RandomStream(FMath::Rand());

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                TotalProcessed++;

                // Check spawn probability
                if (RandomStream.FRand() > Settings->SpawnProbability)
                {
                    continue;
                }

                // Select mesh based on weight
                float RandomWeight = RandomStream.FRand() * TotalWeight;
                float CurrentWeight = 0.0f;
                const FAuracronPCGMeshEntry* SelectedEntry = nullptr;

                for (const FAuracronPCGMeshEntry& Entry : Settings->MeshEntries)
                {
                    CurrentWeight += Entry.Weight;
                    if (RandomWeight <= CurrentWeight)
                    {
                        SelectedEntry = &Entry;
                        break;
                    }
                }

                if (!SelectedEntry || !SelectedEntry->Mesh.IsValid())
                {
                    continue;
                }

                FPCGPoint OutputPoint = InputPoint;

                // Apply scale multiplier
                FVector Scale = OutputPoint.Transform.GetScale3D() * SelectedEntry->ScaleMultiplier;
                
                // Scale by density if enabled
                if (Settings->bScaleByDensity)
                {
                    Scale *= OutputPoint.Density;
                }
                
                OutputPoint.Transform.SetScale3D(Scale);

                // Align to surface if enabled
                if (Settings->bAlignToSurface)
                {
                    FVector Position = OutputPoint.Transform.GetLocation();
                    FVector Normal = AuracronPCGElementUtils::CalculateSurfaceNormal(Position);
                    FQuat SurfaceRotation = FQuat::FindBetweenNormals(FVector::UpVector, Normal);
                    OutputPoint.Transform.SetRotation(SurfaceRotation * OutputPoint.Transform.GetRotation());
                }

                // Add mesh reference to metadata
                if (!OutputPointData->Metadata)
                {
                    OutputPointData->Metadata = NewObject<UPCGMetadata>();
                }

                // Store mesh path as attribute
                FString MeshPath = SelectedEntry->Mesh.GetAssetPathString();
                OutputPointData->Metadata->CreateStringAttribute(TEXT("MeshPath"), MeshPath, false);
                OutputPointData->Metadata->CreateFloatAttribute(TEXT("MeshWeight"), SelectedEntry->Weight, false);
                OutputPointData->Metadata->CreateBoolAttribute(TEXT("UseCollision"), SelectedEntry->bUseCollision, false);
                OutputPointData->Metadata->CreateBoolAttribute(TEXT("CastShadows"), SelectedEntry->bCastShadows, false);

                OutputPoints.Add(OutputPoint);
                SpawnedMeshes++;
            }

            // Copy input metadata if available
            if (InputPointData->Metadata && OutputPointData->Metadata)
            {
                // Sophisticated metadata merging implementation
                MergeMetadataAdvanced(InputPointData->Metadata, OutputPointData->Metadata, Settings->MetadataMergeMode);
            }
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = OutputPointData;
        TaggedData.Pin = TEXT("Output");

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Mesh Spawner processed %d points, spawned %d meshes"), 
                                  TotalProcessed, SpawnedMeshes);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Mesh Spawner error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// ATTRIBUTE MODIFIER IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeModifierSettings::UAuracronPCGAttributeModifierSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Modifier");
    NodeMetadata.NodeDescription = TEXT("Modifies point attributes with various operations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Utility;
    NodeMetadata.Tags.Add(TEXT("Utility"));
    NodeMetadata.Tags.Add(TEXT("Attributes"));
    NodeMetadata.Tags.Add(TEXT("Modifier"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.6f, 0.2f);
}

void UAuracronPCGAttributeModifierSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeModifierSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGAttributeModifierElement::ProcessData(const FPCGDataCollection& InputData,
                                                                           FPCGDataCollection& OutputData,
                                                                           const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAttributeModifierSettings* Settings = GetTypedSettings<UAuracronPCGAttributeModifierSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Modifier");
            return Result;
        }

        if (Settings->AttributeName.IsEmpty())
        {
            Result.ErrorMessage = TEXT("Attribute name is empty");
            return Result;
        }

        // Create output point data
        UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
        OutputPointData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

        int32 TotalProcessed = 0;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();

            for (const FPCGPoint& InputPoint : InputPoints)
            {
                TotalProcessed++;
                FPCGPoint OutputPoint = InputPoint;

                // Get current attribute value
                float CurrentValue = 0.0f;
                if (Settings->AttributeName == TEXT("Density"))
                {
                    CurrentValue = OutputPoint.Density;
                }
                else
                {
                    // Access metadata attribute value
                    CurrentValue = GetMetadataAttributeValue(OutputPoint, Settings->AttributeName, InputPointData->Metadata);
                }

                // Apply noise modulation if enabled
                float ModulatedValue = Settings->Value;
                if (Settings->bUseNoise)
                {
                    FVector Position = OutputPoint.Transform.GetLocation();
                    float NoiseValue = AuracronPCGElementUtils::GenerateNoise(
                        Settings->NoiseType, Position, Settings->NoiseScale, 1);
                    ModulatedValue = FMath::Lerp(Settings->Value, Settings->Value * NoiseValue, Settings->NoiseInfluence);
                }

                // Apply operation
                float NewValue = CurrentValue;
                switch (Settings->Operation)
                {
                    case EAuracronPCGAttributeOperation::Set:
                        NewValue = ModulatedValue;
                        break;
                    case EAuracronPCGAttributeOperation::Add:
                        NewValue = CurrentValue + ModulatedValue;
                        break;
                    case EAuracronPCGAttributeOperation::Multiply:
                        NewValue = CurrentValue * ModulatedValue;
                        break;
                    case EAuracronPCGAttributeOperation::Min:
                        NewValue = FMath::Min(CurrentValue, ModulatedValue);
                        break;
                    case EAuracronPCGAttributeOperation::Max:
                        NewValue = FMath::Max(CurrentValue, ModulatedValue);
                        break;
                    case EAuracronPCGAttributeOperation::Lerp:
                        NewValue = FMath::Lerp(Settings->MinValue, Settings->MaxValue, ModulatedValue);
                        break;
                    case EAuracronPCGAttributeOperation::Clamp:
                        NewValue = FMath::Clamp(CurrentValue, Settings->MinValue, Settings->MaxValue);
                        break;
                    case EAuracronPCGAttributeOperation::Normalize:
                        NewValue = (CurrentValue - Settings->MinValue) / (Settings->MaxValue - Settings->MinValue);
                        break;
                    case EAuracronPCGAttributeOperation::Invert:
                        NewValue = 1.0f - CurrentValue;
                        break;
                }

                // Set the modified value
                if (Settings->AttributeName == TEXT("Density"))
                {
                    OutputPoint.Density = FMath::Clamp(NewValue, 0.0f, 1.0f);
                }
                else
                {
                    // For other attributes, we'd set them in metadata
                    if (!OutputPointData->Metadata)
                    {
                        OutputPointData->Metadata = NewObject<UPCGMetadata>();
                    }
                    OutputPointData->Metadata->CreateFloatAttribute(FName(*Settings->AttributeName), NewValue, false);
                }

                OutputPoints.Add(OutputPoint);
            }

            // Copy input metadata if available
            if (InputPointData->Metadata && !OutputPointData->Metadata)
            {
                OutputPointData->Metadata = InputPointData->Metadata->Copy();
            }
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = OutputPointData;
        TaggedData.Pin = TEXT("Output");

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Modifier processed %d points, modified attribute '%s'"),
                                  TotalProcessed, *Settings->AttributeName);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Modifier error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// DEBUG VISUALIZER IMPLEMENTATION
// =============================================================================

UAuracronPCGDebugVisualizerSettings::UAuracronPCGDebugVisualizerSettings()
{
    NodeMetadata.NodeName = TEXT("Debug Visualizer");
    NodeMetadata.NodeDescription = TEXT("Visualizes point data for debugging purposes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Debug;
    NodeMetadata.Tags.Add(TEXT("Debug"));
    NodeMetadata.Tags.Add(TEXT("Visualizer"));
    NodeMetadata.Tags.Add(TEXT("Utility"));
    NodeMetadata.NodeColor = FLinearColor(1.0f, 0.5f, 0.0f);
}

void UAuracronPCGDebugVisualizerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGDebugVisualizerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGDebugVisualizerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                         FPCGDataCollection& OutputData,
                                                                         const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGDebugVisualizerSettings* Settings = GetTypedSettings<UAuracronPCGDebugVisualizerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Debug Visualizer");
            return Result;
        }

        // Pass through input data unchanged
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
            OutputTaggedData = TaggedData;
        }

        // Perform debug visualization
        UWorld* World = nullptr;
        if (Parameters.Context && Parameters.Context->SourceComponent.IsValid())
        {
            World = Parameters.Context->SourceComponent->GetWorld();
        }

        if (World && (Settings->bShowInEditor || Settings->bShowInGame))
        {
            int32 PointsVisualized = 0;

            for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
            {
                const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
                if (!InputPointData)
                {
                    continue;
                }

                const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();

                for (const FPCGPoint& Point : InputPoints)
                {
                    if (PointsVisualized >= Settings->MaxPointsToVisualize)
                    {
                        break;
                    }

                    FVector Position = Point.Transform.GetLocation();
                    FLinearColor Color = Settings->DebugColor;

                    // Adjust color based on visualization mode
                    switch (Settings->VisualizationMode)
                    {
                        case EAuracronPCGDebugVisualizationMode::Density:
                        {
                            float DensityAlpha = FMath::Clamp(Point.Density, 0.0f, 1.0f);
                            Color = FLinearColor::LerpUsingHSV(Settings->MinColor, Settings->MaxColor, DensityAlpha);
                            break;
                        }
                        case EAuracronPCGDebugVisualizationMode::Attributes:
                        {
                            // Sample the specified attribute from metadata
                            float AttributeValue = GetMetadataAttributeValue(Point, Settings->AttributeName, InputPointData->Metadata);
                            Color = FLinearColor::LerpUsingHSV(Settings->MinColor, Settings->MaxColor, AttributeValue);
                            break;
                        }
                        default:
                            break;
                    }

                    // Draw debug visualization
                    switch (Settings->VisualizationMode)
                    {
                        case EAuracronPCGDebugVisualizationMode::Points:
                        {
                            DrawDebugSphere(World, Position, Settings->DebugSize, 8, Color.ToFColor(true),
                                          false, Settings->VisualizationDuration);
                            break;
                        }
                        case EAuracronPCGDebugVisualizationMode::Bounds:
                        {
                            FBox Bounds = Point.GetLocalBounds();
                            Bounds = Bounds.TransformBy(Point.Transform);
                            DrawDebugBox(World, Bounds.GetCenter(), Bounds.GetExtent(), Color.ToFColor(true),
                                       false, Settings->VisualizationDuration);
                            break;
                        }
                        case EAuracronPCGDebugVisualizationMode::Normals:
                        {
                            FVector Normal = Point.Transform.GetUnitAxis(EAxis::Z);
                            FVector EndPoint = Position + Normal * Settings->DebugSize * 2.0f;
                            DrawDebugDirectionalArrow(World, Position, EndPoint, 5.0f, Color.ToFColor(true),
                                                    false, Settings->VisualizationDuration);
                            break;
                        }
                        default:
                        {
                            DrawDebugSphere(World, Position, Settings->DebugSize, 8, Color.ToFColor(true),
                                          false, Settings->VisualizationDuration);
                            break;
                        }
                    }

                    PointsVisualized++;
                }
            }

            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug Visualizer rendered %d debug elements"), PointsVisualized);
        }

        Result.bSuccess = true;
        Result.PointsProcessed = InputData.GetInputs().Num();
        Result.OutputDataCount = OutputData.TaggedData.Num();
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Debug Visualizer error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// === Metadata Merging Implementation ===

void FAuracronPCGAdvancedFilterElement::MergeMetadataAdvanced(const UPCGMetadata* SourceMetadata, UPCGMetadata* TargetMetadata, EAuracronPCGMetadataMergeMode MergeMode)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronPCGAdvancedFilterElement::MergeMetadataAdvanced);

    if (!SourceMetadata || !TargetMetadata)
    {
        return;
    }

    // Get all attributes from source metadata
    TArray<FName> SourceAttributeNames;
    TArray<EPCGMetadataTypes> SourceAttributeTypes;
    SourceMetadata->GetAttributes(SourceAttributeNames, SourceAttributeTypes);

    // Get all attributes from target metadata
    TArray<FName> TargetAttributeNames;
    TArray<EPCGMetadataTypes> TargetAttributeTypes;
    TargetMetadata->GetAttributes(TargetAttributeNames, TargetAttributeTypes);

    for (int32 i = 0; i < SourceAttributeNames.Num(); i++)
    {
        FName AttributeName = SourceAttributeNames[i];
        EPCGMetadataTypes AttributeType = SourceAttributeTypes[i];

        // Check if attribute exists in target
        int32 TargetIndex = TargetAttributeNames.IndexOfByKey(AttributeName);

        if (TargetIndex != INDEX_NONE)
        {
            // Attribute exists in both - merge based on mode
            EPCGMetadataTypes TargetType = TargetAttributeTypes[TargetIndex];

            if (AttributeType == TargetType)
            {
                MergeAttributeValues(SourceMetadata, TargetMetadata, AttributeName, AttributeType, MergeMode);
            }
            else
            {
                // Type mismatch - handle conversion or skip
                UE_LOG(LogAuracronPCGElements, Warning, TEXT("Metadata attribute type mismatch for %s"), *AttributeName.ToString());
            }
        }
        else
        {
            // Attribute doesn't exist in target - copy it
            CopyAttributeToTarget(SourceMetadata, TargetMetadata, AttributeName, AttributeType);
        }
    }
}

void FAuracronPCGAdvancedFilterElement::MergeAttributeValues(const UPCGMetadata* SourceMetadata, UPCGMetadata* TargetMetadata, FName AttributeName, EPCGMetadataTypes AttributeType, EAuracronPCGMetadataMergeMode MergeMode)
{
    switch (AttributeType)
    {
        case EPCGMetadataTypes::Float:
        {
            const FPCGMetadataAttribute<float>* SourceAttr = SourceMetadata->GetConstTypedAttribute<float>(AttributeName);
            FPCGMetadataAttribute<float>* TargetAttr = TargetMetadata->GetMutableTypedAttribute<float>(AttributeName);

            if (SourceAttr && TargetAttr)
            {
                MergeFloatAttribute(SourceAttr, TargetAttr, MergeMode);
            }
            break;
        }
        case EPCGMetadataTypes::Integer32:
        {
            const FPCGMetadataAttribute<int32>* SourceAttr = SourceMetadata->GetConstTypedAttribute<int32>(AttributeName);
            FPCGMetadataAttribute<int32>* TargetAttr = TargetMetadata->GetMutableTypedAttribute<int32>(AttributeName);

            if (SourceAttr && TargetAttr)
            {
                MergeIntAttribute(SourceAttr, TargetAttr, MergeMode);
            }
            break;
        }
        case EPCGMetadataTypes::Vector:
        {
            const FPCGMetadataAttribute<FVector>* SourceAttr = SourceMetadata->GetConstTypedAttribute<FVector>(AttributeName);
            FPCGMetadataAttribute<FVector>* TargetAttr = TargetMetadata->GetMutableTypedAttribute<FVector>(AttributeName);

            if (SourceAttr && TargetAttr)
            {
                MergeVectorAttribute(SourceAttr, TargetAttr, MergeMode);
            }
            break;
        }
        case EPCGMetadataTypes::String:
        {
            const FPCGMetadataAttribute<FString>* SourceAttr = SourceMetadata->GetConstTypedAttribute<FString>(AttributeName);
            FPCGMetadataAttribute<FString>* TargetAttr = TargetMetadata->GetMutableTypedAttribute<FString>(AttributeName);

            if (SourceAttr && TargetAttr)
            {
                MergeStringAttribute(SourceAttr, TargetAttr, MergeMode);
            }
            break;
        }
        default:
            // Handle other types as needed
            break;
    }
}

void FAuracronPCGAdvancedFilterElement::CopyAttributeToTarget(const UPCGMetadata* SourceMetadata, UPCGMetadata* TargetMetadata, FName AttributeName, EPCGMetadataTypes AttributeType)
{
    // Create new attribute in target metadata and copy all values
    switch (AttributeType)
    {
        case EPCGMetadataTypes::Float:
        {
            const FPCGMetadataAttribute<float>* SourceAttr = SourceMetadata->GetConstTypedAttribute<float>(AttributeName);
            if (SourceAttr)
            {
                FPCGMetadataAttribute<float>* NewAttr = TargetMetadata->CreateAttribute<float>(AttributeName, SourceAttr->GetDefaultValue(), /*bAllowsInterpolation=*/true);
                // Copy all values from source to target
                for (PCGMetadataEntryKey EntryKey = 0; EntryKey < SourceMetadata->GetItemCountForChild(); ++EntryKey)
                {
                    if (SourceAttr->HasNonDefaultValue(EntryKey))
                    {
                        float Value = SourceAttr->GetValueFromItemKey(EntryKey);
                        NewAttr->SetValue(EntryKey, Value);
                    }
                }
            }
            break;
        }
        case EPCGMetadataTypes::Integer32:
        {
            const FPCGMetadataAttribute<int32>* SourceAttr = SourceMetadata->GetConstTypedAttribute<int32>(AttributeName);
            if (SourceAttr)
            {
                FPCGMetadataAttribute<int32>* NewAttr = TargetMetadata->CreateAttribute<int32>(AttributeName, SourceAttr->GetDefaultValue(), /*bAllowsInterpolation=*/false);
                // Copy all values from source to target
                for (PCGMetadataEntryKey EntryKey = 0; EntryKey < SourceMetadata->GetItemCountForChild(); ++EntryKey)
                {
                    if (SourceAttr->HasNonDefaultValue(EntryKey))
                    {
                        int32 Value = SourceAttr->GetValueFromItemKey(EntryKey);
                        NewAttr->SetValue(EntryKey, Value);
                    }
                }
            }
            break;
        }
        case EPCGMetadataTypes::Vector:
        {
            const FPCGMetadataAttribute<FVector>* SourceAttr = SourceMetadata->GetConstTypedAttribute<FVector>(AttributeName);
            if (SourceAttr)
            {
                FPCGMetadataAttribute<FVector>* NewAttr = TargetMetadata->CreateAttribute<FVector>(AttributeName, SourceAttr->GetDefaultValue(), /*bAllowsInterpolation=*/true);
                // Copy all values from source to target
                for (PCGMetadataEntryKey EntryKey = 0; EntryKey < SourceMetadata->GetItemCountForChild(); ++EntryKey)
                {
                    if (SourceAttr->HasNonDefaultValue(EntryKey))
                    {
                        FVector Value = SourceAttr->GetValueFromItemKey(EntryKey);
                        NewAttr->SetValue(EntryKey, Value);
                    }
                }
            }
            break;
        }
        case EPCGMetadataTypes::String:
        {
            const FPCGMetadataAttribute<FString>* SourceAttr = SourceMetadata->GetConstTypedAttribute<FString>(AttributeName);
            if (SourceAttr)
            {
                FPCGMetadataAttribute<FString>* NewAttr = TargetMetadata->CreateAttribute<FString>(AttributeName, SourceAttr->GetDefaultValue(), /*bAllowsInterpolation=*/false);
                // Copy all values from source to target
                for (PCGMetadataEntryKey EntryKey = 0; EntryKey < SourceMetadata->GetItemCountForChild(); ++EntryKey)
                {
                    if (SourceAttr->HasNonDefaultValue(EntryKey))
                    {
                        FString Value = SourceAttr->GetValueFromItemKey(EntryKey);
                        NewAttr->SetValue(EntryKey, Value);
                    }
                }
            }
            break;
        }
        case EPCGMetadataTypes::Boolean:
        {
            const FPCGMetadataAttribute<bool>* SourceAttr = SourceMetadata->GetConstTypedAttribute<bool>(AttributeName);
            if (SourceAttr)
            {
                FPCGMetadataAttribute<bool>* NewAttr = TargetMetadata->CreateAttribute<bool>(AttributeName, SourceAttr->GetDefaultValue(), /*bAllowsInterpolation=*/false);
                // Copy all values from source to target
                for (PCGMetadataEntryKey EntryKey = 0; EntryKey < SourceMetadata->GetItemCountForChild(); ++EntryKey)
                {
                    if (SourceAttr->HasNonDefaultValue(EntryKey))
                    {
                        bool Value = SourceAttr->GetValueFromItemKey(EntryKey);
                        NewAttr->SetValue(EntryKey, Value);
                    }
                }
            }
            break;
        }
        default:
            UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Unsupported metadata attribute type for attribute: %s"), *AttributeName.ToString());
            break;
    }
}

float FAuracronPCGAdvancedFilterElement::GetMetadataAttributeValue(const FPCGPoint& Point, FName AttributeName, const UPCGMetadata* Metadata)
{
    if (!Metadata)
    {
        return 0.0f;
    }

    // Try to get the attribute value from metadata
    const FPCGMetadataAttribute<float>* FloatAttr = Metadata->GetConstTypedAttribute<float>(AttributeName);
    if (FloatAttr)
    {
        PCGMetadataEntryKey EntryKey = Point.MetadataEntry;
        return FloatAttr->GetValueFromItemKey(EntryKey);
    }

    // Try integer attribute
    const FPCGMetadataAttribute<int32>* IntAttr = Metadata->GetConstTypedAttribute<int32>(AttributeName);
    if (IntAttr)
    {
        PCGMetadataEntryKey EntryKey = Point.MetadataEntry;
        return static_cast<float>(IntAttr->GetValueFromItemKey(EntryKey));
    }

    // Fallback to point properties
    if (AttributeName == TEXT("Density"))
    {
        return Point.Density;
    }
    else if (AttributeName == TEXT("Steepness"))
    {
        return Point.Steepness;
    }

    return 0.0f;
}
