// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Collision System Header
// Bridge 2.10: PCG Framework - Collision e Physics

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/PhysicsSettings.h"

#include "AuracronPCGCollisionSystem.generated.h"

// Collision test types
UENUM(BlueprintType)
enum class EAuracronPCGCollisionTestType : uint8
{
    LineTrace           UMETA(DisplayName = "Line Trace"),
    SphereTrace         UMETA(DisplayName = "Sphere Trace"),
    BoxTrace            UMETA(DisplayName = "Box Trace"),
    CapsuleTrace        UMETA(DisplayName = "Capsule Trace"),
    ConvexTrace         UMETA(DisplayName = "Convex Trace"),
    OverlapSphere       UMETA(DisplayName = "Overlap Sphere"),
    OverlapBox          UMETA(DisplayName = "Overlap Box"),
    OverlapCapsule      UMETA(DisplayName = "Overlap Capsule"),
    OverlapConvex       UMETA(DisplayName = "Overlap Convex"),
    RaycastSingle       UMETA(DisplayName = "Raycast Single"),
    RaycastMultiple     UMETA(DisplayName = "Raycast Multiple"),
    Custom              UMETA(DisplayName = "Custom")
};

// Collision response types
UENUM(BlueprintType)
enum class EAuracronPCGCollisionResponse : uint8
{
    Ignore              UMETA(DisplayName = "Ignore"),
    Overlap             UMETA(DisplayName = "Overlap"),
    Block               UMETA(DisplayName = "Block"),
    Custom              UMETA(DisplayName = "Custom")
};

// Physics simulation types
UENUM(BlueprintType)
enum class EAuracronPCGPhysicsSimulationType : uint8
{
    None                UMETA(DisplayName = "None"),
    Static              UMETA(DisplayName = "Static"),
    Kinematic           UMETA(DisplayName = "Kinematic"),
    Dynamic             UMETA(DisplayName = "Dynamic"),
    Simulated           UMETA(DisplayName = "Simulated"),
    Custom              UMETA(DisplayName = "Custom")
};

// Spatial query types
UENUM(BlueprintType)
enum class EAuracronPCGSpatialQueryType : uint8
{
    Nearest             UMETA(DisplayName = "Nearest"),
    WithinRadius        UMETA(DisplayName = "Within Radius"),
    WithinBox           UMETA(DisplayName = "Within Box"),
    WithinCone          UMETA(DisplayName = "Within Cone"),
    LineOfSight         UMETA(DisplayName = "Line of Sight"),
    Visibility          UMETA(DisplayName = "Visibility"),
    Accessibility       UMETA(DisplayName = "Accessibility"),
    Custom              UMETA(DisplayName = "Custom")
};

// Placement validation modes
UENUM(BlueprintType)
enum class EAuracronPCGPlacementValidation : uint8
{
    None                UMETA(DisplayName = "None"),
    GroundCheck         UMETA(DisplayName = "Ground Check"),
    ClearanceCheck      UMETA(DisplayName = "Clearance Check"),
    StabilityCheck      UMETA(DisplayName = "Stability Check"),
    AccessibilityCheck  UMETA(DisplayName = "Accessibility Check"),
    VisibilityCheck     UMETA(DisplayName = "Visibility Check"),
    ProximityCheck      UMETA(DisplayName = "Proximity Check"),
    CustomCheck         UMETA(DisplayName = "Custom Check"),
    AllChecks           UMETA(DisplayName = "All Checks")
};

// =============================================================================
// COLLISION DESCRIPTOR
// =============================================================================

/**
 * Collision Descriptor
 * Describes parameters for collision detection and physics simulation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGCollisionDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    EAuracronPCGCollisionTestType CollisionTestType = EAuracronPCGCollisionTestType::LineTrace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    TEnumAsByte<ECollisionChannel> CollisionChannel = ECC_WorldStatic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    EAuracronPCGCollisionResponse CollisionResponse = EAuracronPCGCollisionResponse::Block;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trace Parameters")
    float TraceDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trace Parameters")
    FVector TraceDirection = FVector(0.0f, 0.0f, -1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape Parameters")
    float SphereRadius = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape Parameters")
    FVector BoxExtent = FVector(50.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape Parameters")
    float CapsuleRadius = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape Parameters")
    float CapsuleHalfHeight = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bTraceComplex = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bIgnoreSelf = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bReturnPhysicalMaterial = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    TArray<TEnumAsByte<EObjectTypeQuery>> ObjectTypes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    TArray<AActor*> ActorsToIgnore;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterByTag = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByTag"))
    TArray<FName> RequiredTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterByClass = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByClass"))
    TArray<TSubclassOf<AActor>> AllowedClasses;

    FAuracronPCGCollisionDescriptor()
    {
        CollisionTestType = EAuracronPCGCollisionTestType::LineTrace;
        CollisionChannel = ECC_WorldStatic;
        CollisionResponse = EAuracronPCGCollisionResponse::Block;
        TraceDistance = 1000.0f;
        TraceDirection = FVector(0.0f, 0.0f, -1.0f);
        SphereRadius = 50.0f;
        BoxExtent = FVector(50.0f);
        CapsuleRadius = 50.0f;
        CapsuleHalfHeight = 100.0f;
        bTraceComplex = false;
        bIgnoreSelf = true;
        bReturnPhysicalMaterial = false;
        bFilterByTag = false;
        bFilterByClass = false;
    }
};

// =============================================================================
// PHYSICS DESCRIPTOR
// =============================================================================

/**
 * Physics Descriptor
 * Describes parameters for physics simulation and body setup
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGPhysicsDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    EAuracronPCGPhysicsSimulationType SimulationType = EAuracronPCGPhysicsSimulationType::Static;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnableGravity = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Mass = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float LinearDamping = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float AngularDamping = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Friction = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Restitution = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Density = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bLockXTranslation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bLockYTranslation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bLockZTranslation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bLockXRotation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bLockYRotation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bLockZRotation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bStartAwake = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bGenerateWakeEvents = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    float SleepThresholdMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    float StabilizationThresholdMultiplier = 1.0f;

    FAuracronPCGPhysicsDescriptor()
    {
        SimulationType = EAuracronPCGPhysicsSimulationType::Static;
        bEnableGravity = true;
        Mass = 1.0f;
        LinearDamping = 0.01f;
        AngularDamping = 0.0f;
        Friction = 0.7f;
        Restitution = 0.3f;
        Density = 1.0f;
        bLockXTranslation = false;
        bLockYTranslation = false;
        bLockZTranslation = false;
        bLockXRotation = false;
        bLockYRotation = false;
        bLockZRotation = false;
        bStartAwake = true;
        bGenerateWakeEvents = false;
        SleepThresholdMultiplier = 1.0f;
        StabilizationThresholdMultiplier = 1.0f;
    }
};

// =============================================================================
// SPATIAL QUERY DESCRIPTOR
// =============================================================================

/**
 * Spatial Query Descriptor
 * Describes parameters for spatial queries and proximity testing
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGSpatialQueryDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    EAuracronPCGSpatialQueryType QueryType = EAuracronPCGSpatialQueryType::WithinRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float SearchRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector SearchExtent = FVector(500.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float ConeAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector ConeDirection = FVector::ForwardVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    int32 MaxResults = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    bool bSortByDistance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterByType = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByType"))
    TArray<TSubclassOf<AActor>> TargetTypes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterByAttribute = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByAttribute"))
    FString AttributeName = TEXT("Type");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByAttribute"))
    FString AttributeValue = TEXT("Target");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseComplexCollision = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bIgnoreBlockingHits = false;

    FAuracronPCGSpatialQueryDescriptor()
    {
        QueryType = EAuracronPCGSpatialQueryType::WithinRadius;
        SearchRadius = 500.0f;
        SearchExtent = FVector(500.0f);
        ConeAngle = 45.0f;
        ConeDirection = FVector::ForwardVector;
        MaxResults = 10;
        bSortByDistance = true;
        bFilterByType = false;
        bFilterByAttribute = false;
        AttributeName = TEXT("Type");
        AttributeValue = TEXT("Target");
        bUseComplexCollision = false;
        bIgnoreBlockingHits = false;
    }
};

// =============================================================================
// COLLISION TESTER
// =============================================================================

/**
 * Collision Tester
 * Tests collision and physics properties for point placement validation
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGCollisionTesterSettings, FAuracronPCGCollisionTesterElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGCollisionTesterSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGCollisionTesterSettings();

    // Collision configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FAuracronPCGCollisionDescriptor CollisionDescriptor;

    // Placement validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    EAuracronPCGPlacementValidation ValidationMode = EAuracronPCGPlacementValidation::GroundCheck;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    bool bProjectToSurface = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float ProjectionDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float MinGroundAngle = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float MaxGroundAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float ClearanceRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float ClearanceHeight = 200.0f;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputHitResults = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputCollisionInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bFilterInvalidPoints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString HitDistanceAttribute = TEXT("HitDistance");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString HitNormalAttribute = TEXT("HitNormal");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString ValidPlacementAttribute = TEXT("ValidPlacement");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGCollisionTesterElement, UAuracronPCGCollisionTesterSettings)

// =============================================================================
// PHYSICS SIMULATOR
// =============================================================================

/**
 * Physics Simulator
 * Simulates physics properties and applies physics-based transformations
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGPhysicsSimulatorSettings, FAuracronPCGPhysicsSimulatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGPhysicsSimulatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGPhysicsSimulatorSettings();

    // Physics configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    FAuracronPCGPhysicsDescriptor PhysicsDescriptor;

    // Simulation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    bool bRunSimulation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float SimulationTime = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float TimeStep = 0.016f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    int32 MaxIterations = 60;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    bool bStabilizeResults = true;

    // Forces and impulses
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Forces")
    bool bApplyInitialForce = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Forces", meta = (EditCondition = "bApplyInitialForce"))
    FVector InitialForce = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Forces")
    bool bApplyInitialImpulse = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Forces", meta = (EditCondition = "bApplyInitialImpulse"))
    FVector InitialImpulse = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Forces")
    bool bApplyGravity = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Forces", meta = (EditCondition = "bApplyGravity"))
    FVector GravityOverride = FVector(0.0f, 0.0f, -980.0f);

    // Constraints
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bUseConstraints = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints", meta = (EditCondition = "bUseConstraints"))
    FVector ConstraintCenter = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints", meta = (EditCondition = "bUseConstraints"))
    float ConstraintRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints", meta = (EditCondition = "bUseConstraints"))
    bool bBounceOffConstraints = true;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputVelocity = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputAngularVelocity = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputPhysicsState = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString VelocityAttribute = TEXT("Velocity");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString AngularVelocityAttribute = TEXT("AngularVelocity");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGPhysicsSimulatorElement, UAuracronPCGPhysicsSimulatorSettings)

// =============================================================================
// SPATIAL QUERY PROCESSOR
// =============================================================================

/**
 * Spatial Query Processor
 * Performs spatial queries and proximity testing for point relationships
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGSpatialQuerySettings, FAuracronPCGSpatialQueryElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGSpatialQuerySettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGSpatialQuerySettings();

    // Query configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FAuracronPCGSpatialQueryDescriptor QueryDescriptor;

    // Target selection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targets")
    bool bUseInputAsTargets = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targets")
    bool bQueryWorldActors = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targets")
    bool bQueryOtherPoints = false;

    // Relationship analysis
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    bool bAnalyzeRelationships = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis", meta = (EditCondition = "bAnalyzeRelationships"))
    bool bCalculateDistances = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis", meta = (EditCondition = "bAnalyzeRelationships"))
    bool bCalculateAngles = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis", meta = (EditCondition = "bAnalyzeRelationships"))
    bool bCalculateVisibility = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis", meta = (EditCondition = "bAnalyzeRelationships"))
    bool bCalculateAccessibility = false;

    // Clustering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering")
    bool bPerformClustering = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering", meta = (EditCondition = "bPerformClustering"))
    float ClusterRadius = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering", meta = (EditCondition = "bPerformClustering"))
    int32 MinClusterSize = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering", meta = (EditCondition = "bPerformClustering"))
    int32 MaxClusterSize = 10;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputQueryResults = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputRelationshipData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputClusterData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString NearestDistanceAttribute = TEXT("NearestDistance");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString NeighborCountAttribute = TEXT("NeighborCount");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString ClusterIDAttribute = TEXT("ClusterID");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGSpatialQueryElement, UAuracronPCGSpatialQuerySettings)

// =============================================================================
// PLACEMENT VALIDATOR
// =============================================================================

/**
 * Placement Validator
 * Validates point placement using comprehensive collision and physics checks
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGPlacementValidatorSettings, FAuracronPCGPlacementValidatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGPlacementValidatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGPlacementValidatorSettings();

    // Validation configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    EAuracronPCGPlacementValidation ValidationMode = EAuracronPCGPlacementValidation::AllChecks;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    FAuracronPCGCollisionDescriptor CollisionDescriptor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    FAuracronPCGSpatialQueryDescriptor SpatialQueryDescriptor;

    // Ground validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ground Check")
    bool bRequireGroundContact = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ground Check")
    float MaxGroundDistance = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ground Check")
    float MinSlopeAngle = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ground Check")
    float MaxSlopeAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ground Check")
    bool bSnapToGround = true;

    // Clearance validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clearance Check")
    float ClearanceRadius = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clearance Check")
    float ClearanceHeight = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clearance Check")
    bool bCheckOverhead = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clearance Check")
    float OverheadClearance = 300.0f;

    // Stability validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stability Check")
    bool bCheckStability = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stability Check")
    float StabilityRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stability Check")
    int32 StabilitySamples = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stability Check")
    float MinStabilityScore = 0.7f;

    // Proximity validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Proximity Check")
    float MinProximityDistance = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Proximity Check")
    float MaxProximityDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Proximity Check")
    bool bAvoidOverlap = true;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputValidationScores = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputFailureReasons = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString ValidationScoreAttribute = TEXT("ValidationScore");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString FailureReasonAttribute = TEXT("FailureReason");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGPlacementValidatorElement, UAuracronPCGPlacementValidatorSettings)

// =============================================================================
// COLLISION SYSTEM UTILITIES
// =============================================================================

/**
 * Collision System Utilities
 * Utility functions for collision detection and physics simulation
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGCollisionSystemUtils : public UObject
{
    GENERATED_BODY()

public:
    // Collision testing utilities
    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static bool PerformCollisionTest(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult);

    // C++ only function (TArray<FOverlapResult> não suportado pelo Blueprint)
    static bool PerformOverlapTest(UWorld* World, const FVector& Location, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static bool ValidatePlacement(UWorld* World, const FVector& Location, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, EAuracronPCGPlacementValidation ValidationMode);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static FVector ProjectToSurface(UWorld* World, const FVector& Location, float ProjectionDistance, const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    // Spatial query utilities
    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static TArray<AActor*> PerformSpatialQuery(UWorld* World, const FVector& Location, const FAuracronPCGSpatialQueryDescriptor& QueryDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static float CalculateDistance(const FVector& PointA, const FVector& PointB);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static float CalculateAngle(const FVector& PointA, const FVector& PointB, const FVector& Reference);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static bool CheckLineOfSight(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    // Physics simulation utilities
    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static FVector SimulatePhysicsStep(const FVector& Position, const FVector& Velocity, const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static FVector CalculateGravityForce(const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static bool IsPhysicsStable(const FVector& Velocity, const FVector& AngularVelocity, float Threshold = 0.1f);

    // Validation utilities
    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static float CalculateGroundAngle(const FVector& Normal);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static float CalculateStabilityScore(UWorld* World, const FVector& Location, float Radius, int32 Samples, const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static bool CheckClearance(UWorld* World, const FVector& Location, float Radius, float Height, const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static bool ValidateCollisionDescriptor(const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Collision System Utils")
    static bool ValidatePhysicsDescriptor(const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor);
};

// Namespace for collision system utility functions
namespace AuracronPCGCollisionSystemUtils
{
    AURACRONPCGFRAMEWORK_API FCollisionQueryParams CreateCollisionQueryParams(const FAuracronPCGCollisionDescriptor& CollisionDescriptor, const AActor* IgnoreActor = nullptr);
    AURACRONPCGFRAMEWORK_API FCollisionObjectQueryParams CreateObjectQueryParams(const FAuracronPCGCollisionDescriptor& CollisionDescriptor);
    AURACRONPCGFRAMEWORK_API FCollisionShape CreateCollisionShape(const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    AURACRONPCGFRAMEWORK_API bool LineTrace(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult);
    AURACRONPCGFRAMEWORK_API bool SphereTrace(UWorld* World, const FVector& Start, const FVector& End, float Radius, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult);
    AURACRONPCGFRAMEWORK_API bool BoxTrace(UWorld* World, const FVector& Start, const FVector& End, const FVector& Extent, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult);
    AURACRONPCGFRAMEWORK_API bool CapsuleTrace(UWorld* World, const FVector& Start, const FVector& End, float Radius, float HalfHeight, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult);

    AURACRONPCGFRAMEWORK_API bool SphereOverlap(UWorld* World, const FVector& Location, float Radius, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps);
    AURACRONPCGFRAMEWORK_API bool BoxOverlap(UWorld* World, const FVector& Location, const FVector& Extent, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps);
    AURACRONPCGFRAMEWORK_API bool CapsuleOverlap(UWorld* World, const FVector& Location, float Radius, float HalfHeight, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps);

    AURACRONPCGFRAMEWORK_API void ApplyPhysicsProperties(UPrimitiveComponent* Component, const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor);
    AURACRONPCGFRAMEWORK_API FBodyInstance* CreateBodyInstance(const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor);
    AURACRONPCGFRAMEWORK_API void ConfigureCollisionResponse(UPrimitiveComponent* Component, const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    AURACRONPCGFRAMEWORK_API TArray<FVector> GenerateStabilitySamplePoints(const FVector& Center, float Radius, int32 Samples);
    AURACRONPCGFRAMEWORK_API float CalculateTerrainStability(UWorld* World, const TArray<FVector>& SamplePoints, const FAuracronPCGCollisionDescriptor& CollisionDescriptor);
    AURACRONPCGFRAMEWORK_API bool IsLocationAccessible(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor);

    AURACRONPCGFRAMEWORK_API void FilterActorsByTag(TArray<AActor*>& Actors, const TArray<FName>& RequiredTags);
    AURACRONPCGFRAMEWORK_API void FilterActorsByClass(TArray<AActor*>& Actors, const TArray<TSubclassOf<AActor>>& AllowedClasses);
    AURACRONPCGFRAMEWORK_API void SortActorsByDistance(TArray<AActor*>& Actors, const FVector& ReferenceLocation);
}
