// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionLighting.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionLighting() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLightingManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLightingManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingDescriptor();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingStatistics();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronLightingStreamingState *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLightingStreamingState;
static UEnum* EAuracronLightingStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightingStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLightingStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLightingStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronLightingStreamingState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightingStreamingState>()
{
	return EAuracronLightingStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EAuracronLightingStreamingState::Active" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lighting streaming states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronLightingStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronLightingStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronLightingStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting streaming states" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronLightingStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronLightingStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLightingStreamingState::Unloaded", (int64)EAuracronLightingStreamingState::Unloaded },
		{ "EAuracronLightingStreamingState::Loading", (int64)EAuracronLightingStreamingState::Loading },
		{ "EAuracronLightingStreamingState::Loaded", (int64)EAuracronLightingStreamingState::Loaded },
		{ "EAuracronLightingStreamingState::Active", (int64)EAuracronLightingStreamingState::Active },
		{ "EAuracronLightingStreamingState::Unloading", (int64)EAuracronLightingStreamingState::Unloading },
		{ "EAuracronLightingStreamingState::Failed", (int64)EAuracronLightingStreamingState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLightingStreamingState",
	"EAuracronLightingStreamingState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightingStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLightingStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLightingStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronLightingStreamingState *********************************************

// ********** Begin Enum EAuracronLightingLODLevel *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLightingLODLevel;
static UEnum* EAuracronLightingLODLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightingLODLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLightingLODLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLightingLODLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronLightingLODLevel.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightingLODLevel>()
{
	return EAuracronLightingLODLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lighting LOD levels\n" },
#endif
		{ "LOD0.DisplayName", "LOD 0 (Highest)" },
		{ "LOD0.Name", "EAuracronLightingLODLevel::LOD0" },
		{ "LOD1.DisplayName", "LOD 1" },
		{ "LOD1.Name", "EAuracronLightingLODLevel::LOD1" },
		{ "LOD2.DisplayName", "LOD 2" },
		{ "LOD2.Name", "EAuracronLightingLODLevel::LOD2" },
		{ "LOD3.DisplayName", "LOD 3" },
		{ "LOD3.Name", "EAuracronLightingLODLevel::LOD3" },
		{ "LOD4.DisplayName", "LOD 4 (Lowest)" },
		{ "LOD4.Name", "EAuracronLightingLODLevel::LOD4" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting LOD levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLightingLODLevel::LOD0", (int64)EAuracronLightingLODLevel::LOD0 },
		{ "EAuracronLightingLODLevel::LOD1", (int64)EAuracronLightingLODLevel::LOD1 },
		{ "EAuracronLightingLODLevel::LOD2", (int64)EAuracronLightingLODLevel::LOD2 },
		{ "EAuracronLightingLODLevel::LOD3", (int64)EAuracronLightingLODLevel::LOD3 },
		{ "EAuracronLightingLODLevel::LOD4", (int64)EAuracronLightingLODLevel::LOD4 },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLightingLODLevel",
	"EAuracronLightingLODLevel",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightingLODLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLightingLODLevel.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLightingLODLevel.InnerSingleton;
}
// ********** End Enum EAuracronLightingLODLevel ***************************************************

// ********** Begin Enum EAuracronLightType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLightType;
static UEnum* EAuracronLightType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLightType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLightType"));
	}
	return Z_Registration_Info_UEnum_EAuracronLightType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightType>()
{
	return EAuracronLightType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Area.DisplayName", "Area" },
		{ "Area.Name", "EAuracronLightType::Area" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Light types\n" },
#endif
		{ "Directional.DisplayName", "Directional" },
		{ "Directional.Name", "EAuracronLightType::Directional" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
		{ "Point.DisplayName", "Point" },
		{ "Point.Name", "EAuracronLightType::Point" },
		{ "Sky.DisplayName", "Sky" },
		{ "Sky.Name", "EAuracronLightType::Sky" },
		{ "Spot.DisplayName", "Spot" },
		{ "Spot.Name", "EAuracronLightType::Spot" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Light types" },
#endif
		{ "Volumetric.DisplayName", "Volumetric" },
		{ "Volumetric.Name", "EAuracronLightType::Volumetric" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLightType::Directional", (int64)EAuracronLightType::Directional },
		{ "EAuracronLightType::Point", (int64)EAuracronLightType::Point },
		{ "EAuracronLightType::Spot", (int64)EAuracronLightType::Spot },
		{ "EAuracronLightType::Sky", (int64)EAuracronLightType::Sky },
		{ "EAuracronLightType::Area", (int64)EAuracronLightType::Area },
		{ "EAuracronLightType::Volumetric", (int64)EAuracronLightType::Volumetric },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLightType",
	"EAuracronLightType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLightType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLightType.InnerSingleton;
}
// ********** End Enum EAuracronLightType **********************************************************

// ********** Begin Enum EAuracronLightMobility ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLightMobility;
static UEnum* EAuracronLightMobility_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightMobility.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLightMobility.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLightMobility"));
	}
	return Z_Registration_Info_UEnum_EAuracronLightMobility.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLightMobility>()
{
	return EAuracronLightMobility_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Light mobility\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
		{ "Movable.DisplayName", "Movable" },
		{ "Movable.Name", "EAuracronLightMobility::Movable" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronLightMobility::Static" },
		{ "Stationary.DisplayName", "Stationary" },
		{ "Stationary.Name", "EAuracronLightMobility::Stationary" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Light mobility" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLightMobility::Static", (int64)EAuracronLightMobility::Static },
		{ "EAuracronLightMobility::Stationary", (int64)EAuracronLightMobility::Stationary },
		{ "EAuracronLightMobility::Movable", (int64)EAuracronLightMobility::Movable },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLightMobility",
	"EAuracronLightMobility",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility()
{
	if (!Z_Registration_Info_UEnum_EAuracronLightMobility.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLightMobility.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLightMobility.InnerSingleton;
}
// ********** End Enum EAuracronLightMobility ******************************************************

// ********** Begin Enum EAuracronShadowQuality ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronShadowQuality;
static UEnum* EAuracronShadowQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronShadowQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronShadowQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronShadowQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronShadowQuality.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronShadowQuality>()
{
	return EAuracronShadowQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Shadow quality\n" },
#endif
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronShadowQuality::Epic" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronShadowQuality::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronShadowQuality::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronShadowQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shadow quality" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronShadowQuality::Low", (int64)EAuracronShadowQuality::Low },
		{ "EAuracronShadowQuality::Medium", (int64)EAuracronShadowQuality::Medium },
		{ "EAuracronShadowQuality::High", (int64)EAuracronShadowQuality::High },
		{ "EAuracronShadowQuality::Epic", (int64)EAuracronShadowQuality::Epic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronShadowQuality",
	"EAuracronShadowQuality",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronShadowQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronShadowQuality.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronShadowQuality.InnerSingleton;
}
// ********** End Enum EAuracronShadowQuality ******************************************************

// ********** Begin ScriptStruct FAuracronLightingConfiguration ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration;
class UScriptStruct* FAuracronLightingConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLightingConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLightingConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lighting Configuration\n * Configuration settings for lighting streaming in world partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting Configuration\nConfiguration settings for lighting streaming in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLightingStreaming_MetaData[] = {
		{ "Category", "Lighting" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLightingLOD_MetaData[] = {
		{ "Category", "Lighting" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLumenIntegration_MetaData[] = {
		{ "Category", "Lighting" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLightmapStreaming_MetaData[] = {
		{ "Category", "Lighting" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableShadowStreaming_MetaData[] = {
		{ "Category", "Lighting" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingStreamingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingUnloadingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentLightingOperations_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLODDistance_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistanceMultiplier_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevel_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultShadowQuality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalLightIntensityMultiplier_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShadowDistanceScale_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLightingMemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLightingCaching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLightingCulling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLumenGlobalIllumination_MetaData[] = {
		{ "Category", "Lumen" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLumenReflections_MetaData[] = {
		{ "Category", "Lumen" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenSceneViewDistance_MetaData[] = {
		{ "Category", "Lumen" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightmapResolution_MetaData[] = {
		{ "Category", "Lightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCompressLightmaps_MetaData[] = {
		{ "Category", "Lightmap" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLightingDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogLightingOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableLightingStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLightingStreaming;
	static void NewProp_bEnableLightingLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLightingLOD;
	static void NewProp_bEnableLumenIntegration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLumenIntegration;
	static void NewProp_bEnableLightmapStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLightmapStreaming;
	static void NewProp_bEnableShadowStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableShadowStreaming;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightingStreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightingUnloadingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentLightingOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseLODDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistanceMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultShadowQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultShadowQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalLightIntensityMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShadowDistanceScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxLightingMemoryUsageMB;
	static void NewProp_bEnableLightingCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLightingCaching;
	static void NewProp_bEnableLightingCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLightingCulling;
	static void NewProp_bEnableLumenGlobalIllumination_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLumenGlobalIllumination;
	static void NewProp_bEnableLumenReflections_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLumenReflections;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LumenSceneViewDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LightmapResolution;
	static void NewProp_bCompressLightmaps_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCompressLightmaps;
	static void NewProp_bEnableLightingDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLightingDebug;
	static void NewProp_bLogLightingOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogLightingOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLightingConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingStreaming_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLightingStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingStreaming = { "bEnableLightingStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLightingStreaming_MetaData), NewProp_bEnableLightingStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingLOD_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLightingLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingLOD = { "bEnableLightingLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLightingLOD_MetaData), NewProp_bEnableLightingLOD_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenIntegration_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLumenIntegration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenIntegration = { "bEnableLumenIntegration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenIntegration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLumenIntegration_MetaData), NewProp_bEnableLumenIntegration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightmapStreaming_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLightmapStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightmapStreaming = { "bEnableLightmapStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightmapStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLightmapStreaming_MetaData), NewProp_bEnableLightmapStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableShadowStreaming_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableShadowStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableShadowStreaming = { "bEnableShadowStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableShadowStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableShadowStreaming_MetaData), NewProp_bEnableShadowStreaming_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LightingStreamingDistance = { "LightingStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, LightingStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingStreamingDistance_MetaData), NewProp_LightingStreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LightingUnloadingDistance = { "LightingUnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, LightingUnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingUnloadingDistance_MetaData), NewProp_LightingUnloadingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_MaxConcurrentLightingOperations = { "MaxConcurrentLightingOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, MaxConcurrentLightingOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentLightingOperations_MetaData), NewProp_MaxConcurrentLightingOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_BaseLODDistance = { "BaseLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, BaseLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLODDistance_MetaData), NewProp_BaseLODDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LODDistanceMultiplier = { "LODDistanceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, LODDistanceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistanceMultiplier_MetaData), NewProp_LODDistanceMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_MaxLODLevel = { "MaxLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, MaxLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevel_MetaData), NewProp_MaxLODLevel_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_DefaultShadowQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_DefaultShadowQuality = { "DefaultShadowQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, DefaultShadowQuality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultShadowQuality_MetaData), NewProp_DefaultShadowQuality_MetaData) }; // 317022883
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_GlobalLightIntensityMultiplier = { "GlobalLightIntensityMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, GlobalLightIntensityMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalLightIntensityMultiplier_MetaData), NewProp_GlobalLightIntensityMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_ShadowDistanceScale = { "ShadowDistanceScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, ShadowDistanceScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShadowDistanceScale_MetaData), NewProp_ShadowDistanceScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_MaxLightingMemoryUsageMB = { "MaxLightingMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, MaxLightingMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLightingMemoryUsageMB_MetaData), NewProp_MaxLightingMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCaching_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLightingCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCaching = { "bEnableLightingCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLightingCaching_MetaData), NewProp_bEnableLightingCaching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCulling_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLightingCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCulling = { "bEnableLightingCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLightingCulling_MetaData), NewProp_bEnableLightingCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenGlobalIllumination_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLumenGlobalIllumination = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenGlobalIllumination = { "bEnableLumenGlobalIllumination", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenGlobalIllumination_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLumenGlobalIllumination_MetaData), NewProp_bEnableLumenGlobalIllumination_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenReflections_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLumenReflections = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenReflections = { "bEnableLumenReflections", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenReflections_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLumenReflections_MetaData), NewProp_bEnableLumenReflections_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LumenSceneViewDistance = { "LumenSceneViewDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, LumenSceneViewDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenSceneViewDistance_MetaData), NewProp_LumenSceneViewDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LightmapResolution = { "LightmapResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfiguration, LightmapResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightmapResolution_MetaData), NewProp_LightmapResolution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bCompressLightmaps_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bCompressLightmaps = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bCompressLightmaps = { "bCompressLightmaps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bCompressLightmaps_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCompressLightmaps_MetaData), NewProp_bCompressLightmaps_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingDebug_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bEnableLightingDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingDebug = { "bEnableLightingDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLightingDebug_MetaData), NewProp_bEnableLightingDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bLogLightingOperations_SetBit(void* Obj)
{
	((FAuracronLightingConfiguration*)Obj)->bLogLightingOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bLogLightingOperations = { "bLogLightingOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfiguration), &Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bLogLightingOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogLightingOperations_MetaData), NewProp_bLogLightingOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenIntegration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightmapStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableShadowStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LightingStreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LightingUnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_MaxConcurrentLightingOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_BaseLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LODDistanceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_MaxLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_DefaultShadowQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_DefaultShadowQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_GlobalLightIntensityMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_ShadowDistanceScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_MaxLightingMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenGlobalIllumination,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLumenReflections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LumenSceneViewDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_LightmapResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bCompressLightmaps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bEnableLightingDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewProp_bLogLightingOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLightingConfiguration",
	Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::PropPointers),
	sizeof(FAuracronLightingConfiguration),
	alignof(FAuracronLightingConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLightingConfiguration **************************************

// ********** Begin ScriptStruct FAuracronLightingDescriptor ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor;
class UScriptStruct* FAuracronLightingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLightingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLightingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lighting Descriptor\n * Descriptor for lighting sources and their properties\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting Descriptor\nDescriptor for lighting sources and their properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingName_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceActorId_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightType_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightMobility_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLODLevel_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightColor_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Intensity_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttenuationRadius_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Temperature_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastShadows_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastStaticShadows_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastDynamicShadows_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectGlobalIllumination_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEnabled_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Lighting Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LightType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LightType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LightMobility_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LightMobility;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentLODLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentLODLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttenuationRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Temperature;
	static void NewProp_bCastShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastShadows;
	static void NewProp_bCastStaticShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastStaticShadows;
	static void NewProp_bCastDynamicShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastDynamicShadows;
	static void NewProp_bAffectGlobalIllumination_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectGlobalIllumination;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLightingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightingName = { "LightingName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, LightingName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingName_MetaData), NewProp_LightingName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_SourceActorId = { "SourceActorId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, SourceActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceActorId_MetaData), NewProp_SourceActorId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightType = { "LightType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, LightType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightType_MetaData), NewProp_LightType_MetaData) }; // 767143608
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightMobility_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightMobility = { "LightMobility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, LightMobility), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightMobility, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightMobility_MetaData), NewProp_LightMobility_MetaData) }; // 1252204914
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 2718364282
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CurrentLODLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CurrentLODLevel = { "CurrentLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, CurrentLODLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLODLevel_MetaData), NewProp_CurrentLODLevel_MetaData) }; // 2925161937
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightColor = { "LightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, LightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightColor_MetaData), NewProp_LightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, Intensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Intensity_MetaData), NewProp_Intensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_AttenuationRadius = { "AttenuationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, AttenuationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttenuationRadius_MetaData), NewProp_AttenuationRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Temperature = { "Temperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, Temperature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Temperature_MetaData), NewProp_Temperature_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastShadows_SetBit(void* Obj)
{
	((FAuracronLightingDescriptor*)Obj)->bCastShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastShadows = { "bCastShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingDescriptor), &Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastShadows_MetaData), NewProp_bCastShadows_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastStaticShadows_SetBit(void* Obj)
{
	((FAuracronLightingDescriptor*)Obj)->bCastStaticShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastStaticShadows = { "bCastStaticShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingDescriptor), &Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastStaticShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastStaticShadows_MetaData), NewProp_bCastStaticShadows_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastDynamicShadows_SetBit(void* Obj)
{
	((FAuracronLightingDescriptor*)Obj)->bCastDynamicShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastDynamicShadows = { "bCastDynamicShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingDescriptor), &Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastDynamicShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastDynamicShadows_MetaData), NewProp_bCastDynamicShadows_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bAffectGlobalIllumination_SetBit(void* Obj)
{
	((FAuracronLightingDescriptor*)Obj)->bAffectGlobalIllumination = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bAffectGlobalIllumination = { "bAffectGlobalIllumination", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingDescriptor), &Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bAffectGlobalIllumination_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectGlobalIllumination_MetaData), NewProp_bAffectGlobalIllumination_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((FAuracronLightingDescriptor*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingDescriptor), &Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEnabled_MetaData), NewProp_bIsEnabled_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingDescriptor, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightingName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_SourceActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightMobility_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightMobility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CurrentLODLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CurrentLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_AttenuationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_Temperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastShadows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastStaticShadows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bCastDynamicShadows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bAffectGlobalIllumination,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewProp_LastAccessTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLightingDescriptor",
	Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::PropPointers),
	sizeof(FAuracronLightingDescriptor),
	alignof(FAuracronLightingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLightingDescriptor *****************************************

// ********** Begin ScriptStruct FAuracronLightingStatistics ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics;
class UScriptStruct* FAuracronLightingStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLightingStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLightingStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lighting Statistics\n * Performance and usage statistics for lighting system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting Statistics\nPerformance and usage statistics for lighting system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLightSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedLightSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveLightSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingLightSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODTransitions_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightmapOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShadowOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalLightSources;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedLightSources;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveLightSources;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingLightSources;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODTransitions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightingEfficiency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LightmapOperations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ShadowOperations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLightingStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_TotalLightSources = { "TotalLightSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, TotalLightSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLightSources_MetaData), NewProp_TotalLightSources_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LoadedLightSources = { "LoadedLightSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, LoadedLightSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedLightSources_MetaData), NewProp_LoadedLightSources_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_ActiveLightSources = { "ActiveLightSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, ActiveLightSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveLightSources_MetaData), NewProp_ActiveLightSources_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_StreamingLightSources = { "StreamingLightSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, StreamingLightSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingLightSources_MetaData), NewProp_StreamingLightSources_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_AverageLoadingTime = { "AverageLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, AverageLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadingTime_MetaData), NewProp_AverageLoadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LODTransitions = { "LODTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, LODTransitions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODTransitions_MetaData), NewProp_LODTransitions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_FailedOperations = { "FailedOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, FailedOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedOperations_MetaData), NewProp_FailedOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LightingEfficiency = { "LightingEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, LightingEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingEfficiency_MetaData), NewProp_LightingEfficiency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LightmapOperations = { "LightmapOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, LightmapOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightmapOperations_MetaData), NewProp_LightmapOperations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_ShadowOperations = { "ShadowOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, ShadowOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShadowOperations_MetaData), NewProp_ShadowOperations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_TotalLightSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LoadedLightSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_ActiveLightSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_StreamingLightSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_AverageLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LODTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_FailedOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LightingEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LightmapOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_ShadowOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLightingStatistics",
	Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::PropPointers),
	sizeof(FAuracronLightingStatistics),
	alignof(FAuracronLightingStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLightingStatistics *****************************************

// ********** Begin Delegate FOnLightingLoaded *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms
	{
		FString LightingId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms, LightingId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "OnLightingLoaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLightingManager::FOnLightingLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnLightingLoaded, const FString& LightingId, bool bSuccess)
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms
	{
		FString LightingId;
		bool bSuccess;
	};
	AuracronWorldPartitionLightingManager_eventOnLightingLoaded_Parms Parms;
	Parms.LightingId=LightingId;
	Parms.bSuccess=bSuccess ? true : false;
	OnLightingLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLightingLoaded *******************************************************

// ********** Begin Delegate FOnLightingUnloaded ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingUnloaded_Parms
	{
		FString LightingId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventOnLightingUnloaded_Parms, LightingId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::NewProp_LightingId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "OnLightingUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLightingManager::FOnLightingUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnLightingUnloaded, const FString& LightingId)
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingUnloaded_Parms
	{
		FString LightingId;
	};
	AuracronWorldPartitionLightingManager_eventOnLightingUnloaded_Parms Parms;
	Parms.LightingId=LightingId;
	OnLightingUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLightingUnloaded *****************************************************

// ********** Begin Delegate FOnLightingActivated **************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingActivated_Parms
	{
		FString LightingId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventOnLightingActivated_Parms, LightingId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::NewProp_LightingId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "OnLightingActivated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLightingManager::FOnLightingActivated_DelegateWrapper(const FMulticastScriptDelegate& OnLightingActivated, const FString& LightingId)
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingActivated_Parms
	{
		FString LightingId;
	};
	AuracronWorldPartitionLightingManager_eventOnLightingActivated_Parms Parms;
	Parms.LightingId=LightingId;
	OnLightingActivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLightingActivated ****************************************************

// ********** Begin Delegate FOnLightingDeactivated ************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingDeactivated_Parms
	{
		FString LightingId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventOnLightingDeactivated_Parms, LightingId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::NewProp_LightingId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "OnLightingDeactivated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingDeactivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingDeactivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLightingManager::FOnLightingDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnLightingDeactivated, const FString& LightingId)
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingDeactivated_Parms
	{
		FString LightingId;
	};
	AuracronWorldPartitionLightingManager_eventOnLightingDeactivated_Parms Parms;
	Parms.LightingId=LightingId;
	OnLightingDeactivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLightingDeactivated **************************************************

// ********** Begin Delegate FOnLightingLODChanged *************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingLODChanged_Parms
	{
		FString LightingId;
		EAuracronLightingLODLevel NewLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLOD_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventOnLightingLODChanged_Parms, LightingId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::NewProp_NewLOD = { "NewLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventOnLightingLODChanged_Parms, NewLOD), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel, METADATA_PARAMS(0, nullptr) }; // 2925161937
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::NewProp_NewLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "OnLightingLODChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingLODChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::AuracronWorldPartitionLightingManager_eventOnLightingLODChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLightingManager::FOnLightingLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLightingLODChanged, const FString& LightingId, EAuracronLightingLODLevel NewLOD)
{
	struct AuracronWorldPartitionLightingManager_eventOnLightingLODChanged_Parms
	{
		FString LightingId;
		EAuracronLightingLODLevel NewLOD;
	};
	AuracronWorldPartitionLightingManager_eventOnLightingLODChanged_Parms Parms;
	Parms.LightingId=LightingId;
	Parms.NewLOD=NewLOD;
	OnLightingLODChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLightingLODChanged ***************************************************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function ActivateLightSource ******
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics
{
	struct AuracronWorldPartitionLightingManager_eventActivateLightSource_Parms
	{
		FString LightingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Light activation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Light activation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventActivateLightSource_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventActivateLightSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventActivateLightSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "ActivateLightSource", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::AuracronWorldPartitionLightingManager_eventActivateLightSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::AuracronWorldPartitionLightingManager_eventActivateLightSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execActivateLightSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateLightSource(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function ActivateLightSource ********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function BuildLightmapsForCell ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics
{
	struct AuracronWorldPartitionLightingManager_eventBuildLightmapsForCell_Parms
	{
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lightmap management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lightmap management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventBuildLightmapsForCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventBuildLightmapsForCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventBuildLightmapsForCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "BuildLightmapsForCell", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::AuracronWorldPartitionLightingManager_eventBuildLightmapsForCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::AuracronWorldPartitionLightingManager_eventBuildLightmapsForCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execBuildLightmapsForCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BuildLightmapsForCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function BuildLightmapsForCell ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function CalculateLODForDistance **
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics
{
	struct AuracronWorldPartitionLightingManager_eventCalculateLODForDistance_Parms
	{
		float Distance;
		EAuracronLightingLODLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventCalculateLODForDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventCalculateLODForDistance_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel, METADATA_PARAMS(0, nullptr) }; // 2925161937
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "CalculateLODForDistance", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::AuracronWorldPartitionLightingManager_eventCalculateLODForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::AuracronWorldPartitionLightingManager_eventCalculateLODForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execCalculateLODForDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLightingLODLevel*)Z_Param__Result=P_THIS->CalculateLODForDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function CalculateLODForDistance ****

// ********** Begin Class UAuracronWorldPartitionLightingManager Function CreateLightSource ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics
{
	struct AuracronWorldPartitionLightingManager_eventCreateLightSource_Parms
	{
		FString SourceActorId;
		EAuracronLightType LightType;
		FVector Location;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Light source creation and management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Light source creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LightType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LightType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_SourceActorId = { "SourceActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventCreateLightSource_Parms, SourceActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceActorId_MetaData), NewProp_SourceActorId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_LightType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_LightType = { "LightType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventCreateLightSource_Parms, LightType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType, METADATA_PARAMS(0, nullptr) }; // 767143608
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventCreateLightSource_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventCreateLightSource_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_SourceActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_LightType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_LightType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "CreateLightSource", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::AuracronWorldPartitionLightingManager_eventCreateLightSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::AuracronWorldPartitionLightingManager_eventCreateLightSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execCreateLightSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceActorId);
	P_GET_ENUM(EAuracronLightType,Z_Param_LightType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateLightSource(Z_Param_SourceActorId,EAuracronLightType(Z_Param_LightType),Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function CreateLightSource **********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function DeactivateLightSource ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics
{
	struct AuracronWorldPartitionLightingManager_eventDeactivateLightSource_Parms
	{
		FString LightingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventDeactivateLightSource_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventDeactivateLightSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventDeactivateLightSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "DeactivateLightSource", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::AuracronWorldPartitionLightingManager_eventDeactivateLightSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::AuracronWorldPartitionLightingManager_eventDeactivateLightSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execDeactivateLightSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeactivateLightSource(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function DeactivateLightSource ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function DoesLightSourceExist *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics
{
	struct AuracronWorldPartitionLightingManager_eventDoesLightSourceExist_Parms
	{
		FString LightingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventDoesLightSourceExist_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventDoesLightSourceExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventDoesLightSourceExist_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "DoesLightSourceExist", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::AuracronWorldPartitionLightingManager_eventDoesLightSourceExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::AuracronWorldPartitionLightingManager_eventDoesLightSourceExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execDoesLightSourceExist)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesLightSourceExist(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function DoesLightSourceExist *******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function DrawDebugLightingInfo ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics
{
	struct AuracronWorldPartitionLightingManager_eventDrawDebugLightingInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventDrawDebugLightingInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "DrawDebugLightingInfo", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::AuracronWorldPartitionLightingManager_eventDrawDebugLightingInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::AuracronWorldPartitionLightingManager_eventDrawDebugLightingInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execDrawDebugLightingInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugLightingInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function DrawDebugLightingInfo ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function EnableLightingDebug ******
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics
{
	struct AuracronWorldPartitionLightingManager_eventEnableLightingDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventEnableLightingDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventEnableLightingDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "EnableLightingDebug", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::AuracronWorldPartitionLightingManager_eventEnableLightingDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::AuracronWorldPartitionLightingManager_eventEnableLightingDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execEnableLightingDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableLightingDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function EnableLightingDebug ********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function EnableShadowsForLight ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics
{
	struct AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms
	{
		FString LightingId;
		bool bEnabled;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Shadow management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shadow management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "EnableShadowsForLight", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::AuracronWorldPartitionLightingManager_eventEnableShadowsForLight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execEnableShadowsForLight)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EnableShadowsForLight(Z_Param_LightingId,Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function EnableShadowsForLight ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetActiveLightSourceCount 
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetActiveLightSourceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetActiveLightSourceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetActiveLightSourceCount", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::AuracronWorldPartitionLightingManager_eventGetActiveLightSourceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::AuracronWorldPartitionLightingManager_eventGetActiveLightSourceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetActiveLightSourceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveLightSourceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetActiveLightSourceCount **

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetActiveLightSources ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetActiveLightSources_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetActiveLightSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetActiveLightSources", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetActiveLightSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetActiveLightSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetActiveLightSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActiveLightSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetActiveLightSources ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetAllLightSources *******
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetAllLightSources_Parms
	{
		TArray<FAuracronLightingDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLightingDescriptor, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetAllLightSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetAllLightSources", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetAllLightSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetAllLightSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetAllLightSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLightingDescriptor>*)Z_Param__Result=P_THIS->GetAllLightSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetAllLightSources *********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetConfiguration *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetConfiguration_Parms
	{
		FAuracronLightingConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLightingConfiguration, METADATA_PARAMS(0, nullptr) }; // 2198031420
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::AuracronWorldPartitionLightingManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::AuracronWorldPartitionLightingManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLightingConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetConfiguration ***********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetInfluencingLightSources 
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetInfluencingLightSources_Parms
	{
		FVector Location;
		TArray<FAuracronLightingDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetInfluencingLightSources_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLightingDescriptor, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetInfluencingLightSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetInfluencingLightSources", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetInfluencingLightSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetInfluencingLightSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetInfluencingLightSources)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLightingDescriptor>*)Z_Param__Result=P_THIS->GetInfluencingLightSources(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetInfluencingLightSources *

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetInstance **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionLightingManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionLightingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::AuracronWorldPartitionLightingManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::AuracronWorldPartitionLightingManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionLightingManager**)Z_Param__Result=UAuracronWorldPartitionLightingManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetInstance ****************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightingDescriptor ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightingDescriptor_Parms
	{
		FString LightingId;
		FAuracronLightingDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingDescriptor_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLightingDescriptor, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightingDescriptor", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::AuracronWorldPartitionLightingManager_eventGetLightingDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::AuracronWorldPartitionLightingManager_eventGetLightingDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightingDescriptor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLightingDescriptor*)Z_Param__Result=P_THIS->GetLightingDescriptor(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightingDescriptor ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightingIds ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightingIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightingIds", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::AuracronWorldPartitionLightingManager_eventGetLightingIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::AuracronWorldPartitionLightingManager_eventGetLightingIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightingIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLightingIds();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightingIds *************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightingLOD ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightingLOD_Parms
	{
		FString LightingId;
		EAuracronLightingLODLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingLOD_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingLOD_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel, METADATA_PARAMS(0, nullptr) }; // 2925161937
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightingLOD", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::AuracronWorldPartitionLightingManager_eventGetLightingLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::AuracronWorldPartitionLightingManager_eventGetLightingLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightingLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLightingLODLevel*)Z_Param__Result=P_THIS->GetLightingLOD(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightingLOD *************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightingStatistics ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightingStatistics_Parms
	{
		FAuracronLightingStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLightingStatistics, METADATA_PARAMS(0, nullptr) }; // 2471045903
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightingStatistics", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::AuracronWorldPartitionLightingManager_eventGetLightingStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::AuracronWorldPartitionLightingManager_eventGetLightingStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightingStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLightingStatistics*)Z_Param__Result=P_THIS->GetLightingStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightingStatistics ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightingStreamingState 
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightingStreamingState_Parms
	{
		FString LightingId;
		EAuracronLightingStreamingState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingStreamingState_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightingStreamingState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingStreamingState, METADATA_PARAMS(0, nullptr) }; // 2718364282
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightingStreamingState", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::AuracronWorldPartitionLightingManager_eventGetLightingStreamingState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::AuracronWorldPartitionLightingManager_eventGetLightingStreamingState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightingStreamingState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLightingStreamingState*)Z_Param__Result=P_THIS->GetLightingStreamingState(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightingStreamingState **

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightSourceCell *******
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightSourceCell_Parms
	{
		FString LightingId;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourceCell_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourceCell_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightSourceCell", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourceCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourceCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightSourceCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetLightSourceCell(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightSourceCell *********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightSourcesByType ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightSourcesByType_Parms
	{
		EAuracronLightType LightType;
		TArray<FAuracronLightingDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LightType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LightType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_LightType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_LightType = { "LightType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourcesByType_Parms, LightType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightType, METADATA_PARAMS(0, nullptr) }; // 767143608
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLightingDescriptor, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourcesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_LightType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_LightType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightSourcesByType", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourcesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourcesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightSourcesByType)
{
	P_GET_ENUM(EAuracronLightType,Z_Param_LightType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLightingDescriptor>*)Z_Param__Result=P_THIS->GetLightSourcesByType(EAuracronLightType(Z_Param_LightType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightSourcesByType ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightSourcesInCell ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightSourcesInCell_Parms
	{
		FString CellId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourcesInCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourcesInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightSourcesInCell", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourcesInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourcesInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightSourcesInCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLightSourcesInCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightSourcesInCell ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLightSourcesInRadius **
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLightSourcesInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FAuracronLightingDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lighting queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourcesInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourcesInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLightingDescriptor, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLightSourcesInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2055805550
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLightSourcesInRadius", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourcesInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::AuracronWorldPartitionLightingManager_eventGetLightSourcesInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLightSourcesInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLightingDescriptor>*)Z_Param__Result=P_THIS->GetLightSourcesInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLightSourcesInRadius ****

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLoadedLightSourceCount 
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLoadedLightSourceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLoadedLightSourceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLoadedLightSourceCount", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::AuracronWorldPartitionLightingManager_eventGetLoadedLightSourceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::AuracronWorldPartitionLightingManager_eventGetLoadedLightSourceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLoadedLightSourceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedLightSourceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLoadedLightSourceCount **

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetLoadedLightSources ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetLoadedLightSources_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetLoadedLightSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetLoadedLightSources", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetLoadedLightSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetLoadedLightSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetLoadedLightSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedLightSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetLoadedLightSources ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetStreamingLightSources *
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetStreamingLightSources_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetStreamingLightSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetStreamingLightSources", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetStreamingLightSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::AuracronWorldPartitionLightingManager_eventGetStreamingLightSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetStreamingLightSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetStreamingLightSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetStreamingLightSources ***

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetTotalLightSourceCount *
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetTotalLightSourceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetTotalLightSourceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetTotalLightSourceCount", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::AuracronWorldPartitionLightingManager_eventGetTotalLightSourceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::AuracronWorldPartitionLightingManager_eventGetTotalLightSourceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetTotalLightSourceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalLightSourceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetTotalLightSourceCount ***

// ********** Begin Class UAuracronWorldPartitionLightingManager Function GetTotalMemoryUsage ******
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics
{
	struct AuracronWorldPartitionLightingManager_eventGetTotalMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventGetTotalMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "GetTotalMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionLightingManager_eventGetTotalMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionLightingManager_eventGetTotalMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execGetTotalMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function GetTotalMemoryUsage ********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function Initialize ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics
{
	struct AuracronWorldPartitionLightingManager_eventInitialize_Parms
	{
		FAuracronLightingConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronLightingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2198031420
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::AuracronWorldPartitionLightingManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::AuracronWorldPartitionLightingManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronLightingConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function Initialize *****************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function IsInitialized ************
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionLightingManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::AuracronWorldPartitionLightingManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::AuracronWorldPartitionLightingManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function IsInitialized **************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function IsLightingDebugEnabled ***
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics
{
	struct AuracronWorldPartitionLightingManager_eventIsLightingDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventIsLightingDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventIsLightingDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "IsLightingDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::AuracronWorldPartitionLightingManager_eventIsLightingDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::AuracronWorldPartitionLightingManager_eventIsLightingDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execIsLightingDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLightingDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function IsLightingDebugEnabled *****

// ********** Begin Class UAuracronWorldPartitionLightingManager Function LoadLightmapsForCell *****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics
{
	struct AuracronWorldPartitionLightingManager_eventLoadLightmapsForCell_Parms
	{
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventLoadLightmapsForCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventLoadLightmapsForCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventLoadLightmapsForCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "LoadLightmapsForCell", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::AuracronWorldPartitionLightingManager_eventLoadLightmapsForCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::AuracronWorldPartitionLightingManager_eventLoadLightmapsForCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execLoadLightmapsForCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadLightmapsForCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function LoadLightmapsForCell *******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function LoadLightSource **********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics
{
	struct AuracronWorldPartitionLightingManager_eventLoadLightSource_Parms
	{
		FString LightingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lighting streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventLoadLightSource_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventLoadLightSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventLoadLightSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "LoadLightSource", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::AuracronWorldPartitionLightingManager_eventLoadLightSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::AuracronWorldPartitionLightingManager_eventLoadLightSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execLoadLightSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadLightSource(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function LoadLightSource ************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function LogLightingState *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LogLightingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LogLightingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "LogLightingState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LogLightingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LogLightingState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LogLightingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LogLightingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execLogLightingState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogLightingState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function LogLightingState ***********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function MoveLightSourceToCell ****
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics
{
	struct AuracronWorldPartitionLightingManager_eventMoveLightSourceToCell_Parms
	{
		FString LightingId;
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventMoveLightSourceToCell_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventMoveLightSourceToCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventMoveLightSourceToCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventMoveLightSourceToCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "MoveLightSourceToCell", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::AuracronWorldPartitionLightingManager_eventMoveLightSourceToCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::AuracronWorldPartitionLightingManager_eventMoveLightSourceToCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execMoveLightSourceToCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveLightSourceToCell(Z_Param_LightingId,Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function MoveLightSourceToCell ******

// ********** Begin Class UAuracronWorldPartitionLightingManager Function RemoveLightSource ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics
{
	struct AuracronWorldPartitionLightingManager_eventRemoveLightSource_Parms
	{
		FString LightingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventRemoveLightSource_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventRemoveLightSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventRemoveLightSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "RemoveLightSource", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::AuracronWorldPartitionLightingManager_eventRemoveLightSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::AuracronWorldPartitionLightingManager_eventRemoveLightSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execRemoveLightSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveLightSource(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function RemoveLightSource **********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function ResetStatistics **********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function ResetStatistics ************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetConfiguration *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetConfiguration_Parms
	{
		FAuracronLightingConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronLightingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2198031420
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::AuracronWorldPartitionLightingManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::AuracronWorldPartitionLightingManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronLightingConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetConfiguration ***********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetLightAttenuationRadius 
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetLightAttenuationRadius_Parms
	{
		FString LightingId;
		float Radius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightAttenuationRadius_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightAttenuationRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetLightAttenuationRadius_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetLightAttenuationRadius_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetLightAttenuationRadius", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::AuracronWorldPartitionLightingManager_eventSetLightAttenuationRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::AuracronWorldPartitionLightingManager_eventSetLightAttenuationRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetLightAttenuationRadius)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLightAttenuationRadius(Z_Param_LightingId,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetLightAttenuationRadius **

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetLightColor ************
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetLightColor_Parms
	{
		FString LightingId;
		FLinearColor Color;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightColor_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightColor_Parms, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetLightColor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetLightColor_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetLightColor", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::AuracronWorldPartitionLightingManager_eventSetLightColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::AuracronWorldPartitionLightingManager_eventSetLightColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetLightColor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Color);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLightColor(Z_Param_LightingId,Z_Param_Out_Color);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetLightColor **************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetLightingLOD ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetLightingLOD_Parms
	{
		FString LightingId;
		EAuracronLightingLODLevel LODLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lighting LOD\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightingLOD_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_LODLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightingLOD_Parms, LODLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLightingLODLevel, METADATA_PARAMS(0, nullptr) }; // 2925161937
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetLightingLOD_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetLightingLOD_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_LODLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetLightingLOD", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::AuracronWorldPartitionLightingManager_eventSetLightingLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::AuracronWorldPartitionLightingManager_eventSetLightingLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetLightingLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_ENUM(EAuracronLightingLODLevel,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLightingLOD(Z_Param_LightingId,EAuracronLightingLODLevel(Z_Param_LODLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetLightingLOD *************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetLightIntensity ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetLightIntensity_Parms
	{
		FString LightingId;
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Light properties\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Light properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightIntensity_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetLightIntensity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetLightIntensity_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetLightIntensity", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::AuracronWorldPartitionLightingManager_eventSetLightIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::AuracronWorldPartitionLightingManager_eventSetLightIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetLightIntensity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLightIntensity(Z_Param_LightingId,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetLightIntensity **********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetLightLocation *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetLightLocation_Parms
	{
		FString LightingId;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightLocation_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetLightLocation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetLightLocation_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetLightLocation", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::AuracronWorldPartitionLightingManager_eventSetLightLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::AuracronWorldPartitionLightingManager_eventSetLightLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetLightLocation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLightLocation(Z_Param_LightingId,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetLightLocation ***********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetLightRotation *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetLightRotation_Parms
	{
		FString LightingId;
		FRotator Rotation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightRotation_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightRotation_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetLightRotation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetLightRotation_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetLightRotation", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::AuracronWorldPartitionLightingManager_eventSetLightRotation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::AuracronWorldPartitionLightingManager_eventSetLightRotation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetLightRotation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLightRotation(Z_Param_LightingId,Z_Param_Out_Rotation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetLightRotation ***********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetLightTemperature ******
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetLightTemperature_Parms
	{
		FString LightingId;
		float Temperature;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Temperature;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightTemperature_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_Temperature = { "Temperature", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetLightTemperature_Parms, Temperature), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetLightTemperature_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetLightTemperature_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_Temperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetLightTemperature", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::AuracronWorldPartitionLightingManager_eventSetLightTemperature_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::AuracronWorldPartitionLightingManager_eventSetLightTemperature_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetLightTemperature)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Temperature);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLightTemperature(Z_Param_LightingId,Z_Param_Temperature);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetLightTemperature ********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function SetShadowQuality *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics
{
	struct AuracronWorldPartitionLightingManager_eventSetShadowQuality_Parms
	{
		FString LightingId;
		EAuracronShadowQuality Quality;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetShadowQuality_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventSetShadowQuality_Parms, Quality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronShadowQuality, METADATA_PARAMS(0, nullptr) }; // 317022883
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventSetShadowQuality_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventSetShadowQuality_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "SetShadowQuality", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::AuracronWorldPartitionLightingManager_eventSetShadowQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::AuracronWorldPartitionLightingManager_eventSetShadowQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execSetShadowQuality)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_GET_ENUM(EAuracronShadowQuality,Z_Param_Quality);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetShadowQuality(Z_Param_LightingId,EAuracronShadowQuality(Z_Param_Quality));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function SetShadowQuality ***********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function Shutdown *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function Shutdown *******************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function Tick *********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics
{
	struct AuracronWorldPartitionLightingManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::AuracronWorldPartitionLightingManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::AuracronWorldPartitionLightingManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function Tick ***********************

// ********** Begin Class UAuracronWorldPartitionLightingManager Function UnloadLightmapsForCell ***
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics
{
	struct AuracronWorldPartitionLightingManager_eventUnloadLightmapsForCell_Parms
	{
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventUnloadLightmapsForCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventUnloadLightmapsForCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventUnloadLightmapsForCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "UnloadLightmapsForCell", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::AuracronWorldPartitionLightingManager_eventUnloadLightmapsForCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::AuracronWorldPartitionLightingManager_eventUnloadLightmapsForCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execUnloadLightmapsForCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadLightmapsForCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function UnloadLightmapsForCell *****

// ********** Begin Class UAuracronWorldPartitionLightingManager Function UnloadLightSource ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics
{
	struct AuracronWorldPartitionLightingManager_eventUnloadLightSource_Parms
	{
		FString LightingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::NewProp_LightingId = { "LightingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventUnloadLightSource_Parms, LightingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingId_MetaData), NewProp_LightingId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLightingManager_eventUnloadLightSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLightingManager_eventUnloadLightSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::NewProp_LightingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "UnloadLightSource", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::AuracronWorldPartitionLightingManager_eventUnloadLightSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::AuracronWorldPartitionLightingManager_eventUnloadLightSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execUnloadLightSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LightingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadLightSource(Z_Param_LightingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function UnloadLightSource **********

// ********** Begin Class UAuracronWorldPartitionLightingManager Function UpdateDistanceBasedLODs **
struct Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics
{
	struct AuracronWorldPartitionLightingManager_eventUpdateDistanceBasedLODs_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLightingManager_eventUpdateDistanceBasedLODs_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLightingManager, nullptr, "UpdateDistanceBasedLODs", Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionLightingManager_eventUpdateDistanceBasedLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionLightingManager_eventUpdateDistanceBasedLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLightingManager::execUpdateDistanceBasedLODs)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedLODs(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLightingManager Function UpdateDistanceBasedLODs ****

// ********** Begin Class UAuracronWorldPartitionLightingManager ***********************************
void UAuracronWorldPartitionLightingManager::StaticRegisterNativesUAuracronWorldPartitionLightingManager()
{
	UClass* Class = UAuracronWorldPartitionLightingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateLightSource", &UAuracronWorldPartitionLightingManager::execActivateLightSource },
		{ "BuildLightmapsForCell", &UAuracronWorldPartitionLightingManager::execBuildLightmapsForCell },
		{ "CalculateLODForDistance", &UAuracronWorldPartitionLightingManager::execCalculateLODForDistance },
		{ "CreateLightSource", &UAuracronWorldPartitionLightingManager::execCreateLightSource },
		{ "DeactivateLightSource", &UAuracronWorldPartitionLightingManager::execDeactivateLightSource },
		{ "DoesLightSourceExist", &UAuracronWorldPartitionLightingManager::execDoesLightSourceExist },
		{ "DrawDebugLightingInfo", &UAuracronWorldPartitionLightingManager::execDrawDebugLightingInfo },
		{ "EnableLightingDebug", &UAuracronWorldPartitionLightingManager::execEnableLightingDebug },
		{ "EnableShadowsForLight", &UAuracronWorldPartitionLightingManager::execEnableShadowsForLight },
		{ "GetActiveLightSourceCount", &UAuracronWorldPartitionLightingManager::execGetActiveLightSourceCount },
		{ "GetActiveLightSources", &UAuracronWorldPartitionLightingManager::execGetActiveLightSources },
		{ "GetAllLightSources", &UAuracronWorldPartitionLightingManager::execGetAllLightSources },
		{ "GetConfiguration", &UAuracronWorldPartitionLightingManager::execGetConfiguration },
		{ "GetInfluencingLightSources", &UAuracronWorldPartitionLightingManager::execGetInfluencingLightSources },
		{ "GetInstance", &UAuracronWorldPartitionLightingManager::execGetInstance },
		{ "GetLightingDescriptor", &UAuracronWorldPartitionLightingManager::execGetLightingDescriptor },
		{ "GetLightingIds", &UAuracronWorldPartitionLightingManager::execGetLightingIds },
		{ "GetLightingLOD", &UAuracronWorldPartitionLightingManager::execGetLightingLOD },
		{ "GetLightingStatistics", &UAuracronWorldPartitionLightingManager::execGetLightingStatistics },
		{ "GetLightingStreamingState", &UAuracronWorldPartitionLightingManager::execGetLightingStreamingState },
		{ "GetLightSourceCell", &UAuracronWorldPartitionLightingManager::execGetLightSourceCell },
		{ "GetLightSourcesByType", &UAuracronWorldPartitionLightingManager::execGetLightSourcesByType },
		{ "GetLightSourcesInCell", &UAuracronWorldPartitionLightingManager::execGetLightSourcesInCell },
		{ "GetLightSourcesInRadius", &UAuracronWorldPartitionLightingManager::execGetLightSourcesInRadius },
		{ "GetLoadedLightSourceCount", &UAuracronWorldPartitionLightingManager::execGetLoadedLightSourceCount },
		{ "GetLoadedLightSources", &UAuracronWorldPartitionLightingManager::execGetLoadedLightSources },
		{ "GetStreamingLightSources", &UAuracronWorldPartitionLightingManager::execGetStreamingLightSources },
		{ "GetTotalLightSourceCount", &UAuracronWorldPartitionLightingManager::execGetTotalLightSourceCount },
		{ "GetTotalMemoryUsage", &UAuracronWorldPartitionLightingManager::execGetTotalMemoryUsage },
		{ "Initialize", &UAuracronWorldPartitionLightingManager::execInitialize },
		{ "IsInitialized", &UAuracronWorldPartitionLightingManager::execIsInitialized },
		{ "IsLightingDebugEnabled", &UAuracronWorldPartitionLightingManager::execIsLightingDebugEnabled },
		{ "LoadLightmapsForCell", &UAuracronWorldPartitionLightingManager::execLoadLightmapsForCell },
		{ "LoadLightSource", &UAuracronWorldPartitionLightingManager::execLoadLightSource },
		{ "LogLightingState", &UAuracronWorldPartitionLightingManager::execLogLightingState },
		{ "MoveLightSourceToCell", &UAuracronWorldPartitionLightingManager::execMoveLightSourceToCell },
		{ "RemoveLightSource", &UAuracronWorldPartitionLightingManager::execRemoveLightSource },
		{ "ResetStatistics", &UAuracronWorldPartitionLightingManager::execResetStatistics },
		{ "SetConfiguration", &UAuracronWorldPartitionLightingManager::execSetConfiguration },
		{ "SetLightAttenuationRadius", &UAuracronWorldPartitionLightingManager::execSetLightAttenuationRadius },
		{ "SetLightColor", &UAuracronWorldPartitionLightingManager::execSetLightColor },
		{ "SetLightingLOD", &UAuracronWorldPartitionLightingManager::execSetLightingLOD },
		{ "SetLightIntensity", &UAuracronWorldPartitionLightingManager::execSetLightIntensity },
		{ "SetLightLocation", &UAuracronWorldPartitionLightingManager::execSetLightLocation },
		{ "SetLightRotation", &UAuracronWorldPartitionLightingManager::execSetLightRotation },
		{ "SetLightTemperature", &UAuracronWorldPartitionLightingManager::execSetLightTemperature },
		{ "SetShadowQuality", &UAuracronWorldPartitionLightingManager::execSetShadowQuality },
		{ "Shutdown", &UAuracronWorldPartitionLightingManager::execShutdown },
		{ "Tick", &UAuracronWorldPartitionLightingManager::execTick },
		{ "UnloadLightmapsForCell", &UAuracronWorldPartitionLightingManager::execUnloadLightmapsForCell },
		{ "UnloadLightSource", &UAuracronWorldPartitionLightingManager::execUnloadLightSource },
		{ "UpdateDistanceBasedLODs", &UAuracronWorldPartitionLightingManager::execUpdateDistanceBasedLODs },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager;
UClass* UAuracronWorldPartitionLightingManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionLightingManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionLightingManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionLightingManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionLightingManager_NoRegister()
{
	return UAuracronWorldPartitionLightingManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Lighting Manager\n * Central manager for lighting streaming in world partition\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionLighting.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Lighting Manager\nCentral manager for lighting streaming in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLightingLoaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLightingUnloaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLightingActivated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLightingDeactivated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLightingLODChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLighting.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLightingLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLightingUnloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLightingActivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLightingDeactivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLightingLODChanged;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ActivateLightSource, "ActivateLightSource" }, // 947778161
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_BuildLightmapsForCell, "BuildLightmapsForCell" }, // 2272374307
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CalculateLODForDistance, "CalculateLODForDistance" }, // 3678089951
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_CreateLightSource, "CreateLightSource" }, // 1454315379
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DeactivateLightSource, "DeactivateLightSource" }, // 4074179555
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DoesLightSourceExist, "DoesLightSourceExist" }, // 1718982642
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_DrawDebugLightingInfo, "DrawDebugLightingInfo" }, // 179592751
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableLightingDebug, "EnableLightingDebug" }, // 1385699387
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_EnableShadowsForLight, "EnableShadowsForLight" }, // 333320338
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSourceCount, "GetActiveLightSourceCount" }, // 1315311293
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetActiveLightSources, "GetActiveLightSources" }, // 3764377128
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetAllLightSources, "GetAllLightSources" }, // 610714858
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetConfiguration, "GetConfiguration" }, // 4089496563
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInfluencingLightSources, "GetInfluencingLightSources" }, // 1319730939
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetInstance, "GetInstance" }, // 3554109423
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingDescriptor, "GetLightingDescriptor" }, // 3830332617
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingIds, "GetLightingIds" }, // 869467324
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingLOD, "GetLightingLOD" }, // 2106436326
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStatistics, "GetLightingStatistics" }, // 4211837802
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightingStreamingState, "GetLightingStreamingState" }, // 1737111440
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourceCell, "GetLightSourceCell" }, // 311313118
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesByType, "GetLightSourcesByType" }, // 40346929
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInCell, "GetLightSourcesInCell" }, // 2594133974
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLightSourcesInRadius, "GetLightSourcesInRadius" }, // 3630175965
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSourceCount, "GetLoadedLightSourceCount" }, // 101455306
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetLoadedLightSources, "GetLoadedLightSources" }, // 480203346
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetStreamingLightSources, "GetStreamingLightSources" }, // 721277170
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalLightSourceCount, "GetTotalLightSourceCount" }, // 1128840750
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_GetTotalMemoryUsage, "GetTotalMemoryUsage" }, // 1507031583
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Initialize, "Initialize" }, // 2822718590
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsInitialized, "IsInitialized" }, // 226723900
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_IsLightingDebugEnabled, "IsLightingDebugEnabled" }, // 4237237059
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightmapsForCell, "LoadLightmapsForCell" }, // 2387182406
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LoadLightSource, "LoadLightSource" }, // 1767891752
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_LogLightingState, "LogLightingState" }, // 3855282244
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_MoveLightSourceToCell, "MoveLightSourceToCell" }, // 3609101075
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature, "OnLightingActivated__DelegateSignature" }, // 3259925158
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature, "OnLightingDeactivated__DelegateSignature" }, // 1141809629
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature, "OnLightingLoaded__DelegateSignature" }, // 229927229
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature, "OnLightingLODChanged__DelegateSignature" }, // 724228679
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature, "OnLightingUnloaded__DelegateSignature" }, // 379301984
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_RemoveLightSource, "RemoveLightSource" }, // 1664000647
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_ResetStatistics, "ResetStatistics" }, // 1660881239
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetConfiguration, "SetConfiguration" }, // 1773336101
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightAttenuationRadius, "SetLightAttenuationRadius" }, // 1472059314
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightColor, "SetLightColor" }, // 2120708576
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightingLOD, "SetLightingLOD" }, // 3861937798
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightIntensity, "SetLightIntensity" }, // 1779697232
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightLocation, "SetLightLocation" }, // 1580895305
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightRotation, "SetLightRotation" }, // 2408764440
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetLightTemperature, "SetLightTemperature" }, // 881370222
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_SetShadowQuality, "SetShadowQuality" }, // 2719863304
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Shutdown, "Shutdown" }, // 2391246927
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_Tick, "Tick" }, // 2412690920
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightmapsForCell, "UnloadLightmapsForCell" }, // 797656434
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UnloadLightSource, "UnloadLightSource" }, // 3415229203
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLightingManager_UpdateDistanceBasedLODs, "UpdateDistanceBasedLODs" }, // 2332224262
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionLightingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingLoaded = { "OnLightingLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLightingManager, OnLightingLoaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLightingLoaded_MetaData), NewProp_OnLightingLoaded_MetaData) }; // 229927229
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingUnloaded = { "OnLightingUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLightingManager, OnLightingUnloaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLightingUnloaded_MetaData), NewProp_OnLightingUnloaded_MetaData) }; // 379301984
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingActivated = { "OnLightingActivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLightingManager, OnLightingActivated), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLightingActivated_MetaData), NewProp_OnLightingActivated_MetaData) }; // 3259925158
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingDeactivated = { "OnLightingDeactivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLightingManager, OnLightingDeactivated), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLightingDeactivated_MetaData), NewProp_OnLightingDeactivated_MetaData) }; // 1141809629
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingLODChanged = { "OnLightingLODChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLightingManager, OnLightingLODChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLightingLODChanged_MetaData), NewProp_OnLightingLODChanged_MetaData) }; // 724228679
void Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionLightingManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionLightingManager), &Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLightingManager, Configuration), Z_Construct_UScriptStruct_FAuracronLightingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2198031420
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLightingManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingActivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingDeactivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_OnLightingLODChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::ClassParams = {
	&UAuracronWorldPartitionLightingManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionLightingManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionLightingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager.OuterSingleton;
}
UAuracronWorldPartitionLightingManager::UAuracronWorldPartitionLightingManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionLightingManager);
UAuracronWorldPartitionLightingManager::~UAuracronWorldPartitionLightingManager() {}
// ********** End Class UAuracronWorldPartitionLightingManager *************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronLightingStreamingState_StaticEnum, TEXT("EAuracronLightingStreamingState"), &Z_Registration_Info_UEnum_EAuracronLightingStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2718364282U) },
		{ EAuracronLightingLODLevel_StaticEnum, TEXT("EAuracronLightingLODLevel"), &Z_Registration_Info_UEnum_EAuracronLightingLODLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2925161937U) },
		{ EAuracronLightType_StaticEnum, TEXT("EAuracronLightType"), &Z_Registration_Info_UEnum_EAuracronLightType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 767143608U) },
		{ EAuracronLightMobility_StaticEnum, TEXT("EAuracronLightMobility"), &Z_Registration_Info_UEnum_EAuracronLightMobility, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1252204914U) },
		{ EAuracronShadowQuality_StaticEnum, TEXT("EAuracronShadowQuality"), &Z_Registration_Info_UEnum_EAuracronShadowQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 317022883U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLightingConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronLightingConfiguration_Statics::NewStructOps, TEXT("AuracronLightingConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronLightingConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLightingConfiguration), 2198031420U) },
		{ FAuracronLightingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronLightingDescriptor_Statics::NewStructOps, TEXT("AuracronLightingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronLightingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLightingDescriptor), 2055805550U) },
		{ FAuracronLightingStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronLightingStatistics_Statics::NewStructOps, TEXT("AuracronLightingStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronLightingStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLightingStatistics), 2471045903U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionLightingManager, UAuracronWorldPartitionLightingManager::StaticClass, TEXT("UAuracronWorldPartitionLightingManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionLightingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionLightingManager), 1305891422U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_1847187398(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLighting_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
