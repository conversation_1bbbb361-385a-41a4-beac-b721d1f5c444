// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanBridge_init() {}
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanCharacterGenerated__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanDNALoaded__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanDNAModified__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanRigCreated__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnAsyncOperationCompleted__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnErrorOccurred__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnErrorRecovered__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnMemoryPoolStatusChanged__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnPerformanceMetricsUpdated__DelegateSignature();
	AURACRONMETAHUMANBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnSystemHealthChanged__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronMetaHumanBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronMetaHumanBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanCharacterGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanDNALoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanDNAModified__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronMetaHumanBridge_OnMetaHumanRigCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnAsyncOperationCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnErrorOccurred__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnErrorRecovered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnMemoryPoolStatusChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnPerformanceMetricsUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronMetaHumanBridgeAPI_OnSystemHealthChanged__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronMetaHumanBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x718FF39F,
				0x10E10A8A,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronMetaHumanBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronMetaHumanBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronMetaHumanBridge(Z_Construct_UPackage__Script_AuracronMetaHumanBridge, TEXT("/Script/AuracronMetaHumanBridge"), Z_Registration_Info_UPackage__Script_AuracronMetaHumanBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x718FF39F, 0x10E10A8A));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
